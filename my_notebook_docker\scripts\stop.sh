#!/bin/bash
# ===========================================
# 语音处理智能平台 Docker 停止脚本
# ===========================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 停止服务
stop_services() {
    log_info "停止语音处理智能平台服务..."
    
    # 切换到脚本目录
    cd "$(dirname "$0")/.."
    
    # 检查是否有运行的服务
    if docker-compose ps -q | grep -q .; then
        log_info "正在停止服务..."
        
        # 优雅停止服务
        docker-compose stop
        
        log_success "服务已停止"
        
        # 询问是否删除容器
        read -p "是否删除容器? (保留数据卷) (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            docker-compose down
            log_success "容器已删除"
        fi
        
        # 询问是否清理镜像
        read -p "是否清理未使用的Docker镜像? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            docker image prune -f
            log_success "未使用的镜像已清理"
        fi
        
    else
        log_warning "没有运行的服务"
    fi
}

# 显示状态
show_status() {
    echo
    log_info "当前服务状态:"
    docker-compose ps
    
    echo
    log_info "数据卷状态:"
    ls -la volumes/ 2>/dev/null || log_warning "volumes目录不存在"
}

# 主函数
main() {
    echo "🛑 语音处理智能平台 Docker 服务停止"
    echo "========================================"
    
    stop_services
    show_status
    
    echo
    log_success "✅ 停止操作完成"
    echo
    log_info "数据已保留在 volumes/ 目录中"
    log_info "重新启动请运行: ./scripts/start.sh"
}

# 执行主函数
main "$@"
