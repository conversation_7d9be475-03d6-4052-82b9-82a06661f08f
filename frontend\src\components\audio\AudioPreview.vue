<template>
  <div class="audio-preview">
    <div class="preview-header">
      <h4>{{ file.name }}</h4>
      <div class="file-meta">
        <span class="file-size">{{ formatFileSize(file.size) }}</span>
        <span v-if="file.duration" class="file-duration">{{ formatDuration(file.duration) }}</span>
      </div>
    </div>

    <!-- 音频播放器 -->
    <div class="audio-player">
      <audio
        ref="audioRef"
        :src="audioUrl"
        controls
        preload="metadata"
        @loadedmetadata="handleAudioLoaded"
        @timeupdate="handleTimeUpdate"
        @error="handleAudioError"
      />
    </div>

    <!-- 波形图 -->
    <div v-if="showWaveform" class="waveform-section">
      <div class="section-title">
        <span>🌊 波形图</span>
        <el-button text @click="toggleWaveform" size="small">
          {{ waveformVisible ? '隐藏' : '显示' }}
        </el-button>
      </div>
      <div v-show="waveformVisible" class="waveform-container">
        <canvas
          ref="waveformCanvas"
          class="waveform-canvas"
          @click="handleWaveformClick"
        />
        <div v-if="waveformLoading" class="waveform-loading">
          <el-icon class="is-loading"><Loading /></el-icon>
          <span>生成波形图中...</span>
        </div>
      </div>
    </div>

    <!-- 频谱图 -->
    <div v-if="showSpectrum" class="spectrum-section">
      <div class="section-title">
        <span>📊 频谱图</span>
        <el-button text @click="toggleSpectrum" size="small">
          {{ spectrumVisible ? '隐藏' : '显示' }}
        </el-button>
      </div>
      <div v-show="spectrumVisible" class="spectrum-container">
        <canvas ref="spectrumCanvas" class="spectrum-canvas" />
        <div v-if="spectrumLoading" class="spectrum-loading">
          <el-icon class="is-loading"><Loading /></el-icon>
          <span>生成频谱图中...</span>
        </div>
      </div>
    </div>

    <!-- 音频质量分析 -->
    <div v-if="showQualityAnalysis" class="quality-section">
      <div class="section-title">
        <span>📈 音频质量分析</span>
        <el-button text @click="toggleQualityAnalysis" size="small">
          {{ qualityVisible ? '隐藏' : '显示' }}
        </el-button>
      </div>
      <div v-show="qualityVisible" class="quality-container">
        <div v-if="qualityLoading" class="quality-loading">
          <el-icon class="is-loading"><Loading /></el-icon>
          <span>分析音频质量中...</span>
        </div>
        <div v-else class="quality-metrics">
          <div class="metric-item">
            <label>RMS 音量</label>
            <div class="metric-bar">
              <div class="bar-fill" :style="{ width: `${(qualityMetrics.rms || 0) * 100}%` }"></div>
            </div>
            <span>{{ ((qualityMetrics.rms || 0) * 100).toFixed(1) }}%</span>
          </div>
          <div class="metric-item">
            <label>峰值音量</label>
            <div class="metric-bar">
              <div class="bar-fill" :style="{ width: `${(qualityMetrics.peak || 0) * 100}%` }"></div>
            </div>
            <span>{{ ((qualityMetrics.peak || 0) * 100).toFixed(1) }}%</span>
          </div>
          <div class="metric-item">
            <label>动态范围</label>
            <span class="metric-value">{{ (qualityMetrics.dynamicRange || 0).toFixed(1) }} dB</span>
          </div>
          <div class="metric-item">
            <label>信噪比</label>
            <span class="metric-value">{{ (qualityMetrics.snr || 0).toFixed(1) }} dB</span>
          </div>
          <div class="metric-item">
            <label>质量评分</label>
            <div class="quality-score" :class="getQualityClass(qualityMetrics.score)">
              {{ qualityMetrics.score || 0 }}/100
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 音频片段选择 -->
    <div v-if="showSegmentSelector" class="segment-section">
      <div class="section-title">
        <span>✂️ 片段选择</span>
        <el-button text @click="toggleSegmentSelector" size="small">
          {{ segmentVisible ? '隐藏' : '显示' }}
        </el-button>
      </div>
      <div v-show="segmentVisible" class="segment-container">
        <div class="segment-controls">
          <el-input-number
            v-model="segmentStart"
            :min="0"
            :max="audioInfo.duration || 0"
            :step="0.1"
            :precision="1"
            size="small"
            placeholder="开始时间"
          />
          <span>-</span>
          <el-input-number
            v-model="segmentEnd"
            :min="segmentStart"
            :max="audioInfo.duration || 0"
            :step="0.1"
            :precision="1"
            size="small"
            placeholder="结束时间"
          />
          <el-button @click="playSegment" size="small" type="primary">播放片段</el-button>
          <el-button @click="exportSegment" size="small">导出片段</el-button>
        </div>
        <div class="segment-info">
          <span>片段时长: {{ formatDuration((segmentEnd || 0) - (segmentStart || 0)) }}</span>
        </div>
      </div>
    </div>

    <!-- 音频信息 -->
    <div class="audio-info">
      <div class="info-grid">
        <div class="info-item">
          <label>采样率</label>
          <span>{{ audioInfo.sampleRate || 'N/A' }} Hz</span>
        </div>
        <div class="info-item">
          <label>声道数</label>
          <span>{{ audioInfo.channels || 'N/A' }}</span>
        </div>
        <div class="info-item">
          <label>比特率</label>
          <span>{{ audioInfo.bitRate || 'N/A' }} kbps</span>
        </div>
        <div class="info-item">
          <label>格式</label>
          <span>{{ getFileExtension(file.name) }}</span>
        </div>
        <div class="info-item">
          <label>文件大小</label>
          <span>{{ formatFileSize(file.size) }}</span>
        </div>
        <div class="info-item">
          <label>时长</label>
          <span>{{ formatDuration(audioInfo.duration || 0) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Loading } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  file: {
    type: Object,
    required: true
  },
  showWaveform: {
    type: Boolean,
    default: true
  },
  showSpectrum: {
    type: Boolean,
    default: true
  },
  showQualityAnalysis: {
    type: Boolean,
    default: true
  },
  showSegmentSelector: {
    type: Boolean,
    default: true
  },
  enableAdvancedAnalysis: {
    type: Boolean,
    default: true
  }
})

// 响应式数据
const audioRef = ref()
const waveformCanvas = ref()
const spectrumCanvas = ref()

const audioUrl = ref('')
const audioInfo = ref({})
const waveformVisible = ref(true)
const spectrumVisible = ref(true)
const waveformLoading = ref(false)
const spectrumLoading = ref(false)

// 新增的响应式数据
const qualityVisible = ref(true)
const qualityLoading = ref(false)
const qualityMetrics = ref({})
const segmentVisible = ref(true)
const segmentStart = ref(0)
const segmentEnd = ref(0)

const audioContext = ref(null)
const audioBuffer = ref(null)
const currentTime = ref(0)
const analyser = ref(null)
const frequencyData = ref(null)

// 计算属性
const canvasWidth = computed(() => 800)
const canvasHeight = computed(() => 200)

// 工具函数
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDuration = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

const getFileExtension = (filename) => {
  return filename.split('.').pop().toUpperCase()
}

// 事件处理
const handleAudioLoaded = () => {
  if (audioRef.value) {
    audioInfo.value = {
      duration: audioRef.value.duration,
      sampleRate: audioContext.value?.sampleRate,
      channels: 2, // 默认值，实际需要从音频文件获取
      bitRate: 128 // 默认值
    }
    
    if (props.showWaveform) {
      generateWaveform()
    }
    
    if (props.showSpectrum) {
      generateSpectrum()
    }

    if (props.showQualityAnalysis) {
      analyzeAudioQuality()
    }

    // 设置片段选择的默认值
    segmentStart.value = 0
    segmentEnd.value = audioRef.value.duration || 0
  }
}

const handleTimeUpdate = () => {
  if (audioRef.value) {
    currentTime.value = audioRef.value.currentTime
  }
}

const handleAudioError = (error) => {
  console.error('音频加载错误:', error)
  ElMessage.error('音频文件加载失败')
}

const handleWaveformClick = (event) => {
  if (!audioRef.value || !waveformCanvas.value) return
  
  const canvas = waveformCanvas.value
  const rect = canvas.getBoundingClientRect()
  const x = event.clientX - rect.left
  const percentage = x / canvas.width
  const newTime = percentage * audioRef.value.duration
  
  audioRef.value.currentTime = newTime
}

// 波形图相关
const toggleWaveform = () => {
  waveformVisible.value = !waveformVisible.value
  if (waveformVisible.value && !audioBuffer.value) {
    generateWaveform()
  }
}

const generateWaveform = async () => {
  if (!audioUrl.value || waveformLoading.value) return
  
  try {
    waveformLoading.value = true
    
    // 初始化音频上下文
    if (!audioContext.value) {
      audioContext.value = new (window.AudioContext || window.webkitAudioContext)()
    }
    
    // 获取音频数据
    const response = await fetch(audioUrl.value)
    const arrayBuffer = await response.arrayBuffer()
    audioBuffer.value = await audioContext.value.decodeAudioData(arrayBuffer)
    
    // 绘制波形图
    drawWaveform()
    
  } catch (error) {
    console.error('生成波形图失败:', error)
    ElMessage.error('生成波形图失败')
  } finally {
    waveformLoading.value = false
  }
}

const drawWaveform = () => {
  if (!waveformCanvas.value || !audioBuffer.value) return
  
  const canvas = waveformCanvas.value
  const ctx = canvas.getContext('2d')
  
  // 设置画布尺寸
  canvas.width = canvasWidth.value
  canvas.height = canvasHeight.value
  
  // 清空画布
  ctx.fillStyle = 'var(--surface-bg)'
  ctx.fillRect(0, 0, canvas.width, canvas.height)
  
  // 获取音频数据
  const channelData = audioBuffer.value.getChannelData(0)
  const samples = channelData.length
  const samplesPerPixel = Math.floor(samples / canvas.width)
  
  // 绘制波形
  ctx.strokeStyle = 'var(--accent-primary)'
  ctx.lineWidth = 1
  ctx.beginPath()
  
  for (let x = 0; x < canvas.width; x++) {
    const start = x * samplesPerPixel
    const end = start + samplesPerPixel
    
    let min = 0
    let max = 0
    
    for (let i = start; i < end && i < samples; i++) {
      const sample = channelData[i]
      if (sample < min) min = sample
      if (sample > max) max = sample
    }
    
    const yMin = (1 + min) * canvas.height / 2
    const yMax = (1 + max) * canvas.height / 2
    
    if (x === 0) {
      ctx.moveTo(x, yMin)
    } else {
      ctx.lineTo(x, yMin)
      ctx.lineTo(x, yMax)
    }
  }
  
  ctx.stroke()
  
  // 绘制播放进度线
  if (audioRef.value && audioRef.value.duration > 0) {
    const progress = currentTime.value / audioRef.value.duration
    const progressX = progress * canvas.width
    
    ctx.strokeStyle = 'var(--danger-color)'
    ctx.lineWidth = 2
    ctx.beginPath()
    ctx.moveTo(progressX, 0)
    ctx.lineTo(progressX, canvas.height)
    ctx.stroke()
  }
}

// 频谱图相关
const toggleSpectrum = () => {
  spectrumVisible.value = !spectrumVisible.value
  if (spectrumVisible.value && !audioBuffer.value) {
    generateSpectrum()
  }
}

const generateSpectrum = async () => {
  if (!audioBuffer.value || spectrumLoading.value) return
  
  try {
    spectrumLoading.value = true
    
    // 绘制频谱图
    drawSpectrum()
    
  } catch (error) {
    console.error('生成频谱图失败:', error)
    ElMessage.error('生成频谱图失败')
  } finally {
    spectrumLoading.value = false
  }
}

const drawSpectrum = () => {
  if (!spectrumCanvas.value || !audioBuffer.value) return
  
  const canvas = spectrumCanvas.value
  const ctx = canvas.getContext('2d')
  
  // 设置画布尺寸
  canvas.width = canvasWidth.value
  canvas.height = canvasHeight.value
  
  // 清空画布
  ctx.fillStyle = 'var(--surface-bg)'
  ctx.fillRect(0, 0, canvas.width, canvas.height)
  
  // 简化的频谱图绘制（实际需要FFT分析）
  const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height)
  gradient.addColorStop(0, 'var(--accent-primary)')
  gradient.addColorStop(1, 'var(--accent-secondary)')
  
  ctx.fillStyle = gradient
  
  // 模拟频谱数据
  for (let x = 0; x < canvas.width; x += 4) {
    const height = Math.random() * canvas.height * 0.8
    ctx.fillRect(x, canvas.height - height, 2, height)
  }
}

// 生命周期
onMounted(() => {
  // 创建音频URL
  if (props.file.file) {
    audioUrl.value = URL.createObjectURL(props.file.file)
  }
})

onUnmounted(() => {
  // 清理资源
  if (audioUrl.value) {
    URL.revokeObjectURL(audioUrl.value)
  }
  
  if (audioContext.value) {
    audioContext.value.close()
  }
})

// 音频质量分析
const toggleQualityAnalysis = () => {
  qualityVisible.value = !qualityVisible.value
  if (qualityVisible.value && Object.keys(qualityMetrics.value).length === 0) {
    analyzeAudioQuality()
  }
}

const analyzeAudioQuality = async () => {
  if (!audioBuffer.value || qualityLoading.value) return

  try {
    qualityLoading.value = true

    const channelData = audioBuffer.value.getChannelData(0)
    const samples = channelData.length

    // 计算RMS (Root Mean Square)
    let sumSquares = 0
    let peak = 0

    for (let i = 0; i < samples; i++) {
      const sample = Math.abs(channelData[i])
      sumSquares += sample * sample
      if (sample > peak) peak = sample
    }

    const rms = Math.sqrt(sumSquares / samples)

    // 计算动态范围
    const dynamicRange = peak > 0 ? 20 * Math.log10(peak / (rms + 1e-10)) : 0

    // 计算信噪比 (简化版本)
    const noiseFloor = calculateNoiseFloor(channelData)
    const snr = rms > 0 ? 20 * Math.log10(rms / (noiseFloor + 1e-10)) : 0

    // 计算质量评分
    const score = calculateQualityScore(rms, peak, dynamicRange, snr)

    qualityMetrics.value = {
      rms,
      peak,
      dynamicRange,
      snr,
      score
    }

  } catch (error) {
    console.error('音频质量分析失败:', error)
    ElMessage.error('音频质量分析失败')
  } finally {
    qualityLoading.value = false
  }
}

const calculateNoiseFloor = (channelData) => {
  // 简化的噪声底计算：取最小的10%样本的平均值
  const sortedSamples = [...channelData].map(Math.abs).sort((a, b) => a - b)
  const noiseCount = Math.floor(sortedSamples.length * 0.1)
  const noiseSum = sortedSamples.slice(0, noiseCount).reduce((sum, val) => sum + val, 0)
  return noiseSum / noiseCount
}

const calculateQualityScore = (rms, peak, dynamicRange, snr) => {
  let score = 0

  // RMS评分 (0-25分)
  if (rms > 0.1 && rms < 0.8) score += 25
  else if (rms > 0.05) score += 15
  else score += 5

  // 峰值评分 (0-25分)
  if (peak < 0.95 && peak > 0.3) score += 25
  else if (peak < 0.99) score += 15
  else score += 5

  // 动态范围评分 (0-25分)
  if (dynamicRange > 10) score += 25
  else if (dynamicRange > 5) score += 15
  else score += 5

  // 信噪比评分 (0-25分)
  if (snr > 40) score += 25
  else if (snr > 20) score += 15
  else score += 5

  return Math.min(100, score)
}

const getQualityClass = (score) => {
  if (score >= 80) return 'excellent'
  if (score >= 60) return 'good'
  if (score >= 40) return 'fair'
  return 'poor'
}

// 片段选择功能
const toggleSegmentSelector = () => {
  segmentVisible.value = !segmentVisible.value
}

const playSegment = () => {
  if (!audioRef.value) return

  audioRef.value.currentTime = segmentStart.value
  audioRef.value.play()

  // 设置定时器在结束时间停止播放
  const duration = (segmentEnd.value - segmentStart.value) * 1000
  setTimeout(() => {
    if (audioRef.value && !audioRef.value.paused) {
      audioRef.value.pause()
    }
  }, duration)
}

const exportSegment = () => {
  // 这里可以实现片段导出功能
  ElMessage.info('片段导出功能开发中...')
}

// 监听播放时间变化，更新波形图进度线
watch(currentTime, () => {
  if (waveformVisible.value && audioBuffer.value) {
    drawWaveform()
  }
})
</script>

<style scoped>
.audio-preview {
  background: var(--card-bg);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  border: 1px solid var(--border-color);
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.preview-header h4 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1.1rem;
}

.file-meta {
  display: flex;
  gap: var(--spacing-md);
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.audio-player {
  margin-bottom: var(--spacing-lg);
}

.audio-player audio {
  width: 100%;
  height: 40px;
}

.waveform-section,
.spectrum-section,
.quality-section,
.segment-section {
  margin-bottom: var(--spacing-lg);
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  font-weight: 500;
  color: var(--text-primary);
}

.waveform-container,
.spectrum-container,
.quality-container,
.segment-container {
  position: relative;
  background: var(--surface-bg);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
}

.waveform-canvas,
.spectrum-canvas {
  width: 100%;
  height: 200px;
  cursor: pointer;
  border-radius: var(--radius-sm);
}

.waveform-loading,
.spectrum-loading,
.quality-loading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--text-secondary);
  font-size: 0.9rem;
}

/* 质量分析样式 */
.quality-metrics {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.metric-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.metric-item label {
  min-width: 80px;
  font-size: 0.9rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.metric-bar {
  flex: 1;
  height: 8px;
  background: var(--border-color);
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.bar-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--success-color), var(--warning-color), var(--danger-color));
  transition: width 0.3s ease;
}

.metric-value {
  min-width: 60px;
  text-align: right;
  font-family: var(--font-mono);
  font-size: 0.9rem;
  color: var(--text-primary);
}

.quality-score {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-weight: 600;
  font-size: 0.9rem;
  text-align: center;
  min-width: 60px;
}

.quality-score.excellent {
  background: var(--success-color);
  color: white;
}

.quality-score.good {
  background: var(--info-color);
  color: white;
}

.quality-score.fair {
  background: var(--warning-color);
  color: white;
}

.quality-score.poor {
  background: var(--danger-color);
  color: white;
}

/* 片段选择样式 */
.segment-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
  flex-wrap: wrap;
}

.segment-info {
  font-size: 0.9rem;
  color: var(--text-secondary);
  padding: var(--spacing-sm);
  background: var(--input-bg);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
}

.audio-info {
  background: var(--surface-bg);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  border: 1px solid var(--border-color);
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-md);
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.info-item label {
  font-size: 0.85rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.info-item span {
  color: var(--text-primary);
  font-family: var(--font-mono);
}
</style>
