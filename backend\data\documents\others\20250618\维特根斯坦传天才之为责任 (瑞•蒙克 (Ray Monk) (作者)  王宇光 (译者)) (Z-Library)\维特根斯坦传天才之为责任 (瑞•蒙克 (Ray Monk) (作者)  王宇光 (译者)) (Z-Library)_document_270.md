出现证成(Justify)一种游戏的问题:如果人们能玩它,人们就理解它。语
法或句法也类似:“一条句法规则对应于游戏的一个局面……句法不能
被证成。”
但魏斯曼问,不能有某种游戏理论吗?例如有象棋理论;象棋理论告
诉我们,某一串行棋是否可能——比如,能否从一个给定的局面出发用八
步将死国王。“好吧,如果有一种象棋理论”,他加上,“那我看不出为什么
不该有算术游戏的理论,为什么我们不该从这一理论的命题那儿学到有
关这游戏的可能性的某些实质性的东西。这一理论就是希尔伯特的元
数学。”
不,维特根斯坦回答,所谓“象棋理论”自身是一种演算、一种游戏。
它用的是词和符号而非实际的棋子,但这件事不该误导我们:“我能用八
步走到那儿的证明,在于我实际上在这符号系统里走到了那儿,因而在于
用符号做用棋子在棋盘上做的事……而我们都赞同在木板上推动小木
片不是实质性的,不是吗?”。代数用字母而非实际的数字来计算,这并不
使代数成为算术的理论;代数只是另一种演算。
对维特根斯坦来说,清除了这种迷雾之后,不能再谈元理论、游戏理
论。只有游戏及玩游戏的人、规则及其应用:“我们不能为一条规则的
应用规定另一条规则。”为了把两样东西联系起来,并不总是需要第三
样东西:“事物必须用不着绳子而直接相连,即,它们必须已位于一种相互
联系之中了,就像链条的链环。”语词与其意义之间的联系不是在理论
中、而是在实践(practice)中、在语词的使用中找到的。不能用另一条
规则阐明规则与其应用之间、语词与行为之间的直接联系;这联系必须


被看出:“在这儿看出有着根本的重要性:只要你没看出新的系统,你就还
没掌握它。”维特根斯坦对理论的摈弃,并非像罗素认为的那样拒绝了
严肃的思考和理解的努力,而是采纳了一种对“要理解的是什么”的不同
观念——与他之前的斯宾格勒和歌德的观念一样,这种观念强调“端赖
于看出联系的理解”[188]的重要性和必要性。