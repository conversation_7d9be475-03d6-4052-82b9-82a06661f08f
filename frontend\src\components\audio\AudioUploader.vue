<template>
  <div class="audio-uploader">
    <el-upload
      ref="uploadRef"
      class="upload-area"
      :class="{ 'is-dragover': isDragover }"
      drag
      :multiple="mode === 'batch'"
      :accept="accept.join(',')"
      :before-upload="beforeUpload"
      :on-change="handleFileChange"
      :on-remove="handleFileRemove"
      :on-exceed="handleExceed"
      :limit="mode === 'single' ? 1 : 10"
      :auto-upload="false"
      :show-file-list="false"
      @dragover="isDragover = true"
      @dragleave="isDragover = false"
      @drop="isDragover = false"
    >
      <div class="upload-content">
        <div class="upload-icon">
          <el-icon size="48" color="var(--accent-primary)">
            <UploadFilled />
          </el-icon>
        </div>
        <div class="upload-text">
          <h3>{{ uploadText }}</h3>
          <p class="upload-hint">{{ uploadHint }}</p>
        </div>
        <div class="upload-formats">
          <span class="format-label">支持格式：</span>
          <span class="format-list">{{ accept.join(', ') }}</span>
        </div>
        <div class="upload-size">
          <span class="size-label">最大文件大小：{{ formatFileSize(maxSize) }}</span>
        </div>
      </div>
    </el-upload>

    <!-- 文件列表 -->
    <div class="file-list" v-if="fileList.length > 0">
      <div class="file-list-header">
        <h4>已选择文件 ({{ fileList.length }})</h4>
        <el-button text @click="clearFiles" :icon="Delete">清空</el-button>
      </div>
      
      <div class="file-items">
        <div
          v-for="(file, index) in fileList"
          :key="file.uid"
          class="file-item"
          :class="{ 'has-error': file.status === 'error' }"
        >
          <div class="file-info">
            <div class="file-icon">
              <el-icon><Headset /></el-icon>
            </div>
            <div class="file-details">
              <div class="file-name">{{ file.name }}</div>
              <div class="file-meta">
                <span class="file-size">{{ formatFileSize(file.size) }}</span>
                <span class="file-type">{{ getFileExtension(file.name) }}</span>
                <span v-if="file.duration" class="file-duration">{{ formatDuration(file.duration) }}</span>

                <!-- 上传进度 -->
                <div v-if="file.status === 'uploading'" class="file-progress">
                  <el-progress
                    :percentage="file.progress || 0"
                    :stroke-width="4"
                    :show-text="false"
                    color="var(--accent-primary)"
                  />
                  <span class="progress-text">{{ file.progress || 0 }}%</span>
                </div>

                <!-- 错误信息 -->
                <div v-if="file.status === 'error' && file.error" class="file-error">
                  <el-icon color="var(--danger-color)"><Warning /></el-icon>
                  <span class="error-text">{{ file.error }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <div class="file-actions">
            <!-- 上传控制按钮 -->
            <template v-if="file.status === 'uploading'">
              <el-button
                v-if="!pausedTasks.has(file.uid)"
                text
                @click="pauseUpload(file.uid)"
                :icon="VideoPause"
                title="暂停上传"
              />
              <el-button
                v-else
                text
                @click="resumeUpload(file.uid)"
                :icon="VideoPlay"
                title="恢复上传"
              />
              <el-button
                text
                @click="cancelUpload(file.uid)"
                :icon="Delete"
                title="取消上传"
              />
            </template>

            <!-- 重试按钮 -->
            <el-button
              v-if="file.status === 'error'"
              text
              @click="retryUpload(file, index)"
              :icon="RefreshRight"
              title="重试上传"
            />

            <!-- 预览和删除按钮 -->
            <template v-if="file.status !== 'uploading'">
              <el-button
                text
                @click="previewFile(file, index)"
                :icon="View"
                title="预览"
              />
              <el-button
                text
                @click="removeFile(index)"
                :icon="Delete"
                title="删除"
              />
            </template>
          </div>
          
          <!-- 文件状态 -->
          <div class="file-status">
            <el-icon v-if="file.status === 'success'" color="var(--success-color)">
              <CircleCheckFilled />
            </el-icon>
            <el-icon v-else-if="file.status === 'error'" color="var(--danger-color)">
              <CircleCloseFilled />
            </el-icon>
            <el-icon v-else color="var(--warning-color)">
              <Clock />
            </el-icon>
          </div>
        </div>
      </div>
    </div>

    <!-- 上传进度 -->
    <div class="upload-progress" v-if="isUploading">
      <div class="progress-header">
        <span>上传进度</span>
        <span>{{ Math.round(uploadProgress) }}%</span>
      </div>
      <el-progress
        :percentage="Math.round(uploadProgress)"
        :stroke-width="8"
        :show-text="false"
        color="var(--accent-primary)"
      />
      <div class="progress-details">
        <span>{{ uploadingFileName }}</span>
        <span v-if="uploadSpeed">{{ uploadSpeed }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  UploadFilled,
  Headset,
  Delete,
  View,
  CircleCheckFilled,
  CircleCloseFilled,
  Clock,
  VideoPause,
  VideoPlay,
  RefreshRight,
  Warning
} from '@element-plus/icons-vue'
import { audioFileAPI } from '@/api/audioProcessing'

// Props
const props = defineProps({
  mode: {
    type: String,
    default: 'single',
    validator: (value) => ['single', 'batch'].includes(value)
  },
  accept: {
    type: Array,
    default: () => ['.wav', '.mp3', '.m4a', '.aac', '.flac', '.ogg']
  },
  maxSize: {
    type: Number,
    default: 200 * 1024 * 1024 // 200MB
  },
  chunkSize: {
    type: Number,
    default: 5 * 1024 * 1024 // 5MB chunks for large files
  },
  enableChunkedUpload: {
    type: Boolean,
    default: true
  },
  enableResume: {
    type: Boolean,
    default: true
  },
  autoUpload: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits([
  'files-selected',
  'upload-progress',
  'file-preview',
  'file-remove',
  'upload-start',
  'upload-success',
  'upload-error',
  'upload-pause',
  'upload-resume',
  'upload-cancel'
])

// 响应式数据
const uploadRef = ref()
const fileList = ref([])
const isDragover = ref(false)
const isUploading = ref(false)
const uploadProgress = ref(0)
const uploadingFileName = ref('')
const uploadSpeed = ref('')
const uploadTasks = ref(new Map()) // 存储上传任务
const pausedTasks = ref(new Set()) // 暂停的任务
const failedTasks = ref(new Map()) // 失败的任务，用于重试

// 上传统计
const uploadStats = ref({
  startTime: 0,
  uploadedBytes: 0,
  totalBytes: 0,
  lastUpdateTime: 0,
  lastUploadedBytes: 0
})

// 计算属性
const uploadText = computed(() => {
  if (props.mode === 'single') {
    return '拖拽音频文件到此处或点击选择'
  }
  return '拖拽多个音频文件到此处或点击选择'
})

const uploadHint = computed(() => {
  if (props.mode === 'single') {
    return '支持单个音频文件上传'
  }
  return '支持批量音频文件上传，最多10个文件'
})

// 工具函数
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getFileExtension = (filename) => {
  return filename.split('.').pop().toUpperCase()
}

const formatDuration = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

// 增强的文件验证
const validateAudioFile = (file) => {
  const errors = []
  const warnings = []

  // 检查文件类型
  const audioTypes = [
    'audio/wav', 'audio/wave', 'audio/x-wav',
    'audio/mpeg', 'audio/mp3',
    'audio/mp4', 'audio/m4a',
    'audio/aac',
    'audio/flac',
    'audio/ogg', 'audio/vorbis'
  ]

  const isValidType = audioTypes.includes(file.type) || props.accept.some(ext =>
    file.name.toLowerCase().endsWith(ext.toLowerCase())
  )

  if (!isValidType) {
    errors.push(`不支持的文件格式: ${getFileExtension(file.name)}`)
  }

  // 检查文件大小
  if (file.size > props.maxSize) {
    errors.push(`文件大小超过限制: ${formatFileSize(props.maxSize)}`)
  }

  // 检查文件名
  if (file.name.length > 255) {
    errors.push('文件名过长，请重命名后重试')
  }

  // 检查特殊字符
  if (/[<>:"/\\|?*]/.test(file.name)) {
    warnings.push('文件名包含特殊字符，可能影响处理')
  }

  // 大文件警告
  if (file.size > 100 * 1024 * 1024) { // 100MB
    warnings.push('文件较大，上传可能需要较长时间')
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

const isAudioFile = (file) => {
  return validateAudioFile(file).isValid
}

// 分片上传功能
const uploadLargeFile = async (file, onProgress) => {
  const fileId = generateFileId()
  const totalChunks = Math.ceil(file.size / props.chunkSize)
  let uploadedChunks = 0

  try {
    // 检查是否有未完成的上传任务（断点续传）
    const resumeData = props.enableResume ? getResumeData(file) : null
    const startChunk = resumeData ? resumeData.uploadedChunks : 0

    for (let i = startChunk; i < totalChunks; i++) {
      // 检查是否被暂停
      if (pausedTasks.value.has(fileId)) {
        saveResumeData(file, i)
        throw new Error('Upload paused')
      }

      const start = i * props.chunkSize
      const end = Math.min(start + props.chunkSize, file.size)
      const chunk = file.slice(start, end)

      const formData = new FormData()
      formData.append('chunk', chunk)
      formData.append('chunkIndex', i)
      formData.append('totalChunks', totalChunks)
      formData.append('fileId', fileId)
      formData.append('fileName', file.name)
      formData.append('fileSize', file.size)

      await uploadChunk(formData)
      uploadedChunks = i + 1

      // 更新进度
      const progress = Math.round((uploadedChunks / totalChunks) * 100)
      onProgress && onProgress(progress)
    }

    // 完成上传，合并文件
    const result = await mergeChunks(fileId, file.name, totalChunks)
    clearResumeData(file)
    return result

  } catch (error) {
    if (error.message !== 'Upload paused') {
      // 保存失败信息用于重试
      failedTasks.value.set(fileId, {
        file,
        uploadedChunks,
        totalChunks,
        error: error.message
      })
    }
    throw error
  }
}

const uploadChunk = async (formData) => {
  const response = await fetch('/api/v1/audio/upload/chunk', {
    method: 'POST',
    body: formData,
    headers: {
      'Authorization': `Bearer ${getAuthToken()}`
    }
  })

  if (!response.ok) {
    throw new Error(`Chunk upload failed: ${response.statusText}`)
  }

  return response.json()
}

const mergeChunks = async (fileId, fileName, totalChunks) => {
  const response = await fetch('/api/v1/audio/upload/merge', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${getAuthToken()}`
    },
    body: JSON.stringify({
      fileId,
      fileName,
      totalChunks
    })
  })

  if (!response.ok) {
    throw new Error(`File merge failed: ${response.statusText}`)
  }

  return response.json()
}

// 断点续传相关
const getResumeData = (file) => {
  const key = `upload_resume_${file.name}_${file.size}`
  const data = localStorage.getItem(key)
  return data ? JSON.parse(data) : null
}

const saveResumeData = (file, uploadedChunks) => {
  const key = `upload_resume_${file.name}_${file.size}`
  localStorage.setItem(key, JSON.stringify({
    uploadedChunks,
    timestamp: Date.now()
  }))
}

const clearResumeData = (file) => {
  const key = `upload_resume_${file.name}_${file.size}`
  localStorage.removeItem(key)
}

const generateFileId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substring(2)
}

const getAuthToken = () => {
  // 从store或localStorage获取认证token
  return localStorage.getItem('auth_token') || ''
}

// 上传速度计算
const updateUploadSpeed = (progress) => {
  const now = Date.now()
  const currentBytes = Math.round((progress / 100) * uploadStats.value.totalBytes)

  // 至少间隔1秒才更新速度
  if (now - uploadStats.value.lastUpdateTime >= 1000) {
    const timeDiff = (now - uploadStats.value.lastUpdateTime) / 1000 // 秒
    const bytesDiff = currentBytes - uploadStats.value.lastUploadedBytes

    if (timeDiff > 0 && bytesDiff > 0) {
      const speed = bytesDiff / timeDiff // bytes/second
      uploadSpeed.value = formatUploadSpeed(speed)
    }

    uploadStats.value.lastUpdateTime = now
    uploadStats.value.lastUploadedBytes = currentBytes
  }

  uploadStats.value.uploadedBytes = currentBytes
}

const formatUploadSpeed = (bytesPerSecond) => {
  if (bytesPerSecond < 1024) {
    return `${Math.round(bytesPerSecond)} B/s`
  } else if (bytesPerSecond < 1024 * 1024) {
    return `${Math.round(bytesPerSecond / 1024)} KB/s`
  } else {
    return `${(bytesPerSecond / (1024 * 1024)).toFixed(1)} MB/s`
  }
}

// 文件去重函数
const isDuplicateFile = (newFile, existingFiles) => {
  return existingFiles.some(existingFile => {
    return existingFile.name === newFile.name &&
           existingFile.size === newFile.size &&
           existingFile.file?.lastModified === newFile.lastModified
  })
}

const addFileToList = (fileInfo) => {
  // 检查是否为重复文件
  if (isDuplicateFile(fileInfo, fileList.value)) {
    ElMessage.warning(`文件 "${fileInfo.name}" 已存在，跳过重复上传`)
    return false
  }

  if (props.mode === 'single') {
    fileList.value = [fileInfo]
  } else {
    fileList.value.push(fileInfo)
  }

  return true
}

// 事件处理
const beforeUpload = (file) => {
  const validation = validateAudioFile(file)

  if (!validation.isValid) {
    validation.errors.forEach(error => {
      ElMessage.error(error)
    })
    return false
  }

  // 显示警告
  if (validation.warnings.length > 0) {
    validation.warnings.forEach(warning => {
      ElMessage.warning(warning)
    })
  }

  return true
}

const handleFileChange = async (file, files) => {
  if (file.status === 'ready') {
    // 添加文件到列表
    const fileInfo = {
      uid: file.uid,
      name: file.name,
      size: file.size,
      type: file.raw.type,
      file: file.raw,
      status: 'ready',
      progress: 0,
      fileId: null,
      lastModified: file.raw.lastModified
    }

    // 尝试获取音频时长
    getAudioDuration(file.raw).then(duration => {
      fileInfo.duration = duration
    })

    // 使用去重函数添加文件
    const added = addFileToList(fileInfo)
    if (!added) {
      // 如果是重复文件，直接返回，不进行上传
      return
    }

    // 立即上传文件获取file_id
    try {
      fileInfo.status = 'uploading'
      isUploading.value = true
      uploadingFileName.value = fileInfo.name
      uploadProgress.value = 0

      // 初始化上传统计
      uploadStats.value = {
        startTime: Date.now(),
        uploadedBytes: 0,
        totalBytes: fileInfo.size,
        lastUpdateTime: Date.now(),
        lastUploadedBytes: 0
      }

      emit('files-selected', fileList.value)

      // 调用上传API
      const result = await audioFileAPI.uploadAudio(file.raw, (progress) => {
        // 更新文件进度
        fileInfo.progress = progress

        // 更新全局上传进度
        uploadProgress.value = progress

        // 计算上传速度
        updateUploadSpeed(progress)

        emit('upload-progress', { file: fileInfo, progress })
      })

      fileInfo.status = 'success'
      fileInfo.result = result
      fileInfo.fileId = result.file_id || result.data?.file_id

      // 重置上传状态
      isUploading.value = false
      uploadProgress.value = 100
      uploadingFileName.value = ''
      uploadSpeed.value = ''

      emit('upload-success', { file: fileInfo, result })

    } catch (error) {
      fileInfo.status = 'error'
      fileInfo.error = error.message

      // 重置上传状态
      isUploading.value = false
      uploadProgress.value = 0
      uploadingFileName.value = ''
      uploadSpeed.value = ''

      emit('upload-error', { file: fileInfo, error })
    }

    emit('files-selected', fileList.value)
  }
}

const handleFileRemove = (file) => {
  const index = fileList.value.findIndex(f => f.uid === file.uid)
  if (index > -1) {
    fileList.value.splice(index, 1)
    emit('files-selected', fileList.value)
  }
}

const handleExceed = (files) => {
  const limit = props.mode === 'single' ? 1 : 10
  ElMessage.warning(`最多只能选择 ${limit} 个文件`)
}

const removeFile = (index) => {
  const file = fileList.value[index]
  fileList.value.splice(index, 1)
  emit('file-remove', file, index)
  emit('files-selected', fileList.value)
}

const previewFile = (file, index) => {
  emit('file-preview', file, index)
}

const clearFiles = () => {
  fileList.value = []
  uploadRef.value?.clearFiles()
  emit('files-selected', [])
}

// 上传控制功能
const pauseUpload = (fileId) => {
  pausedTasks.value.add(fileId)
  emit('upload-pause', fileId)
  ElMessage.info('上传已暂停')
}

const resumeUpload = async (fileId) => {
  pausedTasks.value.delete(fileId)
  emit('upload-resume', fileId)

  // 查找对应的文件并恢复上传
  const fileIndex = fileList.value.findIndex(f => f.uid === fileId)
  if (fileIndex > -1) {
    const file = fileList.value[fileIndex]
    try {
      await startFileUpload(file, fileIndex)
    } catch (error) {
      ElMessage.error(`恢复上传失败: ${error.message}`)
    }
  }
}

const cancelUpload = (fileId) => {
  pausedTasks.value.delete(fileId)
  uploadTasks.value.delete(fileId)

  // 更新文件状态
  const fileIndex = fileList.value.findIndex(f => f.uid === fileId)
  if (fileIndex > -1) {
    fileList.value[fileIndex].status = 'ready'
    fileList.value[fileIndex].progress = 0
  }

  emit('upload-cancel', fileId)
  ElMessage.info('上传已取消')
}

const retryUpload = async (file, index) => {
  // 清除失败状态
  failedTasks.value.delete(file.uid)
  file.status = 'ready'
  file.progress = 0

  try {
    await startFileUpload(file, index)
  } catch (error) {
    ElMessage.error(`重试上传失败: ${error.message}`)
  }
}

// 开始文件上传
const startFileUpload = async (file, index) => {
  file.status = 'uploading'
  file.progress = 0
  isUploading.value = true
  uploadingFileName.value = file.name
  uploadProgress.value = 0

  // 初始化上传统计
  uploadStats.value = {
    startTime: Date.now(),
    uploadedBytes: 0,
    totalBytes: file.size,
    lastUpdateTime: Date.now(),
    lastUploadedBytes: 0
  }

  emit('upload-start', file)

  try {
    let result

    // 根据文件大小选择上传方式
    if (props.enableChunkedUpload && file.size > props.chunkSize) {
      // 大文件分片上传
      result = await uploadLargeFile(file.file, (progress) => {
        file.progress = progress
        uploadProgress.value = progress
        updateUploadSpeed(progress)
        emit('upload-progress', { file, progress })
      })
    } else {
      // 小文件直接上传
      result = await audioFileAPI.uploadAudio(file.file, (progress) => {
        file.progress = progress
        uploadProgress.value = progress
        updateUploadSpeed(progress)
        emit('upload-progress', { file, progress })
      })
    }

    file.status = 'success'
    file.result = result
    file.fileId = result.file_id || result.data?.file_id

    // 重置上传状态
    isUploading.value = false
    uploadProgress.value = 100
    uploadingFileName.value = ''
    uploadSpeed.value = ''

    emit('upload-success', { file, result })
    ElMessage.success(`${file.name} 上传成功`)

  } catch (error) {
    file.status = 'error'
    file.error = error.message

    // 重置上传状态
    isUploading.value = false
    uploadProgress.value = 0
    uploadingFileName.value = ''
    uploadSpeed.value = ''

    emit('upload-error', { file, error })
    ElMessage.error(`${file.name} 上传失败: ${error.message}`)
  }
}

// 批量上传
const startBatchUpload = async () => {
  const readyFiles = fileList.value.filter(f => f.status === 'ready')

  if (readyFiles.length === 0) {
    ElMessage.warning('没有可上传的文件')
    return
  }

  isUploading.value = true

  try {
    // 并发上传，但限制并发数
    const concurrency = 3
    const chunks = []

    for (let i = 0; i < readyFiles.length; i += concurrency) {
      chunks.push(readyFiles.slice(i, i + concurrency))
    }

    for (const chunk of chunks) {
      await Promise.all(
        chunk.map((file, index) => startFileUpload(file, index))
      )
    }

    ElMessage.success('批量上传完成')

  } catch (error) {
    ElMessage.error(`批量上传失败: ${error.message}`)
  } finally {
    isUploading.value = false
  }
}

// 暴露方法给父组件
defineExpose({
  startBatchUpload,
  clearFiles,
  pauseUpload,
  resumeUpload,
  cancelUpload,
  retryUpload
})

// 获取音频时长
const getAudioDuration = (file) => {
  return new Promise((resolve) => {
    const audio = new Audio()
    const url = URL.createObjectURL(file)
    
    audio.addEventListener('loadedmetadata', () => {
      resolve(audio.duration)
      URL.revokeObjectURL(url)
    })
    
    audio.addEventListener('error', () => {
      resolve(0)
      URL.revokeObjectURL(url)
    })
    
    audio.src = url
  })
}

// 监听文件列表变化
watch(() => fileList.value.length, (newLength) => {
  if (newLength === 0) {
    uploadRef.value?.clearFiles()
  }
})
</script>

<style scoped>
.audio-uploader {
  width: 100%;
}

.upload-area {
  width: 100%;
}

.upload-area :deep(.el-upload) {
  width: 100%;
}

.upload-area :deep(.el-upload-dragger) {
  width: 100%;
  height: auto;
  min-height: 200px;
  border: 2px dashed var(--border-color);
  border-radius: var(--radius-lg);
  background: var(--input-bg);
  transition: all 0.3s ease;
}

.upload-area.is-dragover :deep(.el-upload-dragger) {
  border-color: var(--accent-primary);
  background: rgba(88, 166, 255, 0.1);
}

.upload-content {
  padding: var(--spacing-xl);
  text-align: center;
}

.upload-icon {
  margin-bottom: var(--spacing-lg);
}

.upload-text h3 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
  font-size: 1.2rem;
}

.upload-hint {
  margin: 0 0 var(--spacing-lg) 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.upload-formats,
.upload-size {
  margin-bottom: var(--spacing-sm);
  font-size: 0.85rem;
  color: var(--text-muted);
}

.format-label,
.size-label {
  font-weight: 500;
}

.file-list {
  margin-top: var(--spacing-lg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--card-bg);
}

.file-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background: var(--surface-bg);
}

.file-list-header h4 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1rem;
}

.file-items {
  max-height: 120px;
  overflow-y: auto;
}

.file-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  transition: background-color 0.2s ease;
}

.file-item:hover {
  background: var(--surface-hover);
}

.file-item:last-child {
  border-bottom: none;
}

.file-item.has-error {
  background: rgba(248, 81, 73, 0.1);
}

.file-info {
  display: flex;
  align-items: center;
  flex: 1;
  gap: var(--spacing-md);
}

.file-icon {
  color: var(--accent-primary);
  font-size: 1.5rem;
}

.file-details {
  flex: 1;
}

.file-name {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.file-meta {
  display: flex;
  gap: var(--spacing-md);
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.file-actions {
  display: flex;
  gap: var(--spacing-sm);
  margin-right: var(--spacing-md);
}

.file-status {
  font-size: 1.2rem;
}

.upload-progress {
  margin-top: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--card-bg);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
  font-size: 0.9rem;
  color: var(--text-secondary);
}

/* 文件进度样式 */
.file-progress {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-xs);
}

.file-progress .el-progress {
  flex: 1;
  min-width: 100px;
}

.progress-text {
  font-size: 0.8rem;
  color: var(--text-secondary);
  min-width: 35px;
}

/* 错误信息样式 */
.file-error {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-xs);
  padding: var(--spacing-xs);
  background: rgba(248, 81, 73, 0.1);
  border-radius: var(--radius-sm);
}

.error-text {
  font-size: 0.8rem;
  color: var(--danger-color);
}

/* 上传控制按钮样式 */
.file-actions {
  display: flex;
  gap: var(--spacing-xs);
  margin-right: var(--spacing-md);
}

.file-actions .el-button {
  padding: var(--spacing-xs);
}

/* 拖拽状态增强 */
.upload-area.is-dragover :deep(.el-upload-dragger) {
  border-color: var(--accent-primary);
  background: rgba(88, 166, 255, 0.1);
  transform: scale(1.02);
}

/* 文件状态指示器 */
.file-status {
  font-size: 1.2rem;
  display: flex;
  align-items: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .file-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .file-actions {
    margin-right: 0;
    align-self: flex-end;
  }

  .file-progress {
    width: 100%;
  }
}

/* 全局上传进度样式 */
.upload-progress {
  margin-top: 16px;
  padding: 16px;
  background: var(--bg-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

.progress-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
  font-size: 12px;
  color: var(--text-secondary);
}

.progress-details span:first-child {
  font-weight: 500;
  color: var(--text-primary);
  max-width: 60%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.progress-details span:last-child {
  color: var(--accent-primary);
  font-weight: 500;
}
</style>
