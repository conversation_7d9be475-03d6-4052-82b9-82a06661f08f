#!/usr/bin/env python3
"""
测试会议转录音频预处理集成功能
验证第一阶段和第二阶段的修改是否正常工作
"""

import os
import sys
import time
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_meeting_transcription_with_preprocessing():
    """测试会议转录的音频预处理集成功能"""
    print("🎤 测试会议转录音频预处理集成功能...")
    
    try:
        # 导入必要的模块
        from backend.tasks.audio_processing_tasks import _perform_meeting_transcription
        from backend.utils.audio.speech_recognition_core import create_speech_recognizer
        from backend.utils.audio.speech_recognition_utils import load_vad_model
        
        # 测试音频文件
        test_audio = "resource/对话.mp3"
        if not os.path.exists(test_audio):
            print(f"⚠️ 测试音频不存在: {test_audio}")
            return False
        
        # 模型路径
        model_path = "./models/SenseVoiceSmall"
        vad_model_path = "./models/model_dir/fsmn_vad_zh"
        
        if not os.path.exists(model_path):
            print(f"⚠️ 语音识别模型路径不存在: {model_path}")
            return False
        
        # 进度回调函数
        progress_log = []
        def progress_callback(progress, message, stage):
            progress_log.append(f"[{progress:.1f}%] {stage}: {message}")
            print(f"  进度: {progress:.1f}% - {message}")
        
        print("📋 初始化模型...")
        
        # 加载VAD模型
        try:
            vad_model = load_vad_model(vad_model_path)
            print("✅ VAD模型加载成功")
        except Exception as e:
            print(f"❌ VAD模型加载失败: {e}")
            return False
        
        # 创建语音识别器
        try:
            recognizer = create_speech_recognizer(model_path)
            print("✅ 语音识别器创建成功")
        except Exception as e:
            print(f"❌ 语音识别器创建失败: {e}")
            return False
        
        # 配置参数
        config = {
            'merge_length_s': 15,
            'min_speech_duration': 0.5,
            'max_speech_duration': 60,
            'threshold': 0.5
        }
        
        print("🚀 开始会议转录测试...")
        start_time = time.time()
        
        # 执行会议转录（包含音频预处理）
        result = _perform_meeting_transcription(
            file_path=test_audio,
            vad_model=vad_model,
            recognizer=recognizer,
            speaker_recognizer=None,  # 暂时不测试说话人识别
            language="auto",
            output_format="text",
            include_timestamps=True,
            speaker_labeling=False,
            config=config,
            progress_callback=progress_callback,
            start_progress=0.0,
            end_progress=100.0
        )
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        print(f"⏱️ 处理耗时: {processing_time:.2f}秒")
        
        # 验证结果
        if result and 'text' in result:
            print("✅ 会议转录成功")
            print(f"📝 识别文本: {result['text'][:100]}...")
            print(f"🎯 语言: {result.get('language', 'unknown')}")
            print(f"📊 置信度: {result.get('confidence', 0):.2f}")
            print(f"⏰ 总时长: {result.get('total_duration', 0):.2f}秒")
            
            # 检查进度日志
            print("\n📋 进度日志:")
            for log in progress_log:
                print(f"  {log}")
            
            # 验证是否包含预处理步骤
            preprocessing_found = any("预处理" in log for log in progress_log)
            if preprocessing_found:
                print("✅ 音频预处理步骤已集成")
            else:
                print("⚠️ 未检测到音频预处理步骤")
            
            return True
        else:
            print("❌ 会议转录失败，结果为空")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_preprocessing_file_cleanup():
    """测试预处理临时文件清理功能"""
    print("\n🧹 测试预处理临时文件清理功能...")
    
    # 检查临时目录中是否有残留的预处理文件
    temp_dir = tempfile.gettempdir()
    temp_files_before = len([f for f in os.listdir(temp_dir) if f.startswith('tmp') and f.endswith('.wav')])
    
    print(f"📁 测试前临时文件数量: {temp_files_before}")
    
    # 运行会议转录测试
    success = test_meeting_transcription_with_preprocessing()
    
    # 等待一段时间确保清理完成
    time.sleep(2)
    
    # 再次检查临时文件
    temp_files_after = len([f for f in os.listdir(temp_dir) if f.startswith('tmp') and f.endswith('.wav')])
    
    print(f"📁 测试后临时文件数量: {temp_files_after}")
    
    if temp_files_after <= temp_files_before:
        print("✅ 临时文件清理正常")
        return True
    else:
        print("⚠️ 可能存在临时文件泄漏")
        return False

def main():
    """主测试函数"""
    print("🎯 会议转录音频预处理集成测试")
    print("=" * 60)
    
    # 测试1: 基本功能测试
    test1_success = test_meeting_transcription_with_preprocessing()
    
    # 测试2: 文件清理测试
    test2_success = test_preprocessing_file_cleanup()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"  会议转录功能: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"  文件清理功能: {'✅ 通过' if test2_success else '❌ 失败'}")
    
    if test1_success and test2_success:
        print("🎉 所有测试通过！音频预处理集成成功")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步调试")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
