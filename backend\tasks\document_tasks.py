"""
文档处理异步任务
"""

import asyncio
import json
import sys
from pathlib import Path
from typing import Dict, Any, Optional
from celery import current_app
from datetime import datetime, timezone
from loguru import logger

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

try:
    from backend.core.task_queue import celery_app
    from backend.tasks.base_task import BaseTask, ProgressCallback
    from backend.services.document_service import document_processor
    from backend.services.document_db_service import document_db_service
    from backend.core.database import get_db_session
except ImportError:
    from core.task_queue import celery_app
    from tasks.base_task import BaseTask, ProgressCallback
    from services.document_service import document_processor
    from services.document_db_service import document_db_service
    from core.database import get_db_session


@celery_app.task(bind=True, base=BaseTask)
def process_document_task(
    self,
    task_id: str,
    user_id: str,
    filename: str,
    file_key: str,
    document_id: Optional[int] = None,
    use_ocr: bool = False,
    ocr_config: Optional[Dict] = None
):
    """异步处理文档任务"""
    
    progress_callback = ProgressCallback(task_id, self)
    
    try:
        # 1. 初始化进度
        progress_callback(5, "开始处理文档...", "initializing")
        
        # 2. 从Redis获取文件内容（使用二进制客户端）
        progress_callback(10, "读取文件数据...", "loading")
        file_content = self.redis_binary_client.get(file_key)
        if not file_content:
            raise Exception("文件数据不存在或已过期")

        # 确保是bytes类型（Redis返回的应该已经是bytes）
        if not isinstance(file_content, bytes):
            raise Exception(f"文件数据类型错误: {type(file_content)}")
        
        # 3. 验证文件
        progress_callback(15, "验证文件格式...", "validating")
        if not document_processor.is_supported_format(filename):
            raise Exception(f"不支持的文件格式: {filename}")
        
        file_size = len(file_content)
        max_size = 50 * 1024 * 1024  # 50MB
        if file_size > max_size:
            raise Exception(f"文件大小超过限制（最大{max_size // (1024*1024)}MB）")
        
        # 4. 处理文档
        progress_callback(20, "开始文档处理...", "analysis")

        # 创建进度回调函数
        def processing_progress(percentage: float, detail: str = ""):
            # 将处理进度映射到20%-80%的范围，并根据进度设置正确的stage
            mapped_percentage = 20 + (percentage / 100) * 60

            # 🔧 修复：根据进度百分比设置正确的stage
            if percentage < 50:
                stage = "analysis"  # 文档分析阶段
            elif percentage < 70:
                stage = "ocr" if use_ocr else "analysis"  # OCR处理或继续分析
            elif percentage < 85:
                stage = "splitting"  # 智能切分阶段
            else:
                stage = "indexing"  # 索引建立阶段

            progress_callback(mapped_percentage, detail, stage)
        
        # 异步处理文档
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            result = loop.run_until_complete(
                document_processor.process_file_with_splitting(
                    file_content,
                    filename,
                    ocr_settings=ocr_config,
                    progress_callback=processing_progress
                )
            )
        finally:
            loop.close()
        
        if not result["success"]:
            raise Exception(result.get("error", "文档处理失败"))
        
        # 5. 保存到数据库
        progress_callback(85, "保存到数据库...", "saving")
        
        db = get_db_session()
        try:
            sections_data = result.get("section_details", [])

            if document_id:
                # 更新现有文档
                document_data = {
                    "file_type": result.get("file_type", "unknown"),
                    "document_type": result.get("document_type", "general"),
                    "status": "completed",
                    "processing_progress": 1.0,
                    "processed_at": datetime.now(timezone.utc),
                    "sections_count": len(sections_data),
                    "total_characters": sum(len(s.get("content", "")) for s in sections_data),
                    "total_words": sum(len(s.get("content", "").split()) for s in sections_data),
                    "average_section_length": sum(len(s.get("content", "")) for s in sections_data) // max(len(sections_data), 1),
                    "used_ocr": use_ocr,
                    "ocr_settings": json.dumps(ocr_config) if ocr_config else None,
                    "processing_settings": {"method": "intelligent_splitting", "source": "task_queue"}
                }

                document_db_service.update_document(db, document_id, user_id, document_data)
            else:
                # 创建新文档（向后兼容）
                document_data = {
                    "filename": filename,
                    "original_filename": filename,
                    "file_type": result.get("file_type", "unknown"),
                    "file_size": file_size,
                    "document_type": result.get("document_type", "general"),
                    "status": "completed",
                    "processing_progress": 1.0,
                    "processed_at": datetime.now(timezone.utc),
                    "sections_count": len(sections_data),
                    "total_characters": sum(len(s.get("content", "")) for s in sections_data),
                    "total_words": sum(len(s.get("content", "").split()) for s in sections_data),
                    "average_section_length": sum(len(s.get("content", "")) for s in sections_data) // max(len(sections_data), 1),
                    "used_ocr": use_ocr,
                    "ocr_settings": json.dumps(ocr_config) if ocr_config else None,
                    "processing_settings": {"method": "intelligent_splitting", "source": "task_queue"}
                }

                document = document_db_service.create_document(db, user_id, document_data)
                document_id = document.id

            # 创建文档节点
            if sections_data:
                # 转换节点数据格式
                formatted_sections = []
                for i, section in enumerate(sections_data):
                    formatted_section = {
                        "title": section.get("title", f"第{i+1}节"),
                        "content": section.get("content", ""),
                        "section_type": "content",
                        "section_index": i,
                        "char_count": len(section.get("content", "")),
                        "word_count": len(section.get("content", "").split())
                    }
                    formatted_sections.append(formatted_section)

                document_db_service.create_document_sections(db, document_id, formatted_sections)
            
            # 添加处理日志
            document_db_service.add_processing_log(
                db, document_id, "INFO",
                f"文档处理完成，生成 {len(formatted_sections)} 个节点",
                "task_queue_processing"
            )

            # 在提交数据库之前先更新进度为完成状态
            progress_callback(100, "文档处理完成！", "completed")

            # 最后提交数据库事务
            db.commit()

        except Exception as e:
            db.rollback()
            # 确保在异常情况下也更新进度状态
            try:
                progress_callback(0, f"处理失败: {str(e)}", "failed")
            except:
                pass  # 忽略进度更新失败
            raise e
        finally:
            db.close()
        
        return {
            "success": True,
            "document_id": document_id,
            "filename": filename,
            "file_size": file_size,
            "char_count": result.get("char_count", 0),
            "sections_count": len(formatted_sections) if 'formatted_sections' in locals() else 0,
            "processing_time": result.get("processing_time", 0),
            "message": "文档处理完成"
        }
        
    except Exception as e:
        error_message = str(e)
        logger.error(f"文档处理任务失败: {task_id}, {error_message}")
        
        # 更新进度为失败状态
        progress_callback(0, f"处理失败: {error_message}", "failed")
        
        # 重新抛出异常，让Celery处理
        raise e


@celery_app.task(bind=True, base=BaseTask)
def batch_process_documents_task(
    self,
    task_id: str,
    user_id: str,
    file_list: list,
    processing_options: Dict[str, Any]
):
    """批量处理文档任务"""
    
    progress_callback = ProgressCallback(task_id, self)
    
    try:
        progress_callback(5, "开始批量处理...", "initializing")
        
        total_files = len(file_list)
        processed_files = []
        failed_files = []
        
        for i, file_info in enumerate(file_list):
            try:
                # 计算当前文件的进度范围
                file_start = 10 + (i * 80 // total_files)
                file_end = 10 + ((i + 1) * 80 // total_files)
                
                progress_callback(
                    file_start, 
                    f"处理文件 {i+1}/{total_files}: {file_info['filename']}", 
                    "processing"
                )
                
                # 处理单个文件
                result = process_document_task.apply(
                    args=[
                        f"{task_id}_file_{i}",
                        user_id,
                        file_info['filename'],
                        file_info['file_key'],
                        None,  # document_id - 批量处理时为None，会创建新文档
                        processing_options.get('use_ocr', False),
                        processing_options.get('ocr_config')
                    ]
                ).get()
                
                processed_files.append({
                    "filename": file_info['filename'],
                    "document_id": result['document_id'],
                    "success": True
                })
                
                progress_callback(file_end, f"完成文件: {file_info['filename']}", "processing")
                
            except Exception as e:
                failed_files.append({
                    "filename": file_info['filename'],
                    "error": str(e),
                    "success": False
                })
                
                logger.error(f"批量处理中文件失败: {file_info['filename']}, {e}")
        
        # 完成
        progress_callback(100, "批量处理完成！", "completed")
        
        return {
            "success": True,
            "total_files": total_files,
            "processed_count": len(processed_files),
            "failed_count": len(failed_files),
            "processed_files": processed_files,
            "failed_files": failed_files,
            "message": f"批量处理完成，成功 {len(processed_files)} 个，失败 {len(failed_files)} 个"
        }
        
    except Exception as e:
        error_message = str(e)
        logger.error(f"批量处理任务失败: {task_id}, {error_message}")
        progress_callback(0, f"批量处理失败: {error_message}", "failed")
        raise e
