@echo off
echo ========================================
echo 重建Docker镜像 - 修复Redis和FunASR问题
echo ========================================

cd /d "%~dp0"

echo.
echo [1/5] 停止现有容器...
docker-compose -f my_notebook_docker/docker-compose.yml down

echo.
echo [2/5] 清理旧镜像...
docker rmi mynotebook-celery:20250721 2>nul
docker rmi mynotebook-backend:20250721 2>nul

echo.
echo [3/5] 重建Celery镜像...
cd my_notebook_docker
docker build -f Dockerfile.celery -t mynotebook-celery:20250721 ..
if %ERRORLEVEL% neq 0 (
    echo ❌ Celery镜像构建失败
    pause
    exit /b 1
)

echo.
echo [4/5] 重建Backend镜像...
docker build -f Dockerfile.backend -t mynotebook-backend:20250721 ..
if %ERRORLEVEL% neq 0 (
    echo ❌ Backend镜像构建失败
    pause
    exit /b 1
)

echo.
echo [5/5] 启动服务...
docker-compose up -d

echo.
echo ✅ Docker重建完成！
echo.
echo 查看日志命令：
echo   docker-compose -f my_notebook_docker/docker-compose.yml logs -f celery-worker
echo   docker-compose -f my_notebook_docker/docker-compose.yml logs -f backend
echo.
echo 验证修复效果：
echo   python test_docker_fixes.py
echo.

pause
