# 核心功能模块

## 音频处理模块

### 语音识别 (ASR)
- **模型**: SenseVoiceSmall (FunASR)
- **功能**: 音频转文字、多语言支持
- **任务**: `speech_recognition_task`
- **配置**: GPU加速、批量处理
- **离线优化**: 完全本地化加载，无网络依赖

### 语音活动检测 (VAD)
- **模型**: fsmn_vad_zh
- **功能**: 检测语音段落、去除静音
- **任务**: `vad_detection_task`
- **优化**: 音频预处理、噪声过滤
- **离线配置**: 使用本地模型路径，避免ModelScope下载

### 说话人识别
- **模型**: cam++ (说话人嵌入)
- **功能**: 说话人聚类、身份识别
- **任务**: `speaker_recognition_task`
- **特性**: 无监督聚类、多说话人分离
- **离线优化**: 完全本地化配置，性能提升显著

### 会议转录
- **功能**: 多说话人会议记录
- **任务**: `meeting_transcription_task`
- **流程**: VAD → ASR → 说话人识别 → 时间戳对齐
- **核心修复**: 文本-时间-说话人三元组关联算法优化
- **性能提升**: 离线配置后处理时间从15.5秒降至3.8秒

## 离线模型配置系统

### 环境变量管理
- **离线变量**: HF_HUB_OFFLINE, TRANSFORMERS_OFFLINE等
- **函数**: `_setup_offline_environment()`
- **作用**: 确保完全离线运行，无网络请求

### 模型路径验证
- **验证函数**: `_validate_all_model_paths()`
- **检查项目**: SenseVoice、VAD、CAM++模型路径有效性
- **错误处理**: 模型缺失时提供明确错误信息

### FunASR优化管理器
- **文件**: `optimized_funasr_manager.py`
- **核心修复**: VAD模型配置使用本地路径替代网络标识符
- **remote_code处理**: 创建本地model.py文件解决警告

## 文档处理模块

### OCR文字识别
- **引擎**: Tesseract + OpenCV
- **支持格式**: PDF, 图片 (PNG, JPG, TIFF)
- **任务**: `process_ocr_task`, `pdf_ocr_task`
- **优化**: 图像预处理、版面分析

### 文档解析
- **支持格式**: PDF, Word, Excel, PowerPoint
- **功能**: 文本提取、结构化解析
- **任务**: `process_document_task`
- **特性**: 智能分段、元数据提取

### 文档向量化
- **嵌入模型**: sentence-transformers
- **向量数据库**: ChromaDB
- **任务**: `vectorize_document_task`
- **索引**: 语义搜索、相似度匹配

## RAG知识库模块

### 知识库管理
- **框架**: LlamaIndex + LangChain
- **存储**: ChromaDB向量数据库
- **功能**: 文档上传、索引构建、查询检索

### 智能问答
- **检索**: 语义相似度搜索
- **重排序**: Qwen3-Reranker-0.6B
- **生成**: 流式响应、上下文感知
- **API**: 支持流式和非流式查询

### 文档分块
- **策略**: 智能分段、重叠窗口
- **优化**: 语义完整性、长度控制
- **预览**: 分块效果实时预览

## 任务管理模块

### 异步任务队列
- **框架**: Celery + Redis
- **队列**: document_processing, vectorization, ocr_processing
- **并发**: 多线程模式 (4线程)
- **监控**: 任务状态、进度跟踪

### 进度监控
- **实时通信**: WebSocket
- **进度服务**: EnhancedProgressService
- **状态管理**: 任务生命周期管理
- **错误恢复**: 自动重试、故障转移

### 资源管理
- **GPU管理**: 统一资源分配
- **内存监控**: 实时内存使用跟踪
- **并发控制**: 任务队列限制
- **性能优化**: 资源池化、缓存机制

## 最新技术优化成果

### 会议转录核心算法优化
- **问题解决**: 修复文本-VAD片段-说话人关联失败问题
- **算法改进**: 实现基于时间对齐的文本分段方法
- **质量提升**: 添加文本质量验证机制和异常处理

### 离线模型配置完善
- **网络隔离**: 彻底解决模型下载依赖问题
- **性能提升**: 处理速度提升约75% (15.5s → 3.8s)
- **稳定性**: 消除网络不稳定对处理的影响

### 前端用户体验优化
- **容错处理**: 增强空文本片段处理逻辑
- **生命周期**: 修复Vue组件生命周期警告
- **异常状态**: 提升异常情况下的用户体验