#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内存优化功能测试脚本
任务#7.3 - 内存优化和缓存机制测试

测试功能：
1. 内存监控和指标收集
2. LRU缓存功能
3. 模型缓存管理
4. 音频数据缓存
5. 垃圾回收优化
6. 内存优化器集成
7. 缓存装饰器功能

作者: AI Assistant
创建时间: 2025-01-27
任务关联: 任务#7.3 内存优化测试
"""

import sys
import time
import os
import gc
import threading
from pathlib import Path
import numpy as np

# 添加utils目录到路径
utils_path = Path(__file__).parent / "utils"
sys.path.insert(0, str(utils_path))

def test_memory_monitoring():
    """测试内存监控功能"""
    print("📊 测试内存监控功能...")
    
    try:
        from memory_optimizer import MemoryConfig, MemoryMonitor
        
        config = MemoryConfig(
            enable_memory_monitoring=True,
            memory_warning_threshold=0.8,
            memory_critical_threshold=0.9
        )
        
        monitor = MemoryMonitor(config)
        print("✅ 内存监控器创建成功")
        
        # 启动监控
        monitor.start_monitoring()
        print("✅ 内存监控已启动")
        
        # 等待收集指标
        time.sleep(2)
        
        # 获取当前指标
        metrics = monitor.get_current_metrics()
        if metrics:
            print(f"✅ 系统内存使用: {metrics.memory_percent:.1f}%")
            print(f"✅ 进程内存使用: {metrics.process_memory_gb:.2f}GB")
            print(f"✅ 垃圾回收对象: {metrics.gc_objects}")
        else:
            print("❌ 未获取到内存指标")
        
        # 停止监控
        monitor.stop_monitoring()
        print("✅ 内存监控已停止")
        
        return True
        
    except Exception as e:
        print(f"❌ 内存监控测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_lru_cache():
    """测试LRU缓存功能"""
    print("\n💾 测试LRU缓存功能...")
    
    try:
        from memory_optimizer import LRUCache
        
        # 创建小容量缓存测试LRU特性
        cache = LRUCache(max_size=3)
        print("✅ LRU缓存创建成功")
        
        # 添加缓存项
        cache.put("key1", "value1")
        cache.put("key2", "value2")
        cache.put("key3", "value3")
        print(f"✅ 添加3个缓存项，缓存大小: {cache.size()}")
        
        # 测试LRU特性
        cache.put("key4", "value4")  # 应该移除key1
        if cache.get("key1") is None and cache.get("key4") == "value4":
            print("✅ LRU特性验证通过")
        else:
            print("❌ LRU特性验证失败")
        
        # 测试访问更新
        cache.get("key2")  # key2变为最近使用
        cache.put("key5", "value5")  # 应该移除key3而不是key2
        if cache.get("key2") == "value2" and cache.get("key3") is None:
            print("✅ 访问更新特性验证通过")
        else:
            print("❌ 访问更新特性验证失败")
        
        # 清空缓存
        cache.clear()
        if cache.size() == 0:
            print("✅ 缓存清空成功")
        else:
            print("❌ 缓存清空失败")
        
        return True
        
    except Exception as e:
        print(f"❌ LRU缓存测试失败: {e}")
        return False

def test_model_cache():
    """测试模型缓存功能"""
    print("\n🎯 测试模型缓存功能...")
    
    try:
        from memory_optimizer import MemoryConfig, ModelCache
        
        config = MemoryConfig(
            model_cache_enabled=True,
            max_model_cache_count=2
        )
        
        model_cache = ModelCache(config)
        print("✅ 模型缓存创建成功")
        
        # 创建模拟模型
        class MockModel:
            def __init__(self, name, size_mb=50):
                self.name = name
                self.data = np.random.random((int(size_mb * 1024 * 256 / 4),))  # 模拟数据
            
            def __sizeof__(self):
                return self.data.nbytes
        
        # 缓存模型
        model1 = MockModel("model1", 10)
        model2 = MockModel("model2", 15)
        
        model_cache.cache_model("model1", model1, {"type": "test"})
        model_cache.cache_model("model2", model2, {"type": "test"})
        
        stats = model_cache.get_cache_stats()
        print(f"✅ 缓存了 {stats['cached_models']} 个模型")
        print(f"✅ 总缓存大小: {stats['total_size_mb']:.2f}MB")
        
        # 测试模型获取
        cached_model1 = model_cache.get_model("model1")
        if cached_model1 is not None:
            print("✅ 模型获取成功")
        else:
            print("❌ 模型获取失败")
        
        # 测试缓存容量限制
        model3 = MockModel("model3", 20)
        model_cache.cache_model("model3", model3)
        
        final_stats = model_cache.get_cache_stats()
        if final_stats['cached_models'] <= config.max_model_cache_count:
            print("✅ 缓存容量限制验证通过")
        else:
            print("❌ 缓存容量限制验证失败")
        
        # 清空缓存
        model_cache.clear_cache()
        print("✅ 模型缓存清空成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型缓存测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_audio_cache():
    """测试音频缓存功能"""
    print("\n🎵 测试音频缓存功能...")
    
    try:
        from memory_optimizer import MemoryConfig, AudioCache
        
        config = MemoryConfig(
            audio_cache_enabled=True,
            max_audio_cache_size_mb=50.0
        )
        
        audio_cache = AudioCache(config)
        print("✅ 音频缓存创建成功")
        
        # 创建模拟音频数据
        def create_audio_data(duration_sec=1, sample_rate=16000):
            samples = int(duration_sec * sample_rate)
            return np.random.random((samples,)).astype(np.float32)
        
        # 缓存音频数据
        audio1 = create_audio_data(1)  # 1秒音频
        audio2 = create_audio_data(2)  # 2秒音频
        audio3 = create_audio_data(3)  # 3秒音频
        
        audio_cache.cache_audio("audio1", audio1, {"duration": 1})
        audio_cache.cache_audio("audio2", audio2, {"duration": 2})
        audio_cache.cache_audio("audio3", audio3, {"duration": 3})
        
        print(f"✅ 缓存了 {audio_cache.cache.size()} 个音频片段")
        print(f"✅ 总缓存大小: {audio_cache._total_size_mb:.2f}MB")
        
        # 测试音频获取
        cached_audio1 = audio_cache.get_audio("audio1")
        if cached_audio1 is not None and len(cached_audio1) == len(audio1):
            print("✅ 音频获取成功")
        else:
            print("❌ 音频获取失败")
        
        # 测试大音频缓存（触发清理机制）
        large_audio = create_audio_data(10)  # 10秒音频，可能触发清理
        audio_cache.cache_audio("large_audio", large_audio)
        
        print(f"✅ 添加大音频后缓存大小: {audio_cache._total_size_mb:.2f}MB")
        
        return True
        
    except Exception as e:
        print(f"❌ 音频缓存测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_garbage_collector():
    """测试垃圾回收功能"""
    print("\n🗑️ 测试垃圾回收功能...")
    
    try:
        from memory_optimizer import MemoryConfig, GarbageCollector
        
        config = MemoryConfig(
            enable_auto_gc=True,
            gc_interval=1  # 1秒间隔，测试用
        )
        
        garbage_collector = GarbageCollector(config)
        print("✅ 垃圾回收器创建成功")
        
        # 获取初始对象数量
        initial_objects = len(gc.get_objects())
        print(f"✅ 初始对象数量: {initial_objects}")
        
        # 创建一些临时对象
        temp_objects = []
        for i in range(1000):
            temp_objects.append([i] * 100)
        
        after_creation = len(gc.get_objects())
        print(f"✅ 创建对象后数量: {after_creation}")
        
        # 删除引用
        del temp_objects
        
        # 强制垃圾回收
        garbage_collector.force_gc()
        
        after_gc = len(gc.get_objects())
        print(f"✅ 垃圾回收后数量: {after_gc}")
        
        if after_gc < after_creation:
            print("✅ 垃圾回收有效")
        else:
            print("⚠️ 垃圾回收效果不明显（正常情况）")
        
        # 测试自动垃圾回收
        garbage_collector.start()
        print("✅ 自动垃圾回收已启动")
        
        time.sleep(2)  # 等待自动回收执行
        
        garbage_collector.stop()
        print("✅ 自动垃圾回收已停止")
        
        return True
        
    except Exception as e:
        print(f"❌ 垃圾回收测试失败: {e}")
        return False

def test_memory_optimizer():
    """测试内存优化器集成"""
    print("\n🧠 测试内存优化器集成...")
    
    try:
        from memory_optimizer import MemoryConfig, MemoryOptimizer
        
        config = MemoryConfig(
            max_memory_usage_gb=4.0,
            enable_memory_monitoring=True,
            enable_caching=True,
            model_cache_enabled=True,
            audio_cache_enabled=True,
            enable_auto_gc=True
        )
        
        # 使用上下文管理器
        with MemoryOptimizer(config) as optimizer:
            print("✅ 内存优化器启动成功")
            
            # 等待初始化
            time.sleep(1)
            
            # 获取内存统计
            stats = optimizer.get_memory_stats()
            memory_metrics = stats.get('memory_metrics', {})
            if memory_metrics:
                print(f"✅ 内存使用: {memory_metrics.get('memory_percent', 0):.1f}%")
                print(f"✅ 进程内存: {memory_metrics.get('process_memory_gb', 0):.2f}GB")
            
            model_cache = stats.get('model_cache', {})
            print(f"✅ 模型缓存: {model_cache.get('cached_models', 0)} 个")
            
            audio_cache = stats.get('audio_cache', {})
            print(f"✅ 音频缓存: {audio_cache.get('count', 0)} 个")
            
            gc_info = stats.get('garbage_collection', {})
            print(f"✅ 垃圾回收启用: {gc_info.get('enabled', False)}")
            
            # 测试缓存清理
            optimizer.cleanup_cache()
            print("✅ 缓存清理完成")
            
        print("✅ 内存优化器停止成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 内存优化器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_memory_cached_decorator():
    """测试内存缓存装饰器"""
    print("\n🎨 测试内存缓存装饰器...")
    
    try:
        from memory_optimizer import memory_cached
        
        # 创建带缓存的函数
        call_count = 0
        
        @memory_cached(ttl=10)  # 10秒TTL
        def expensive_function(x, y):
            nonlocal call_count
            call_count += 1
            time.sleep(0.1)  # 模拟耗时计算
            return x * y + call_count
        
        print("✅ 缓存装饰器创建成功")
        
        # 第一次调用
        start_time = time.time()
        result1 = expensive_function(5, 10)
        first_call_time = time.time() - start_time
        print(f"✅ 第一次调用结果: {result1}, 用时: {first_call_time:.3f}秒")
        
        # 第二次调用（应该使用缓存）
        start_time = time.time()
        result2 = expensive_function(5, 10)
        second_call_time = time.time() - start_time
        print(f"✅ 第二次调用结果: {result2}, 用时: {second_call_time:.3f}秒")
        
        # 验证缓存效果
        if result1 == result2 and second_call_time < first_call_time:
            print("✅ 缓存装饰器工作正常")
        else:
            print("❌ 缓存装饰器可能有问题")
        
        # 测试不同参数
        result3 = expensive_function(3, 7)
        if result3 != result1:
            print("✅ 不同参数缓存隔离正常")
        else:
            print("❌ 不同参数缓存隔离可能有问题")
        
        print(f"✅ 函数总调用次数: {call_count}")
        
        return True
        
    except Exception as e:
        print(f"❌ 缓存装饰器测试失败: {e}")
        return False

def test_global_functions():
    """测试全局函数"""
    print("\n🌍 测试全局函数...")
    
    try:
        from memory_optimizer import get_global_optimizer, cleanup_memory, create_memory_optimizer
        
        # 测试全局优化器
        global_optimizer = get_global_optimizer()
        if global_optimizer is not None:
            print("✅ 全局优化器获取成功")
        else:
            print("❌ 全局优化器获取失败")
        
        # 测试创建优化器
        custom_optimizer = create_memory_optimizer(
            max_memory_usage_gb=2.0,
            enable_caching=True
        )
        if custom_optimizer is not None:
            print("✅ 自定义优化器创建成功")
        else:
            print("❌ 自定义优化器创建失败")
        
        # 测试全局清理
        cleanup_memory()
        print("✅ 全局内存清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 全局函数测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 70)
    print("内存优化功能测试 - 任务#7.3")
    print("=" * 70)
    
    tests = [
        ("内存监控功能", test_memory_monitoring),
        ("LRU缓存功能", test_lru_cache),
        ("模型缓存功能", test_model_cache),
        ("音频缓存功能", test_audio_cache),
        ("垃圾回收功能", test_garbage_collector),
        ("内存优化器集成", test_memory_optimizer),
        ("缓存装饰器", test_memory_cached_decorator),
        ("全局函数", test_global_functions),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}测试通过")
            else:
                print(f"❌ {test_name}测试失败")
        except Exception as e:
            print(f"❌ {test_name}测试出错: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n{'='*70}")
    print(f"📊 测试结果: {passed}/{total} 通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有内存优化功能测试通过！")
        print("🧠 内存优化和缓存机制实现成功！")
    elif passed > 0:
        print("⚠️ 部分功能正常，需要进一步优化")
    else:
        print("❌ 内存优化功能需要修复")
    
    print("=" * 70)

if __name__ == "__main__":
    main() 