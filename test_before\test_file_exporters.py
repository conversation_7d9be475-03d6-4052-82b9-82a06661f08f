#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件导出功能测试脚本
测试JSON、TXT、DOCX、SRT等格式的导出功能
"""

import os
import sys
import tempfile
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from utils.file_exporters import (
        JSONExporter, TXTExporter, SRTExporter, DOCXExporter, BatchExporter,
        export_analysis_result, export_multiple_formats
    )
    from utils.result_data_structures import (
        AnalysisResult, SpeechSegment, SpeakerProfile, TimeInterval, 
        ProcessingMetrics, create_speech_segment
    )
    print("✓ 成功导入文件导出模块")
except ImportError as e:
    print(f"✗ 导入失败: {e}")
    sys.exit(1)


def create_test_data():
    """创建测试数据"""
    print("\n创建测试数据...")
    
    # 创建分析结果
    result = AnalysisResult(
        audio_file_path="test_audio.wav",
        audio_duration=60.0,
        processing_status="completed"
    )
    
    # 创建说话人档案
    speaker1 = SpeakerProfile(
        id="speaker_001",
        name="张三",
        gender="male",
        total_speech_time=30.0,
        segment_count=3,
        avg_speech_rate=5.0
    )
    
    speaker2 = SpeakerProfile(
        id="speaker_002", 
        name="李四",
        gender="female",
        total_speech_time=25.0,
        segment_count=2,
        avg_speech_rate=4.5
    )
    
    result.speakers = [speaker1, speaker2]
    
    # 创建语音片段
    segments = [
        create_speech_segment(
            start_time=0.0, end_time=10.0,
            text="大家好，欢迎参加今天的会议。我是张三，将主持这次讨论。",
            speaker_id="speaker_001",
            confidence=0.95
        ),
        create_speech_segment(
            start_time=10.5, end_time=25.0,
            text="感谢张总的介绍。我是李四，负责项目的技术实施。今天我们要讨论的是新产品的开发计划。",
            speaker_id="speaker_002",
            confidence=0.92
        ),
        create_speech_segment(
            start_time=26.0, end_time=40.0,
            text="非常好。让我们从技术架构开始讨论。我认为我们应该采用微服务架构来提高系统的可扩展性。",
            speaker_id="speaker_001",
            confidence=0.88
        ),
        create_speech_segment(
            start_time=41.0, end_time=55.0,
            text="我同意张总的观点。微服务架构确实有很多优势，但我们也需要考虑实施的复杂性和维护成本。",
            speaker_id="speaker_002",
            confidence=0.90
        ),
        create_speech_segment(
            start_time=56.0, end_time=60.0,
            text="这是个好建议。我们会在下次会议中详细讨论这些问题。",
            speaker_id="speaker_001",
            confidence=0.87
        )
    ]
    
    result.segments = segments
    
    # 设置处理指标
    result.metrics = ProcessingMetrics(
        total_processing_time=15.0,
        vad_time=2.0,
        recognition_time=10.0,
        speaker_analysis_time=2.5,
        avg_recognition_confidence=0.90,
        avg_speaker_confidence=0.88,
        models_used={
            "speech_recognition": "SenseVoice",
            "speaker_recognition": "CAM++",
            "vad": "SileroVAD"
        }
    )
    
    # 更新统计信息
    result.update_statistics()
    
    print(f"✓ 创建了包含 {len(result.segments)} 个片段和 {len(result.speakers)} 个说话人的测试数据")
    return result


def test_json_export(result, output_dir):
    """测试JSON导出"""
    print("\n=== 测试JSON导出 ===")
    
    json_exporter = JSONExporter()
    
    # 测试不同选项
    test_cases = [
        {
            "filename": "test_basic.json",
            "options": {"pretty_print": True, "include_raw_data": False},
            "description": "基础格式化JSON"
        },
        {
            "filename": "test_compressed.json",
            "options": {"pretty_print": False, "include_raw_data": False},
            "description": "压缩JSON"
        },
        {
            "filename": "test_with_raw.json",
            "options": {"pretty_print": True, "include_raw_data": True},
            "description": "包含原始数据的JSON"
        }
    ]
    
    results = {}
    for case in test_cases:
        output_path = os.path.join(output_dir, case["filename"])
        success = json_exporter.export(result, output_path, **case["options"])
        results[case["description"]] = success
        
        if success:
            file_size = os.path.getsize(output_path) / 1024  # KB
            print(f"✓ {case['description']}: {output_path} ({file_size:.1f} KB)")
        else:
            print(f"✗ {case['description']} 导出失败")
    
    return results


def test_txt_export(result, output_dir):
    """测试TXT导出"""
    print("\n=== 测试TXT导出 ===")
    
    txt_exporter = TXTExporter()
    
    test_cases = [
        {
            "filename": "test_standard.txt",
            "options": {"format_style": "standard", "include_timestamps": True, "include_speakers": True},
            "description": "标准格式"
        },
        {
            "filename": "test_dialogue.txt", 
            "options": {"format_style": "dialogue", "include_timestamps": True, "include_speakers": True},
            "description": "对话格式"
        },
        {
            "filename": "test_transcript.txt",
            "options": {"format_style": "transcript", "include_speakers": True},
            "description": "转录稿格式"
        },
        {
            "filename": "test_simple.txt",
            "options": {"format_style": "dialogue", "include_timestamps": False, "include_speakers": True},
            "description": "简单对话格式（无时间戳）"
        }
    ]
    
    results = {}
    for case in test_cases:
        output_path = os.path.join(output_dir, case["filename"])
        success = txt_exporter.export(result, output_path, **case["options"])
        results[case["description"]] = success
        
        if success:
            file_size = os.path.getsize(output_path) / 1024  # KB
            print(f"✓ {case['description']}: {output_path} ({file_size:.1f} KB)")
        else:
            print(f"✗ {case['description']} 导出失败")
    
    return results


def test_srt_export(result, output_dir):
    """测试SRT导出"""
    print("\n=== 测试SRT导出 ===")
    
    srt_exporter = SRTExporter()
    
    test_cases = [
        {
            "filename": "test_with_speakers.srt",
            "options": {"include_speakers": True, "max_subtitle_length": 80},
            "description": "包含说话人信息"
        },
        {
            "filename": "test_no_speakers.srt",
            "options": {"include_speakers": False, "max_subtitle_length": 60},
            "description": "不包含说话人信息"
        },
        {
            "filename": "test_merged.srt",
            "options": {"include_speakers": True, "merge_short_segments": True, "min_segment_duration": 2.0},
            "description": "合并短片段"
        }
    ]
    
    results = {}
    for case in test_cases:
        output_path = os.path.join(output_dir, case["filename"])
        success = srt_exporter.export(result, output_path, **case["options"])
        results[case["description"]] = success
        
        if success:
            file_size = os.path.getsize(output_path) / 1024  # KB
            print(f"✓ {case['description']}: {output_path} ({file_size:.1f} KB)")
        else:
            print(f"✗ {case['description']} 导出失败")
    
    return results


def test_docx_export(result, output_dir):
    """测试DOCX导出"""
    print("\n=== 测试DOCX导出 ===")
    
    docx_exporter = DOCXExporter()
    
    test_cases = [
        {
            "filename": "test_full_report.docx",
            "options": {"include_summary": True, "include_speaker_analysis": True},
            "description": "完整报告"
        },
        {
            "filename": "test_simple_report.docx",
            "options": {"include_summary": False, "include_speaker_analysis": False},
            "description": "简单转录"
        }
    ]
    
    results = {}
    for case in test_cases:
        output_path = os.path.join(output_dir, case["filename"])
        success = docx_exporter.export(result, output_path, **case["options"])
        results[case["description"]] = success
        
        if success:
            if os.path.exists(output_path):
                file_size = os.path.getsize(output_path) / 1024  # KB
                print(f"✓ {case['description']}: {output_path} ({file_size:.1f} KB)")
            else:
                print(f"? {case['description']}: 文件可能未正确创建")
        else:
            print(f"✗ {case['description']} 导出失败")
    
    return results


def test_batch_export(result, output_dir):
    """测试批量导出"""
    print("\n=== 测试批量导出 ===")
    
    batch_exporter = BatchExporter()
    
    # 测试多格式导出
    batch_dir = os.path.join(output_dir, "batch_export")
    os.makedirs(batch_dir, exist_ok=True)
    
    export_options = {
        'json': {'pretty_print': True, 'include_raw_data': False},
        'txt': {'format_style': 'dialogue', 'include_timestamps': True},
        'srt': {'include_speakers': True, 'max_subtitle_length': 80}
    }
    
    # 不包含DOCX的批量导出（避免依赖问题）
    formats = ['json', 'txt', 'srt']
    
    results = batch_exporter.export_multiple_formats(
        result=result,
        output_dir=batch_dir,
        base_filename="会议转录",
        formats=formats,
        create_zip=True,
        **export_options
    )
    
    print(f"批量导出结果: {results}")
    
    # 检查生成的文件
    for format_name in formats:
        if results.get(format_name, False):
            print(f"✓ {format_name.upper()} 格式导出成功")
        else:
            print(f"✗ {format_name.upper()} 格式导出失败")
    
    if results.get('zip', False):
        print("✓ ZIP压缩包创建成功")
    
    return results


def test_convenience_functions(result, output_dir):
    """测试便捷函数"""
    print("\n=== 测试便捷函数 ===")
    
    # 测试单格式导出
    json_path = os.path.join(output_dir, "convenience_test.json")
    success = export_analysis_result(result, json_path, "json", pretty_print=True)
    print(f"单格式导出 (JSON): {'✓ 成功' if success else '✗ 失败'}")
    
    # 测试多格式导出
    multi_dir = os.path.join(output_dir, "multi_export")
    results = export_multiple_formats(
        result, multi_dir, 
        formats=['json', 'txt'], 
        create_zip=True
    )
    print(f"多格式导出结果: {results}")
    
    return {"single": success, "multiple": results}


def main():
    """主测试函数"""
    print("=" * 60)
    print("文件导出功能测试")
    print("=" * 60)
    
    # 创建临时输出目录
    with tempfile.TemporaryDirectory() as temp_dir:
        output_dir = os.path.join(temp_dir, "export_tests")
        os.makedirs(output_dir, exist_ok=True)
        print(f"测试输出目录: {output_dir}")
        
        # 创建测试数据
        test_result = create_test_data()
        
        # 运行各种测试
        all_results = {}
        
        try:
            all_results["JSON"] = test_json_export(test_result, output_dir)
        except Exception as e:
            print(f"JSON测试失败: {e}")
            all_results["JSON"] = {"error": str(e)}
        
        try:
            all_results["TXT"] = test_txt_export(test_result, output_dir)
        except Exception as e:
            print(f"TXT测试失败: {e}")
            all_results["TXT"] = {"error": str(e)}
        
        try:
            all_results["SRT"] = test_srt_export(test_result, output_dir)
        except Exception as e:
            print(f"SRT测试失败: {e}")
            all_results["SRT"] = {"error": str(e)}
        
        try:
            all_results["DOCX"] = test_docx_export(test_result, output_dir)
        except Exception as e:
            print(f"DOCX测试失败: {e}")
            all_results["DOCX"] = {"error": str(e)}
        
        try:
            all_results["Batch"] = test_batch_export(test_result, output_dir)
        except Exception as e:
            print(f"批量导出测试失败: {e}")
            all_results["Batch"] = {"error": str(e)}
        
        try:
            all_results["Convenience"] = test_convenience_functions(test_result, output_dir)
        except Exception as e:
            print(f"便捷函数测试失败: {e}")
            all_results["Convenience"] = {"error": str(e)}
        
        # 汇总结果
        print("\n" + "=" * 60)
        print("测试结果汇总")
        print("=" * 60)
        
        total_tests = 0
        passed_tests = 0
        
        for category, results in all_results.items():
            if isinstance(results, dict) and "error" not in results:
                category_passed = sum(1 for v in results.values() if v is True)
                category_total = len(results)
                total_tests += category_total
                passed_tests += category_passed
                print(f"{category}: {category_passed}/{category_total} 通过")
            elif "error" in results:
                print(f"{category}: 测试失败 - {results['error']}")
            else:
                print(f"{category}: 结果未知")
        
        print(f"\n总计: {passed_tests}/{total_tests} 测试通过")
        
        if passed_tests == total_tests:
            print("🎉 所有测试通过！")
        else:
            print(f"⚠️  有 {total_tests - passed_tests} 个测试失败")
        
        # 显示生成的文件
        print(f"\n生成的测试文件保存在: {output_dir}")
        print("文件列表:")
        for root, dirs, files in os.walk(output_dir):
            level = root.replace(output_dir, '').count(os.sep)
            indent = ' ' * 2 * level
            print(f"{indent}{os.path.basename(root)}/")
            subindent = ' ' * 2 * (level + 1)
            for file in files:
                file_path = os.path.join(root, file)
                file_size = os.path.getsize(file_path) / 1024  # KB
                print(f"{subindent}{file} ({file_size:.1f} KB)")


if __name__ == "__main__":
    main() 