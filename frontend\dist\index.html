<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/svg+xml" href="/favicon.ico" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>语音处理智能平台</title>
  <meta name="description" content="基于AI的语音识别、说话人识别、音频处理智能平台" />
  <meta name="keywords" content="语音识别,说话人识别,音频处理,AI,人工智能" />
  
  <!-- 预加载静态资源 -->
  <link rel="preload" href="/static/css/tailwind-offline.css" as="style" />
  <link rel="preload" href="/static/js/axios.min.js" as="script" />
  
  <!-- 引入Tailwind CSS -->
  <link rel="stylesheet" href="/static/css/tailwind-offline.css" />
  
  <!-- 自定义样式 -->
  <style>
    /* 全局样式重置 */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    html, body {
      height: 100%;
      font-family: 'PingFang SC', 'Microsoft YaHei', 'Helvetica Neue', Arial, sans-serif;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }
    
    #app {
      height: 100%;
    }
    
    /* 加载动画 */
    .loading-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 9999;
    }
    
    .loading-spinner {
      width: 50px;
      height: 50px;
      border: 4px solid rgba(255, 255, 255, 0.3);
      border-top: 4px solid white;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .loading-text {
      color: white;
      font-size: 18px;
      margin-top: 20px;
      text-align: center;
    }
    
    /* 滚动条样式 */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    
    ::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }
    
    ::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;
    }
    
    ::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }
    
    /* 响应式字体 */
    @media (max-width: 768px) {
      html {
        font-size: 14px;
      }
    }
    
    @media (max-width: 480px) {
      html {
        font-size: 12px;
      }
    }
  </style>
  <script type="module" crossorigin src="/static/js/index-2c134546.js"></script>
  <link rel="stylesheet" href="/static/css/index-5cad6ac7.css">
</head>
<body>
  <div id="app">
    <!-- 加载页面 -->
    <div id="loading" class="loading-container">
      <div>
        <div class="loading-spinner"></div>
        <div class="loading-text">
          <div>语音处理智能平台</div>
          <div style="font-size: 14px; margin-top: 10px; opacity: 0.8;">正在加载中...</div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 引入Axios -->
  <script src="/static/js/axios.min.js"></script>
  
  <!-- 主应用脚本 -->
  
  
  <!-- 隐藏加载页面的脚本 -->
  <script>
    // 当页面加载完成后隐藏加载动画
    window.addEventListener('load', function() {
      setTimeout(function() {
        const loading = document.getElementById('loading');
        if (loading) {
          loading.style.opacity = '0';
          loading.style.transition = 'opacity 0.5s ease';
          setTimeout(function() {
            loading.style.display = 'none';
          }, 500);
        }
      }, 1000);
    });
    
    // 错误处理
    window.addEventListener('error', function(e) {
      console.error('页面加载错误:', e.error);
      const loading = document.getElementById('loading');
      if (loading) {
        const loadingText = loading.querySelector('.loading-text');
        if (loadingText) {
          loadingText.innerHTML = '<div style="color: #ff6b6b;">加载失败，请刷新页面重试</div>';
        }
      }
    });
  </script>
</body>
</html>
