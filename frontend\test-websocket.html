<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket连接测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .info { background-color: #d1ecf1; color: #0c5460; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-secondary { background-color: #6c757d; color: white; }
        .btn-danger { background-color: #dc3545; color: white; }
    </style>
</head>
<body>
    <h1>WebSocket连接测试</h1>
    
    <div id="status" class="status info">准备连接...</div>
    
    <div>
        <button id="connectBtn" class="btn-primary">连接WebSocket</button>
        <button id="disconnectBtn" class="btn-danger" disabled>断开连接</button>
        <button id="clearLogBtn" class="btn-secondary">清空日志</button>
    </div>
    
    <div>
        <h3>测试任务订阅</h3>
        <input type="text" id="taskIdInput" placeholder="输入任务ID" value="test-task-123">
        <button id="subscribeBtn" class="btn-primary" disabled>订阅任务</button>
        <button id="unsubscribeBtn" class="btn-secondary" disabled>取消订阅</button>
    </div>
    
    <div>
        <h3>连接日志</h3>
        <div id="log" class="log"></div>
    </div>

    <script>
        let ws = null;
        let isConnected = false;
        
        const statusDiv = document.getElementById('status');
        const logDiv = document.getElementById('log');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const subscribeBtn = document.getElementById('subscribeBtn');
        const unsubscribeBtn = document.getElementById('unsubscribeBtn');
        const taskIdInput = document.getElementById('taskIdInput');
        const clearLogBtn = document.getElementById('clearLogBtn');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            logEntry.style.color = type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#333';
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function updateStatus(message, type) {
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        function updateButtons() {
            connectBtn.disabled = isConnected;
            disconnectBtn.disabled = !isConnected;
            subscribeBtn.disabled = !isConnected;
            unsubscribeBtn.disabled = !isConnected;
        }
        
        function connect() {
            const token = 'demo-token'; // 使用测试token
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.hostname}:8002/ws/progress?token=${token}`;
            
            log(`尝试连接到: ${wsUrl}`);
            updateStatus('正在连接...', 'info');
            
            try {
                ws = new WebSocket(wsUrl);
                
                ws.onopen = function(event) {
                    isConnected = true;
                    log('WebSocket连接成功！', 'success');
                    updateStatus('已连接', 'success');
                    updateButtons();
                };
                
                ws.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        log(`收到消息: ${JSON.stringify(data, null, 2)}`, 'success');
                    } catch (e) {
                        log(`收到原始消息: ${event.data}`);
                    }
                };
                
                ws.onclose = function(event) {
                    isConnected = false;
                    log(`WebSocket连接关闭: 代码=${event.code}, 原因=${event.reason}`, 'error');
                    updateStatus('连接已关闭', 'error');
                    updateButtons();
                };
                
                ws.onerror = function(error) {
                    log(`WebSocket错误: ${error}`, 'error');
                    updateStatus('连接错误', 'error');
                };
                
            } catch (error) {
                log(`连接失败: ${error}`, 'error');
                updateStatus('连接失败', 'error');
            }
        }
        
        function disconnect() {
            if (ws) {
                ws.close(1000, 'User disconnect');
                ws = null;
            }
        }
        
        function subscribe() {
            const taskId = taskIdInput.value.trim();
            if (!taskId) {
                alert('请输入任务ID');
                return;
            }
            
            if (ws && isConnected) {
                const message = {
                    type: 'subscribe',
                    task_id: taskId
                };
                ws.send(JSON.stringify(message));
                log(`发送订阅消息: ${JSON.stringify(message)}`);
            }
        }
        
        function unsubscribe() {
            const taskId = taskIdInput.value.trim();
            if (!taskId) {
                alert('请输入任务ID');
                return;
            }
            
            if (ws && isConnected) {
                const message = {
                    type: 'unsubscribe',
                    task_id: taskId
                };
                ws.send(JSON.stringify(message));
                log(`发送取消订阅消息: ${JSON.stringify(message)}`);
            }
        }
        
        function clearLog() {
            logDiv.innerHTML = '';
        }
        
        // 事件监听器
        connectBtn.addEventListener('click', connect);
        disconnectBtn.addEventListener('click', disconnect);
        subscribeBtn.addEventListener('click', subscribe);
        unsubscribeBtn.addEventListener('click', unsubscribe);
        clearLogBtn.addEventListener('click', clearLog);
        
        // 初始化
        updateButtons();
        log('WebSocket测试页面已加载');
    </script>
</body>
</html>
