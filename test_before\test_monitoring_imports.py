#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控模块导入测试脚本
"""

import sys
import os

def test_monitoring_imports():
    """测试监控模块导入"""
    print("开始测试监控模块导入...")
    
    try:
        print("1. 测试 enhanced_batch_processor 导入...")
        from utils.enhanced_batch_processor import AudioBatchProcessor, AudioBatchConfig
        print("   ✅ enhanced_batch_processor 导入成功")
        
        print("2. 测试 performance_monitor 导入...")
        from utils.performance_monitor import PerformanceMonitor
        print("   ✅ performance_monitor 导入成功")
        
        print("3. 测试 monitoring_components 导入...")
        from utils.monitoring_components import ProcessingMonitor, MonitoringComponents
        print("   ✅ monitoring_components 导入成功")
        
        # 测试基本功能
        print("4. 测试基本功能...")
        monitor = ProcessingMonitor("test")
        print(f"   ✅ ProcessingMonitor 实例化成功: {type(monitor)}")
        
        print("\n🎉 所有导入测试通过！监控模块可用。")
        return True
        
    except ImportError as e:
        print(f"   ❌ 导入失败: {str(e)}")
        return False
    except Exception as e:
        print(f"   ❌ 其他错误: {str(e)}")
        return False

if __name__ == "__main__":
    success = test_monitoring_imports()
    sys.exit(0 if success else 1) 