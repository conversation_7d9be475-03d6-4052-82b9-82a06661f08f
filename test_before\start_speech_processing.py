#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语音处理页面启动脚本
解决torch日志配置问题
"""

import os
import sys
import subprocess

def setup_environment():
    """设置环境变量"""
    print("🔧 设置环境变量...")
    
    # 禁用torch日志
    env_vars = {
        'TORCH_LOGS': '',
        'TORCH_LOG_LEVEL': 'ERROR',
        'TORCH_SHOW_CPP_STACKTRACES': '0',
        'PYTHONWARNINGS': 'ignore',
        'TF_CPP_MIN_LOG_LEVEL': '2'
    }
    
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"  {key} = {value}")
    
    print("✅ 环境变量设置完成")

def test_imports():
    """测试关键导入"""
    print("\n🧪 测试关键导入...")
    
    try:
        import torch
        print(f"✅ torch: {torch.__version__}")
    except Exception as e:
        print(f"❌ torch导入失败: {e}")
        return False
    
    try:
        import streamlit
        print(f"✅ streamlit: {streamlit.__version__}")
    except Exception as e:
        print(f"❌ streamlit导入失败: {e}")
        return False
    
    try:
        import soundfile
        print(f"✅ soundfile: 可用")
    except Exception as e:
        print(f"❌ soundfile导入失败: {e}")
        return False
    
    return True

def start_streamlit():
    """启动streamlit应用"""
    print("\n🚀 启动语音处理页面...")
    
    # 构建启动命令
    cmd = [
        sys.executable, "-m", "streamlit", "run", 
        "pages/语音处理分析.py",
        "--server.headless", "true",
        "--server.port", "8501",
        "--server.address", "localhost"
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        # 启动streamlit
        subprocess.run(cmd, env=os.environ.copy())
    except KeyboardInterrupt:
        print("\n👋 应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

def main():
    """主函数"""
    print("🎵 语音处理分析页面启动器")
    print("=" * 50)
    
    # 设置环境
    setup_environment()
    
    # 测试导入
    if not test_imports():
        print("\n❌ 导入测试失败，请检查依赖安装")
        return
    
    print("\n✅ 所有检查通过，准备启动应用...")
    
    # 启动应用
    start_streamlit()

if __name__ == "__main__":
    main()
