#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证音频处理系统是否正确使用已验证的工具
测试speech_recognition_core.py和speaker_recognition.py的集成
"""

import sys
import os
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_speech_recognition_core():
    """测试语音识别核心模块"""
    print("\n=== 测试语音识别核心模块 ===")
    
    try:
        from backend.utils.audio.speech_recognition_core import (
            SpeechRecognitionManager,
            SpeechRecognitionConfig,
            create_speech_recognizer,
            quick_recognize
        )
        from backend.core.config import get_model_path
        
        print("✅ 成功导入语音识别核心模块")
        
        # 测试配置创建
        config = SpeechRecognitionConfig(
            model_path="./models/SenseVoiceSmall",
            language="auto",
            use_itn=True,
            device="auto"
        )
        print(f"✅ 配置创建成功: {config.to_dict()}")
        
        # 测试管理器创建
        manager = SpeechRecognitionManager()
        print("✅ 语音识别管理器创建成功")
        
        # 测试模型路径获取
        try:
            model_path = get_model_path("sensevoice")
            print(f"✅ 模型路径获取成功: {model_path}")
            
            # 检查模型文件是否存在
            if Path(model_path).exists():
                print(f"✅ 模型文件存在: {model_path}")
                
                # 检查关键文件
                required_files = ['model.pt', 'config.yaml', 'tokens.json']
                for file_name in required_files:
                    file_path = Path(model_path) / file_name
                    if file_path.exists():
                        print(f"  ✅ {file_name} 存在")
                    else:
                        print(f"  ⚠️  {file_name} 不存在")
            else:
                print(f"⚠️  模型路径不存在: {model_path}")
                
        except Exception as e:
            print(f"⚠️  模型路径获取失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 语音识别核心模块测试失败: {e}")
        return False

def test_speaker_recognition():
    """测试说话人识别模块"""
    print("\n=== 测试说话人识别模块 ===")
    
    try:
        from backend.utils.audio.speaker_recognition import (
            SpeakerRecognition,
            CAMPlusModel
        )
        from backend.core.config import get_model_path
        
        print("✅ 成功导入说话人识别模块")
        
        # 测试模型路径获取
        try:
            model_path = get_model_path("speaker")
            print(f"✅ 说话人模型路径获取成功: {model_path}")
            
            # 检查模型文件是否存在
            if Path(model_path).exists():
                print(f"✅ 说话人模型文件存在: {model_path}")
                
                # 检查关键文件
                required_files = ['campplus_cn_common.bin', 'config.yaml']
                for file_name in required_files:
                    file_path = Path(model_path) / file_name
                    if file_path.exists():
                        print(f"  ✅ {file_name} 存在")
                    else:
                        print(f"  ⚠️  {file_name} 不存在")
            else:
                print(f"⚠️  说话人模型路径不存在: {model_path}")
                
        except Exception as e:
            print(f"⚠️  说话人模型路径获取失败: {e}")
        
        # 测试说话人识别器创建
        try:
            speaker_recognizer = SpeakerRecognition(device="auto")
            print("✅ 说话人识别器创建成功")
        except Exception as e:
            print(f"⚠️  说话人识别器创建失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 说话人识别模块测试失败: {e}")
        return False

def test_vad_model():
    """测试VAD模型"""
    print("\n=== 测试VAD模型 ===")
    
    try:
        from backend.core.config import get_model_path
        
        # 测试VAD模型路径获取
        try:
            vad_path = get_model_path("vad")
            print(f"✅ VAD模型路径获取成功: {vad_path}")
            
            # 检查模型文件是否存在
            if Path(vad_path).exists():
                print(f"✅ VAD模型文件存在: {vad_path}")
                
                # 检查关键文件
                required_files = ['model.pt', 'config.yaml']
                for file_name in required_files:
                    file_path = Path(vad_path) / file_name
                    if file_path.exists():
                        print(f"  ✅ {file_name} 存在")
                    else:
                        print(f"  ⚠️  {file_name} 不存在")
            else:
                print(f"⚠️  VAD模型路径不存在: {vad_path}")
                
        except Exception as e:
            print(f"⚠️  VAD模型路径获取失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ VAD模型测试失败: {e}")
        return False

def test_model_path_configuration():
    """测试模型路径配置"""
    print("\n=== 测试模型路径配置 ===")
    
    try:
        from backend.core.config import get_model_path, settings
        
        print(f"模型基础路径: {settings.MODELS_BASE_PATH}")
        
        # 测试所有模型类型
        model_types = [
            "vad", "fsmn_vad", 
            "asr", "sensevoice", 
            "speaker", "campplus", "cam++",
            "reranker"
        ]
        
        for model_type in model_types:
            try:
                model_path = get_model_path(model_type)
                exists = Path(model_path).exists()
                status = "✅ 存在" if exists else "⚠️  不存在"
                print(f"  {model_type}: {model_path} - {status}")
            except Exception as e:
                print(f"  {model_type}: ❌ 获取失败 - {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型路径配置测试失败: {e}")
        return False

def test_task_integration():
    """测试任务集成"""
    print("\n=== 测试任务集成 ===")
    
    try:
        from backend.tasks.audio_processing_tasks import (
            _get_model_path,
            vad_detection_task,
            speech_recognition_task,
            speaker_recognition_task
        )
        
        print("✅ 成功导入音频处理任务")
        
        # 测试模型路径获取函数
        model_types = ['vad', 'sensevoice', 'speaker']
        for model_type in model_types:
            try:
                path = _get_model_path(model_type)
                print(f"✅ 任务模型路径 {model_type}: {path}")
            except Exception as e:
                print(f"⚠️  任务模型路径 {model_type} 获取失败: {e}")
        
        # 测试任务函数存在性
        tasks = [
            ("VAD检测任务", vad_detection_task),
            ("语音识别任务", speech_recognition_task),
            ("说话人识别任务", speaker_recognition_task)
        ]
        
        for task_name, task_func in tasks:
            if callable(task_func):
                print(f"✅ {task_name} 函数可调用")
            else:
                print(f"❌ {task_name} 函数不可调用")
        
        return True
        
    except Exception as e:
        print(f"❌ 任务集成测试失败: {e}")
        return False

def test_funasr_availability():
    """测试FunASR可用性"""
    print("\n=== 测试FunASR可用性 ===")
    
    try:
        import funasr
        from funasr import AutoModel
        print("✅ FunASR库已安装并可导入")
        print(f"FunASR版本: {getattr(funasr, '__version__', '未知')}")
        return True
    except ImportError:
        print("⚠️  FunASR库未安装")
        print("建议安装: pip install funasr")
        return False
    except Exception as e:
        print(f"❌ FunASR测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎵 验证音频处理系统工具集成")
    print("="*60)
    
    tests = [
        ("FunASR可用性", test_funasr_availability),
        ("模型路径配置", test_model_path_configuration),
        ("VAD模型", test_vad_model),
        ("语音识别核心模块", test_speech_recognition_core),
        ("说话人识别模块", test_speaker_recognition),
        ("任务集成", test_task_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试出错: {e}")
            results.append((test_name, False))
    
    # 输出测试结果汇总
    print("\n" + "="*60)
    print("验证结果汇总:")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有验证通过！音频处理系统正确使用了已验证的工具")
    else:
        print("⚠️  部分验证失败，请检查配置和依赖")
    
    # 输出建议
    print("\n建议:")
    if passed < total:
        print("1. 确保所有模型文件已正确下载到models目录")
        print("2. 检查FunASR库是否正确安装: pip install funasr")
        print("3. 验证模型路径配置是否正确")
    else:
        print("✅ 系统配置正确，可以开始使用音频处理功能")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
