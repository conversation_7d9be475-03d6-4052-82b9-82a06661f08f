#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试API逻辑和音频文件查询
"""

try:
    print("🔍 测试API逻辑...")

    # 测试导入
    from backend.models.task_models import TaskRecord
    from backend.models.audio import AudioFile
    from backend.core.database import get_db
    from sqlalchemy import func
    import json

    print("✅ 导入成功")

    # 模拟API逻辑
    user_id = "1"
    limit = 50
    offset = 0

    db = next(get_db())

    print("\n🔍 测试音频文件查询...")

    # 查询用户的音频文件
    audio_files = db.query(AudioFile).filter(
        AudioFile.owner_id == user_id
    ).order_by(AudioFile.created_at.desc()).all()

    print(f"✅ 音频文件查询成功，找到 {len(audio_files)} 个文件")

    for i, file in enumerate(audio_files[:3], 1):
        print(f"\n📁 文件 {i}:")
        print(f"  id: {file.id}")
        print(f"  filename: {file.filename}")
        print(f"  original_filename: {file.original_filename}")
        print(f"  size: {file.file_size}")
        print(f"  duration: {file.duration}")
        print(f"  status: {file.status}")
        print(f"  created_at: {file.created_at}")
        print("------------------------------------------------------------")

    print("\n🔍 测试活跃任务查询...")

    # 查询活跃任务
    active_tasks = db.query(TaskRecord).filter(
        TaskRecord.user_id == user_id,
        TaskRecord.task_type.in_(['audio_processing', 'speech_recognition', 'speaker_recognition', 'vad_detection']),
        TaskRecord.status.in_(['PENDING', 'STARTED', 'RETRY'])
    ).order_by(TaskRecord.created_at.desc()).all()

    print(f"✅ 活跃任务查询成功，找到 {len(active_tasks)} 个活跃任务")

    for i, task in enumerate(active_tasks[:3], 1):
        print(f"\n⚡ 活跃任务 {i}:")
        print(f"  task_id: {task.task_id}")
        print(f"  task_type: {task.task_type}")
        print(f"  status: {task.status}")
        print(f"  progress: {task.progress_percentage}%")
        print(f"  created_at: {task.created_at}")
        print("------------------------------------------------------------")

    print("\n🔍 测试文件统计查询...")

    # 查询文件统计
    total_count = db.query(func.count(AudioFile.id)).filter(
        AudioFile.owner_id == user_id
    ).scalar() or 0

    success_count = db.query(func.count(AudioFile.id)).filter(
        AudioFile.owner_id == user_id,
        AudioFile.status == "completed"
    ).scalar() or 0

    failed_count = db.query(func.count(AudioFile.id)).filter(
        AudioFile.owner_id == user_id,
        AudioFile.status == "failed"
    ).scalar() or 0

    total_size = db.query(func.sum(AudioFile.file_size)).filter(
        AudioFile.owner_id == user_id
    ).scalar() or 0

    print(f"✅ 文件统计查询成功:")
    print(f"  总文件数: {total_count}")
    print(f"  成功处理: {success_count}")
    print(f"  处理失败: {failed_count}")
    print(f"  总大小: {total_size} 字节")

    db.close()
    print("\n✅ 所有测试完成")

except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
