@echo off
echo ========================================
echo 语音处理智能平台 - 一键启动脚本
echo ========================================
echo.

:: 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python未安装或未添加到PATH
    echo 请安装Python 3.11+并添加到系统PATH
    pause
    exit /b 1
)

:: 检查Node.js环境
node --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Node.js未安装或未添加到PATH
    echo 请安装Node.js 16.0+并添加到系统PATH
    pause
    exit /b 1
)

:: 检查虚拟环境
if not exist ".venv\Scripts\activate.bat" (
    echo 🔧 创建Python虚拟环境...
    python -m venv .venv
    if errorlevel 1 (
        echo ❌ 虚拟环境创建失败
        pause
        exit /b 1
    )
)

:: 激活虚拟环境
echo 🔧 激活虚拟环境...
call .venv\Scripts\activate.bat

:: 安装后端依赖
if not exist ".venv\Lib\site-packages\fastapi" (
    echo 📦 安装后端依赖...
    pip install -r backend\requirements.txt
    if errorlevel 1 (
        echo ❌ 后端依赖安装失败
        pause
        exit /b 1
    )
)

:: 检查前端依赖
if not exist "frontend\node_modules" (
    echo 📦 安装前端依赖...
    cd frontend
    npm install
    if errorlevel 1 (
        echo ❌ 前端依赖安装失败
        pause
        exit /b 1
    )
    cd ..
)

:: 检查Redis
echo 🔍 检查Redis服务...
docker ps | findstr redis >nul 2>&1
if errorlevel 1 (
    echo 🚀 启动Redis容器...
    docker run -d --name redis -p 6379:6379 redis:latest
    if errorlevel 1 (
        echo ❌ Redis启动失败，请确保Docker已安装并运行
        pause
        exit /b 1
    )
    timeout /t 3 >nul
)

:: 初始化数据库
if not exist "data\speech_platform.db" (
    echo 🗄️ 初始化数据库...
    python -c "from backend.core.database import init_db; init_db()"
)

echo.
echo ✅ 环境检查完成，开始启动服务...
echo.

:: 启动后端服务
echo 🚀 启动后端服务 (端口: 8002)...
start "后端服务" cmd /k "call .venv\Scripts\activate.bat && python -m uvicorn backend.main:app --host 0.0.0.0 --port 8002 --reload"

:: 等待后端启动
timeout /t 5 >nul

:: 启动Celery Worker
echo 🚀 启动Celery Worker...
start "Celery Worker" cmd /k "call .venv\Scripts\activate.bat && python start_worker_windows.py"

:: 等待Worker启动
timeout /t 3 >nul

:: 启动前端服务
echo 🚀 启动前端服务 (端口: 3000)...
start "前端服务" cmd /k "cd frontend && npm run dev"

:: 等待前端启动
timeout /t 5 >nul

echo.
echo ========================================
echo 🎉 所有服务启动完成！
echo ========================================
echo.
echo 📱 前端界面: http://localhost:3000
echo 🔧 后端API: http://localhost:8002
echo 📚 API文档: http://localhost:8002/docs
echo.
echo 默认登录账户:
echo   用户名: admin
echo   密码: admin123
echo.
echo 💡 提示: 
echo   - 首次启动可能需要下载AI模型，请耐心等待
echo   - 如遇问题请查看各服务窗口的日志信息
echo   - 按Ctrl+C可停止对应服务
echo.
echo 🔗 更多帮助: 
echo   - 项目文档: README.md
echo   - 部署指南: DEPLOYMENT_GUIDE.md
echo   - 问题反馈: GitHub Issues
echo.

:: 打开浏览器
timeout /t 3 >nul
start http://localhost:3000

echo 按任意键退出启动脚本...
pause >nul
