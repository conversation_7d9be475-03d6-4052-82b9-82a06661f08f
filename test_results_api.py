#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试处理结果API
"""

import requests
import json

try:
    print("🔧 测试处理结果API...")
    
    # API端点
    base_url = "http://localhost:8002"
    
    # 获取认证token（使用admin账户）
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    print("🔐 登录获取token...")
    login_response = requests.post(f"{base_url}/api/v1/auth/login", json=login_data)
    
    if login_response.status_code == 200:
        token = login_response.json().get("access_token")
        print(f"✅ 登录成功，token: {token[:20]}...")
        
        # 设置认证头
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        print("🔍 测试处理结果API...")
        
        # 调用处理结果API
        results_url = f"{base_url}/api/v1/audio/results"
        results_response = requests.get(results_url, headers=headers)
        
        print(f"📊 API响应状态码: {results_response.status_code}")
        
        if results_response.status_code == 200:
            result_data = results_response.json()
            print(f"✅ API调用成功")
            print(f"📋 响应数据结构:")
            print(f"  - success: {result_data.get('success')}")
            print(f"  - total: {result_data.get('total')}")
            print(f"  - results数组长度: {len(result_data.get('results', []))}")
            
            # 显示前3个结果的详细信息
            results = result_data.get('results', [])
            for i, result in enumerate(results[:3], 1):
                print(f"\n📋 结果 {i}:")
                print(f"  task_id: {result.get('task_id')}")
                print(f"  file_name: {result.get('file_name')}")
                print(f"  mode: {result.get('mode')}")
                print(f"  status: {result.get('status')}")
                print(f"  progress: {result.get('progress')}%")
                print(f"  created_time: {result.get('created_time')}")
                print("------------------------------------------------------------")
            
            if len(results) == 0:
                print("⚠️ 返回的结果数组为空")
                
                # 让我们直接查询数据库看看有什么数据
                print("\n🔍 直接查询数据库...")
                
        else:
            print(f"❌ API调用失败: {results_response.status_code}")
            print(f"错误信息: {results_response.text}")
    
    else:
        print(f"❌ 登录失败: {login_response.status_code}")
        print(f"错误信息: {login_response.text}")

except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
