"""
任务队列系统核心模块
基于Celery + Redis实现异步任务处理
"""

import os
import sys
import uuid
from pathlib import Path
from typing import Dict, Any, Optional, Callable
from celery import Celery
from celery.result import AsyncResult
from kombu import Queue
import redis
from loguru import logger

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent.parent
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))

try:
    from backend.services.task_persistence_service import get_task_persistence_service
    from backend.services.concurrency_control import get_concurrency_controller
    from backend.core.database import get_db_session
    from backend.models.task_models import TaskStatus
except ImportError:
    # 如果在 backend 目录下运行，使用相对导入
    from services.task_persistence_service import get_task_persistence_service
    from services.concurrency_control import get_concurrency_controller
    from core.database import get_db_session
    from models.task_models import TaskStatus

# Redis配置
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")
CELERY_BROKER_URL = os.getenv("CELERY_BROKER_URL", REDIS_URL)
CELERY_RESULT_BACKEND = os.getenv("CELERY_RESULT_BACKEND", REDIS_URL)

# 创建Celery应用
celery_app = Celery(
    "task_queue",
    broker=CELERY_BROKER_URL,
    backend=CELERY_RESULT_BACKEND,
    include=[
        'backend.tasks.document_tasks',
        'backend.tasks.vectorization_tasks',
        'backend.tasks.ocr_tasks',
        'backend.tasks.audio_processing_tasks'
    ]
)

# Celery配置
celery_app.conf.update(
    # 任务序列化
    task_serializer='json',
    accept_content=['json'],
    result_serializer='json',
    timezone='Asia/Shanghai',
    enable_utc=True,

    # 任务路由
    task_routes={
        'backend.tasks.document_tasks.*': {'queue': 'document_processing'},
        'backend.tasks.vectorization_tasks.*': {'queue': 'vectorization'},
        'backend.tasks.ocr_tasks.*': {'queue': 'ocr_processing'},
        'backend.tasks.audio_processing_tasks.*': {'queue': 'audio_processing'},
    },

    # 队列定义
    task_queues=(
        Queue('document_processing', routing_key='document_processing'),
        Queue('vectorization', routing_key='vectorization'),
        Queue('ocr_processing', routing_key='ocr_processing'),
        Queue('audio_processing', routing_key='audio_processing'),
        Queue('default', routing_key='default'),
    ),

    # 任务执行配置
    task_acks_late=True,
    worker_prefetch_multiplier=1,
    task_reject_on_worker_lost=True,

    # 结果过期时间
    result_expires=3600,  # 1小时

    # 重试配置
    task_default_retry_delay=60,
    task_max_retries=3,

    # Windows兼容性配置 - 使用多线程模式
    worker_pool='threads',  # 使用线程池而不是进程池
    worker_concurrency=2,  # 降低并发数到2，避免GPU资源竞争
    worker_disable_rate_limits=True,  # 禁用速率限制

    # 避免Windows权限问题
    worker_hijack_root_logger=False,
    worker_log_color=False,

    # 线程池优化配置 - 更保守的设置
    worker_max_tasks_per_child=10,  # 每个线程最多处理10个任务后重启，避免内存累积
    worker_pool_restarts=True,  # 允许线程池重启

    # GPU资源管理
    worker_send_task_events=True,  # 启用任务事件监控
    task_track_started=True,  # 跟踪任务开始状态

    # 🔧 任务超时配置 - 大文件处理需要更长时间
    task_soft_time_limit=1800,  # 🔧 30分钟软超时（大文件处理）
    task_time_limit=3600,       # 🔧 60分钟硬超时

    # 内存管理
    worker_max_memory_per_child=2048000,  # 2GB内存限制后重启worker
)

# Redis客户端用于进度存储（文本数据）
redis_client = redis.Redis.from_url(REDIS_URL, decode_responses=True)

# Redis客户端用于二进制数据存储（不解码）
redis_binary_client = redis.Redis.from_url(REDIS_URL, decode_responses=False)


class TaskManager:
    """任务管理器"""

    def __init__(self):
        self.redis_client = redis_client  # 用于文本数据
        self.redis_binary_client = redis_binary_client  # 用于二进制数据
        self.celery_app = celery_app
        self.persistence_service = get_task_persistence_service()
        self.concurrency_controller = get_concurrency_controller()
    
    def create_task_id(self, prefix: str = "task") -> str:
        """生成唯一任务ID"""
        return f"{prefix}_{uuid.uuid4().hex[:12]}"
    
    def create_task(self, task_type: str, user_id: str, data: Dict[str, Any]) -> str:
        """创建通用任务（主要用于测试）"""
        task_id = self.create_task_id(task_type)
        
        try:
            # 创建任务记录
            db = get_db_session()
            try:
                self.persistence_service.create_task_record(
                    db=db,
                    task_id=task_id,
                    user_id=user_id,
                    task_type=task_type,
                    task_name=f"任务: {task_type}",
                    task_args=[task_id, user_id, data],
                    queue_name='default',
                    metadata=data
                )
            finally:
                db.close()
            
            # 设置初始状态
            self.set_task_progress(task_id, 0, f"任务已创建: {task_type}")
            
            logger.info(f"创建通用任务: {task_id}, 类型: {task_type}")
            return task_id
            
        except Exception as e:
            logger.error(f"创建任务失败: {e}")
            raise
    
    def set_task_progress(self, task_id: str, percentage: float, detail: str = "", stage: str = ""):
        """设置任务进度"""
        try:
            import time
            progress_key = f"task_progress:{task_id}"
            progress_data = {
                "percentage": percentage,
                "detail": detail,
                "stage": stage,
                "update_time": str(time.time())
            }
            
            # 如果是第一次设置进度，记录开始时间
            if not self.redis_client.exists(progress_key):
                progress_data["start_time"] = str(time.time())
            
            self.redis_client.hset(progress_key, mapping=progress_data)
            self.redis_client.expire(progress_key, 3600)  # 1小时过期
            
        except Exception as e:
            logger.error(f"设置任务进度失败: {task_id}, {e}")
    
    def submit_document_processing_task(
        self,
        user_id: str,
        file_content: bytes,
        filename: str,
        document_id: Optional[int] = None,
        use_ocr: bool = False,
        ocr_config: Optional[Dict] = None,
        progress_callback: Optional[Callable] = None
    ) -> str:
        """提交文档处理任务"""
        try:
            from backend.tasks.document_tasks import process_document_task
        except ImportError:
            from tasks.document_tasks import process_document_task
        
        task_id = self.create_task_id("doc_proc")

        # 检查并发限制
        can_accept = self.concurrency_controller.can_accept_task(
            user_id=user_id,
            queue_name='document_processing',
            required_memory_mb=len(file_content) / (1024 * 1024) + 100  # 文件大小 + 100MB缓冲
        )

        if not can_accept["can_accept"]:
            logger.warning(f"任务被拒绝: {can_accept['reason']}")
            raise Exception(f"无法提交任务: {can_accept['reason']}")

        # 存储文件内容到Redis（临时）- 使用二进制客户端
        file_key = f"temp_file:{task_id}"
        self.redis_binary_client.setex(file_key, 3600, file_content)  # 1小时过期
        
        # 创建任务记录
        db = get_db_session()
        try:
            self.persistence_service.create_task_record(
                db=db,
                task_id=task_id,
                user_id=user_id,
                task_type="document_processing",
                task_name=f"处理文档: {filename}",
                task_args=[task_id, user_id, filename, file_key, document_id, use_ocr, ocr_config],
                queue_name='document_processing',
                metadata={"filename": filename, "use_ocr": use_ocr}
            )
        finally:
            db.close()

        # 注册任务到并发控制器
        self.concurrency_controller.register_task(
            task_id=task_id,
            user_id=user_id,
            queue_name='document_processing',
            task_info={
                "filename": filename,
                "file_size": len(file_content),
                "use_ocr": use_ocr
            }
        )

        # 提交任务
        result = process_document_task.apply_async(
            args=[task_id, user_id, filename, file_key, document_id, use_ocr, ocr_config],
            task_id=task_id,
            queue='document_processing'
        )

        logger.info(f"提交文档处理任务: {task_id}")
        return task_id
    
    def submit_vectorization_task(
        self,
        user_id: str,
        document_id: int,
        sections_data: list,
        embedding_config: Optional[Dict] = None
    ) -> str:
        """提交向量化任务"""
        try:
            from backend.tasks.vectorization_tasks import vectorize_document_task
        except ImportError:
            from tasks.vectorization_tasks import vectorize_document_task
        
        task_id = self.create_task_id("vectorize")

        # 检查并发限制
        can_accept = self.concurrency_controller.can_accept_task(
            user_id=user_id,
            queue_name='vectorization',
            required_memory_mb=min(len(sections_data) * 5 + 100, 300)  # 更合理的内存估算，最大300MB
        )

        if not can_accept["can_accept"]:
            logger.warning(f"向量化任务被拒绝: {can_accept['reason']}")
            raise Exception(f"无法提交向量化任务: {can_accept['reason']}")

        # 创建任务记录
        db = get_db_session()
        try:
            self.persistence_service.create_task_record(
                db=db,
                task_id=task_id,
                user_id=user_id,
                task_type="vectorization",
                task_name=f"向量化文档: {document_id}",
                task_args=[task_id, user_id, document_id, sections_data, embedding_config],
                queue_name='vectorization',
                metadata={"document_id": document_id, "sections_count": len(sections_data)}
            )
        finally:
            db.close()

        # 注册任务到并发控制器
        self.concurrency_controller.register_task(
            task_id=task_id,
            user_id=user_id,
            queue_name='vectorization',
            task_info={
                "document_id": document_id,
                "sections_count": len(sections_data)
            }
        )

        # 提交任务
        result = vectorize_document_task.apply_async(
            args=[task_id, user_id, document_id, sections_data, embedding_config],
            task_id=task_id,
            queue='vectorization'
        )

        logger.info(f"提交向量化任务: {task_id}")
        return task_id
    
    def submit_ocr_task(
        self,
        user_id: str,
        image_data: bytes,
        ocr_config: Dict,
        filename: str
    ) -> str:
        """提交OCR任务"""
        try:
            from backend.tasks.ocr_tasks import process_ocr_task
        except ImportError:
            from tasks.ocr_tasks import process_ocr_task
        
        task_id = self.create_task_id("ocr")
        
        # 存储图像数据到Redis（临时）- 使用二进制客户端
        image_key = f"temp_image:{task_id}"
        self.redis_binary_client.setex(image_key, 3600, image_data)  # 1小时过期
        
        # 创建任务记录
        db = get_db_session()
        try:
            self.persistence_service.create_task_record(
                db=db,
                task_id=task_id,
                user_id=user_id,
                task_type="ocr_processing",
                task_name=f"OCR处理: {filename}",
                task_args=[task_id, user_id, filename, image_key, ocr_config],
                queue_name='ocr_processing',
                metadata={"filename": filename, "ocr_config": ocr_config}
            )
        finally:
            db.close()

        # 提交任务
        result = process_ocr_task.apply_async(
            args=[task_id, user_id, filename, image_key, ocr_config],
            task_id=task_id,
            queue='ocr_processing'
        )

        logger.info(f"提交OCR任务: {task_id}")
        return task_id
    
    def get_task_status(self, task_id: str) -> Dict[str, Any]:
        """获取任务状态 - 优先使用数据库状态"""
        try:
            # 从Celery获取任务结果
            result = AsyncResult(task_id, app=self.celery_app)

            # 从Redis获取进度信息
            progress_key = f"task_progress:{task_id}"
            progress_data = self.redis_client.hgetall(progress_key)

            # 从数据库获取任务记录（包含user_id和状态）
            user_id = None
            db_status = None
            db_progress = None
            db_detail = None
            db_result = None

            try:
                db = get_db_session()
                try:
                    task_record = self.persistence_service.get_task_record(db, task_id)
                    if task_record:
                        user_id = task_record.user_id
                        db_status = task_record.status
                        db_progress = task_record.progress_percentage
                        db_detail = task_record.progress_detail
                        db_result = task_record.result

                        logger.debug(f"数据库任务状态: {task_id} -> {db_status}")
                finally:
                    db.close()
            except Exception as e:
                logger.warning(f"无法从数据库获取任务信息: {task_id}, {e}")

            # 优先使用数据库状态，如果数据库状态可用且不是PENDING
            if db_status and db_status != "PENDING":
                # 使用数据库状态
                state = db_status
                ready = db_status in ["SUCCESS", "FAILURE"]
                successful = db_status == "SUCCESS"
                failed = db_status == "FAILURE"
                task_result = db_result if db_result else (result.result if result.ready() else None)

                logger.debug(f"使用数据库状态: {task_id} -> {state}")
            else:
                # 回退到Celery状态
                state = result.state
                ready = result.ready()
                successful = result.successful() if result.ready() else False
                failed = result.failed() if result.ready() else False
                task_result = result.result if result.ready() else None

                logger.debug(f"使用Celery状态: {task_id} -> {state}")

            # 优先使用Redis进度信息，如果不可用则使用数据库进度
            if progress_data:
                # Redis数据可用，使用Redis数据
                progress_percentage = float(progress_data.get("percentage", 0))
                progress_detail = progress_data.get("detail", "")
                progress_stage = progress_data.get("stage", "")
            else:
                # Redis数据不可用，使用数据库数据
                progress_percentage = float(db_progress or 0)
                progress_detail = db_detail or ""
                progress_stage = ""

            # 对已完成任务确保进度为100%
            if state == "SUCCESS" and progress_percentage < 100:
                progress_percentage = 100.0
                progress_detail = progress_detail or "任务已完成"
                progress_stage = "completed"

            status = {
                "task_id": task_id,
                "state": state,
                "ready": ready,
                "successful": successful,
                "failed": failed,
                "result": task_result,
                "traceback": result.traceback if result.failed() else None,
                "user_id": user_id,
                "progress": {
                    "percentage": progress_percentage,
                    "detail": progress_detail,
                    "stage": progress_stage,
                    "start_time": progress_data.get("start_time", ""),
                    "update_time": progress_data.get("update_time", "")
                }
            }

            return status

        except Exception as e:
            logger.error(f"获取任务状态失败: {task_id}, {e}")
            return {
                "task_id": task_id,
                "state": "UNKNOWN",
                "ready": False,
                "successful": False,
                "failed": True,
                "error": str(e)
            }
    
    def cancel_task(self, task_id: str) -> bool:
        """取消任务"""
        try:
            self.celery_app.control.revoke(task_id, terminate=True)
            
            # 清理Redis中的进度数据
            progress_key = f"task_progress:{task_id}"
            self.redis_client.delete(progress_key)
            
            logger.info(f"任务已取消: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"取消任务失败: {task_id}, {e}")
            return False
    
    def cleanup_completed_tasks(self, max_age_hours: int = 24):
        """清理已完成的任务数据"""
        try:
            # 清理Redis中过期的进度数据
            pattern = "task_progress:*"
            keys = self.redis_client.keys(pattern)
            
            cleaned_count = 0
            for key in keys:
                # 检查任务是否已完成且超过指定时间
                task_id = key.split(":")[-1]
                result = AsyncResult(task_id, app=self.celery_app)
                
                if result.ready():
                    # 删除进度数据
                    self.redis_client.delete(key)
                    cleaned_count += 1
            
            logger.info(f"清理了 {cleaned_count} 个已完成任务的数据")
            return cleaned_count
            
        except Exception as e:
            logger.error(f"清理任务数据失败: {e}")
            return 0


# 全局任务管理器实例
task_manager = TaskManager()


def get_task_manager() -> TaskManager:
    """获取任务管理器实例"""
    return task_manager
