"""
基础任务类，提供通用的任务功能
"""

import time
import traceback
from typing import Dict, Any, Optional
from celery import Task
from celery.utils.log import get_task_logger
import redis
import os

# 直接创建Redis客户端，避免循环导入
REDIS_URL = os.getenv("REDIS_URL", "redis://localhost:6379/0")
redis_client = redis.Redis.from_url(REDIS_URL, decode_responses=True)  # 用于文本数据
redis_binary_client = redis.Redis.from_url(REDIS_URL, decode_responses=False)  # 用于二进制数据
from backend.services.task_persistence_service import get_task_persistence_service
from backend.services.concurrency_control import get_concurrency_controller
from backend.services.error_handler import get_error_handler
from backend.core.database import get_db_session
from backend.models.task_models import TaskStatus

logger = get_task_logger(__name__)

# WebSocket通知器 - 延迟导入避免循环依赖
def get_progress_notifier():
    """获取WebSocket进度通知器"""
    try:
        from backend.api.websocket import get_progress_notifier
        return get_progress_notifier()
    except ImportError:
        logger.warning("WebSocket模块未找到，跳过实时通知")
        return None


class BaseTask(Task):
    """基础任务类"""
    
    def __init__(self):
        self.redis_client = redis_client  # 用于文本数据
        self.redis_binary_client = redis_binary_client  # 用于二进制数据
        self.persistence_service = get_task_persistence_service()
        self.concurrency_controller = get_concurrency_controller()
        self.error_handler = get_error_handler()
    
    def update_progress(
        self,
        task_id: str,
        percentage: float,
        detail: str = "",
        stage: str = ""
    ):
        """更新任务进度"""
        try:
            # [FIX] 严格的进度边界检查，确保进度值在0-100范围内
            percentage = max(0.0, min(100.0, float(percentage or 0)))

            progress_key = f"task_progress:{task_id}"
            progress_data = {
                "percentage": str(percentage),
                "detail": detail,
                "stage": stage,
                "update_time": str(time.time())
            }

            # 如果是第一次更新，记录开始时间
            if not self.redis_client.exists(progress_key):
                progress_data["start_time"] = str(time.time())

            self.redis_client.hset(progress_key, mapping=progress_data)
            self.redis_client.expire(progress_key, 3600)  # 1小时过期，与其他地方保持一致

            # 同时更新数据库记录
            try:
                db = get_db_session()
                try:
                    self.persistence_service.log_task_progress(
                        db=db,
                        task_id=task_id,
                        percentage=percentage,
                        detail=detail,
                        stage=stage
                    )
                finally:
                    db.close()
            except Exception as db_error:
                logger.warning(f"数据库进度更新失败: {task_id}, {db_error}")

            # 🔧 优化WebSocket实时通知机制 - 确保数据格式一致
            try:
                progress_notifier = get_progress_notifier()
                if progress_notifier:
                    # 🔧 构建标准化的进度数据格式
                    websocket_progress_data = {
                        'percentage': float(percentage),
                        'detail': str(detail),
                        'stage': str(stage),
                        'update_time': time.time(),
                        'task_id': task_id  # 确保包含task_id
                    }

                    # 🔧 使用同步方式发送WebSocket通知，避免竞态条件
                    try:
                        # 检查是否在异步上下文中
                        import asyncio
                        try:
                            # 如果已经在事件循环中，使用create_task
                            loop = asyncio.get_running_loop()
                            asyncio.create_task(
                                progress_notifier.notify_progress_update(task_id, websocket_progress_data)
                            )
                            logger.info(f"📡 WebSocket进度通知已创建任务: {task_id} - {percentage}% - {stage}")
                        except RuntimeError:
                            # 如果不在事件循环中，使用简单的Redis发布
                            # 直接发布到Redis频道，让WebSocket服务器处理
                            notification_data = {
                                'type': 'progress_update',
                                'task_id': task_id,
                                'data': websocket_progress_data
                            }
                            import json
                            self.redis_client.publish(
                                f"task_progress_channel:{task_id}",
                                json.dumps(notification_data)
                            )
                            logger.info(f"📡 WebSocket进度通知已发布到Redis: {task_id} - {percentage}% - {stage}")
                    except Exception as notify_error:
                        logger.warning(f"WebSocket通知发送失败: {task_id} - {notify_error}")

            except Exception as ws_error:
                logger.warning(f"WebSocket进度通知失败: {task_id}, {ws_error}")

            logger.info(f"任务进度更新: {task_id} - {percentage}% - {detail}")

        except Exception as e:
            logger.error(f"更新进度失败: {task_id}, {e}")
    
    def mark_task_failed(self, task_id: str, error_message: str, traceback_str: str = None):
        """标记任务失败"""
        try:
            progress_key = f"task_progress:{task_id}"
            self.redis_client.hset(progress_key, mapping={
                "percentage": "0",
                "detail": f"任务失败: {error_message}",
                "stage": "failed",
                "update_time": str(time.time()),
                "error": error_message
            })

            # 更新数据库记录
            try:
                db = get_db_session()
                try:
                    self.persistence_service.update_task_status(
                        db=db,
                        task_id=task_id,
                        status=TaskStatus.FAILURE,
                        error_message=error_message,
                        traceback=traceback_str
                    )
                finally:
                    db.close()
            except Exception as db_error:
                logger.warning(f"数据库状态更新失败: {task_id}, {db_error}")

            # 发送WebSocket任务失败通知
            try:
                import asyncio
                progress_notifier = get_progress_notifier()
                if progress_notifier:
                    try:
                        loop = asyncio.get_event_loop()
                        if loop.is_running():
                            asyncio.create_task(
                                progress_notifier.notify_task_failed(task_id, error_message)
                            )
                        else:
                            loop.run_until_complete(
                                progress_notifier.notify_task_failed(task_id, error_message)
                            )
                    except RuntimeError:
                        asyncio.run(
                            progress_notifier.notify_task_failed(task_id, error_message)
                        )
            except Exception as ws_error:
                logger.warning(f"WebSocket任务失败通知失败: {task_id}, {ws_error}")

            # 从并发控制器注销任务
            try:
                self.concurrency_controller.unregister_task(task_id)
            except Exception as cc_error:
                logger.warning(f"并发控制器注销失败: {task_id}, {cc_error}")

            logger.error(f"任务失败: {task_id} - {error_message}")

        except Exception as e:
            logger.error(f"标记任务失败时出错: {task_id}, {e}")
    
    def mark_task_completed(self, task_id: str, result: Dict[str, Any]):
        """标记任务完成"""
        try:
            progress_key = f"task_progress:{task_id}"
            self.redis_client.hset(progress_key, mapping={
                "percentage": "100",
                "detail": "任务完成",
                "stage": "completed",
                "update_time": str(time.time()),
                "result": str(result)
            })
            # 为已完成任务设置更长的TTL（24小时）
            self.redis_client.expire(progress_key, 86400)

            # 更新数据库记录
            try:
                db = get_db_session()
                try:
                    self.persistence_service.update_task_status(
                        db=db,
                        task_id=task_id,
                        status=TaskStatus.SUCCESS,
                        progress_percentage=100.0,
                        progress_detail="任务完成",
                        progress_stage="completed",
                        result=result
                    )
                finally:
                    db.close()
            except Exception as db_error:
                logger.warning(f"数据库状态更新失败: {task_id}, {db_error}")

            # 发送WebSocket任务完成通知
            try:
                progress_notifier = get_progress_notifier()
                if progress_notifier:
                    import asyncio
                    import threading

                    def send_completion_notification():
                        try:
                            # 在新线程中创建事件循环
                            loop = asyncio.new_event_loop()
                            asyncio.set_event_loop(loop)
                            loop.run_until_complete(
                                progress_notifier.notify_task_completed(task_id, result)
                            )
                            loop.close()
                        except Exception as e:
                            logger.warning(f"WebSocket完成通知线程执行失败: {e}")

                    # 在后台线程中发送通知
                    thread = threading.Thread(target=send_completion_notification, daemon=True)
                    thread.start()

                    logger.info(f"📡 WebSocket任务完成通知已发送: {task_id}")

            except Exception as ws_error:
                logger.warning(f"WebSocket任务完成通知失败: {task_id}, {ws_error}")

            # 从并发控制器注销任务
            try:
                self.concurrency_controller.unregister_task(task_id)
            except Exception as cc_error:
                logger.warning(f"并发控制器注销失败: {task_id}, {cc_error}")

            logger.info(f"任务完成: {task_id}")

        except Exception as e:
            logger.error(f"标记任务完成时出错: {task_id}, {e}")
    
    def cleanup_temp_data(self, task_id: str):
        """清理临时数据"""
        try:
            # 清理临时文件
            temp_keys = [
                f"temp_file:{task_id}",
                f"temp_image:{task_id}",
                f"temp_data:{task_id}"
            ]
            
            for key in temp_keys:
                self.redis_client.delete(key)
            
            logger.info(f"清理临时数据: {task_id}")
            
        except Exception as e:
            logger.error(f"清理临时数据失败: {task_id}, {e}")
    
    def on_failure(self, exc, task_id, args, kwargs, einfo):
        """任务失败时的回调"""
        error_message = str(exc)
        traceback_str = str(einfo) if einfo else None

        # 使用错误处理器处理错误
        try:
            context = {
                "args": args,
                "kwargs": kwargs,
                "task_name": getattr(self, 'name', 'unknown'),
                "worker_name": getattr(self, 'request', {}).get('hostname', 'unknown')
            }
            self.error_handler.handle_task_error(task_id, exc, context)
        except Exception as e:
            logger.error(f"错误处理器处理失败: {e}")

        # 原有的失败处理逻辑
        self.mark_task_failed(task_id, error_message, traceback_str)
        self.cleanup_temp_data(task_id)

        logger.error(f"任务执行失败: {task_id}")
        logger.error(f"错误信息: {error_message}")
        logger.error(f"堆栈跟踪: {einfo}")
    
    def on_success(self, retval, task_id, args, kwargs):
        """任务成功时的回调"""
        self.mark_task_completed(task_id, retval)
        self.cleanup_temp_data(task_id)
        
        logger.info(f"任务执行成功: {task_id}")
    
    def on_retry(self, exc, task_id, args, kwargs, einfo):
        """任务重试时的回调"""
        logger.warning(f"任务重试: {task_id}, 原因: {exc}")
        self.update_progress(task_id, 0, f"任务重试中: {exc}", "retrying")

    def after_return(self, status, retval, task_id, args, kwargs, einfo):
        """任务完成后的清理回调"""
        try:
            import torch
            import gc

            # 清理CUDA缓存
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.synchronize()

            # 强制垃圾回收
            gc.collect()

            # 清理临时数据
            self.cleanup_temp_data(task_id)

            logger.info(f"任务 {task_id} 后置清理完成，状态: {status}")

        except Exception as e:
            logger.warning(f"任务 {task_id} 后置清理失败: {e}")


class ProgressCallback:
    """进度回调类"""
    
    def __init__(self, task_id: str, base_task: BaseTask):
        self.task_id = task_id
        self.base_task = base_task
        self.stage_progress = {}
    
    def __call__(self, percentage: float, detail: str = "", stage: str = ""):
        """进度回调函数"""
        self.base_task.update_progress(self.task_id, percentage, detail, stage)
    
    def set_stage(self, stage: str, start_percentage: float = 0, end_percentage: float = 100):
        """设置当前阶段"""
        self.stage_progress[stage] = {
            "start": start_percentage,
            "end": end_percentage,
            "current": start_percentage
        }
    
    def update_stage_progress(self, stage: str, stage_percentage: float, detail: str = ""):
        """更新阶段内进度"""
        if stage not in self.stage_progress:
            self.set_stage(stage)
        
        stage_info = self.stage_progress[stage]
        total_percentage = stage_info["start"] + (stage_info["end"] - stage_info["start"]) * (stage_percentage / 100)
        
        self.base_task.update_progress(self.task_id, total_percentage, detail, stage)
