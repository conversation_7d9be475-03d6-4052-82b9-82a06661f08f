#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检索功能测试脚本
测试ChromaDB的查询和检索功能
"""

import os
import chromadb
import numpy as np
from datetime import datetime
from typing import List, Dict, Any

def test_basic_retrieval():
    """测试基础检索功能"""
    print("=" * 60)
    print("检索功能测试报告")
    print("=" * 60)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 连接数据库
    db_path = "./data/chroma_db"
    collection_name = "knowledge_base"
    
    try:
        client = chromadb.PersistentClient(path=db_path)
        collection = client.get_collection(name=collection_name)
        
        doc_count = collection.count()
        print(f"📊 总文档数: {doc_count:,}")
        
        if doc_count == 0:
            print("❌ 集合中没有文档")
            return False
        
        # 测试1: 使用peek方法获取样本
        print(f"\n🔍 测试1: 获取文档样本")
        test_peek_method(collection)
        
        # 测试2: 测试相似度查询
        print(f"\n🔍 测试2: 相似度查询测试")
        test_similarity_search(collection)
        
        # 测试3: 测试不同查询参数
        print(f"\n🔍 测试3: 查询参数测试")
        test_query_parameters(collection)
        
        return True
        
    except Exception as e:
        print(f"❌ 检索功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_peek_method(collection):
    """测试peek方法"""
    try:
        # 获取少量样本
        results = collection.peek(limit=3)
        
        if not results:
            print("   ❌ peek方法返回空结果")
            return
        
        print(f"   ✅ peek方法成功")
        print(f"   返回字段: {list(results.keys())}")
        
        # 检查各个字段
        for field in ['ids', 'documents', 'metadatas']:
            if field in results and results[field]:
                print(f"   {field}: {len(results[field])} 项")
                
                # 显示第一项的详细信息
                if field == 'documents' and results[field]:
                    doc = results[field][0]
                    if doc:
                        preview = doc[:100] + "..." if len(doc) > 100 else doc
                        print(f"      第一个文档预览: {preview}")
                
                elif field == 'metadatas' and results[field]:
                    meta = results[field][0]
                    if meta:
                        print(f"      第一个元数据字段: {list(meta.keys())}")
            else:
                print(f"   {field}: 无数据")
        
        # 检查embeddings字段
        if 'embeddings' in results:
            embeddings = results['embeddings']
            if embeddings and embeddings[0]:
                print(f"   embeddings: {len(embeddings)} 项")
                print(f"      第一个向量维度: {len(embeddings[0])}")
                print(f"      向量值范围: [{min(embeddings[0]):.4f}, {max(embeddings[0]):.4f}]")
            else:
                print(f"   embeddings: 无向量数据")
        
    except Exception as e:
        print(f"   ❌ peek方法测试失败: {e}")

def test_similarity_search(collection):
    """测试相似度搜索"""
    test_queries = [
        "军事",
        "技术发展", 
        "国际关系",
        "经济政策",
        "科技创新"
    ]
    
    for i, query in enumerate(test_queries):
        print(f"\n   查询 {i+1}: '{query}'")
        
        try:
            # 执行相似度搜索
            results = collection.query(
                query_texts=[query],
                n_results=5,
                include=['documents', 'metadatas', 'distances']
            )
            
            if not results or not results.get('ids'):
                print(f"      ❌ 查询无结果")
                continue
            
            ids = results['ids'][0] if results['ids'] else []
            documents = results['documents'][0] if results.get('documents') else []
            distances = results['distances'][0] if results.get('distances') else []
            metadatas = results['metadatas'][0] if results.get('metadatas') else []
            
            print(f"      ✅ 找到 {len(ids)} 个结果")
            
            # 显示前3个结果
            for j in range(min(3, len(ids))):
                print(f"         结果 {j+1}:")
                print(f"            ID: {ids[j]}")
                
                if j < len(distances):
                    print(f"            距离: {distances[j]:.4f}")
                
                if j < len(metadatas) and metadatas[j]:
                    filename = metadatas[j].get('filename', '未知')
                    print(f"            文件: {filename}")
                
                if j < len(documents) and documents[j]:
                    content = documents[j][:80] + "..." if len(documents[j]) > 80 else documents[j]
                    print(f"            内容: {content}")
                
                print()
        
        except Exception as e:
            print(f"      ❌ 查询失败: {e}")

def test_query_parameters(collection):
    """测试不同的查询参数"""
    test_query = "技术发展"
    
    # 测试不同的n_results
    print(f"   测试查询: '{test_query}'")
    
    for n_results in [1, 5, 10, 20]:
        try:
            results = collection.query(
                query_texts=[test_query],
                n_results=n_results,
                include=['distances']
            )
            
            if results and results.get('ids'):
                actual_results = len(results['ids'][0])
                distances = results['distances'][0] if results.get('distances') else []
                
                print(f"      n_results={n_results}: 实际返回 {actual_results} 个结果")
                
                if distances:
                    min_dist = min(distances)
                    max_dist = max(distances)
                    print(f"         距离范围: [{min_dist:.4f}, {max_dist:.4f}]")
            else:
                print(f"      n_results={n_results}: 无结果")
                
        except Exception as e:
            print(f"      n_results={n_results}: 查询失败 - {e}")

def test_embedding_consistency():
    """测试嵌入一致性"""
    print(f"\n🧪 嵌入一致性测试:")
    
    # 这里需要模拟RAG服务的嵌入过程
    # 由于我们无法直接访问嵌入模型，我们跳过这个测试
    print("   ⚠️ 需要RAG服务运行才能测试嵌入一致性")
    print("   建议: 启动backend服务后通过API测试")

def analyze_collection_statistics(collection):
    """分析集合统计信息"""
    print(f"\n📈 集合统计分析:")
    
    try:
        # 获取样本进行统计
        sample_size = min(50, collection.count())
        results = collection.peek(limit=sample_size)
        
        if not results:
            print("   ❌ 无法获取样本数据")
            return
        
        # 分析文档长度分布
        if results.get('documents'):
            documents = results['documents']
            lengths = [len(doc) if doc else 0 for doc in documents]
            
            if lengths:
                print(f"   文档长度统计 (样本: {len(lengths)}):")
                print(f"      最短: {min(lengths)} 字符")
                print(f"      最长: {max(lengths)} 字符")
                print(f"      平均: {sum(lengths)/len(lengths):.1f} 字符")
                
                # 长度分布
                short_docs = sum(1 for l in lengths if l < 50)
                medium_docs = sum(1 for l in lengths if 50 <= l < 200)
                long_docs = sum(1 for l in lengths if l >= 200)
                
                print(f"      短文档(<50字符): {short_docs}")
                print(f"      中等文档(50-200字符): {medium_docs}")
                print(f"      长文档(>=200字符): {long_docs}")
        
        # 分析文件类型分布
        if results.get('metadatas'):
            metadatas = results['metadatas']
            filenames = [meta.get('filename', '') for meta in metadatas if meta]
            
            if filenames:
                # 统计文件扩展名
                extensions = {}
                for filename in filenames:
                    if filename:
                        ext = filename.split('.')[-1].lower() if '.' in filename else 'unknown'
                        extensions[ext] = extensions.get(ext, 0) + 1
                
                print(f"   文件类型分布 (样本: {len(filenames)}):")
                for ext, count in sorted(extensions.items()):
                    print(f"      .{ext}: {count} 个文件")
    
    except Exception as e:
        print(f"   ❌ 统计分析失败: {e}")

def main():
    """主函数"""
    try:
        success = test_basic_retrieval()
        
        if success:
            # 连接数据库进行额外分析
            db_path = "./data/chroma_db"
            collection_name = "knowledge_base"
            
            client = chromadb.PersistentClient(path=db_path)
            collection = client.get_collection(name=collection_name)
            
            analyze_collection_statistics(collection)
            test_embedding_consistency()
        
        print("\n" + "=" * 60)
        if success:
            print("检索功能测试完成")
        else:
            print("检索功能测试失败")
        print("=" * 60)
        
        return success
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
