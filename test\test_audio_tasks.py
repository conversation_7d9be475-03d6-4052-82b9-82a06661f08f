"""
音频处理Celery任务测试
测试异步任务的执行和状态管理
"""

import sys
import os
import time
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from backend.core.task_queue import celery_app, get_task_manager
from backend.tasks.audio_processing_tasks import (
    vad_detection_task,
    speech_recognition_task,
    speaker_recognition_task,
    meeting_transcription_task,
    audio_preprocessing_task
)
from backend.utils.audio.enhanced_audio_processor import EnhancedAudioProcessor

class TestAudioTasks:
    """音频任务测试类"""
    
    @classmethod
    def setup_class(cls):
        """测试类初始化"""
        print("=== 初始化音频任务测试 ===")
        cls.test_user_id = "test_user_tasks"
        cls.test_files = []
        cls.task_ids = []
        
        # 创建测试音频文件
        cls.create_test_files()
    
    @classmethod
    def teardown_class(cls):
        """测试类清理"""
        print("=== 清理音频任务测试 ===")
        
        # 清理测试文件
        for file_path in cls.test_files:
            try:
                if os.path.exists(file_path):
                    os.unlink(file_path)
                    print(f"删除测试文件: {file_path}")
            except Exception as e:
                print(f"删除文件失败: {e}")
        
        # 清理任务
        task_manager = get_task_manager()
        for task_id in cls.task_ids:
            try:
                task_manager.cancel_task(task_id)
                print(f"取消任务: {task_id}")
            except Exception as e:
                print(f"取消任务失败: {e}")
    
    @classmethod
    def create_test_files(cls):
        """创建测试音频文件"""
        try:
            import numpy as np
            import soundfile as sf
            
            # 创建不同类型的测试音频
            sample_rate = 16000
            duration = 2.0  # 2秒
            
            # 1. 正弦波音频
            t = np.linspace(0, duration, int(sample_rate * duration))
            sine_wave = 0.5 * np.sin(2 * np.pi * 440 * t)  # A4音符
            
            temp_file1 = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
            sf.write(temp_file1.name, sine_wave, sample_rate)
            cls.test_files.append(temp_file1.name)
            
            # 2. 混合频率音频
            mixed_wave = (
                0.3 * np.sin(2 * np.pi * 440 * t) +  # A4
                0.3 * np.sin(2 * np.pi * 523 * t) +  # C5
                0.1 * np.random.normal(0, 0.1, len(t))  # 添加噪声
            )
            
            temp_file2 = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
            sf.write(temp_file2.name, mixed_wave, sample_rate)
            cls.test_files.append(temp_file2.name)
            
            print(f"✅ 创建测试音频文件: {len(cls.test_files)} 个")
            
        except ImportError:
            # 如果没有音频库，创建假的WAV文件
            for i in range(2):
                temp_file = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
                
                # 写入最小的WAV文件头
                wav_header = b'RIFF\x24\x08\x00\x00WAVEfmt \x10\x00\x00\x00\x01\x00\x01\x00\x80>\x00\x00\x00}\x00\x00\x02\x00\x10\x00data\x00\x08\x00\x00'
                temp_file.write(wav_header)
                temp_file.write(b'\x00' * 4096)  # 添加音频数据
                temp_file.close()
                
                cls.test_files.append(temp_file.name)
            
            print(f"✅ 创建模拟音频文件: {len(cls.test_files)} 个")
    
    def test_celery_connection(self):
        """测试Celery连接"""
        print("\n--- 测试Celery连接 ---")
        
        try:
            # 检查Celery应用状态
            inspect = celery_app.control.inspect()
            stats = inspect.stats()
            
            if stats:
                print("✅ Celery连接正常")
                for worker, stat in stats.items():
                    print(f"  Worker: {worker}")
                assert True  # 测试通过
            else:
                print("❌ 没有发现活跃的Celery Worker")
                print("请确保已启动Celery Worker: celery -A backend.core.task_queue worker --loglevel=info")
                assert False, "没有活跃的Celery Worker"
                
        except Exception as e:
            print(f"❌ Celery连接失败: {e}")
            assert False, f"Celery连接失败: {e}"
    
    def test_task_manager(self):
        """测试任务管理器"""
        print("\n--- 测试任务管理器 ---")
        
        try:
            task_manager = get_task_manager()
            
            # 测试Redis连接
            task_manager.redis_client.ping()
            print("✅ 任务管理器Redis连接正常")
            
            # 创建测试任务
            task_id = task_manager.create_task(
                task_type="test_audio",
                user_id=self.test_user_id,
                data={"test": "data"}
            )
            
            self.task_ids.append(task_id)
            print(f"✅ 创建测试任务成功: {task_id}")
            
            # 获取任务状态
            status = task_manager.get_task_status(task_id)
            assert status is not None
            print("✅ 获取任务状态成功")
            
        except Exception as e:
            print(f"❌ 任务管理器测试失败: {e}")
            assert False, f"任务管理器测试失败: {e}"
    
    def test_vad_detection_task(self):
        """测试VAD检测任务"""
        print("\n--- 测试VAD检测任务 ---")
        
        if not self.test_files:
            print("❌ 没有测试文件")
            assert False, "没有测试文件"
        
        try:
            # 模拟文件ID（实际应该是上传后的文件ID）
            file_ids = ["test_file_1", "test_file_2"]
            
            # 创建任务
            task_manager = get_task_manager()
            task_id = task_manager.create_task(
                task_type="vad_detection",
                user_id=self.test_user_id,
                data={
                    "file_ids": file_ids,
                    "config": {
                        "merge_length_s": 15,
                        "min_speech_duration": 0.5,
                        "threshold": 0.5
                    }
                }
            )
            
            self.task_ids.append(task_id)
            print(f"✅ VAD检测任务创建成功: {task_id}")
            
            # 注意：实际的任务执行需要真实的音频文件和模型
            # 这里只测试任务创建和状态管理
            
        except Exception as e:
            print(f"❌ VAD检测任务测试失败: {e}")
            assert False, f"VAD检测任务测试失败: {e}"
    
    def test_speech_recognition_task(self):
        """测试语音识别任务"""
        print("\n--- 测试语音识别任务 ---")
        
        try:
            file_ids = ["test_file_1"]
            
            task_manager = get_task_manager()
            task_id = task_manager.create_task(
                task_type="speech_recognition",
                user_id=self.test_user_id,
                data={
                    "file_ids": file_ids,
                    "language": "auto",
                    "use_itn": True,
                    "ban_emo_unk": False,
                    "config": {}
                }
            )
            
            self.task_ids.append(task_id)
            print(f"✅ 语音识别任务创建成功: {task_id}")
            
        except Exception as e:
            print(f"❌ 语音识别任务测试失败: {e}")
            assert False, f"语音识别任务测试失败: {e}"
    
    def test_speaker_recognition_task(self):
        """测试说话人识别任务"""
        print("\n--- 测试说话人识别任务 ---")
        
        try:
            file_ids = ["test_file_1"]
            
            task_manager = get_task_manager()
            task_id = task_manager.create_task(
                task_type="speaker_recognition",
                user_id=self.test_user_id,
                data={
                    "file_ids": file_ids,
                    "clustering_method": "auto",
                    "expected_speakers": 2,
                    "similarity_threshold": 0.7,
                    "config": {}
                }
            )
            
            self.task_ids.append(task_id)
            print(f"✅ 说话人识别任务创建成功: {task_id}")
            
        except Exception as e:
            print(f"❌ 说话人识别任务测试失败: {e}")
            assert False, f"说话人识别任务测试失败: {e}"
    
    def test_meeting_transcription_task(self):
        """测试会议转录任务"""
        print("\n--- 测试会议转录任务 ---")
        
        try:
            file_ids = ["test_file_1", "test_file_2"]
            
            task_manager = get_task_manager()
            task_id = task_manager.create_task(
                task_type="meeting_transcription",
                user_id=self.test_user_id,
                data={
                    "file_ids": file_ids,
                    "language": "auto",
                    "output_format": "timeline",
                    "include_timestamps": True,
                    "speaker_labeling": True,
                    "config": {}
                }
            )
            
            self.task_ids.append(task_id)
            print(f"✅ 会议转录任务创建成功: {task_id}")
            
        except Exception as e:
            print(f"❌ 会议转录任务测试失败: {e}")
            assert False, f"会议转录任务测试失败: {e}"
    
    def test_audio_preprocessing_task(self):
        """测试音频预处理任务"""
        print("\n--- 测试音频预处理任务 ---")
        
        try:
            file_ids = ["test_file_1"]
            
            task_manager = get_task_manager()
            task_id = task_manager.create_task(
                task_type="audio_preprocessing",
                user_id=self.test_user_id,
                data={
                    "file_ids": file_ids,
                    "target_sr": 16000,
                    "target_channels": 1,
                    "normalize": True,
                    "denoise": True,
                    "config": {
                        "target_db": -20.0
                    }
                }
            )
            
            self.task_ids.append(task_id)
            print(f"✅ 音频预处理任务创建成功: {task_id}")
            
        except Exception as e:
            print(f"❌ 音频预处理任务测试失败: {e}")
            assert False, f"音频预处理任务测试失败: {e}"
    
    def test_task_status_monitoring(self):
        """测试任务状态监控"""
        print("\n--- 测试任务状态监控 ---")
        
        if not self.task_ids:
            print("❌ 没有可监控的任务")
            print("ℹ️  跳过状态监控测试")
            return  # 跳过测试，不是错误
        
        try:
            task_manager = get_task_manager()
            
            for task_id in self.task_ids[-3:]:  # 只检查最后3个任务
                status = task_manager.get_task_status(task_id)
                
                if status:
                    print(f"任务 {task_id}: {status.get('state', 'UNKNOWN')}")
                else:
                    print(f"任务 {task_id}: 状态未知")
            
            print("✅ 任务状态监控测试完成")
            
        except Exception as e:
            print(f"❌ 任务状态监控测试失败: {e}")
            assert False, f"任务状态监控测试失败: {e}"
    
    def test_audio_processor_integration(self):
        """测试音频处理器集成"""
        print("\n--- 测试音频处理器集成 ---")
        
        if not self.test_files:
            print("❌ 没有测试文件")
            assert False, "没有测试文件"
        
        try:
            processor = EnhancedAudioProcessor()
            
            # 测试音频信息获取
            for i, file_path in enumerate(self.test_files[:2]):
                audio_info = processor.get_audio_info(file_path)
                
                if audio_info.is_valid:
                    print(f"✅ 文件 {i+1} 信息获取成功: {audio_info.duration:.2f}s")
                else:
                    print(f"❌ 文件 {i+1} 信息获取失败: {audio_info.error}")
                    assert False, f"文件 {i+1} 信息获取失败: {audio_info.error}"
            
            # 测试音频加载
            audio, sr = processor.load_audio(self.test_files[0])
            if audio is not None:
                print(f"✅ 音频加载成功: {len(audio)} 采样点, {sr}Hz")
            else:
                print("❌ 音频加载失败")
                assert False, "音频加载失败"
            
            # 测试音频质量分析
            try:
                quality_metrics = processor.analyze_audio_quality(audio, sr)
                if quality_metrics:
                    print(f"✅ 音频质量分析成功: 质量评分 {quality_metrics.get('quality_score', 0):.1f}")
                else:
                    print("❌ 音频质量分析失败")
            except Exception as quality_error:
                print(f"❌ 音频质量分析失败: {quality_error}")
                # 质量分析失败不应该导致整个测试失败，因为这可能是由于版本兼容性问题
            
            # 清理
            processor.cleanup_temp_files()
            
        except Exception as e:
            print(f"❌ 音频处理器集成测试失败: {e}")
            assert False, f"音频处理器集成测试失败: {e}"


def run_audio_tasks_test():
    """运行音频任务测试"""
    print("开始音频任务测试...\n")
    
    test_instance = TestAudioTasks()
    test_instance.setup_class()
    
    tests = [
        ("Celery连接", test_instance.test_celery_connection),
        ("任务管理器", test_instance.test_task_manager),
        ("VAD检测任务", test_instance.test_vad_detection_task),
        ("语音识别任务", test_instance.test_speech_recognition_task),
        ("说话人识别任务", test_instance.test_speaker_recognition_task),
        ("会议转录任务", test_instance.test_meeting_transcription_task),
        ("音频预处理任务", test_instance.test_audio_preprocessing_task),
        ("任务状态监控", test_instance.test_task_status_monitoring),
        ("音频处理器集成", test_instance.test_audio_processor_integration),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            test_func()  # 如果没有抛出异常，则认为测试通过
            results.append((test_name, True))
        except Exception as e:
            print(f"❌ {test_name}测试出错: {e}")
            results.append((test_name, False))
    
    # 清理
    test_instance.teardown_class()
    
    # 输出测试结果
    print("\n" + "="*50)
    print("音频任务测试结果汇总:")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有音频任务测试通过！")
        return True
    else:
        print("⚠️  部分测试失败，请检查系统配置")
        return False


if __name__ == "__main__":
    success = run_audio_tasks_test()
    sys.exit(0 if success else 1)
