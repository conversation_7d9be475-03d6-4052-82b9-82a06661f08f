#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python 3.11兼容性安装脚本
根据项目代码分析，安装所需的依赖包
"""

import subprocess
import sys
import os
import time

def run_uv_command(cmd, description="", ignore_errors=False):
    """运行uv命令"""
    print(f"\n{'='*60}")
    print(f"执行: {description}")
    print(f"命令: {cmd}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, shell=True, check=not ignore_errors, 
                              capture_output=True, text=True, timeout=300)
        
        if result.stdout:
            print("输出:", result.stdout[-1000:])  # 显示最后1000字符
        
        if result.stderr and result.returncode != 0:
            print("错误:", result.stderr[-500:])
        
        if result.returncode == 0:
            print("✅ 安装成功")
            return True
        else:
            print(f"❌ 安装失败，返回码: {result.returncode}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 命令执行超时")
        return False
    except Exception as e:
        print(f"❌ 命令执行异常: {e}")
        return False

def install_core_packages():
    """安装核心基础包"""
    print("🔧 第1步: 安装核心基础包")
    
    core_packages = [
        "numpy==1.24.3",
        "pandas>=1.5.0",
        "requests>=2.25.0",
        "python-dateutil>=2.8.0",
        "tqdm>=4.65.0",
        "psutil>=5.9.0"
    ]
    
    success_count = 0
    for pkg in core_packages:
        if run_uv_command(f"uv pip install {pkg}", f"核心包 {pkg}"):
            success_count += 1
        time.sleep(1)  # 避免并发问题
    
    print(f"核心包安装结果: {success_count}/{len(core_packages)}")
    return success_count >= len(core_packages) - 1  # 允许1个失败

def install_pytorch():
    """安装PyTorch"""
    print("\n🔧 第2步: 安装PyTorch")
    
    # 尝试安装CPU版本的PyTorch (更稳定)
    pytorch_cmd = "uv pip install torch==2.1.0 torchaudio==2.1.0 --index-url https://download.pytorch.org/whl/cpu"
    
    if run_uv_command(pytorch_cmd, "PyTorch CPU版本"):
        return True
    
    # 如果失败，尝试默认版本
    print("CPU版本失败，尝试默认版本...")
    return run_uv_command("uv pip install torch>=2.0.0,<2.2.0 torchaudio>=2.0.0,<2.2.0", 
                         "PyTorch默认版本", ignore_errors=True)

def install_streamlit():
    """安装Streamlit和Web框架"""
    print("\n🔧 第3步: 安装Streamlit和Web框架")
    
    web_packages = [
        "streamlit>=1.28.0,<1.40.0",
        "streamlit-extras>=0.3.0"
    ]
    
    success_count = 0
    for pkg in web_packages:
        if run_uv_command(f"uv pip install {pkg}", f"Web框架 {pkg}"):
            success_count += 1
        time.sleep(1)
    
    return success_count >= 1  # 至少streamlit要成功

def install_ml_packages():
    """安装机器学习包"""
    print("\n🔧 第4步: 安装机器学习包")
    
    ml_packages = [
        "scikit-learn>=1.3.0",
        "transformers>=4.36.0,<4.45.0",
        "accelerate>=0.21.0",
        "safetensors>=0.3.1",
        "sentence-transformers"
    ]
    
    success_count = 0
    for pkg in ml_packages:
        if run_uv_command(f"uv pip install {pkg}", f"机器学习 {pkg}", ignore_errors=True):
            success_count += 1
        time.sleep(2)  # ML包较大，多等待
    
    return success_count >= 3  # 至少3个成功

def install_audio_packages():
    """安装音频处理包"""
    print("\n🔧 第5步: 安装音频处理包")
    
    audio_packages = [
        "soundfile>=0.12.1",
        "SpeechRecognition>=3.10.0",
        "pydub>=0.25.1",
        "librosa>=0.10.0"
    ]
    
    success_count = 0
    for pkg in audio_packages:
        if run_uv_command(f"uv pip install {pkg}", f"音频处理 {pkg}", ignore_errors=True):
            success_count += 1
        time.sleep(1)
    
    return success_count >= 2  # 至少2个成功

def install_document_packages():
    """安装文档处理包"""
    print("\n🔧 第6步: 安装文档处理包")
    
    doc_packages = [
        "PyPDF2>=3.0.0",
        "python-docx>=0.8.11",
        "python-pptx>=0.6.21",
        "openpyxl>=3.0.0",
        "Pillow>=9.5.0"
    ]
    
    success_count = 0
    for pkg in doc_packages:
        if run_uv_command(f"uv pip install {pkg}", f"文档处理 {pkg}", ignore_errors=True):
            success_count += 1
        time.sleep(1)
    
    return success_count >= 3

def install_visualization():
    """安装可视化包"""
    print("\n🔧 第7步: 安装可视化包")
    
    viz_packages = [
        "matplotlib>=3.7.2",
        "scipy>=1.10.0",
        "plotly>=5.0.0"
    ]
    
    success_count = 0
    for pkg in viz_packages:
        if run_uv_command(f"uv pip install {pkg}", f"可视化 {pkg}", ignore_errors=True):
            success_count += 1
        time.sleep(1)
    
    return success_count >= 2

def install_vector_db():
    """安装向量数据库"""
    print("\n🔧 第8步: 安装向量数据库")
    
    # ChromaDB可能有兼容性问题，单独处理
    if run_uv_command("uv pip install chromadb>=0.4.0,<0.5.0", "ChromaDB", ignore_errors=True):
        return True
    
    print("ChromaDB安装失败，尝试其他版本...")
    return run_uv_command("uv pip install chromadb", "ChromaDB最新版", ignore_errors=True)

def install_llama_index():
    """安装LlamaIndex"""
    print("\n🔧 第9步: 安装LlamaIndex")
    
    llama_packages = [
        "llama-index>=0.9.0,<0.11.0",
        "llama-index-embeddings-ollama",
        "llama-index-llms-ollama",
        "llama-index-vector-stores-chroma"
    ]
    
    success_count = 0
    for pkg in llama_packages:
        if run_uv_command(f"uv pip install {pkg}", f"LlamaIndex {pkg}", ignore_errors=True):
            success_count += 1
        time.sleep(2)
    
    return success_count >= 2

def install_langchain():
    """安装LangChain"""
    print("\n🔧 第10步: 安装LangChain")
    
    langchain_packages = [
        "langchain>=0.1.0",
        "langchain-core>=0.1.0"
    ]
    
    success_count = 0
    for pkg in langchain_packages:
        if run_uv_command(f"uv pip install {pkg}", f"LangChain {pkg}", ignore_errors=True):
            success_count += 1
        time.sleep(1)
    
    return success_count >= 1

def install_optional_packages():
    """安装可选包"""
    print("\n🔧 第11步: 安装可选包")
    
    optional_packages = [
        "modelscope>=1.9.5",
        "pydantic>=2.0.0",
        "opencv-python>=4.8.0",
        "pytesseract>=0.3.10"
    ]
    
    success_count = 0
    for pkg in optional_packages:
        if run_uv_command(f"uv pip install {pkg}", f"可选包 {pkg}", ignore_errors=True):
            success_count += 1
        time.sleep(1)
    
    print(f"可选包安装结果: {success_count}/{len(optional_packages)}")

def test_imports():
    """测试关键包导入"""
    print("\n🧪 测试包导入")
    
    test_packages = [
        ("numpy", "import numpy as np; print(f'NumPy: {np.__version__}')"),
        ("pandas", "import pandas as pd; print(f'Pandas: {pd.__version__}')"),
        ("streamlit", "import streamlit as st; print(f'Streamlit: {st.__version__}')"),
        ("torch", "import torch; print(f'PyTorch: {torch.__version__}')"),
        ("sklearn", "import sklearn; print(f'Scikit-learn: {sklearn.__version__}')"),
        ("matplotlib", "import matplotlib; print(f'Matplotlib: {matplotlib.__version__}')"),
    ]
    
    success_count = 0
    for name, test_code in test_packages:
        try:
            result = subprocess.run([sys.executable, "-c", test_code], 
                                  capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                print(f"✅ {name}: {result.stdout.strip()}")
                success_count += 1
            else:
                print(f"❌ {name}: 导入失败")
        except Exception as e:
            print(f"❌ {name}: 测试异常 - {e}")
    
    print(f"\n📊 导入测试结果: {success_count}/{len(test_packages)}")
    return success_count >= len(test_packages) - 2  # 允许2个失败

def main():
    """主安装流程"""
    print("🚀 Python 3.11项目依赖安装脚本")
    print("基于项目代码分析的精确依赖列表")
    print("="*60)
    
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    
    # 确认继续
    response = input("\n是否开始安装? (y/N): ").strip().lower()
    if response != 'y':
        print("安装已取消")
        return
    
    # 执行安装步骤
    results = []
    
    results.append(("核心包", install_core_packages()))
    results.append(("PyTorch", install_pytorch()))
    results.append(("Streamlit", install_streamlit()))
    results.append(("机器学习", install_ml_packages()))
    results.append(("音频处理", install_audio_packages()))
    results.append(("文档处理", install_document_packages()))
    results.append(("可视化", install_visualization()))
    results.append(("向量数据库", install_vector_db()))
    results.append(("LlamaIndex", install_llama_index()))
    results.append(("LangChain", install_langchain()))
    
    # 安装可选包
    install_optional_packages()
    
    # 测试导入
    import_success = test_imports()
    
    # 显示结果
    print("\n" + "="*60)
    print("📋 安装结果总结")
    print("="*60)
    
    success_count = 0
    for name, success in results:
        status = "✅ 成功" if success else "❌ 失败"
        print(f"{name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n总体结果: {success_count}/{len(results)} 组件安装成功")
    print(f"导入测试: {'✅ 通过' if import_success else '❌ 部分失败'}")
    
    if success_count >= len(results) - 2 and import_success:
        print("\n🎉 安装基本成功！可以开始使用项目")
        print("\n下一步:")
        print("1. 运行: streamlit run Home.py")
        print("2. 测试语音处理: streamlit run pages/语音处理分析.py")
    else:
        print("\n⚠️ 安装存在问题，但核心功能可能仍可使用")
        print("建议检查失败的组件并手动安装")

if __name__ == "__main__":
    main()
