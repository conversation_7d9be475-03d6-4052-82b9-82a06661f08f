#!/usr/bin/env python3
"""
测试完整的认证和文档管理流程
"""

import requests
import json

# 测试配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

def test_auth_and_document_flow():
    """测试认证和文档管理完整流程"""
    print("🔍 测试认证和文档管理完整流程...")
    
    # 1. 测试用户注册
    print("\n1. 测试用户注册...")
    register_data = {
        "username": "testuser",
        "password": "testpass123",
        "full_name_pinyin": "ceshiyonghu",
        "full_name": "测试用户"
    }
    
    try:
        response = requests.post(f"{API_BASE}/auth/register", json=register_data, timeout=5)
        print(f"   注册状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ 用户注册成功")
        elif response.status_code == 400:
            print("   ⚠️ 用户可能已存在，继续测试登录")
        else:
            print(f"   ❌ 注册失败: {response.text}")
            
    except Exception as e:
        print(f"   ❌ 注册请求失败: {e}")
        return False
    
    # 2. 测试用户登录
    print("\n2. 测试用户登录...")
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(f"{API_BASE}/auth/login", json=login_data, timeout=5)
        print(f"   登录状态码: {response.status_code}")
        
        if response.status_code == 200:
            login_result = response.json()
            access_token = login_result.get("access_token")
            print("   ✅ 用户登录成功")
            print(f"   用户信息: {login_result.get('user', {}).get('username')}")
        else:
            print(f"   ❌ 登录失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 登录请求失败: {e}")
        return False
    
    # 3. 测试获取用户信息
    print("\n3. 测试获取用户信息...")
    try:
        headers = {"Authorization": f"Bearer {access_token}"}
        response = requests.get(f"{API_BASE}/auth/me", headers=headers, timeout=5)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            user_info = response.json()
            print(f"   ✅ 获取用户信息成功: {user_info.get('username')}")
        else:
            print(f"   ❌ 获取用户信息失败: {response.text}")
            
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    # 4. 测试文档列表API（使用有效token）
    print("\n4. 测试文档列表API（使用有效token）...")
    try:
        headers = {"Authorization": f"Bearer {access_token}"}
        response = requests.get(f"{API_BASE}/documents/documents", headers=headers, timeout=5)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 成功获取文档列表")
            print(f"   文档数量: {data.get('total', 0)}")
            print(f"   成功状态: {data.get('success')}")
        else:
            print(f"   ❌ 获取文档列表失败: {response.text}")
            
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
        return False
    
    # 5. 测试文档节点API（使用有效token）
    print("\n5. 测试文档节点API（使用有效token）...")
    try:
        headers = {"Authorization": f"Bearer {access_token}"}
        response = requests.get(f"{API_BASE}/documents/documents/1/nodes", headers=headers, timeout=5)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("   ✅ 文档节点API正常")
            print(f"   节点数量: {len(data.get('sections', []))}")
        elif response.status_code == 404:
            print("   ✅ 文档不存在（正常，因为没有上传文档）")
        else:
            print(f"   ❌ 文档节点API异常: {response.text}")
            
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
        return False
    
    # 6. 测试用户统计API
    print("\n6. 测试用户统计API...")
    try:
        headers = {"Authorization": f"Bearer {access_token}"}
        response = requests.get(f"{API_BASE}/documents/user/statistics", headers=headers, timeout=5)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("   ✅ 用户统计API正常")
            print(f"   统计信息: {data}")
        else:
            print(f"   ❌ 用户统计API异常: {response.text}")
            
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    return True

if __name__ == "__main__":
    print("🚀 开始测试认证和文档管理完整流程...")
    success = test_auth_and_document_flow()
    
    if success:
        print("\n🎉 测试完成！文档管理API需要有效的认证token才能访问。")
        print("\n💡 解决方案:")
        print("   1. 前端需要先登录获取有效token")
        print("   2. 使用获取的token访问文档管理API")
        print("   3. 检查前端的token管理逻辑")
    else:
        print("\n❌ 测试失败！")
