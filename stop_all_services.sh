#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================"
echo -e "语音处理智能平台 - 停止服务脚本"
echo -e "========================================${NC}"
echo

# 停止服务函数
stop_service() {
    local service_name=$1
    local pid_file=$2
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            echo -e "${YELLOW}🛑 停止${service_name}服务 (PID: $pid)...${NC}"
            kill -TERM "$pid"
            
            # 等待进程优雅退出
            local count=0
            while kill -0 "$pid" 2>/dev/null && [ $count -lt 10 ]; do
                sleep 1
                count=$((count + 1))
            done
            
            # 如果进程仍在运行，强制终止
            if kill -0 "$pid" 2>/dev/null; then
                echo -e "${RED}⚠️ 强制终止${service_name}服务...${NC}"
                kill -KILL "$pid"
            fi
            
            echo -e "${GREEN}✅ ${service_name}服务已停止${NC}"
        else
            echo -e "${YELLOW}⚠️ ${service_name}服务未运行${NC}"
        fi
        rm -f "$pid_file"
    else
        echo -e "${YELLOW}⚠️ 未找到${service_name}服务的PID文件${NC}"
    fi
}

# 停止前端服务
stop_service "前端" ".frontend.pid"

# 停止Celery Worker
stop_service "Celery Worker" ".celery.pid"

# 停止后端服务
stop_service "后端" ".backend.pid"

# 停止可能残留的进程
echo -e "${YELLOW}🔍 检查残留进程...${NC}"

# 查找并停止uvicorn进程
UVICORN_PIDS=$(pgrep -f "uvicorn.*backend.main:app")
if [ ! -z "$UVICORN_PIDS" ]; then
    echo -e "${YELLOW}🛑 停止残留的uvicorn进程...${NC}"
    echo "$UVICORN_PIDS" | xargs kill -TERM
    sleep 2
    echo "$UVICORN_PIDS" | xargs kill -KILL 2>/dev/null
fi

# 查找并停止celery进程
CELERY_PIDS=$(pgrep -f "celery.*worker")
if [ ! -z "$CELERY_PIDS" ]; then
    echo -e "${YELLOW}🛑 停止残留的celery进程...${NC}"
    echo "$CELERY_PIDS" | xargs kill -TERM
    sleep 2
    echo "$CELERY_PIDS" | xargs kill -KILL 2>/dev/null
fi

# 查找并停止npm/node进程
NPM_PIDS=$(pgrep -f "npm.*run.*dev")
if [ ! -z "$NPM_PIDS" ]; then
    echo -e "${YELLOW}🛑 停止残留的npm进程...${NC}"
    echo "$NPM_PIDS" | xargs kill -TERM
    sleep 2
    echo "$NPM_PIDS" | xargs kill -KILL 2>/dev/null
fi

NODE_PIDS=$(pgrep -f "node.*vite")
if [ ! -z "$NODE_PIDS" ]; then
    echo -e "${YELLOW}🛑 停止残留的node进程...${NC}"
    echo "$NODE_PIDS" | xargs kill -TERM
    sleep 2
    echo "$NODE_PIDS" | xargs kill -KILL 2>/dev/null
fi

# 清理临时文件
echo -e "${YELLOW}🧹 清理临时文件...${NC}"
rm -f .backend.pid .celery.pid .frontend.pid

echo
echo -e "${GREEN}========================================"
echo -e "✅ 所有服务已停止"
echo -e "========================================${NC}"
echo

# 显示端口占用情况
echo -e "${YELLOW}📊 检查端口占用情况:${NC}"
if command -v lsof &> /dev/null; then
    echo "端口 3000 (前端):"
    lsof -i :3000 || echo "  无进程占用"
    echo "端口 8002 (后端):"
    lsof -i :8002 || echo "  无进程占用"
    echo "端口 6379 (Redis):"
    lsof -i :6379 || echo "  无进程占用"
elif command -v netstat &> /dev/null; then
    echo "端口占用情况:"
    netstat -tulpn | grep -E ":(3000|8002|6379)" || echo "  相关端口无占用"
fi

echo
echo -e "${BLUE}💡 提示:${NC}"
echo "  - 如需重新启动: ./start_all_services.sh"
echo "  - 如需重启服务: ./restart_all_services.sh"
echo "  - Redis容器仍在运行，如需停止: docker stop redis"
echo
