<template>
  <div class="batch-file-list">
    <div class="list-header">
      <div class="header-info">
        <h4>文件列表 ({{ files.length }})</h4>
        <div class="status-summary">
          <span class="status-item success">
            <el-icon><CircleCheckFilled /></el-icon>
            成功: {{ successCount }}
          </span>
          <span class="status-item processing">
            <el-icon><Loading /></el-icon>
            处理中: {{ processingCount }}
          </span>
          <span class="status-item error">
            <el-icon><CircleCloseFilled /></el-icon>
            失败: {{ errorCount }}
          </span>
        </div>
      </div>
      
      <div class="header-actions">
        <el-button @click="selectAll" size="small" :disabled="files.length === 0">
          {{ allSelected ? '取消全选' : '全选' }}
        </el-button>
        <el-button @click="removeSelected" size="small" type="danger" :disabled="selectedFiles.length === 0">
          删除选中 ({{ selectedFiles.length }})
        </el-button>
      </div>
    </div>

    <div class="file-list-container">
      <div v-if="files.length === 0" class="empty-list">
        <div class="empty-icon">📁</div>
        <p>暂无文件</p>
      </div>
      
      <div v-else class="file-items">
        <div
          v-for="(file, index) in files"
          :key="file.uid || index"
          class="file-item"
          :class="{
            'is-selected': selectedFiles.includes(index),
            'is-processing': getFileStatus(index) === 'processing',
            'is-success': getFileStatus(index) === 'success',
            'is-error': getFileStatus(index) === 'error'
          }"
        >
          <!-- 选择框 -->
          <div class="file-checkbox">
            <el-checkbox
              :model-value="selectedFiles.includes(index)"
              @change="toggleFileSelection(index)"
            />
          </div>
          
          <!-- 文件信息 -->
          <div class="file-info" @click="handleFileClick(file, index)">
            <div class="file-icon">
              <el-icon size="24"><Headset /></el-icon>
            </div>
            
            <div class="file-details">
              <div class="file-name" :title="file.name">{{ file.name }}</div>
              <div class="file-meta">
                <span class="file-size">{{ formatFileSize(file.size) }}</span>
                <span class="file-type">{{ getFileExtension(file.name) }}</span>
                <span v-if="file.duration" class="file-duration">{{ formatDuration(file.duration) }}</span>
              </div>
            </div>
          </div>
          
          <!-- 处理状态 -->
          <div class="file-status">
            <div v-if="getFileStatus(index) === 'processing'" class="status-processing">
              <el-icon class="is-loading"><Loading /></el-icon>
              <span>处理中</span>
              <div class="progress-info">
                {{ getFileProgress(index) }}%
              </div>
            </div>
            
            <div v-else-if="getFileStatus(index) === 'success'" class="status-success">
              <el-icon color="var(--success-color)"><CircleCheckFilled /></el-icon>
              <span>完成</span>
            </div>
            
            <div v-else-if="getFileStatus(index) === 'error'" class="status-error">
              <el-icon color="var(--danger-color)"><CircleCloseFilled /></el-icon>
              <span>失败</span>
              <div class="error-info" :title="getFileError(index)">
                {{ getFileError(index) }}
              </div>
            </div>
            
            <div v-else class="status-pending">
              <el-icon color="var(--text-muted)"><Clock /></el-icon>
              <span>等待</span>
            </div>
          </div>
          
          <!-- 操作按钮 -->
          <div class="file-actions">
            <el-button
              text
              @click="handlePreview(file, index)"
              :icon="View"
              title="预览"
              size="small"
            />
            
            <el-button
              v-if="getFileStatus(index) === 'success'"
              text
              @click="handleDownload(file, index)"
              :icon="Download"
              title="下载结果"
              size="small"
            />
            
            <el-button
              text
              @click="handleRemove(index)"
              :icon="Delete"
              title="删除"
              size="small"
              type="danger"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 批量操作栏 -->
    <div v-if="files.length > 0" class="batch-actions">
      <div class="batch-info">
        <span>总计: {{ files.length }} 个文件</span>
        <span>总大小: {{ formatFileSize(totalSize) }}</span>
        <span v-if="totalDuration > 0">总时长: {{ formatDuration(totalDuration) }}</span>
      </div>
      
      <div class="batch-buttons">
        <el-button @click="retryFailed" :disabled="errorCount === 0" size="small">
          重试失败项 ({{ errorCount }})
        </el-button>
        <el-button @click="exportResults" :disabled="successCount === 0" size="small" type="primary">
          导出结果 ({{ successCount }})
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Headset,
  CircleCheckFilled,
  CircleCloseFilled,
  Loading,
  Clock,
  View,
  Download,
  Delete
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  files: {
    type: Array,
    default: () => []
  },
  processingStatus: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['remove-file', 'preview-file', 'download-result', 'retry-failed', 'export-results'])

// 响应式数据
const selectedFiles = ref([])

// 计算属性
const allSelected = computed(() => 
  props.files.length > 0 && selectedFiles.value.length === props.files.length
)

const successCount = computed(() => 
  props.files.filter((_, index) => getFileStatus(index) === 'success').length
)

const processingCount = computed(() => 
  props.files.filter((_, index) => getFileStatus(index) === 'processing').length
)

const errorCount = computed(() => 
  props.files.filter((_, index) => getFileStatus(index) === 'error').length
)

const totalSize = computed(() => 
  props.files.reduce((sum, file) => sum + (file.size || 0), 0)
)

const totalDuration = computed(() => 
  props.files.reduce((sum, file) => sum + (file.duration || 0), 0)
)

// 工具函数
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDuration = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

const getFileExtension = (filename) => {
  return filename.split('.').pop().toUpperCase()
}

const getFileStatus = (index) => {
  const fileId = props.files[index]?.uid || index
  return props.processingStatus[fileId]?.status || 'pending'
}

const getFileProgress = (index) => {
  const fileId = props.files[index]?.uid || index
  return props.processingStatus[fileId]?.progress || 0
}

const getFileError = (index) => {
  const fileId = props.files[index]?.uid || index
  return props.processingStatus[fileId]?.error || '未知错误'
}

// 事件处理
const toggleFileSelection = (index) => {
  const selectedIndex = selectedFiles.value.indexOf(index)
  if (selectedIndex > -1) {
    selectedFiles.value.splice(selectedIndex, 1)
  } else {
    selectedFiles.value.push(index)
  }
}

const selectAll = () => {
  if (allSelected.value) {
    selectedFiles.value = []
  } else {
    selectedFiles.value = props.files.map((_, index) => index)
  }
}

const removeSelected = async () => {
  if (selectedFiles.value.length === 0) return
  
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedFiles.value.length} 个文件吗？`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    // 按索引倒序删除，避免索引变化问题
    const sortedIndexes = [...selectedFiles.value].sort((a, b) => b - a)
    
    for (const index of sortedIndexes) {
      emit('remove-file', index)
    }
    
    selectedFiles.value = []
    ElMessage.success(`已删除 ${sortedIndexes.length} 个文件`)
    
  } catch {
    // 用户取消删除
  }
}

const handleFileClick = (file, index) => {
  // 点击文件时切换选择状态
  toggleFileSelection(index)
}

const handlePreview = (file, index) => {
  emit('preview-file', file, index)
}

const handleDownload = (file, index) => {
  emit('download-result', file, index)
}

const handleRemove = async (index) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除文件 "${props.files[index].name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    emit('remove-file', index)
    
    // 如果删除的文件在选中列表中，也要移除
    const selectedIndex = selectedFiles.value.indexOf(index)
    if (selectedIndex > -1) {
      selectedFiles.value.splice(selectedIndex, 1)
    }
    
    // 更新其他选中文件的索引
    selectedFiles.value = selectedFiles.value.map(i => i > index ? i - 1 : i)
    
  } catch {
    // 用户取消删除
  }
}

const retryFailed = () => {
  emit('retry-failed')
}

const exportResults = () => {
  emit('export-results')
}
</script>

<style scoped>
.batch-file-list {
  background: var(--card-bg);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background: var(--surface-bg);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.header-info h4 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
}

.status-summary {
  display: flex;
  gap: var(--spacing-lg);
}

.status-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 0.9rem;
}

.status-item.success {
  color: var(--success-color);
}

.status-item.processing {
  color: var(--warning-color);
}

.status-item.error {
  color: var(--danger-color);
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.file-list-container {
  max-height: 400px;
  overflow-y: auto;
}

.empty-list {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-xl);
  color: var(--text-muted);
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: var(--spacing-md);
}

.file-items {
  padding: var(--spacing-sm);
}

.file-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.file-item:hover {
  background: var(--surface-hover);
}

.file-item.is-selected {
  background: rgba(88, 166, 255, 0.1);
  border-color: var(--accent-primary);
}

.file-item.is-processing {
  background: rgba(255, 193, 7, 0.1);
}

.file-item.is-success {
  background: rgba(40, 167, 69, 0.1);
}

.file-item.is-error {
  background: rgba(248, 81, 73, 0.1);
}

.file-checkbox {
  flex-shrink: 0;
}

.file-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex: 1;
  cursor: pointer;
}

.file-icon {
  color: var(--accent-primary);
  flex-shrink: 0;
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-meta {
  display: flex;
  gap: var(--spacing-md);
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.file-status {
  flex-shrink: 0;
  min-width: 80px;
  text-align: center;
}

.status-processing,
.status-success,
.status-error,
.status-pending {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 0.85rem;
}

.progress-info,
.error-info {
  font-size: 0.75rem;
  color: var(--text-muted);
  max-width: 80px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-actions {
  display: flex;
  gap: var(--spacing-xs);
  flex-shrink: 0;
}

.batch-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
  background: var(--surface-bg);
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
}

.batch-info {
  display: flex;
  gap: var(--spacing-lg);
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.batch-buttons {
  display: flex;
  gap: var(--spacing-sm);
}
</style>
