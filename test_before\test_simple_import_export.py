#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化版结果导入导出功能测试
测试任务 11.5 - 结果导入导出功能
"""

import sys
import os
import json
import subprocess
import time
import requests
from datetime import datetime

def test_import_export_functions_exist():
    """测试导入导出函数是否存在"""
    print("📋 导入导出函数存在性测试")
    print("   描述: 检查所有导入导出相关函数是否正确定义")
    
    try:
        from pages.结果展示和编辑 import (
            display_import_export,
            display_export_functions,
            display_import_functions,
            generate_json_export,
            generate_txt_export,
            generate_srt_export,
            generate_vtt_export,
            generate_csv_export,
            get_file_extension,
            get_mime_type,
            format_time,
            format_srt_time,
            format_vtt_time,
            split_text_for_subtitle,
            parse_json_to_analysis,
            parse_txt_to_analysis,
            parse_txt_line,
            parse_time_string
        )
        
        # 验证所有函数都是可调用的
        functions = [
            display_import_export,
            display_export_functions,
            display_import_functions,
            generate_json_export,
            generate_txt_export,
            generate_srt_export,
            generate_vtt_export,
            generate_csv_export,
            get_file_extension,
            get_mime_type,
            format_time,
            format_srt_time,
            format_vtt_time,
            split_text_for_subtitle,
            parse_json_to_analysis,
            parse_txt_to_analysis,
            parse_txt_line,
            parse_time_string
        ]
        
        for func in functions:
            assert callable(func), f"{func.__name__} 不是可调用函数"
        
        print("   ✅ 所有导入导出函数完整")
        print("   结果: ✅ 通过")
        return True
        
    except ImportError as e:
        print(f"   ❌ 导入函数失败: {str(e)}")
        print("   结果: ❌ 失败")
        return False
    except Exception as e:
        print(f"   ❌ 函数检查失败: {str(e)}")
        print("   结果: ❌ 失败")
        return False

def test_utility_functions():
    """测试工具函数"""
    print("📋 工具函数测试")
    print("   描述: 测试文件格式、时间格式化等工具函数")
    
    try:
        from pages.结果展示和编辑 import (
            get_file_extension,
            get_mime_type,
            format_time,
            format_srt_time,
            format_vtt_time,
            split_text_for_subtitle,
            parse_time_string
        )
        
        # 测试文件扩展名
        assert get_file_extension("JSON (结构化数据)") == ".json"
        assert get_file_extension("TXT (纯文本)") == ".txt"
        assert get_file_extension("SRT (字幕)") == ".srt"
        
        # 测试MIME类型
        assert get_mime_type("JSON (结构化数据)") == "application/json"
        assert get_mime_type("TXT (纯文本)") == "text/plain"
        
        # 测试时间格式化
        assert format_time(3661) == "01:01:01"
        assert format_srt_time(3661.5) == "01:01:01,500"
        assert format_vtt_time(3661.5) == "01:01:01.500"
        
        # 测试时间解析
        assert parse_time_string("01:30:00") == 5400
        
        # 测试文本分割
        long_text = "这是一个很长的文本内容，需要分割成多个部分"
        chunks = split_text_for_subtitle(long_text, 15)
        assert len(chunks) > 1
        
        print("   ✅ 工具函数完整")
        print("   结果: ✅ 通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 工具函数测试失败: {str(e)}")
        print("   结果: ❌ 失败")
        return False

def test_text_parsing():
    """测试文本解析功能"""
    print("📋 文本解析功能测试")
    print("   描述: 测试TXT文件的行解析功能")
    
    try:
        from pages.结果展示和编辑 import parse_txt_line, parse_time_string
        
        # 测试时间戳解析
        line_data = parse_txt_line("[00:01:30 - 00:01:35] 说话人A: 这是一段测试内容")
        
        assert line_data['start_time'] == 90  # 1分30秒 = 90秒
        assert line_data['end_time'] == 95   # 1分35秒 = 95秒
        assert line_data['speaker_id'] == '说话人A'
        assert line_data['text'] == '这是一段测试内容'
        
        # 测试无时间戳的行
        line_data2 = parse_txt_line("说话人B: 这是另一段内容")
        assert line_data2['speaker_id'] == '说话人B'
        assert line_data2['text'] == '这是另一段内容'
        
        print("   ✅ 文本解析功能完整")
        print("   结果: ✅ 通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 文本解析测试失败: {str(e)}")
        print("   结果: ❌ 失败")
        return False

def test_json_parsing():
    """测试JSON解析功能"""
    print("📋 JSON解析功能测试")
    print("   描述: 测试JSON格式数据的解析")
    
    try:
        from pages.结果展示和编辑 import parse_json_to_analysis
        
        # 创建测试JSON数据
        test_data = {
            "metadata": {
                "audio_file": "test.wav",
                "duration": 30.0,
                "speaker_count": 2
            },
            "speakers": {
                "speaker1": {
                    "name": "张三",
                    "total_speaking_time": 15.0
                }
            },
            "segments": [
                {
                    "start_time": 0.0,
                    "end_time": 5.0,
                    "text": "测试内容",
                    "speaker_id": "speaker1",
                    "confidence": 0.95
                }
            ]
        }
        
        # 解析JSON数据
        result = parse_json_to_analysis(test_data)
        
        # 验证结果
        assert result is not None
        assert result.audio_duration == 30.0
        assert len(result.segments) == 1
        assert result.segments[0].text == "测试内容"
        
        print("   ✅ JSON解析功能完整")
        print("   结果: ✅ 通过")
        return True
        
    except Exception as e:
        print(f"   ❌ JSON解析测试失败: {str(e)}")
        print("   结果: ❌ 失败")
        return False

def test_streamlit_deployment():
    """测试Streamlit部署"""
    print("📋 Streamlit部署测试")
    print("   描述: 测试导入导出功能的Streamlit集成")
    
    try:
        # 启动Streamlit应用
        port = 8512
        cmd = [
            "streamlit", "run", "pages/结果展示和编辑.py",
            "--server.port", str(port),
            "--server.headless", "true"
        ]
        
        print(f"   🚀 启动Streamlit应用测试...")
        print(f"   执行命令: {' '.join(cmd)}")
        
        # 启动进程
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待应用启动
        time.sleep(8)
        
        # 检查应用是否响应
        try:
            response = requests.get(f"http://localhost:{port}", timeout=10)
            if response.status_code == 200:
                print(f"   ✅ Streamlit应用成功启动在端口 {port}")
                print("   ✅ 页面响应正常")
                
                # 终止进程
                process.terminate()
                process.wait(timeout=5)
                
                return True
            else:
                print(f"   ❌ 页面响应错误: {response.status_code}")
                process.terminate()
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"   ❌ 页面连接失败: {str(e)}")
            process.terminate()
            return False
            
    except Exception as e:
        print(f"   ❌ Streamlit部署测试失败: {str(e)}")
        return False

def run_simplified_test():
    """运行简化测试"""
    print("🎯 开始结果导入导出功能简化测试")
    print("=" * 60)
    print("🧪 测试结果导入导出核心功能...")
    print()
    
    # 测试用例列表
    test_cases = [
        ("导入导出函数存在性测试", test_import_export_functions_exist),
        ("工具函数测试", test_utility_functions),
        ("文本解析功能测试", test_text_parsing),
        ("JSON解析功能测试", test_json_parsing),
    ]
    
    # 运行测试
    results = []
    for test_name, test_func in test_cases:
        result = test_func()
        results.append((test_name, result))
        print()
    
    # 汇总结果
    print("=" * 60)
    print("🎯 核心功能测试总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"总测试用例: {total}")
    print(f"通过: {passed}")
    print(f"失败: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    print()
    
    print("📊 详细结果:")
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {status} - {test_name}")
    
    print()
    print("=" * 60)
    print("🚀 Streamlit部署测试")
    print("=" * 60)
    print()
    
    deployment_result = test_streamlit_deployment()
    
    print()
    print("=" * 60)
    print("🏆 最终测试结果")
    print("=" * 60)
    
    basic_passed = all(result for _, result in results)
    print(f"核心功能测试: {'✅ 通过' if basic_passed else '❌ 失败'}")
    print(f"部署测试: {'✅ 通过' if deployment_result else '❌ 失败'}")
    print()
    
    if basic_passed and deployment_result:
        print("总体结果: 🎉 全部通过")
        print()
        print("🎯 任务11.5 - 结果导入导出功能 ✅ 完成")
        print("✨ 导入导出核心功能已实现并测试通过！")
        print("📤 支持多种导出格式的内容生成")
        print("📥 支持多种导入方式的数据解析")
        print("🛠️ 提供完整的工具函数支持")
        print("🌐 Streamlit界面集成成功")
        return True
    else:
        print("总体结果: ❌ 部分失败")
        return False

if __name__ == "__main__":
    success = run_simplified_test()
    sys.exit(0 if success else 1) 