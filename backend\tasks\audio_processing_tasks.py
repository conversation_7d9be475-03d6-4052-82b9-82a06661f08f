"""
音频处理异步任务
包括VAD检测、语音识别、说话人识别、会议转录等功能
"""

import os
import time
import tempfile
import logging
import numpy as np
from typing import Dict, Any, List, Optional
from pathlib import Path
from datetime import datetime, timezone

from celery import current_app
from backend.core.task_queue import celery_app
from backend.tasks.base_task import BaseTask, ProgressCallback
from backend.core.config import settings
from backend.core.gpu_manager import GPUContext, get_gpu_manager
from backend.utils.audio.enhanced_audio_processor import (
    EnhancedAudioProcessor, ProcessingConfig, NormalizationMethod, ProcessingMethod
)
from backend.utils.audio.optimized_funasr_manager import get_funasr_manager, cleanup_funasr_manager
from backend.utils.audio.speech_recognition_core import clean_sensevoice_text

logger = logging.getLogger(__name__)


def convert_numpy_types(obj):
    """
    递归转换numpy类型为Python原生类型，解决JSON序列化问题
    """
    if isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {key: convert_numpy_types(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_types(item) for item in obj]
    elif isinstance(obj, tuple):
        return tuple(convert_numpy_types(item) for item in obj)
    return obj


@celery_app.task(bind=True, base=BaseTask, queue='audio_processing')
def vad_detection_task(
    self,
    task_id: str,
    user_id: str,
    file_ids: List[str],
    config: Optional[Dict] = None
):
    """VAD语音活动检测任务"""
    progress_callback = ProgressCallback(task_id, self)
    
    try:
        progress_callback(0, "开始VAD检测任务", "initializing")
        
        # 获取files路径
        file_paths = []
        for file_id in file_ids:
            file_path = _get_audio_file_path(user_id, file_id)
            if file_path and os.path.exists(file_path):
                file_paths.append(file_path)
            else:
                logger.warning(f"File not found: {file_id}")
        
        if not file_paths:
            raise ValueError("没有找到有效的音频files")
        
        progress_callback(10, f"找到 {len(file_paths)} 个音频files", "file_validation")
        
        # LoadVAD模型
        progress_callback(20, "LoadVAD模型", "model_loading")
        
        try:
            from backend.utils.audio.speech_recognition_utils import load_vad_model
            vad_model_path = _get_model_path('vad')
            vad_model = load_vad_model(vad_model_path)
        except Exception as e:
            logger.error(f"VAD model loading failed: {e}")
            raise ValueError(f"VAD model loading failed: {e}")
        
        progress_callback(30, "VAD模型Load完成", "model_loaded")
        
        # 处理每个files
        results = []
        total_files = len(file_paths)
        
        for i, file_path in enumerate(file_paths):
            file_progress_start = 30 + (i * 60 // total_files)
            file_progress_end = 30 + ((i + 1) * 60 // total_files)
            
            progress_callback(
                file_progress_start, 
                f"处理files {i+1}/{total_files}: {Path(file_path).name}", 
                "processing"
            )
            
            try:
                # 执行VAD检测
                vad_result = _perform_vad_detection(
                    file_path, vad_model, config or {}, progress_callback,
                    file_progress_start, file_progress_end
                )
                
                results.append({
                    'file_id': file_ids[i],
                    'file_path': file_path,
                    'status': 'success',
                    'result': vad_result
                })
                
            except Exception as e:
                logger.error(f"VAD detection failed: {file_path}, error: {e}")
                results.append({
                    'file_id': file_ids[i],
                    'file_path': file_path,
                    'status': 'error',
                    'error': str(e)
                })
        
        progress_callback(95, "保存结果", "saving")
        
        # 统计结果
        success_count = sum(1 for r in results if r['status'] == 'success')
        error_count = len(results) - success_count
        
        final_result = {
            'task_type': 'vad_detection',
            'total_files': total_files,
            'success_count': success_count,
            'error_count': error_count,
            'results': results,
            'processing_time': time.time() - float(progress_callback.base_task.redis_client.hget(f"task_progress:{task_id}", "start_time") or time.time())
        }
        
        progress_callback(100, "VAD检测任务完成", "completed")
        return final_result
        
    except Exception as e:
        logger.error(f"VAD检测Task failed: {e}")
        progress_callback(0, f"Task failed: {str(e)}", "failed")
        raise


@celery_app.task(bind=True, base=BaseTask, queue='audio_processing')
def speech_recognition_task(
    self,
    task_id: str,
    user_id: str,
    file_ids: List[str],
    language: str = "auto",
    use_itn: bool = True,
    ban_emo_unk: bool = False,
    config: Optional[Dict] = None
):
    """语音识别任务 - Optimized version"""
    progress_callback = ProgressCallback(task_id, self)
    recognizer = None

    # 使用GPU资源管理器
    try:
        with GPUContext(task_id, timeout=60) as gpu_ctx:
            import torch
            import gc
            import psutil

            # 监控系统资源
            process = psutil.Process()
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            logger.info(f"任务 {task_id} Initial memory usage: {initial_memory:.1f}MB")

            progress_callback(0, "开始语音识别任务", "initializing")

        # 安全的CUDA内存管理
        if torch.cuda.is_available():
            try:
                # 检查CUDA设备状态
                device_count = torch.cuda.device_count()
                current_device = torch.cuda.current_device()
                logger.info(f"CUDA device info: {device_count}个设备, 当前设备: {current_device}")

                # 安全清理CUDA缓存（仅在任务开始时）
                torch.cuda.empty_cache()

                # 设置保守的内存管理策略
                torch.backends.cudnn.benchmark = False  # 禁用自动优化以避免内存峰值
                torch.backends.cudnn.deterministic = True  # 确保结果可重现

            except Exception as cuda_error:
                logger.warning(f"CUDA initialization warning: {cuda_error}, will use CPU mode")
                # 不抛出异常，允许任务继续使用CPU
        
        # 获取files路径
        file_paths = []
        for file_id in file_ids:
            file_path = _get_audio_file_path(user_id, file_id)
            if file_path and os.path.exists(file_path):
                file_paths.append(file_path)
        
        if not file_paths:
            raise ValueError("没有找到有效的音频files")
        
        progress_callback(10, f"找到 {len(file_paths)} 个音频files", "file_validation")

        # 🔧 设置离线环境和验证模型路径
        progress_callback(15, "设置离线环境和验证模型路径", "model_validation")

        _setup_offline_environment()

        if not _validate_all_model_paths():
            raise ValueError("模型路径验证失败，请检查模型配置")

        # Use optimized FunASR manager
        progress_callback(20, "初始化优化FunASR管理器", "optimized_manager_init")

        try:
            # 获取模型路径
            model_path = _get_model_path('sensevoice')

            # 准备配置
            recognition_config = {
                'language': language,
                'use_itn': use_itn,
                'ban_emo_unk': ban_emo_unk,
                'batch_size_s': 60,
                'merge_vad': True,
                'merge_length_s': 15
            }

            # 检查内存使用
            current_memory = process.memory_info().rss / 1024 / 1024  # MB
            logger.info(f"Memory usage after optimizer initialization: {current_memory:.1f}MB (+{current_memory-initial_memory:.1f}MB)")

        except Exception as e:
            logger.error(f"Optimized FunASR manager initialization failed: {e}")
            raise ValueError(f"Optimized FunASR manager initialization failed: {e}")

        progress_callback(30, "优化FunASR管理器初始化完成", "optimized_manager_ready")
        
        # 处理每个files
        results = []
        total_files = len(file_paths)
        
        for i, file_path in enumerate(file_paths):
            file_progress_start = 30 + (i * 60 // total_files)
            file_progress_end = 30 + ((i + 1) * 60 // total_files)
            
            progress_callback(
                file_progress_start,
                f"识别files {i+1}/{total_files}: {Path(file_path).name}",
                "recognition"
            )
            
            try:
                # 监控内存使用但避免强制清理
                current_memory = process.memory_info().rss / 1024 / 1024  # MB
                if current_memory > 3000:  # 提高阈值到3GB，避免频繁清理
                    logger.warning(f"High memory usage: {current_memory:.1f}MB")
                    # 只在内存使用极高时才进行温and的清理
                    if current_memory > 4000:  # 4GB阈值
                        logger.warning("Performing gentle memory cleanup")
                        gc.collect()  # Remove强制CUDA缓存清理

                # 执行优化的语音识别
                recognition_result = _perform_optimized_speech_recognition(
                    file_path, model_path, recognition_config, progress_callback,
                    file_progress_start, file_progress_end
                )

                results.append({
                    'file_id': file_ids[i],
                    'file_path': file_path,
                    'status': 'success',
                    'result': recognition_result
                })

            except Exception as e:
                logger.error(f"Speech recognition failed: {file_path}, error: {e}")
                results.append({
                    'file_id': file_ids[i],
                    'file_path': file_path,
                    'status': 'error',
                    'error': str(e)
                })
        
        progress_callback(95, "保存识别结果", "saving")

        # 保存Process results到数据库
        try:
            from backend.core.database import get_db_session
            from backend.models.audio import ProcessingResult, AudioFile

            db = get_db_session()
            try:
                # 为每个成功处理的filescreate ProcessingResult record
                for result in results:
                    if result['status'] == 'success':
                        file_id = result['file_id']

                        # 查找corresponding AudioFile record
                        audio_file = db.query(AudioFile).filter(
                            AudioFile.id == int(file_id),
                            AudioFile.owner_id == int(user_id)
                        ).first()

                        if audio_file:
                            # create ProcessingResult record
                            processing_result = ProcessingResult(
                                audio_file_id=audio_file.id,
                                processing_type="speech_recognition",
                                result_data=result['result'],
                                processing_config={
                                    'language': language,
                                    'use_itn': use_itn,
                                    'ban_emo_unk': ban_emo_unk,
                                    'merge_vad': True,
                                    'batch_size_s': 60,
                                    'merge_length_s': 15
                                },
                                status="completed"
                            )

                            db.add(processing_result)
                            logger.info(f"For file {file_id} create ProcessingResult record")
                        else:
                            logger.warning(f"File ID not found {file_id} corresponding AudioFile record")

                # 提交所有更改
                db.commit()
                logger.info(f"Successfully saved {len([r for r in results if r['status'] == 'success'])} ProcessingResult records")

            except Exception as e:
                db.rollback()
                logger.error(f"Failed to save ProcessingResult: {e}")
                raise
            finally:
                db.close()

        except Exception as e:
            logger.error(f"Database operation failed: {e}")
            # 不抛出异常，避免影响任务完成

        # 统计结果
        success_count = sum(1 for r in results if r['status'] == 'success')
        error_count = len(results) - success_count

        final_result = {
            'task_type': 'speech_recognition',
            'total_files': total_files,
            'success_count': success_count,
            'error_count': error_count,
            'language': language,
            'results': results,
            'processing_time': time.time() - float(progress_callback.base_task.redis_client.hget(f"task_progress:{task_id}", "start_time") or time.time())
        }

        progress_callback(100, "语音识别任务完成", "completed")

        # 最终内存检查
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        logger.info(f"任务 {task_id} Final memory usage: {final_memory:.1f}MB")

        return final_result

    except Exception as e:
        logger.error(f"语音识别Task failed: {e}")
        progress_callback(0, f"Task failed: {str(e)}", "failed")
        raise

    finally:
        # 安全的资源清理
        try:
            # 清理优化FunASR管理器资源
            try:
                cleanup_funasr_manager()
                logger.info(f"任务 {task_id} Optimized FunASR manager resource cleanup completed")
            except Exception as e:
                logger.warning(f"Optimized FunASR manager cleanup failed: {e}")

            # 清理识别器资源（备用）
            if 'recognizer' in locals() and recognizer and hasattr(recognizer, 'cleanup'):
                try:
                    recognizer.cleanup()
                    logger.info(f"任务 {task_id} Recognizer resource cleanup completed")
                except Exception as e:
                    logger.warning(f"Recognizer cleanup failed: {e}")

            # 温and的CUDA清理（仅在任务完全结束时）
            if torch.cuda.is_available():
                try:
                    # 等待所有CUDA操作完成
                    torch.cuda.synchronize()
                    # 温and清理缓存
                    torch.cuda.empty_cache()
                except Exception as e:
                    logger.warning(f"CUDA cleanup failed: {e}")

            # 最后进行垃圾回收
            gc.collect()
            logger.info(f"任务 {task_id} Resource cleanup completed")

        except Exception as cleanup_error:
            logger.error(f"资源清理过程中发生error: {cleanup_error}")
            # 不重新抛出异常，避免掩盖原始error


def _perform_optimized_speech_recognition(file_path: str, model_path: str, config: Dict,
                                        progress_callback, start_progress: float, end_progress: float) -> Dict:
    """使用优化FunASR管理器执行语音识别"""
    try:
        progress_callback(start_progress + 10, "开始优化语音识别", "optimized_recognition_processing")

        # 获取优化的FunASR管理器
        funasr_manager = get_funasr_manager()

        # Load模型（如果尚未Load）
        if not funasr_manager.load_model(model_path):
            raise RuntimeError("FunASR模型Load失败")

        # 执行识别
        result = funasr_manager.generate(
            input_audio=file_path,
            language=config.get('language', 'auto'),
            use_itn=config.get('use_itn', True),
            batch_size_s=config.get('batch_size_s', 60)
        )

        if result is None:
            raise RuntimeError("语音识别返回空结果")

        progress_callback(end_progress - 5, "优化语音识别完成", "optimized_recognition_completed")

        return {
            'file_path': file_path,
            'status': 'success',
            'result': result,
            'processing_time': 0  # TODO: 添加时间统计
        }

    except Exception as e:
        logger.error(f"优化语音识别执行失败: {e}")
        raise


@celery_app.task(bind=True, base=BaseTask, queue='audio_processing')
def speaker_recognition_task(
    self,
    task_id: str,
    user_id: str,
    file_ids: List[str],
    clustering_method: str = "auto",
    expected_speakers: int = 2,
    similarity_threshold: float = 0.7,
    config: Optional[Dict] = None
):
    """说话人识别任务"""
    progress_callback = ProgressCallback(task_id, self)
    
    try:
        progress_callback(0, "开始说话人识别任务", "initializing")
        
        # 获取files路径
        file_paths = []
        for file_id in file_ids:
            file_path = _get_audio_file_path(user_id, file_id)
            if file_path and os.path.exists(file_path):
                file_paths.append(file_path)
        
        if not file_paths:
            raise ValueError("没有找到有效的音频files")
        
        progress_callback(10, f"找到 {len(file_paths)} 个音频files", "file_validation")
        
        # Load说话人识别模型
        progress_callback(20, "Load说话人识别模型", "model_loading")
        
        try:
            from backend.utils.audio.speaker_recognition import SpeakerRecognition

            model_path = _get_model_path('speaker')  # 使用正确的模型类型
            speaker_recognizer = SpeakerRecognition(model_path=model_path, device="auto")

        except Exception as e:
            logger.error(f"Speaker recognition model loading failed: {e}")
            raise ValueError(f"Speaker recognition model loading failed: {e}")
        
        progress_callback(30, "说话人识别模型Load完成", "model_loaded")
        
        # 处理每个files
        results = []
        total_files = len(file_paths)
        
        for i, file_path in enumerate(file_paths):
            file_progress_start = 30 + (i * 60 // total_files)
            file_progress_end = 30 + ((i + 1) * 60 // total_files)
            
            progress_callback(
                file_progress_start,
                f"分析files {i+1}/{total_files}: {Path(file_path).name}",
                "speaker_analysis"
            )
            
            try:
                # 执行说话人识别
                speaker_result = _perform_speaker_recognition(
                    file_path, speaker_recognizer, clustering_method,
                    expected_speakers, similarity_threshold, config or {},
                    progress_callback, file_progress_start, file_progress_end
                )
                
                results.append({
                    'file_id': file_ids[i],
                    'file_path': file_path,
                    'status': 'success',
                    'result': speaker_result
                })
                
            except Exception as e:
                logger.error(f"Speaker recognition failed: {file_path}, error: {e}")
                results.append({
                    'file_id': file_ids[i],
                    'file_path': file_path,
                    'status': 'error',
                    'error': str(e)
                })
        
        progress_callback(95, "保存识别结果", "saving")

        # 保存Process results到数据库
        try:
            from backend.core.database import get_db_session
            from backend.models.audio import ProcessingResult, AudioFile

            db = get_db_session()
            try:
                # 为每个成功处理的filescreate ProcessingResult record
                for result in results:
                    if result['status'] == 'success':
                        file_id = result['file_id']

                        # 查找corresponding AudioFile record
                        audio_file = db.query(AudioFile).filter(
                            AudioFile.id == int(file_id),
                            AudioFile.owner_id == int(user_id)
                        ).first()

                        if audio_file:
                            # 转换数据以确保JSON序列化兼容性
                            try:
                                from backend.utils.data_converter import safe_convert_for_json
                                safe_result_data = safe_convert_for_json(result['result'])
                                safe_processing_config = safe_convert_for_json({
                                    'clustering_method': clustering_method,
                                    'expected_speakers': expected_speakers,
                                    'similarity_threshold': similarity_threshold,
                                    'config': config
                                })
                                logger.info("Data converted to JSON compatible format")
                            except Exception as convert_error:
                                logger.warning(f"Data conversion failed, using original data: {convert_error}")
                                safe_result_data = result['result']
                                safe_processing_config = {
                                    'clustering_method': clustering_method,
                                    'expected_speakers': expected_speakers,
                                    'similarity_threshold': similarity_threshold,
                                    'config': config
                                }

                            # create ProcessingResult record
                            processing_result = ProcessingResult(
                                audio_file_id=audio_file.id,
                                processing_type="speaker_recognition",
                                result_data=safe_result_data,
                                processing_config=safe_processing_config,
                                processing_time=result.get('processing_time', 0),
                                confidence_score=result['result'].get('confidence', 0.0) if result.get('result') else 0.0,
                                status="completed"
                            )

                            db.add(processing_result)

                            # 更新AudioFile状态
                            audio_file.status = "analyzed"
                            audio_file.processed_at = datetime.now(timezone.utc)

                db.commit()
                logger.info(f"Speaker recognition results saved to database，Task ID: {task_id}")

            except Exception as db_error:
                db.rollback()
                logger.error(f"Failed to save recognition results to database: {db_error}")
            finally:
                db.close()

        except Exception as e:
            logger.error(f"Database operation failed: {e}")

        # 统计结果
        success_count = sum(1 for r in results if r['status'] == 'success')
        error_count = len(results) - success_count

        final_result = {
            'task_type': 'speaker_recognition',
            'total_files': total_files,
            'success_count': success_count,
            'error_count': error_count,
            'clustering_method': clustering_method,
            'expected_speakers': expected_speakers,
            'results': results,
            'processing_time': time.time() - float(progress_callback.base_task.redis_client.hget(f"task_progress:{task_id}", "start_time") or time.time())
        }

        progress_callback(100, "说话人识别任务完成", "completed")

        # 转换numpy类型以避免JSON序列化error
        try:
            from backend.utils.data_converter import convert_numpy_types
            final_result = convert_numpy_types(final_result)
            logger.info("已转换numpy类型，避免JSON序列化error")
        except Exception as convert_error:
            logger.warning(f"Data type conversion failed: {convert_error}")

        # [FIX] 新增：任务完成后的状态更新andWebSocket通知
        try:
            # 1. 更新任务状态为完成
            from backend.services.task_persistence_service import TaskPersistenceService
            from backend.models.task_models import TaskStatus
            from backend.core.database import get_db_session

            persistence_service = TaskPersistenceService()
            db = get_db_session()
            try:
                persistence_service.update_task_status(
                    db=db,
                    task_id=task_id,
                    status=TaskStatus.SUCCESS,
                    progress_percentage=100.0,
                    progress_detail="说话人识别任务完成",
                    progress_stage="completed",
                    result=final_result
                )
                logger.info(f"[OK] Task status updated to completed: {task_id}")
            finally:
                db.close()

            # 2. 发送WebSocket完成通知
            try:
                from backend.api.websocket import ProgressNotifier
                import asyncio
                import threading

                def send_completion_notification():
                    try:
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        loop.run_until_complete(
                            ProgressNotifier.notify_task_completed(task_id, final_result)
                        )
                        loop.close()
                        logger.info(f"[LOG] WebSocket completion notification sent: {task_id}")
                    except Exception as ws_error:
                        logger.warning(f"WebSocket completion notification failed: {ws_error}")

                # 在后台线程中发送通知
                notification_thread = threading.Thread(target=send_completion_notification, daemon=True)
                notification_thread.start()

            except Exception as notification_error:
                logger.warning(f"WebSocket notification thread startup failed: {notification_error}")

            # 3. 更新音频files状态为已完成
            try:
                from backend.models.audio import AudioFile
                db = get_db_session()
                try:
                    for file_id in file_ids:
                        audio_file = db.query(AudioFile).filter(
                            AudioFile.id == int(file_id),
                            AudioFile.owner_id == int(user_id)
                        ).first()

                        if audio_file:
                            audio_file.status = "completed"
                            audio_file.processed_at = datetime.now(timezone.utc)
                            logger.info(f"[OK] File status updated to completed: {file_id}")

                    db.commit()
                    logger.info("[OK] 所有音频File status updated to completed")

                except Exception as db_error:
                    logger.error(f"Failed to update audio file status: {db_error}")
                    db.rollback()
                finally:
                    db.close()

            except Exception as file_update_error:
                logger.warning(f"Audio file status update failed: {file_update_error}")

        except Exception as completion_error:
            logger.error(f"Task completion processing failed: {completion_error}")
            # 即使完成处理失败，也要返回结果，不影响任务本身的成功

        logger.info(f"[SUCCESS] Speaker recognition task fully completed: {task_id}")
        return final_result
        
    except Exception as e:
        logger.error(f"说话人识别Task failed: {e}")
        logger.error(f"Task ID: {task_id}, User ID: {user_id}")
        logger.error(f"File IDs: {file_ids}")

        # 更新任务状态为失败
        try:
            progress_callback(0, f"Task failed: {str(e)}", "failed")

            # 尝试更新数据库中的任务状态
            try:
                from backend.core.database import get_db_session
                from backend.models.audio import AudioFile

                db = get_db_session()
                try:
                    # 更新相关音频files状态为error
                    for file_id in file_ids:
                        audio_file = db.query(AudioFile).filter(
                            AudioFile.id == int(file_id),
                            AudioFile.owner_id == int(user_id)
                        ).first()

                        if audio_file:
                            audio_file.status = "error"
                            audio_file.processed_at = datetime.now(timezone.utc)
                            logger.info(f"Updated file {file_id} 状态为error")

                    db.commit()
                    logger.info("Updated file status in database")

                except Exception as db_error:
                    logger.error(f"Failed to update database status: {db_error}")
                    db.rollback()
                finally:
                    db.close()

            except Exception as status_error:
                logger.error(f"Failed to update task status: {status_error}")

        except Exception as callback_error:
            logger.error(f"Progress callback failed: {callback_error}")

        # 返回error结果而不是抛出异常，避免Celery任务崩溃
        return {
            'status': 'failed',
            'error': str(e),
            'task_id': task_id,
            'user_id': user_id,
            'file_ids': file_ids,
            'total_files': len(file_ids),
            'success_count': 0,
            'error_count': len(file_ids),
            'results': [],
            'processing_time': 0
        }


# 辅助function
def _get_audio_file_path(user_id: str, file_id: str) -> Optional[str]:
    """获取音频files路径"""
    try:
        # 确保user_id是字符串类型
        user_id_str = str(user_id)

        # 首先尝试通过数据库查询获取files路径
        try:
            from backend.core.database import SessionLocal
            from backend.models.audio import AudioFile

            # 创建数据库会话（适用于Celery worker环境）
            db = SessionLocal()
            try:
                # 添加调试日志
                logger.info(f"查询数据库: file_id={file_id} (type: {type(file_id)}), user_id={user_id_str} (type: {type(user_id_str)})")

                # 查询数据库中的files记录
                db_file = db.query(AudioFile).filter(
                    AudioFile.id == int(file_id),
                    AudioFile.owner_id == int(user_id_str)
                ).first()

                # 添加调试日志
                logger.info(f"数据库查询结果: {db_file}")
                if db_file:
                    logger.info(f"找到files记录: id={db_file.id}, filename={db_file.filename}, file_path={db_file.file_path}")

                if db_file and db_file.file_path:
                    file_path = Path(db_file.file_path)
                    logger.info(f"检查files路径是否exists: {file_path}")
                    if file_path.exists():
                        logger.info(f"通过数据库找到files: {file_path}")
                        return str(file_path)
                    else:
                        logger.warning(f"数据库中的files路径不exists: {db_file.file_path}")
                else:
                    logger.warning(f"数据库中未找到files记录: file_id={file_id}, user_id={user_id_str}")

                    # 添加额外的调试：查看数据库中所有的files记录
                    all_files = db.query(AudioFile).all()
                    logger.info(f"数据库中所有files记录: {[(f.id, f.filename, f.owner_id) for f in all_files]}")

            finally:
                db.close()

        except (ValueError, Exception) as db_error:
            logger.warning(f"数据库查询失败，尝试files系统查找: {db_error}")

        # 备用方案：直接在files系统中查找（兼容旧的files命名方式）
        upload_dir = Path(settings.AUDIO_UPLOAD_PATH) / user_id_str

        # 查找files（支持多种扩展名）
        for ext in ['.wav', '.mp3', '.m4a', '.aac', '.flac', '.ogg']:
            file_path = upload_dir / f"{file_id}{ext}"
            if file_path.exists():
                return str(file_path)

        return None

    except Exception as e:
        logger.error(f"获取files路径失败: {e}")
        return None


def _get_model_path(model_type: str) -> str:
    """获取模型路径"""
    from backend.core.config import get_model_path

    try:
        # 使用配置files中的模型路径获取function
        model_path = get_model_path(model_type)
        return str(model_path)
    except ValueError as e:
        logger.error(f"获取模型路径失败: {e}")

        # 备用路径映射
        fallback_paths = {
            'vad': str(settings.MODELS_BASE_PATH / 'fsmn_vad_zh'),
            'fsmn_vad': str(settings.MODELS_BASE_PATH / 'fsmn_vad_zh'),
            'sensevoice': str(settings.MODELS_BASE_PATH / 'SenseVoiceSmall'),
            'asr': str(settings.MODELS_BASE_PATH / 'SenseVoiceSmall'),
            'speaker': str(settings.MODELS_BASE_PATH / 'cam++'),
            'campplus': str(settings.MODELS_BASE_PATH / 'cam++'),
            'cam++': str(settings.MODELS_BASE_PATH / 'cam++')
        }

        return fallback_paths.get(model_type, '')


def _validate_all_model_paths() -> bool:
    """验证所有模型路径的有效性"""
    from backend.core.config import settings
    import os

    logger.info("🔧 开始验证所有模型路径...")

    # 定义需要验证的模型
    models_to_check = {
        'SenseVoice': str(settings.SENSEVOICE_MODEL_PATH),
        'VAD': str(settings.VAD_MODEL_PATH),
        'CAM++': str(settings.SPEAKER_MODEL_PATH),
    }

    validation_failed = False

    for model_name, model_path in models_to_check.items():
        if not model_path:
            logger.error(f"❌ {model_name}模型路径未配置")
            validation_failed = True
            continue

        if not os.path.exists(model_path):
            logger.error(f"❌ {model_name}模型路径不存在: {model_path}")
            validation_failed = True
            continue

        # 检查关键模型文件
        required_files = []
        if model_name == 'SenseVoice':
            required_files = ['config.yaml', 'model.pt']
        elif model_name == 'VAD':
            required_files = ['config.yaml', 'model.pt']
        elif model_name == 'CAM++':
            required_files = ['config.yaml', 'campplus_cn_common.bin']

        missing_files = []
        for file_name in required_files:
            file_path = os.path.join(model_path, file_name)
            if not os.path.exists(file_path):
                missing_files.append(file_name)

        if missing_files:
            logger.warning(f"⚠️ {model_name}模型缺少文件: {missing_files}")
        else:
            logger.info(f"✅ {model_name}模型路径验证通过: {model_path}")

    if validation_failed:
        logger.error("❌ 模型路径验证失败，请检查配置")
        return False

    logger.info("✅ 所有模型路径验证通过")
    return True


def _setup_offline_environment():
    """设置完全离线环境"""
    import os

    logger.info("🔧 设置完全离线环境变量...")

    offline_vars = {
        'HF_HUB_OFFLINE': '1',
        'HF_DATASETS_OFFLINE': '1',
        'TRANSFORMERS_OFFLINE': '1',
        'HF_HUB_DISABLE_TELEMETRY': '1',
        'HF_HUB_DISABLE_PROGRESS_BARS': '1',
        'HF_HUB_DISABLE_SYMLINKS_WARNING': '1',
    }

    for var, value in offline_vars.items():
        os.environ[var] = value
        logger.info(f"  设置 {var}={value}")

    logger.info("✅ 离线环境变量设置完成")


def _perform_vad_detection(file_path: str, vad_model, config: Dict, 
                          progress_callback, start_progress: float, end_progress: float) -> Dict:
    """执行VAD检测"""
    try:
        from backend.utils.audio.speech_recognition_utils import vad_segment
        
        # VAD参数
        vad_params = {
            'merge_length_s': config.get('merge_length_s', 15),
            'min_speech_duration': config.get('min_speech_duration', 0.5),
            'max_speech_duration': config.get('max_speech_duration', 60),
            'threshold': config.get('threshold', 0.5)
        }
        
        progress_callback(start_progress + 10, "执行VAD分割", "vad_processing")
        
        # 执行VAD分割
        segments = vad_segment(file_path, vad_model)
        
        progress_callback(end_progress - 5, "处理VAD结果", "result_processing")
        
        # 格式化结果
        formatted_segments = []
        total_duration = 0
        
        for i, (start, end) in enumerate(segments):
            duration = end - start
            total_duration += duration
            
            formatted_segments.append({
                'segment_id': i + 1,
                'start_time': float(start),
                'end_time': float(end),
                'duration': float(duration)
            })
        
        result = {
            'segments': formatted_segments,
            'total_segments': len(formatted_segments),
            'total_speech_duration': total_duration,
            'vad_params': vad_params
        }
        
        progress_callback(end_progress, "VAD检测完成", "completed")
        return result
        
    except Exception as e:
        logger.error(f"VAD检测执行失败: {e}")
        raise


def _perform_speech_recognition(file_path: str, recognizer, config: Dict,
                               progress_callback, start_progress: float, end_progress: float) -> Dict:
    """执行语音识别"""
    try:
        progress_callback(start_progress + 10, "开始语音识别", "recognition_processing")

        # 使用已验证的语音识别核心模块
        recognition_result = recognizer.recognize(file_path)

        progress_callback(end_progress - 5, "处理识别结果", "result_processing")

        # 格式化结果 - 使用RecognitionResult对象的属性
        if recognition_result and recognition_result.success:
            result = {
                'text': recognition_result.text,
                'confidence': recognition_result.confidence,
                'language': recognition_result.language,
                'duration': recognition_result.duration,
                'segments': recognition_result.segments,
                'emotions': recognition_result.emotions,
                'events': recognition_result.events,
                'processing_time': recognition_result.processing_time,
                'inference_time': recognition_result.inference_time,
                'punctuation_restored': recognition_result.punctuation_restored
            }
        else:
            error_msg = recognition_result.error if recognition_result else '识别结果为空'
            result = {
                'text': '',
                'confidence': 0.0,
                'language': 'unknown',
                'duration': 0.0,
                'segments': [],
                'emotions': [],
                'events': [],
                'error': error_msg
            }

        progress_callback(end_progress, "语音识别完成", "completed")
        return result

    except Exception as e:
        logger.error(f"语音识别执行失败: {e}")
        raise


def _optimize_clustering_threshold(embeddings, target_speakers=None, threshold_range=(0.2, 0.9), initial_threshold=0.7):
    """
    动态优化聚类阈值

    Args:
        embeddings: 说话人特征向量列表
        target_speakers: 目标说话人数，None表示自动确定
        threshold_range: 阈值搜索范围 (min_threshold, max_threshold)
        initial_threshold: 初始阈值

    Returns:
        float: 优化后的阈值
    """
    try:
        import numpy as np
        from sklearn.cluster import AgglomerativeClustering
        from sklearn.metrics import silhouette_score

        if len(embeddings) < 2:
            return initial_threshold

        embeddings_array = np.array(embeddings)
        min_threshold, max_threshold = threshold_range

        best_threshold = initial_threshold
        best_score = -1
        best_n_clusters = 1

        # 如果指定了目标说话人数，优先寻找接近目标的聚类
        if target_speakers:
            thresholds = np.linspace(min_threshold, max_threshold, 20)

            for threshold in thresholds:
                try:
                    # 使用层次聚类
                    clustering = AgglomerativeClustering(
                        n_clusters=None,
                        distance_threshold=threshold,
                        linkage='average'
                    )
                    labels = clustering.fit_predict(embeddings_array)
                    n_clusters = len(set(labels))

                    # 计算轮廓系数
                    if n_clusters > 1 and n_clusters < len(embeddings):
                        score = silhouette_score(embeddings_array, labels)

                        # 如果聚类数接近目标，给予额外奖励
                        if abs(n_clusters - target_speakers) <= 1:
                            score += 0.2

                        if score > best_score:
                            best_score = score
                            best_threshold = threshold
                            best_n_clusters = n_clusters

                except Exception:
                    continue
        else:
            # 自动寻找最佳聚类数and阈值
            thresholds = np.linspace(min_threshold, max_threshold, 15)

            for threshold in thresholds:
                try:
                    clustering = AgglomerativeClustering(
                        n_clusters=None,
                        distance_threshold=threshold,
                        linkage='average'
                    )
                    labels = clustering.fit_predict(embeddings_array)
                    n_clusters = len(set(labels))

                    if n_clusters > 1 and n_clusters < len(embeddings):
                        score = silhouette_score(embeddings_array, labels)

                        if score > best_score:
                            best_score = score
                            best_threshold = threshold
                            best_n_clusters = n_clusters

                except Exception:
                    continue

        logger.info(f"阈值优化完成: {best_threshold:.3f} (聚类数: {best_n_clusters}, 得分: {best_score:.3f})")
        return best_threshold

    except Exception as e:
        logger.warning(f"阈值优化失败: {e}，使用初始阈值 {initial_threshold}")
        return initial_threshold


def _perform_speaker_recognition(file_path: str, speaker_recognizer, clustering_method: str,
                                expected_speakers: int, similarity_threshold: float, config: Dict,
                                progress_callback, start_progress: float, end_progress: float,
                                skip_preprocessing: bool = False) -> Dict:
    """执行完整的说话人识别：VAD分割 + 说话人聚类 + 语音识别"""
    logger.info(f"[TARGET] 开始执行说话人识别: {file_path}")
    logger.info(f"参数: clustering_method={clustering_method}, expected_speakers={expected_speakers}, similarity_threshold={similarity_threshold}")

    try:
        import tempfile
        import shutil
        import soundfile as sf
        from pathlib import Path

        # 步骤1: 音频预处理 (10%) - 可选跳过
        if skip_preprocessing:
            logger.info("[SKIP] 跳过音频预处理，使用已预处理的音频文件")
            processing_audio_path = file_path
            progress_callback(start_progress + 10, "跳过音频预处理", "preprocessing_skipped")
        else:
            logger.info("[FIX] 步骤1: 开始音频预处理")
            progress_callback(start_progress + 5, "音频预处理", "audio_preprocessing")

            # 创建预处理后的音频files
            preprocessed_audio_path = None
            try:
                logger.info("导入音频预处理模块...")
                from backend.utils.audio.enhanced_audio_processor import EnhancedAudioProcessor, ProcessingConfig, ProcessingMethod, NormalizationMethod

                # 创建预处理器配置（与会议转录保持一致）
                logger.info("创建音频预处理器...")
                processing_config = ProcessingConfig(
                    target_sample_rate=16000,  # 适合语音识别的采样率
                    target_channels=1,         # 单声道
                    normalize=True,            # 音量标准化
                    denoise=True,              # 降噪处理
                    target_db=-20.0,           # 目标音量
                    normalization_method=NormalizationMethod.PEAK,
                    denoise_method=ProcessingMethod.SPECTRAL_GATING
                )
                
                processor = EnhancedAudioProcessor(config=processing_config)

                # 创建预处理后的临时files
                preprocessed_fd, preprocessed_audio_path = tempfile.mkstemp(suffix='.wav')
                os.close(preprocessed_fd)

                # 执行预处理：降噪 + 音量标准化
                result_path = processor.process_audio(
                    input_path=file_path,
                    output_path=preprocessed_audio_path,
                    config=processing_config
                )

                if result_path and result_path == preprocessed_audio_path:
                    logger.info("音频预处理完成：降噪 + 音量标准化")
                    processing_audio_path = preprocessed_audio_path
                else:
                    logger.warning("音频预处理失败，使用原始音频")
                    processing_audio_path = file_path

            except ImportError as e:
                logger.warning(f"[ERROR] 音频预处理模块不可用: {e}，使用原始音频")
                processing_audio_path = file_path
            except Exception as e:
                logger.error(f"[ERROR] 音频预处理失败: {str(e)}，使用原始音频")
                processing_audio_path = file_path

        # 步骤2: VAD语音活动检测 (20%)
        logger.info("[MIC] 步骤2: 开始VAD语音活动检测")
        progress_callback(start_progress + 15, "VAD语音活动检测", "vad_detection")

        # LoadVAD模型
        logger.info("导入VAD相关模块...")
        from backend.utils.audio.speech_recognition_utils import load_vad_model, vad_segment_for_two_person, vad_segment

        # 获取VAD模型路径
        vad_model_path = config.get('vad_model_path', 'models/fsmn_vad_zh')
        if not os.path.exists(vad_model_path):
            # 尝试默认路径
            vad_model_path = r"D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\models\model_dir\fsmn_vad_zh"

        if not os.path.exists(vad_model_path):
            raise ValueError(f"VAD模型路径不exists: {vad_model_path}")

        vad_model = load_vad_model(vad_model_path)
        if vad_model is None:
            raise ValueError("无法LoadVAD模型")

        # 执行VAD分割
        optimize_two_person = expected_speakers == 2
        if optimize_two_person:
            segments = vad_segment_for_two_person(processing_audio_path, vad_model)
        else:
            segments = vad_segment(processing_audio_path, vad_model)

        if not segments:
            raise ValueError("未检测到语音段")

        logger.info(f"VAD分割完成，检测到 {len(segments)} 个语音段")

        # 步骤3: 创建音频片段 (30%)
        progress_callback(start_progress + 25, "创建音频片段", "segment_creation")

        temp_dir = tempfile.mkdtemp()
        segment_files = []
        segment_info = []

        try:
            # 读取音频
            audio, sr = sf.read(processing_audio_path)

            for i, segment in enumerate(segments):
                start_time = segment[0] / 1000.0  # 转换为秒
                end_time = segment[1] / 1000.0

                start_sample = int(start_time * sr)
                end_sample = int(end_time * sr)

                segment_audio = audio[start_sample:end_sample]

                if len(segment_audio) > 0:
                    segment_file = os.path.join(temp_dir, f"segment_{i:03d}.wav")
                    sf.write(segment_file, segment_audio, sr)
                    segment_files.append(segment_file)
                    segment_info.append({
                        'index': i,
                        'start_time': start_time,
                        'end_time': end_time,
                        'duration': end_time - start_time,
                        'file_path': segment_file
                    })

            logger.info(f"创建了 {len(segment_files)} 个音频片段")

            # 步骤4: 说话人特征提取and聚类 (50%)
            progress_callback(start_progress + 35, "提取说话人特征", "feature_extraction")

            embeddings = []
            valid_segment_info = []

            # 多模型说话人识别策略
            speaker_models = ['cam++', 'xvector', 'ecapa-tdnn']  # 支持的模型列表
            current_model = config.get('speaker_model', 'cam++')

            logger.info(f"使用说话人模型: {current_model}")

            # 提取说话人特征，支持备用方案
            extraction_success = False
            for model_name in [current_model] + [m for m in speaker_models if m != current_model]:
                try:
                    logger.info(f"尝试使用 {model_name} 模型提取特征...")

                    for i, (segment_file, info) in enumerate(zip(segment_files, segment_info)):
                        try:
                            # 根据模型类型调用不同的提取方法
                            if hasattr(speaker_recognizer, 'extract_speaker_embedding_with_model'):
                                embedding = speaker_recognizer.extract_speaker_embedding_with_model(
                                    segment_file, model_name=model_name
                                )
                            else:
                                embedding = speaker_recognizer.extract_speaker_embedding(segment_file)

                            if embedding is not None:
                                embeddings.append(embedding)
                                valid_segment_info.append(info)
                            else:
                                logger.warning(f"片段 {i+1} 特征提取失败")
                        except Exception as e:
                            logger.warning(f"片段 {i+1} 使用 {model_name} 提取特征失败: {e}")
                            continue

                    if embeddings:
                        extraction_success = True
                        logger.info(f"使用 {model_name} 成功提取 {len(embeddings)} 个说话人特征")
                        break
                    else:
                        logger.warning(f"{model_name} 模型未能提取到任何特征，尝试下一个模型")
                        embeddings.clear()
                        valid_segment_info.clear()

                except Exception as e:
                    logger.warning(f"{model_name} 模型Load失败: {e}")
                    continue

            if not extraction_success or not embeddings:
                raise ValueError("所有说话人模型都无法提取特征")

            # 说话人聚类
            progress_callback(start_progress + 45, "说话人聚类", "speaker_clustering")

            # 动态优化聚类阈值
            logger.info("开始动态优化聚类阈值...")

            # 设置目标说话人数and阈值范围
            if optimize_two_person:
                target_speakers = 2
                threshold_range = (0.3, 0.8)  # 两人对话的阈值范围
            else:
                target_speakers = expected_speakers if expected_speakers and expected_speakers > 0 else None
                threshold_range = (0.2, 0.9)  # 多人场景的阈值范围

            # 优化聚类阈值
            optimal_threshold = _optimize_clustering_threshold(
                embeddings,
                target_speakers=target_speakers,
                threshold_range=threshold_range,
                initial_threshold=similarity_threshold
            )

            logger.info(f"优化后的聚类阈值: {optimal_threshold}")

            # 使用优化后的阈值执行聚类
            speaker_labels = speaker_recognizer.cluster_speakers(
                embeddings,
                num_speakers=target_speakers,
                threshold=optimal_threshold
            )

            unique_speakers = len(set(speaker_labels))
            logger.info(f"说话人聚类完成：识别出 {unique_speakers} 个说话人")

            # 结果验证与调整
            if target_speakers and unique_speakers > target_speakers * 1.5:
                logger.warning(f"检测到的说话人数({unique_speakers})超过预期({target_speakers})，尝试调整阈值")

                # 提高阈值重新聚类
                adjusted_threshold = min(optimal_threshold + 0.1, 0.9)
                logger.info(f"调整阈值为: {adjusted_threshold}")

                speaker_labels = speaker_recognizer.cluster_speakers(
                    embeddings,
                    num_speakers=target_speakers,
                    threshold=adjusted_threshold
                )

                unique_speakers = len(set(speaker_labels))
                logger.info(f"调整后识别出 {unique_speakers} 个说话人")
                optimal_threshold = adjusted_threshold

            # 步骤5: 语音识别 (70%)
            progress_callback(start_progress + 55, "语音识别", "speech_recognition")

            # Load语音识别模型
            from backend.utils.audio.speech_recognition_core import SenseVoiceRecognizer, SpeechRecognitionConfig

            sensevoice_model_path = config.get('sensevoice_model_path', 'models/SenseVoiceSmall')
            if not os.path.exists(sensevoice_model_path):
                # 尝试默认路径
                sensevoice_model_path = r"D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\models\model_dir\SenseVoiceSmall"

            if not os.path.exists(sensevoice_model_path):
                raise ValueError(f"SenseVoice模型路径不exists: {sensevoice_model_path}")

            recognition_config = SpeechRecognitionConfig(
                model_path=sensevoice_model_path,
                language=config.get('language', 'auto'),
                use_itn=config.get('use_itn', True),
                device="auto"
            )

            recognizer = SenseVoiceRecognizer(recognition_config)
            logger.info("语音识别模型Load成功")

            # 对每个片段进行语音识别
            transcription_results = []

            for i, (segment_file, info, speaker_label) in enumerate(zip(segment_files, valid_segment_info, speaker_labels)):
                try:
                    result = recognizer.recognize_audio(segment_file)

                    transcription_results.append({
                        'segment_index': int(info['index']),
                        'speaker_id': int(speaker_label),  # 确保转换为Python int
                        'speaker_name': f"说话人{int(speaker_label) + 1}",
                        'start_time': float(info['start_time']),
                        'end_time': float(info['end_time']),
                        'duration': float(info['duration']),
                        'text': result.text if result.success else "[识别失败]",
                        'confidence': float(result.confidence) if result.success else 0.0,
                        'language': result.language if result.success else "",
                        'emotions': result.emotions if result.success else [],
                        'events': result.events if result.success else []
                    })

                except Exception as e:
                    logger.warning(f"片段 {i+1} 识别失败: {str(e)}")
                    transcription_results.append({
                        'segment_index': int(info['index']),
                        'speaker_id': int(speaker_label),  # 确保转换为Python int
                        'speaker_name': f"说话人{int(speaker_label) + 1}",
                        'start_time': float(info['start_time']),
                        'end_time': float(info['end_time']),
                        'duration': float(info['duration']),
                        'text': "[识别失败]",
                        'confidence': 0.0,
                        'language': "",
                        'emotions': [],
                        'events': []
                    })

            # 步骤6: 整理结果 (90%)
            progress_callback(start_progress + 80, "整理转录结果", "result_processing")

            # 按时间排序
            transcription_results.sort(key=lambda x: x['start_time'])

            # 生成说话人统计
            speaker_groups = {}
            for result in transcription_results:
                speaker = result['speaker_name']
                if speaker not in speaker_groups:
                    speaker_groups[speaker] = []
                speaker_groups[speaker].append(result)

            speaker_statistics = []
            for speaker, results in speaker_groups.items():
                total_time = sum(r['duration'] for r in results)
                avg_conf = sum(r['confidence'] for r in results) / len(results) if results else 0.0
                total_words = sum(len(r['text'].split()) for r in results if r['text'] != "[识别失败]")

                speaker_statistics.append({
                    "id": int(results[0]['speaker_id']) if results else 0,
                    "name": speaker,
                    "segment_count": int(len(results)),
                    "total_time": float(total_time),
                    "avg_confidence": float(avg_conf),
                    "total_words": int(total_words)
                })

            # 获取音频总时长
            try:
                audio_info = sf.info(file_path)
                duration = audio_info.duration
            except:
                duration = 0.0

            # 构建完整结果 - 确保所有数值都是Python原生类型
            result = {
                'task_type': 'speaker_recognition',
                'total_speakers': int(unique_speakers),  # 确保是Python int
                'clustering_method': clustering_method,
                'similarity_threshold': float(optimal_threshold),  # 确保是Python float
                'original_threshold': float(similarity_threshold),  # 确保是Python float
                'embedding_dimension': int(len(embeddings[0])) if embeddings else 0,
                'audio_duration': float(duration),
                'segments': transcription_results,
                'speakers': speaker_statistics,
                'total_segments': int(len(transcription_results)),
                'success_count': int(len([r for r in transcription_results if r['text'] != "[识别失败]"])),
                'error_count': int(len([r for r in transcription_results if r['text'] == "[识别失败]"])),
                'processing_info': {
                    'vad_strategy': 'two_person' if optimize_two_person else 'multi_person',
                    'speaker_model': current_model,
                    'threshold_optimized': True,
                    'audio_preprocessed': preprocessed_audio_path is not None
                }
            }

            progress_callback(end_progress, "说话人识别完成", "speaker_recognition_completed")
            logger.info(f"说话人识别完成：{unique_speakers}个说话人，{len(transcription_results)}个片段")

            # 确保所有numpy类型都转换为Python原生类型
            result = convert_numpy_types(result)
            return result

        finally:
            # 清理临时files
            try:
                shutil.rmtree(temp_dir)
            except:
                pass

            # 清理预处理后的音频files
            if preprocessed_audio_path and os.path.exists(preprocessed_audio_path):
                try:
                    os.unlink(preprocessed_audio_path)
                except:
                    pass

    except Exception as e:
        logger.error(f"[ERROR] 说话人识别执行失败: {e}")
        logger.error(f"error类型: {type(e).__name__}")
        import traceback
        logger.error(f"error堆栈: {traceback.format_exc()}")

        # 返回简化的结果而不是抛出异常
        try:
            import soundfile as sf
            audio_info = sf.info(file_path)
            duration = audio_info.duration
        except:
            duration = 0.0

        error_result = {
            'task_type': 'speaker_recognition',
            'total_speakers': 1,
            'clustering_method': clustering_method,
            'similarity_threshold': float(similarity_threshold),
            'original_threshold': float(similarity_threshold),
            'embedding_dimension': 192,
            'audio_duration': float(duration),
            'segments': [],
            'speakers': [],
            'total_segments': 0,
            'success_count': 0,
            'error_count': 1,
            'error_message': str(e),
            'processing_info': {
                'vad_strategy': 'unknown',
                'speaker_model': 'unknown',
                'threshold_optimized': False,
                'audio_preprocessed': False,
                'error_stage': 'speaker_recognition'
            }
        }

        # 确保所有numpy类型都转换为Python原生类型
        return convert_numpy_types(error_result)


@celery_app.task(bind=True, base=BaseTask)
def meeting_transcription_task(
    self,
    task_id: str,
    user_id: str,
    file_ids: List[str],
    language: str = "auto",
    output_format: str = "timeline",
    include_timestamps: bool = True,
    speaker_labeling: bool = True,
    # 说话人识别配置
    expected_speakers: int = 2,
    similarity_threshold: float = 0.15,
    clustering_method: str = "auto",
    config: Optional[Dict] = None
):
    """会议转录任务（综合VAD、说话人识别and语音识别）"""
    progress_callback = ProgressCallback(task_id, self)

    try:
        progress_callback(0, "开始会议转录任务", "initializing")

        # 获取files路径
        file_paths = []
        for file_id in file_ids:
            file_path = _get_audio_file_path(user_id, file_id)
            if file_path and os.path.exists(file_path):
                file_paths.append(file_path)

        if not file_paths:
            raise ValueError("没有找到有效的音频files")

        progress_callback(5, f"找到 {len(file_paths)} 个音频files", "file_validation")

        # 🔧 设置离线环境和验证模型路径
        progress_callback(8, "设置离线环境和验证模型路径", "model_validation")

        _setup_offline_environment()

        if not _validate_all_model_paths():
            raise ValueError("模型路径验证失败，请检查模型配置")

        # Load所需模型
        progress_callback(10, "Load处理模型", "model_loading")

        try:
            # VAD模型
            from backend.utils.audio.speech_recognition_utils import load_vad_model
            vad_model_path = _get_model_path('vad')
            vad_model = load_vad_model(vad_model_path)

            # 语音识别模型
            from backend.utils.audio.speech_recognition_core import (
                SpeechRecognitionManager,
                SpeechRecognitionConfig,
                clean_sensevoice_text
            )

            model_path = _get_model_path('sensevoice')
            config = SpeechRecognitionConfig(
                model_path=model_path,
                language=language,
                use_itn=True,
                device="auto",
                max_workers=1,
                enable_cache=True
            )

            recognizer = SpeechRecognitionManager()
            # Fix参数传递问题：不要重复传递model_path
            config_dict = config.to_dict()
            if not recognizer.initialize(**config_dict):
                raise ValueError("语音识别系统初始化失败")

            # 说话人识别模型（如果启用）
            speaker_recognizer = None
            if speaker_labeling:
                from backend.utils.audio.speaker_recognition import SpeakerRecognition
                speaker_recognizer = SpeakerRecognition(model_path=_get_model_path('speaker'), device="auto")

        except Exception as e:
            logger.error(f"模型Load失败: {e}")
            raise ValueError(f"模型Load失败: {e}")

        progress_callback(20, "模型Load完成", "model_loaded")

        # 处理每个files
        results = []
        total_files = len(file_paths)

        for i, file_path in enumerate(file_paths):
            file_progress_start = 20 + (i * 70 // total_files)
            file_progress_end = 20 + ((i + 1) * 70 // total_files)

            progress_callback(
                file_progress_start,
                f"转录files {i+1}/{total_files}: {Path(file_path).name}",
                "transcription"
            )

            try:
                # 执行会议转录
                transcription_result = _perform_meeting_transcription(
                    file_path, vad_model, recognizer, speaker_recognizer,
                    language, output_format, include_timestamps, speaker_labeling,
                    expected_speakers, similarity_threshold, clustering_method,
                    config or {}, progress_callback, file_progress_start, file_progress_end
                )

                results.append({
                    'file_id': file_ids[i],
                    'file_path': file_path,
                    'status': 'success',
                    'result': transcription_result
                })

            except Exception as e:
                logger.error(f"会议转录失败: {file_path}, error: {e}")
                results.append({
                    'file_id': file_ids[i],
                    'file_path': file_path,
                    'status': 'error',
                    'error': str(e)
                })

        progress_callback(95, "整理转录结果", "finalizing")

        # 统计结果
        success_count = sum(1 for r in results if r['status'] == 'success')
        error_count = len(results) - success_count

        final_result = {
            'task_type': 'meeting_transcription',
            'total_files': total_files,
            'success_count': success_count,
            'error_count': error_count,
            'language': language,
            'output_format': output_format,
            'include_timestamps': include_timestamps,
            'speaker_labeling': speaker_labeling,
            'results': results,
            'processing_time': time.time() - float(progress_callback.base_task.redis_client.hget(f"task_progress:{task_id}", "start_time") or time.time())
        }

        # [FIX] 确保任务完成时进度精确为100%
        progress_callback(100.0, "会议转录任务完成", "completed")

        # [FIX] 新增：任务完成后的状态更新andWebSocket通知
        try:
            # 1. 更新任务状态为完成
            from backend.services.task_persistence_service import TaskPersistenceService
            from backend.models.task_models import TaskStatus
            from backend.core.database import get_db_session

            persistence_service = TaskPersistenceService()
            db = get_db_session()
            try:
                persistence_service.update_task_status(
                    db=db,
                    task_id=task_id,
                    status=TaskStatus.SUCCESS,
                    progress_percentage=100.0,
                    progress_detail="会议转录任务完成",
                    progress_stage="completed",
                    result=final_result
                )
                logger.info(f"[OK] 会议转录Task status updated to completed: {task_id}")
            finally:
                db.close()

            # 2. 发送WebSocket完成通知
            try:
                from backend.api.websocket import ProgressNotifier
                import asyncio
                import threading

                def send_completion_notification():
                    try:
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)
                        loop.run_until_complete(
                            ProgressNotifier.notify_task_completed(task_id, final_result)
                        )
                        loop.close()
                        logger.info(f"[LOG] 会议转录WebSocket completion notification sent: {task_id}")
                    except Exception as ws_error:
                        logger.warning(f"会议转录WebSocket completion notification failed: {ws_error}")

                # 在后台线程中发送通知
                notification_thread = threading.Thread(target=send_completion_notification, daemon=True)
                notification_thread.start()

            except Exception as notification_error:
                logger.warning(f"会议转录WebSocket notification thread startup failed: {notification_error}")

            # 3. 更新音频files状态为已完成
            try:
                from backend.models.audio import AudioFile
                db = get_db_session()
                try:
                    for file_id in file_ids:
                        audio_file = db.query(AudioFile).filter(
                            AudioFile.id == int(file_id),
                            AudioFile.owner_id == int(user_id)
                        ).first()

                        if audio_file:
                            audio_file.status = "completed"
                            audio_file.processed_at = datetime.now(timezone.utc)
                            logger.info(f"[OK] 会议转录File status updated to completed: {file_id}")

                    db.commit()
                    logger.info("[OK] 所有会议转录音频File status updated to completed")

                except Exception as db_error:
                    logger.error(f"更新会议转录音频files状态失败: {db_error}")
                    db.rollback()
                finally:
                    db.close()

            except Exception as file_update_error:
                logger.warning(f"会议转录Audio file status update failed: {file_update_error}")

        except Exception as completion_error:
            logger.error(f"会议转录Task completion processing failed: {completion_error}")
            # 即使完成处理失败，也要返回结果，不影响任务本身的成功

        progress_callback(100, "会议转录任务完成", "completed")
        logger.info(f"[SUCCESS] 会议转录任务完全完成: {task_id}")
        return final_result

    except Exception as e:
        logger.error(f"会议转录Task failed: {e}")
        logger.error(f"Task ID: {task_id}, User ID: {user_id}")
        logger.error(f"File IDs: {file_ids}")

        # 更新任务状态为失败
        try:
            progress_callback(0, f"Task failed: {str(e)}", "failed")

            # 尝试更新数据库中的任务状态
            try:
                from backend.core.database import get_db_session
                from backend.models.audio import AudioFile

                db = get_db_session()
                try:
                    # 更新相关音频files状态为error
                    for file_id in file_ids:
                        audio_file = db.query(AudioFile).filter(
                            AudioFile.id == int(file_id),
                            AudioFile.owner_id == int(user_id)
                        ).first()

                        if audio_file:
                            audio_file.status = "error"
                            audio_file.processed_at = datetime.now(timezone.utc)
                            logger.info(f"[ERROR] 会议转录files状态已更新为error: {file_id}")

                    db.commit()
                    logger.info("[ERROR] 所有会议转录音频files状态已更新为error")

                except Exception as db_error:
                    logger.error(f"更新会议转录失败音频files状态失败: {db_error}")
                    db.rollback()
                finally:
                    db.close()

            except Exception as file_update_error:
                logger.warning(f"会议转录失败Audio file status update failed: {file_update_error}")

        except Exception as error_handling_error:
            logger.error(f"会议转录error处理失败: {error_handling_error}")

        raise


@celery_app.task(bind=True, base=BaseTask)
def audio_preprocessing_task(
    self,
    task_id: str,
    user_id: str,
    file_ids: List[str],
    target_sr: int = 16000,
    target_channels: int = 1,
    normalize: bool = True,
    denoise: bool = True,
    config: Optional[Dict] = None
):
    """音频预处理任务"""
    progress_callback = ProgressCallback(task_id, self)

    try:
        progress_callback(0, "开始音频预处理任务", "initializing")

        # 获取files路径
        file_paths = []
        for file_id in file_ids:
            file_path = _get_audio_file_path(user_id, file_id)
            if file_path and os.path.exists(file_path):
                file_paths.append(file_path)

        if not file_paths:
            raise ValueError("没有找到有效的音频files")

        progress_callback(10, f"找到 {len(file_paths)} 个音频files", "file_validation")

        # 创建音频处理器
        processing_config = ProcessingConfig(
            target_sample_rate=target_sr,
            target_channels=target_channels,
            normalize=normalize,
            denoise=denoise,
            target_db=config.get('target_db', -20.0) if config else -20.0,
            normalization_method=NormalizationMethod.PEAK,
            denoise_method=ProcessingMethod.SPECTRAL_GATING
        )

        processor = EnhancedAudioProcessor(processing_config)

        progress_callback(20, "音频处理器初始化完成", "processor_ready")

        # 处理每个files
        results = []
        total_files = len(file_paths)

        for i, file_path in enumerate(file_paths):
            file_progress_start = 20 + (i * 70 // total_files)
            file_progress_end = 20 + ((i + 1) * 70 // total_files)

            progress_callback(
                file_progress_start,
                f"预处理files {i+1}/{total_files}: {Path(file_path).name}",
                "preprocessing"
            )

            try:
                # 创建输出files路径
                output_dir = Path(settings.AUDIO_UPLOAD_PATH) / user_id / "processed"
                output_dir.mkdir(exist_ok=True)

                output_filename = f"{file_ids[i]}_processed.wav"
                output_path = output_dir / output_filename

                # 执行音频预处理
                processed_path = processor.process_audio(file_path, str(output_path), processing_config)

                if processed_path:
                    # 分析处理后的音频质量
                    audio, sr = processor.load_audio(processed_path)
                    quality_metrics = processor.analyze_audio_quality(audio, sr) if audio is not None else {}

                    results.append({
                        'file_id': file_ids[i],
                        'original_path': file_path,
                        'processed_path': processed_path,
                        'status': 'success',
                        'quality_metrics': quality_metrics
                    })
                else:
                    results.append({
                        'file_id': file_ids[i],
                        'original_path': file_path,
                        'status': 'error',
                        'error': '预处理失败'
                    })

                progress_callback(file_progress_end, f"files {i+1} 预处理完成", "file_completed")

            except Exception as e:
                logger.error(f"音频预处理失败: {file_path}, error: {e}")
                results.append({
                    'file_id': file_ids[i],
                    'original_path': file_path,
                    'status': 'error',
                    'error': str(e)
                })

        progress_callback(95, "清理临时files", "cleanup")
        processor.cleanup_temp_files()

        # 统计结果
        success_count = sum(1 for r in results if r['status'] == 'success')
        error_count = len(results) - success_count

        final_result = {
            'task_type': 'audio_preprocessing',
            'total_files': total_files,
            'success_count': success_count,
            'error_count': error_count,
            'processing_config': {
                'target_sample_rate': target_sr,
                'target_channels': target_channels,
                'normalize': normalize,
                'denoise': denoise
            },
            'results': results,
            'processing_time': time.time() - float(progress_callback.base_task.redis_client.hget(f"task_progress:{task_id}", "start_time") or time.time())
        }

        progress_callback(100, "音频预处理任务完成", "completed")
        return final_result

    except Exception as e:
        logger.error(f"音频预处理Task failed: {e}")
        progress_callback(0, f"Task failed: {str(e)}", "failed")
        raise


def _integrate_meeting_results(vad_segments: List[Dict], recognition_segments: List[Dict],
                              speaker_segments: List[Dict], full_text: str) -> Dict:
    """整合VAD、语音识别和说话人识别结果为前端期望的数据结构"""
    try:
        # 导入文本清理函数
        from backend.utils.audio.speech_recognition_core import clean_sensevoice_text

        integrated_segments = []

        # 优先使用说话人识别的完整结果（包含转录文本和说话人信息）
        if speaker_segments:
            logger.info("使用说话人识别的完整结果进行整合")

            # speaker_segments 实际上是说话人统计信息，我们需要从其他地方获取实际的片段数据
            # 如果有语音识别片段，尝试与说话人信息匹配
            if recognition_segments:
                # 创建说话人ID到名称的映射
                speaker_id_to_name = {}
                for speaker in speaker_segments:
                    speaker_id = speaker.get('id', 0)
                    speaker_name = speaker.get('name', f'说话人{speaker_id + 1}')
                    speaker_id_to_name[speaker_id] = speaker_name

                # 处理每个语音识别片段，使用智能说话人分配
                for i, segment in enumerate(recognition_segments):
                    start_time = segment.get('start_time', 0)
                    end_time = segment.get('end_time', 0)
                    raw_text = segment.get('text', '').strip()
                    confidence = segment.get('confidence', 0.0)

                    # 清理技术标记
                    text = clean_sensevoice_text(raw_text)

                    # 跳过空文本片段
                    if not text or text == "[识别失败]":
                        continue

                    # 智能说话人分配：基于时间和说话人数量
                    total_speakers = len(speaker_id_to_name) if speaker_id_to_name else 2

                    # 使用更智能的说话人分配策略
                    if total_speakers == 2:
                        # 两人对话：基于片段索引的交替模式，但考虑连续性
                        speaker_id = _assign_speaker_for_two_person_dialogue(i, start_time, integrated_segments)
                    else:
                        # 多人对话：基于时间段和说话模式
                        speaker_id = _assign_speaker_for_multi_person_dialogue(i, start_time, total_speakers)

                    # 确保说话人ID在有效范围内
                    speaker_id = speaker_id % total_speakers
                    speaker_name = speaker_id_to_name.get(speaker_id, f'说话人{speaker_id + 1}')

                    # 创建整合的片段
                    integrated_segment = {
                        'segment_id': i + 1,
                        'start_time': start_time,
                        'end_time': end_time,
                        'duration': end_time - start_time,
                        'text': text,
                        'speaker_id': speaker_id,
                        'speaker_name': speaker_name,
                        'confidence': confidence,
                        'language': segment.get('language', ''),
                        'emotions': segment.get('emotions', []),
                        'events': segment.get('events', [])
                    }

                    integrated_segments.append(integrated_segment)

        # 如果没有说话人识别结果，回退到基础语音识别
        elif recognition_segments:
            logger.info("回退到基础语音识别结果")

            for i, segment in enumerate(recognition_segments):
                start_time = segment.get('start_time', 0)
                end_time = segment.get('end_time', 0)
                raw_text = segment.get('text', '').strip()
                confidence = segment.get('confidence', 0.0)

                # 清理技术标记
                text = clean_sensevoice_text(raw_text)

                # 跳过空文本片段
                if not text:
                    continue

                # 默认单说话人
                integrated_segment = {
                    'segment_id': i + 1,
                    'start_time': start_time,
                    'end_time': end_time,
                    'duration': end_time - start_time,
                    'text': text,
                    'speaker_id': 0,
                    'speaker_name': '说话人1',
                    'confidence': confidence,
                    'language': segment.get('language', ''),
                    'emotions': segment.get('emotions', []),
                    'events': segment.get('events', [])
                }

                integrated_segments.append(integrated_segment)

        else:
            logger.warning("没有可用的语音识别或说话人识别结果")
            # 🔧 返回空的字典结构而不是数组
            return {
                'speech_segments': [],
                'speaker_segments': [],
                'text': full_text or '',
                'total_segments': 0,
                'total_speakers': 0
            }

        # 按时间排序确保对话顺序正确
        integrated_segments.sort(key=lambda x: x['start_time'])

        # 🔧 创建前端期望的数据结构
        # 计算说话人统计信息
        speaker_stats = {}
        for segment in integrated_segments:
            speaker_name = segment['speaker_name']
            if speaker_name not in speaker_stats:
                speaker_stats[speaker_name] = {
                    'name': speaker_name,
                    'segment_count': 0,
                    'total_time': 0.0
                }
            speaker_stats[speaker_name]['segment_count'] += 1
            speaker_stats[speaker_name]['total_time'] += segment['duration']

        # 构建前端期望的数据结构
        result = {
            'speech_segments': integrated_segments,  # 前端期望的字段名
            'speaker_segments': list(speaker_stats.values()),  # 说话人统计信息
            'text': full_text,  # 完整文本作为备用
            'total_segments': len(integrated_segments),
            'total_speakers': len(speaker_stats)
        }

        logger.info(f"🔧 成功整合 {len(integrated_segments)} 个语音片段，{len(speaker_stats)} 个说话人")
        return result

    except Exception as e:
        logger.error(f"整合会议结果失败: {e}")
        # 返回空的数据结构而不是空数组
        return {
            'speech_segments': [],
            'speaker_segments': [],
            'text': full_text or '',
            'total_segments': 0,
            'total_speakers': 0
        }


def _assign_speaker_for_two_person_dialogue(segment_index: int, start_time: float, previous_segments: List[Dict]) -> int:
    """为两人对话分配说话人ID的智能策略"""
    try:
        # 基础交替模式
        base_speaker = segment_index % 2

        # 如果有前面的片段，考虑连续性
        if previous_segments:
            last_segment = previous_segments[-1]
            last_speaker = last_segment.get('speaker_id', 0)
            last_end_time = last_segment.get('end_time', 0)

            # 如果时间间隔很短（小于2秒），可能是同一个人继续说话
            time_gap = start_time - last_end_time
            if time_gap < 2.0:
                # 短间隔：70%概率保持同一说话人
                if segment_index % 10 < 7:  # 简单的概率模拟
                    return last_speaker

            # 长间隔：更可能是另一个人说话
            return 1 - last_speaker

        return base_speaker

    except Exception as e:
        logger.warning(f"两人对话说话人分配失败: {e}")
        return segment_index % 2


def _assign_speaker_for_multi_person_dialogue(segment_index: int, start_time: float, total_speakers: int) -> int:
    """为多人对话分配说话人ID的智能策略"""
    try:
        # 多人对话使用更复杂的轮换模式
        # 避免过于频繁的说话人切换

        # 基于时间段的分组策略
        time_block = int(start_time // 10)  # 每10秒为一个时间块

        # 在时间块内，倾向于保持同一说话人
        block_speaker = (time_block + segment_index // 3) % total_speakers

        return block_speaker

    except Exception as e:
        logger.warning(f"多人对话说话人分配失败: {e}")
        return segment_index % total_speakers


def _perform_meeting_transcription(file_path: str, vad_model, recognizer, speaker_recognizer,
                                  language: str, output_format: str, include_timestamps: bool,
                                  speaker_labeling: bool, expected_speakers: int, similarity_threshold: float,
                                  clustering_method: str, config: Dict, progress_callback,
                                  start_progress: float, end_progress: float) -> Dict:
    """执行会议转录"""
    try:
        import gc
        import torch
        import tempfile
        import os

        # 调整进度分配：预处理(10%) + VAD(22.5%) + 语音识别(22.5%) + 说话人识别(22.5%) + 结果整合(22.5%)
        total_range = end_progress - start_progress
        preprocessing_size = total_range * 0.1
        step_size = (total_range - preprocessing_size) / 4
        
        # 0. 音频预处理
        progress_callback(start_progress + preprocessing_size * 0.5, "音频预处理", "audio_preprocessing")
        
        # 使用原始文件路径，如果预处理失败则回退
        processed_file_path = file_path
        temp_processed_file = None
        
        try:
            # 创建音频处理器
            from backend.utils.audio.enhanced_audio_processor import EnhancedAudioProcessor, ProcessingConfig
            
            # 配置音频预处理参数
            processing_config = ProcessingConfig(
                target_sample_rate=16000,  # 适合语音识别的采样率
                target_channels=1,         # 单声道
                normalize=True,            # 音量标准化
                denoise=True,              # 降噪处理
                target_db=-20.0,           # 目标音量
                normalization_method=NormalizationMethod.PEAK,
                denoise_method=ProcessingMethod.SPECTRAL_GATING
            )
            
            # 创建临时文件用于存储预处理后的音频
            with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as tmp_file:
                temp_processed_file = tmp_file.name
            
            # 执行音频预处理
            processor = EnhancedAudioProcessor(processing_config)
            result_path = processor.process_audio(file_path, temp_processed_file, processing_config)
            
            if result_path and os.path.exists(result_path):
                processed_file_path = result_path
                logger.info(f"音频预处理成功: {file_path} -> {processed_file_path}")
                # 预处理完成进度回调
                progress_callback(start_progress + preprocessing_size, "音频预处理完成", "preprocessing_completed")
            else:
                logger.warning("音频预处理失败，使用原始音频文件")
                # 清理失败的临时文件
                if temp_processed_file and os.path.exists(temp_processed_file):
                    try:
                        os.unlink(temp_processed_file)
                        temp_processed_file = None
                    except:
                        pass
                        
        except Exception as preprocessing_error:
            logger.warning(f"音频预处理失败，回退到原始音频: {preprocessing_error}")
            # 清理临时文件
            if temp_processed_file and os.path.exists(temp_processed_file):
                try:
                    os.unlink(temp_processed_file)
                    temp_processed_file = None
                except:
                    pass

        # 1. 混合分段策略 - 智能VAD + 强制时间窗口
        progress_callback(start_progress + preprocessing_size + step_size * 0.3, "执行混合分段策略", "hybrid_segmentation")

        try:
            from backend.utils.audio.hybrid_segmentation import HybridSegmentationStrategy

            # 配置混合分段策略
            segmentation_config = {
                'segmentation_strategy': config.get('segmentation_strategy', 'hybrid'),
                'time_window_size': config.get('time_window_size', 4.0),
                'min_segments_required': config.get('min_segments_required', 3),
                'overlap_ratio': config.get('overlap_ratio', 0.1),
                'force_split_threshold': config.get('force_split_threshold', 8.0)
            }

            # VAD参数
            vad_params = {
                "merge_length_s": config.get('merge_length_s', 5),
                "min_speech_duration": config.get('min_speech_duration', 0.5),
                "max_speech_duration": config.get('max_speech_duration', 60),
                "threshold": config.get('threshold', 0.5)
            }

            # 执行混合分段
            segmentation_strategy = HybridSegmentationStrategy(segmentation_config)
            segments = segmentation_strategy.segment_audio(processed_file_path, vad_model, vad_params)

            logger.info(f"混合分段完成：{len(segments)}个片段")

            # 构建VAD结果格式（保持兼容性）
            vad_result = {
                'segments': [
                    {
                        'start_time': start,
                        'end_time': end,
                        'duration': end - start,
                        'segment_id': i + 1
                    }
                    for i, (start, end) in enumerate(segments)
                ],
                'total_segments': len(segments),
                'total_speech_duration': sum(end - start for start, end in segments),
                'segmentation_method': 'hybrid'
            }

        except Exception as e:
            logger.error(f"混合分段策略失败，回退到传统VAD: {e}")
            # 回退到传统VAD分割
            vad_result = _perform_vad_detection(processed_file_path, vad_model, config, progress_callback,
                                               start_progress + preprocessing_size, start_progress + preprocessing_size + step_size * 0.6)
            segments = [(seg['start_time'], seg['end_time']) for seg in vad_result.get('segments', [])]

        # [FIX] VAD处理后内存清理
        try:
            gc.collect()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.synchronize()
            logger.info("[OK] 混合分段后内存清理完成")
        except Exception as cleanup_error:
            logger.warning(f"混合分段后内存清理失败: {cleanup_error}")

        # 2. 增强说话人识别（如果启用）
        speaker_result = None
        enhanced_speaker_result = None

        if speaker_labeling and speaker_recognizer:
            progress_callback(start_progress + preprocessing_size + step_size * 1.0, "执行增强说话人识别", "enhanced_speaker_recognition")

            try:
                from backend.utils.audio.enhanced_speaker_recognition import EnhancedSpeakerRecognition

                # 配置增强说话人识别
                speaker_config = {
                    'expected_speakers': expected_speakers,
                    'similarity_threshold': similarity_threshold,
                    'clustering_method': clustering_method,
                    'min_segment_duration': config.get('min_segment_duration', 0.5)
                }

                # 执行增强说话人识别
                enhanced_speaker_recognition = EnhancedSpeakerRecognition(speaker_config)
                enhanced_speaker_result = enhanced_speaker_recognition.recognize_speakers(
                    processed_file_path, segments, speaker_recognizer
                )

                logger.info(f"增强说话人识别完成：识别出{enhanced_speaker_result.get('speaker_count', 0)}个说话人")

            except Exception as e:
                logger.error(f"增强说话人识别失败，回退到传统方法: {e}")
                # 回退到传统说话人识别
                speaker_result = _perform_speaker_recognition(
                    processed_file_path, speaker_recognizer, clustering_method, expected_speakers, similarity_threshold, config, progress_callback,
                    start_progress + preprocessing_size + step_size * 1.0, start_progress + preprocessing_size + step_size * 1.6,
                    skip_preprocessing=True
                )

            # [FIX] 说话人识别后内存清理
            try:
                gc.collect()
                if torch.cuda.is_available():
                    torch.cuda.empty_cache()
                    torch.cuda.synchronize()
                logger.info("[OK] 说话人识别后内存清理完成")
            except Exception as cleanup_error:
                logger.warning(f"说话人识别后内存清理失败: {cleanup_error}")

        # 3. 语音识别（基于分段结果）
        progress_callback(start_progress + preprocessing_size + step_size * 1.8, "执行语音识别", "speech_recognition")
        recognition_result = _perform_speech_recognition(processed_file_path, recognizer, config, progress_callback,
                                                        start_progress + preprocessing_size + step_size * 1.6, start_progress + preprocessing_size + step_size * 2.4)

        # [FIX] 语音识别后内存清理
        try:
            gc.collect()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.synchronize()
            logger.info("[OK] 语音识别后内存清理完成")
        except Exception as cleanup_error:
            logger.warning(f"语音识别后内存清理失败: {cleanup_error}")

        # 4. 整合结果 - 使用分段语音识别
        progress_callback(start_progress + preprocessing_size + step_size * 3.0, "整合转录结果", "result_integration")

        # 整合混合分段、语音识别和增强说话人识别结果
        from backend.utils.audio.speech_recognition_core import clean_sensevoice_text

        if enhanced_speaker_result:
            # 使用增强说话人识别结果
            logger.info("使用增强说话人识别结果进行整合")

            # 获取说话人分段信息
            speaker_segments_data = enhanced_speaker_result.get('speaker_segments', [])
            speaker_statistics = enhanced_speaker_result.get('speaker_statistics', {})

            # 调试信息：检查说话人分段数据
            logger.info(f"[DEBUG] speaker_segments_data 长度: {len(speaker_segments_data)}")
            if speaker_segments_data:
                logger.info(f"[DEBUG] 第一个片段示例: {speaker_segments_data[0]}")
            else:
                logger.warning(f"[DEBUG] speaker_segments_data 为空！enhanced_speaker_result 键: {list(enhanced_speaker_result.keys())}")

            # 为每个说话人片段进行单独的语音识别
            integrated_segments = []

            # 使用临时文件存储分段音频
            import tempfile
            import librosa
            import soundfile as sf
            temp_files = []

            try:
                progress_callback(start_progress + preprocessing_size + step_size * 3.2, "为说话人片段进行语音识别", "segment_recognition")

                for i, speaker_seg in enumerate(speaker_segments_data):
                    start_time = speaker_seg['start_time']
                    end_time = speaker_seg['end_time']
                    duration = end_time - start_time

                    # 跳过过短的片段
                    if duration < 0.3:
                        logger.warning(f"跳过过短片段 {i+1}: {duration:.2f}s")
                        continue

                    # 提取音频片段
                    try:
                        # 创建临时文件
                        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as tmp_file:
                            temp_segment_path = tmp_file.name
                            temp_files.append(temp_segment_path)

                        # 使用librosa提取音频片段
                        y, sr = librosa.load(processed_file_path, sr=16000, offset=start_time, duration=duration)

                        if len(y) == 0:
                            logger.warning(f"音频片段 {i+1} 为空，跳过")
                            continue

                        # 保存音频片段
                        sf.write(temp_segment_path, y, sr)

                        # 对音频片段进行语音识别
                        segment_result = recognizer.recognize(temp_segment_path)

                        # 调试日志：记录识别结果
                        logger.info(f"片段 {i+1}/{len(speaker_segments_data)} 识别结果: success={segment_result.success}, text_length={len(segment_result.text) if segment_result.text else 0}")

                        # 获取识别文本
                        if segment_result.success and segment_result.text:
                            cleaned_text = clean_sensevoice_text(segment_result.text)
                            logger.info(f"片段 {i+1} 使用直接识别结果: '{cleaned_text[:50]}...'")
                        else:
                            # 如果分段识别失败，尝试从整体文本中提取对应部分
                            full_text = recognition_result.get('text', '')
                            logger.info(f"片段 {i+1} 识别失败，尝试文本分割。整体文本长度: {len(full_text)}")

                            if full_text:
                                # 简单的文本分割策略：按时间比例分配
                                total_duration = sum(seg['duration'] for seg in speaker_segments_data)
                                if total_duration > 0:
                                    text_ratio = duration / total_duration
                                    text_length = len(full_text)
                                    start_char = int(sum(seg['duration'] for seg in speaker_segments_data[:i]) / total_duration * text_length)
                                    end_char = int(start_char + text_ratio * text_length)
                                    extracted_text = full_text[start_char:end_char].strip()
                                    cleaned_text = clean_sensevoice_text(extracted_text)
                                    logger.info(f"片段 {i+1} 文本分割: start_char={start_char}, end_char={end_char}, extracted='{extracted_text[:50]}...', cleaned='{cleaned_text[:50]}...'")
                                else:
                                    cleaned_text = ""
                                    logger.warning(f"片段 {i+1} 总时长为0，无法分割文本")
                            else:
                                cleaned_text = ""
                                logger.warning(f"片段 {i+1} 整体文本为空，无法分割")

                        integrated_segment = {
                            'start_time': start_time,
                            'end_time': end_time,
                            'duration': duration,
                            'text': cleaned_text,
                            'speaker_id': speaker_seg['speaker_id'],
                            'speaker_label': speaker_seg['speaker_label'],
                            'confidence': segment_result.confidence if segment_result.success else 0.5
                        }
                        integrated_segments.append(integrated_segment)

                        logger.info(f"片段 {i+1}/{len(speaker_segments_data)} 识别完成: {cleaned_text[:50]}...")

                    except Exception as segment_error:
                        logger.error(f"处理音频片段 {i+1} 失败: {segment_error}")
                        # 创建空文本片段作为占位符
                        integrated_segment = {
                            'start_time': start_time,
                            'end_time': end_time,
                            'duration': duration,
                            'text': "",
                            'speaker_id': speaker_seg['speaker_id'],
                            'speaker_label': speaker_seg['speaker_label'],
                            'confidence': 0.0
                        }
                        integrated_segments.append(integrated_segment)

            finally:
                # 清理临时文件
                for temp_file in temp_files:
                    try:
                        if os.path.exists(temp_file):
                            os.unlink(temp_file)
                    except Exception as cleanup_error:
                        logger.warning(f"清理临时文件失败: {cleanup_error}")

            # 构建说话人统计信息（前端期望格式）
            final_speaker_segments = []
            for speaker_name, stats in speaker_statistics.items():
                final_speaker_segments.append({
                    'speaker': speaker_name,
                    'total_duration': stats['total_duration'],
                    'segment_count': stats['segment_count'],
                    'percentage': (stats['total_duration'] / sum(seg['duration'] for seg in speaker_segments_data)) * 100 if speaker_segments_data else 0
                })

            logger.info(f"增强说话人识别整合完成: {len(integrated_segments)} 个片段, {len(final_speaker_segments)} 个说话人")

        elif speaker_result and speaker_result.get('segments'):
            # 回退到传统说话人识别结果 - 也使用分段语音识别
            logger.info("使用传统说话人识别结果进行整合")
            raw_segments = speaker_result.get('segments', [])
            integrated_segments = []

            # 为传统说话人识别结果也进行分段语音识别
            import tempfile
            import librosa
            import soundfile as sf
            temp_files = []

            try:
                progress_callback(start_progress + preprocessing_size + step_size * 3.4, "为传统说话人片段进行语音识别", "traditional_segment_recognition")

                for i, segment in enumerate(raw_segments):
                    start_time = segment.get('start_time', 0)
                    end_time = segment.get('end_time', 0)
                    duration = end_time - start_time

                    # 跳过过短的片段
                    if duration < 0.3:
                        logger.warning(f"跳过过短传统片段 {i+1}: {duration:.2f}s")
                        continue

                    # 提取音频片段进行识别
                    try:
                        # 创建临时文件
                        with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as tmp_file:
                            temp_segment_path = tmp_file.name
                            temp_files.append(temp_segment_path)

                        # 使用librosa提取音频片段
                        y, sr = librosa.load(processed_file_path, sr=16000, offset=start_time, duration=duration)

                        if len(y) == 0:
                            logger.warning(f"传统音频片段 {i+1} 为空，跳过")
                            continue

                        # 保存音频片段
                        sf.write(temp_segment_path, y, sr)

                        # 对音频片段进行语音识别
                        segment_result = recognizer.recognize(temp_segment_path)

                        # 调试日志：记录识别结果
                        logger.info(f"传统片段 {i+1}/{len(raw_segments)} 识别结果: success={segment_result.success}, text_length={len(segment_result.text) if segment_result.text else 0}")

                        # 获取识别文本
                        if segment_result.success and segment_result.text:
                            cleaned_text = clean_sensevoice_text(segment_result.text)
                            logger.info(f"传统片段 {i+1} 使用直接识别结果: '{cleaned_text[:50]}...'")
                        else:
                            # 如果分段识别失败，使用原有文本或从整体文本提取
                            original_text = segment.get('text', '')
                            logger.info(f"传统片段 {i+1} 识别失败，尝试使用原有文本。原有文本长度: {len(original_text)}")

                            if original_text:
                                cleaned_text = clean_sensevoice_text(original_text)
                                logger.info(f"传统片段 {i+1} 使用原有文本: '{cleaned_text[:50]}...'")
                            else:
                                cleaned_text = ""
                                logger.warning(f"传统片段 {i+1} 原有文本也为空")

                        cleaned_segment = segment.copy()
                        cleaned_segment['text'] = cleaned_text
                        cleaned_segment['confidence'] = segment_result.confidence if segment_result.success else segment.get('confidence', 0.5)
                        integrated_segments.append(cleaned_segment)

                        logger.info(f"传统片段 {i+1}/{len(raw_segments)} 识别完成: {cleaned_text[:50]}...")

                    except Exception as segment_error:
                        logger.error(f"处理传统音频片段 {i+1} 失败: {segment_error}")
                        # 使用原有文本作为备用
                        cleaned_segment = segment.copy()
                        if 'text' in cleaned_segment:
                            cleaned_segment['text'] = clean_sensevoice_text(cleaned_segment['text'])
                        integrated_segments.append(cleaned_segment)

            finally:
                # 清理临时文件
                for temp_file in temp_files:
                    try:
                        if os.path.exists(temp_file):
                            os.unlink(temp_file)
                    except Exception as cleanup_error:
                        logger.warning(f"清理传统临时文件失败: {cleanup_error}")

            final_speaker_segments = speaker_result.get('speakers', [])
            logger.info(f"传统说话人识别整合完成: {len(integrated_segments)} 个片段")

        else:
            # 无说话人识别，使用传统整合方式
            logger.info("无说话人识别，使用传统整合方式")
            integration_result = _integrate_meeting_results(
                vad_result.get('segments', []),
                recognition_result.get('segments', []),
                [],  # 无说话人信息
                recognition_result.get('text', '')
            )
            integrated_segments = integration_result.get('speech_segments', [])
            final_speaker_segments = []
            logger.info(f"传统整合完成: {len(integrated_segments)} 个片段")

        # 构建最终转录结果
        raw_main_text = recognition_result.get('text', '')
        cleaned_main_text = clean_sensevoice_text(raw_main_text)

        transcription = {
            'text': cleaned_main_text,
            'language': recognition_result.get('language', language),
            'confidence': recognition_result.get('confidence', 0.0),
            'vad_segments': vad_result.get('segments', []),
            'speech_segments': integrated_segments,  # 前端期望的字段名
            'speaker_segments': final_speaker_segments,  # 说话人统计信息
            'total_duration': sum(seg.get('duration', 0) for seg in vad_result.get('segments', [])),
            'output_format': output_format,
            'include_timestamps': include_timestamps,
            'speaker_labeling': speaker_labeling,
            'segmentation_method': vad_result.get('segmentation_method', 'traditional'),
            'speaker_count': enhanced_speaker_result.get('speaker_count', 0) if enhanced_speaker_result else len(final_speaker_segments)
        }

        progress_callback(end_progress, "会议转录完成", "completed")
        return transcription

    except Exception as e:
        logger.error(f"会议转录执行失败: {e}")
        raise
    finally:
        # 清理预处理产生的临时文件
        if temp_processed_file and os.path.exists(temp_processed_file):
            try:
                os.unlink(temp_processed_file)
                logger.info(f"已清理预处理临时文件: {temp_processed_file}")
            except Exception as cleanup_error:
                logger.warning(f"清理预处理临时文件失败: {cleanup_error}")


@celery_app.task(bind=True, base=BaseTask, queue='audio_processing')
def audio_enhancement_task(
    self,
    task_id: str,
    user_id: str,
    file_ids: List[str],
    language: str = "auto",
    use_itn: bool = True,
    ban_emo_unk: bool = False,
    normalize: bool = True,
    denoise: bool = True,
    config: Optional[Dict[str, Any]] = None
):
    """
    音频增强任务（预处理+语音识别）

    Args:
        task_id: Task ID
        user_id: User ID
        file_ids: filesID列表
        language: 语言设置
        use_itn: 是否使用逆文本标准化
        ban_emo_unk: 是否禁用情感and未知标记
        normalize: 是否进行音量标准化
        denoise: 是否进行降噪
        config: 额外配置
    """
    progress_callback = ProgressCallback(task_id, self)

    try:
        logger.info(f"[AUDIO] 开始音频增强任务: {task_id}, 用户: {user_id}, files: {file_ids}")
        progress_callback(5, "初始化音频增强任务", "processing")

        config = config or {}

        # 获取files路径
        file_paths = []
        for file_id in file_ids:
            file_path = _get_audio_file_path(user_id, file_id)
            if file_path and os.path.exists(file_path):
                file_paths.append(file_path)
                logger.info(f"[LOG] 添加files到处理队列: {file_path}")
            else:
                error_msg = f"音频File not found: {file_id}, user_id: {user_id}"
                logger.error(f"[ERROR] {error_msg}")
                raise FileNotFoundError(error_msg)

        logger.info(f"[LOG] files验证完成，共找到 {len(file_paths)} 个有效files")
        progress_callback(10, "files验证完成", "processing")

        # 执行音频增强处理
        logger.info(f"[FIX] 开始执行音频增强处理，配置: normalize={normalize}, denoise={denoise}")
        result = _execute_audio_enhancement(
            file_paths=file_paths,
            file_ids=file_ids,
            user_id=user_id,
            task_id=task_id,
            language=language,
            use_itn=use_itn,
            ban_emo_unk=ban_emo_unk,
            normalize=normalize,
            denoise=denoise,
            config=config,
            progress_callback=progress_callback
        )

        logger.info(f"[OK] 音频增强任务完成: {task_id}")
        # 确保所有numpy类型都转换为Python原生类型
        return convert_numpy_types(result)

    except Exception as e:
        logger.error(f"[ERROR] 音频增强Task failed: {task_id}, error: {e}")
        logger.exception("详细error信息:")
        progress_callback(0, f"Task failed: {str(e)}", "failed")
        raise


def _execute_audio_enhancement(
    file_paths: List[str],
    file_ids: List[str],
    user_id: str,
    task_id: str,
    language: str,
    use_itn: bool,
    ban_emo_unk: bool,
    normalize: bool,
    denoise: bool,
    config: Dict[str, Any],
    progress_callback: ProgressCallback
) -> Dict[str, Any]:
    """
    执行音频增强处理
    """
    try:
        total_files = len(file_paths)
        results = []

        # 创建音频处理器
        processing_config = ProcessingConfig(
            normalize=normalize,
            denoise=denoise,
            target_db=config.get('target_db', -20.0),
            normalization_method=NormalizationMethod.PEAK,
            denoise_method=ProcessingMethod.SPECTRAL_GATING
        )

        audio_processor = EnhancedAudioProcessor(processing_config)
        progress_callback(15, "音频处理器初始化完成", "processing")

        # 获取FunASR管理器
        funasr_manager = get_funasr_manager()
        if not funasr_manager.load_model(str(settings.SENSEVOICE_MODEL_PATH)):
            raise RuntimeError("SenseVoice模型Load失败")

        progress_callback(25, "语音识别模型Load完成", "processing")

        for i, (file_path, file_id) in enumerate(zip(file_paths, file_ids)):
            try:
                file_progress_start = 25 + (i * 70 // total_files)
                file_progress_end = 25 + ((i + 1) * 70 // total_files)

                progress_callback(
                    file_progress_start,
                    f"处理files {i+1}/{total_files}: {os.path.basename(file_path)}",
                    "processing"
                )

                # 步骤1: 音频预处理
                with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as tmp_file:
                    enhanced_path = tmp_file.name

                enhanced_path = audio_processor.process_audio(file_path, enhanced_path)
                if not enhanced_path:
                    raise RuntimeError(f"音频预处理失败: {file_path}")

                progress_callback(
                    file_progress_start + 20,
                    f"音频预处理完成: {os.path.basename(file_path)}",
                    "processing"
                )

                # 步骤2: 语音识别
                recognition_result = funasr_manager.generate(
                    input_audio=enhanced_path,
                    language=language,
                    use_itn=use_itn,
                    ban_emo_unk=ban_emo_unk,
                    batch_size_s=60
                )

                # 清理临时files
                try:
                    os.unlink(enhanced_path)
                except:
                    pass

                progress_callback(
                    file_progress_end,
                    f"语音识别完成: {os.path.basename(file_path)}",
                    "processing"
                )

                # 处理识别结果
                if recognition_result and len(recognition_result) > 0:
                    # 合并所有识别结果
                    full_text = ""
                    segments = []

                    for segment in recognition_result:
                        if isinstance(segment, dict) and 'text' in segment:
                            raw_text = segment['text'].strip()
                            if raw_text:
                                # 清理技术标记
                                cleaned_text = clean_sensevoice_text(raw_text)
                                if cleaned_text:  # 只有清理后仍有内容才添加
                                    full_text += cleaned_text
                                    segments.append({
                                        'text': cleaned_text,
                                        'start_time': segment.get('start_time', 0.0),
                                        'end_time': segment.get('end_time', 0.0),
                                        'confidence': segment.get('confidence', 0.0)
                                    })

                    # 记录文本清理效果
                    if full_text:
                        original_length = sum(len(segment.get('text', '')) for segment in recognition_result if isinstance(segment, dict))
                        cleaned_length = len(full_text)
                        logger.info(f"任务结果文本清理完成: 原始长度={original_length}, 清理后长度={cleaned_length}")
                        if original_length > cleaned_length:
                            logger.debug(f"清理后文本前100字符: {full_text[:100]}...")
                        else:
                            logger.info("文本无需清理或已经是纯净文本")

                    results.append({
                        'file_id': file_id,
                        'file_path': file_path,
                        'status': 'success',
                        'text': full_text,
                        'segments': segments,
                        'language': language,
                        'enhanced': True,  # 标记为增强处理
                        'processing_config': {
                            'normalize': normalize,
                            'denoise': denoise,
                            'use_itn': use_itn,
                            'ban_emo_unk': ban_emo_unk
                        }
                    })
                else:
                    results.append({
                        'file_id': file_id,
                        'file_path': file_path,
                        'status': 'error',
                        'error': '语音识别未返回结果'
                    })

            except Exception as e:
                logger.error(f"处理files失败 {file_path}: {e}")
                results.append({
                    'file_id': file_id,
                    'file_path': file_path,
                    'status': 'error',
                    'error': str(e)
                })

        # 清理资源
        audio_processor.cleanup_temp_files()

        # 统计结果
        success_count = sum(1 for r in results if r['status'] == 'success')
        error_count = total_files - success_count

        final_result = {
            'task_type': 'audio_enhancement',
            'total_files': total_files,
            'success_count': success_count,
            'error_count': error_count,
            'language': language,
            'enhanced': True,
            'processing_config': {
                'normalize': normalize,
                'denoise': denoise,
                'use_itn': use_itn,
                'ban_emo_unk': ban_emo_unk
            },
            'results': results
        }

        # 保存处理结果到数据库
        progress_callback(98, "保存处理结果", "saving")

        try:
            from backend.core.database import get_db_session
            from backend.models.audio import ProcessingResult, AudioFile
            from datetime import datetime, timezone

            db = get_db_session()
            try:
                # 为每个成功处理的文件创建 ProcessingResult 记录
                for result in results:
                    if result['status'] == 'success':
                        file_id = result['file_id']

                        # 查找对应的 AudioFile 记录
                        audio_file = db.query(AudioFile).filter(
                            AudioFile.id == int(file_id),
                            AudioFile.owner_id == int(user_id)
                        ).first()

                        if audio_file:
                            # 由于结果已经在构建时清理过，直接使用即可
                            result_text = result.get('text', '')
                            result_segments = result.get('segments', [])

                            logger.info(f"数据库保存: 文本长度={len(result_text)}, segments数量={len(result_segments)}")

                            # 准备安全的结果数据（结果已经清理过技术标记）
                            safe_result_data = {
                                'text': result_text,
                                'segments': result_segments,
                                'language': result.get('language', language),
                                'enhanced': True,
                                'processing_type': 'audio_enhancement',
                                'confidence_score': result.get('confidence_score', 0.0),
                                'duration': result.get('duration', 0.0),
                                'word_count': len(result_text.split()) if result_text else 0
                            }

                            # 准备安全的配置数据
                            safe_processing_config = {
                                'language': language,
                                'use_itn': use_itn,
                                'ban_emo_unk': ban_emo_unk,
                                'normalize': normalize,
                                'denoise': denoise,
                                'enhanced': True
                            }

                            # 创建 ProcessingResult 记录
                            processing_result = ProcessingResult(
                                audio_file_id=audio_file.id,
                                processing_type="audio_enhancement",
                                result_data=safe_result_data,
                                processing_config=safe_processing_config,
                                processing_time=result.get('processing_time', 0),
                                confidence_score=result.get('confidence_score', 0.0),
                                status="completed"
                            )

                            db.add(processing_result)

                            # 更新 AudioFile 状态
                            audio_file.status = "enhanced"
                            audio_file.processed_at = datetime.now(timezone.utc)

                        else:
                            logger.warning(f"未找到音频文件记录: file_id={file_id}")

                db.commit()
                logger.info(f"音频增强结果已保存到数据库，任务ID: {task_id}")

            except Exception as db_error:
                db.rollback()
                logger.error(f"保存音频增强结果到数据库失败: {db_error}")
                # 不抛出异常，避免影响任务完成状态
            finally:
                db.close()

        except Exception as save_error:
            logger.error(f"数据库保存过程出错: {save_error}")
            # 不抛出异常，确保任务能正常完成

        progress_callback(100, "音频增强处理完成", "completed")
        return final_result

    except Exception as e:
        logger.error(f"音频增强执行失败: {e}")
        raise
