# ===========================================
# 语音处理智能平台 Docker 环境配置文件
# ===========================================
# 
# 使用说明:
# 1. 复制此文件为 .env: cp .env.template .env
# 2. 根据您的环境修改下面的配置值
# 3. 确保 .env 文件不被提交到版本控制系统
#

# ===========================================
# 🚀 服务器配置
# ===========================================
# Backend API服务器配置
BACKEND_HOST=0.0.0.0
BACKEND_PORT=8002
BACKEND_DEBUG=false
BACKEND_RELOAD=false

# ===========================================
# 🌐 前端服务配置
# ===========================================
# 前端服务器配置
FRONTEND_HOST=0.0.0.0
FRONTEND_PORT=3000
FRONTEND_BUILD_MODE=production

# Nginx配置
NGINX_PORT=80
NGINX_WORKER_PROCESSES=auto
NGINX_WORKER_CONNECTIONS=1024
NGINX_CLIENT_MAX_BODY_SIZE=100M
NGINX_KEEPALIVE_TIMEOUT=65

# 前端代理配置
FRONTEND_API_PROXY_HOST=backend
FRONTEND_API_PROXY_PORT=8002
FRONTEND_WEBSOCKET_PROXY_HOST=backend
FRONTEND_WEBSOCKET_PROXY_PORT=8002

# 静态资源配置
FRONTEND_STATIC_CACHE_EXPIRES=1y
FRONTEND_GZIP_ENABLED=true
FRONTEND_GZIP_MIN_LENGTH=1024

# 跨域配置 (多个地址用逗号分隔)
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://localhost:8002

# ===========================================
# 🗄️ 数据库配置
# ===========================================
# SQLite数据库配置
DATABASE_URL=sqlite:///./data/speech_platform.db
DATABASE_ECHO=false

# ===========================================
# 🤖 AI模型API配置 (关键配置)
# ===========================================
# Ollama配置 - 重要：确保向量维度一致性
# Docker环境中通常需要使用宿主机IP或host.docker.internal
OLLAMA_BASE_URL=http://host.docker.internal:11434
OLLAMA_MODEL=qwen3:4b

# 嵌入模型配置 - 关键：必须与现有向量数据库(768维)兼容
EMBEDDING_MODEL=nomic-embed-text:latest
EMBEDDING_DIMENSION=768

# OpenAI配置 (可选)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo

# Anthropic配置 (可选)
ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_MODEL=claude-3-sonnet-20240229

# Google配置 (可选)
GOOGLE_API_KEY=your_google_api_key_here
GOOGLE_MODEL=gemini-pro

# ===========================================
# 🎤 语音处理模型配置
# ===========================================
# 模型基础路径 (容器内路径)
MODELS_BASE_PATH=/app/models

# VAD模型配置
VAD_MODEL_PATH=/app/models/fsmn_vad_zh
VAD_MODEL_TYPE=fsmn_vad
VAD_USE_GPU=false

# 语音识别模型配置
ASR_MODEL_PATH=/app/models/SenseVoiceSmall
ASR_MODEL_TYPE=sensevoice
ASR_USE_GPU=false
ASR_LANGUAGE=auto
ASR_USE_ITN=true

# 说话人识别模型配置
SPEAKER_MODEL_PATH=/app/models/cam++
SPEAKER_MODEL_TYPE=campplus
SPEAKER_USE_GPU=false

# SenseVoice模型配置
SENSEVOICE_MODEL_PATH=/app/models/SenseVoiceSmall
SENSEVOICE_USE_GPU=false

# RAG重排序模型配置
RERANKER_MODEL_PATH=/app/models/Qwen3-Reranker-0.6B
RERANKER_USE_GPU=false
RERANKER_TOP_K=3
RERANK_INSTRUCTION=Find documents containing accurate factual information related to the query

# ===========================================
# 🔊 音频处理配置
# ===========================================
# 音频文件配置
AUDIO_UPLOAD_PATH=/app/data/uploads
AUDIO_MAX_SIZE=100MB
AUDIO_ALLOWED_FORMATS=wav,mp3,m4a,flac,aac

# 音频预处理配置
AUDIO_TARGET_SAMPLE_RATE=16000
AUDIO_TARGET_DB=-20.0
AUDIO_DENOISE_METHOD=spectral_gating

# VAD处理配置
VAD_THRESHOLD=0.5
VAD_MIN_SILENCE_DURATION=0.3
VAD_MIN_SPEECH_DURATION=0.1

# ===========================================
# 📊 向量数据库配置 (关键配置)
# ===========================================
# ChromaDB配置 - 重要：确保路径一致性
CHROMADB_PATH=/app/data/chroma_db
CHROMADB_COLLECTION_NAME=knowledge_base
CHROMADB_PERSIST_DIRECTORY=/app/data/chroma_db

# 检索配置
SIMILARITY_TOP_K=5
SIMILARITY_CUTOFF=0.0
RESPONSE_MODE=compact
STREAMING=false
CHUNK_SIZE=512
CHUNK_OVERLAP=32

# ===========================================
# ⚡ 批量处理配置
# ===========================================
# 并行处理配置
MAX_WORKERS=4
BATCH_SIZE=10
ENABLE_GPU_ACCELERATION=false

# 缓存配置
ENABLE_CACHE=true
CACHE_SIZE=1000
CACHE_TTL=3600

# ===========================================
# 🔐 安全配置
# ===========================================
# JWT配置
JWT_SECRET_KEY=your_jwt_secret_key_change_in_production_docker_env
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# 文件上传安全
MAX_UPLOAD_SIZE=100MB
ALLOWED_EXTENSIONS=wav,mp3,m4a,flac,aac,pdf,txt,docx,md,xlsx,pptx

# ===========================================
# 📝 日志配置
# ===========================================
# 日志级别
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# 日志文件配置 (容器内路径)
LOG_FILE_PATH=/app/logs/app.log
LOG_MAX_SIZE=10MB
LOG_BACKUP_COUNT=5

# ===========================================
# 🔄 Redis配置 (任务队列)
# ===========================================
# Redis连接配置
REDIS_URL=redis://redis:6379/0
REDIS_ENABLED=true
REDIS_PASSWORD=

# Celery配置
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0
CELERY_TASK_SERIALIZER=json
CELERY_RESULT_SERIALIZER=json
CELERY_ACCEPT_CONTENT=json
CELERY_TIMEZONE=UTC
CELERY_ENABLE_UTC=true

# ===========================================
# 📈 监控配置
# ===========================================
# 性能监控
ENABLE_MONITORING=true
METRICS_ENDPOINT=/metrics

# 健康检查
HEALTH_CHECK_ENDPOINT=/health

# ===========================================
# 🐳 Docker配置
# ===========================================
# Docker环境标识
DOCKER_ENV=true

# 容器内路径配置
CONTAINER_MODELS_PATH=/app/models
CONTAINER_DATA_PATH=/app/data
CONTAINER_LOGS_PATH=/app/logs
CONTAINER_UPLOADS_PATH=/app/data/uploads

# 数据卷映射配置 (宿主机路径，请根据实际情况修改)
HOST_MODELS_PATH=./volumes/models
HOST_DATA_PATH=./volumes/data
HOST_LOGS_PATH=./volumes/logs
HOST_UPLOADS_PATH=./volumes/uploads

# ===========================================
# 🌐 网络配置
# ===========================================
# 容器网络配置
DOCKER_NETWORK_NAME=speech_platform_network

# 服务发现配置
FRONTEND_SERVICE_NAME=frontend
BACKEND_SERVICE_NAME=backend
CELERY_SERVICE_NAME=celery-worker
REDIS_SERVICE_NAME=redis

# ===========================================
# 🔧 开发和调试配置
# ===========================================
# 开发模式配置
DEVELOPMENT_MODE=false
DEBUG_MODE=false

# Python配置
PYTHONUNBUFFERED=1
PYTHONDONTWRITEBYTECODE=1
PYTHONPATH=/app

# ===========================================
# 📦 依赖和版本配置
# ===========================================
# Python版本
PYTHON_VERSION=3.11

# 关键依赖版本 (确保一致性)
TORCH_VERSION=2.1.0
TRANSFORMERS_VERSION=4.36.0
CHROMADB_VERSION=0.4.24
LLAMA_INDEX_VERSION=0.10.0

# ===========================================
# 🚨 故障排除配置
# ===========================================
# 向量维度验证
VECTOR_DIMENSION_CHECK=true
EMBEDDING_MODEL_VALIDATION=true

# 启动时健康检查
STARTUP_HEALTH_CHECK=true
HEALTH_CHECK_TIMEOUT=300

# 错误恢复配置
AUTO_RESTART_ON_FAILURE=true
MAX_RESTART_ATTEMPTS=3
