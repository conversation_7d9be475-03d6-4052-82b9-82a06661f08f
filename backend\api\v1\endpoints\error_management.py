"""
错误管理和超时控制API端点
"""

from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.security import HTTPAuthorizationCredentials
from pydantic import BaseModel

from backend.core.security import get_current_user_id
from fastapi.security import HTTPBearer

security = HTTPBearer()
from backend.services.error_handler import get_error_handler
from backend.services.timeout_control import get_timeout_controller, TimeoutConfig
from loguru import logger

router = APIRouter()


class TimeoutConfigUpdate(BaseModel):
    """超时配置更新请求"""
    task_type: str
    soft_timeout: Optional[int] = None
    hard_timeout: Optional[int] = None
    warning_threshold: Optional[int] = None
    enabled: Optional[bool] = None


@router.get("/errors/statistics")
async def get_error_statistics(
    hours: int = Query(24, description="统计时间范围（小时）"),
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """获取错误统计信息"""
    try:
        user_id = get_current_user_id(credentials)
        error_handler = get_error_handler()
        
        # 获取错误统计
        stats = error_handler.get_error_statistics(hours)
        
        return {
            "success": True,
            "statistics": stats,
            "period_hours": hours
        }
        
    except Exception as e:
        logger.error(f"获取错误统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取错误统计失败"
        )


@router.get("/errors/report/{task_id}")
async def get_error_report(
    task_id: str,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """获取任务错误报告"""
    try:
        user_id = get_current_user_id(credentials)
        error_handler = get_error_handler()
        
        # 生成错误报告
        report = error_handler.create_error_report(task_id)
        
        if "error" in report:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=report["error"]
            )
        
        return {
            "success": True,
            "report": report
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取错误报告失败: {task_id}, {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取错误报告失败"
        )


@router.get("/timeouts/statistics")
async def get_timeout_statistics(
    hours: int = Query(24, description="统计时间范围（小时）"),
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """获取超时统计信息"""
    try:
        user_id = get_current_user_id(credentials)
        timeout_controller = get_timeout_controller()
        
        # 获取超时统计
        stats = timeout_controller.get_timeout_statistics(hours)
        
        return {
            "success": True,
            "statistics": stats,
            "period_hours": hours
        }
        
    except Exception as e:
        logger.error(f"获取超时统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取超时统计失败"
        )


@router.get("/timeouts/current")
async def get_current_timeouts(
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """获取当前超时任务"""
    try:
        user_id = get_current_user_id(credentials)
        timeout_controller = get_timeout_controller()
        
        # 获取当前超时任务
        timeout_tasks = timeout_controller.get_current_timeouts()
        
        return {
            "success": True,
            "timeout_tasks": timeout_tasks,
            "count": len(timeout_tasks)
        }
        
    except Exception as e:
        logger.error(f"获取当前超时任务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取当前超时任务失败"
        )


@router.get("/timeouts/config")
async def get_timeout_configs(
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """获取超时配置"""
    try:
        user_id = get_current_user_id(credentials)
        timeout_controller = get_timeout_controller()
        
        # 获取所有超时配置
        configs = {}
        for task_type, config in timeout_controller.timeout_configs.items():
            configs[task_type] = {
                "task_type": config.task_type,
                "soft_timeout": config.soft_timeout,
                "hard_timeout": config.hard_timeout,
                "warning_threshold": config.warning_threshold,
                "enabled": config.enabled
            }
        
        return {
            "success": True,
            "configs": configs
        }
        
    except Exception as e:
        logger.error(f"获取超时配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取超时配置失败"
        )


@router.put("/timeouts/config")
async def update_timeout_config(
    config_update: TimeoutConfigUpdate,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """更新超时配置（管理员功能）"""
    try:
        user_id = get_current_user_id(credentials)
        
        # 这里可以添加管理员权限检查
        # if not is_admin(user_id):
        #     raise HTTPException(status_code=403, detail="需要管理员权限")
        
        timeout_controller = get_timeout_controller()
        
        # 获取当前配置
        current_config = timeout_controller.get_timeout_config(config_update.task_type)
        
        # 更新配置
        new_config = TimeoutConfig(
            task_type=config_update.task_type,
            soft_timeout=config_update.soft_timeout if config_update.soft_timeout is not None else current_config.soft_timeout,
            hard_timeout=config_update.hard_timeout if config_update.hard_timeout is not None else current_config.hard_timeout,
            warning_threshold=config_update.warning_threshold if config_update.warning_threshold is not None else current_config.warning_threshold,
            enabled=config_update.enabled if config_update.enabled is not None else current_config.enabled
        )
        
        # 验证配置
        if new_config.soft_timeout >= new_config.hard_timeout:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="软超时时间必须小于硬超时时间"
            )
        
        if new_config.warning_threshold >= new_config.soft_timeout:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="警告阈值必须小于软超时时间"
            )
        
        # 应用配置
        timeout_controller.update_timeout_config(config_update.task_type, new_config)
        
        return {
            "success": True,
            "message": f"超时配置已更新: {config_update.task_type}",
            "config": {
                "task_type": new_config.task_type,
                "soft_timeout": new_config.soft_timeout,
                "hard_timeout": new_config.hard_timeout,
                "warning_threshold": new_config.warning_threshold,
                "enabled": new_config.enabled
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"更新超时配置失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新超时配置失败"
        )


@router.post("/timeouts/check/{task_id}")
async def check_task_timeout(
    task_id: str,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """检查特定任务的超时状态"""
    try:
        user_id = get_current_user_id(credentials)
        timeout_controller = get_timeout_controller()
        
        # 获取任务记录
        from backend.services.task_persistence_service import get_task_persistence_service
        from backend.core.database import get_db_session
        
        persistence_service = get_task_persistence_service()
        db = get_db_session()
        
        try:
            task_record = persistence_service.get_task_record(db, task_id)
            
            if not task_record:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="任务不存在"
                )
            
            # 检查权限
            if task_record.user_id != user_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权访问此任务"
                )
            
            # 检查超时状态
            timeout_info = timeout_controller.check_task_timeout(task_record)
            
            return {
                "success": True,
                "task_id": task_id,
                "timeout_info": timeout_info
            }
            
        finally:
            db.close()
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"检查任务超时失败: {task_id}, {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="检查任务超时失败"
        )


@router.get("/monitoring/status")
async def get_monitoring_status(
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """获取监控服务状态"""
    try:
        user_id = get_current_user_id(credentials)
        timeout_controller = get_timeout_controller()
        
        # 获取监控状态
        status_info = {
            "timeout_monitoring": {
                "enabled": timeout_controller.monitoring,
                "configs_count": len(timeout_controller.timeout_configs),
                "warned_tasks_count": len(timeout_controller.warned_tasks)
            }
        }
        
        return {
            "success": True,
            "monitoring_status": status_info
        }
        
    except Exception as e:
        logger.error(f"获取监控状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取监控状态失败"
        )


@router.post("/monitoring/start")
async def start_monitoring(
    interval: int = Query(30, description="监控间隔（秒）"),
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """启动监控服务（管理员功能）"""
    try:
        user_id = get_current_user_id(credentials)
        
        # 这里可以添加管理员权限检查
        # if not is_admin(user_id):
        #     raise HTTPException(status_code=403, detail="需要管理员权限")
        
        timeout_controller = get_timeout_controller()
        
        # 启动超时监控
        import asyncio
        asyncio.create_task(timeout_controller.start_monitoring(interval))
        
        return {
            "success": True,
            "message": f"监控服务已启动，间隔: {interval}秒"
        }
        
    except Exception as e:
        logger.error(f"启动监控服务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="启动监控服务失败"
        )


@router.post("/monitoring/stop")
async def stop_monitoring(
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """停止监控服务（管理员功能）"""
    try:
        user_id = get_current_user_id(credentials)
        
        # 这里可以添加管理员权限检查
        # if not is_admin(user_id):
        #     raise HTTPException(status_code=403, detail="需要管理员权限")
        
        timeout_controller = get_timeout_controller()
        
        # 停止超时监控
        timeout_controller.stop_monitoring()
        
        return {
            "success": True,
            "message": "监控服务已停止"
        }
        
    except Exception as e:
        logger.error(f"停止监控服务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="停止监控服务失败"
        )
