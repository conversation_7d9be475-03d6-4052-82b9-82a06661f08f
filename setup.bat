@echo off
echo ========================================
echo 语音处理智能平台 - 环境设置脚本
echo ========================================
echo.

:: 检查 Python
echo 🔍 检查 Python 环境...
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python 未安装或未添加到 PATH
    echo 请安装 Python 3.11+ 并添加到系统 PATH
    pause
    exit /b 1
)
echo ✅ Python 环境正常

:: 检查 Node.js
echo 🔍 检查 Node.js 环境...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装或未添加到 PATH
    echo 请安装 Node.js 16+ 并添加到系统 PATH
    pause
    exit /b 1
)
echo ✅ Node.js 环境正常

:: 检查 Docker
echo 🔍 检查 Docker 环境...
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ⚠️ Docker 未安装，Redis 需要手动启动
    echo 建议安装 Docker Desktop for Windows
) else (
    echo ✅ Docker 环境正常
)

:: 检查 uv
echo 🔍 检查 uv 包管理器...
uv --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 📦 安装 uv 包管理器...
    pip install uv
    if %errorlevel% neq 0 (
        echo ❌ uv 安装失败
        pause
        exit /b 1
    )
)
echo ✅ uv 包管理器正常

:: 创建虚拟环境
echo.
echo 🏗️ 创建 Python 虚拟环境...
if not exist ".venv" (
    uv venv .venv
    if %errorlevel% neq 0 (
        echo ❌ 虚拟环境创建失败
        pause
        exit /b 1
    )
    echo ✅ 虚拟环境创建成功
) else (
    echo ✅ 虚拟环境已存在
)

:: 激活虚拟环境并安装后端依赖
echo.
echo 📦 安装后端依赖...
call .venv\Scripts\activate.bat
cd backend
uv pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo ❌ 后端依赖安装失败
    pause
    exit /b 1
)
echo ✅ 后端依赖安装成功
cd ..

:: 安装前端依赖
echo.
echo 📦 安装前端依赖...
cd frontend
npm install
if %errorlevel% neq 0 (
    echo ❌ 前端依赖安装失败
    pause
    exit /b 1
)
echo ✅ 前端依赖安装成功
cd ..

:: 创建必要的目录
echo.
echo 📁 创建必要的目录...
if not exist "backend\data\uploads" mkdir backend\data\uploads
if not exist "backend\data\documents" mkdir backend\data\documents
if not exist "backend\data\chroma_db" mkdir backend\data\chroma_db
if not exist "backend\logs" mkdir backend\logs
echo ✅ 目录创建完成

:: 创建环境变量文件
echo.
echo ⚙️ 创建环境配置文件...
if not exist "backend\.env" (
    echo # 数据库配置 > backend\.env
    echo DATABASE_URL=sqlite:///./speech_platform.db >> backend\.env
    echo. >> backend\.env
    echo # Redis 配置 >> backend\.env
    echo REDIS_URL=redis://localhost:6379/0 >> backend\.env
    echo CELERY_BROKER_URL=redis://localhost:6379/0 >> backend\.env
    echo CELERY_RESULT_BACKEND=redis://localhost:6379/0 >> backend\.env
    echo. >> backend\.env
    echo # 安全配置 >> backend\.env
    echo SECRET_KEY=your-secret-key-change-in-production >> backend\.env
    echo ALGORITHM=HS256 >> backend\.env
    echo ACCESS_TOKEN_EXPIRE_MINUTES=30 >> backend\.env
    echo. >> backend\.env
    echo # 模型路径配置 >> backend\.env
    echo MODEL_BASE_PATH=../models >> backend\.env
    echo EMBEDDING_MODEL_PATH=../models/embedding >> backend\.env
    echo RERANKER_MODEL_PATH=../models/Qwen3-Reranker-0.6B >> backend\.env
    echo. >> backend\.env
    echo # 文件上传配置 >> backend\.env
    echo MAX_FILE_SIZE=100MB >> backend\.env
    echo UPLOAD_DIR=./data/uploads >> backend\.env
    echo ✅ 环境配置文件创建成功
) else (
    echo ✅ 环境配置文件已存在
)

echo.
echo ========================================
echo 🎉 环境设置完成！
echo ========================================
echo.
echo 📋 下一步操作:
echo   1. 运行 start_services.bat 启动所有服务
echo   2. 或者手动启动各个服务 (参考 PROJECT_STARTUP_GUIDE.md)
echo.
echo 💡 提示:
echo   - 首次启动可能需要下载 AI 模型
echo   - 确保网络连接稳定
echo   - 如遇问题请查看 PROJECT_STARTUP_GUIDE.md
echo.
pause
