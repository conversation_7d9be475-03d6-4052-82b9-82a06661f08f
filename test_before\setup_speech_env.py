#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语音处理识别系统环境配置脚本
用于设置模型路径、环境变量和验证系统配置
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

# 模型配置
MODEL_BASE_PATH = r"C:\Users\<USER>\Documents\my_project\models\model_dir"
MODELS_CONFIG = {
    "sensevoice": {
        "name": "SenseVoiceSmall",
        "path": os.path.join(MODEL_BASE_PATH, "SenseVoiceSmall"),
        "description": "多语言语音识别模型"
    },
    "campplus": {
        "name": "CAM++",
        "path": os.path.join(MODEL_BASE_PATH, "cam++"),
        "description": "说话人识别模型"
    },
    "vad": {
        "name": "fsmn_vad_zh",
        "path": os.path.join(MODEL_BASE_PATH, "fsmn_vad_zh"),
        "description": "语音活动检测模型"
    }
}

def check_python_version():
    """检查Python版本"""
    print("🐍 检查Python版本...")
    version = sys.version_info
    print(f"   当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ 错误: 需要Python 3.8或更高版本")
        return False
    
    print("✅ Python版本符合要求")
    return True

def check_system_info():
    """检查系统信息"""
    print("💻 系统信息:")
    print(f"   操作系统: {platform.system()} {platform.release()}")
    print(f"   架构: {platform.machine()}")
    print(f"   处理器: {platform.processor()}")
    
    # 检查CUDA可用性
    try:
        import torch
        cuda_available = torch.cuda.is_available()
        print(f"   CUDA可用: {'是' if cuda_available else '否'}")
        if cuda_available:
            print(f"   CUDA版本: {torch.version.cuda}")
            print(f"   GPU数量: {torch.cuda.device_count()}")
            for i in range(torch.cuda.device_count()):
                print(f"   GPU {i}: {torch.cuda.get_device_name(i)}")
    except ImportError:
        print("   CUDA状态: 未安装PyTorch，无法检测")

def setup_environment_variables():
    """设置环境变量"""
    print("🔧 设置环境变量...")
    
    # 设置模型路径
    os.environ['SPEECH_MODEL_BASE_PATH'] = MODEL_BASE_PATH
    
    # 设置离线模式
    offline_vars = {
        'FUNASR_CACHE_OFFLINE': '1',
        'MODELSCOPE_OFFLINE_MODE': '1',
        'HF_HUB_OFFLINE': '1',
        'TRANSFORMERS_OFFLINE': '1',
        'DISABLE_MODEL_DOWNLOAD': '1'
    }
    
    for key, value in offline_vars.items():
        os.environ[key] = value
        print(f"   设置 {key}={value}")
    
    # 设置缓存目录
    cache_dir = os.path.join(os.getcwd(), ".cache")
    os.environ['FUNASR_CACHE_DIR'] = cache_dir
    os.environ['TRANSFORMERS_CACHE'] = cache_dir
    
    print("✅ 环境变量设置完成")

def check_model_paths():
    """检查模型路径"""
    print("📁 检查模型路径...")
    
    base_path = Path(MODEL_BASE_PATH)
    if not base_path.exists():
        print(f"❌ 模型基础路径不存在: {MODEL_BASE_PATH}")
        print("   请确保模型文件已下载到指定路径")
        return False
    
    print(f"✅ 模型基础路径存在: {MODEL_BASE_PATH}")
    
    # 检查各个模型路径
    for model_key, model_info in MODELS_CONFIG.items():
        model_path = Path(model_info["path"])
        if model_path.exists():
            print(f"✅ {model_info['name']} 路径存在: {model_path}")
        else:
            print(f"⚠️  {model_info['name']} 路径不存在: {model_path}")
            print(f"   描述: {model_info['description']}")
    
    return True

def install_dependencies():
    """安装依赖包"""
    print("📦 检查和安装依赖包...")
    
    requirements_file = "requirements_speech.txt"
    if not os.path.exists(requirements_file):
        print(f"❌ 依赖文件不存在: {requirements_file}")
        return False
    
    try:
        # 升级pip
        print("   升级pip...")
        subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                      check=True, capture_output=True)
        
        # 安装依赖
        print("   安装依赖包...")
        result = subprocess.run([sys.executable, "-m", "pip", "install", "-r", requirements_file], 
                              check=True, capture_output=True, text=True)
        
        print("✅ 依赖包安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装依赖失败: {e}")
        print(f"   错误输出: {e.stderr}")
        return False

def test_imports():
    """测试关键模块导入"""
    print("🧪 测试关键模块导入...")
    
    test_modules = [
        ("streamlit", "Streamlit Web框架"),
        ("funasr", "FunASR语音识别框架"),
        ("torch", "PyTorch深度学习框架"),
        ("torchaudio", "PyTorch音频处理"),
        ("soundfile", "音频文件处理"),
        ("librosa", "音频分析库"),
        ("sklearn", "机器学习库"),
        ("numpy", "数值计算库"),
        ("pandas", "数据处理库")
    ]
    
    success_count = 0
    for module_name, description in test_modules:
        try:
            __import__(module_name)
            print(f"✅ {module_name} - {description}")
            success_count += 1
        except ImportError as e:
            print(f"❌ {module_name} - {description}: {e}")
    
    print(f"📊 导入测试结果: {success_count}/{len(test_modules)} 成功")
    return success_count == len(test_modules)

def create_config_file():
    """创建配置文件"""
    print("📝 创建配置文件...")
    
    config_content = f"""# 语音处理识别系统配置文件
# 自动生成于环境配置过程

[models]
base_path = {MODEL_BASE_PATH}
sensevoice_path = {MODELS_CONFIG['sensevoice']['path']}
campplus_path = {MODELS_CONFIG['campplus']['path']}
vad_path = {MODELS_CONFIG['vad']['path']}

[processing]
# 并行处理线程数（建议为CPU核心数的2倍）
max_workers = 8
# 音频分块大小（秒）
chunk_size = 30
# 缓存启用
enable_cache = true

[performance]
# GPU加速
use_gpu = auto
# 内存限制（GB）
memory_limit = 8
# 批处理大小
batch_size = 4

[output]
# 默认输出格式
default_format = json
# 输出目录
output_dir = ./output
# 文件命名模式
filename_pattern = speech_result_{{timestamp}}
"""
    
    config_path = "speech_config.ini"
    with open(config_path, 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print(f"✅ 配置文件已创建: {config_path}")

def main():
    """主函数"""
    print("🚀 语音处理识别系统环境配置")
    print("=" * 50)
    
    # 检查Python版本
    if not check_python_version():
        sys.exit(1)
    
    # 显示系统信息
    check_system_info()
    print()
    
    # 设置环境变量
    setup_environment_variables()
    print()
    
    # 检查模型路径
    check_model_paths()
    print()
    
    # 安装依赖
    if input("是否安装/更新依赖包? (y/n): ").lower() == 'y':
        install_dependencies()
        print()
    
    # 测试导入
    test_imports()
    print()
    
    # 创建配置文件
    create_config_file()
    print()
    
    print("🎉 环境配置完成！")
    print("💡 提示:")
    print("   1. 请确保模型文件已下载到指定路径")
    print("   2. 如有GPU，建议安装CUDA版本的PyTorch")
    print("   3. 运行 'streamlit run Home.py' 启动应用")

if __name__ == "__main__":
    main() 