@echo off
echo ========================================
echo 语音处理智能平台 - 服务启动脚本
echo ========================================
echo.

:: 检查虚拟环境
if not exist ".venv\Scripts\activate.bat" (
    echo ❌ 虚拟环境不存在，请先运行 setup.bat
    pause
    exit /b 1
)

:: 检查 Redis 是否运行
echo 🔍 检查 Redis 服务状态...
docker ps | findstr redis-server >nul
if %errorlevel% neq 0 (
    echo 🚀 启动 Redis 服务...
    docker run -d --name redis-server -p 6379:6379 redis:latest
    if %errorlevel% neq 0 (
        echo ❌ Redis 启动失败，请检查 Docker 是否安装
        pause
        exit /b 1
    )
    echo ✅ Redis 服务启动成功
) else (
    echo ✅ Redis 服务已运行
)

echo.
echo 🚀 启动后端服务...
start "后端服务" cmd /k "cd /d %~dp0 && .venv\Scripts\activate && cd backend && python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload"

echo.
echo 🚀 启动 Celery Worker...
start "Celery Worker" cmd /k "cd /d %~dp0 && .venv\Scripts\activate && python start_worker.py"

echo.
echo 🚀 启动前端服务...
start "前端服务" cmd /k "cd /d %~dp0frontend && npm run dev"

echo.
echo ========================================
echo 🎉 所有服务启动完成！
echo ========================================
echo.
echo 📱 访问地址:
echo   前端界面: http://localhost:3000
echo   后端 API: http://localhost:8000
echo   API 文档: http://localhost:8000/docs
echo.
echo 💡 提示:
echo   - 等待几秒钟让服务完全启动
echo   - 如果遇到问题，请查看各个终端窗口的日志
echo   - 按任意键退出此窗口
echo.
pause
