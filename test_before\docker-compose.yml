services:
  notebook-app:
    image: my-notebook-rag:latest
    container_name: my-notebook-app
    ports:
      - "8501:8501"
    volumes:
      # 持久化数据存储
      - ./chroma_db:/app/chroma_db
      - ./data:/app/data
      - ./knowledge_base:/app/knowledge_base
      - C:\Users\<USER>\Documents\my_project\models\model_dir:/app/models
    environment:
      - PYTHONPATH=/app
      - STREAMLIT_SERVER_PORT=8501
      - STREAMLIT_SERVER_ADDRESS=0.0.0.0
      - STREAMLIT_SERVER_HEADLESS=true
      - STREAMLIT_BROWSER_GATHER_USAGE_STATS=false
      # 添加NVIDIA相关环境变量
      - NVIDIA_VISIBLE_DEVICES=all
      - CUDA_VISIBLE_DEVICES=0
      # 从 .env 文件中读取OLLAMA_BASE_URL
      - OLLAMA_BASE_URL=${OLLAMA_BASE_URL:-http://host.docker.internal:11434}
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    # 添加host.docker.internal解析支持
    extra_hosts:
      - "host.docker.internal:host-gateway"
    # 可选：使用host网络模式以获得最佳连接性
    # network_mode: "host"  # 如果其他方式不工作，取消注释这行
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: all
              capabilities: [gpu, compute, utility]


# 可选：如果需要在容器中运行Ollama
# ollama:
#   image: ollama/ollama:latest
#   container_name: ollama-server
#   ports:
#     - "11434:11434"
#   volumes:
#     - ollama_data:/root/.ollama
#   restart: unless-stopped

# volumes:
#   ollama_data: 