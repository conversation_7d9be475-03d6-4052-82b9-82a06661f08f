#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试进度追踪功能
"""

import asyncio
import time
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.progress_service import progress_service, ProgressTracker

async def test_progress_tracking():
    """测试进度追踪功能"""
    print("🧪 开始测试进度追踪功能...")
    
    # 测试1: 基本进度追踪
    print("\n📊 测试1: 基本进度追踪")
    task_id = "test_task_1"
    
    # 创建任务
    progress = progress_service.create_task(task_id, "测试任务", "开始测试...")
    print(f"✅ 创建任务: {progress.to_dict()}")
    
    # 更新进度
    for i in range(0, 101, 20):
        progress_service.update_progress(task_id, i, f"进度 {i}%")
        current = progress_service.get_progress(task_id)
        print(f"📈 进度更新: {i}% - {current.detail}")
        await asyncio.sleep(0.5)
    
    # 完成任务
    progress_service.complete_task(task_id, "任务完成！")
    final = progress_service.get_progress(task_id)
    print(f"🎉 任务完成: {final.to_dict()}")
    
    # 测试2: 使用上下文管理器
    print("\n📊 测试2: 上下文管理器")
    task_id_2 = "test_task_2"
    
    try:
        with ProgressTracker(task_id_2, "上下文测试", "使用上下文管理器") as tracker:
            for i in range(0, 101, 25):
                tracker.update(i, f"上下文进度 {i}%")
                current = progress_service.get_progress(task_id_2)
                print(f"📈 上下文进度: {i}% - {current.detail}")
                await asyncio.sleep(0.3)
    except Exception as e:
        print(f"❌ 上下文测试失败: {e}")
    
    final_2 = progress_service.get_progress(task_id_2)
    print(f"🎉 上下文任务完成: {final_2.to_dict()}")
    
    # 测试3: 错误处理
    print("\n📊 测试3: 错误处理")
    task_id_3 = "test_task_3"
    
    try:
        with ProgressTracker(task_id_3, "错误测试", "模拟错误") as tracker:
            tracker.update(30, "即将发生错误...")
            await asyncio.sleep(0.5)
            raise Exception("模拟的错误")
    except Exception as e:
        print(f"⚠️ 捕获到预期错误: {e}")
    
    final_3 = progress_service.get_progress(task_id_3)
    print(f"❌ 错误任务状态: {final_3.to_dict()}")
    
    # 测试4: 获取所有进度
    print("\n📊 测试4: 获取所有进度")
    all_progress = progress_service.get_all_progress()
    print(f"📋 所有任务数量: {len(all_progress)}")
    for tid, prog in all_progress.items():
        print(f"  - {tid}: {prog.status.value} ({prog.percentage}%)")
    
    # 清理测试任务
    print("\n🧹 清理测试任务...")
    progress_service.remove_task(task_id)
    progress_service.remove_task(task_id_2)
    progress_service.remove_task(task_id_3)
    
    print("✅ 进度追踪测试完成！")

if __name__ == "__main__":
    asyncio.run(test_progress_tracking())
