#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试RAG上传进度显示
"""

import requests
import time
import json

# 配置
API_BASE_URL = "http://localhost:8002"

def login():
    """登录获取token"""
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    response = requests.post(f"{API_BASE_URL}/api/v1/auth/login", json=login_data)
    
    if response.status_code == 200:
        token = response.json()["access_token"]
        print("✅ 登录成功")
        return token
    else:
        print(f"❌ 登录失败: {response.status_code} - {response.text}")
        return None

def get_documents(token):
    """获取文档列表"""
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    response = requests.get(f"{API_BASE_URL}/api/v1/documents/documents", headers=headers)
    
    if response.status_code == 200:
        result = response.json()
        print(f"📊 API响应: {result}")

        # 处理不同的响应格式
        if isinstance(result, dict):
            documents = result.get("data", result.get("documents", []))
        else:
            documents = result

        print(f"✅ 获取到 {len(documents)} 个文档")

        # 找到已完成的文档
        completed_docs = []
        for doc in documents:
            if isinstance(doc, dict) and doc.get("status") == "completed":
                completed_docs.append(doc)

        print(f"📄 已完成的文档: {len(completed_docs)} 个")

        if completed_docs:
            return completed_docs[0]  # 返回第一个已完成的文档
        else:
            print("❌ 没有找到已完成的文档")
            # 如果没有已完成的文档，返回第一个文档用于测试
            if documents and isinstance(documents[0], dict):
                print(f"🔧 使用第一个文档进行测试: {documents[0].get('filename', 'Unknown')}")
                return documents[0]
            return None
    else:
        print(f"❌ 获取文档列表失败: {response.status_code} - {response.text}")
        return None

def upload_to_rag(token, document_id):
    """上传文档到RAG知识库"""
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    response = requests.post(
        f"{API_BASE_URL}/api/v1/documents/documents/{document_id}/upload-to-rag",
        headers=headers
    )
    
    if response.status_code == 200:
        result = response.json()
        print("✅ RAG上传请求成功")
        print(f"  任务ID: {result.get('task_id', 'N/A')}")
        print(f"  消息: {result.get('message', 'N/A')}")
        return result.get('task_id')
    else:
        print(f"❌ RAG上传失败: {response.status_code} - {response.text}")
        return None

def monitor_task_progress(task_id):
    """监控任务进度"""
    import redis
    
    redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
    progress_key = f"task_progress:{task_id}"
    
    print(f"\n📈 开始监控任务进度: {task_id}")
    print("=" * 60)
    
    max_wait = 120  # 最多等待2分钟
    start_time = time.time()
    last_percentage = -1
    
    while time.time() - start_time < max_wait:
        try:
            data = redis_client.hgetall(progress_key)
            
            if data:
                percentage = float(data.get('percentage', 0))
                detail = data.get('detail', '')
                stage = data.get('stage', '')
                
                # 只在进度变化时打印
                if percentage != last_percentage:
                    print(f"📊 进度: {percentage:5.1f}% | 阶段: {stage:12} | 详情: {detail}")
                    last_percentage = percentage
                
                # 检查是否完成
                if stage == 'completed' or percentage >= 100:
                    print("✅ 任务完成！")
                    break
                elif stage == 'failed':
                    print("❌ 任务失败！")
                    print(f"错误信息: {data.get('error', '未知错误')}")
                    break
            else:
                print("⚠️ 无法获取进度数据")
            
            time.sleep(1)
            
        except Exception as e:
            print(f"❌ 监控进度失败: {e}")
            break
    else:
        print("⏰ 监控超时")
    
    print("=" * 60)

def test_websocket_data():
    """测试WebSocket数据格式"""
    print("\n🔍 测试WebSocket数据转换...")
    
    # 模拟后端发送的WebSocket数据格式
    test_payloads = [
        {
            "task_id": "vectorize_test123",
            "progress": {
                "task_id": "vectorize_test123",
                "state": "PROGRESS",
                "progress": {
                    "percentage": 25.5,
                    "detail": "正在向量化第 10/40 个文档块...",
                    "stage": "vectorizing"
                }
            }
        },
        {
            "task_id": "vectorize_test123",
            "status": {
                "state": "PROGRESS",
                "progress": {
                    "percentage": 75.0,
                    "detail": "正在保存向量数据...",
                    "stage": "saving"
                }
            }
        },
        {
            "task_id": "vectorize_test123",
            "progress": {
                "state": "SUCCESS",
                "percentage": 100,
                "detail": "向量化完成",
                "stage": "completed"
            }
        }
    ]
    
    for i, payload in enumerate(test_payloads, 1):
        print(f"\n测试用例 {i}:")
        print(f"输入: {json.dumps(payload, indent=2, ensure_ascii=False)}")
        
        # 模拟前端的数据转换逻辑
        converted = convert_websocket_data(payload)
        print(f"转换后: {json.dumps(converted, indent=2, ensure_ascii=False)}")

def convert_websocket_data(payload):
    """模拟前端的WebSocket数据转换逻辑"""
    if not payload:
        return None
    
    let_percentage = 0
    let_detail = '处理中...'
    let_status = 'pending'

    # 从payload中提取实际的任务数据
    task_data = payload.get('progress') or payload.get('status') or payload
    
    # 安全地获取状态信息
    task_status = task_data.get('state') or task_data.get('status') or 'PENDING'
    
    # 获取嵌套的进度信息
    progress_info = task_data.get('progress') or {}
    
    # 根据任务状态确定进度
    if task_status == 'PENDING':
        if progress_info.get('percentage') is not None and progress_info.get('percentage') > 0:
            let_percentage = progress_info.get('percentage')
            let_detail = progress_info.get('detail') or progress_info.get('stage') or '处理中...'
            let_status = 'progress'
        else:
            let_percentage = 5
            let_detail = '等待处理...'
            let_status = 'pending'
    elif task_status == 'PROGRESS':
        if progress_info.get('percentage') is not None:
            let_percentage = progress_info.get('percentage')
            let_detail = progress_info.get('detail') or progress_info.get('stage') or '处理中...'
        elif task_data.get('percentage') is not None:
            let_percentage = task_data.get('percentage')
            let_detail = task_data.get('detail') or '处理中...'
        else:
            let_percentage = 20
            let_detail = '正在处理...'
        let_status = 'progress'
    elif task_status == 'SUCCESS':
        let_percentage = 100
        let_detail = '处理完成'
        let_status = 'completed'
    elif task_status == 'FAILURE':
        let_percentage = 0
        let_detail = '处理失败'
        let_status = 'failed'

    return {
        "percentage": let_percentage,
        "detail": let_detail,
        "status": let_status,
        "task_id": payload.get('task_id')
    }

def main():
    """主函数"""
    print("=" * 80)
    print("RAG上传进度显示测试")
    print("=" * 80)
    
    # 1. 测试WebSocket数据转换
    test_websocket_data()
    
    # 2. 登录
    token = login()
    if not token:
        return
    
    # 3. 获取文档
    document = get_documents(token)
    if not document:
        return
    
    print(f"\n📄 选择文档: {document['filename'][:50]}... (ID: {document['id']})")
    
    # 4. 上传到RAG
    task_id = upload_to_rag(token, document['id'])
    if not task_id:
        return
    
    # 5. 监控进度
    monitor_task_progress(task_id)
    
    print("\n✅ 测试完成！")
    print("💡 提示: 现在可以在前端查看全局进度条是否正常显示")

if __name__ == "__main__":
    main()
