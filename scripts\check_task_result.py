#!/usr/bin/env python3
"""检查特定任务的详细结果"""

import sqlite3
import sys
import json
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_task_result(task_id):
    """检查特定任务的详细结果"""
    db_path = project_root / "data" / "speech_platform.db"
    
    if not db_path.exists():
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # 查找特定任务的详细信息
        cursor.execute("""
            SELECT task_id, task_type, task_name, status, progress_percentage, 
                   created_at, completed_at, result, error_message, traceback,
                   task_args, task_kwargs
            FROM task_records 
            WHERE task_id = ?
        """, (task_id,))
        
        result = cursor.fetchone()
        
        if not result:
            print(f"没有找到任务: {task_id}")
            return
        
        task_id, task_type, task_name, status, progress, created_at, completed_at, result_data, error, traceback, args, kwargs = result
        
        print(f"任务详细信息:")
        print("-" * 100)
        print(f"任务ID: {task_id}")
        print(f"任务类型: {task_type}")
        print(f"任务名称: {task_name}")
        print(f"状态: {status}")
        print(f"进度: {progress}%")
        print(f"创建时间: {created_at}")
        print(f"完成时间: {completed_at}")
        print(f"错误信息: {error or '无'}")
        
        if traceback:
            print(f"错误堆栈: {traceback}")
        
        print(f"\n任务参数: {args}")
        print(f"任务关键字参数: {kwargs}")
        
        if result_data:
            try:
                data = json.loads(result_data)
                print(f"\n结果数据:")
                print(json.dumps(data, ensure_ascii=False, indent=2))
            except Exception as e:
                print(f"\n结果数据 (原始): {result_data}")
                print(f"JSON解析错误: {e}")
        else:
            print("\n结果数据: 无")
        
        conn.close()
        
    except Exception as e:
        print(f"检查任务时出错: {e}")

if __name__ == "__main__":
    # 检查最新的会议转录任务
    task_id = "meeting_transcription_17bd5f5891c9"
    check_task_result(task_id)
