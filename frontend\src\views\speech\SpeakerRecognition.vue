<template>
  <div class="speaker-recognition">
    <div class="page-header">
      <h1>说话人识别</h1>
      <p>上传音频文件进行说话人识别和分析</p>
    </div>

    <div class="content-container">
      <!-- 文件上传区域 -->
      <el-card class="upload-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>音频文件上传</span>
          </div>
        </template>
        
        <el-upload
          ref="uploadRef"
          class="upload-demo"
          drag
          :action="uploadUrl"
          :headers="uploadHeaders"
          :before-upload="beforeUpload"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :file-list="fileList"
          accept=".wav,.mp3,.m4a,.flac"
          :limit="1"
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将音频文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持 WAV、MP3、M4A、FLAC 格式，文件大小不超过 100MB
            </div>
          </template>
        </el-upload>
      </el-card>

      <!-- 处理结果区域 -->
      <el-card v-if="recognitionResult" class="result-card" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>识别结果</span>
          </div>
        </template>
        
        <div class="result-content">
          <div class="speaker-info">
            <h3>说话人信息</h3>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="说话人数量">
                {{ recognitionResult.speakerCount || 'N/A' }}
              </el-descriptions-item>
              <el-descriptions-item label="音频时长">
                {{ recognitionResult.duration || 'N/A' }}
              </el-descriptions-item>
              <el-descriptions-item label="采样率">
                {{ recognitionResult.sampleRate || 'N/A' }}
              </el-descriptions-item>
              <el-descriptions-item label="处理状态">
                <el-tag :type="recognitionResult.status === 'completed' ? 'success' : 'warning'">
                  {{ recognitionResult.status === 'completed' ? '已完成' : '处理中' }}
                </el-tag>
              </el-descriptions-item>
            </el-descriptions>
          </div>

          <div v-if="recognitionResult.speakers" class="speakers-list">
            <h3>说话人详情</h3>
            <el-table :data="recognitionResult.speakers" style="width: 100%">
              <el-table-column prop="id" label="说话人ID" width="120" />
              <el-table-column prop="segments" label="语音段数" width="120" />
              <el-table-column prop="totalDuration" label="总时长" width="120" />
              <el-table-column prop="confidence" label="置信度" width="120">
                <template #default="scope">
                  <el-progress 
                    :percentage="Math.round(scope.row.confidence * 100)" 
                    :color="getConfidenceColor(scope.row.confidence)"
                  />
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-card>

      <!-- 加载状态 -->
      <el-card v-if="loading" class="loading-card" shadow="hover">
        <div class="loading-content">
          <el-icon class="loading-icon"><loading /></el-icon>
          <p>正在处理音频文件，请稍候...</p>
          <el-progress :percentage="uploadProgress" />
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled, Loading } from '@element-plus/icons-vue'
import { useAuthStore } from '@/stores/auth'

export default {
  name: 'SpeakerRecognition',
  components: {
    UploadFilled,
    Loading
  },
  setup() {
    const authStore = useAuthStore()
    
    const uploadRef = ref()
    const fileList = ref([])
    const loading = ref(false)
    const uploadProgress = ref(0)
    const recognitionResult = ref(null)
    
    const uploadUrl = `${import.meta.env.VITE_API_BASE_URL}/api/audio/speaker-recognition`
    const uploadHeaders = {
      'Authorization': `Bearer ${authStore.token}`
    }

    const beforeUpload = (file) => {
      const isValidType = ['audio/wav', 'audio/mpeg', 'audio/mp4', 'audio/flac'].includes(file.type)
      const isLt100M = file.size / 1024 / 1024 < 100

      if (!isValidType) {
        ElMessage.error('只支持 WAV、MP3、M4A、FLAC 格式的音频文件!')
        return false
      }
      if (!isLt100M) {
        ElMessage.error('文件大小不能超过 100MB!')
        return false
      }
      
      loading.value = true
      uploadProgress.value = 0
      recognitionResult.value = null
      
      return true
    }

    const handleUploadSuccess = (response, file) => {
      loading.value = false
      uploadProgress.value = 100
      
      if (response.success) {
        recognitionResult.value = response.data
        ElMessage.success('说话人识别完成!')
      } else {
        ElMessage.error(response.message || '识别失败')
      }
    }

    const handleUploadError = (error, file) => {
      loading.value = false
      uploadProgress.value = 0
      ElMessage.error('上传失败: ' + error.message)
    }

    const getConfidenceColor = (confidence) => {
      if (confidence >= 0.8) return '#67c23a'
      if (confidence >= 0.6) return '#e6a23c'
      return '#f56c6c'
    }

    return {
      uploadRef,
      fileList,
      loading,
      uploadProgress,
      recognitionResult,
      uploadUrl,
      uploadHeaders,
      beforeUpload,
      handleUploadSuccess,
      handleUploadError,
      getConfidenceColor
    }
  }
}
</script>

<style scoped>
.speaker-recognition {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
}

.page-header h1 {
  margin: 0 0 8px 0;
  color: var(--el-text-color-primary);
}

.page-header p {
  margin: 0;
  color: var(--el-text-color-regular);
}

.content-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.upload-card,
.result-card,
.loading-card {
  width: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 600;
}

.upload-demo {
  width: 100%;
}

.result-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.speaker-info h3,
.speakers-list h3 {
  margin: 0 0 16px 0;
  color: var(--el-text-color-primary);
}

.loading-content {
  text-align: center;
  padding: 40px 20px;
}

.loading-icon {
  font-size: 48px;
  color: var(--el-color-primary);
  margin-bottom: 16px;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.loading-content p {
  margin: 16px 0;
  color: var(--el-text-color-regular);
}

@media (max-width: 768px) {
  .speaker-recognition {
    padding: 16px;
  }
  
  .content-container {
    gap: 16px;
  }
}
</style>
