#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的模型加载功能
验证SenseVoice和Paraformer的 "not registered" 错误修复
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

from utils.speech_recognition_utils import (
    set_offline_mode, 
    load_sensevoice_model_fixed, 
    load_paraformer_model_fixed
)

def test_model_loading():
    """测试模型加载修复"""
    print("🔧 开始测试模型加载修复...")
    
    # 设置离线模式
    set_offline_mode()
    print("✅ 离线模式已设置")
    
    # 测试模型路径（请根据实际情况修改）
    test_paths = {
        "SenseVoice": [
            "C:/Users/<USER>/Documents/my_project/models/model_dir/SenseVoiceSmall",
            "/path/to/sensevoice/model",  # Linux路径示例
            "~/models/SenseVoiceSmall"     # 相对路径示例
        ],
        "Paraformer": [
            "C:/Users/<USER>/Documents/my_project/models/model_dir/Paraformer",
            "/path/to/paraformer/model",   # Linux路径示例
            "~/models/paraformer"          # 相对路径示例
        ]
    }
    
    device = "cpu"  # 使用CPU进行测试
    
    print("\n" + "="*60)
    print("🎯 测试 SenseVoice 模型加载")
    print("="*60)
    
    sensevoice_success = False
    for path in test_paths["SenseVoice"]:
        if os.path.exists(path):
            print(f"\n📂 测试路径: {path}")
            try:
                model = load_sensevoice_model_fixed(path, device)
                if model is not None:
                    print(f"✅ SenseVoice模型加载成功: {path}")
                    print(f"   模型类型: {type(model)}")
                    sensevoice_success = True
                    break
                else:
                    print(f"❌ SenseVoice模型加载失败: {path}")
            except Exception as e:
                print(f"💥 SenseVoice模型加载异常: {e}")
        else:
            print(f"⚠️ 路径不存在，跳过: {path}")
    
    print("\n" + "="*60)
    print("🎯 测试 Paraformer 模型加载")
    print("="*60)
    
    paraformer_success = False
    for path in test_paths["Paraformer"]:
        if os.path.exists(path):
            print(f"\n📂 测试路径: {path}")
            try:
                model = load_paraformer_model_fixed(path, device)
                if model is not None:
                    print(f"✅ Paraformer模型加载成功: {path}")
                    print(f"   模型类型: {type(model)}")
                    paraformer_success = True
                    break
                else:
                    print(f"❌ Paraformer模型加载失败: {path}")
            except Exception as e:
                print(f"💥 Paraformer模型加载异常: {e}")
        else:
            print(f"⚠️ 路径不存在，跳过: {path}")
    
    print("\n" + "="*60)
    print("📊 测试结果总结")
    print("="*60)
    
    if sensevoice_success:
        print("✅ SenseVoice模型: 修复成功")
    else:
        print("❌ SenseVoice模型: 仍存在问题")
        
    if paraformer_success:
        print("✅ Paraformer模型: 修复成功")
    else:
        print("❌ Paraformer模型: 仍存在问题")
    
    if sensevoice_success and paraformer_success:
        print("\n🎉 所有模型加载修复验证通过！")
        return True
    else:
        print("\n⚠️ 部分或全部模型仍存在问题，需要进一步调试")
        return False

def test_funasr_imports():
    """测试FunASR相关导入"""
    print("\n🔧 测试FunASR相关导入...")
    
    try:
        from funasr import AutoModel
        print("✅ funasr.AutoModel 导入成功")
        
        # 测试AutoModel的基本功能
        print("🔍 检查AutoModel可用参数...")
        import inspect
        sig = inspect.signature(AutoModel.__init__)
        params = list(sig.parameters.keys())
        print(f"   可用参数: {params[:10]}...")  # 只显示前10个参数
        
        return True
        
    except ImportError as e:
        print(f"❌ FunASR导入失败: {e}")
        return False
    except Exception as e:
        print(f"💥 FunASR测试异常: {e}")
        return False

if __name__ == "__main__":
    print("🚀 启动模型加载修复测试")
    print("="*60)
    
    # 首先测试导入
    import_success = test_funasr_imports()
    
    if import_success:
        # 然后测试模型加载
        test_success = test_model_loading()
        
        if test_success:
            print("\n🎊 所有测试通过！修复生效！")
            sys.exit(0)
        else:
            print("\n⚠️ 测试未完全通过，需要进一步调试")
            sys.exit(1)
    else:
        print("\n❌ FunASR导入失败，请检查安装")
        sys.exit(1) 