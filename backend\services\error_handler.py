"""
错误处理和恢复服务
提供任务错误处理、超时控制和自动恢复机制
"""

import asyncio
import time
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass
from datetime import datetime, timezone, timedelta
from enum import Enum
from loguru import logger
import traceback

from backend.services.task_persistence_service import get_task_persistence_service
from backend.services.task_recovery_service import get_task_recovery_service
from backend.core.database import get_db_session
from backend.models.task_models import TaskStatus


class ErrorSeverity(str, Enum):
    """错误严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(str, Enum):
    """错误类别"""
    SYSTEM = "system"
    RESOURCE = "resource"
    NETWORK = "network"
    DATA = "data"
    TIMEOUT = "timeout"
    PERMISSION = "permission"
    UNKNOWN = "unknown"


@dataclass
class ErrorInfo:
    """错误信息"""
    task_id: str
    error_type: str
    error_message: str
    error_category: ErrorCategory
    severity: ErrorSeverity
    timestamp: datetime
    traceback: Optional[str] = None
    context: Optional[Dict[str, Any]] = None
    retry_count: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "task_id": self.task_id,
            "error_type": self.error_type,
            "error_message": self.error_message,
            "error_category": self.error_category.value,
            "severity": self.severity.value,
            "timestamp": self.timestamp.isoformat(),
            "traceback": self.traceback,
            "context": self.context,
            "retry_count": self.retry_count
        }


class ErrorHandler:
    """错误处理器"""
    
    def __init__(self):
        self.persistence_service = get_task_persistence_service()
        self.recovery_service = get_task_recovery_service()
        self.error_patterns = self._init_error_patterns()
        self.retry_strategies = self._init_retry_strategies()
        self.error_callbacks: Dict[str, List[Callable]] = {}
    
    def _init_error_patterns(self) -> Dict[str, Dict[str, Any]]:
        """初始化错误模式匹配"""
        return {
            "memory_error": {
                "patterns": ["MemoryError", "out of memory", "memory allocation"],
                "category": ErrorCategory.RESOURCE,
                "severity": ErrorSeverity.HIGH,
                "auto_retry": False,
                "suggestions": ["减少批处理大小", "增加系统内存", "优化内存使用"]
            },
            "timeout_error": {
                "patterns": ["TimeoutError", "timeout", "timed out"],
                "category": ErrorCategory.TIMEOUT,
                "severity": ErrorSeverity.MEDIUM,
                "auto_retry": True,
                "suggestions": ["增加超时时间", "检查网络连接", "优化处理逻辑"]
            },
            "network_error": {
                "patterns": ["ConnectionError", "NetworkError", "connection refused"],
                "category": ErrorCategory.NETWORK,
                "severity": ErrorSeverity.MEDIUM,
                "auto_retry": True,
                "suggestions": ["检查网络连接", "重试网络请求", "使用备用服务"]
            },
            "permission_error": {
                "patterns": ["PermissionError", "access denied", "permission denied"],
                "category": ErrorCategory.PERMISSION,
                "severity": ErrorSeverity.HIGH,
                "auto_retry": False,
                "suggestions": ["检查文件权限", "验证用户权限", "联系管理员"]
            },
            "data_error": {
                "patterns": ["ValueError", "TypeError", "invalid data", "corrupt"],
                "category": ErrorCategory.DATA,
                "severity": ErrorSeverity.MEDIUM,
                "auto_retry": False,
                "suggestions": ["验证输入数据", "检查数据格式", "清理数据"]
            },
            "system_error": {
                "patterns": ["SystemError", "OSError", "system call failed"],
                "category": ErrorCategory.SYSTEM,
                "severity": ErrorSeverity.HIGH,
                "auto_retry": True,
                "suggestions": ["检查系统状态", "重启服务", "联系系统管理员"]
            }
        }
    
    def _init_retry_strategies(self) -> Dict[ErrorCategory, Dict[str, Any]]:
        """初始化重试策略"""
        return {
            ErrorCategory.TIMEOUT: {
                "max_retries": 3,
                "base_delay": 5,
                "backoff_factor": 2,
                "max_delay": 60
            },
            ErrorCategory.NETWORK: {
                "max_retries": 5,
                "base_delay": 2,
                "backoff_factor": 1.5,
                "max_delay": 30
            },
            ErrorCategory.SYSTEM: {
                "max_retries": 2,
                "base_delay": 10,
                "backoff_factor": 2,
                "max_delay": 120
            },
            ErrorCategory.RESOURCE: {
                "max_retries": 1,
                "base_delay": 30,
                "backoff_factor": 1,
                "max_delay": 30
            },
            ErrorCategory.DATA: {
                "max_retries": 0,  # 数据错误通常不应重试
                "base_delay": 0,
                "backoff_factor": 1,
                "max_delay": 0
            },
            ErrorCategory.PERMISSION: {
                "max_retries": 0,  # 权限错误通常不应重试
                "base_delay": 0,
                "backoff_factor": 1,
                "max_delay": 0
            }
        }
    
    def classify_error(self, error_message: str, error_type: str = None) -> Dict[str, Any]:
        """分类错误"""
        error_text = f"{error_type} {error_message}".lower()
        
        for pattern_name, pattern_info in self.error_patterns.items():
            for pattern in pattern_info["patterns"]:
                if pattern.lower() in error_text:
                    return {
                        "pattern": pattern_name,
                        "category": pattern_info["category"],
                        "severity": pattern_info["severity"],
                        "auto_retry": pattern_info["auto_retry"],
                        "suggestions": pattern_info["suggestions"]
                    }
        
        # 默认分类
        return {
            "pattern": "unknown",
            "category": ErrorCategory.UNKNOWN,
            "severity": ErrorSeverity.MEDIUM,
            "auto_retry": True,
            "suggestions": ["检查错误日志", "联系技术支持"]
        }
    
    def handle_task_error(
        self,
        task_id: str,
        error: Exception,
        context: Dict[str, Any] = None
    ) -> ErrorInfo:
        """处理任务错误"""
        try:
            error_type = type(error).__name__
            error_message = str(error)
            error_traceback = traceback.format_exc()
            
            # 分类错误
            classification = self.classify_error(error_message, error_type)
            
            # 创建错误信息
            error_info = ErrorInfo(
                task_id=task_id,
                error_type=error_type,
                error_message=error_message,
                error_category=classification["category"],
                severity=classification["severity"],
                timestamp=datetime.now(timezone.utc),
                traceback=error_traceback,
                context=context or {}
            )
            
            # 记录错误
            self._log_error(error_info)
            
            # 更新任务状态
            self._update_task_error_status(task_id, error_info)
            
            # 决定是否自动重试
            if classification["auto_retry"]:
                self._schedule_retry(task_id, error_info)
            
            # 执行错误回调
            self._execute_error_callbacks(error_info)
            
            return error_info
            
        except Exception as e:
            logger.error(f"处理任务错误时出错: {task_id}, {e}")
            raise
    
    def _log_error(self, error_info: ErrorInfo):
        """记录错误日志"""
        log_level = {
            ErrorSeverity.LOW: logger.debug,
            ErrorSeverity.MEDIUM: logger.warning,
            ErrorSeverity.HIGH: logger.error,
            ErrorSeverity.CRITICAL: logger.critical
        }.get(error_info.severity, logger.error)
        
        log_level(
            f"任务错误 [{error_info.severity.value.upper()}] "
            f"任务ID: {error_info.task_id}, "
            f"类型: {error_info.error_type}, "
            f"消息: {error_info.error_message}"
        )
    
    def _update_task_error_status(self, task_id: str, error_info: ErrorInfo):
        """更新任务错误状态"""
        try:
            db = get_db_session()
            try:
                self.persistence_service.update_task_status(
                    db=db,
                    task_id=task_id,
                    status=TaskStatus.FAILURE,
                    error_message=error_info.error_message,
                    traceback=error_info.traceback
                )
            finally:
                db.close()
        except Exception as e:
            logger.error(f"更新任务错误状态失败: {task_id}, {e}")
    
    def _schedule_retry(self, task_id: str, error_info: ErrorInfo):
        """安排重试"""
        try:
            # 获取重试策略
            strategy = self.retry_strategies.get(
                error_info.error_category,
                self.retry_strategies[ErrorCategory.UNKNOWN]
            )
            
            # 检查是否还能重试
            if error_info.retry_count >= strategy["max_retries"]:
                logger.info(f"任务 {task_id} 已达最大重试次数，不再重试")
                return
            
            # 计算延迟时间
            delay = min(
                strategy["base_delay"] * (strategy["backoff_factor"] ** error_info.retry_count),
                strategy["max_delay"]
            )
            
            # 安排延迟重试
            asyncio.create_task(self._delayed_retry(task_id, delay))
            
            logger.info(f"安排任务 {task_id} 在 {delay} 秒后重试")
            
        except Exception as e:
            logger.error(f"安排重试失败: {task_id}, {e}")
    
    async def _delayed_retry(self, task_id: str, delay: float):
        """延迟重试"""
        try:
            await asyncio.sleep(delay)
            
            # 执行重试
            success = self.recovery_service.retry_failed_task(task_id)
            
            if success:
                logger.info(f"任务 {task_id} 自动重试成功")
            else:
                logger.warning(f"任务 {task_id} 自动重试失败")
                
        except Exception as e:
            logger.error(f"延迟重试出错: {task_id}, {e}")
    
    def _execute_error_callbacks(self, error_info: ErrorInfo):
        """执行错误回调"""
        try:
            callbacks = self.error_callbacks.get(error_info.error_category.value, [])
            callbacks.extend(self.error_callbacks.get("all", []))
            
            for callback in callbacks:
                try:
                    callback(error_info)
                except Exception as e:
                    logger.error(f"错误回调执行失败: {e}")
                    
        except Exception as e:
            logger.error(f"执行错误回调时出错: {e}")
    
    def register_error_callback(
        self,
        category: str,
        callback: Callable[[ErrorInfo], None]
    ):
        """注册错误回调"""
        if category not in self.error_callbacks:
            self.error_callbacks[category] = []
        self.error_callbacks[category].append(callback)
    
    def get_error_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """获取错误统计"""
        try:
            db = get_db_session()
            try:
                # 获取失败任务
                failed_tasks = self.persistence_service.get_failed_tasks(db, hours=hours)
                
                stats = {
                    "total_errors": len(failed_tasks),
                    "error_categories": {},
                    "error_types": {},
                    "hourly_distribution": {},
                    "retry_statistics": {
                        "total_retries": 0,
                        "successful_retries": 0,
                        "failed_retries": 0
                    }
                }
                
                # 按类别和类型统计
                for task in failed_tasks:
                    # 这里需要从错误信息中提取分类信息
                    # 简化处理，可以根据实际需求完善
                    error_type = "unknown"
                    if task.error_message:
                        classification = self.classify_error(task.error_message)
                        error_type = classification["pattern"]
                    
                    stats["error_types"][error_type] = stats["error_types"].get(error_type, 0) + 1
                    stats["retry_statistics"]["total_retries"] += task.retry_count
                
                return stats
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"获取错误统计失败: {e}")
            return {"error": str(e)}
    
    def create_error_report(self, task_id: str) -> Dict[str, Any]:
        """创建错误报告"""
        try:
            db = get_db_session()
            try:
                # 获取任务记录
                task_record = self.persistence_service.get_task_record(db, task_id)
                
                if not task_record:
                    return {"error": "任务不存在"}
                
                # 分析错误
                classification = {}
                if task_record.error_message:
                    classification = self.classify_error(task_record.error_message)
                
                # 生成报告
                report = {
                    "task_id": task_id,
                    "task_name": task_record.task_name,
                    "error_info": {
                        "message": task_record.error_message,
                        "traceback": task_record.traceback,
                        "classification": classification
                    },
                    "retry_info": {
                        "retry_count": task_record.retry_count,
                        "max_retries": task_record.max_retries,
                        "can_retry": task_record.retry_count < task_record.max_retries
                    },
                    "recommendations": classification.get("suggestions", []),
                    "timestamp": task_record.updated_at.isoformat() if task_record.updated_at else None
                }
                
                return report
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"创建错误报告失败: {task_id}, {e}")
            return {"error": str(e)}


# 全局错误处理器实例
error_handler = ErrorHandler()


def get_error_handler() -> ErrorHandler:
    """获取错误处理器实例"""
    return error_handler
