#!/usr/bin/env python3
"""
手动初始化数据库
"""

import sys
import asyncio
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from backend.core.database import init_db, get_db_session
from backend.models.user import User
from backend.core.security import get_password_hash

async def main():
    """主函数"""
    print("🚀 开始初始化数据库...")
    
    try:
        # 1. 初始化数据库（创建表和默认数据）
        print("\n1. 初始化数据库表...")
        await init_db()
        print("   ✅ 数据库表创建完成")
        
        # 2. 验证表是否创建成功
        print("\n2. 验证数据库表...")
        db = get_db_session()
        
        # 检查用户表
        users = db.query(User).all()
        print(f"   📋 用户表中有 {len(users)} 个用户")
        
        for user in users:
            print(f"      - ID: {user.id}, 用户名: {user.username}, 全名: {user.full_name}")
        
        db.close()
        
        # 3. 创建测试用户（如果不存在）
        print("\n3. 创建测试用户...")
        db = get_db_session()
        
        test_user = db.query(User).filter(User.username == "testuser").first()
        if not test_user:
            test_user = User(
                username="testuser",
                full_name_pinyin="ceshiyonghu",
                hashed_password=get_password_hash("testpass123"),
                full_name="测试用户",
                is_active=True,
                is_superuser=False
            )
            db.add(test_user)
            db.commit()
            db.refresh(test_user)
            print(f"   ✅ 测试用户创建成功: {test_user.username}")
        else:
            print(f"   ✅ 测试用户已存在: {test_user.username}")
        
        db.close()
        
        print("\n🎉 数据库初始化完成！")
        print("\n📝 可用的登录账户:")
        print("   管理员: admin / admin123")
        print("   测试用户: testuser / testpass123")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 数据库初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
