---
description: 
globs: 
alwaysApply: true
---
# 项目结构规范

## 整体架构布局

### 根目录结构
```
my_notebook_version_0.1.0/
├── backend/               # 后端FastAPI应用
├── frontend/              # 前端Vue.js应用
├── data/                  # 数据存储目录
├── models/                # AI模型存储
├── docs/                  # 项目文档
├── scripts/               # 部署和工具脚本
├── docker-compose.yml     # Docker编排文件
├── README.md              # 项目说明
└── requirements.txt       # Python依赖
```

## 后端项目结构

### 核心目录组织
参考后端主要结构：
- 主入口：[backend/main.py](mdc:backend/main.py)
- 核心配置：[backend/core/](mdc:backend/core)
- API路由：[backend/api/v1/](mdc:backend/api/v1)
- 数据模型：[backend/models/](mdc:backend/models)
- 业务服务：[backend/services/](mdc:backend/services)
- 数据库：[backend/schemas/](mdc:backend/schemas)

### 详细后端结构
```
backend/
├── api/                   # API层
│   ├── v1/               # API版本1
│   │   ├── endpoints/    # 具体端点实现
│   │   │   ├── auth.py          # 认证相关
│   │   │   ├── speech.py        # 语音处理
│   │   │   ├── audio.py         # 音频处理
│   │   │   ├── knowledge.py     # 知识库
│   │   │   ├── document_manager.py  # 文档管理
│   │   │   └── task_management.py   # 任务管理
│   │   └── api.py        # 路由注册
│   └── websocket.py      # WebSocket处理
├── core/                 # 核心模块
│   ├── config.py         # 配置管理
│   ├── database.py       # 数据库连接
│   ├── security.py       # 安全认证
│   ├── task_queue.py     # 任务队列
│   └── logging_config.py # 日志配置
├── services/             # 业务服务层
│   ├── rag_service.py           # RAG服务
│   ├── document_service.py      # 文档服务
│   ├── concurrency_control.py   # 并发控制
│   ├── progress_service.py      # 进度服务
│   └── error_handler.py         # 错误处理
├── models/               # 数据模型
│   ├── user.py           # 用户模型
│   ├── document.py       # 文档模型
│   └── task.py           # 任务模型
├── schemas/              # Pydantic模式
│   ├── user.py           # 用户模式
│   ├── document.py       # 文档模式
│   └── response.py       # 响应模式
├── utils/                # 工具函数
├── tests/                # 测试文件
├── data/                 # 数据存储
├── migrations/           # 数据库迁移
└── main.py               # 应用入口
```

## 前端项目结构

### 核心目录组织
参考前端主要结构：
- 主入口：[frontend/src/main.js](mdc:frontend/src/main.js)
- 应用组件：[frontend/src/App.vue](mdc:frontend/src/App.vue)
- 路由配置：[frontend/src/router/](mdc:frontend/src/router)
- 页面组件：[frontend/src/views/](mdc:frontend/src/views)
- 通用组件：[frontend/src/components/](mdc:frontend/src/components)

### 详细前端结构
```
frontend/
├── public/               # 静态资源
│   ├── static/          # 公共静态文件
│   └── index.html       # HTML模板
├── src/                 # 源代码
│   ├── api/             # API接口层
│   │   ├── auth.js      # 认证接口
│   │   ├── document.js  # 文档接口
│   │   └── speech.js    # 语音接口
│   ├── assets/          # 资源文件
│   │   ├── styles/      # 样式文件
│   │   │   ├── main.scss      # 主样式
│   │   │   └── global.css     # 全局样式
│   │   └── images/      # 图片资源
│   ├── components/      # 组件库
│   │   ├── common/      # 通用组件
│   │   ├── audio/       # 音频组件
│   │   └── layout/      # 布局组件
│   ├── composables/     # 组合式函数
│   ├── router/          # 路由配置
│   │   └── index.js     # 路由定义
│   ├── stores/          # Pinia状态管理
│   │   ├── user.js      # 用户状态
│   │   ├── document.js  # 文档状态
│   │   └── app.js       # 应用状态
│   ├── utils/           # 工具函数
│   │   ├── request.js   # 请求封装
│   │   ├── date.js      # 日期工具
│   │   └── validation.js # 验证工具
│   ├── views/           # 页面组件
│   │   ├── Home.vue           # 主页
│   │   ├── Dashboard.vue      # 仪表盘
│   │   ├── DocumentManager.vue # 文档管理
│   │   ├── KnowledgeBase.vue   # 知识库
│   │   ├── auth/              # 认证页面
│   │   ├── speech/            # 语音页面
│   │   └── error/             # 错误页面
│   ├── App.vue          # 根组件
│   └── main.js          # 入口文件
├── package.json         # 依赖配置
├── vite.config.js       # Vite配置
└── nginx.conf           # Nginx配置
```

## 数据存储结构

### 数据目录组织
```
data/
├── chroma_db/           # ChromaDB向量数据库
├── documents/           # 文档存储
│   ├── novels/          # 小说类文档
│   ├── manuals/         # 手册类文档
│   └── others/          # 其他文档
└── uploads/             # 用户上传文件
```

### 缓存和临时文件
```
cache/
├── static/              # 静态文件缓存
└── temp/                # 临时文件

voice_recognize_result/  # 语音识别结果
performance_reports/     # 性能报告
optimization_results/    # 优化结果
```

## 模型存储结构

### AI模型组织
```
models/
├── cam++/               # CAM++模型
│   └── examples/
├── fsmn_vad_zh/         # FSMN VAD中文模型
│   ├── example/
│   └── fig/
├── Qwen3-Reranker-0.6B/ # Qwen重排序模型
└── SenseVoiceSmall/     # SenseVoice小模型
    ├── example/
    └── fig/
```

## 配置文件结构

### 核心配置文件
- 应用配置：[backend/core/config.py](mdc:backend/core/config.py)
- 简化配置：[backend/core/config_simple.py](mdc:backend/core/config_simple.py)
- 语音配置：[speech_config.ini](mdc:speech_config.ini)
- 数据库配置：[backend/core/database.py](mdc:backend/core/database.py)

### 部署配置
```
docker-compose.yml       # Docker编排
Dockerfile               # Docker构建
nginx.conf               # Nginx配置
.env                     # 环境变量
requirements.txt         # Python依赖
package.json             # Node.js依赖
```

## 文档和脚本结构

### 文档组织
```
docs/                    # 项目文档
├── api/                 # API文档
├── deployment/          # 部署文档
├── development/         # 开发文档
└── user/                # 用户文档
```

### 工具脚本
```
scripts/                 # 工具脚本
├── setup.bat            # 环境设置
├── start_services.bat   # 启动服务
├── deploy.sh            # 部署脚本
└── backup.sh            # 备份脚本
```

## 测试文件结构

### 测试组织
```
backend/tests/           # 后端测试
├── unit/                # 单元测试
├── integration/         # 集成测试
└── api/                 # API测试

test_*.py               # 根目录测试文件
├── test_streamlit_ui_e2e.py      # UI端到端测试
├── test_speech_recognition.py     # 语音识别测试
└── test_rag_functionality.py     # RAG功能测试
```

## 日志和监控结构

### 日志组织
```
backend/logs/            # 后端日志
├── application.log      # 应用日志
├── error.log            # 错误日志
└── access.log           # 访问日志

custom_reports/          # 自定义报告
实施总结/                # 实施总结文档
```

## 文件命名约定

### 后端文件命名
- Python文件：使用snake_case，如`user_service.py`
- 配置文件：使用snake_case，如`config_simple.py`
- 测试文件：以`test_`开头，如`test_auth.py`

### 前端文件命名
- Vue组件：使用PascalCase，如`UserProfile.vue`
- JavaScript工具：使用camelCase，如`httpRequest.js`
- 样式文件：使用kebab-case，如`user-profile.scss`

### 通用文件命名
- 文档文件：使用大写开头，如`README.md`
- 配置文件：使用小写，如`docker-compose.yml`
- 脚本文件：使用小写，如`setup.bat`


