import{_ as B,B as V,V as N,h as c,r as s,a as v,c as A,b as a,d as o,w as t,z as M,x as k,E as u,f as p,t as h}from"./index-2c134546.js";import{u as E}from"./auth-e6295339.js";const L={name:"SpeakerRecognition",components:{UploadFilled:V,Loading:N},setup(){const w=E(),l=c(),R=c([]),e=c(!1),d=c(0),_=c(null),b="http://localhost:8002/api/audio/speaker-recognition",f={Authorization:`Bearer ${w.token}`};return{uploadRef:l,fileList:R,loading:e,uploadProgress:d,recognitionResult:_,uploadUrl:b,uploadHeaders:f,beforeUpload:n=>{const r=["audio/wav","audio/mpeg","audio/mp4","audio/flac"].includes(n.type),m=n.size/1024/1024<100;return r?m?(e.value=!0,d.value=0,_.value=null,!0):(u.error("文件大小不能超过 100MB!"),!1):(u.error("只支持 WAV、MP3、M4A、FLAC 格式的音频文件!"),!1)},handleUploadSuccess:(n,r)=>{e.value=!1,d.value=100,n.success?(_.value=n.data,u.success("说话人识别完成!")):u.error(n.message||"识别失败")},handleUploadError:(n,r)=>{e.value=!1,d.value=0,u.error("上传失败: "+n.message)},getConfidenceColor:n=>n>=.8?"#67c23a":n>=.6?"#e6a23c":"#f56c6c"}}},P={class:"speaker-recognition"},z={class:"content-container"},D={class:"result-content"},F={class:"speaker-info"},H={key:0,class:"speakers-list"},I={class:"loading-content"};function T(w,l,R,e,d,_){const b=s("upload-filled"),f=s("el-icon"),C=s("el-upload"),g=s("el-card"),i=s("el-descriptions-item"),U=s("el-tag"),n=s("el-descriptions"),r=s("el-table-column"),m=s("el-progress"),S=s("el-table"),x=s("loading");return v(),A("div",P,[l[7]||(l[7]=a("div",{class:"page-header"},[a("h1",null,"说话人识别"),a("p",null,"上传音频文件进行说话人识别和分析")],-1)),a("div",z,[o(g,{class:"upload-card",shadow:"hover"},{header:t(()=>l[0]||(l[0]=[a("div",{class:"card-header"},[a("span",null,"音频文件上传")],-1)])),default:t(()=>[o(C,{ref:"uploadRef",class:"upload-demo",drag:"",action:e.uploadUrl,headers:e.uploadHeaders,"before-upload":e.beforeUpload,"on-success":e.handleUploadSuccess,"on-error":e.handleUploadError,"file-list":e.fileList,accept:".wav,.mp3,.m4a,.flac",limit:1},{tip:t(()=>l[1]||(l[1]=[a("div",{class:"el-upload__tip"}," 支持 WAV、MP3、M4A、FLAC 格式，文件大小不超过 100MB ",-1)])),default:t(()=>[o(f,{class:"el-icon--upload"},{default:t(()=>[o(b)]),_:1}),l[2]||(l[2]=a("div",{class:"el-upload__text"},[p(" 将音频文件拖到此处，或"),a("em",null,"点击上传")],-1))]),_:1,__:[2]},8,["action","headers","before-upload","on-success","on-error","file-list"])]),_:1}),e.recognitionResult?(v(),M(g,{key:0,class:"result-card",shadow:"hover"},{header:t(()=>l[3]||(l[3]=[a("div",{class:"card-header"},[a("span",null,"识别结果")],-1)])),default:t(()=>[a("div",D,[a("div",F,[l[4]||(l[4]=a("h3",null,"说话人信息",-1)),o(n,{column:2,border:""},{default:t(()=>[o(i,{label:"说话人数量"},{default:t(()=>[p(h(e.recognitionResult.speakerCount||"N/A"),1)]),_:1}),o(i,{label:"音频时长"},{default:t(()=>[p(h(e.recognitionResult.duration||"N/A"),1)]),_:1}),o(i,{label:"采样率"},{default:t(()=>[p(h(e.recognitionResult.sampleRate||"N/A"),1)]),_:1}),o(i,{label:"处理状态"},{default:t(()=>[o(U,{type:e.recognitionResult.status==="completed"?"success":"warning"},{default:t(()=>[p(h(e.recognitionResult.status==="completed"?"已完成":"处理中"),1)]),_:1},8,["type"])]),_:1})]),_:1})]),e.recognitionResult.speakers?(v(),A("div",H,[l[5]||(l[5]=a("h3",null,"说话人详情",-1)),o(S,{data:e.recognitionResult.speakers,style:{width:"100%"}},{default:t(()=>[o(r,{prop:"id",label:"说话人ID",width:"120"}),o(r,{prop:"segments",label:"语音段数",width:"120"}),o(r,{prop:"totalDuration",label:"总时长",width:"120"}),o(r,{prop:"confidence",label:"置信度",width:"120"},{default:t(y=>[o(m,{percentage:Math.round(y.row.confidence*100),color:e.getConfidenceColor(y.row.confidence)},null,8,["percentage","color"])]),_:1})]),_:1},8,["data"])])):k("",!0)])]),_:1})):k("",!0),e.loading?(v(),M(g,{key:1,class:"loading-card",shadow:"hover"},{default:t(()=>[a("div",I,[o(f,{class:"loading-icon"},{default:t(()=>[o(x)]),_:1}),l[6]||(l[6]=a("p",null,"正在处理音频文件，请稍候...",-1)),o(m,{percentage:e.uploadProgress},null,8,["percentage"])])]),_:1})):k("",!0)])])}const q=B(L,[["render",T],["__scopeId","data-v-e884c038"]]);export{q as default};
