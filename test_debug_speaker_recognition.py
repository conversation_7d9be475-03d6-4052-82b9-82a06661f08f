#!/usr/bin/env python3
"""
调试说话人识别的详细日志测试
"""

import sys
import os
import time
import json
import logging
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('debug_speaker_recognition.log', encoding='utf-8')
    ]
)

def test_debug_speaker_recognition():
    """测试说话人识别的详细调试信息"""
    
    try:
        from backend.tasks.audio_processing_tasks import meeting_transcription_task
        
        # 测试音频文件
        test_audio = "resource/对话.mp3"
        if not os.path.exists(test_audio):
            print(f"⚠️ 测试音频不存在: {test_audio}")
            return False
        
        print("🔍 开始调试说话人识别...")
        print(f"🎵 音频文件: {test_audio}")
        
        # 任务参数
        task_id = f"debug_meeting_transcription_{int(time.time())}"
        user_id = "admin"
        file_ids = ["debug_test"]
        
        # 配置参数 - 使用最优化的设置
        config = {
            # 混合分段策略配置
            'segmentation_strategy': 'hybrid',
            'time_window_size': 4.0,
            'min_segments_required': 3,
            'force_split_threshold': 8,
            'merge_length_s': 5,  # 优化为5秒
            
            # 说话人识别配置
            'expected_speakers': 2,
            'similarity_threshold': 0.1,  # 极严格阈值
            'clustering_method': 'auto',
            'min_segment_duration': 0.5,
            
            # 调试配置
            'debug_mode': True,
            'verbose_logging': True
        }
        
        print(f"⚙️ 配置参数:")
        print(json.dumps(config, indent=2, ensure_ascii=False))
        
        # 创建临时文件映射
        import tempfile
        import shutil
        temp_dir = tempfile.mkdtemp()
        temp_audio_path = os.path.join(temp_dir, "debug_test.mp3")
        shutil.copy2(test_audio, temp_audio_path)
        
        # 模拟文件路径获取函数
        def mock_get_audio_file_path(user_id, file_id):
            if file_id == "debug_test":
                return temp_audio_path
            return None
        
        # 临时替换文件路径获取函数
        import backend.tasks.audio_processing_tasks as apt
        original_get_path = apt._get_audio_file_path
        apt._get_audio_file_path = mock_get_audio_file_path
        
        try:
            print("\n🚀 开始执行会议转录任务...")
            start_time = time.time()
            
            # 执行会议转录任务
            result = meeting_transcription_task(
                task_id=task_id,
                user_id=user_id,
                file_ids=file_ids,
                language="auto",
                output_format="detailed",
                include_timestamps=True,
                speaker_labeling=True,
                expected_speakers=2,
                similarity_threshold=0.1,
                clustering_method="auto",
                config=config
            )
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            print(f"\n⏱️ 处理耗时: {processing_time:.2f}秒")
            print("\n" + "=" * 80)
            print("📊 转录结果:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            
            # 分析结果
            if result and result.get('status') == 'success':
                results = result.get('results', [])
                if results:
                    first_result = results[0]
                    transcription_result = first_result.get('result', {})
                    
                    # 检查说话人识别结果
                    speaker_count = transcription_result.get('speaker_count', 0)
                    speaker_segments = transcription_result.get('speaker_segments', [])
                    
                    print(f"\n🎯 说话人识别结果分析:")
                    print(f"  - 识别出的说话人数量: {speaker_count}")
                    print(f"  - 期望的说话人数量: 2")
                    print(f"  - 说话人分布: {speaker_segments}")
                    
                    if speaker_count == 2:
                        print("✅ 说话人识别成功！")
                        return True
                    else:
                        print("❌ 说话人识别失败 - 未能识别出2个说话人")
                        return False
                else:
                    print("❌ 没有转录结果")
                    return False
            else:
                print(f"❌ 会议转录任务失败: {result}")
                return False
                
        finally:
            # 恢复原始函数
            apt._get_audio_file_path = original_get_path
            # 清理临时文件
            shutil.rmtree(temp_dir, ignore_errors=True)
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

if __name__ == "__main__":
    print("🔍 开始调试说话人识别系统...")
    success = test_debug_speaker_recognition()
    
    if success:
        print("\n✅ 调试测试完成 - 说话人识别成功")
    else:
        print("\n❌ 调试测试完成 - 说话人识别失败")
        print("\n📋 请查看详细日志文件: debug_speaker_recognition.log")
    
    print("\n🔍 调试信息已保存到日志文件中，请查看详细的嵌入向量和聚类分析")
