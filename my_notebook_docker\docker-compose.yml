version: '3.8'

# ===========================================
# 语音处理智能平台 Docker Compose 配置
# 解决向量维度不匹配问题的完整部署方案
# ===========================================

services:
  # ===========================================
  # Frontend 服务 - Vue.js + Nginx
  # ===========================================
  frontend:
    image: mynotebook-frontend:20250719
    container_name: speech_platform_frontend
    restart: unless-stopped
    ports:
      - "${FRONTEND_PORT:-3000}:80"
    environment:
      - NGINX_PORT=${NGINX_PORT:-80}
      - NGINX_WORKER_PROCESSES=${NGINX_WORKER_PROCESSES:-auto}
      - NGINX_WORKER_CONNECTIONS=${NGINX_WORKER_CONNECTIONS:-1024}
      - NGINX_CLIENT_MAX_BODY_SIZE=${NGINX_CLIENT_MAX_BODY_SIZE:-100M}
      - FRONTEND_API_PROXY_HOST=${FRONTEND_API_PROXY_HOST:-backend}
      - FRONTEND_API_PROXY_PORT=${FRONTEND_API_PROXY_PORT:-8002}
    depends_on:
      backend:
        condition: service_healthy
    networks:
      - speech_platform_network
    healthcheck:
      test: ["CMD", "/usr/local/bin/health-check.sh"]
      interval: 30s
      timeout: 10s
      start_period: 15s
      retries: 3

  # ===========================================
  # Redis 服务 - 任务队列存储
  # ===========================================
  redis:
    image: redis:7.2-alpine
    container_name: speech_platform_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - speech_platform_network

  # ===========================================
  # Backend 服务 - FastAPI应用
  # ===========================================
  backend:
    image: mynotebook-backend:20250721
    container_name: speech_platform_backend
    restart: unless-stopped
    ports:
      - "${BACKEND_PORT:-8002}:${BACKEND_PORT:-8002}"
    volumes:
      # 数据持久化 - 关键配置
      - ./volumes/data:/app/data
      - ./volumes/logs:/app/logs
      - D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\models:/app/models
      - ./volumes/uploads:/app/data/uploads
      # AI库缓存挂载 - 避免重复下载
      - ./volumes/cache/huggingface:/root/.cache/huggingface
      - ./volumes/cache/torch:/root/.cache/torch
      - ./volumes/cache/nltk_data:/root/nltk_data
      - ./volumes/cache/llama_index:/root/.cache/llama_index
      # 配置文件映射
      - ../backend:/app/backend:ro
      - ../config:/app/config:ro
      - ../utils:/app/utils:ro
    environment:
      # 从.env文件加载环境变量
      - BACKEND_HOST=${BACKEND_HOST:-0.0.0.0}
      - BACKEND_PORT=${BACKEND_PORT:-8002}
      - BACKEND_DEBUG=${BACKEND_DEBUG:-false}
      - DATABASE_URL=${DATABASE_URL:-sqlite:///./data/speech_platform.db}
      - OLLAMA_BASE_URL=${OLLAMA_BASE_URL:-http://host.docker.internal:11434}
      - EMBEDDING_MODEL=${EMBEDDING_MODEL:-nomic-embed-text:latest}
      - CHROMADB_PATH=${CHROMADB_PATH:-/app/data/chroma_db}
      - CHROMADB_COLLECTION_NAME=${CHROMADB_COLLECTION_NAME:-knowledge_base}
      - REDIS_URL=${REDIS_URL:-redis://redis:6379/0}
      - CELERY_BROKER_URL=${CELERY_BROKER_URL:-redis://redis:6379/0}
      - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND:-redis://redis:6379/0}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - LOG_FILE_PATH=${LOG_FILE_PATH:-/app/logs/app.log}
      - DOCKER_ENV=true
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
    depends_on:
      redis:
        condition: service_healthy
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      - speech_platform_network
    healthcheck:
      test: ["CMD", "/app/health_check.sh"]
      interval: 30s
      timeout: 30s
      start_period: 60s
      retries: 3

  # ===========================================
  # Celery Worker 服务 - 向量化任务处理
  # ===========================================
  celery-worker:
    image: mynotebook-celery:20250721
    container_name: speech_platform_celery
    restart: unless-stopped
    volumes:
      # 共享数据卷 - 确保与Backend访问相同的数据
      - ./volumes/data:/app/data
      - ./volumes/logs:/app/logs
      - D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\models:/app/models
      - ./volumes/uploads:/app/data/uploads
      # AI库缓存挂载 - 与Backend共享缓存
      - ./volumes/cache/huggingface:/root/.cache/huggingface
      - ./volumes/cache/torch:/root/.cache/torch
      - ./volumes/cache/nltk_data:/root/nltk_data
      - ./volumes/cache/llama_index:/root/.cache/llama_index
      # 配置文件映射
      - ../backend:/app/backend:ro
      - ../config:/app/config:ro
      - ../utils:/app/utils:ro
    environment:
      # 与Backend相同的关键环境变量
      - OLLAMA_BASE_URL=${OLLAMA_BASE_URL:-http://host.docker.internal:11434}
      - EMBEDDING_MODEL=${EMBEDDING_MODEL:-nomic-embed-text:latest}
      - CHROMADB_PATH=${CHROMADB_PATH:-/app/data/chroma_db}
      - CHROMADB_COLLECTION_NAME=${CHROMADB_COLLECTION_NAME:-knowledge_base}
      - DATABASE_URL=${DATABASE_URL:-sqlite:///./data/speech_platform.db}
      - CELERY_BROKER_URL=${CELERY_BROKER_URL:-redis://redis:6379/0}
      - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND:-redis://redis:6379/0}
      - MAX_WORKERS=${MAX_WORKERS:-4}
      - LOG_LEVEL=${LOG_LEVEL:-INFO}
      - LOG_FILE_PATH=${LOG_FILE_PATH:-/app/logs/celery.log}
      - DOCKER_ENV=true
      - PYTHONPATH=/app
      - PYTHONUNBUFFERED=1
      - C_FORCE_ROOT=1
    depends_on:
      redis:
        condition: service_healthy
      backend:
        condition: service_healthy
    extra_hosts:
      - "host.docker.internal:host-gateway"
    networks:
      - speech_platform_network
    healthcheck:
      test: ["CMD", "/app/celery_health_check.sh"]
      interval: 30s
      timeout: 30s
      start_period: 90s
      retries: 3

  # ===========================================
  # Celery Flower 监控 (可选)
  # ===========================================
  flower:
    image: mynotebook-celery:20250721
    container_name: speech_platform_flower
    restart: unless-stopped
    ports:
      - "5555:5555"
    volumes:
      - ./volumes/logs:/app/logs
    environment:
      - CELERY_BROKER_URL=${CELERY_BROKER_URL:-redis://redis:6379/0}
      - CELERY_RESULT_BACKEND=${CELERY_RESULT_BACKEND:-redis://redis:6379/0}
      - FLOWER_PORT=5555
      - FLOWER_BASIC_AUTH=${FLOWER_BASIC_AUTH:-admin:admin123}
    command: >
      bash -c "
        source /app/.venv/bin/activate &&
        cd /app/backend &&
        celery -A backend.celery_app flower --port=5555 --basic_auth=admin:admin123
      "
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - speech_platform_network
    profiles:
      - monitoring

# ===========================================
# 数据卷配置
# ===========================================
volumes:
  redis_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./volumes/redis_data

# ===========================================
# 网络配置
# ===========================================
networks:
  speech_platform_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
