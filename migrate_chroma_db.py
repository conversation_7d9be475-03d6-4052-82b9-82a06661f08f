#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ChromaDB数据库迁移脚本
将根目录的chroma_db数据迁移到data/chroma_db
"""

import os
import shutil
import sqlite3
from pathlib import Path

def migrate_chroma_database():
    """迁移ChromaDB数据库"""
    
    # 定义路径
    source_path = Path("./chroma_db")
    target_path = Path("./data/chroma_db")
    
    print("=" * 60)
    print("ChromaDB数据库迁移脚本")
    print("=" * 60)
    print(f"源路径: {source_path.absolute()}")
    print(f"目标路径: {target_path.absolute()}")
    print()
    
    # 检查源目录是否存在
    if not source_path.exists():
        print("❌ 源目录不存在，无需迁移")
        return False
    
    # 检查源目录是否有数据
    source_files = list(source_path.rglob("*"))
    if not source_files:
        print("❌ 源目录为空，无需迁移")
        return False
    
    print(f"📊 源目录包含 {len(source_files)} 个文件/目录")
    
    # 检查目标目录
    if target_path.exists():
        print("⚠️ 目标目录已存在")
        
        # 检查目标目录的数据
        target_files = list(target_path.rglob("*"))
        print(f"📊 目标目录包含 {len(target_files)} 个文件/目录")
        
        # 询问是否覆盖
        response = input("是否要覆盖目标目录？(y/N): ").strip().lower()
        if response != 'y':
            print("❌ 用户取消迁移")
            return False
        
        # 备份现有目标目录
        backup_path = target_path.parent / f"{target_path.name}_backup"
        if backup_path.exists():
            shutil.rmtree(backup_path)
        
        print(f"📦 备份现有目标目录到: {backup_path}")
        shutil.move(str(target_path), str(backup_path))
    
    try:
        # 确保目标父目录存在
        target_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 复制整个目录
        print("🚀 开始迁移数据...")
        shutil.copytree(str(source_path), str(target_path))
        
        print("✅ 数据迁移完成")
        
        # 验证迁移结果
        print("\n🔍 验证迁移结果...")
        
        # 检查SQLite数据库
        sqlite_file = target_path / "chroma.sqlite3"
        if sqlite_file.exists():
            try:
                conn = sqlite3.connect(str(sqlite_file))
                cursor = conn.cursor()
                
                # 检查集合表
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='collections'")
                if cursor.fetchone():
                    cursor.execute("SELECT id, name FROM collections")
                    collections = cursor.fetchall()
                    print(f"📚 找到 {len(collections)} 个集合:")
                    for coll_id, coll_name in collections:
                        print(f"   - {coll_name} (ID: {coll_id})")
                        
                        # 检查文档数量
                        cursor.execute("SELECT COUNT(*) FROM embeddings WHERE collection_id = ?", (coll_id,))
                        doc_count = cursor.fetchone()[0]
                        print(f"     文档数量: {doc_count}")
                
                conn.close()
                print("✅ SQLite数据库验证成功")
                
            except Exception as e:
                print(f"⚠️ SQLite数据库验证失败: {e}")
        
        # 检查向量索引文件
        index_dirs = [d for d in target_path.iterdir() if d.is_dir() and len(d.name) == 36]  # UUID格式
        print(f"📁 找到 {len(index_dirs)} 个向量索引目录")
        
        for index_dir in index_dirs[:3]:  # 只显示前3个
            index_files = list(index_dir.glob("*.bin"))
            print(f"   - {index_dir.name}: {len(index_files)} 个索引文件")
        
        if len(index_dirs) > 3:
            print(f"   ... 还有 {len(index_dirs) - 3} 个索引目录")
        
        print("\n🎉 迁移验证完成！")
        
        # 询问是否删除源目录
        print(f"\n📂 源目录仍然存在: {source_path}")
        response = input("是否删除源目录？(y/N): ").strip().lower()
        if response == 'y':
            shutil.rmtree(source_path)
            print("🗑️ 源目录已删除")
        else:
            print("📂 源目录保留，建议手动删除以避免混淆")
        
        return True
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        
        # 如果有备份，尝试恢复
        backup_path = target_path.parent / f"{target_path.name}_backup"
        if backup_path.exists():
            print("🔄 尝试恢复备份...")
            if target_path.exists():
                shutil.rmtree(target_path)
            shutil.move(str(backup_path), str(target_path))
            print("✅ 备份已恢复")
        
        return False

def test_new_path():
    """测试新路径的ChromaDB连接"""
    try:
        import chromadb
        
        print("\n🧪 测试新路径的ChromaDB连接...")
        
        client = chromadb.PersistentClient(path="./data/chroma_db")
        collections = client.list_collections()
        
        print(f"✅ 连接成功，找到 {len(collections)} 个集合:")
        for collection in collections:
            print(f"   - {collection.name}: {collection.count()} 个文档")
        
        return True
        
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

if __name__ == "__main__":
    print("开始ChromaDB数据库迁移...")
    
    success = migrate_chroma_database()
    
    if success:
        print("\n" + "=" * 60)
        print("迁移完成！")
        print("=" * 60)
        
        # 测试新路径
        test_new_path()
        
        print("\n📝 后续步骤:")
        print("1. 重启后端服务和Celery Worker")
        print("2. 测试RAG功能是否正常工作")
        print("3. 确认无问题后可删除根目录的chroma_db文件夹")
        
    else:
        print("\n❌ 迁移失败，请检查错误信息")
