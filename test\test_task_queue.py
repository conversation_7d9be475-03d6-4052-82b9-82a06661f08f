"""
测试任务队列系统
"""

import sys
import os
import time
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from backend.core.task_queue import get_task_manager
from backend.services.enhanced_progress_service import get_enhanced_progress_service


def test_task_manager():
    """测试任务管理器"""
    print("=== 测试任务管理器 ===")
    
    task_manager = get_task_manager()
    
    # 测试Redis连接
    try:
        task_manager.redis_client.ping()
        print("✅ Redis连接成功")
    except Exception as e:
        print(f"❌ Redis连接失败: {e}")
        return False
    
    return True


def test_progress_service():
    """测试进度服务"""
    print("\n=== 测试进度服务 ===")
    
    progress_service = get_enhanced_progress_service()
    
    # 创建测试任务
    task_id = "test_task_001"
    user_id = "test_user"
    
    try:
        # 创建任务
        progress = progress_service.create_task(
            task_id=task_id,
            title="测试任务",
            detail="这是一个测试任务",
            user_id=user_id,
            task_type="test"
        )
        print(f"✅ 创建任务成功: {task_id}")
        
        # 更新进度
        progress_service.update_progress(task_id, 25, "第一阶段完成")
        progress_service.update_progress(task_id, 50, "第二阶段完成")
        progress_service.update_progress(task_id, 75, "第三阶段完成")
        progress_service.update_progress(task_id, 100, "任务完成")
        
        # 获取进度
        final_progress = progress_service.get_progress(task_id)
        if final_progress and final_progress.percentage == 100:
            print("✅ 进度更新成功")
        else:
            print("❌ 进度更新失败")
            return False
        
        # 获取用户任务
        user_tasks = progress_service.get_user_tasks(user_id)
        if user_tasks and len(user_tasks) > 0:
            print("✅ 获取用户任务成功")
        else:
            print("❌ 获取用户任务失败")
            return False
        
        # 清理测试任务
        progress_service.remove_task(task_id, user_id)
        print("✅ 清理测试任务成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 进度服务测试失败: {e}")
        return False


def test_document_task():
    """测试文档处理任务"""
    print("\n=== 测试文档处理任务 ===")
    
    task_manager = get_task_manager()
    
    try:
        # 创建测试文件内容
        test_content = b"This is a test document content for testing the task queue system."
        
        # 提交文档处理任务
        task_id = task_manager.submit_document_processing_task(
            user_id="test_user",
            file_content=test_content,
            filename="test_document.txt",
            use_ocr=False,
            ocr_config=None
        )
        
        print(f"✅ 提交文档处理任务成功: {task_id}")
        
        # 监控任务状态
        max_wait = 30  # 最多等待30秒
        wait_time = 0
        
        while wait_time < max_wait:
            status = task_manager.get_task_status(task_id)
            print(f"任务状态: {status.get('state', 'UNKNOWN')}")
            
            if status.get('ready', False):
                if status.get('successful', False):
                    print("✅ 文档处理任务完成")
                    return True
                else:
                    print(f"❌ 文档处理任务失败: {status.get('result', 'Unknown error')}")
                    return False
            
            time.sleep(2)
            wait_time += 2
        
        print("❌ 文档处理任务超时")
        return False
        
    except Exception as e:
        print(f"❌ 文档处理任务测试失败: {e}")
        return False


def test_celery_workers():
    """测试Celery Worker状态"""
    print("\n=== 测试Celery Worker状态 ===")
    
    try:
        from backend.core.task_queue import celery_app
        
        # 检查活跃的Worker
        inspect = celery_app.control.inspect()
        active_workers = inspect.active()
        
        if active_workers:
            print("✅ 发现活跃的Worker:")
            for worker_name, tasks in active_workers.items():
                print(f"  - {worker_name}: {len(tasks)} 个活跃任务")
            return True
        else:
            print("❌ 没有发现活跃的Worker")
            print("请确保已启动Celery Worker进程")
            return False
            
    except Exception as e:
        print(f"❌ 检查Worker状态失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始测试任务队列系统...\n")
    
    tests = [
        ("任务管理器", test_task_manager),
        ("进度服务", test_progress_service),
        ("Celery Worker", test_celery_workers),
        ("文档处理任务", test_document_task),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试出错: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "="*50)
    print("测试结果汇总:")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！任务队列系统工作正常")
        return 0
    else:
        print("⚠️  部分测试失败，请检查系统配置")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
