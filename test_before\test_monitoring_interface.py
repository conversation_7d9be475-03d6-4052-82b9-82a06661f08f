#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控界面功能测试脚本
测试监控界面的各项功能和性能
"""

import sys
import os
import time
import requests
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_monitoring_interface():
    """测试监控界面功能"""
    print("=" * 60)
    print("开始监控界面功能测试")
    print("=" * 60)
    
    test_results = []
    
    # 测试1: 检查页面可访问性
    print("\n1. 测试页面可访问性...")
    try:
        response = requests.get("http://localhost:8504", timeout=10)
        if response.status_code == 200:
            print("✅ 页面可正常访问")
            test_results.append(("页面可访问性", True))
        else:
            print(f"❌ 页面访问失败: HTTP {response.status_code}")
            test_results.append(("页面可访问性", False))
    except Exception as e:
        print(f"❌ 页面连接失败: {str(e)}")
        test_results.append(("页面可访问性", False))
    
    # 测试2: 检查监控组件导入
    print("\n2. 测试监控组件导入...")
    try:
        from utils.monitoring_components import ProcessingMonitor, MonitoringComponents
        print("✅ 监控组件成功导入")
        test_results.append(("监控组件导入", True))
    except ImportError as e:
        print(f"❌ 监控组件导入失败: {str(e)}")
        test_results.append(("监控组件导入", False))
    
    # 测试3: 测试监控器基本功能
    print("\n3. 测试监控器基本功能...")
    try:
        from utils.monitoring_components import ProcessingMonitor
        
        monitor = ProcessingMonitor("test_monitor")
        monitor.start_monitoring()
        
        # 测试任务更新
        monitor.update_task("test_task", "running", 50.0, "test.wav")
        
        # 测试错误记录
        monitor.add_error("测试错误", "TEST_ERROR", "test_task")
        
        # 测试统计信息
        stats = monitor.get_stats()
        
        monitor.stop_monitoring()
        
        print("✅ 监控器基本功能正常")
        test_results.append(("监控器基本功能", True))
        
    except Exception as e:
        print(f"❌ 监控器功能测试失败: {str(e)}")
        test_results.append(("监控器基本功能", False))
    
    # 测试4: 测试进度跟踪
    print("\n4. 测试进度跟踪功能...")
    try:
        from utils.monitoring_components import ProcessingMonitor
        
        monitor = ProcessingMonitor("progress_test")
        monitor.start_monitoring()
        
        # 添加多个任务
        for i in range(3):
            monitor.update_task(f"task_{i}", "running", i*30.0, f"file_{i}.wav")
        
        stats = monitor.get_stats()
        if stats['total_tasks'] == 3:
            print("✅ 进度跟踪功能正常")
            test_results.append(("进度跟踪功能", True))
        else:
            print(f"❌ 进度跟踪异常: 期望3个任务，实际{stats['total_tasks']}个")
            test_results.append(("进度跟踪功能", False))
        
        monitor.stop_monitoring()
        
    except Exception as e:
        print(f"❌ 进度跟踪测试失败: {str(e)}")
        test_results.append(("进度跟踪功能", False))
    
    # 测试5: 测试错误处理
    print("\n5. 测试错误处理功能...")
    try:
        from utils.monitoring_components import ProcessingMonitor
        
        monitor = ProcessingMonitor("error_test")
        monitor.start_monitoring()
        
        # 添加不同类型错误
        error_types = ["PROCESSING_ERROR", "MEMORY_ERROR", "TIMEOUT_ERROR"]
        for error_type in error_types:
            monitor.add_error(f"测试{error_type}", error_type, "error_task")
        
        if len(monitor.error_log) == len(error_types):
            print("✅ 错误处理功能正常")
            test_results.append(("错误处理功能", True))
        else:
            print(f"❌ 错误处理异常: 期望{len(error_types)}个错误，实际{len(monitor.error_log)}个")
            test_results.append(("错误处理功能", False))
        
        monitor.stop_monitoring()
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {str(e)}")
        test_results.append(("错误处理功能", False))
    
    # 测试结果汇总
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    total_tests = len(test_results)
    passed_tests = sum(1 for _, success in test_results if success)
    failed_tests = total_tests - passed_tests
    
    print(f"总测试数: {total_tests}")
    print(f"通过: {passed_tests}")
    print(f"失败: {failed_tests}")
    print(f"通过率: {(passed_tests/total_tests)*100:.1f}%")
    
    # 列出失败的测试
    failed_names = [name for name, success in test_results if not success]
    if failed_names:
        print(f"\n失败的测试: {', '.join(failed_names)}")
    
    return passed_tests == total_tests

if __name__ == "__main__":
    success = test_monitoring_interface()
    sys.exit(0 if success else 1) 