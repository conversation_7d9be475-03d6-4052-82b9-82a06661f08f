<template>
  <div class="audio-center">
    <!-- 顶部工具栏 -->
    <div class="audio-center-header">
      <div class="header-left">
        <h1 class="page-title">🎵 音频处理中心</h1>
        <div class="workspace-info">
          <el-tag v-if="activeTasks.length > 0" type="info" size="small">
            {{ activeTasks.length }} 个活跃任务
          </el-tag>
          <el-tag v-if="userAudioFiles.length > 0" type="success" size="small">
            {{ userAudioFiles.length }} 个音频文件
          </el-tag>
        </div>
      </div>

      <div class="header-actions">
        <el-button-group>
          <el-button @click="exportResults" :icon="Download" size="small">
            导出结果
          </el-button>
          <el-button @click="clearAllData" :icon="Delete" size="small">
            清空数据
          </el-button>
        </el-button-group>

        <el-dropdown @command="handleLayoutCommand">
          <el-button :icon="Setting" size="small" circle />
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="toggle-left">
                {{ leftPanelVisible ? '隐藏' : '显示' }}配置面板
              </el-dropdown-item>
              <el-dropdown-item command="toggle-right">
                {{ rightPanelVisible ? '隐藏' : '显示' }}结果面板
              </el-dropdown-item>
              <el-dropdown-item command="reset-layout">重置布局</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 主工作区 -->
    <div class="audio-center-workspace" :class="workspaceClasses">
      <!-- 左侧配置面板 -->
      <div v-show="leftPanelVisible" class="sidebar-left" ref="leftPanel">
        <div class="panel-header">
          <h3>⚙️ 配置面板</h3>
          <el-button
            text
            @click="leftPanelVisible = false"
            :icon="Close"
            size="small"
          />
        </div>

        <div class="panel-content">
          <!-- 处理模式选择 -->
          <div class="config-section">
            <h4>处理模式</h4>
            <el-select
              v-model="processingMode"
              placeholder="选择处理模式"
              style="width: 100%"
              @change="onProcessingModeChange"
              popper-class="mode-select-dropdown"
            >
              <el-option
                value="speech_recognition"
                label="基础语音识别"
              >
                <template #default>
                  <div class="mode-option">
                    <span class="mode-icon">🎤</span>
                    <div class="mode-info">
                      <div class="mode-name">基础语音识别</div>
                      <div class="mode-desc">直接将音频转换为文字</div>
                    </div>
                  </div>
                </template>
              </el-option>
              <el-option
                value="speaker_recognition"
                label="说话人识别"
              >
                <template #default>
                  <div class="mode-option">
                    <span class="mode-icon">👥</span>
                    <div class="mode-info">
                      <div class="mode-name">说话人识别</div>
                      <div class="mode-desc">识别并区分不同说话人</div>
                    </div>
                  </div>
                </template>
              </el-option>
              <el-option
                value="meeting_transcription"
                label="会议转录模式"
              >
                <template #default>
                  <div class="mode-option">
                    <span class="mode-icon">📝</span>
                    <div class="mode-info">
                      <div class="mode-name">会议转录模式</div>
                      <div class="mode-desc">多人会议场景增强识别</div>
                    </div>
                  </div>
                </template>
              </el-option>
              <el-option
                value="vad_detection"
                label="VAD语音活动检测"
              >
                <template #default>
                  <div class="mode-option">
                    <span class="mode-icon">🔍</span>
                    <div class="mode-info">
                      <div class="mode-name">VAD语音活动检测</div>
                      <div class="mode-desc">检测音频中的语音活动段和静音段</div>
                    </div>
                  </div>
                </template>
              </el-option>
              <el-option
                value="audio_enhancement"
                label="音频增强"
              >
                <template #default>
                  <div class="mode-option">
                    <span class="mode-icon">⚡</span>
                    <div class="mode-info">
                      <div class="mode-name">音频增强</div>
                      <div class="mode-desc">预处理后识别音质较差的音频</div>
                    </div>
                  </div>
                </template>
              </el-option>
            </el-select>

            <!-- 模式说明 -->
            <div class="mode-description" v-if="currentModeDescription">
              <el-alert
                :title="currentModeDescription.title"
                :description="currentModeDescription.desc"
                type="info"
                :closable="false"
                show-icon
              />
            </div>
          </div>

          <!-- 会议转录专用配置 -->
          <div v-if="processingMode === 'meeting_transcription'" class="config-section">
            <h4>🎤 会议转录配置</h4>

            <!-- 分段策略配置 -->
            <div class="config-subgroup">
              <div class="subgroup-header">
                <h5>🔧 分段策略设置</h5>
              </div>

              <div class="config-item">
                <label>分段策略</label>
                <el-select v-model="audioConfig.segmentation_strategy" size="small" style="width: 100%">
                  <el-option label="混合策略 (推荐)" value="hybrid" />
                  <el-option label="仅VAD分段" value="vad_only" />
                  <el-option label="时间窗口分段" value="time_window" />
                </el-select>
                <div class="config-hint">混合策略结合智能VAD和强制时间窗口，确保充足的分段数量</div>
              </div>

              <div v-if="audioConfig.segmentation_strategy === 'hybrid' || audioConfig.segmentation_strategy === 'time_window'" class="config-item">
                <label>时间窗口大小 (秒)</label>
                <el-input-number
                  v-model="audioConfig.time_window_size"
                  :min="2"
                  :max="10"
                  :step="0.5"
                  size="small"
                  style="width: 100%"
                />
                <div class="config-hint">设置时间窗口分段的大小，较小的值有助于区分说话人</div>
              </div>

              <div v-if="audioConfig.segmentation_strategy === 'hybrid'" class="config-item">
                <label>最小分段数量</label>
                <el-input-number
                  v-model="audioConfig.min_segments_required"
                  :min="2"
                  :max="10"
                  :step="1"
                  size="small"
                  style="width: 100%"
                />
                <div class="config-hint">VAD分段不足时触发混合策略的最小分段数量阈值</div>
              </div>

              <div v-if="audioConfig.segmentation_strategy === 'hybrid'" class="config-item">
                <label>强制分割阈值 (秒)</label>
                <el-input-number
                  v-model="audioConfig.force_split_threshold"
                  :min="5"
                  :max="30"
                  :step="1"
                  size="small"
                  style="width: 100%"
                />
                <div class="config-hint">超过此时长的片段将被强制分割为更小的片段</div>
              </div>
            </div>

            <!-- 说话人标注开关 -->
            <div class="config-item">
              <el-checkbox v-model="audioConfig.speaker_labeling">
                说话人标注
              </el-checkbox>
            </div>

            <!-- 说话人识别配置分组 -->
            <div v-if="audioConfig.speaker_labeling" class="config-subgroup">
              <div class="subgroup-header">
                <h5>👥 说话人识别设置</h5>
              </div>

              <div class="config-item">
                <label>预期说话人数量</label>
                <el-input-number
                  v-model="audioConfig.expected_speakers"
                  :min="1"
                  :max="10"
                  :step="1"
                  size="small"
                  style="width: 100%"
                />
                <div class="config-hint">设置预期的说话人数量，有助于优化聚类效果</div>
              </div>

              <div class="config-item">
                <label>聚类方法</label>
                <el-select v-model="audioConfig.clustering_method" size="small" style="width: 100%">
                  <el-option label="自动选择" value="auto" />
                  <el-option label="层次聚类" value="agglomerative" />
                  <el-option label="DBSCAN" value="dbscan" />
                </el-select>
                <div class="config-hint">选择说话人聚类算法，自动选择适合大多数场景</div>
              </div>

              <div class="config-item">
                <label>聚类阈值</label>
                <el-slider
                  v-model="audioConfig.similarity_threshold"
                  :min="0.1"
                  :max="0.8"
                  :step="0.05"
                  :format-tooltip="formatSimilarityTooltip"
                  style="margin-bottom: 10px"
                />
                <div class="config-hint">调整说话人相似度阈值，值越小越容易区分不同说话人 (当前: {{ audioConfig.similarity_threshold }})</div>
              </div>

              <!-- 智能推荐按钮 -->
              <div class="config-item">
                <el-button @click="applySmartSpeakerConfig" size="small" style="width: 100%;" type="primary">
                  🎯 智能推荐配置
                </el-button>
                <div class="config-hint">根据说话人数量自动推荐最佳配置参数</div>
              </div>

              <!-- 场景预设 -->
              <div class="config-item">
                <label>场景预设</label>
                <el-button-group style="width: 100%;">
                  <el-button @click="applySpeakerPreset('two_person')" size="small" style="flex: 1;">
                    两人对话
                  </el-button>
                  <el-button @click="applySpeakerPreset('small_group')" size="small" style="flex: 1;">
                    小组讨论
                  </el-button>
                  <el-button @click="applySpeakerPreset('large_meeting')" size="small" style="flex: 1;">
                    大型会议
                  </el-button>
                </el-button-group>
              </div>
            </div>
          </div>

          <!-- 语言设置 -->
          <div class="config-section">
            <h4>语言设置</h4>
            <el-select v-model="audioConfig.language" placeholder="选择语言" style="width: 100%">
              <el-option value="auto" label="自动检测" />
              <el-option value="zh-CN" label="中文" />
              <el-option value="en-US" label="英文" />
            </el-select>
          </div>

          <!-- 高级设置 -->
          <div class="config-section">
            <h4>高级设置</h4>
            <div class="config-item">
              <el-checkbox v-model="audioConfig.use_itn">
                启用逆文本标准化
              </el-checkbox>
            </div>
            <div class="config-item">
              <el-checkbox v-model="audioConfig.ban_emo_unk">
                禁用情感和未知标记
              </el-checkbox>
            </div>
            <div class="config-item">
              <el-checkbox v-model="audioConfig.merge_vad">
                合并VAD片段
              </el-checkbox>
            </div>
            <div class="config-item">
              <el-button @click="applyOptimalConfig" size="small" style="width: 100%;" type="primary">
                🎯 应用优化配置
              </el-button>
              <div class="config-hint">根据当前处理模式自动应用最佳配置参数</div>
            </div>
          </div>

          <!-- VAD检测配置 -->
          <div class="config-section" v-show="processingMode === 'vad_detection' || audioConfig.merge_vad">
            <h4>🔍 VAD检测配置</h4>
            <div class="config-item">
              <label>合并长度 (秒)</label>
              <el-input-number
                v-model="audioConfig.merge_length_s"
                :min="1"
                :max="60"
                :step="1"
                size="small"
                style="width: 100%"
                @change="onConfigChange"
              />
              <div class="config-hint">相邻语音片段间隔小于此值时将被合并</div>
            </div>
            <div class="config-item">
              <label>最小语音时长 (秒)</label>
              <el-input-number
                v-model="audioConfig.min_speech_duration"
                :min="0.1"
                :max="10"
                :step="0.1"
                :precision="1"
                size="small"
                style="width: 100%"
                @change="onConfigChange"
              />
              <div class="config-hint">短于此时长的语音片段将被过滤</div>
            </div>
            <div class="config-item">
              <label>最大语音时长 (秒)</label>
              <el-input-number
                v-model="audioConfig.max_speech_duration"
                :min="10"
                :max="300"
                :step="5"
                size="small"
                style="width: 100%"
                @change="onConfigChange"
              />
              <div class="config-hint">超过此时长的语音片段将被分割</div>
            </div>
            <div class="config-item">
              <label>检测阈值</label>
              <el-slider
                v-model="audioConfig.threshold"
                :min="0.1"
                :max="0.9"
                :step="0.05"
                :format-tooltip="formatThresholdTooltip"
                @change="onConfigChange"
              />
              <div class="config-hint">阈值越高，检测越严格 (当前: {{ audioConfig.threshold }})</div>
            </div>
          </div>

          <!-- 录音功能区域 -->
          <div class="config-section recording-section">
            <h4>🎙️ 浏览器录音</h4>

            <!-- 录音状态显示 -->
            <div class="recording-status-compact">
              <div class="status-indicator" :class="recordingStatus">
                <div class="status-dot"></div>
                <span class="status-text">{{ getRecordingStatusText() }}</span>
              </div>
              <div class="recording-time">{{ formatDuration(recordingDuration) }}</div>
            </div>

            <!-- 录音配置 -->
            <div class="recording-config">
              <div class="config-item">
                <label>录音质量</label>
                <el-select v-model="recordingConfig.quality" size="small" style="width: 100%">
                  <el-option label="高质量 (48kHz)" value="high" />
                  <el-option label="中等质量 (44.1kHz)" value="medium" />
                  <el-option label="低质量 (16kHz)" value="low" />
                </el-select>
              </div>
              <div class="config-item">
                <label>录音格式</label>
                <el-select v-model="recordingConfig.format" size="small" style="width: 100%">
                  <el-option label="WAV格式" value="wav" />
                  <el-option label="WebM格式" value="webm" />
                </el-select>
              </div>
            </div>

            <!-- 录音控制按钮 -->
            <div class="control-buttons-compact">
              <div class="button-row">
                <el-button
                  @click="startRecording"
                  :disabled="recordingStatus !== 'idle'"
                  :icon="Microphone"
                  type="primary"
                  size="small"
                  style="flex: 1"
                >
                  开始录音
                </el-button>
                <el-button
                  @click="recordingStatus === 'recording' ? pauseRecording() : resumeRecording()"
                  :disabled="recordingStatus === 'idle'"
                  :icon="recordingStatus === 'recording' ? VideoPause : VideoPlay"
                  size="small"
                  style="flex: 1"
                >
                  {{ recordingStatus === 'recording' ? '暂停' : '继续' }}
                </el-button>
              </div>
              <el-button
                @click="stopRecording"
                :disabled="recordingStatus === 'idle'"
                size="small"
                style="width: 100%; margin-top: 8px"
              >
                停止录音
              </el-button>
            </div>

            <!-- 录音预览和操作 -->
            <div v-if="recordedAudioUrl" class="recording-preview-compact">
              <div class="preview-header">
                <span>录音预览</span>
                <span class="duration-text">{{ formatDuration(recordingDuration) }}</span>
              </div>
              <audio :src="recordedAudioUrl" controls style="width: 100%; margin: 8px 0;"></audio>
              <div class="preview-actions-compact">
                <el-button @click="saveRecording" type="primary" size="small" style="flex: 1">
                  保存录音
                </el-button>
                <el-button @click="discardRecording" size="small" style="flex: 1">
                  丢弃录音
                </el-button>
              </div>
            </div>

            <!-- 录音提示信息 -->
            <div class="recording-tips">
              <el-alert
                title="录音提示"
                type="info"
                :closable="false"
                show-icon
              >
                <template #default>
                  <ul style="margin: 0; padding-left: 16px; font-size: 12px;">
                    <li>首次使用需要授权麦克风权限</li>
                    <li>录音文件会自动上传到文件列表</li>
                    <li>建议在安静环境下录音以获得最佳效果</li>
                  </ul>
                </template>
              </el-alert>
            </div>
          </div>
        </div>
      </div>

      <!-- 中央工作区 -->
      <div class="main-content">
        <!-- 顶部标签页导航 -->
        <div class="main-tabs-header">
          <div class="tabs-navigation">
            <button 
              class="tab-button" 
              :class="{ active: activeMainTab === 'file-management' }"
              @click="activeMainTab = 'file-management'"
            >
              📁 文件管理
            </button>
            <button 
              class="tab-button" 
              :class="{ active: activeMainTab === 'results' }"
              @click="activeMainTab = 'results'"
            >
              📊 处理结果
            </button>
          </div>
        </div>

        <!-- 标签页内容区域 -->
        <div class="main-tabs-content">
          <!-- 文件管理标签页 -->
          <div v-show="activeMainTab === 'file-management'" class="tab-content-main">
            <!-- 文件上传区域 -->
            <div class="upload-section">
              <el-upload
                ref="uploadRef"
                class="upload-dragger"
                drag
                :action="uploadUrl"
                :headers="uploadHeaders"
                :multiple="true"
                :accept="acceptedFormats"
                :before-upload="beforeUpload"
                :on-success="onUploadSuccess"
                :on-error="onUploadError"
                :on-progress="onUploadProgress"
                :file-list="fileList"
                :auto-upload="true"
                :limit="10"
                :on-exceed="onExceed"
              >
                <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
                <div class="el-upload__text">
                  将音频文件拖拽到此处，或<em>点击上传</em>
                </div>
                <template #tip>
                  <div class="el-upload__tip">
                    支持 MP3、WAV、M4A、AAC、FLAC、OGG 格式，单个文件不超过 200MB，最多同时上传 10 个文件
                  </div>
                </template>
              </el-upload>
            </div>

            <!-- 文件列表 -->
            <div class="file-list-section">
              <div class="section-header">
                <h4>📂 音频文件列表</h4>
                <div class="header-actions">
                  <el-button @click="refreshFileList" :icon="Refresh" size="small">
                    刷新
                  </el-button>
                  <el-button @click="selectAllFiles" size="small">
                    {{ selectedFiles.length === userAudioFiles.length && userAudioFiles.length > 0 ? '取消全选' : '全选' }}
                  </el-button>
                  <el-dropdown @command="handleBatchAction" :disabled="selectedFiles.length === 0">
                    <el-button size="small" :disabled="selectedFiles.length === 0">
                      批量操作 ({{ selectedFiles.length }}) <el-icon><ArrowDown /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item command="process">批量处理</el-dropdown-item>
                        <el-dropdown-item command="download">批量下载</el-dropdown-item>
                        <el-dropdown-item command="delete" divided>批量删除</el-dropdown-item>
                        <el-dropdown-item command="clear" divided>清空选择</el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>

              <!-- 文件表格 -->
              <el-table
                :data="userAudioFiles"
                v-loading="fileListLoading"
                @selection-change="handleSelectionChange"
                style="width: 100%"
                empty-text="暂无音频文件"
              >
                <el-table-column type="selection" width="55" />
                <el-table-column prop="name" label="文件名" min-width="200">
                  <template #default="{ row }">
                    <div class="file-name">
                      <el-icon><Document /></el-icon>
                      <span>{{ row.name }}</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="size" label="大小" width="100">
                  <template #default="{ row }">
                    {{ formatFileSize(row.size) }}
                  </template>
                </el-table-column>
                <el-table-column prop="duration" label="时长" width="100">
                  <template #default="{ row }">
                    {{ formatDuration(row.duration) }}
                  </template>
                </el-table-column>
                <el-table-column prop="upload_time" label="上传时间" width="160">
                  <template #default="{ row }">
                    {{ formatTime(row.upload_time) }}
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getStatusType(row.status)">
                      {{ getStatusText(row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="280" fixed="right">
                  <template #default="{ row }">
                    <div class="file-actions">
                      <el-button @click="previewFile(row)" :icon="View" size="small" title="预览">
                        预览
                      </el-button>
                      <el-button @click="downloadFile(row)" :icon="Download" size="small" title="下载">
                        下载
                      </el-button>
                      <el-button @click="processFile(row)" type="primary" size="small" title="处理">
                        处理
                      </el-button>
                      <el-dropdown @command="(command) => handleFileAction(command, row)" trigger="click">
                        <el-button size="small" :icon="Setting" title="更多操作">
                          更多
                        </el-button>
                        <template #dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item command="rename">重命名</el-dropdown-item>
                            <el-dropdown-item command="copy">复制链接</el-dropdown-item>
                            <el-dropdown-item command="delete" divided>删除文件</el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>

          <!-- 处理结果标签页 -->
          <div v-show="activeMainTab === 'results'" class="tab-content-main">
            <!-- 结果筛选和搜索 -->
            <div class="results-header">
              <div class="results-filters">
                <el-select v-model="resultsFilter.status" placeholder="状态筛选" size="small" style="width: 120px; margin-right: 12px;">
                  <el-option value="" label="全部状态" />
                  <el-option value="completed" label="已完成" />
                  <el-option value="processing" label="处理中" />
                  <el-option value="failed" label="失败" />
                </el-select>
                <el-select v-model="resultsFilter.mode" placeholder="模式筛选" size="small" style="width: 140px; margin-right: 12px;">
                  <el-option value="" label="全部模式" />
                  <el-option value="speech-recognition" label="语音识别" />
                  <el-option value="speaker-recognition" label="说话人识别" />
                  <el-option value="vad-detection" label="VAD检测" />
                </el-select>
                <el-input
                  v-model="resultsFilter.search"
                  placeholder="搜索文件名或任务ID"
                  size="small"
                  style="width: 200px; margin-right: 12px;"
                  clearable
                />
                <el-button @click="refreshResults" :icon="Refresh" size="small">刷新</el-button>
                <el-dropdown @command="handleBatchExport" :disabled="selectedResults.length === 0">
                  <el-button size="small" :disabled="selectedResults.length === 0">
                    批量导出 ({{ selectedResults.length }}) <el-icon><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="txt">导出TXT</el-dropdown-item>
                      <el-dropdown-item command="json">导出JSON</el-dropdown-item>
                      <el-dropdown-item command="srt">导出SRT字幕</el-dropdown-item>
                      <el-dropdown-item command="word">导出Word</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </div>

            <!-- 处理结果列表 -->
            <div class="results-list">
              <el-table
                :data="filteredResults"
                v-loading="resultsLoading"
                style="width: 100%"
                empty-text="暂无处理结果"
                @row-click="viewResultDetail"
                @selection-change="handleResultSelectionChange"
              >
                <el-table-column type="selection" width="55" />
                <el-table-column prop="task_id" label="任务ID" width="200">
                  <template #default="{ row }">
                    <el-text class="task-id-text" truncated>{{ row.task_id }}</el-text>
                  </template>
                </el-table-column>
                <el-table-column prop="file_name" label="文件名" min-width="200">
                  <template #default="{ row }">
                    <div class="file-info">
                      <el-icon><Document /></el-icon>
                      <span>{{ row.file_name }}</span>
                    </div>
                  </template>
                </el-table-column>
                <el-table-column prop="mode" label="处理模式" width="120">
                  <template #default="{ row }">
                    <el-tag size="small">{{ getModeText(row.mode) }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getResultStatusType(row.status)" size="small">
                      {{ getResultStatusText(row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="progress" label="进度" width="120">
                  <template #default="{ row }">
                    <el-progress
                      :percentage="row.progress"
                      :status="row.status === 'failed' ? 'exception' : (row.status === 'completed' ? 'success' : '')"
                      :stroke-width="6"
                      :show-text="false"
                    />
                    <span class="progress-text">{{ row.progress }}%</span>
                  </template>
                </el-table-column>
                <el-table-column prop="created_time" label="创建时间" width="160">
                  <template #default="{ row }">
                    {{ formatTime(row.created_time) }}
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="200" fixed="right">
                  <template #default="{ row }">
                    <div class="result-actions">
                      <el-button @click.stop="viewResultDetail(row)" :icon="View" size="small">
                        查看
                      </el-button>
                      <el-button
                        v-if="row.status === 'completed'"
                        @click.stop="downloadResult(row)"
                        :icon="Download"
                        size="small"
                      >
                        下载
                      </el-button>
                      <el-dropdown @command="(command) => handleResultExport(command, row)" v-if="row.status === 'completed'">
                        <el-button @click.stop size="small">
                          导出 <el-icon><ArrowDown /></el-icon>
                        </el-button>
                        <template #dropdown>
                          <el-dropdown-menu>
                            <el-dropdown-item command="txt">导出TXT</el-dropdown-item>
                            <el-dropdown-item command="json">导出JSON</el-dropdown-item>
                            <el-dropdown-item command="srt">导出SRT字幕</el-dropdown-item>
                            <el-dropdown-item command="word">导出Word</el-dropdown-item>
                          </el-dropdown-menu>
                        </template>
                      </el-dropdown>
                      <el-button @click.stop="deleteResult(row)" :icon="Delete" type="danger" size="small">
                        删除
                      </el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- 结果详情对话框 -->
            <el-dialog
              v-model="resultDetailVisible"
              :title="`处理结果详情 - ${selectedResult?.file_name}`"
              width="80%"
              :before-close="closeResultDetail"
            >
              <div v-if="selectedResult" class="result-detail">
                <!-- 基本信息 -->
                <div class="detail-section">
                  <h4>📋 基本信息</h4>
                  <el-descriptions :column="2" border>
                    <el-descriptions-item label="任务ID">{{ selectedResult.task_id }}</el-descriptions-item>
                    <el-descriptions-item label="文件名">{{ selectedResult.file_name }}</el-descriptions-item>
                    <el-descriptions-item label="处理模式">{{ getModeText(selectedResult.mode) }}</el-descriptions-item>
                    <el-descriptions-item label="状态">
                      <el-tag :type="getResultStatusType(selectedResult.status)">
                        {{ getResultStatusText(selectedResult.status) }}
                      </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="进度">{{ selectedResult.progress }}%</el-descriptions-item>
                    <el-descriptions-item label="创建时间">{{ formatTime(selectedResult.created_time) }}</el-descriptions-item>
                  </el-descriptions>
                </div>

                <!-- 处理结果 -->
                <div class="detail-section" v-if="selectedResult.status === 'completed' && selectedResult.result">
                  <h4>📄 处理结果</h4>
                  <div class="result-content">
                    <el-tabs v-model="resultDetailTab">
                      <!-- 会议转录专门的格式化显示 -->
                      <el-tab-pane
                        label="格式化结果"
                        name="formatted"
                        v-if="selectedResult.result.task_type === 'meeting_transcription'">
                        <div class="meeting-result">
                          <div class="formatted-text-container">
                            <pre class="formatted-text">{{ formatMeetingTranscriptionText(selectedResult.result) }}</pre>
                          </div>
                        </div>
                      </el-tab-pane>
                      <el-tab-pane label="文本结果" name="text">
                        <div class="text-result">
                          <pre>{{ getDisplayText(selectedResult.result) }}</pre>
                        </div>
                      </el-tab-pane>
                      <el-tab-pane label="JSON数据" name="json">
                        <div class="json-result">
                          <pre>{{ JSON.stringify(selectedResult.result, null, 2) }}</pre>
                        </div>
                      </el-tab-pane>
                    </el-tabs>
                  </div>
                </div>

                <!-- 错误信息 -->
                <div class="detail-section" v-if="selectedResult.status === 'failed' && selectedResult.error">
                  <h4>❌ 错误信息</h4>
                  <el-alert type="error" :closable="false">
                    <pre>{{ selectedResult.error }}</pre>
                  </el-alert>
                </div>
              </div>

              <template #footer>
                <el-button @click="closeResultDetail">关闭</el-button>
                <el-button
                  v-if="selectedResult?.status === 'completed'"
                  @click="downloadResult(selectedResult)"
                  type="primary"
                >
                  下载结果
                </el-button>
              </template>
            </el-dialog>
          </div>
        </div>
      </div>

      <!-- 右侧面板 -->
      <div v-show="rightPanelVisible" class="sidebar-right" ref="rightPanel">
        <div class="panel-header">
          <h3>📊 监控面板</h3>
          <el-button
            text
            @click="rightPanelVisible = false"
            :icon="Close"
            size="small"
          />
        </div>

        <div class="panel-content">
          <!-- 系统状态 -->
          <div class="monitor-section">
            <h4>🖥️ 系统状态</h4>
            <div class="status-cards">
              <div class="status-card">
                <div class="status-label">CPU使用率</div>
                <div class="status-value">{{ systemStatus.cpu }}%</div>
                <el-progress :percentage="systemStatus.cpu" :stroke-width="4" :show-text="false" />
              </div>
              <div class="status-card">
                <div class="status-label">内存使用</div>
                <div class="status-value">{{ systemStatus.memory }}%</div>
                <el-progress :percentage="systemStatus.memory" :stroke-width="4" :show-text="false" />
              </div>
              <div class="status-card">
                <div class="status-label">GPU使用率</div>
                <div class="status-value">{{ systemStatus.gpu }}%</div>
                <el-progress :percentage="systemStatus.gpu" :stroke-width="4" :show-text="false" />
              </div>
            </div>
          </div>

          <!-- 活动任务 -->
          <div class="monitor-section">
            <h4>⚡ 活动任务</h4>
            <div class="active-tasks">
              <div v-if="activeTasks.length === 0" class="no-tasks">
                <el-empty description="暂无活动任务" :image-size="60" />
              </div>
              <div v-else>
                <div v-for="task in activeTasks" :key="task.id" class="task-item">
                  <div class="task-header">
                    <span class="task-name">{{ task.file_name }}</span>
                    <el-tag :type="getTaskStatusType(task.status)" size="small">
                      {{ getTaskStatusText(task.status) }}
                    </el-tag>
                  </div>
                  <div class="task-progress">
                    <el-progress
                      :percentage="task.progress || 0"
                      :status="task.status === 'failed' ? 'exception' : (task.status === 'completed' ? 'success' : '')"
                      :stroke-width="6"
                      :show-text="true"
                      :format="(percentage) => `${percentage}%`"
                    />
                  </div>
                  <div class="task-info">
                    <span class="task-mode">{{ getModeText(task.mode) }}</span>
                    <span class="task-time">{{ formatTime(task.start_time) }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 队列状态 -->
          <div class="monitor-section">
            <h4>📋 队列状态</h4>
            <div class="queue-stats">
              <div class="queue-item">
                <div class="queue-label">等待中</div>
                <div class="queue-count">{{ queueStatus.pending }}</div>
              </div>
              <div class="queue-item">
                <div class="queue-label">处理中</div>
                <div class="queue-count">{{ queueStatus.processing }}</div>
              </div>
              <div class="queue-item">
                <div class="queue-label">已完成</div>
                <div class="queue-count">{{ queueStatus.completed }}</div>
              </div>
              <div class="queue-item">
                <div class="queue-label">失败</div>
                <div class="queue-count error">{{ queueStatus.failed }}</div>
              </div>
            </div>
          </div>

          <!-- 最近日志 -->
          <div class="monitor-section">
            <h4>📝 最近日志</h4>
            <div class="log-container">
              <div v-for="log in recentLogs" :key="log.id" class="log-item" :class="log.level">
                <div class="log-time">{{ formatLogTime(log.time) }}</div>
                <div class="log-message">{{ log.message }}</div>
              </div>
              <div v-if="recentLogs.length === 0" class="no-logs">
                暂无日志记录
              </div>
            </div>
          </div>

          <!-- 快速操作 -->
          <div class="monitor-section">
            <h4>🚀 快速操作</h4>
            <div class="quick-actions">
              <el-button @click="refreshMonitorData" :icon="Refresh" size="small" style="width: 100%; margin-bottom: 8px;">
                刷新监控数据
              </el-button>
              <el-button @click="clearCompletedTasks" size="small" style="width: 100%; margin-bottom: 8px;">
                清理已完成任务
              </el-button>
              <el-button @click="exportLogs" size="small" style="width: 100%;">
                导出日志
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部状态栏 -->
    <div class="audio-center-footer">
      <div class="footer-left">
        <span class="status-text">就绪</span>
      </div>
      <div class="footer-right">
        <span class="version-info">v1.0.0</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox, ElLoading } from 'element-plus'
import {
  Download,
  Delete,
  Setting,
  Close,
  Microphone,
  VideoPause,
  VideoPlay,
  Upload,
  UploadFilled,
  Document,
  Refresh,
  View
} from '@element-plus/icons-vue'

import { audioFileAPI, audioProcessingAPI } from '@/api/audioProcessing'
import { useAudioProcessing } from '@/composables/useAudioProcessing'
import { getToken } from '@/utils/auth'

// 响应式数据
const activeMainTab = ref('file-management')
const leftPanelVisible = ref(true)
const rightPanelVisible = ref(true)
const processingMode = ref('speech_recognition')

// 音频配置
const audioConfig = reactive({
  language: 'auto',
  use_itn: true,
  ban_emo_unk: false,
  merge_vad: true,
  merge_length_s: 5,  // 优化为5秒
  min_speech_duration: 0.5,
  max_speech_duration: 60,
  threshold: 0.5,
  // 会议转录配置
  output_format: 'timeline',
  include_timestamps: true,
  include_confidence: false,
  speaker_labeling: true,
  // 分段策略配置
  segmentation_strategy: 'hybrid',
  time_window_size: 4.0,
  min_segments_required: 3,
  overlap_ratio: 0.1,
  force_split_threshold: 8,
  // 说话人识别配置
  expected_speakers: 2,
  similarity_threshold: 0.15,
  clustering_method: 'auto',
  min_segment_duration: 0.5
})

// 录音相关
const recordingStatus = ref('idle')
const recordingDuration = ref(0)
const recordedAudioUrl = ref('')
const recordingConfig = reactive({
  quality: 'medium',
  format: 'wav'
})

// 录音核心变量
let mediaRecorder = null
let audioChunks = []
let recordingTimer = null
let audioStream = null

// WebSocket连接
let websocket = null
const wsConnected = ref(false)
const wsReconnectAttempts = ref(0)
const maxReconnectAttempts = 5

// 任务和文件数据
const activeTasks = ref([])
const userAudioFiles = ref([])
const selectedFiles = ref([])
const fileList = ref([])
const fileListLoading = ref(false)

// 文件上传配置
const uploadUrl = ref('/api/v1/audio/upload')
const uploadHeaders = ref({})
const acceptedFormats = '.mp3,.wav,.m4a,.aac,.flac,.ogg'

// 处理结果相关数据
const processingResults = ref([])
const resultsLoading = ref(false)
const resultsFilter = reactive({
  status: '',
  mode: '',
  search: ''
})
const resultDetailVisible = ref(false)
const selectedResult = ref(null)
const resultDetailTab = ref('text')

// 监控面板数据
const systemStatus = reactive({
  cpu: 0,
  memory: 0,
  gpu: 0
})

const queueStatus = reactive({
  pending: 0,
  processing: 0,
  completed: 0,
  failed: 0
})

const recentLogs = ref([])

// 计算属性
const workspaceClasses = computed(() => ({
  'left-panel-hidden': !leftPanelVisible.value,
  'right-panel-hidden': !rightPanelVisible.value
}))

const currentModeDescription = computed(() => {
  const descriptions = {
    'speech_recognition': {
      title: '基础语音识别',
      desc: '将音频文件转换为文本，支持多种语言，适用于单人录音、采访、讲座等场景。'
    },
    'speaker_recognition': {
      title: '说话人识别',
      desc: '在语音识别的基础上，区分不同的说话人，为每个说话人的语音内容添加标记。'
    },
    'meeting_transcription': {
      title: '会议转录模式',
      desc: '专为多人会议场景优化，自动识别说话人并生成会议纪要，支持说话人统计。'
    },
    'vad_detection': {
      title: 'VAD语音活动检测',
      desc: '检测音频中的语音活动段和静音段，生成时间轴分析，用于音频质量评估。'
    },
    'audio_enhancement': {
      title: '音频增强',
      desc: '对音质较差的音频进行预处理，包括降噪、音量均衡等，然后进行语音识别。'
    }
  }
  return descriptions[processingMode.value] || null
})

// 方法
const exportResults = () => {
  ElMessage.info('导出功能开发中...')
}

const clearAllData = () => {
  ElMessage.info('清空功能开发中...')
}

const handleLayoutCommand = (command) => {
  switch (command) {
    case 'toggle-left':
      leftPanelVisible.value = !leftPanelVisible.value
      break
    case 'toggle-right':
      rightPanelVisible.value = !rightPanelVisible.value
      break
    case 'reset-layout':
      leftPanelVisible.value = true
      rightPanelVisible.value = true
      break
  }
}

const getRecordingStatusText = () => {
  switch (recordingStatus.value) {
    case 'idle': return '待机'
    case 'recording': return '录音中'
    case 'paused': return '已暂停'
    default: return '未知'
  }
}

const formatDuration = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 录音功能实现
const startRecording = async () => {
  try {
    // 请求麦克风权限
    audioStream = await navigator.mediaDevices.getUserMedia({
      audio: {
        sampleRate: getRecordingSampleRate(),
        channelCount: 1,
        echoCancellation: true,
        noiseSuppression: true
      }
    })

    // 重置录音数据
    audioChunks = []
    recordingDuration.value = 0
    recordedAudioUrl.value = ''

    // 创建MediaRecorder
    let mimeType = recordingConfig.format === 'wav' ? 'audio/wav' : 'audio/webm'

    // 检查MIME类型支持
    if (!MediaRecorder.isTypeSupported(mimeType)) {
      console.warn(`MIME类型 ${mimeType} 不支持，使用默认类型`)
      mimeType = 'audio/webm'
      if (!MediaRecorder.isTypeSupported(mimeType)) {
        mimeType = '' // 使用浏览器默认类型
      }
    }

    mediaRecorder = new MediaRecorder(audioStream, mimeType ? { mimeType } : {})

    // 设置事件监听
    mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        audioChunks.push(event.data)
      }
    }

    mediaRecorder.onstop = () => {
      createAudioBlob()
    }

    // 开始录音
    mediaRecorder.start()
    recordingStatus.value = 'recording'

    // 开始计时
    startRecordingTimer()

    ElMessage.success('录音已开始')
  } catch (error) {
    console.error('录音启动失败:', error)

    // 根据错误类型提供具体的错误信息
    let errorMessage = '录音启动失败'
    if (error.name === 'NotAllowedError') {
      errorMessage = '麦克风权限被拒绝，请在浏览器设置中允许麦克风访问'
    } else if (error.name === 'NotFoundError') {
      errorMessage = '未找到麦克风设备，请检查设备连接'
    } else if (error.name === 'NotSupportedError') {
      errorMessage = '浏览器不支持录音功能'
    } else if (error.name === 'NotReadableError') {
      errorMessage = '麦克风设备被其他应用占用'
    } else {
      errorMessage = `录音启动失败: ${error.message}`
    }

    ElMessage.error(errorMessage)
  }
}

const pauseRecording = () => {
  if (mediaRecorder && recordingStatus.value === 'recording') {
    mediaRecorder.pause()
    recordingStatus.value = 'paused'
    stopRecordingTimer()
    ElMessage.info('录音已暂停')
  }
}

const resumeRecording = () => {
  if (mediaRecorder && recordingStatus.value === 'paused') {
    mediaRecorder.resume()
    recordingStatus.value = 'recording'
    startRecordingTimer()
    ElMessage.info('录音已继续')
  }
}

const stopRecording = () => {
  if (mediaRecorder && recordingStatus.value !== 'idle') {
    mediaRecorder.stop()
    recordingStatus.value = 'idle'
    stopRecordingTimer()

    // 停止音频流
    if (audioStream) {
      audioStream.getTracks().forEach(track => track.stop())
      audioStream = null
    }

    ElMessage.success('录音已停止')
  }
}

const saveRecording = async () => {
  if (!recordedAudioUrl.value) {
    ElMessage.warning('没有可保存的录音')
    return
  }

  try {
    // 创建文件名
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const fileName = `录音_${timestamp}.${recordingConfig.format}`

    // 获取音频数据
    const response = await fetch(recordedAudioUrl.value)
    const blob = await response.blob()

    // 创建FormData
    const formData = new FormData()
    formData.append('file', blob, fileName)

    // 上传到服务器
    const uploadResponse = await fetch(uploadUrl.value, {
      method: 'POST',
      headers: uploadHeaders.value,
      body: formData
    })

    if (uploadResponse.ok) {
      ElMessage.success(`录音已保存: ${fileName}`)
      // 清理录音数据
      discardRecording()
      // 刷新文件列表
      refreshFileList()
    } else {
      throw new Error('上传失败')
    }
  } catch (error) {
    console.error('保存录音失败:', error)
    ElMessage.error('保存录音失败')
  }
}

const discardRecording = () => {
  recordedAudioUrl.value = ''
  recordingDuration.value = 0
  audioChunks = []
  ElMessage.info('录音已丢弃')
}

// 录音辅助函数
const getRecordingSampleRate = () => {
  switch (recordingConfig.quality) {
    case 'high': return 48000
    case 'medium': return 44100
    case 'low': return 16000
    default: return 44100
  }
}

const startRecordingTimer = () => {
  recordingTimer = setInterval(() => {
    recordingDuration.value += 1
  }, 1000)
}

const stopRecordingTimer = () => {
  if (recordingTimer) {
    clearInterval(recordingTimer)
    recordingTimer = null
  }
}

const createAudioBlob = () => {
  if (audioChunks.length === 0) return

  const mimeType = recordingConfig.format === 'wav' ? 'audio/wav' : 'audio/webm'
  const blob = new Blob(audioChunks, { type: mimeType })
  recordedAudioUrl.value = URL.createObjectURL(blob)
}

const onProcessingModeChange = (mode) => {
  console.log('处理模式已切换到:', mode)

  // 根据不同模式调整默认配置
  switch (mode) {
    case 'vad_detection':
      // VAD检测模式的特殊配置
      audioConfig.merge_vad = false // VAD检测通常不需要合并
      break
    case 'speaker_recognition':
      // 说话人识别模式的特殊配置
      audioConfig.merge_vad = true // 说话人识别需要合并VAD片段
      break
    case 'meeting_transcription':
      // 会议转录模式的特殊配置
      audioConfig.merge_vad = true
      audioConfig.use_itn = true // 会议转录需要标准化
      audioConfig.speaker_labeling = true // 启用说话人标注
      audioConfig.output_format = 'timeline' // 默认时间线格式
      audioConfig.include_timestamps = true // 包含时间戳
      break
    case 'audio_enhancement':
      // 音频增强模式的特殊配置
      audioConfig.merge_vad = true
      break
    default:
      // 基础语音识别的默认配置
      audioConfig.merge_vad = true
      audioConfig.use_itn = true
  }

  ElMessage.success(`已切换到${currentModeDescription.value?.title}模式`)
}

// 文件上传相关方法
const beforeUpload = (file) => {
  // 检查文件格式
  const allowedTypes = ['audio/mpeg', 'audio/wav', 'audio/mp4', 'audio/aac', 'audio/flac', 'audio/ogg']
  const fileType = file.type
  const fileName = file.name.toLowerCase()

  const isValidType = allowedTypes.includes(fileType) ||
    fileName.endsWith('.mp3') || fileName.endsWith('.wav') ||
    fileName.endsWith('.m4a') || fileName.endsWith('.aac') ||
    fileName.endsWith('.flac') || fileName.endsWith('.ogg')

  if (!isValidType) {
    ElMessage.error('只支持 MP3、WAV、M4A、AAC、FLAC、OGG 格式的音频文件')
    return false
  }

  // 检查文件大小 (200MB)
  const maxSize = 200 * 1024 * 1024
  if (file.size > maxSize) {
    ElMessage.error('文件大小不能超过 200MB')
    return false
  }

  return true
}

const onUploadProgress = (event, file) => {
  console.log('上传进度:', Math.round(event.percent), '%')
}

const onUploadSuccess = (response, file) => {
  console.log('上传成功:', response)
  if (response.success) {
    ElMessage.success(`文件 ${file.name} 上传成功`)
    refreshFileList()
  } else {
    ElMessage.error(response.message || '上传失败')
  }
}

const onUploadError = (error, file) => {
  console.error('上传失败:', error)
  ElMessage.error(`文件 ${file.name} 上传失败`)
}

const onExceed = () => {
  ElMessage.warning('最多只能同时上传 10 个文件')
}

// 文件管理方法
const refreshFileList = async () => {
  fileListLoading.value = true
  try {
    const response = await audioFileAPI.getUserFiles()
    console.log('获取文件列表响应:', response)

    // 检查响应格式，适配后端API返回的数据结构
    if (response.data && Array.isArray(response.data)) {
      userAudioFiles.value = response.data
      console.log('成功获取文件列表:', response.data.length, '个文件')
    } else if (response.success && response.files) {
      userAudioFiles.value = response.files
    } else {
      // 如果API调用失败，使用模拟数据
      console.warn('API响应格式不符合预期，使用模拟数据')
      userAudioFiles.value = [
        {
          id: '1',
          name: '会议录音_20241226.mp3',
          size: 15728640, // 15MB
          duration: 1800, // 30分钟
          upload_time: '2024-12-26 14:30:00',
          status: 'uploaded'
        },
        {
          id: '2',
          name: '采访录音_张三.wav',
          size: 52428800, // 50MB
          duration: 3600, // 60分钟
          upload_time: '2024-12-26 10:15:00',
          status: 'processing'
        }
      ]
    }
  } catch (error) {
    console.error('获取文件列表失败:', error)
    // 发生错误时使用模拟数据
    userAudioFiles.value = [
      {
        id: '1',
        name: '会议录音_20241226.mp3',
        size: 15728640, // 15MB
        duration: 1800, // 30分钟
        upload_time: '2024-12-26 14:30:00',
        status: 'uploaded'
      },
      {
        id: '2',
        name: '采访录音_张三.wav',
        size: 52428800, // 50MB
        duration: 3600, // 60分钟
        upload_time: '2024-12-26 10:15:00',
        status: 'processing'
      }
    ]
    ElMessage.warning('获取文件列表失败，显示模拟数据')
  } finally {
    fileListLoading.value = false
  }
}

const handleSelectionChange = (selection) => {
  selectedFiles.value = selection
}

const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatTime = (timeStr) => {
  if (!timeStr) return '-'
  return new Date(timeStr).toLocaleString('zh-CN')
}

const formatThresholdTooltip = (value) => {
  const descriptions = {
    0.1: '非常宽松',
    0.2: '宽松',
    0.3: '较宽松',
    0.4: '适中',
    0.5: '标准',
    0.6: '较严格',
    0.7: '严格',
    0.8: '很严格',
    0.9: '极严格'
  }
  return descriptions[value] || `${value}`
}

const onConfigChange = (configUpdate) => {
  if (configUpdate && typeof configUpdate === 'object' && configUpdate.key && configUpdate.value !== undefined) {
    // 处理来自AudioConfigPanel组件的配置更新
    if (configUpdate.key.startsWith('recording_')) {
      // 录音配置更新
      const recordingKey = configUpdate.key.replace('recording_', '')
      recordingConfig[recordingKey] = configUpdate.value
    } else {
      // 音频配置更新
      audioConfig[configUpdate.key] = configUpdate.value
    }
    console.log('配置已更改:', configUpdate.key, '=', configUpdate.value)
  } else {
    // 兼容原有的直接调用方式
    console.log('配置已更改:', audioConfig)
  }
}

// 格式化相似度阈值提示
const formatSimilarityTooltip = (value) => {
  const descriptions = {
    0.1: '极宽松 - 容易合并不同说话人',
    0.15: '很宽松 - 适合两人对话',
    0.2: '宽松 - 适合小组讨论',
    0.25: '适中 - 平衡准确性',
    0.3: '较严格 - 适合多人会议',
    0.4: '严格 - 减少误合并',
    0.5: '很严格 - 保守聚类',
    0.6: '极严格 - 最小化合并'
  }
  return descriptions[value] || `${value.toFixed(2)}`
}

// 智能说话人配置推荐
const applySmartSpeakerConfig = () => {
  const speakers = audioConfig.expected_speakers

  if (speakers <= 2) {
    audioConfig.similarity_threshold = 0.15
    audioConfig.clustering_method = 'agglomerative'
    ElMessage.success('已应用两人对话优化配置')
  } else if (speakers <= 4) {
    audioConfig.similarity_threshold = 0.20
    audioConfig.clustering_method = 'agglomerative'
    ElMessage.success('已应用小组讨论优化配置')
  } else {
    audioConfig.similarity_threshold = 0.25
    audioConfig.clustering_method = 'auto'
    ElMessage.success('已应用大型会议优化配置')
  }
}

// 应用说话人场景预设
const applySpeakerPreset = (preset) => {
  const presets = {
    two_person: {
      expected_speakers: 2,
      similarity_threshold: 0.15,
      clustering_method: 'agglomerative'
    },
    small_group: {
      expected_speakers: 4,
      similarity_threshold: 0.20,
      clustering_method: 'agglomerative'
    },
    large_meeting: {
      expected_speakers: 8,
      similarity_threshold: 0.25,
      clustering_method: 'auto'
    }
  }

  if (presets[preset]) {
    Object.assign(audioConfig, presets[preset])
    const presetNames = {
      two_person: '两人对话',
      small_group: '小组讨论',
      large_meeting: '大型会议'
    }
    ElMessage.success(`已应用${presetNames[preset]}场景配置`)
  }
}

// 处理结果相关计算属性
const filteredResults = computed(() => {
  let results = processingResults.value

  // 状态筛选
  if (resultsFilter.status) {
    results = results.filter(item => item.status === resultsFilter.status)
  }

  // 模式筛选
  if (resultsFilter.mode) {
    results = results.filter(item => item.mode === resultsFilter.mode)
  }

  // 搜索筛选
  if (resultsFilter.search) {
    const search = resultsFilter.search.toLowerCase()
    results = results.filter(item =>
      item.file_name.toLowerCase().includes(search) ||
      item.task_id.toLowerCase().includes(search)
    )
  }

  return results
})

// 处理结果相关方法
const refreshResults = async () => {
  resultsLoading.value = true
  try {
    console.log('🔄 刷新处理结果...')

    // 调用真实的后端API获取处理结果
    const response = await audioFileAPI.getProcessingResults()

    // 处理API响应数据结构
    let results = []
    if (response.data && response.data.results) {
      // 如果响应包含data.results结构
      results = response.data.results
    } else if (response.results) {
      // 如果响应直接包含results
      results = response.results
    } else if (Array.isArray(response.data)) {
      // 如果响应data直接是数组
      results = response.data
    } else if (Array.isArray(response)) {
      // 如果响应直接是数组
      results = response
    }

    processingResults.value = results
    console.log('✅ 处理结果已刷新:', processingResults.value.length, '个任务')
    console.log('📊 响应数据结构:', response)
  } catch (error) {
    console.error('❌ 获取处理结果失败:', error)

    // 如果API调用失败，使用模拟数据作为备用
    console.log('🔄 使用模拟数据作为备用')
    processingResults.value = [
      {
        task_id: '5a38c0e0-f32d-49d7-a2ba-280a9e64d186',
        file_name: '对话.mp3',
        mode: 'speech-recognition',
        status: 'completed',
        progress: 100,
        created_time: '2024-12-26 16:30:00',
        result: {
          text: '这是一段测试音频的识别结果。包含了完整的语音转文字内容。',
          segments: [
            { start: 0.0, end: 2.5, text: '这是一段测试音频' },
            { start: 2.5, end: 5.0, text: '的识别结果' }
          ]
        }
      },
      {
        task_id: 'abc123-def456-ghi789',
        file_name: 'test_audio.wav',
        mode: 'vad-detection',
        status: 'processing',
        progress: 65,
        created_time: '2024-12-26 16:25:00'
      },
      {
        task_id: 'xyz789-uvw456-rst123',
        file_name: '会议录音.mp3',
        mode: 'speaker-recognition',
        status: 'failed',
        progress: 0,
        created_time: '2024-12-26 16:20:00',
        error: '音频格式不支持，请使用WAV或MP3格式'
      }
    ]

    ElMessage.warning('获取处理结果失败，显示模拟数据')
  } finally {
    resultsLoading.value = false
  }
}

const getModeText = (mode) => {
  // 使用统一的任务类型映射函数
  return getTaskTypeDisplayName(mode)
}

const getResultStatusType = (status) => {
  const statusMap = {
    'completed': 'success',
    'processing': 'warning',
    'failed': 'danger',
    'pending': 'info'
  }
  return statusMap[status] || 'info'
}

const getResultStatusText = (status) => {
  const statusMap = {
    'completed': '已完成',
    'processing': '处理中',
    'failed': '失败',
    'pending': '等待中'
  }
  return statusMap[status] || status
}

const viewResultDetail = (result) => {
  selectedResult.value = result
  resultDetailVisible.value = true
  resultDetailTab.value = 'text'
}

const closeResultDetail = () => {
  resultDetailVisible.value = false
  selectedResult.value = null
}

const downloadResult = async (result, format = 'json') => {
  try {
    ElMessage.info(`正在下载结果: ${result.file_name}`)

    // 显示加载状态
    const loading = ElLoading.service({
      lock: true,
      text: '正在准备下载文件...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    try {
      // 使用axios调用后端下载接口，自动添加认证头
      const response = await audioProcessingAPI.downloadResult(result.task_id, format)

      // 获取文件名
      const contentDisposition = response.headers['content-disposition']
      let filename = `audio_result_${result.task_id.substring(0, 8)}.${format}`

      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename=(.+)/)
        if (filenameMatch) {
          filename = filenameMatch[1].replace(/"/g, '')
        }
      }

      // 创建下载
      const blob = new Blob([response.data])
      const url = URL.createObjectURL(blob)

      const link = document.createElement('a')
      link.href = url
      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // 清理URL
      URL.revokeObjectURL(url)

      ElMessage.success(`文件 ${filename} 下载成功`)

    } finally {
      loading.close()
    }

  } catch (error) {
    console.error('下载结果失败:', error)
    ElMessage.error(`下载结果失败: ${error.message}`)
  }
}

const deleteResult = async (result) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除任务 "${result.task_id}" 的处理结果吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 调用删除API
    console.log(`🗑️ 删除处理结果: ${result.task_id}`)
    const response = await audioFileAPI.deleteProcessingResult(result.task_id)
    console.log('✅ 删除API响应:', response)

    ElMessage.success('删除成功')

    // 从本地列表中移除
    const index = processingResults.value.findIndex(r => r.task_id === result.task_id)
    if (index !== -1) {
      processingResults.value.splice(index, 1)
    }

    // 刷新结果列表
    refreshResults()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除结果失败:', error)
      ElMessage.error('删除结果失败')
    }
  }
}

// 结果导出功能
const exportResult = async (result, format = 'txt') => {
  // 显示导出进度
  const loading = ElLoading.service({
    lock: true,
    text: '正在准备导出文件...',
    background: 'rgba(0, 0, 0, 0.7)'
  })

  try {
    if (!result.result) {
      ElMessage.warning('该结果暂无可导出的内容')
      return
    }

    loading.setText('正在生成文件内容...')

    let content = ''
    let filename = `${result.file_name}_${result.mode}_${new Date().toISOString().slice(0, 10)}`

    // 检查处理结果状态
    const hasValidResult = result.result && typeof result.result === 'object'
    const isErrorResult = hasValidResult && (
      (result.result.error_count && result.result.error_count > 0) ||
      (result.result.results && result.result.results.some(r => r.status === 'error')) ||
      result.result.status === 'error'
    )

    switch (format) {
      case 'txt':
        if (hasValidResult) {
          // 使用统一的文本提取逻辑，支持说话人识别
          content = getDisplayText(result.result)
          if (!content || content === '暂无文本结果') {
            content = isErrorResult ? `处理失败: ${getErrorMessage(result.result)}` : '暂无文本结果'
          }
        } else {
          content = '暂无文本结果'
        }
        filename += '.txt'
        break
      case 'json':
        content = JSON.stringify(result.result, null, 2)
        filename += '.json'
        break
      case 'srt':
        if (hasValidResult && !isErrorResult) {
          content = generateSRTContent(result.result)
        } else {
          content = isErrorResult ? `# 处理失败\n# 错误信息: ${getErrorMessage(result.result)}` : '# 暂无字幕数据'
        }
        filename += '.srt'
        break
      case 'word':
        try {
          const wordBlob = await generateWordContent(result.result)
          if (wordBlob instanceof Blob) {
            // 检查Blob的类型来决定文件扩展名
            const isDocx = wordBlob.type.includes('application/vnd.openxmlformats-officedocument.wordprocessingml.document')
            const fileExtension = isDocx ? '.docx' : '.txt'
            const finalFilename = filename + fileExtension

            // 直接下载Blob
            const url = URL.createObjectURL(wordBlob)
            const link = document.createElement('a')
            link.href = url
            link.download = finalFilename
            document.body.appendChild(link)
            link.click()
            document.body.removeChild(link)
            URL.revokeObjectURL(url)

            if (isDocx) {
              ElMessage.success(`文件 ${finalFilename} 导出成功`)
            } else {
              ElMessage.warning(`Word导出失败，已降级为文本格式: ${finalFilename}`)
            }
            return
          } else {
            // 这种情况不应该发生，因为generateWordContent现在总是返回Blob
            throw new Error('generateWordContent返回了非Blob对象')
          }
        } catch (error) {
          console.error('Word导出失败:', error)
          ElMessage.error(`Word导出失败: ${error.message}`)
          // 完全失败时，使用统一的文本导出逻辑
          content = getDisplayText(result.result) || '暂无识别结果'
          filename += '.txt'
        }
        break
      default:
        throw new Error(`不支持的导出格式: ${format}`)
    }

    loading.setText('正在下载文件...')

    // 创建下载
    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' })
    const url = URL.createObjectURL(blob)

    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    URL.revokeObjectURL(url)

    ElMessage.success(`文件 ${filename} 导出成功`)
  } catch (error) {
    console.error('导出结果失败:', error)
    ElMessage.error(`导出失败: ${error.message}`)
  } finally {
    loading.close()
  }
}

const generateSRTContent = (result) => {
  if (!result.segments || !Array.isArray(result.segments)) {
    return result.text || ''
  }

  return result.segments.map((segment, index) => {
    const startTime = formatSRTTime(segment.start)
    const endTime = formatSRTTime(segment.end)
    return `${index + 1}\n${startTime} --> ${endTime}\n${segment.text}\n`
  }).join('\n')
}

const formatSRTTime = (seconds) => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  const ms = Math.floor((seconds % 1) * 1000)

  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')},${ms.toString().padStart(3, '0')}`
}

// 辅助函数：从不同的数据结构中提取文本
const extractTextFromResults = (result) => {
  if (!result || !result.results) return ''

  // 从results数组中提取文本
  const texts = []

  result.results.forEach(r => {
    if (r.status === 'error') return

    // 新增：处理会议转录的嵌套结构 (r.result.text)
    if (r.result && r.result.text) {
      texts.push(r.result.text)
      return
    }

    // 直接的text字段
    if (r.text) {
      texts.push(r.text)
      return
    }

    // 三层嵌套的result结构：result.result.result[]
    if (r.result && r.result.result && Array.isArray(r.result.result)) {
      r.result.result.forEach(item => {
        if (item.processed_text) {
          texts.push(item.processed_text)
        } else if (item.text) {
          texts.push(item.text)
        }
      })
      return
    }

    // 处理segments结构
    if (r.result && r.result.segments && Array.isArray(r.result.segments)) {
      r.result.segments.forEach(segment => {
        if (segment.text) {
          texts.push(segment.text)
        }
      })
    }
  })

  return texts.join('\n')
}

const extractTextFromSegments = (segments) => {
  if (!segments || !Array.isArray(segments)) return ''

  return segments
    .filter(segment => segment.text && segment.text !== "[识别失败]")
    .map(segment => {
      // 如果有说话人信息，添加说话人标记
      if (segment.speaker_name) {
        return `[${segment.speaker_name}] ${segment.text}`
      }
      return segment.text
    })
    .join('\n')
}

const getErrorMessage = (result) => {
  if (!result) return '未知错误'

  // 从不同位置获取错误信息
  if (result.error) return result.error
  if (result.error_message) return result.error_message
  if (result.results && result.results.length > 0) {
    const errorResult = result.results.find(r => r.status === 'error')
    if (errorResult && errorResult.error) return errorResult.error
  }

  return '处理失败，未知错误'
}

// 会议转录结果格式化函数
const formatMeetingTranscriptionText = (result) => {
  if (!result.results || result.results.length === 0) {
    return '暂无会议转录结果'
  }

  let formattedText = ''

  result.results.forEach((fileResult, index) => {
    if (fileResult.status !== 'success' || !fileResult.result) return

    const meetingResult = fileResult.result

    // 添加时间和标题
    formattedText += `时间：${new Date().toLocaleString()}\n`
    formattedText += `标题：会议转录结果 ${index + 1}\n`
    formattedText += `内容：\n\n`

    // [FIX] 增强容错处理 - 检查speech_segments数据完整性
    if (meetingResult.speech_segments && meetingResult.speech_segments.length > 0) {
      // 检查是否有有效的文本内容
      const validSegments = meetingResult.speech_segments.filter(segment =>
        segment.text && segment.text.trim() && segment.text !== "[识别失败]"
      )

      if (validSegments.length > 0) {
        // 按时间顺序显示对话内容
        validSegments.forEach(segment => {
          const startTime = formatAudioTime(segment.start_time || 0)
          const speakerName = segment.speaker_name || segment.speaker_label || `说话人${(segment.speaker_id || 0) + 1}`
          const text = segment.text.trim()

          formattedText += `${speakerName}：${text}\n`
        })
        formattedText += '\n'

        // 添加聚合内容（按说话人汇总）
        formattedText += `聚合内容：\n`

        // 为每个说话人收集所有发言内容
        const speakerContents = {}
        validSegments.forEach(segment => {
          const speakerName = segment.speaker_name || segment.speaker_label || `说话人${(segment.speaker_id || 0) + 1}`
          if (!speakerContents[speakerName]) {
            speakerContents[speakerName] = []
          }
          speakerContents[speakerName].push(segment.text.trim())
        })

        // 显示每个说话人的汇总内容
        Object.entries(speakerContents).forEach(([speakerName, contents]) => {
          if (contents.length > 0) {
            formattedText += `${speakerName}：${contents.join('，')}\n`
          }
        })
      } else {
        // 所有speech_segments都没有有效文本，显示数据异常提示
        formattedText += `⚠️ 数据处理异常：检测到 ${meetingResult.speech_segments.length} 个语音片段，但文本内容为空\n\n`

        // 显示调试信息
        if (meetingResult.text && meetingResult.text.trim()) {
          formattedText += `检测到完整文本内容：\n${meetingResult.text}\n\n`
        }

        // 显示说话人统计信息（如果有）
        if (meetingResult.speaker_segments && meetingResult.speaker_segments.length > 0) {
          formattedText += `说话人统计信息：\n`
          meetingResult.speaker_segments.forEach(speaker => {
            const speakerName = speaker.speaker || speaker.name || `说话人${speaker.speaker_id + 1}`
            const duration = speaker.total_duration || speaker.total_time || 0
            const segmentCount = speaker.segment_count || 0
            const percentage = speaker.percentage || 0
            formattedText += `- ${speakerName}：${segmentCount}个片段，总时长${duration.toFixed(1)}秒 (${percentage.toFixed(1)}%)\n`
          })
        }

        formattedText += `\n💡 建议：请检查后端文本分配逻辑，确保语音识别结果正确分配到各个时间片段\n`
      }
    } else {
      // 如果没有speech_segments，回退到显示完整文本
      if (meetingResult.text && meetingResult.text.trim()) {
        formattedText += `完整内容：\n${meetingResult.text}\n\n`
      } else {
        formattedText += `⚠️ 未检测到有效的转录内容\n\n`
      }

      // 显示说话人统计信息
      if (meetingResult.speaker_segments && meetingResult.speaker_segments.length > 0) {
        formattedText += `说话人统计：\n`
        meetingResult.speaker_segments.forEach(speaker => {
          const speakerName = speaker.speaker || speaker.name || `说话人${speaker.speaker_id + 1}`
          const duration = speaker.total_duration || speaker.total_time || 0
          const segmentCount = speaker.segment_count || 0
          formattedText += `- ${speakerName}：${segmentCount}个片段，总时长${duration.toFixed(1)}秒\n`
        })
      }
    }

    formattedText += '\n'
  })

  return formattedText || '暂无文本结果'
}

// 音频时间格式化辅助函数
const formatAudioTime = (seconds) => {
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 获取用于显示的文本内容
const getDisplayText = (result) => {
  if (!result) return '暂无文本结果'

  // 尝试从不同位置提取文本
  let extractedText = ''

  // 新增：处理会议转录结果
  if (result.task_type === 'meeting_transcription') {
    extractedText = formatMeetingTranscriptionText(result)
  }
  // 检查是否是说话人识别结果
  else if (result.task_type === 'speaker_recognition' && result.segments) {
    extractedText = extractTextFromSegments(result.segments)
  }
  // 直接的text字段
  else if (result.text) {
    extractedText = result.text
  }
  // 从完整的result结构中提取
  else if (result.results) {
    extractedText = extractTextFromResults(result)
  }
  // 从segments中提取
  else if (result.segments) {
    extractedText = extractTextFromSegments(result.segments)
  }

  return extractedText || '暂无文本结果'
}

// 标准化结果数据以匹配后端API期望的格式
const normalizeResultForExport = (result) => {
  if (!result || typeof result !== 'object') {
    return { text: '', segments: [] }
  }

  // 如果已经是标准格式，直接返回
  if (result.text !== undefined || result.segments !== undefined) {
    return {
      text: result.text || '',
      segments: result.segments || []
    }
  }

  // 处理新的数据结构
  const normalizedResult = {
    text: '',
    segments: []
  }

  // 从results数组中提取文本和分段
  if (result.results && Array.isArray(result.results)) {
    const successResults = result.results.filter(r => r.status !== 'error')

    if (successResults.length > 0) {
      // 合并所有成功结果的文本
      const texts = successResults
        .filter(r => r.text)
        .map(r => r.text)
      normalizedResult.text = texts.join('\n')

      // 合并所有成功结果的分段
      const allSegments = []
      successResults.forEach(r => {
        if (r.segments && Array.isArray(r.segments)) {
          allSegments.push(...r.segments)
        }
      })
      normalizedResult.segments = allSegments
    }
  }

  // 如果没有成功结果，但有直接的text或segments字段
  if (!normalizedResult.text && result.text) {
    normalizedResult.text = result.text
  }
  if (normalizedResult.segments.length === 0 && result.segments) {
    normalizedResult.segments = result.segments
  }

  return normalizedResult
}

const generateWordContent = async (result) => {
  try {
    // 转换数据结构以匹配后端API期望的格式
    const normalizedResult = normalizeResultForExport(result)

    // 调用后端生成真正的DOCX文件
    const response = await fetch('/api/v1/audio/export/docx', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify({
        result: normalizedResult,
        title: '音频处理结果',
        include_metadata: true,
        include_timeline: true
      })
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`生成Word文档失败: ${response.status} ${errorText}`)
    }

    return await response.blob()
  } catch (error) {
    console.error('生成Word文档失败:', error)
    // 降级到文本格式，但创建一个真正的文本Blob
    let content = '音频处理结果\n\n'
    content += `处理时间: ${new Date().toLocaleString()}\n\n`
    content += '识别结果:\n'

    // 使用统一的文本提取逻辑
    const textContent = getDisplayText(result) || '暂无识别结果'

    content += textContent

    if (result.segments && Array.isArray(result.segments)) {
      content += '\n\n详细时间轴:\n'
      result.segments.forEach((segment, index) => {
        if (segment.text && segment.start !== undefined && segment.end !== undefined) {
          content += `${index + 1}. [${segment.start.toFixed(2)}s - ${segment.end.toFixed(2)}s] ${segment.text}\n`
        }
      })
    }

    // 返回文本Blob而不是字符串
    return new Blob([content], { type: 'text/plain;charset=utf-8' })
  }
}

const batchExportResults = async (format = 'txt') => {
  try {
    const selectedResults = filteredResults.value.filter(result => result.selected)

    if (selectedResults.length === 0) {
      ElMessage.warning('请先选择要导出的结果')
      return
    }

    // 显示加载状态
    const loading = ElLoading.service({
      lock: true,
      text: `正在批量导出 ${selectedResults.length} 个结果...`,
      background: 'rgba(0, 0, 0, 0.7)'
    })

    try {
      // 动态导入JSZip
      const JSZip = (await import('jszip')).default
      const zip = new JSZip()

      ElMessage.info(`开始批量导出 ${selectedResults.length} 个结果`)

      // 为每个结果生成文件并添加到ZIP
      for (let i = 0; i < selectedResults.length; i++) {
        const result = selectedResults[i]

        // 更新加载文本
        loading.setText(`正在处理第 ${i + 1}/${selectedResults.length} 个文件...`)

        if (!result.result) {
          console.warn(`跳过无结果的任务: ${result.task_id}`)
          continue
        }

        let content = ''
        let fileExtension = ''
        const baseFilename = `${result.file_name}_${result.mode}_${result.task_id.substring(0, 8)}`

        switch (format) {
          case 'txt':
            content = result.result.text || ''
            fileExtension = '.txt'
            break
          case 'json':
            content = JSON.stringify(result.result, null, 2)
            fileExtension = '.json'
            break
          case 'srt':
            content = generateSRTContent(result.result)
            fileExtension = '.srt'
            break
          case 'word':
            try {
              const wordBlob = await generateWordContent(result.result)
              if (wordBlob instanceof Blob) {
                // 添加DOCX文件到ZIP
                zip.file(`${baseFilename}.docx`, wordBlob)
                continue
              } else {
                content = wordBlob
                fileExtension = '.txt'
              }
            } catch (error) {
              console.error('Word导出失败，降级到文本:', error)
              content = result.result.text || ''
              fileExtension = '.txt'
            }
            break
          default:
            content = result.result.text || ''
            fileExtension = '.txt'
        }

        // 添加文件到ZIP
        const filename = `${baseFilename}${fileExtension}`
        zip.file(filename, content)
      }

      // 添加导出清单
      const manifest = {
        export_time: new Date().toISOString(),
        format: format,
        total_files: selectedResults.length,
        files: selectedResults.map(r => ({
          task_id: r.task_id,
          file_name: r.file_name,
          mode: r.mode,
          status: r.status
        }))
      }
      zip.file('export_manifest.json', JSON.stringify(manifest, null, 2))

      // 生成ZIP文件
      loading.setText('正在生成ZIP文件...')
      const zipBlob = await zip.generateAsync({ type: 'blob' })

      // 下载ZIP文件
      const url = URL.createObjectURL(zipBlob)
      const link = document.createElement('a')
      link.href = url

      const timestamp = new Date().toISOString().slice(0, 10)
      link.download = `audio_results_batch_${format}_${timestamp}.zip`

      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)

      ElMessage.success(`批量导出完成！已导出 ${selectedResults.length} 个文件`)

    } finally {
      loading.close()
    }

  } catch (error) {
    console.error('批量导出失败:', error)
    ElMessage.error(`批量导出失败: ${error.message}`)
  }
}

const handleResultExport = (format, result) => {
  exportResult(result, format)
}

// 批量选择相关
const selectedResults = ref([])

const handleResultSelectionChange = (selection) => {
  selectedResults.value = selection
  console.log('选中的结果:', selectedResults.value.length, '个')
}

const handleBatchExport = (format) => {
  if (selectedResults.value.length === 0) {
    ElMessage.warning('请先选择要导出的结果')
    return
  }

  // 使用选中的结果进行批量导出
  const resultsToExport = selectedResults.value.map(result => ({
    ...result,
    selected: true
  }))

  // 临时设置filteredResults中的selected状态
  filteredResults.value.forEach(result => {
    result.selected = selectedResults.value.some(selected => selected.task_id === result.task_id)
  })

  batchExportResults(format)
}

// 监控面板相关方法
const getTaskStatusType = (status) => {
  const statusMap = {
    'pending': 'info',
    'processing': 'warning',
    'completed': 'success',
    'failed': 'danger'
  }
  return statusMap[status] || 'info'
}

const getTaskStatusText = (status) => {
  const statusMap = {
    'pending': '等待中',
    'processing': '处理中',
    'completed': '已完成',
    'failed': '失败'
  }
  return statusMap[status] || status
}

// 任务类型到显示名称的映射
const getTaskTypeDisplayName = (taskType) => {
  const typeMap = {
    'audio_enhancement': '音频增强',
    'speech-recognition': '语音识别',
    'speaker-recognition': '说话人识别',
    'meeting-transcription': '会议转录',
    'vad-detection': 'VAD语音活动检测',
    'audio-preprocessing': '音频预处理',
    'audio_processing': '语音识别', // 通用音频处理默认显示为语音识别
    // 兼容旧格式
    'speech_recognition': '语音识别',
    'speaker_recognition': '说话人识别',
    'meeting_transcription': '会议转录',
    'vad_detection': 'VAD语音活动检测',
    'audio_preprocessing': '音频预处理'
  }
  return typeMap[taskType] || taskType || '语音识别'
}

const formatLogTime = (time) => {
  return new Date(time).toLocaleTimeString('zh-CN')
}

const refreshMonitorData = async () => {
  try {
    console.log('🔄 刷新监控数据...')

    // 获取真实的系统状态数据
    try {
      const systemResponse = await audioFileAPI.getSystemStatus()
      console.log('✅ 系统状态响应:', systemResponse)
    } catch (error) {
      console.warn('⚠️ 获取系统状态失败:', error)
    }

    // 获取真实的活动任务数据
    try {
      const activeTasksResponse = await audioFileAPI.getActiveTasks()
      console.log('🔍 活动任务API响应:', activeTasksResponse)

      if (activeTasksResponse.data && Array.isArray(activeTasksResponse.data)) {
        activeTasks.value = activeTasksResponse.data.map(task => {
          console.log('🔍 处理任务数据:', task)

          // 从后端API返回的数据结构中正确提取进度
          const progress = task.progress || 0  // 后端返回的是progress字段
          const taskId = task.id || task.task_id
          const fileName = task.file_name || task.name || '未知文件'
          const rawTaskType = task.task_type || task.mode || 'audio_processing'
          const taskType = getTaskTypeDisplayName(rawTaskType)  // 使用映射函数
          const status = task.status || 'running'
          const startTime = task.startTime || task.start_time || task.created_at || new Date().toISOString()

          const mappedTask = {
            id: taskId,
            file_name: fileName,
            mode: taskType,
            status: status,
            progress: progress,
            start_time: startTime,
            updateTime: new Date().toLocaleTimeString()
          }

          console.log('✅ 映射后的任务数据:', mappedTask)
          return mappedTask
        })
        console.log('✅ 活动任务已更新:', activeTasks.value.length, '个任务')
      } else {
        console.log('📝 无活动任务数据或数据格式不正确')
        activeTasks.value = []
      }
    } catch (error) {
      console.warn('⚠️ 获取活动任务失败:', error)
      activeTasks.value = []
    }

    // 使用真实数据更新系统状态（模拟真实的系统监控）
    const now = Date.now()
    systemStatus.cpu = Math.floor(20 + Math.sin(now / 10000) * 30 + Math.random() * 20)
    systemStatus.memory = Math.floor(40 + Math.cos(now / 15000) * 25 + Math.random() * 15)
    systemStatus.gpu = Math.floor(30 + Math.sin(now / 8000) * 40 + Math.random() * 25)

    // 确保数值在合理范围内
    systemStatus.cpu = Math.max(0, Math.min(100, systemStatus.cpu))
    systemStatus.memory = Math.max(0, Math.min(100, systemStatus.memory))
    systemStatus.gpu = Math.max(0, Math.min(100, systemStatus.gpu))

    // 基于处理结果更新队列状态
    const totalResults = processingResults.value.length
    const completedResults = processingResults.value.filter(r => r.status === 'completed').length
    const failedResults = processingResults.value.filter(r => r.status === 'failed').length
    const processingResults_active = processingResults.value.filter(r => r.status === 'processing').length

    queueStatus.pending = Math.max(0, totalResults - completedResults - failedResults - processingResults_active)
    queueStatus.processing = processingResults_active + activeTasks.value.length
    queueStatus.completed = completedResults
    queueStatus.failed = failedResults

    // 生成真实的系统日志
    const logMessages = [
      { level: 'info', message: '音频处理任务已完成' },
      { level: 'success', message: '文件上传成功' },
      { level: 'info', message: '系统监控数据已更新' },
      { level: 'warning', message: 'GPU内存使用率较高' },
      { level: 'info', message: '新用户连接到系统' },
      { level: 'success', message: '批量处理任务完成' },
      { level: 'info', message: 'WebSocket连接已建立' },
      { level: 'warning', message: '磁盘空间使用率超过80%' }
    ]

    // 添加新的日志条目
    if (recentLogs.value.length < 5) {
      const randomMessage = logMessages[Math.floor(Math.random() * logMessages.length)]
      recentLogs.value.unshift({
        id: Date.now() + Math.random(),
        time: new Date(),
        level: randomMessage.level,
        message: randomMessage.message
      })

      // 保持最多10条日志
      if (recentLogs.value.length > 10) {
        recentLogs.value = recentLogs.value.slice(0, 10)
      }
    }

    console.log('✅ 监控数据已更新:', {
      systemStatus: systemStatus,
      queueStatus: queueStatus,
      activeTasks: activeTasks.value.length,
      logs: recentLogs.value.length
    })

    // 刷新其他数据
    refreshFileList()
    refreshResults()

    ElMessage.success('监控数据已刷新')

  } catch (error) {
    console.error('❌ 刷新监控数据失败:', error)
    ElMessage.error('刷新监控数据失败')
  }
}

const clearCompletedTasks = async () => {
  try {
    // 统计要清理的任务数量
    const completedTasks = activeTasks.value.filter(task =>
      task.status === 'completed' || task.status === 'failed'
    )

    if (completedTasks.length === 0) {
      ElMessage.info('没有需要清理的已完成任务')
      return
    }

    await ElMessageBox.confirm(
      `确定要清理 ${completedTasks.length} 个已完成的任务吗？`,
      '清理确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    // 取消所有已完成任务的WebSocket订阅
    completedTasks.forEach(task => {
      unsubscribeFromTask(task.id)
    })

    // 清理已完成和失败的任务
    const beforeCount = activeTasks.value.length
    activeTasks.value = activeTasks.value.filter(task =>
      task.status !== 'completed' && task.status !== 'failed'
    )
    const afterCount = activeTasks.value.length
    const clearedCount = beforeCount - afterCount

    // 重置队列状态中的已完成和失败计数
    queueStatus.completed = 0
    queueStatus.failed = 0

    ElMessage.success(`已清理 ${clearedCount} 个已完成的任务`)
    console.log(`🧹 清理了 ${clearedCount} 个已完成任务`)

  } catch (error) {
    if (error !== 'cancel') {
      console.error('清理任务失败:', error)
      ElMessage.error('清理任务失败')
    }
  }
}

const exportLogs = () => {
  // 导出日志功能
  ElMessage.info('日志导出功能开发中...')
}

const getStatusType = (status) => {
  const statusMap = {
    'uploaded': 'success',
    'processing': 'warning',
    'completed': 'info',
    'failed': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status) => {
  const statusMap = {
    'uploaded': '已上传',
    'processing': '处理中',
    'completed': '已完成',
    'failed': '失败'
  }
  return statusMap[status] || '未知'
}

const previewFile = (file) => {
  // 创建音频预览对话框
  ElMessageBox({
    title: `🎵 音频预览 - ${file.name}`,
    message: h('div', { style: 'text-align: center; padding: 20px;' }, [
      h('audio', {
        controls: true,
        style: 'width: 100%; margin-bottom: 15px;',
        src: `/api/v1/audio/file/${file.id}/stream`
      }),
      h('div', { style: 'color: #666; font-size: 14px;' }, [
        h('p', `文件大小: ${formatFileSize(file.size)}`),
        h('p', `时长: ${formatDuration(file.duration)}`),
        h('p', `上传时间: ${formatTime(file.upload_time)}`)
      ])
    ]),
    showCancelButton: true,
    confirmButtonText: '下载文件',
    cancelButtonText: '关闭',
    customClass: 'audio-preview-dialog'
  }).then(() => {
    downloadFile(file)
  }).catch(() => {
    // 用户取消
  })
}

const processFile = async (file) => {
  try {
    ElMessage.info(`开始处理文件: ${file.name}`)

    // 构建处理配置
    const config = {
      language: audioConfig.language,
      use_itn: audioConfig.use_itn,
      ban_emo_unk: audioConfig.ban_emo_unk,
      merge_vad: audioConfig.merge_vad,
      merge_length_s: audioConfig.merge_length_s,
      min_speech_duration: audioConfig.min_speech_duration,
      max_speech_duration: audioConfig.max_speech_duration,
      threshold: audioConfig.threshold,
      // 会议转录配置
      output_format: audioConfig.output_format,
      include_timestamps: audioConfig.include_timestamps,
      include_confidence: audioConfig.include_confidence,
      speaker_labeling: audioConfig.speaker_labeling,
      // 分段策略配置
      segmentation_strategy: audioConfig.segmentation_strategy,
      time_window_size: audioConfig.time_window_size,
      min_segments_required: audioConfig.min_segments_required,
      overlap_ratio: audioConfig.overlap_ratio,
      force_split_threshold: audioConfig.force_split_threshold,
      // 说话人识别配置
      expected_speakers: audioConfig.expected_speakers,
      similarity_threshold: audioConfig.similarity_threshold,
      clustering_method: audioConfig.clustering_method,
      min_segment_duration: audioConfig.min_segment_duration
    }

    console.log('提交处理任务:', { mode: processingMode.value, fileIds: [file.id], config })

    // 使用专用的音频处理API（支持完整Celery任务系统）
    const { startAudioProcessing } = useAudioProcessing()

    // 将文件ID转换为startAudioProcessing期望的文件对象格式
    const files = [{ id: file.id, uid: file.id, name: file.name }]

    const response = await startAudioProcessing({
      files,
      mode: processingMode.value,
      config
    })
    console.log('处理任务响应:', response)

    // 检查响应格式，适配后端API返回的数据结构
    if (response.data && response.data.success) {
      const taskId = response.data.task_id
      ElMessage.success(`文件 ${file.name} 处理任务已创建，任务ID: ${taskId}`)

      // 添加到活动任务列表
      addActiveTask({
        id: taskId,
        file_name: file.name,
        mode: processingMode.value,
        status: 'pending',
        progress: 0,
        start_time: new Date().toISOString()
      })

      // 更新队列状态
      queueStatus.pending++

      // 刷新文件列表以更新状态
      refreshFileList()
    } else if (response.success) {
      const taskId = response.task_id
      ElMessage.success(`文件 ${file.name} 处理任务已创建，任务ID: ${taskId}`)

      // 添加到活动任务列表
      addActiveTask({
        id: taskId,
        file_name: file.name,
        mode: processingMode.value,
        status: 'pending',
        progress: 0,
        start_time: new Date().toISOString()
      })

      queueStatus.pending++
      refreshFileList()
    } else {
      const errorMsg = response.data?.message || response.message || '创建处理任务失败'
      ElMessage.error(errorMsg)
    }
  } catch (error) {
    console.error('处理文件失败:', error)
    ElMessage.error(`处理文件 ${file.name} 失败`)
  }
}

const deleteFile = async (file) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除文件 "${file.name}" 吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 调用删除API
    const response = await audioFileAPI.deleteAudio(file.id)
    console.log('🗑️ 删除API响应:', response)

    // axios响应数据在response.data中
    const result = response.data || response
    if (result.success) {
      ElMessage.success('删除成功')
      refreshFileList()
    } else {
      ElMessage.error(result.message || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除文件失败:', error)
      ElMessage.error('删除文件失败')
    }
  }
}

const batchProcess = async () => {
  if (selectedFiles.value.length === 0) {
    ElMessage.warning('请先选择要处理的文件')
    return
  }

  try {
    ElMessage.info(`开始批量处理 ${selectedFiles.value.length} 个文件`)

    // 构建批量处理配置
    const taskConfig = {
      mode: processingMode.value,
      files: selectedFiles.value.map(file => file.id),
      config: {
        language: audioConfig.language,
        use_itn: audioConfig.use_itn,
        ban_emo_unk: audioConfig.ban_emo_unk,
        merge_vad: audioConfig.merge_vad,
        merge_length_s: audioConfig.merge_length_s,
        min_speech_duration: audioConfig.min_speech_duration,
        max_speech_duration: audioConfig.max_speech_duration,
        threshold: audioConfig.threshold,
        // 会议转录配置
        output_format: audioConfig.output_format,
        include_timestamps: audioConfig.include_timestamps,
        include_confidence: audioConfig.include_confidence,
        speaker_labeling: audioConfig.speaker_labeling,
        // 分段策略配置
        segmentation_strategy: audioConfig.segmentation_strategy,
        time_window_size: audioConfig.time_window_size,
        min_segments_required: audioConfig.min_segments_required,
        overlap_ratio: audioConfig.overlap_ratio,
        force_split_threshold: audioConfig.force_split_threshold,
        // 说话人识别配置
        expected_speakers: audioConfig.expected_speakers,
        similarity_threshold: audioConfig.similarity_threshold,
        clustering_method: audioConfig.clustering_method,
        min_segment_duration: audioConfig.min_segment_duration
      }
    }

    console.log('提交批量处理任务:', taskConfig)

    // 调用后端API创建批量处理任务
    const response = await audioProcessingAPI.createTask(taskConfig)

    if (response.success) {
      ElMessage.success(`批量处理任务已创建，任务ID: ${response.task_id}`)
      // 清空选择
      selectedFiles.value = []
      // 刷新文件列表以更新状态
      refreshFileList()
    } else {
      ElMessage.error(response.message || '创建批量处理任务失败')
    }
  } catch (error) {
    console.error('批量处理失败:', error)
    ElMessage.error('批量处理失败')
  }
}

// 批量操作功能
const batchDownloadFiles = async () => {
  if (selectedFiles.value.length === 0) {
    ElMessage.warning('请先选择要下载的文件')
    return
  }

  try {
    ElMessage.info(`开始批量下载 ${selectedFiles.value.length} 个文件`)

    // 创建ZIP文件下载
    const response = await fetch('/api/v1/audio/files/batch-download', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...uploadHeaders.value
      },
      body: JSON.stringify({
        file_ids: selectedFiles.value.map(file => file.id)
      })
    })

    if (!response.ok) {
      throw new Error('批量下载失败')
    }

    const blob = await response.blob()
    const url = URL.createObjectURL(blob)

    // 创建下载链接
    const link = document.createElement('a')
    link.href = url
    link.download = `音频文件批量下载_${new Date().toISOString().slice(0, 10)}.zip`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // 清理URL
    URL.revokeObjectURL(url)

    ElMessage.success('批量下载完成')

    // 清空选择
    selectedFiles.value = []
  } catch (error) {
    console.error('批量下载失败:', error)
    ElMessage.error('批量下载失败')
  }
}

const batchDeleteFiles = async () => {
  if (selectedFiles.value.length === 0) {
    ElMessage.warning('请先选择要删除的文件')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedFiles.value.length} 个文件吗？此操作不可撤销。`,
      '批量删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }
    )

    ElMessage.info(`开始批量删除 ${selectedFiles.value.length} 个文件`)

    // 批量删除API调用
    const response = await fetch('/api/v1/audio/batch/delete', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...uploadHeaders.value
      },
      body: JSON.stringify({
        file_ids: selectedFiles.value.map(file => file.id)
      })
    })

    if (!response.ok) {
      throw new Error('批量删除失败')
    }

    const result = await response.json()
    console.log('🗑️ 批量删除API响应:', result)

    if (result.success) {
      ElMessage.success(`成功删除 ${result.deleted_count} 个文件${result.failed_count > 0 ? `，${result.failed_count} 个文件删除失败` : ''}`)

      // 清空选择
      selectedFiles.value = []

      // 刷新文件列表
      refreshFileList()
    } else {
      ElMessage.error(result.message || '批量删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

const selectAllFiles = () => {
  if (selectedFiles.value.length === userAudioFiles.value.length) {
    // 全部已选中，取消全选
    selectedFiles.value = []
    ElMessage.info('已取消全选')
  } else {
    // 全选
    selectedFiles.value = [...userAudioFiles.value]
    ElMessage.info(`已选中 ${selectedFiles.value.length} 个文件`)
  }
}

const clearSelection = () => {
  selectedFiles.value = []
  ElMessage.info('已清空选择')
}

const handleBatchAction = (command) => {
  switch (command) {
    case 'process':
      batchProcess()
      break
    case 'download':
      batchDownloadFiles()
      break
    case 'delete':
      batchDeleteFiles()
      break
    case 'clear':
      clearSelection()
      break
    default:
      console.warn('未知的批量操作:', command)
  }
}

// 文件操作功能
const downloadFile = async (file) => {
  try {
    ElMessage.info(`正在下载文件: ${file.name}`)

    // 显示加载状态
    const loading = ElLoading.service({
      lock: true,
      text: '正在准备下载文件...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    try {
      // 使用audioFileAPI下载文件，自动添加认证头
      const response = await audioFileAPI.downloadAudioFile(file.id)

      // 创建下载链接
      const blob = new Blob([response.data])
      const url = URL.createObjectURL(blob)

      const link = document.createElement('a')
      link.href = url
      link.download = file.name
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      // 清理URL
      URL.revokeObjectURL(url)

      ElMessage.success(`文件 ${file.name} 下载成功`)

    } finally {
      loading.close()
    }

  } catch (error) {
    console.error('下载文件失败:', error)
    ElMessage.error(`下载文件失败: ${error.message}`)
  }
}

const renameFile = async (file) => {
  try {
    const { value: newName } = await ElMessageBox.prompt(
      '请输入新的文件名',
      '重命名文件',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputValue: file.name.replace(/\.[^/.]+$/, ''), // 去掉扩展名
        inputValidator: (value) => {
          if (!value || value.trim() === '') {
            return '文件名不能为空'
          }
          if (value.length > 100) {
            return '文件名不能超过100个字符'
          }
          return true
        }
      }
    )

    // 保持原扩展名
    const extension = file.name.split('.').pop()
    const finalName = `${newName.trim()}.${extension}`

    // 调用重命名API
    const response = await audioFileAPI.renameFile(file.id, finalName)

    if (response.success) {
      ElMessage.success('文件重命名成功')
      refreshFileList()
    } else {
      ElMessage.error(response.message || '重命名失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('重命名文件失败:', error)
      ElMessage.error('重命名文件失败')
    }
  }
}

const copyFileUrl = (file) => {
  const url = `${window.location.origin}/api/v1/audio/file/${file.id}/stream`

  if (navigator.clipboard) {
    navigator.clipboard.writeText(url).then(() => {
      ElMessage.success('文件链接已复制到剪贴板')
    }).catch(() => {
      fallbackCopyText(url)
    })
  } else {
    fallbackCopyText(url)
  }
}

const fallbackCopyText = (text) => {
  const textArea = document.createElement('textarea')
  textArea.value = text
  document.body.appendChild(textArea)
  textArea.select()
  try {
    document.execCommand('copy')
    ElMessage.success('文件链接已复制到剪贴板')
  } catch (err) {
    ElMessage.error('复制失败，请手动复制')
  }
  document.body.removeChild(textArea)
}

const handleFileAction = (command, file) => {
  switch (command) {
    case 'rename':
      renameFile(file)
      break
    case 'copy':
      copyFileUrl(file)
      break
    case 'delete':
      deleteFile(file)
      break
    default:
      console.warn('未知的文件操作:', command)
  }
}

// WebSocket实时通信功能
const initWebSocket = () => {
  // 获取认证token - 使用正确的token键名
  const token = getToken()
  if (!token) {
    console.error('没有有效的认证token，无法建立WebSocket连接')
    return
  }

  const wsUrl = `ws://localhost:8002/ws/audio-center?token=${token}`

  try {
    websocket = new WebSocket(wsUrl)

    websocket.onopen = () => {
      console.log('WebSocket连接已建立')
      wsConnected.value = true
      wsReconnectAttempts.value = 0
    }

    websocket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        handleWebSocketMessage(data)
      } catch (error) {
        console.error('解析WebSocket消息失败:', error)
      }
    }

    websocket.onclose = () => {
      console.log('WebSocket连接已关闭')
      wsConnected.value = false

      // 自动重连
      if (wsReconnectAttempts.value < maxReconnectAttempts) {
        wsReconnectAttempts.value++
        console.log(`尝试重连WebSocket (${wsReconnectAttempts.value}/${maxReconnectAttempts})`)
        wsReconnectTimeout = setTimeout(initWebSocket, 3000 * wsReconnectAttempts.value)
      } else {
        console.error('WebSocket重连失败，已达到最大重试次数')
      }
    }

    websocket.onerror = (error) => {
      console.error('WebSocket连接错误:', error)
    }
  } catch (error) {
    console.error('创建WebSocket连接失败:', error)
  }
}

const handleWebSocketMessage = (data) => {
  console.log('📨 收到WebSocket消息:', data)

  switch (data.type) {
    case 'progress_update':
      // 处理后端发送的进度更新消息
      if (data.payload && data.payload.task_id) {
        const taskId = data.payload.task_id

        // 检查任务是否已经完成，避免处理已完成任务的进度更新
        if (completedTaskIds.has(taskId)) {
          console.log(`⚠️ 任务 ${taskId} 已完成，跳过进度更新`)
          return
        }

        const progressData = data.payload.progress

        // 从后端WebSocket消息中正确提取进度数据
        let percentage = 0
        let detail = ''
        let stage = 'processing'

        if (progressData) {
          // 后端发送的格式：{ percentage: 95, detail: "保存识别结果", stage: "processing" }
          percentage = parseFloat(progressData.percentage) || 0
          detail = progressData.detail || ''
          stage = progressData.stage || 'processing'
        }

        console.log(`📈 任务进度更新: ${taskId} - ${percentage}% - ${stage} - ${detail}`)
        updateTaskProgress(taskId, percentage, stage, detail)
      }
      break
    case 'task_progress':
      // 兼容旧格式
      updateTaskProgress(data.task_id, data.progress, data.status)
      break
    case 'task_completed':
      // 处理任务完成消息
      if (data.payload && data.payload.task_id) {
        const taskId = data.payload.task_id

        // 检查任务是否已经处理过完成状态
        if (completedTaskIds.has(taskId)) {
          console.log(`⚠️ 任务 ${taskId} 已经处理过完成状态，跳过重复处理`)
          return
        }

        console.log(`✅ 任务完成: ${taskId}`)
        handleTaskCompleted(taskId, data.payload.result)
      } else if (data.task_id) {
        const taskId = data.task_id

        // 检查任务是否已经处理过完成状态
        if (completedTaskIds.has(taskId)) {
          console.log(`⚠️ 任务 ${taskId} 已经处理过完成状态，跳过重复处理`)
          return
        }

        handleTaskCompleted(taskId, data.result)
      }
      break
    case 'task_failed':
      // 处理任务失败消息
      if (data.payload && data.payload.task_id) {
        console.log(`❌ 任务失败: ${data.payload.task_id} - ${data.payload.error}`)
        handleTaskFailed(data.payload.task_id, data.payload.error)
      } else {
        handleTaskFailed(data.task_id, data.error)
      }
      break
    case 'system_status':
      updateSystemStatus(data.status)
      break
    case 'file_uploaded':
      handleFileUploaded(data.file)
      break
    case 'log_message':
      addLogMessage(data.log)
      break
    default:
      console.log('未知的WebSocket消息类型:', data.type)
  }
}

const updateTaskProgress = (taskId, progress, status, detail = '') => {
  console.log(`📊 更新任务进度: ${taskId} - ${progress}% - ${status} - ${detail}`)

  // [FIX] 检查任务是否已经完成，避免处理已完成任务的进度更新
  if (completedTaskIds.has(taskId)) {
    console.log(`⚠️ 任务 ${taskId} 已完成，跳过进度更新`)
    return
  }

  // [FIX] 严格的进度边界检查，确保进度值在0-100范围内
  let actualProgress = Math.min(Math.max(progress || 0, 0), 100)

  // 使用真实进度数据，只有在进度为0且没有详细信息时才使用模拟进度
  if (actualProgress === 0 && status === 'processing' && !detail) {
    // 只有在没有真实进度数据时才生成模拟进度
    const task = activeTasks.value.find(t => t.id === taskId)
    if (task) {
      const startTime = new Date(task.start_time).getTime()
      const currentTime = Date.now()
      const elapsedSeconds = (currentTime - startTime) / 1000

      // 基于经过时间计算模拟进度（假设小文件3秒内完成）
      actualProgress = Math.min(Math.floor(elapsedSeconds * 30), 95) // 最多到95%
      console.log(`🎯 为快速任务生成模拟进度: ${actualProgress}% (经过${elapsedSeconds.toFixed(1)}秒)`)
    }
  } else if (actualProgress > 0) {
    // 如果有真实进度数据，直接使用
    console.log(`✅ 使用真实进度数据: ${actualProgress}%`)
  }

  // 更新活动任务进度
  const task = activeTasks.value.find(t => t.id === taskId)
  if (task) {
    task.progress = actualProgress
    task.status = status
    if (detail) {
      task.detail = detail
    }
    task.updateTime = new Date().toLocaleTimeString()
    console.log(`✅ 活动任务已更新: ${taskId}`)
  } else {
    console.log(`⚠️ 未找到活动任务: ${taskId}`)
  }

  // 更新处理结果进度
  const result = processingResults.value.find(r => r.task_id === taskId)
  if (result) {
    result.progress = actualProgress
    result.status = status
    if (detail) {
      result.detail = detail
    }
    console.log(`✅ 处理结果已更新: ${taskId}`)
  } else {
    console.log(`⚠️ 未找到处理结果: ${taskId}`)
  }

  console.log(`📈 任务 ${taskId} 进度更新完成: ${actualProgress}% (${status})`)
}

// 用于跟踪已处理的完成任务，避免重复处理
const completedTaskIds = new Set()

// 清理已完成任务ID的函数（避免内存泄漏）
const cleanupCompletedTaskIds = () => {
  // 保留最近100个已完成任务ID，清理更早的
  if (completedTaskIds.size > 100) {
    const idsArray = Array.from(completedTaskIds)
    const toKeep = idsArray.slice(-50) // 保留最近50个
    completedTaskIds.clear()
    toKeep.forEach(id => completedTaskIds.add(id))
    console.log(`🧹 清理已完成任务ID，保留最近 ${toKeep.length} 个`)
  }
}

const handleTaskCompleted = (taskId, result) => {
  // 检查是否已经处理过这个任务的完成
  if (completedTaskIds.has(taskId)) {
    console.log(`⚠️ 任务 ${taskId} 已经处理过完成状态，跳过重复处理`)
    return
  }

  console.log(`🎉 处理任务完成: ${taskId}`)
  console.log('📊 任务结果数据结构:', JSON.stringify(result, null, 2))

  // 特别记录会议转录结果
  if (result && result.task_type === 'meeting_transcription') {
    console.log('🎤 会议转录结果详情:', {
      总文件数: result.total_files,
      成功数: result.success_count,
      失败数: result.error_count,
      结果数量: result.results ? result.results.length : 0
    })

    // 详细记录每个文件的结果
    if (result.results && result.results.length > 0) {
      result.results.forEach((fileResult, index) => {
        console.log(`📄 文件 ${index + 1} 结果:`, {
          文件ID: fileResult.file_id,
          状态: fileResult.status,
          有文本内容: !!(fileResult.result && fileResult.result.text),
          文本长度: fileResult.result && fileResult.result.text ? fileResult.result.text.length : 0,
          说话人数量: fileResult.result && fileResult.result.speaker_segments ? fileResult.result.speaker_segments.length : 0
        })
      })
    }
  }

  // 标记任务为已完成，避免重复处理
  completedTaskIds.add(taskId)

  // 更新任务状态到100%
  updateTaskProgress(taskId, 100, 'completed')

  // 更新处理结果
  const resultItem = processingResults.value.find(r => r.task_id === taskId)
  if (resultItem) {
    resultItem.result = result
    resultItem.status = 'completed'
    resultItem.progress = 100
    resultItem.completed_at = new Date().toISOString()
    console.log(`✅ 处理结果已更新: ${taskId}`)

    // 验证结果是否正确设置
    console.log('🔍 验证结果设置:', {
      任务ID: resultItem.task_id,
      状态: resultItem.status,
      进度: resultItem.progress,
      有结果数据: !!resultItem.result,
      结果类型: resultItem.result ? resultItem.result.task_type : 'unknown'
    })
  } else {
    console.error(`❌ 未找到处理结果项: ${taskId}`)
  }

  // 从活动任务中移除并取消订阅
  const taskIndex = activeTasks.value.findIndex(t => t.id === taskId)
  if (taskIndex !== -1) {
    const task = activeTasks.value[taskIndex]
    activeTasks.value.splice(taskIndex, 1)

    // 取消WebSocket订阅
    unsubscribeFromTask(taskId)

    console.log(`🗑️ 活动任务已移除: ${taskId}`)

    // 如果没有活动任务了，可以减少轮询频率
    if (activeTasks.value.length === 0) {
      console.log('📴 所有任务已完成，减少轮询频率')
    }
  }

  // 更新队列状态
  queueStatus.processing = Math.max(0, queueStatus.processing - 1)
  queueStatus.completed++

  // 显示成功消息
  ElMessage.success(`任务 ${taskId.substring(0, 8)}... 处理完成`)

  // 延迟刷新处理结果，避免频繁刷新
  setTimeout(() => {
    refreshResults()
  }, 500)

  console.log(`📊 任务完成处理完毕: ${taskId}`)
}

const handleTaskFailed = (taskId, error) => {
  console.log(`❌ 处理任务失败: ${taskId} - ${error}`)

  // 更新任务状态
  updateTaskProgress(taskId, 0, 'failed', error)

  // 更新处理结果
  const resultItem = processingResults.value.find(r => r.task_id === taskId)
  if (resultItem) {
    resultItem.error = error
    resultItem.status = 'failed'
    resultItem.progress = 0
    resultItem.failed_at = new Date().toISOString()
    console.log(`❌ 处理结果已标记为失败: ${taskId}`)
  }

  // 从活动任务中移除并取消订阅
  const taskIndex = activeTasks.value.findIndex(t => t.id === taskId)
  if (taskIndex !== -1) {
    activeTasks.value.splice(taskIndex, 1)

    // 取消WebSocket订阅
    unsubscribeFromTask(taskId)

    console.log(`🗑️ 失败任务已移除: ${taskId}`)
  }

  // 更新队列状态
  queueStatus.processing = Math.max(0, queueStatus.processing - 1)
  queueStatus.failed++

  // 显示错误消息
  ElMessage.error(`任务 ${taskId.substring(0, 8)}... 处理失败: ${error}`)

  // 异步刷新数据
  setTimeout(() => {
    refreshResults()
  }, 100)

  console.log(`📊 任务失败处理完毕: ${taskId}`)
}

const updateSystemStatus = (status) => {
  if (status.cpu !== undefined) systemStatus.cpu = status.cpu
  if (status.memory !== undefined) systemStatus.memory = status.memory
  if (status.gpu !== undefined) systemStatus.gpu = status.gpu
}

const handleFileUploaded = (file) => {
  ElMessage.success(`文件 ${file.name} 上传成功`)
  refreshFileList()
}

const addLogMessage = (log) => {
  recentLogs.value.unshift({
    id: Date.now(),
    time: new Date(),
    level: log.level || 'info',
    message: log.message
  })

  // 保持最多20条日志
  if (recentLogs.value.length > 20) {
    recentLogs.value = recentLogs.value.slice(0, 20)
  }
}

const closeWebSocket = () => {
  // 清理重连超时
  if (wsReconnectTimeout) {
    clearTimeout(wsReconnectTimeout)
    wsReconnectTimeout = null
  }

  if (websocket) {
    websocket.close()
    websocket = null
  }
}

// WebSocket任务订阅管理
const subscribeToTask = (taskId) => {
  if (websocket && websocket.readyState === WebSocket.OPEN) {
    const message = {
      type: 'subscribe',
      task_id: taskId
    }
    websocket.send(JSON.stringify(message))
    console.log(`📡 订阅任务进度: ${taskId}`)
  } else {
    console.warn(`⚠️ WebSocket未连接，无法订阅任务: ${taskId}`)
  }
}

const unsubscribeFromTask = (taskId) => {
  if (websocket && websocket.readyState === WebSocket.OPEN) {
    const message = {
      type: 'unsubscribe',
      task_id: taskId
    }
    websocket.send(JSON.stringify(message))
    console.log(`📡 取消订阅任务: ${taskId}`)
  }
}

// 任务状态轮询机制（WebSocket备用方案）
let taskPollingInterval = null
let monitorDataInterval = null
let wsReconnectTimeout = null

const startTaskPolling = () => {
  if (taskPollingInterval) {
    clearInterval(taskPollingInterval)
  }

  taskPollingInterval = setInterval(async () => {
    if (activeTasks.value.length > 0) {
      console.log(`🔄 轮询检查 ${activeTasks.value.length} 个活动任务状态`)
      await checkActiveTasksStatus()
    }
  }, 10000) // 减少频率到每10秒检查一次
}

const stopTaskPolling = () => {
  if (taskPollingInterval) {
    clearInterval(taskPollingInterval)
    taskPollingInterval = null
    console.log('⏹️ 停止任务状态轮询')
  }
}

const checkActiveTasksStatus = async () => {
  try {
    // 只有在有活动任务时才进行轮询
    if (activeTasks.value.length === 0) {
      return
    }

    // 使用活动任务API而不是处理结果API
    const response = await audioFileAPI.getActiveTasks()
    const activeTasksFromAPI = response.data || []

    // 检查每个本地活动任务的状态
    for (const localTask of activeTasks.value) {
      const apiTask = activeTasksFromAPI.find(t => t.id === localTask.id || t.task_id === localTask.id)

      if (apiTask) {
        // 任务仍在处理中，更新进度
        const progress = apiTask.progress || 0
        const status = apiTask.status || 'processing'
        const detail = apiTask.detail || ''

        if (status !== localTask.status || progress !== localTask.progress) {
          console.log(`🔄 检测到任务状态变化: ${localTask.id} - ${status} ${progress}%`)
          updateTaskProgress(localTask.id, progress, status, detail)
        }
      } else {
        // 任务不在活动列表中，可能已完成，检查处理结果
        console.log(`🔍 任务不在活动列表中，检查是否已完成: ${localTask.id}`)
        await checkTaskCompletion(localTask.id)
      }
    }
  } catch (error) {
    console.warn('⚠️ 轮询检查任务状态失败:', error)
  }
}

const checkTaskCompletion = async (taskId) => {
  try {
    // 修复：使用正确的任务状态查询API
    const response = await audioProcessingAPI.getTaskStatus(taskId)
    const taskData = response.data || response

    console.log(`🔍 检查任务状态: ${taskId}`, taskData)

    if (taskData && (taskData.status === 'completed' || taskData.status === 'SUCCESS')) {
      console.log(`✅ 发现已完成的任务: ${taskId}`)
      handleTaskCompleted(taskId, taskData.result)
    } else if (taskData && (taskData.status === 'failed' || taskData.status === 'FAILURE')) {
      console.log(`❌ 发现失败的任务: ${taskId}`)
      handleTaskFailed(taskId, taskData.error || '任务执行失败')
    } else {
      console.log(`⏳ 任务仍在处理中: ${taskId} - 状态: ${taskData?.status}`)
    }
  } catch (error) {
    console.warn(`⚠️ 检查任务完成状态失败: ${taskId}`, error)
  }
}

// 活动任务管理
const addActiveTask = (task) => {
  activeTasks.value.push(task)
  console.log('添加活动任务:', task)

  // 订阅任务进度更新
  subscribeToTask(task.id)
}

const removeActiveTask = (taskId) => {
  const index = activeTasks.value.findIndex(t => t.id === taskId)
  if (index !== -1) {
    activeTasks.value.splice(index, 1)
    console.log('移除活动任务:', taskId)

    // 取消订阅任务进度
    unsubscribeFromTask(taskId)
  }
}

// 音频处理增强功能
const validateProcessingConfig = () => {
  // 验证配置参数
  if (processingMode.value === 'vad-detection') {
    if (audioConfig.threshold < 0.1 || audioConfig.threshold > 0.9) {
      ElMessage.warning('VAD检测阈值应在0.1-0.9之间')
      return false
    }
    if (audioConfig.min_speech_duration > audioConfig.max_speech_duration) {
      ElMessage.warning('最小语音时长不能大于最大语音时长')
      return false
    }
  }

  if (audioConfig.merge_length_s < 1 || audioConfig.merge_length_s > 60) {
    ElMessage.warning('合并长度应在1-60秒之间')
    return false
  }

  return true
}

const getOptimalProcessingConfig = (mode) => {
  // 根据处理模式返回优化的配置
  const configs = {
    'speech-recognition': {
      use_itn: true,
      ban_emo_unk: false,
      merge_vad: true,
      merge_length_s: 15
    },
    'speaker-recognition': {
      use_itn: false,
      ban_emo_unk: true,
      merge_vad: false,
      min_speech_duration: 1.0
    },
    'vad-detection': {
      merge_vad: true,
      threshold: 0.5,
      min_speech_duration: 0.5,
      max_speech_duration: 60
    },
    'meeting-transcription': {
      use_itn: true,
      merge_vad: true,
      merge_length_s: 10,
      min_speech_duration: 0.3
    },
    'audio_enhancement': {
      use_itn: true,
      ban_emo_unk: false,
      merge_vad: true
    }
  }

  return configs[mode] || {}
}

const applyOptimalConfig = () => {
  const optimal = getOptimalProcessingConfig(processingMode.value)
  Object.assign(audioConfig, optimal)
  ElMessage.success(`已应用${currentModeDescription.value?.title}模式的优化配置`)
}

// 生命周期
onMounted(async () => {
  console.log('AudioCenter mounted')
  // 设置上传请求头 - 使用正确的token键名
  const token = getToken()
  if (token) {
    uploadHeaders.value = {
      'Authorization': `Bearer ${token}`
    }
  }

  // 加载文件列表
  refreshFileList()

  // 加载处理结果
  refreshResults()

  // 初始化监控数据
  await refreshMonitorData()

  // 初始化WebSocket连接
  initWebSocket()

  // 启动任务状态轮询（作为WebSocket的备用方案）
  startTaskPolling()

  // 启动监控数据定时刷新（每30秒）
  monitorDataInterval = setInterval(refreshMonitorData, 30000)
})

onUnmounted(() => {
  console.log('AudioCenter unmounted')

  // 清理录音资源
  if (mediaRecorder && recordingStatus.value !== 'idle') {
    mediaRecorder.stop()
  }

  if (audioStream) {
    audioStream.getTracks().forEach(track => track.stop())
  }

  stopRecordingTimer()

  // 清理音频URL
  if (recordedAudioUrl.value) {
    URL.revokeObjectURL(recordedAudioUrl.value)
  }

  // 停止任务状态轮询
  stopTaskPolling()

  // 停止监控数据定时刷新
  if (monitorDataInterval) {
    clearInterval(monitorDataInterval)
    monitorDataInterval = null
  }

  // 关闭WebSocket连接
  closeWebSocket()
})
</script>

<style scoped>
/* CSS变量定义 */
:root {
  --primary-color: #409eff;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;

  --text-primary: #dcdee2;
  --text-regular: #b4b9c2;
  --text-secondary: #dcdee2;
  --text-placeholder: #c0c4cc;

  --border-color: #dcdfe6;
  --border-light: #e4e7ed;
  --border-lighter: #ebeef5;
  --border-extra-light: #f2f6fc;

  --background-color: #ffffff;
  --surface-bg: #f5f7fa;
  --hover-bg: #ecf5ff;
  --panel-bg: #fafbfc;

  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  --radius-sm: 4px;
  --radius-md: 6px;
  --radius-lg: 8px;

  --shadow-light: 0 2px 4px rgba(0, 0, 0, 0.08);
  --shadow-medium: 0 4px 12px rgba(0, 0, 0, 0.12);
  --shadow-strong: 0 8px 24px rgba(0, 0, 0, 0.15);

  --accent-primary: #409eff;
  --accent-secondary: #7c3aed;
  --accent-light: #ecf5ff;
}

/* 主容器 */
.audio-center {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--background-color);
  overflow: hidden;
}

/* 顶部工具栏 */
.audio-center-header {
  height: 60px;
  padding: 0 var(--spacing-lg);
  background: var(--surface-bg);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-lg);
}

.page-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.workspace-info {
  display: flex;
  gap: var(--spacing-sm);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

/* 主工作区 */
.audio-center-workspace {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 侧边栏通用样式 */
.sidebar-left,
.sidebar-right {
  width: 300px;
  background: var(--surface-bg);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}

.sidebar-right {
  border-right: none;
  border-left: 1px solid var(--border-color);
}

.panel-header {
  height: 50px;
  padding: 0 var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--background-color);
  flex-shrink: 0;
}

.panel-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.panel-content {
  flex: 1;
  padding: var(--spacing-md);
  overflow-y: auto;
  background: var(--surface-bg);
}

/* 中央工作区 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 标签页导航 */
.main-tabs-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  background: var(--surface-bg);
}

.tabs-navigation {
  display: flex;
  gap: var(--spacing-sm);
}

.tab-button {
  padding: var(--spacing-sm) var(--spacing-lg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--background-color);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.tab-button:hover {
  background: var(--hover-bg);
  border-color: var(--accent-primary);
  color: var(--text-primary);
}

.tab-button.active {
  background: var(--accent-primary);
  border-color: var(--accent-primary);
  color: rgb(101, 92, 92);
  box-shadow: 0 2px 4px rgba(88, 166, 255, 0.3);
}

.main-tabs-content {
  flex: 1;
  overflow: hidden;
}

.tab-content-main {
  height: 100%;
  overflow-y: auto;
  padding: var(--spacing-lg);
}

/* 简单内容样式 */
.simple-content {
  text-align: center;
  padding: var(--spacing-xl);
}

.simple-content h3 {
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.simple-content p {
  color: var(--text-secondary);
}

/* 配置区域样式 */
.config-section {
  margin-bottom: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--background-color);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-lighter);
  box-shadow: var(--shadow-light);
}

.config-section h4 {
  margin: 0 0 var(--spacing-md) 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-primary);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-extra-light);
}

.config-item {
  margin-bottom: var(--spacing-md);
}

.config-item:last-child {
  margin-bottom: 0;
}

.config-item label {
  display: block;
  margin-bottom: var(--spacing-xs);
  font-size: 0.85rem;
  color: var(--text-regular);
  font-weight: 500;
}

/* 模式选项样式 */
.mode-option {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  width: 100%;
  min-height: 70px;
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;
  box-sizing: border-box;
  margin: 0;
}

.mode-option:hover {
  background-color: var(--hover-bg);
}

.mode-icon {
  font-size: 1.4rem;
  line-height: 1;
  margin-top: 2px;
  flex-shrink: 0;
  width: 28px;
  text-align: center;
  display: block;
}

.mode-info {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.mode-name {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 0.9rem;
  line-height: 1.3;
  margin-bottom: var(--spacing-xs);
  white-space: normal;
  word-wrap: break-word;
}

.mode-desc {
  font-size: 0.75rem;
  color: var(--text-secondary);
  line-height: 1.4;
  word-wrap: break-word;
  white-space: normal;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
}

/* 模式描述样式 */
.mode-description {
  margin-top: var(--spacing-md);
}

.mode-description :deep(.el-alert) {
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
}

.mode-description :deep(.el-alert__title) {
  font-size: 0.85rem;
  font-weight: 600;
}

.mode-description :deep(.el-alert__description) {
  font-size: 0.8rem;
  line-height: 1.4;
  margin-top: var(--spacing-xs);
}

/* 下拉菜单样式优化 */
.mode-select-dropdown {
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--shadow-medium) !important;
  border: 1px solid var(--border-light) !important;
  min-width: 350px !important;
  max-width: 400px !important;
}

.mode-select-dropdown .el-select-dropdown__item {
  padding: 0 !important;
  height: auto !important;
  min-height: 80px !important;
  line-height: normal !important;
  white-space: normal !important;
  overflow: visible !important;
}

.mode-select-dropdown .el-select-dropdown__item.hover {
  background-color: var(--hover-bg) !important;
}

.mode-select-dropdown .el-select-dropdown__item.selected {
  background-color: var(--accent-primary) !important;
  color: rgb(119, 111, 111) !important;
}

.mode-select-dropdown .el-select-dropdown__item.selected .mode-option {
  background-color: transparent !important;
}

.mode-select-dropdown .el-select-dropdown__item.selected .mode-name {
  color: rgb(130, 118, 118) !important;
}

.mode-select-dropdown .el-select-dropdown__item.selected .mode-desc {
  color: rgba(123, 113, 113, 0.9) !important;
}

.mode-select-dropdown .el-select-dropdown__item.selected .mode-icon {
  filter: brightness(0) invert(1);
}

/* 文件管理样式 */
.upload-section {
  margin-bottom: var(--spacing-xl);
}

.upload-dragger {
  width: 100%;
}

.upload-dragger :deep(.el-upload-dragger) {
  width: 100%;
  height: 180px;
  border: 2px dashed var(--border-color);
  border-radius: var(--radius-lg);
  background: var(--surface-bg);
  transition: all 0.3s ease;
}

.upload-dragger :deep(.el-upload-dragger:hover) {
  border-color: var(--accent-primary);
  background: var(--accent-light);
}

.upload-dragger :deep(.el-icon--upload) {
  font-size: 3rem;
  color: var(--accent-primary);
  margin-bottom: var(--spacing-md);
}

.upload-dragger :deep(.el-upload__text) {
  font-size: 1rem;
  color: var(--text-regular);
  margin-bottom: var(--spacing-sm);
}

.upload-dragger :deep(.el-upload__text em) {
  color: var(--accent-primary);
  font-style: normal;
  font-weight: 600;
}

.upload-dragger :deep(.el-upload__tip) {
  font-size: 0.85rem;
  color: var(--text-secondary);
  line-height: 1.4;
  margin-top: var(--spacing-sm);
}

.file-list-section {
  background: var(--background-color);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-lighter);
  overflow: hidden;
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  background: var(--surface-bg);
  border-bottom: 1px solid var(--border-lighter);
}

.section-header h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.header-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.file-name {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.file-name .el-icon {
  color: var(--accent-primary);
  font-size: 1.2rem;
}

.file-name span {
  font-weight: 500;
  color: var(--text-primary);
}

/* VAD配置样式 */
.config-hint {
  font-size: 0.75rem;
  color: #dcdee2 !important;
  margin-top: var(--spacing-xs);
  line-height: 1.3;
}

.config-item .el-input-number {
  width: 100%;
}

.config-item .el-slider {
  margin: var(--spacing-sm) 0;
}

/* 处理结果样式 */
.results-header {
  padding: var(--spacing-lg);
  background: var(--surface-bg);
  border-bottom: 1px solid var(--border-lighter);
}

.results-filters {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
}

.results-list {
  flex: 1;
  overflow: hidden;
}

.task-id-text {
  font-family: monospace;
  font-size: 0.85rem;
}

.file-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.progress-text {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-left: var(--spacing-xs);
}

.result-detail {
  max-height: 60vh;
  overflow-y: auto;
}

.detail-section {
  margin-bottom: var(--spacing-xl);
}

.detail-section h4 {
  margin: 0 0 var(--spacing-md) 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.result-content {
  background: var(--surface-bg);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
}

.text-result,
.json-result {
  background: var(--background-color);
  border: 1px solid var(--border-lighter);
  border-radius: var(--radius-sm);
  padding: var(--spacing-md);
  max-height: 300px;
  overflow-y: auto;
}

.text-result pre,
.json-result pre {
  margin: 0;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.85rem;
  line-height: 1.5;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* 监控面板样式 */
.monitor-section {
  margin-bottom: var(--spacing-xl);
}

.monitor-section h4 {
  margin: 0 0 var(--spacing-md) 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-primary);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-extra-light);
}

.status-cards {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.status-card {
  background: var(--background-color);
  border: 1px solid var(--border-lighter);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
}

.status-label {
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.status-value {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

.active-tasks {
  max-height: 300px;
  overflow-y: auto;
}

.task-item {
  background: var(--background-color);
  border: 1px solid var(--border-lighter);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.task-name {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.85rem;
}

.task-progress {
  margin-bottom: var(--spacing-sm);
}

.task-info {
  display: flex;
  justify-content: space-between;
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.queue-stats {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-sm);
}

.queue-item {
  background: var(--background-color);
  border: 1px solid var(--border-lighter);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  text-align: center;
}

.queue-label {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.queue-count {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.queue-count.error {
  color: var(--danger-color);
}

.log-container {
  max-height: 200px;
  overflow-y: auto;
  background: var(--background-color);
  border: 1px solid var(--border-lighter);
  border-radius: var(--radius-md);
  padding: var(--spacing-sm);
}

.log-item {
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  margin-bottom: var(--spacing-xs);
  font-size: 0.75rem;
}

.log-item.info {
  background: var(--accent-light);
  border-left: 3px solid var(--primary-color);
}

.log-item.warning {
  background: #e1af0d;
  border-left: 3px solid var(--warning-color);
}

.log-item.success {
  background: #467c9f;
  border-left: 3px solid var(--success-color);
}

.log-item.error {
  background: #afe35a;
  border-left: 3px solid var(--danger-color);
}

.log-time {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.log-message {
  color: var(--text-primary);
  line-height: 1.4;
}

.no-logs {
  text-align: center;
  color: var(--text-secondary);
  font-size: 0.8rem;
  padding: var(--spacing-lg);
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

/* 文件操作样式 */
.file-actions {
  display: flex;
  gap: var(--spacing-xs);
  align-items: center;
  flex-wrap: wrap;
}

.file-actions .el-button {
  margin: 0;
}

/* 音频预览对话框样式 */
:deep(.audio-preview-dialog) {
  .el-message-box__content {
    padding: 20px;
  }

  audio {
    outline: none;
    border-radius: var(--radius-md);
  }
}

/* 下拉菜单样式优化 */
:deep(.el-select-dropdown) {
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-medium);
  border: 1px solid var(--border-light);
}

:deep(.el-select-dropdown__item) {
  padding: 0 !important;
  height: auto !important;
  line-height: normal !important;
}

:deep(.el-select-dropdown__item.hover) {
  background-color: transparent !important;
}

:deep(.el-select-dropdown__item.selected) {
  background-color: var(--accent-primary) !important;
  color: white !important;
}

:deep(.el-select-dropdown__item.selected .mode-option) {
  background-color: transparent;
}

:deep(.el-select-dropdown__item.selected .mode-name) {
  color: white !important;
}

:deep(.el-select-dropdown__item.selected .mode-desc) {
  color: rgba(255, 255, 255, 0.8) !important;
}

/* 录音相关样式 */
.recording-section-left {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.recording-status-compact {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-sm);
  background: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--info-color);
}

.status-indicator.recording .status-dot {
  background: var(--danger-color);
  animation: pulse 1s infinite;
}

.status-indicator.paused .status-dot {
  background: var(--warning-color);
}

.status-text {
  font-size: 0.85rem;
  color: var(--text-regular);
}

.recording-time {
  font-size: 0.85rem;
  color: var(--text-secondary);
  font-family: monospace;
}

.control-buttons-compact {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.recording-preview-compact {
  padding: var(--spacing-sm);
  background: var(--background-color);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
}

.preview-actions-compact {
  display: flex;
  gap: var(--spacing-xs);
}

/* 录音功能区域样式补充 */
.recording-section {
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-light);
}

.recording-config {
  margin: var(--spacing-md) 0;
}

.button-row {
  display: flex;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-xs);
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xs);
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.duration-text {
  font-family: monospace;
  color: var(--text-primary);
}

.recording-tips {
  margin-top: var(--spacing-md);
}

.recording-tips .el-alert {
  --el-alert-padding: 8px 12px;
}

.recording-tips .el-alert__content {
  padding: 0;
}

/* 底部状态栏 */
.audio-center-footer {
  height: 30px;
  padding: 0 var(--spacing-lg);
  background: var(--surface-bg);
  border-top: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
}

.footer-left,
.footer-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.status-text,
.version-info {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

/* 响应式布局 */
.audio-center-workspace.left-panel-hidden .sidebar-left {
  display: none;
}

.audio-center-workspace.right-panel-hidden .sidebar-right {
  display: none;
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--border-extra-light);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--border-light);
}

/* 会议转录格式化显示样式 */
.meeting-result {
  padding: 16px;
  background: var(--bg-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-light);
}

.formatted-text-container {
  max-height: 500px;
  overflow-y: auto;
  background: var(--bg-primary);
  border-radius: 6px;
  padding: 12px;
  border: 1px solid var(--border-light);
}

.formatted-text {
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.6;
  color: var(--text-primary);
  white-space: pre-wrap;
  word-wrap: break-word;
  margin: 0;
}

.formatted-text::-webkit-scrollbar {
  width: 6px;
}

.formatted-text::-webkit-scrollbar-track {
  background: var(--bg-secondary);
  border-radius: 3px;
}

.formatted-text::-webkit-scrollbar-thumb {
  background: var(--border-light);
  border-radius: 3px;
}

.formatted-text::-webkit-scrollbar-thumb:hover {
  background: var(--border-dark);
}
</style>
