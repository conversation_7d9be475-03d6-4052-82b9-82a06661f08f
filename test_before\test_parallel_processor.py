#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
并行处理框架测试脚本
任务#7.1 - 实现多线程并行处理框架

测试功能：
1. 🚀 基础并行处理功能测试
2. 📊 性能监控和资源管理测试  
3. 🔄 批量处理测试
4. 📈 任务调度和优先级测试
5. 🛡️ 异常处理和故障恢复测试
6. 💾 统计信息和指标收集测试

作者: AI Assistant
创建时间: 2025-01-27
任务关联: 任务#7.1 测试
"""

import os
import sys
import time
import random
import logging
from pathlib import Path
from typing import List, Dict, Any
import unittest
from unittest.mock import patch, MagicMock

# 添加utils目录到路径
sys.path.append(str(Path(__file__).parent / "utils"))

try:
    from utils.parallel_processor import (
        ParallelConfig, ParallelProcessor, ResourceMonitor, TaskScheduler,
        create_parallel_processor, parallel_map, parallel_task
    )
    PARALLEL_AVAILABLE = True
except ImportError as e:
    PARALLEL_AVAILABLE = False
    print(f"导入并行处理模块失败: {e}")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ParallelProcessorTester:
    """并行处理器测试器"""
    
    def __init__(self):
        """初始化测试器"""
        self.test_results = {}
        self.performance_metrics = {}
        
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        if not PARALLEL_AVAILABLE:
            return {"error": "并行处理模块不可用"}
        
        logger.info("开始并行处理框架测试...")
        
        # 测试列表
        tests = [
            ("基础配置测试", self.test_basic_config),
            ("资源监控测试", self.test_resource_monitor),
            ("任务调度测试", self.test_task_scheduler), 
            ("并行处理器测试", self.test_parallel_processor),
            ("批量处理测试", self.test_batch_processing),
            ("性能测试", self.test_performance),
            ("异常处理测试", self.test_error_handling),
            ("便利函数测试", self.test_convenience_functions),
        ]
        
        # 执行测试
        for test_name, test_func in tests:
            try:
                logger.info(f"执行测试: {test_name}")
                start_time = time.time()
                result = test_func()
                execution_time = time.time() - start_time
                
                self.test_results[test_name] = {
                    "status": "成功" if result else "失败",
                    "result": result,
                    "execution_time": execution_time
                }
                logger.info(f"测试 {test_name} 完成: {self.test_results[test_name]['status']}")
                
            except Exception as e:
                self.test_results[test_name] = {
                    "status": "错误",
                    "error": str(e),
                    "execution_time": 0
                }
                logger.error(f"测试 {test_name} 出错: {e}")
        
        # 生成报告
        return self.generate_test_report()
    
    def test_basic_config(self) -> bool:
        """测试基础配置功能"""
        try:
            # 测试默认配置
            config = ParallelConfig()
            assert config.max_workers >= 1
            assert config.queue_size > 0
            assert config.timeout > 0
            
            # 测试自定义配置
            custom_config = ParallelConfig(
                max_workers=8,
                queue_size=200,
                timeout=600.0,
                memory_limit_gb=16.0
            )
            assert custom_config.max_workers == 8
            assert custom_config.queue_size == 200
            assert custom_config.timeout == 600.0
            assert custom_config.memory_limit_gb == 16.0
            
            logger.info("基础配置测试通过")
            return True
            
        except Exception as e:
            logger.error(f"基础配置测试失败: {e}")
            return False
    
    def test_resource_monitor(self) -> bool:
        """测试资源监控功能"""
        try:
            config = ParallelConfig(monitor_interval=0.1)
            monitor = ResourceMonitor(config)
            
            # 启动监控
            monitor.start_monitoring()
            time.sleep(0.5)  # 等待收集一些数据
            
            # 检查监控数据
            current_metrics = monitor.get_current_metrics()
            assert current_metrics is not None
            assert current_metrics.cpu_percent >= 0
            assert current_metrics.memory_used_gb >= 0
            
            # 检查历史数据
            history = monitor.get_metrics_history(duration_seconds=1.0)
            assert len(history) > 0
            
            # 检查资源限制
            limits = monitor.check_resource_limits()
            assert isinstance(limits, dict)
            assert "cpu" in limits
            assert "memory" in limits
            
            # 停止监控
            monitor.stop_monitoring()
            
            logger.info("资源监控测试通过")
            return True
            
        except Exception as e:
            logger.error(f"资源监控测试失败: {e}")
            return False
    
    def test_task_scheduler(self) -> bool:
        """测试任务调度功能"""
        try:
            config = ParallelConfig(queue_size=10)
            scheduler = TaskScheduler(config)
            
            # 测试任务提交
            def dummy_task(x):
                return x * 2
            
            task_id = scheduler.submit_task(dummy_task, 5, priority=1)
            assert task_id is not None
            
            # 测试任务获取
            task = scheduler.get_next_task(timeout=1.0)
            assert task is not None
            assert task.task_id == task_id
            assert task.func == dummy_task
            assert task.args == (5,)
            assert task.priority == 1
            
            # 测试任务完成
            scheduler.complete_task(task, result=10)
            assert task.success == True
            assert task.result == 10
            
            # 测试统计信息
            stats = scheduler.get_task_stats()
            assert stats["completed"] == 1
            assert stats["failed"] == 0
            
            logger.info("任务调度测试通过")
            return True
            
        except Exception as e:
            logger.error(f"任务调度测试失败: {e}")
            return False
    
    def test_parallel_processor(self) -> bool:
        """测试并行处理器核心功能"""
        try:
            config = ParallelConfig(max_workers=2, timeout=5.0)
            processor = ParallelProcessor(config)
            
            # 启动处理器
            processor.start()
            time.sleep(0.1)  # 让处理器启动
            
            # 提交测试任务
            def test_task(x):
                time.sleep(0.1)  # 模拟处理时间
                return x ** 2
            
            task_ids = []
            for i in range(5):
                task_id = processor.submit_task(test_task, i, priority=i)
                task_ids.append(task_id)
            
            # 等待任务完成
            max_wait = 10.0
            start_wait = time.time()
            while time.time() - start_wait < max_wait:
                stats = processor.get_performance_stats()
                if stats["completed"] >= 5:
                    break
                time.sleep(0.1)
            
            # 检查结果
            final_stats = processor.get_performance_stats()
            assert final_stats["successful_tasks"] >= 4  # 允许少量失败
            
            # 停止处理器
            processor.stop()
            
            logger.info("并行处理器测试通过")
            return True
            
        except Exception as e:
            logger.error(f"并行处理器测试失败: {e}")
            return False
    
    def test_batch_processing(self) -> bool:
        """测试批量处理功能"""
        try:
            config = ParallelConfig(max_workers=3, timeout=10.0)
            
            # 测试简单批量处理
            def square_task(x):
                time.sleep(0.05)  # 短暂延迟
                return x * x
            
            data = list(range(10))
            expected_results = [x * x for x in data]
            
            with ParallelProcessor(config) as processor:
                results = processor.process_batch(square_task, data)
            
            # 检查结果
            assert len(results) == len(data)
            # 由于并行处理，顺序可能不同，所以检查内容
            success_count = sum(1 for r in results if isinstance(r, int) and r in expected_results)
            assert success_count >= len(data) * 0.8  # 允许80%成功率
            
            logger.info("批量处理测试通过")
            return True
            
        except Exception as e:
            logger.error(f"批量处理测试失败: {e}")
            return False
    
    def test_performance(self) -> bool:
        """测试性能监控和统计"""
        try:
            config = ParallelConfig(max_workers=4, save_metrics=False)
            
            def cpu_task(n):
                # CPU密集型任务
                result = 0
                for i in range(n * 1000):
                    result += i ** 0.5
                return result
            
            start_time = time.time()
            data = [100] * 20  # 20个相同的任务
            
            with ParallelProcessor(config) as processor:
                results = processor.process_batch(cpu_task, data)
                stats = processor.get_performance_stats()
            
            end_time = time.time()
            total_time = end_time - start_time
            
            # 验证性能指标
            assert stats["total_tasks"] >= 20
            assert stats["average_task_time"] > 0
            assert len(results) == 20
            
            # 记录性能指标
            self.performance_metrics["batch_processing"] = {
                "total_time": total_time,
                "tasks_count": len(data),
                "throughput": len(data) / total_time,
                "average_task_time": stats.get("average_task_time", 0),
                "success_rate": stats["successful_tasks"] / stats["total_tasks"] if stats["total_tasks"] > 0 else 0
            }
            
            logger.info("性能测试通过")
            return True
            
        except Exception as e:
            logger.error(f"性能测试失败: {e}")
            return False
    
    def test_error_handling(self) -> bool:
        """测试异常处理功能"""
        try:
            config = ParallelConfig(max_workers=2, timeout=2.0)
            
            def error_task(x):
                if x % 3 == 0:
                    raise ValueError(f"故意错误: {x}")
                elif x % 5 == 0:
                    time.sleep(5)  # 超时任务
                    return x
                else:
                    return x * 2
            
            data = list(range(10))
            
            with ParallelProcessor(config) as processor:
                results = processor.process_batch(error_task, data)
                stats = processor.get_performance_stats()
            
            # 检查结果
            assert len(results) == len(data)
            
            # 统计错误和成功
            errors = sum(1 for r in results if isinstance(r, str) and r.startswith("ERROR"))
            successes = sum(1 for r in results if isinstance(r, int))
            
            assert errors > 0  # 应该有一些错误
            assert successes > 0  # 应该有一些成功
            assert stats["failed_tasks"] > 0  # 统计中应该记录失败
            
            logger.info("异常处理测试通过")
            return True
            
        except Exception as e:
            logger.error(f"异常处理测试失败: {e}")
            return False
    
    def test_convenience_functions(self) -> bool:
        """测试便利函数"""
        try:
            # 测试创建处理器函数
            processor = create_parallel_processor(max_workers=2)
            assert isinstance(processor, ParallelProcessor)
            assert processor.config.max_workers == 2
            
            # 测试并行映射函数
            def simple_task(x):
                return x + 1
            
            data = [1, 2, 3, 4, 5]
            results = parallel_map(simple_task, data, max_workers=2)
            
            # 检查结果
            assert len(results) == len(data)
            success_results = [r for r in results if isinstance(r, int)]
            assert len(success_results) >= len(data) * 0.8  # 至少80%成功
            
            # 测试装饰器
            @parallel_task(priority=5, timeout=10.0)
            def decorated_task(x):
                return x * 3
            
            assert hasattr(decorated_task, '_parallel_priority')
            assert decorated_task._parallel_priority == 5
            assert decorated_task._parallel_timeout == 10.0
            
            logger.info("便利函数测试通过")
            return True
            
        except Exception as e:
            logger.error(f"便利函数测试失败: {e}")
            return False
    
    def generate_test_report(self) -> Dict[str, Any]:
        """生成测试报告"""
        total_tests = len(self.test_results)
        successful_tests = sum(1 for r in self.test_results.values() if r["status"] == "成功")
        failed_tests = sum(1 for r in self.test_results.values() if r["status"] == "失败")
        error_tests = sum(1 for r in self.test_results.values() if r["status"] == "错误")
        
        total_time = sum(r.get("execution_time", 0) for r in self.test_results.values())
        
        report = {
            "summary": {
                "total_tests": total_tests,
                "successful": successful_tests,
                "failed": failed_tests,
                "errors": error_tests,
                "success_rate": successful_tests / total_tests if total_tests > 0 else 0,
                "total_execution_time": total_time
            },
            "detailed_results": self.test_results,
            "performance_metrics": self.performance_metrics,
            "recommendations": self._generate_recommendations()
        }
        
        return report
    
    def _generate_recommendations(self) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # 检查成功率
        successful_tests = sum(1 for r in self.test_results.values() if r["status"] == "成功")
        total_tests = len(self.test_results)
        success_rate = successful_tests / total_tests if total_tests > 0 else 0
        
        if success_rate < 0.8:
            recommendations.append("测试成功率较低，建议检查系统环境和依赖")
        
        # 检查性能指标
        if "batch_processing" in self.performance_metrics:
            perf = self.performance_metrics["batch_processing"]
            if perf["success_rate"] < 0.9:
                recommendations.append("批量处理成功率较低，建议调整超时时间或检查任务复杂度")
            if perf["throughput"] < 1.0:
                recommendations.append("处理吞吐量较低，建议增加工作器数量或优化任务")
        
        if not recommendations:
            recommendations.append("所有测试都表现良好，并行处理框架运行正常")
        
        return recommendations

def main():
    """主函数"""
    print("=" * 80)
    print("并行处理框架测试 - 任务#7.1")
    print("=" * 80)
    
    if not PARALLEL_AVAILABLE:
        print("❌ 并行处理模块不可用，请检查安装")
        return
    
    # 创建测试器
    tester = ParallelProcessorTester()
    
    # 运行测试
    report = tester.run_all_tests()
    
    # 打印报告
    print("\n" + "=" * 40)
    print("测试报告")
    print("=" * 40)
    
    summary = report["summary"]
    print(f"总测试数: {summary['total_tests']}")
    print(f"成功: {summary['successful']} ✅")
    print(f"失败: {summary['failed']} ❌")
    print(f"错误: {summary['errors']} ⚠️")
    print(f"成功率: {summary['success_rate']:.1%}")
    print(f"总执行时间: {summary['total_execution_time']:.2f}秒")
    
    # 详细结果
    print("\n详细测试结果:")
    for test_name, result in report["detailed_results"].items():
        status_icon = "✅" if result["status"] == "成功" else "❌" if result["status"] == "失败" else "⚠️"
        print(f"  {status_icon} {test_name}: {result['status']} ({result.get('execution_time', 0):.2f}s)")
        if result["status"] == "错误":
            print(f"    错误: {result.get('error', 'Unknown')}")
    
    # 性能指标
    if report["performance_metrics"]:
        print("\n性能指标:")
        for metric_name, metrics in report["performance_metrics"].items():
            print(f"  {metric_name}:")
            for key, value in metrics.items():
                if isinstance(value, float):
                    print(f"    {key}: {value:.3f}")
                else:
                    print(f"    {key}: {value}")
    
    # 建议
    print("\n优化建议:")
    for rec in report["recommendations"]:
        print(f"  • {rec}")
    
    print("\n" + "=" * 80)
    if summary['success_rate'] >= 0.8:
        print("🎉 并行处理框架测试总体通过！")
    else:
        print("⚠️ 并行处理框架测试存在问题，请检查相关配置")
    print("=" * 80)

if __name__ == "__main__":
    main() 