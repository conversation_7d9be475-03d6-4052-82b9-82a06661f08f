#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的Celery Worker启动脚本
解决Windows编码和虚拟环境问题
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# 设置日志 - 避免emoji字符
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('worker_simple.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def setup_environment():
    """设置环境变量"""
    try:
        # CUDA内存管理
        os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:256'
        os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
        
        # Python设置
        os.environ['PYTHONUNBUFFERED'] = '1'
        os.environ['PYTHONIOENCODING'] = 'utf-8'
        
        # 禁用可能导致冲突的功能
        os.environ['TOKENIZERS_PARALLELISM'] = 'false'
        os.environ['OMP_NUM_THREADS'] = '2'
        
        logger.info("环境变量设置完成")
        return True
        
    except Exception as e:
        logger.error(f"环境设置失败: {e}")
        return False

def check_virtual_env():
    """检查虚拟环境"""
    try:
        # 检查是否在虚拟环境中
        venv_path = Path('.venv')
        if not venv_path.exists():
            logger.error("虚拟环境不存在: .venv")
            return False
        
        # 检查Python路径
        python_exe = venv_path / 'Scripts' / 'python.exe'
        if not python_exe.exists():
            logger.error(f"Python可执行文件不存在: {python_exe}")
            return False
        
        logger.info(f"虚拟环境检查通过: {python_exe}")
        return str(python_exe)
        
    except Exception as e:
        logger.error(f"虚拟环境检查失败: {e}")
        return False

def start_worker():
    """启动Worker"""
    try:
        # 设置环境
        if not setup_environment():
            return False
        
        # 检查虚拟环境
        python_exe = check_virtual_env()
        if not python_exe:
            return False
        
        # 构建命令 - 修正参数顺序
        cmd = [
            python_exe, '-m', 'celery',
            '-A', 'backend.core.task_queue:celery_app',  # -A 参数移到前面
            'worker',
            '--loglevel=info',
            '--pool=threads',
            '--concurrency=2',
            '--max-tasks-per-child=5',
            '--queues=audio_processing,default',
            '--hostname=audio_worker@%h'
        ]
        
        logger.info(f"启动Worker命令: {' '.join(cmd)}")
        
        # 启动进程
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1,
            encoding='utf-8'
        )
        
        logger.info(f"Worker进程已启动，PID: {process.pid}")
        
        # 实时输出日志
        try:
            for line in iter(process.stdout.readline, ''):
                if line:
                    print(line.strip())
                    
                    # 检查错误
                    if any(error in line.lower() for error in [
                        'error', 'failed', 'exception', 'traceback'
                    ]):
                        logger.warning(f"检测到错误: {line.strip()}")
                        
        except KeyboardInterrupt:
            logger.info("收到中断信号，正在停止Worker...")
            process.terminate()
            process.wait()
            logger.info("Worker已停止")
            
        return True
        
    except Exception as e:
        logger.error(f"Worker启动失败: {e}")
        return False

if __name__ == "__main__":
    try:
        logger.info("启动简化Worker管理器")
        success = start_worker()
        sys.exit(0 if success else 1)
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        sys.exit(1)
