# 代码风格和约定

## Python后端代码风格

### 命名约定
- **文件名**: 小写字母 + 下划线 (`audio_processing_tasks.py`, `speech_recognition_core.py`)
- **类名**: 大驼峰命名 (`AudioProcessor`, `SpeechRecognitionManager`, `OptimizedFunASRManager`)
- **函数名**: 小写字母 + 下划线 (`process_audio_file`, `meeting_transcription_task`)
- **变量名**: 小写字母 + 下划线 (`file_path`, `user_id`, `model_path`)
- **常量**: 大写字母 + 下划线 (`MAX_FILE_SIZE`, `SUPPORTED_FORMATS`)
- **私有函数**: 下划线前缀 (`_get_model_path`, `_validate_all_model_paths`)

### 类型提示 (强制要求)
```python
# 函数类型提示
def meeting_transcription_task(
    task_id: str,
    user_id: str,
    file_ids: List[str],
    language: str = "auto",
    expected_speakers: int = 2
) -> Dict[str, Any]:
    pass

# 类属性类型提示
class SpeechRecognitionManager:
    model_path: str
    device: torch.device
    config: SpeechRecognitionConfig
    
# 使用Union和Optional
from typing import Union, Optional, List, Dict, Any
```

### 文档字符串 (中英文混合)
```python
def meeting_transcription_task(
    self,
    task_id: str,
    user_id: str,
    file_ids: List[str]
) -> Dict[str, Any]:
    """
    会议转录任务（综合VAD、说话人识别和语音识别）
    
    Args:
        task_id: 任务唯一标识符
        user_id: 用户ID
        file_ids: 音频文件ID列表
        
    Returns:
        Dict[str, Any]: 转录结果字典，包含说话人对话格式
        
    Raises:
        ValueError: 模型路径验证失败或文件不存在
        ModelLoadError: 模型加载失败
    """
    pass
```

### 异常处理模式
```python
# 使用具体的异常类型和中文错误信息
try:
    result = _perform_meeting_transcription(file_path, ...)
except FileNotFoundError as e:
    logger.error(f"音频文件不存在: {e}")
    raise ValueError(f"没有找到有效的音频文件")
except Exception as e:
    logger.error(f"会议转录处理失败: {e}")
    raise ValueError(f"会议转录处理失败: {e}")
```

### 日志记录规范
```python
import logging

logger = logging.getLogger(__name__)

# 使用中文日志信息和结构化格式
logger.info("🔧 设置完全离线环境变量...")
logger.info(f"[OK] 会议转录Task status updated to completed: {task_id}")
logger.error(f"会议转录Task failed: {e}")
logger.warning(f"会议转录WebSocket notification failed: {ws_error}")

# 使用表情符号增强可读性
logger.info("✅ 离线环境变量设置完成")
logger.info("🎯 开始执行会议转录任务")
```

### 函数组织模式
```python
# 公共函数
@celery_app.task(bind=True, base=BaseTask)
def meeting_transcription_task(self, ...):
    """公共任务入口"""
    pass

# 私有辅助函数
def _setup_offline_environment():
    """设置完全离线环境"""
    pass

def _validate_all_model_paths() -> bool:
    """验证所有模型路径有效性"""
    pass

def _get_model_path(model_type: str) -> str:
    """获取指定类型模型的路径"""
    pass
```

## 前端代码风格 (Vue 3)

### 命名约定
- **文件名**: 大驼峰命名 (`AudioCenter.vue`, `AudioConfigPanel.vue`)
- **组件名**: 大驼峰命名 (`<AudioConfigPanel />`, `<ProcessingProgress />`)
- **变量名**: 小驼峰命名 (`audioFiles`, `processingStatus`, `meetingTranscriptionConfig`)
- **常量**: 大写字母 + 下划线 (`API_BASE_URL`, `WEBSOCKET_URL`)
- **方法名**: 小驼峰命名 (`formatMeetingTranscriptionText`, `handleFileUpload`)

### 组件结构标准
```vue
<template>
  <!-- 使用Element Plus组件和统一样式 -->
  <el-card class="audio-config-panel">
    <template #header>
      <span class="card-title">音频处理配置</span>
    </template>
    <!-- 内容 -->
  </el-card>
</template>

<script setup>
// 导入顺序：Vue核心 -> 第三方库 -> 本地模块
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useAudioStore } from '@/stores/audio'

// 响应式数据
const audioFiles = ref([])
const processingConfig = reactive({
  language: 'auto',
  expectedSpeakers: 2,
  outputFormat: 'timeline'
})

// 计算属性
const isProcessing = computed(() => audioStore.isProcessing)

// 方法定义
const formatMeetingTranscriptionText = (result) => {
  // 实现会议转录文本格式化
  if (!result || !result.segments) {
    return '暂无转录结果'
  }
  // 处理逻辑...
}

// 生命周期
onMounted(async () => {
  await loadAudioFiles()
})
</script>

<style scoped>
/* 使用SCSS和CSS变量 */
.audio-config-panel {
  margin-bottom: 20px;
  
  .card-title {
    font-size: 16px;
    font-weight: 600;
    color: var(--el-text-color-primary);
  }
}
</style>
```

### API调用约定
```javascript
// 使用统一的API封装和错误处理
import { audioAPI } from '@/api/audio'

const submitMeetingTranscription = async () => {
  try {
    processingStatus.isProcessing = true
    
    const response = await audioAPI.submitMeetingTranscription({
      fileIds: selectedFiles.value.map(f => f.id),
      language: config.language,
      expectedSpeakers: config.expectedSpeakers
    })
    
    ElMessage.success('会议转录任务已提交')
    return response.data
    
  } catch (error) {
    console.error('会议转录提交失败:', error)
    ElMessage.error(error.message || '会议转录提交失败')
    throw error
  } finally {
    processingStatus.isProcessing = false
  }
}
```

## 项目结构约定

### 目录组织原则
```
backend/
├── api/v1/endpoints/    # API端点 (按功能模块分组)
│   ├── speech.py       # 语音处理相关API
│   ├── audio.py        # 音频文件管理API
│   └── auth.py         # 认证相关API
├── core/                # 核心配置和工具
├── models/              # 数据库模型
├── services/            # 业务逻辑服务
├── tasks/               # Celery异步任务
│   └── audio_processing_tasks.py  # 音频处理任务
├── utils/               # 工具模块
│   └── audio/          # 音频处理工具
└── schemas/             # Pydantic数据验证模型

frontend/src/
├── api/                 # API接口封装
├── components/          # 可复用组件
│   └── audio/          # 音频处理组件
├── composables/         # 组合式函数
├── stores/              # Pinia状态管理
├── utils/               # 工具函数
└── views/               # 页面组件
    └── AudioCenter.vue  # 音频处理中心
```

### 导入顺序规范
```python
# Python导入顺序
# 1. 标准库
import os
import sys
import time
from pathlib import Path
from datetime import datetime, timezone

# 2. 第三方库
from fastapi import FastAPI, HTTPException
import torch
from celery import Celery

# 3. 本地模块 (按层级顺序)
from backend.core.config import settings
from backend.models.audio import AudioFile
from backend.services.progress_service import ProgressCallback
from backend.utils.audio.speech_recognition_core import SpeechRecognitionManager
```

## 配置管理规范

### 环境变量管理
```python
# 离线环境变量设置
def _setup_offline_environment():
    """设置完全离线环境"""
    offline_vars = {
        'HF_HUB_OFFLINE': '1',
        'HF_DATASETS_OFFLINE': '1',
        'TRANSFORMERS_OFFLINE': '1',
        'HF_HUB_DISABLE_TELEMETRY': '1',
        'HF_HUB_DISABLE_PROGRESS_BARS': '1',
        'HF_HUB_DISABLE_SYMLINKS_WARNING': '1',
    }
    
    for var, value in offline_vars.items():
        os.environ[var] = value
        logger.info(f"  设置 {var}={value}")
```

### 模型路径配置
```python
# 统一的模型路径管理
def _get_model_path(model_type: str) -> str:
    """获取指定类型模型的路径"""
    model_paths = {
        'sensevoice': os.environ.get('SENSEVOICE_MODEL_PATH', 
                                   os.path.join(settings.models_base_path, 'SenseVoiceSmall')),
        'vad': os.environ.get('VAD_MODEL_PATH', 
                            os.path.join(settings.models_base_path, 'fsmn_vad_zh')),
        'speaker': os.environ.get('SPEAKER_MODEL_PATH', 
                                os.path.join(settings.models_base_path, 'cam++'))
    }
    return model_paths.get(model_type, '')
```

## 错误处理约定

### 统一错误响应格式
```python
# 后端错误响应
{
    "error": "MEETING_TRANSCRIPTION_FAILED",
    "message": "会议转录处理失败",
    "details": "模型路径验证失败，请检查模型配置",
    "timestamp": "2024-01-01T12:00:00Z",
    "task_id": "task_123456"
}
```

### 前端错误处理模式
```javascript
// 统一错误处理和用户友好提示
const handleProcessingError = (error) => {
  console.error('音频处理失败:', error)
  
  let errorMessage = '处理失败，请重试'
  
  if (error.response?.data?.message) {
    errorMessage = error.response.data.message
  } else if (error.message) {
    errorMessage = error.message
  }
  
  ElMessage({
    type: 'error',
    message: errorMessage,
    duration: 5000,
    showClose: true
  })
}
```

## 性能优化约定

### 异步处理模式
```python
# Celery任务装饰器标准配置
@celery_app.task(bind=True, base=BaseTask)
def meeting_transcription_task(self, ...):
    """会议转录任务"""
    progress_callback = ProgressCallback(task_id, self)
    
    try:
        # 任务执行逻辑
        progress_callback(0, "开始会议转录任务", "initializing")
        # ...
        progress_callback(100.0, "会议转录任务完成", "completed")
        
    except Exception as e:
        progress_callback(0, f"Task failed: {str(e)}", "failed")
        raise
```

### 前端性能优化
```javascript
// 使用防抖和节流
import { debounce } from 'lodash-es'

const debouncedSearch = debounce((query) => {
  // 搜索逻辑
}, 300)

// 组件懒加载
const AudioConfigPanel = defineAsyncComponent(() => 
  import('@/components/audio/AudioConfigPanel.vue')
)
```

## 代码注释规范

### 中文注释优先
```python
# 优先使用中文注释，便于团队理解
def _perform_meeting_transcription(
    file_path: str,
    vad_model,
    recognizer,
    speaker_recognizer,
    # ... 其他参数
):
    """
    执行会议转录的核心逻辑
    
    这个函数整合了VAD检测、语音识别和说话人识别，
    实现完整的会议转录流程，包括文本-时间-说话人三元组关联。
    """
    
    # 1. VAD检测 - 识别语音活动段
    vad_segments = detect_voice_activity(file_path, vad_model)
    
    # 2. 语音识别 - 为每个VAD片段进行转录
    transcription_results = []
    for segment in vad_segments:
        # 对单个片段进行语音识别
        text = recognizer.transcribe_segment(segment)
        transcription_results.append(text)
    
    # 3. 说话人识别和聚类
    speaker_labels = identify_speakers(vad_segments, speaker_recognizer)
    
    # 4. 结果整合 - 生成最终的对话格式
    return format_meeting_results(transcription_results, speaker_labels, vad_segments)
```

### 关键算法说明
```python
# 对复杂算法提供详细说明
def align_text_with_segments(
    transcription_results: List[str],
    vad_segments: List[Dict],
    speaker_labels: List[int]
) -> List[Dict]:
    """
    文本-时间-说话人三元组关联算法
    
    核心思路：
    1. 确保transcription_results、vad_segments、speaker_labels长度一致
    2. 按时间顺序对齐每个VAD片段的文本和说话人标签
    3. 生成包含开始时间、结束时间、文本内容、说话人ID的结构化数据
    
    这是解决会议转录核心问题的关键算法。
    """
    pass
```