#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
监控组件集成测试脚本
演示监控组件在语音处理中的使用效果
"""

import sys
import os
import time
import numpy as np
import soundfile as sf
import tempfile
from pathlib import Path

# 添加utils目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))

try:
    from utils.monitoring_components import (
        ProcessingMonitor, MonitoringComponents, IntegratedMonitorWidget
    )
    print("✅ 监控组件导入成功")
except ImportError as e:
    print(f"❌ 监控组件导入失败: {e}")
    sys.exit(1)

def create_test_audio(duration=10.0, sample_rate=16000):
    """创建测试音频文件"""
    print(f"🎵 创建测试音频: {duration}秒, {sample_rate}Hz")
    
    # 生成测试音频信号
    t = np.linspace(0, duration, int(duration * sample_rate))
    
    # 创建语音-静音-语音模式
    speech_freq = 440  # A4音符
    speech1 = np.sin(2 * np.pi * speech_freq * t[:int(3 * sample_rate)])  # 0-3秒
    silence = np.zeros(int(4 * sample_rate))  # 3-7秒静音
    speech2 = np.sin(2 * np.pi * speech_freq * t[:int(3 * sample_rate)]) * 0.8  # 7-10秒
    
    audio = np.concatenate([speech1, silence, speech2])
    
    # 保存临时音频文件
    temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.wav')
    sf.write(temp_file.name, audio, sample_rate)
    
    print(f"✅ 测试音频已创建: {temp_file.name}")
    return temp_file.name

def simulate_vad_processing(monitor_widget, task_id, audio_path):
    """模拟VAD处理过程"""
    print(f"🎯 开始模拟VAD处理: {task_id}")
    
    try:
        # 开始监控
        monitor_widget.monitor.start_monitoring()
        
        # 步骤1: 初始化
        monitor_widget.update_progress(task_id, 10.0, 'running', {
            'stage': '初始化',
            'action': '准备VAD处理环境'
        })
        time.sleep(1)
        
        # 步骤2: 读取音频
        monitor_widget.update_progress(task_id, 25.0, 'running', {
            'stage': '读取音频',
            'action': '加载音频文件并验证格式'
        })
        
        # 模拟读取音频信息
        audio_info = sf.info(audio_path)
        monitor_widget.monitor.add_performance_metric(
            "audio_duration", audio_info.duration, task_id,
            {'sample_rate': audio_info.samplerate, 'channels': audio_info.channels}
        )
        time.sleep(1.5)
        
        # 步骤3: 加载VAD模型
        monitor_widget.update_progress(task_id, 50.0, 'running', {
            'stage': '加载VAD模型',
            'action': '初始化语音活动检测模型'
        })
        time.sleep(2)
        
        # 步骤4: 执行VAD分割
        monitor_widget.update_progress(task_id, 75.0, 'running', {
            'stage': 'VAD分割执行',
            'action': '分析音频中的语音活动'
        })
        
        # 模拟VAD处理时间
        processing_time = 3.0
        time.sleep(processing_time)
        
        # 模拟检测结果
        segments_detected = 2  # 模拟检测到2个语音段
        
        monitor_widget.monitor.add_performance_metric(
            "vad_processing_time", processing_time, task_id,
            {'segments_detected': segments_detected, 'method': '两人对话优化VAD'}
        )
        
        # 步骤5: 完成处理
        monitor_widget.update_progress(task_id, 100.0, 'completed', {
            'stage': 'VAD处理完成',
            'segments_count': segments_detected,
            'processing_time': processing_time,
            'success': True
        })
        
        # 完成任务
        monitor_widget.complete_task(
            task_id,
            processing_time=processing_time,
            success=True,
            details={
                'segments_detected': segments_detected,
                'method': '两人对话优化VAD',
                'audio_duration': audio_info.duration
            }
        )
        
        print(f"✅ VAD处理完成: 检测到 {segments_detected} 个语音段")
        return segments_detected
        
    except Exception as e:
        # 处理失败
        monitor_widget.complete_task(task_id, success=False, details={'error': str(e)})
        monitor_widget.monitor.add_error(f"VAD处理失败: {str(e)}", task_id, 'VAD_PROCESSING_ERROR')
        print(f"❌ VAD处理失败: {e}")
        return None

def simulate_batch_processing(monitor_widget, num_files=3):
    """模拟批量处理"""
    print(f"🔄 开始模拟批量处理: {num_files} 个文件")
    
    # 创建多个测试音频文件
    test_files = []
    for i in range(num_files):
        duration = 5 + i * 2  # 不同长度的音频
        audio_path = create_test_audio(duration, 16000)
        test_files.append((f"test_audio_{i+1}.wav", audio_path))
    
    batch_results = []
    
    try:
        for i, (filename, audio_path) in enumerate(test_files, 1):
            task_id = f"batch_{i}_{int(time.time())}"
            
            print(f"📁 处理文件 {i}/{num_files}: {filename}")
            
            # 开始批处理任务
            monitor_widget.update_progress(task_id, 0.0, 'running', {
                'filename': filename,
                'batch_index': i,
                'total_files': num_files,
                'mode': 'VAD语音活动检测'
            })
            
            # 执行处理
            result = simulate_vad_processing(monitor_widget, task_id, audio_path)
            
            if result is not None:
                batch_results.append({
                    'filename': filename,
                    'status': 'success',
                    'segments': result
                })
            else:
                batch_results.append({
                    'filename': filename,
                    'status': 'failed'
                })
        
        print(f"✅ 批量处理完成: {len(batch_results)} 个文件")
        return batch_results
        
    finally:
        # 清理临时文件
        for _, audio_path in test_files:
            try:
                os.unlink(audio_path)
            except:
                pass

def display_monitoring_stats(monitor_widget):
    """显示监控统计信息"""
    print("\n" + "="*50)
    print("📊 监控统计信息")
    print("="*50)
    
    stats = monitor_widget.monitor.get_stats()
    
    print(f"总任务数: {stats['total_tasks']}")
    print(f"已完成: {stats['completed']}")
    print(f"失败: {stats['failed']}")
    print(f"进行中: {stats['in_progress']}")
    print(f"成功率: {stats['success_rate']:.1f}%")
    print(f"平均速度: {stats['average_speed']:.2f} 任务/秒")
    print(f"总时间: {stats['total_time']:.2f} 秒")
    print(f"当前内存: {stats['current_memory_mb']:.1f} MB")
    print(f"峰值内存: {stats['peak_memory_mb']:.1f} MB")
    print(f"CPU使用率: {stats['cpu_percent']:.1f}%")
    
    # 显示性能指标
    if monitor_widget.monitor.performance_data:
        print("\n📈 性能指标:")
        for metric in monitor_widget.monitor.performance_data[-5:]:  # 显示最近5个指标
            print(f"  {metric['metric']}: {metric['value']:.2f} ({metric['timestamp'][:19]})")
    
    # 显示错误日志
    if monitor_widget.monitor.error_log:
        print("\n🚨 错误日志:")
        for error in monitor_widget.monitor.error_log[-3:]:  # 显示最近3个错误
            print(f"  [{error['type']}] {error['message'][:50]}...")

def main():
    """主测试函数"""
    print("🧪 监控组件集成测试")
    print("="*50)
    
    # 创建监控小部件
    monitor_widget = IntegratedMonitorWidget("test_monitoring")
    
    # 测试1: 单文件处理
    print("\n🎯 测试1: 单文件VAD处理")
    test_audio_path = create_test_audio(8.0, 16000)
    
    try:
        task_id = f"single_test_{int(time.time())}"
        result = simulate_vad_processing(monitor_widget, task_id, test_audio_path)
        print(f"单文件处理结果: {result}")
    finally:
        os.unlink(test_audio_path)
    
    # 等待一下查看监控数据
    time.sleep(2)
    
    # 测试2: 批量处理
    print("\n🔄 测试2: 批量文件处理")
    batch_results = simulate_batch_processing(monitor_widget, 3)
    print(f"批量处理结果: {len(batch_results)} 个文件")
    
    # 等待系统监控收集数据
    time.sleep(3)
    
    # 显示最终统计
    display_monitoring_stats(monitor_widget)
    
    # 停止监控
    monitor_widget.monitor.stop_monitoring()
    
    print("\n✅ 测试完成!")
    print("监控组件已成功集成到语音处理系统中")
    print("主要功能:")
    print("- ✅ 实时任务状态跟踪")
    print("- ✅ 性能指标收集")
    print("- ✅ 错误日志记录")
    print("- ✅ 系统资源监控")
    print("- ✅ 批量处理支持")

if __name__ == "__main__":
    main()