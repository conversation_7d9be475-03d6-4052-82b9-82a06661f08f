/**
 * 进度监控组合式函数
 * 支持WebSocket实时推送和轮询回退机制
 */

import { ref, onUnmounted } from 'vue'
import { getTaskProgress } from '@/api/document'
import wsManager from '@/utils/websocket'
import { getToken } from '@/utils/auth' // 使用统一的token获取函数

export function useProgressMonitor() {
  // 使用统一的token获取函数，与DocumentManager保持一致
  
  // 状态管理
  const isWebSocketEnabled = ref(false)
  const isWebSocketConnected = ref(false)
  const activeMonitors = ref(new Map())
  const pollingIntervals = ref(new Map())

  // 🔧 新增：任务完成检测机制
  const completionCheckIntervals = ref(new Map())
  const taskCompletionCache = ref(new Map())
  
  /**
   * 初始化进度监控
   */
  const initializeMonitor = async () => {
    try {
      // 尝试连接WebSocket
      const token = getToken()
      console.log('🔑 获取到的token:', token ? `有效 (${token.substring(0, 20)}...)` : '无效')

      // 调试：检查token来源
      if (!token) {
        console.log('🔍 检查token存储位置...')
        const localToken = localStorage.getItem('token') || localStorage.getItem('access_token')
        const sessionToken = sessionStorage.getItem('token') || sessionStorage.getItem('access_token')
        console.log('LocalStorage token:', localToken ? `存在 (${localToken.substring(0, 20)}...)` : '不存在')
        console.log('SessionStorage token:', sessionToken ? `存在 (${sessionToken.substring(0, 20)}...)` : '不存在')
      }

      if (token) {
        console.log('🔌 开始连接WebSocket...')
        await wsManager.connect(token)

        // 检查实际连接状态
        const status = wsManager.getStatus()
        console.log('🔍 WebSocket连接状态:', status)

        if (status.isConnected) {
          isWebSocketEnabled.value = true
          isWebSocketConnected.value = true
          console.log('✅ WebSocket进度监控已启用')
          return true
        } else {
          throw new Error('WebSocket连接未建立')
        }
      } else {
        throw new Error('没有有效的认证token')
      }
    } catch (error) {
      console.warn('⚠️ WebSocket连接失败，将使用轮询模式:', error)
      isWebSocketEnabled.value = false
      isWebSocketConnected.value = false
    }

    return false
  }

  /**
   * 开始监控任务进度
   */
  const startMonitoring = async (taskId, callbacks = {}) => {
    const {
      onProgress = () => {},
      onCompleted = () => {},
      onFailed = () => {},
      onCancelled = () => {}
    } = callbacks

    console.log(`🔄 开始监控任务: ${taskId}`)
    console.log(`🔍 WebSocket状态检查: enabled=${isWebSocketEnabled.value}, connected=${isWebSocketConnected.value}`)

    // 如果已经在监控，先停止
    if (activeMonitors.value.has(taskId)) {
      stopMonitoring(taskId)
    }

    // 创建监控器配置
    const monitor = {
      taskId,
      callbacks,
      isActive: true,
      lastUpdate: Date.now(),
      retryCount: 0,
      maxRetries: 15, // 增加重试次数，给任务更多时间启动
      startTime: Date.now() // 记录开始时间
    }

    activeMonitors.value.set(taskId, monitor)

    // 🔧 启动任务完成检测机制（渐进式降级策略）
    startCompletionCheck(taskId, monitor)

    // 尝试使用WebSocket
    if (isWebSocketEnabled.value && isWebSocketConnected.value) {
      console.log(`✅ 使用WebSocket监控任务: ${taskId}`)
      try {
        await startWebSocketMonitoring(taskId, monitor)
        return
      } catch (error) {
        console.warn('⚠️ WebSocket监控失败，切换到轮询模式:', error)
        isWebSocketConnected.value = false
      }
    } else {
      console.log(`⚠️ WebSocket不可用，使用轮询模式: enabled=${isWebSocketEnabled.value}, connected=${isWebSocketConnected.value}`)
    }

    // 回退到轮询模式
    startPollingMonitoring(taskId, monitor)
  }

  /**
   * 映射后端阶段到前端显示阶段
   * 🔧 修复：与DocumentManager保持一致的阶段映射
   */
  const mapBackendStageToFrontend = (backendStage, percentage = 0) => {
    const stageMap = {
      // 文档处理阶段 - 与DocumentManager中的processingStages保持一致
      'upload': { stage: 'upload', name: '文件上传', baseProgress: 5 },
      'uploading': { stage: 'upload', name: '文件上传', baseProgress: 5 },
      'parsing': { stage: 'analysis', name: '文档分析', baseProgress: 20 },
      'analysis': { stage: 'analysis', name: '文档分析', baseProgress: 25 },
      'analyzing': { stage: 'analysis', name: '文档分析', baseProgress: 25 },
      'document_analysis': { stage: 'analysis', name: '文档分析', baseProgress: 25 },
      'ocr': { stage: 'ocr', name: 'OCR处理', baseProgress: 40 },
      'ocr_processing': { stage: 'ocr', name: 'OCR处理', baseProgress: 50 },
      'processing': { stage: 'ocr', name: 'OCR处理', baseProgress: 50 },
      'splitting': { stage: 'splitting', name: '智能切分', baseProgress: 70 },
      'text_splitting': { stage: 'splitting', name: '智能切分', baseProgress: 70 },
      'chunking': { stage: 'splitting', name: '智能切分', baseProgress: 75 },
      'vectorizing': { stage: 'splitting', name: '智能切分', baseProgress: 75 },
      'indexing': { stage: 'indexing', name: '建立索引', baseProgress: 90 },
      'vectorization': { stage: 'indexing', name: '建立索引', baseProgress: 90 },
      'embedding': { stage: 'indexing', name: '建立索引', baseProgress: 90 },
      'completed': { stage: 'complete', name: '处理完成', baseProgress: 100 },
      'complete': { stage: 'complete', name: '处理完成', baseProgress: 100 },
      'finished': { stage: 'complete', name: '处理完成', baseProgress: 100 },
      'failed': { stage: 'upload', name: '处理失败', baseProgress: 0 },

      // Celery任务状态
      'PENDING': { stage: 'upload', name: '等待处理', baseProgress: 5 },
      'PROGRESS': {
        stage: percentage < 25 ? 'upload' : percentage < 50 ? 'analysis' : percentage < 70 ? 'ocr' : percentage < 85 ? 'splitting' : 'indexing',
        name: percentage < 25 ? '文件上传' : percentage < 50 ? '文档分析' : percentage < 70 ? 'OCR处理' : percentage < 85 ? '智能切分' : '建立索引',
        baseProgress: percentage || 20
      },
      'SUCCESS': { stage: 'complete', name: '处理完成', baseProgress: 100 },
      'FAILURE': { stage: 'upload', name: '处理失败', baseProgress: 0 }
    }

    return stageMap[backendStage] || { stage: 'upload', name: '处理中', baseProgress: percentage || 10 }
  }

  /**
   * 将WebSocket数据转换为标准进度格式
   * 🔧 修复：重写以正确处理统一的消息格式 {type, task_id, data}
   */
  const convertWebSocketDataToProgress = (payload) => {
    console.log('🔄 转换WebSocket payload:', payload)

    let percentage = 0
    let detail = '处理中...'
    let status = 'pending'
    let taskData = payload

    // 🔧 修复：处理新的统一消息格式
    // 新格式: { task_id: 'xxx', ...data }，其中data可能包含：
    // - progress: { percentage: 60, detail: '...', stage: '...' }
    // - state: 'PROGRESS', percentage: 60, detail: '...'
    // - 或直接的进度字段

    // 1. 优先从progress字段提取数据（向量化任务使用此格式）
    if (payload.progress && typeof payload.progress === 'object') {
      taskData = payload.progress
      console.log('📊 使用progress字段中的数据:', taskData)

      percentage = taskData.percentage || 0

      // 🔧 使用阶段映射
      const stageInfo = mapBackendStageToFrontend(taskData.stage, percentage)
      detail = taskData.detail || stageInfo.name
      status = stageInfo.stage === 'completed' ? 'completed' :
               stageInfo.stage === 'failed' ? 'failed' : 'progress'

      // 如果没有具体百分比，使用阶段基础进度
      if (percentage === 0 && stageInfo.baseProgress > 0) {
        percentage = stageInfo.baseProgress
      }
    }
    // 2. 从state字段提取数据（Celery任务格式）
    else if (payload.state || payload.status) {
      const taskStatus = payload.state || payload.status
      console.log('📊 使用state字段中的数据:', taskStatus)

      percentage = payload.percentage || 0

      // 🔧 使用阶段映射
      const stageInfo = mapBackendStageToFrontend(taskStatus, percentage)
      detail = payload.detail || stageInfo.name
      status = stageInfo.stage === 'completed' ? 'completed' :
               stageInfo.stage === 'failed' ? 'failed' :
               stageInfo.stage === 'upload' ? 'pending' : 'progress'

      // 如果没有具体百分比，使用阶段基础进度
      if (percentage === 0 && stageInfo.baseProgress > 0) {
        percentage = stageInfo.baseProgress
      }

      // 处理错误信息
      if (taskStatus === 'FAILURE' && payload.error) {
        detail = payload.error
      }
    }
    // 3. 直接从payload提取（简化格式）
    else {
      percentage = payload.percentage || 0
      detail = payload.detail || payload.message || '处理中...'

      if (payload.stage === 'completed' || percentage >= 100) {
        status = 'completed'
        percentage = 100
      } else if (payload.stage === 'failed' || payload.error) {
        status = 'failed'
        percentage = 0
        detail = payload.error || detail
      } else {
        status = percentage > 0 ? 'progress' : 'pending'
      }
    }

    // 确保百分比在有效范围内
    percentage = Math.max(0, Math.min(100, percentage))

    console.log('📊 提取的进度数据:', { percentage, detail, status })

    // 🔧 构建标准化的返回结果
    const result = {
      percentage,
      detail,
      status,
      ready: payload.ready,
      successful: payload.successful || status === 'completed',
      failed: payload.failed || status === 'failed',
      error_message: payload.error || payload.traceback || payload.error_message,
      task_id: payload.task_id
    }

    console.log('✅ 转换后的进度数据:', result)
    return result
  }

  /**
   * 启动任务完成检测机制
   * 🔧 新增：渐进式降级策略，确保任务完成检测
   */
  const startCompletionCheck = (taskId, monitor) => {
    console.log(`🔍 启动任务完成检测: ${taskId}`)

    let checkCount = 0
    const maxChecks = 60 // 最多检测60次（约30分钟）

    const checkCompletion = async () => {
      if (!monitor.isActive) {
        console.log(`🛑 监控已停止，停止完成检测: ${taskId}`)
        return
      }

      checkCount++
      console.log(`🔍 检测任务完成状态 (${checkCount}/${maxChecks}): ${taskId}`)

      try {
        const response = await getTaskProgress(taskId)

        if (response && response.data && response.data.success) {
          const taskStatus = response.data.status
          const isCompleted = taskStatus.state === 'SUCCESS' ||
                             taskStatus.successful === true ||
                             (taskStatus.progress && taskStatus.progress.percentage >= 100)
          const isFailed = taskStatus.state === 'FAILURE' || taskStatus.failed === true

          if (isCompleted || isFailed) {
            console.log(`✅ 通过轮询检测到任务完成: ${taskId}, 状态: ${taskStatus.state}`)

            // 缓存完成状态
            taskCompletionCache.value.set(taskId, {
              status: isCompleted ? 'completed' : 'failed',
              result: taskStatus,
              timestamp: Date.now()
            })

            // 触发完成回调
            if (isCompleted) {
              handleTaskCompleted(taskId, taskStatus, monitor.callbacks)
            } else {
              handleTaskFailed(taskId, taskStatus.result?.error || '任务执行失败', monitor.callbacks)
            }

            // 停止检测
            stopCompletionCheck(taskId)
            return
          }
        }

        // 继续检测
        if (checkCount < maxChecks) {
          setTimeout(checkCompletion, 30000) // 30秒后再次检测
        } else {
          console.warn(`⚠️ 任务完成检测超时: ${taskId}`)
          stopCompletionCheck(taskId)
        }

      } catch (error) {
        console.warn(`⚠️ 任务完成检测失败: ${taskId}`, error)
        if (checkCount < maxChecks) {
          setTimeout(checkCompletion, 30000)
        } else {
          stopCompletionCheck(taskId)
        }
      }
    }

    // 延迟5分钟后开始检测（给WebSocket足够时间）
    const initialDelay = 5 * 60 * 1000 // 5分钟
    const timeoutId = setTimeout(checkCompletion, initialDelay)

    completionCheckIntervals.value.set(taskId, timeoutId)

    console.log(`⏰ 任务完成检测将在${initialDelay/1000}秒后开始: ${taskId}`)
  }

  /**
   * 停止任务完成检测
   */
  const stopCompletionCheck = (taskId) => {
    const timeoutId = completionCheckIntervals.value.get(taskId)
    if (timeoutId) {
      clearTimeout(timeoutId)
      completionCheckIntervals.value.delete(taskId)
      console.log(`🛑 停止任务完成检测: ${taskId}`)
    }
  }

  /**
   * WebSocket监控
   */
  const startWebSocketMonitoring = async (taskId, monitor) => {
    const { callbacks } = monitor

    // 设置WebSocket监听器
    const progressHandler = (payload) => {
      console.log('📨 WebSocket进度payload:', payload)
      if (payload.task_id === taskId) {
        // 将WebSocket payload转换为标准进度格式
        const progressData = convertWebSocketDataToProgress(payload)
        handleProgressUpdate(taskId, progressData, callbacks)
      }
    }

    const completedHandler = (payload) => {
      console.log('📨 WebSocket完成payload:', payload)
      if (payload.task_id === taskId) {
        handleTaskCompleted(taskId, payload.result || payload, callbacks)
      }
    }

    const failedHandler = (payload) => {
      console.log('📨 WebSocket失败payload:', payload)
      if (payload.task_id === taskId) {
        handleTaskFailed(taskId, payload.error || payload.error_message || '任务执行失败', callbacks)
      }
    }

    // 添加监听器
    wsManager.on('progress', progressHandler)
    wsManager.on('completed', completedHandler)
    wsManager.on('failed', failedHandler)

    // 订阅任务
    wsManager.subscribeTask(taskId)

    // 保存清理函数
    monitor.cleanup = () => {
      wsManager.unsubscribeTask(taskId)
      wsManager.off('progress', progressHandler)
      wsManager.off('completed', completedHandler)
      wsManager.off('failed', failedHandler)
    }

    console.log(`✅ WebSocket监控已启动: ${taskId}`)
  }

  /**
   * 轮询监控
   */
  const startPollingMonitoring = (taskId, monitor) => {
    const { callbacks } = monitor

    const pollProgress = async () => {
      if (!monitor.isActive) return

      try {
        const response = await getTaskProgress(taskId)
        
        // 解析响应数据
        let progressData = null
        if (response && response.data && response.data.success && response.data.status) {
          const taskStatus = response.data.status
          progressData = {
            percentage: taskStatus.progress?.percentage || 0,
            detail: taskStatus.progress?.detail || taskStatus.progress?.stage || '处理中...',
            status: taskStatus.state?.toLowerCase(),
            ready: taskStatus.ready,
            successful: taskStatus.successful,
            failed: taskStatus.failed,
            error_message: taskStatus.result?.error || taskStatus.traceback
          }
        }

        if (progressData) {
          monitor.lastUpdate = Date.now()
          monitor.retryCount = 0

          // 处理进度更新
          handleProgressUpdate(taskId, progressData, callbacks)

          // 检查任务是否完成
          const isCompleted = progressData.status === 'completed' ||
                             progressData.status === 'success' ||
                             progressData.successful === true ||
                             progressData.percentage >= 100
          const isFailed = progressData.status === 'failed' ||
                          progressData.status === 'failure' ||
                          progressData.failed === true
          const isCancelled = progressData.status === 'cancelled' ||
                             progressData.status === 'revoked'

          if (isCompleted) {
            handleTaskCompleted(taskId, progressData, callbacks)
          } else if (isFailed) {
            handleTaskFailed(taskId, progressData.error_message || '任务执行失败', callbacks)
          } else if (isCancelled) {
            handleTaskCancelled(taskId, callbacks)
          }
        } else {
          // 没有获取到进度数据，但不立即判定为失败
          const elapsedTime = Date.now() - monitor.startTime
          console.log(`⚠️ 暂未获取到进度数据，已等待 ${Math.round(elapsedTime / 1000)}秒`)
        }

      } catch (error) {
        monitor.retryCount++
        const elapsedTime = Date.now() - monitor.startTime
        console.warn(`⚠️ 轮询进度失败 (${monitor.retryCount}/${monitor.maxRetries}):`, error)
        console.log(`⏰ 任务已运行 ${Math.round(elapsedTime / 1000)}秒`)

        // 如果任务刚开始（30秒内），给更多容错时间
        const isEarlyStage = elapsedTime < 30000 // 30秒内
        const effectiveMaxRetries = isEarlyStage ? monitor.maxRetries * 2 : monitor.maxRetries

        if (monitor.retryCount >= effectiveMaxRetries) {
          console.error(`❌ 轮询重试次数超限，停止监控: ${taskId}`)
          handleTaskFailed(taskId, '进度查询失败', callbacks)
        }
      }
    }

    // 立即执行一次
    pollProgress()

    // 设置动态轮询间隔
    let currentInterval = 1000 // 开始时每1秒轮询一次

    const startPolling = () => {
      const intervalId = setInterval(() => {
        const elapsedTime = Date.now() - monitor.startTime

        // 根据运行时间调整轮询频率
        if (elapsedTime > 60000) { // 1分钟后
          currentInterval = 3000 // 每3秒轮询一次
        } else if (elapsedTime > 30000) { // 30秒后
          currentInterval = 2000 // 每2秒轮询一次
        }

        pollProgress()
      }, currentInterval)

      pollingIntervals.value.set(taskId, intervalId)
      return intervalId
    }

    startPolling()

    monitor.cleanup = () => {
      clearInterval(intervalId)
      pollingIntervals.value.delete(taskId)
    }

    console.log(`✅ 轮询监控已启动: ${taskId}`)
  }

  /**
   * 处理进度更新
   */
  const handleProgressUpdate = (taskId, progressData, callbacks) => {
    console.log(`📈 任务进度更新: ${taskId}`, progressData)

    // [FIX] 添加进度边界检查，确保进度值在0-100范围内
    if (progressData && typeof progressData.percentage === 'number') {
      progressData.percentage = Math.min(Math.max(progressData.percentage, 0), 100)
    }

    if (callbacks.onProgress) {
      callbacks.onProgress(progressData)
    }
  }

  /**
   * 处理任务完成
   */
  const handleTaskCompleted = (taskId, result, callbacks) => {
    console.log(`✅ 任务完成: ${taskId}`, result)
    
    stopMonitoring(taskId)
    
    if (callbacks.onCompleted) {
      callbacks.onCompleted(result)
    }
  }

  /**
   * 处理任务失败
   */
  const handleTaskFailed = (taskId, error, callbacks) => {
    console.log(`❌ 任务失败: ${taskId}`, error)
    
    stopMonitoring(taskId)
    
    if (callbacks.onFailed) {
      callbacks.onFailed(error)
    }
  }

  /**
   * 处理任务取消
   */
  const handleTaskCancelled = (taskId, callbacks) => {
    console.log(`⚠️ 任务取消: ${taskId}`)
    
    stopMonitoring(taskId)
    
    if (callbacks.onCancelled) {
      callbacks.onCancelled()
    }
  }

  /**
   * 停止监控任务 - 🔧 增强清理机制
   */
  const stopMonitoring = (taskId) => {
    const monitor = activeMonitors.value.get(taskId)
    if (monitor) {
      monitor.isActive = false

      if (monitor.cleanup) {
        monitor.cleanup()
      }

      // 🔧 停止任务完成检测
      stopCompletionCheck(taskId)

      // 清理轮询间隔
      const intervalId = pollingIntervals.value.get(taskId)
      if (intervalId) {
        clearInterval(intervalId)
        pollingIntervals.value.delete(taskId)
      }

      activeMonitors.value.delete(taskId)
      console.log(`🛑 停止监控任务: ${taskId}`)
    }
  }

  /**
   * 停止所有监控 - 🔧 增强清理机制
   */
  const stopAllMonitoring = () => {
    // 停止所有活跃监控
    for (const taskId of activeMonitors.value.keys()) {
      stopMonitoring(taskId)
    }

    // 🔧 清理所有完成检测
    for (const taskId of completionCheckIntervals.value.keys()) {
      stopCompletionCheck(taskId)
    }

    // 清理缓存
    taskCompletionCache.value.clear()
  }

  /**
   * 获取监控状态
   */
  const getMonitorStatus = () => {
    return {
      isWebSocketEnabled: isWebSocketEnabled.value,
      isWebSocketConnected: isWebSocketConnected.value,
      activeMonitorCount: activeMonitors.value.size,
      activeTaskIds: Array.from(activeMonitors.value.keys())
    }
  }

  // 组件卸载时清理
  onUnmounted(() => {
    stopAllMonitoring()
    wsManager.disconnect()
  })

  return {
    // 状态
    isWebSocketEnabled,
    isWebSocketConnected,
    
    // 方法
    initializeMonitor,
    startMonitoring,
    stopMonitoring,
    stopAllMonitoring,
    getMonitorStatus
  }
}
