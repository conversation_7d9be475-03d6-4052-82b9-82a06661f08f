# 语音处理智能平台 v0.1.0

基于 Vue.js + FastAPI 开发的语音处理智能平台，集成语音识别、文档处理、RAG 问答等功能。

> 🎉 **新版本特性**: 已从 Streamlit 升级为 Vue.js + FastAPI 架构，提供更好的用户体验和性能。

## 🚀 快速开始

### 方式一：一键启动（推荐）
```bash
# 1. 环境设置（首次运行）
setup.bat

# 2. 启动所有服务
start_services.bat

# 3. 访问应用
# 前端界面: http://localhost:3000
# 后端 API: http://localhost:8000
```

### 方式二：手动启动
详细步骤请参考 [PROJECT_STARTUP_GUIDE.md](PROJECT_STARTUP_GUIDE.md)

## 🌟 主要特性

- 🎤 **语音识别**: 支持实时语音转文字
- 👥 **说话人识别**: 智能识别不同说话人
- 📚 **文档处理**: 支持多种文档格式（PDF、Word、Excel、PPT、TXT、Markdown）
- 🔍 **RAG 问答**: 基于文档的智能问答系统
- 💾 **本地存储**: 数据安全，支持本地向量数据库
- 🤖 **多模型支持**: 支持多种 AI 模型
- 🔄 **实时流式**: 流式响应，实时进度监控
- 📊 **任务队列**: 异步任务处理，支持并发控制
- 🌐 **现代界面**: Vue.js 前端，响应式设计

## 🔧 技术栈

- **Web框架**: Streamlit 1.35+
- **RAG框架**: LlamaIndex 0.10.3+
- **向量数据库**: ChromaDB
- **模型服务**: Ollama
- **文档处理**: 
  - PyMuPDF (PDF)
  - python-docx (Word)
  - python-pptx (PowerPoint)
  - pandas/openpyxl (Excel)

## 📦 安装说明

1. 克隆项目
```bash
git clone <repository_url>
cd my_notebook
```

2. 安装依赖
```bash
pip install -r requirements.txt
```

3. 安装 Ollama 并下载所需模型
```bash
# 下载并安装 Ollama
# Windows: 访问 https://ollama.ai/download
# Linux: curl -fsSL https://ollama.ai/install.sh | sh

# 下载模型
ollama pull deepseek-coder-1.5b-instruct:latest
```

## 🚀 快速开始

1. 启动 Ollama 服务
```bash
ollama serve
```

2. 运行应用
```bash
streamlit run Home.py
```

## 📋 功能模块

1. **主页 (Home.py)**
   - 系统介绍
   - 模型配置
   - API 连接测试

2. **知识库 (pages/本地RAG大模型知识库.py)**
   - 文档上传管理
   - 智能问答
   - 来源追溯

3. **文档转换 (pages/doc转换md文件.py)**
   - 文档格式转换
   - 文本预处理
   - OCR 支持

## ⚙️ 配置说明

1. **环境变量**
```bash
OLLAMA_BASE_URL=http://localhost:11434  # Ollama服务地址
CHROMADB_PATH=./chroma_db              # 向量数据库存储路径
```

2. **模型配置**
- 支持动态加载可用模型
- 可在主页进行模型选择和测试
- 支持切换嵌入模型和对话模型

## 🔒 安全说明

- 所有数据和模型均在本地运行
- 不涉及数据上传到外部服务器
- 支持访问控制和权限管理

## 📝 更新日志

### v0.0.9
- 移除硬编码模型配置
- 优化模型动态加载机制
- 改进错误处理和用户提示
- 增强文档处理稳定性
- 优化向量存储性能

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

## �� 许可证

MIT License 