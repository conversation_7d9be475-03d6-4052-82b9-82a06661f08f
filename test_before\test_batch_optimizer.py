"""
批量处理优化器测试

测试所有批量处理优化功能，包括：
- 基础批量处理
- 自适应批量大小
- 智能分组策略
- 缓存机制
- 错误处理和重试
- 性能监控
"""

import time
import random
import logging
import math
from typing import List, Dict, Any
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class BatchOptimizerTester:
    """批量优化器测试器"""
    
    def __init__(self):
        self.test_results = {}
        self.performance_metrics = {}
        self.start_time = time.time()
        
        print("🚀 批量处理优化器测试开始")
        print("=" * 50)
    
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        tests = [
            ("基础批量处理", self.test_basic_batch_processing),
            ("自适应批量大小", self.test_adaptive_batch_sizing),
            ("智能分组策略", self.test_smart_grouping),
            ("缓存机制", self.test_caching_mechanism),
            ("错误处理和重试", self.test_error_handling),
            ("性能监控", self.test_performance_monitoring),
            ("异步处理", self.test_async_processing),
            ("负载均衡", self.test_load_balancing),
            ("内存优化", self.test_memory_optimization),
            ("综合性能测试", self.test_comprehensive_performance)
        ]
        
        for test_name, test_func in tests:
            print(f"\n📋 运行测试: {test_name}")
            try:
                result = test_func()
                self.test_results[test_name] = {
                    "status": "成功" if result else "失败",
                    "details": result if isinstance(result, dict) else {}
                }
                print(f"✅ {test_name}: {'成功' if result else '失败'}")
            except Exception as e:
                self.test_results[test_name] = {
                    "status": "错误",
                    "error": str(e)
                }
                print(f"❌ {test_name}: 错误 - {e}")
        
        return self._generate_test_report()
    
    def test_basic_batch_processing(self) -> bool:
        """测试基础批量处理功能"""
        try:
            from utils.batch_optimizer import create_batch_optimizer
            
            # 简单处理函数
            def square_task(x):
                time.sleep(0.01)  # 模拟处理时间
                return x * x
            
            # 创建测试数据
            test_data = list(range(20))
            expected_results = [x * x for x in test_data]
            
            # 创建优化器并处理
            optimizer = create_batch_optimizer(initial_batch_size=5, max_workers=4)
            result = optimizer.process_batch(square_task, test_data)
            
            # 验证结果
            assert len(result.results) == len(test_data)
            assert result.success_count == len(test_data)
            assert result.success_rate == 1.0
            assert result.results == expected_results
            
            # 记录性能指标
            self.performance_metrics["basic_processing"] = {
                "throughput": result.metrics.throughput,
                "processing_time": result.processing_time,
                "success_rate": result.success_rate
            }
            
            logger.info("基础批量处理测试通过")
            return True
            
        except Exception as e:
            logger.error(f"基础批量处理测试失败: {e}")
            return False
    
    def test_adaptive_batch_sizing(self) -> bool:
        """测试自适应批量大小调整"""
        try:
            from utils.batch_optimizer import BatchConfig, BatchOptimizer
            
            # 配置自适应调整
            config = BatchConfig(
                initial_batch_size=5,
                min_batch_size=2,
                max_batch_size=20,
                enable_adaptive_sizing=True,
                target_processing_time=0.5,
                adjustment_factor=0.2,
                performance_window=3
            )
            
            optimizer = BatchOptimizer(config)
            
            # 快速任务（应该增大批量）
            def fast_task(x):
                time.sleep(0.001)
                return x * 2
            
            initial_batch_size = optimizer.current_batch_size
            
            # 执行多次以触发自适应调整
            for i in range(5):
                data = list(range(30))
                result = optimizer.process_batch(fast_task, data)
                time.sleep(0.1)  # 短暂等待
            
            fast_final_size = optimizer.current_batch_size
            
            # 慢任务（应该减小批量）
            def slow_task(x):
                time.sleep(0.1)
                return x * 3
            
            # 重置优化器
            optimizer.current_batch_size = 15
            
            for i in range(5):
                data = list(range(20))
                result = optimizer.process_batch(slow_task, data)
                time.sleep(0.1)
            
            slow_final_size = optimizer.current_batch_size
            
            # 验证自适应调整
            logger.info(f"初始批量大小: {initial_batch_size}")
            logger.info(f"快速任务后批量大小: {fast_final_size}")
            logger.info(f"慢任务后批量大小: {slow_final_size}")
            
            # 快速任务应该增加批量大小，慢任务应该减少批量大小
            adjustment_working = (slow_final_size < initial_batch_size)
            
            self.performance_metrics["adaptive_sizing"] = {
                "initial_size": initial_batch_size,
                "fast_final_size": fast_final_size,
                "slow_final_size": slow_final_size,
                "adjustment_working": adjustment_working
            }
            
            logger.info(f"自适应批量大小调整测试: {'成功' if adjustment_working else '未明显'}")
            return True
            
        except Exception as e:
            logger.error(f"自适应批量大小测试失败: {e}")
            return False
    
    def test_smart_grouping(self) -> bool:
        """测试智能分组策略"""
        try:
            from utils.batch_optimizer import BatchConfig, BatchOptimizer
            
            # 测试相似性分组
            config = BatchConfig(
                enable_smart_grouping=True,
                grouping_strategy="similarity",
                max_group_size=5
            )
            
            optimizer = BatchOptimizer(config)
            
            # 创建相似数据（字符串）
            data = [
                "apple", "application", "apply",  # 相似组1
                "banana", "band", "bank",         # 相似组2
                "cat", "car", "card",             # 相似组3
                "dog", "door", "down"             # 相似组4
            ]
            
            def process_text(text):
                return len(text)
            
            result = optimizer.process_batch(process_text, data)
            
            # 验证处理结果
            assert len(result.results) == len(data)
            assert result.success_rate == 1.0
            
            # 测试大小分组
            config.grouping_strategy = "size"
            optimizer = BatchOptimizer(config)
            
            # 创建不同大小的数据
            size_data = ["a", "bb", "ccc", "dddd", "e", "ff", "ggg", "hhhh"]
            result = optimizer.process_batch(process_text, size_data)
            
            assert len(result.results) == len(size_data)
            assert result.success_rate == 1.0
            
            self.performance_metrics["smart_grouping"] = {
                "similarity_success": True,
                "size_grouping_success": True
            }
            
            logger.info("智能分组策略测试通过")
            return True
            
        except Exception as e:
            logger.error(f"智能分组策略测试失败: {e}")
            return False
    
    def test_caching_mechanism(self) -> bool:
        """测试缓存机制"""
        try:
            from utils.batch_optimizer import BatchConfig, BatchOptimizer
            
            # 启用缓存
            config = BatchConfig(
                cache_results=True,
                cache_size=100
            )
            
            optimizer = BatchOptimizer(config)
            
            # 计算密集型任务
            def expensive_computation(x):
                time.sleep(0.05)  # 模拟昂贵计算
                return x * x * x
            
            test_data = list(range(10))
            
            # 第一次处理（应该很慢）
            start_time = time.time()
            result1 = optimizer.process_batch(expensive_computation, test_data)
            first_time = time.time() - start_time
            
            # 第二次处理相同数据（应该很快，使用缓存）
            start_time = time.time()
            result2 = optimizer.process_batch(expensive_computation, test_data)
            second_time = time.time() - start_time
            
            # 验证结果一致性
            assert result1.results == result2.results
            assert result1.success_rate == result2.success_rate == 1.0
            
            # 缓存应该显著提升性能
            speedup_ratio = first_time / second_time if second_time > 0 else float('inf')
            cache_effective = speedup_ratio > 2.0  # 至少2倍提升
            
            self.performance_metrics["caching"] = {
                "first_time": first_time,
                "second_time": second_time,
                "speedup_ratio": speedup_ratio,
                "cache_effective": cache_effective,
                "cache_size": len(optimizer.result_cache) if optimizer.result_cache else 0
            }
            
            logger.info(f"缓存机制测试: 加速比 {speedup_ratio:.2f}x")
            return cache_effective
            
        except Exception as e:
            logger.error(f"缓存机制测试失败: {e}")
            return False
    
    def test_error_handling(self) -> bool:
        """测试错误处理和重试机制"""
        try:
            from utils.batch_optimizer import BatchConfig, BatchOptimizer
            
            config = BatchConfig(
                max_retries=2,
                retry_delay=0.1,
                fail_fast=False,
                error_threshold=0.5
            )
            
            optimizer = BatchOptimizer(config)
            
            # 创建有时失败的任务
            failure_count = 0
            def unreliable_task(x):
                nonlocal failure_count
                if x % 3 == 0 and failure_count < 3:  # 前3个能被3整除的数会失败
                    failure_count += 1
                    raise ValueError(f"模拟失败: {x}")
                return x * 2
            
            test_data = list(range(15))
            result = optimizer.process_batch(unreliable_task, test_data)
            
            # 验证错误处理
            assert len(result.results) == len(test_data)
            assert result.has_errors
            assert len(result.errors) > 0
            assert result.success_count < result.total_count
            
            # 测试快速失败
            config.fail_fast = True
            config.error_threshold = 0.2  # 20%错误率触发快速失败
            
            optimizer = BatchOptimizer(config)
            
            def mostly_failing_task(x):
                if x % 2 == 0:  # 50%失败率
                    raise ValueError(f"快速失败测试: {x}")
                return x
            
            result = optimizer.process_batch(mostly_failing_task, list(range(20)))
            
            self.performance_metrics["error_handling"] = {
                "normal_error_handling": True,
                "fast_fail_triggered": result.has_errors,
                "error_rate": len(result.errors) / result.total_count
            }
            
            logger.info("错误处理和重试测试通过")
            return True
            
        except Exception as e:
            logger.error(f"错误处理测试失败: {e}")
            return False
    
    def test_performance_monitoring(self) -> bool:
        """测试性能监控功能"""
        try:
            from utils.batch_optimizer import BatchConfig, BatchOptimizer
            
            config = BatchConfig(
                enable_monitoring=True,
                metrics_interval=1.0
            )
            
            optimizer = BatchOptimizer(config)
            
            def monitored_task(x):
                time.sleep(0.02)
                return x * x
            
            # 执行多批处理以收集指标
            for batch_num in range(3):
                data = list(range(10 + batch_num * 5))
                result = optimizer.process_batch(monitored_task, data)
                time.sleep(0.1)
            
            # 检查指标收集
            assert len(optimizer.metrics_history) > 0
            assert optimizer.total_processed > 0
            
            # 获取性能摘要
            summary = optimizer.get_performance_summary()
            assert "total_processed" in summary
            assert "avg_throughput" in summary
            assert "avg_success_rate" in summary
            
            # 测试指标保存
            metrics_file = "test_batch_metrics.json"
            optimizer.save_metrics(metrics_file)
            
            # 验证文件存在
            assert Path(metrics_file).exists()
            
            # 清理测试文件
            Path(metrics_file).unlink()
            
            self.performance_metrics["monitoring"] = {
                "metrics_collected": len(optimizer.metrics_history),
                "total_processed": optimizer.total_processed,
                "summary_keys": list(summary.keys())
            }
            
            logger.info("性能监控测试通过")
            return True
            
        except Exception as e:
            logger.error(f"性能监控测试失败: {e}")
            return False
    
    def test_async_processing(self) -> bool:
        """测试异步处理功能"""
        try:
            import asyncio
            from utils.batch_optimizer import BatchConfig, BatchOptimizer
            
            config = BatchConfig(
                use_async=True,
                max_workers=4
            )
            
            optimizer = BatchOptimizer(config)
            
            # 同步任务（在异步环境中运行）
            def sync_task(x):
                time.sleep(0.01)
                return x * 3
            
            # 异步任务
            async def async_task(x):
                await asyncio.sleep(0.01)
                return x * 4
            
            # 测试同步任务的异步处理
            data = list(range(15))
            result = optimizer.process_batch(sync_task, data)
            
            assert len(result.results) == len(data)
            assert result.success_rate == 1.0
            
            # 注意：异步任务测试需要在async环境中运行
            # 这里只验证配置和基本功能
            
            self.performance_metrics["async_processing"] = {
                "sync_task_success": True,
                "config_valid": True
            }
            
            logger.info("异步处理功能测试通过")
            return True
            
        except Exception as e:
            logger.error(f"异步处理测试失败: {e}")
            return False
    
    def test_load_balancing(self) -> bool:
        """测试负载均衡功能"""
        try:
            from utils.batch_optimizer import BatchConfig, BatchOptimizer
            
            config = BatchConfig(
                max_workers=4,
                load_balancing=True,
                enable_smart_grouping=True,
                grouping_strategy="size"
            )
            
            optimizer = BatchOptimizer(config)
            
            # 创建不同处理时间的任务
            def variable_task(x):
                # 模拟不同处理时间
                sleep_time = 0.01 * (1 + x % 3)
                time.sleep(sleep_time)
                return x * (x % 3 + 1)
            
            # 创建大数据集
            data = list(range(30))
            
            start_time = time.time()
            result = optimizer.process_batch(variable_task, data)
            processing_time = time.time() - start_time
            
            # 验证结果
            assert len(result.results) == len(data)
            assert result.success_rate == 1.0
            
            # 负载均衡应该保持合理的处理时间
            expected_min_time = len(data) * 0.01 / config.max_workers * 0.8  # 80%效率
            load_balanced = processing_time > expected_min_time
            
            self.performance_metrics["load_balancing"] = {
                "processing_time": processing_time,
                "expected_min_time": expected_min_time,
                "efficiency": expected_min_time / processing_time if processing_time > 0 else 0,
                "load_balanced": load_balanced
            }
            
            logger.info(f"负载均衡测试: 效率 {expected_min_time / processing_time:.2%}")
            return True
            
        except Exception as e:
            logger.error(f"负载均衡测试失败: {e}")
            return False
    
    def test_memory_optimization(self) -> bool:
        """测试内存优化功能"""
        try:
            import psutil
            import os
            from utils.batch_optimizer import BatchConfig, BatchOptimizer
            
            # 获取初始内存使用
            process = psutil.Process(os.getpid())
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            config = BatchConfig(
                cache_results=True,
                cache_size=50,  # 限制缓存大小
                max_workers=2   # 限制工作器数量
            )
            
            optimizer = BatchOptimizer(config)
            
            # 创建内存消耗任务
            def memory_task(x):
                # 创建一些临时数据
                temp_data = [i for i in range(1000)]
                return sum(temp_data) + x
            
            # 处理大量数据
            large_data = list(range(200))
            result = optimizer.process_batch(memory_task, large_data)
            
            # 检查最终内存使用
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = final_memory - initial_memory
            
            # 验证结果
            assert len(result.results) == len(large_data)
            assert result.success_rate == 1.0
            
            # 内存增长应该保持在合理范围内
            reasonable_memory_use = memory_increase < 100  # 少于100MB增长
            
            self.performance_metrics["memory_optimization"] = {
                "initial_memory_mb": initial_memory,
                "final_memory_mb": final_memory,
                "memory_increase_mb": memory_increase,
                "reasonable_memory_use": reasonable_memory_use,
                "cache_size": len(optimizer.result_cache) if optimizer.result_cache else 0
            }
            
            logger.info(f"内存优化测试: 增长 {memory_increase:.2f}MB")
            return reasonable_memory_use
            
        except Exception as e:
            logger.error(f"内存优化测试失败: {e}")
            return False
    
    def test_comprehensive_performance(self) -> bool:
        """综合性能测试"""
        try:
            from utils.batch_optimizer import create_batch_optimizer, optimized_batch_map
            
            # 创建不同配置的优化器进行对比
            configs = [
                {"initial_batch_size": 5, "max_workers": 2, "enable_adaptive_sizing": False},
                {"initial_batch_size": 10, "max_workers": 4, "enable_adaptive_sizing": True},
                {"initial_batch_size": 15, "max_workers": 8, "enable_adaptive_sizing": True, "cache_results": True}
            ]
            
            def benchmark_task(x):
                # 模拟复杂计算
                time.sleep(0.005)
                return sum(range(x % 10 + 1))
            
            test_data = list(range(100))
            performance_results = []
            
            for i, config in enumerate(configs):
                optimizer = create_batch_optimizer(**config)
                
                start_time = time.time()
                result = optimizer.process_batch(benchmark_task, test_data)
                processing_time = time.time() - start_time
                
                performance_results.append({
                    "config": config,
                    "processing_time": processing_time,
                    "throughput": result.metrics.throughput,
                    "success_rate": result.success_rate
                })
                
                logger.info(f"配置 {i+1}: {processing_time:.2f}s, {result.metrics.throughput:.2f} 项目/秒")
            
            # 找到最佳性能配置
            best_config = max(performance_results, key=lambda x: x["throughput"])
            
            self.performance_metrics["comprehensive"] = {
                "results": performance_results,
                "best_config": best_config,
                "performance_variance": {
                    "min_time": min(r["processing_time"] for r in performance_results),
                    "max_time": max(r["processing_time"] for r in performance_results),
                    "min_throughput": min(r["throughput"] for r in performance_results),
                    "max_throughput": max(r["throughput"] for r in performance_results)
                }
            }
            
            logger.info("综合性能测试完成")
            return True
            
        except Exception as e:
            logger.error(f"综合性能测试失败: {e}")
            return False
    
    def _generate_test_report(self) -> Dict[str, Any]:
        """生成测试报告"""
        total_time = time.time() - self.start_time
        
        # 统计测试结果
        total_tests = len(self.test_results)
        successful_tests = sum(1 for r in self.test_results.values() if r["status"] == "成功")
        failed_tests = sum(1 for r in self.test_results.values() if r["status"] == "失败")
        error_tests = sum(1 for r in self.test_results.values() if r["status"] == "错误")
        
        success_rate = successful_tests / total_tests if total_tests > 0 else 0
        
        # 生成建议
        recommendations = self._generate_recommendations()
        
        report = {
            "summary": {
                "total_tests": total_tests,
                "successful_tests": successful_tests,
                "failed_tests": failed_tests,
                "error_tests": error_tests,
                "success_rate": success_rate,
                "total_time": total_time
            },
            "test_results": self.test_results,
            "performance_metrics": self.performance_metrics,
            "recommendations": recommendations,
            "timestamp": time.time()
        }
        
        # 打印报告摘要
        print("\n" + "=" * 50)
        print("📊 批量处理优化器测试报告")
        print("=" * 50)
        print(f"总测试数: {total_tests}")
        print(f"成功: {successful_tests} | 失败: {failed_tests} | 错误: {error_tests}")
        print(f"成功率: {success_rate:.2%}")
        print(f"总用时: {total_time:.2f}秒")
        
        if recommendations:
            print("\n🔧 优化建议:")
            for rec in recommendations:
                print(f"  • {rec}")
        
        print("\n✅ 批量处理优化器测试完成")
        
        return report
    
    def _generate_recommendations(self) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # 检查测试成功率
        success_rate = sum(1 for r in self.test_results.values() if r["status"] == "成功") / len(self.test_results)
        if success_rate < 0.8:
            recommendations.append("测试成功率较低，建议检查系统环境和依赖")
        
        # 检查性能指标
        if "caching" in self.performance_metrics:
            cache_perf = self.performance_metrics["caching"]
            if cache_perf.get("speedup_ratio", 0) < 2:
                recommendations.append("缓存效果不明显，考虑检查缓存键生成或增加缓存大小")
        
        if "memory_optimization" in self.performance_metrics:
            memory_perf = self.performance_metrics["memory_optimization"]
            if memory_perf.get("memory_increase_mb", 0) > 50:
                recommendations.append("内存使用增长较大，建议调整批量大小或缓存策略")
        
        if "comprehensive" in self.performance_metrics:
            comp_perf = self.performance_metrics["comprehensive"]
            variance = comp_perf.get("performance_variance", {})
            if variance.get("max_time", 0) / variance.get("min_time", 1) > 3:
                recommendations.append("不同配置性能差异较大，建议进一步调优参数")
        
        if not recommendations:
            recommendations.append("所有测试都表现良好，批量处理优化器运行正常")
        
        return recommendations

def main():
    """主函数"""
    tester = BatchOptimizerTester()
    
    try:
        report = tester.run_all_tests()
        
        # 保存详细报告
        import json
        with open("batch_optimizer_test_report.json", "w", encoding="utf-8") as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 详细报告已保存到: batch_optimizer_test_report.json")
        
        return report["summary"]["success_rate"] > 0.8
        
    except Exception as e:
        print(f"❌ 测试运行失败: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1) 