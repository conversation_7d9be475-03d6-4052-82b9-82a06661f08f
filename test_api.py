#!/usr/bin/env python3
"""
测试说话人识别API的脚本
"""
import requests
import json
import time

# API基础URL
BASE_URL = "http://localhost:8002/api/v1"

def login():
    """登录获取token"""
    url = f"{BASE_URL}/auth/login"
    data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(url, json=data)
        response.raise_for_status()
        result = response.json()
        print(f"✅ 登录成功: {result}")
        return result.get("access_token")
    except Exception as e:
        print(f"❌ 登录失败: {e}")
        return None

def get_audio_files(token):
    """获取音频文件列表"""
    url = f"{BASE_URL}/audio/"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        result = response.json()
        print(f"✅ 获取音频文件列表成功: 共{len(result)}个文件")
        for file in result:
            print(f"  - ID: {file['id']}, 文件名: {file['filename']}")
        return result
    except Exception as e:
        print(f"❌ 获取音频文件列表失败: {e}")
        if hasattr(e, 'response'):
            print(f"响应状态码: {e.response.status_code}")
            print(f"响应内容: {e.response.text}")
        return []

def process_speaker_recognition(token, file_id):
    """调用说话人识别API"""
    url = f"{BASE_URL}/audio/process"
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    data = {
        "file_ids": [file_id],
        "processing_mode": "speaker-recognition",
        "config": {
            "clustering_method": "kmeans",
            "expected_speakers": None,
            "similarity_threshold": 0.7,
            "language": "auto",
            "use_itn": True
        }
    }
    
    try:
        response = requests.post(url, json=data, headers=headers)
        response.raise_for_status()
        result = response.json()
        print(f"✅ 说话人识别任务创建成功: {result}")
        return result.get("task_id")
    except Exception as e:
        print(f"❌ 说话人识别任务创建失败: {e}")
        if hasattr(e, 'response'):
            print(f"响应内容: {e.response.text}")
        return None

def get_task_status_from_db(task_id):
    """直接从数据库获取任务状态"""
    import sqlite3

    try:
        conn = sqlite3.connect('data/speech_platform.db')
        cursor = conn.cursor()

        # 查询任务状态
        cursor.execute("""
            SELECT id, status, progress, result, error_message, created_at, updated_at
            FROM audio_processing_tasks
            WHERE id = ?
        """, (task_id,))

        task = cursor.fetchone()
        if task:
            task_data = {
                "id": task[0],
                "status": task[1],
                "progress": task[2],
                "result": task[3],
                "error_message": task[4],
                "created_at": task[5],
                "updated_at": task[6]
            }
            print(f"✅ 数据库任务状态: {json.dumps(task_data, indent=2, ensure_ascii=False)}")
            return task_data
        else:
            print(f"❌ 数据库中未找到任务: {task_id}")
            return None

    except Exception as e:
        print(f"❌ 查询数据库失败: {e}")
        return None
    finally:
        if 'conn' in locals():
            conn.close()

def check_celery_logs():
    """检查Celery日志文件"""
    import os
    import glob

    log_patterns = [
        "celery*.log",
        "worker*.log",
        "logs/celery*.log",
        "logs/worker*.log"
    ]

    print("🔍 查找Celery日志文件...")
    for pattern in log_patterns:
        files = glob.glob(pattern)
        for file in files:
            print(f"📄 找到日志文件: {file}")
            try:
                with open(file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                    # 显示最后20行
                    print(f"📝 {file} 最后20行:")
                    for line in lines[-20:]:
                        print(f"  {line.strip()}")
            except Exception as e:
                print(f"❌ 读取日志文件失败: {e}")

def main():
    print("🚀 开始测试说话人识别API...")

    # 1. 登录
    print("\n1. 登录...")
    token = login()
    if not token:
        return

    # 2. 跳过获取音频文件列表（由于数据格式问题），直接使用已知文件ID
    print("\n2. 跳过获取音频文件列表，使用已知文件ID...")
    # 从前端测试中我们知道有一个文件ID是1
    file_id = "1"
    print(f"使用文件ID: {file_id}")

    # 3. 调用说话人识别API
    print(f"\n3. 使用文件ID {file_id} 进行说话人识别测试...")
    task_id = process_speaker_recognition(token, file_id)
    if not task_id:
        return
    
    # 5. 等待任务完成并检查结果
    print(f"\n4. 等待任务 {task_id} 完成...")

    # 先检查Celery日志
    print("\n📋 检查Celery日志...")
    check_celery_logs()

    for i in range(15):  # 最多等待15次，每次3秒
        time.sleep(3)
        print(f"\n⏳ 检查任务状态... ({i+1}/15)")

        # 直接从数据库查询任务状态
        status = get_task_status_from_db(task_id)
        if status:
            if status.get("status") == "completed":
                print(f"✅ 任务完成！")
                if status.get("result"):
                    try:
                        result_data = json.loads(status["result"])
                        print(f"📊 处理结果: {json.dumps(result_data, indent=2, ensure_ascii=False)}")
                    except:
                        print(f"📊 原始结果: {status['result']}")
                break
            elif status.get("status") == "failed":
                print(f"❌ 任务失败！")
                if status.get("error_message"):
                    print(f"错误信息: {status['error_message']}")
                break
            else:
                print(f"📊 当前状态: {status.get('status')}, 进度: {status.get('progress', 0)}%")
        else:
            print("❌ 无法获取任务状态")

    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
