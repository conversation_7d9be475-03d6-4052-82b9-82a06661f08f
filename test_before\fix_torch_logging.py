#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复torch日志配置问题
"""

import os
import sys

def fix_torch_logging():
    """修复torch日志配置问题"""
    print("🔧 修复torch日志配置问题...")
    
    # 设置环境变量禁用torch日志
    os.environ['TORCH_LOGS'] = ''
    os.environ['TORCH_LOG_LEVEL'] = 'ERROR'
    os.environ['TORCH_SHOW_CPP_STACKTRACES'] = '0'
    
    # 禁用torch的详细日志
    try:
        import torch
        import logging
        
        # 设置torch相关的日志级别
        logging.getLogger('torch').setLevel(logging.ERROR)
        logging.getLogger('torch._dynamo').setLevel(logging.ERROR)
        logging.getLogger('torch._inductor').setLevel(logging.ERROR)
        
        print("✅ torch日志配置已修复")
        print(f"torch版本: {torch.__version__}")
        
        # 测试torch基本功能
        x = torch.tensor([1.0, 2.0, 3.0])
        print(f"✅ torch基本功能测试成功: {x.sum().item()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

def create_torch_config():
    """创建torch配置文件"""
    print("📝 创建torch配置...")
    
    # 创建.torch目录
    torch_dir = os.path.expanduser("~/.torch")
    os.makedirs(torch_dir, exist_ok=True)
    
    # 创建配置文件禁用日志
    config_content = """
# Torch配置文件
# 禁用详细日志输出
TORCH_LOGS=
TORCH_LOG_LEVEL=ERROR
TORCH_SHOW_CPP_STACKTRACES=0
"""
    
    config_path = os.path.join(torch_dir, "config")
    try:
        with open(config_path, 'w') as f:
            f.write(config_content)
        print(f"✅ 配置文件已创建: {config_path}")
        return True
    except Exception as e:
        print(f"❌ 创建配置文件失败: {e}")
        return False

def main():
    """主修复函数"""
    print("🚀 修复torch日志配置问题")
    print("=" * 50)
    
    # 方法1: 设置环境变量
    success1 = fix_torch_logging()
    
    # 方法2: 创建配置文件
    success2 = create_torch_config()
    
    if success1:
        print("\n🎉 修复成功！")
        print("现在可以正常使用torch而不会出现日志错误")
    else:
        print("\n⚠️ 修复可能不完全，但torch基本功能应该可用")
    
    print("\n📋 使用建议:")
    print("1. 重启Python环境")
    print("2. 重新运行streamlit应用")
    print("3. 如果仍有问题，尝试重新安装torch")

if __name__ == "__main__":
    main()
