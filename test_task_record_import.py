#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试TaskRecord导入
"""

try:
    print("🔍 测试TaskRecord导入...")
    
    # 测试导入TaskRecord
    from backend.models.task_models import TaskRecord
    print("✅ TaskRecord导入成功")
    
    # 测试数据库连接
    from backend.core.database import get_db
    print("✅ 数据库模块导入成功")
    
    # 测试数据库会话
    db = next(get_db())
    print("✅ 数据库会话创建成功")
    
    # 测试查询TaskRecord
    tasks = db.query(TaskRecord).limit(5).all()
    print(f"✅ 查询TaskRecord成功，找到 {len(tasks)} 个记录")

    for i, task in enumerate(tasks, 1):
        print(f"📋 任务 {i}: {task.task_id}")
        print(f"  task_type: {task.task_type}")
        print(f"  status: {task.status}")
        print(f"  progress: {task.progress_percentage}%")
        print(f"  created_at: {task.created_at}")
        print("------------------------------------------------------------")

    # 测试查询音频处理任务
    print("\n🔍 查询音频处理任务...")
    audio_tasks = db.query(TaskRecord).filter(
        TaskRecord.task_type.in_(['audio_processing', 'speech_recognition', 'speaker_recognition', 'vad_detection'])
    ).order_by(TaskRecord.created_at.desc()).limit(10).all()

    print(f"✅ 找到 {len(audio_tasks)} 个音频处理任务")

    for i, task in enumerate(audio_tasks, 1):
        print(f"📋 音频任务 {i}: {task.task_id}")
        print(f"  task_type: {task.task_type}")
        print(f"  status: {task.status}")
        print(f"  progress: {task.progress_percentage}%")
        print(f"  user_id: {task.user_id}")
        print(f"  created_at: {task.created_at}")
        print("------------------------------------------------------------")
    
    db.close()
    print("✅ 测试完成")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
