2025-06-24 13:45:29,602 - __main__ - INFO - 启动简化Worker管理器
2025-06-24 13:45:29,603 - __main__ - INFO - 环境变量设置完成
2025-06-24 13:45:29,604 - __main__ - INFO - 虚拟环境检查通过: .venv\Scripts\python.exe
2025-06-24 13:45:29,604 - __main__ - INFO - 启动Worker命令: .venv\Scripts\python.exe -m celery -A backend.core.task_queue:celery_app worker --loglevel=info --pool=threads --concurrency=2 --max-tasks-per-child=5 --queues=audio_processing,default --hostname=audio_worker@%h
2025-06-24 13:45:29,610 - __main__ - INFO - Worker进程已启动，PID: 24228
2025-06-24 13:45:30,817 - __main__ - WARNING - 检测到错误: Error:
2025-06-24 13:45:30,817 - __main__ - WARNING - 检测到错误: While trying to load the module backend.core.task_queue:celery_app the following error occurred:
2025-06-24 13:45:30,817 - __main__ - WARNING - 检测到错误: Traceback (most recent call last):
2025-06-24 13:45:30,835 - __main__ - WARNING - 检测到错误: SyntaxError: keyword argument repeated: task_soft_time_limit
