# 项目概述

## 项目名称
语音处理智能平台 (Speech Processing Intelligence Platform)

## 项目目的
这是一个基于AI的语音处理和文档管理平台，主要功能包括：
- 音频处理：语音识别、说话人识别、会议转录
- 文档处理：OCR、智能文档分割、向量化
- RAG系统：知识库管理、智能问答
- 实时协作：WebSocket支持的实时进度监控

## 技术架构
- **前端**: Vue 3 + Element Plus + Vite (端口: 3000)
- **后端**: FastAPI + SQLite + Celery (端口: 8002)
- **任务队列**: Redis + Celery (多线程模式)
- **AI框架**: PyTorch + Transformers + FunASR
- **向量数据库**: ChromaDB
- **实时通信**: WebSocket

## 部署环境
- 操作系统: Windows
- Python版本: 3.11
- 包管理: uv (替代pip)
- 虚拟环境: .venv
- 容器化: Docker (仅Redis)

## 项目结构
```
my_notebook_version_0.1.0/
├── frontend/          # Vue前端应用
├── backend/           # FastAPI后端服务
├── models/            # AI模型存储
├── data/              # SQLite数据库
├── logs/              # 日志文件
├── test/              # 测试文件
├── scripts/           # 工具脚本
└── requirements.txt   # Python依赖
```