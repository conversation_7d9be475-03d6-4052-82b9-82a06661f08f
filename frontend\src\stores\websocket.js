/**
 * WebSocket状态管理Store
 * 统一管理WebSocket连接状态，解决状态不一致问题
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { useAuthStore } from './auth'

export const useWebSocketStore = defineStore('websocket', () => {
  // 状态
  const ws = ref(null)
  const connectionStatus = ref('disconnected') // 'disconnected', 'connecting', 'connected', 'error'
  const reconnectAttempts = ref(0)
  const lastError = ref(null)
  const messageQueue = ref([])

  // 🔧 连接健康检测状态
  const missedHeartbeats = ref(0)
  const maxMissedHeartbeats = 3
  
  // 配置 - 🔧 优化大文件处理的连接管理
  const config = {
    maxReconnectAttempts: 10,    // 🔧 进一步增加重连次数
    reconnectInterval: 3000,     // 🔧 缩短重连间隔（更快恢复）
    heartbeatInterval: 90000,    // 🔧 进一步延长心跳间隔到90秒
    heartbeatTimeout: 45000,     // 🔧 延长心跳超时到45秒
    connectionTimeout: 15000,    // 🔧 延长连接超时时间
    maxIdleTime: 600000,         // 🔧 延长最大空闲时间到10分钟
    maxBackoffDelay: 60000       // 🔧 最大退避延迟60秒
  }
  
  // 定时器
  let reconnectTimer = null
  let heartbeatTimer = null
  let heartbeatTimeout = null
  
  // 事件监听器
  const listeners = new Map()
  
  // 计算属性
  const isConnected = computed(() => connectionStatus.value === 'connected')
  const isConnecting = computed(() => connectionStatus.value === 'connecting')
  const isDisconnected = computed(() => connectionStatus.value === 'disconnected')
  const hasError = computed(() => connectionStatus.value === 'error')
  
  /**
   * 连接WebSocket
   */
  const connect = async () => {
    if (isConnecting.value || isConnected.value) {
      return Promise.resolve()
    }
    
    return new Promise((resolve, reject) => {
      try {
        connectionStatus.value = 'connecting'
        lastError.value = null
        
        // 获取认证token
        const authStore = useAuthStore()
        const token = authStore.token
        if (!token) {
          throw new Error('没有有效的认证token')
        }
        
        // 构建WebSocket URL
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
        const host = import.meta.env.VITE_WS_HOST || window.location.hostname
        const port = import.meta.env.VITE_WS_PORT || '8002'
        const wsUrl = `${protocol}//${host}:${port}/ws/progress?token=${token}`
        
        console.log('🔌 连接WebSocket:', wsUrl)
        
        ws.value = new WebSocket(wsUrl)
        
        ws.value.onopen = () => {
          console.log('✅ WebSocket连接成功')
          connectionStatus.value = 'connected'
          reconnectAttempts.value = 0
          
          // 启动心跳
          startHeartbeat()
          
          // 发送队列中的消息
          flushMessageQueue()
          
          // 触发连接成功事件
          emit('connected')
          
          resolve()
        }
        
        ws.value.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data)
            // 🔧 增强消息日志记录
            console.log('📨 收到WebSocket消息:', {
              type: data.type,
              task_id: data.task_id,
              timestamp: new Date().toISOString(),
              dataSize: JSON.stringify(data).length
            })
            handleMessage(data)
          } catch (error) {
            // 🔧 增强错误日志记录
            console.error('❌ WebSocket消息解析失败:', {
              error: error.message,
              rawData: event.data,
              timestamp: new Date().toISOString()
            })

            // 触发解析错误事件
            emit('parse_error', { error, rawData: event.data })
          }
        }
        
        ws.value.onclose = (event) => {
          console.log('🔌 WebSocket连接关闭:', event.code, event.reason)
          connectionStatus.value = 'disconnected'
          
          stopHeartbeat()
          
          // 触发断开连接事件
          emit('disconnected', { code: event.code, reason: event.reason })
          
          // 🔧 智能重连机制：根据关闭原因决定是否重连
          if (shouldAttemptReconnect(event.code, event.reason) && reconnectAttempts.value < config.maxReconnectAttempts) {
            scheduleReconnect()
          }
        }
        
        ws.value.onerror = (error) => {
          // 🔧 增强错误日志记录
          console.error('❌ WebSocket连接错误:', {
            error: error,
            readyState: ws.value?.readyState,
            url: ws.value?.url,
            timestamp: new Date().toISOString(),
            reconnectAttempts: reconnectAttempts.value
          })

          connectionStatus.value = 'error'
          lastError.value = {
            error: error,
            timestamp: Date.now(),
            reconnectAttempts: reconnectAttempts.value
          }

          // 触发错误事件
          emit('error', error)

          reject(error)
        }
        
      } catch (error) {
        connectionStatus.value = 'error'
        lastError.value = error
        reject(error)
      }
    })
  }
  
  /**
   * 断开WebSocket连接
   */
  const disconnect = () => {
    connectionStatus.value = 'disconnected'
    
    // 清理定时器
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
      reconnectTimer = null
    }
    
    stopHeartbeat()
    
    // 关闭WebSocket
    if (ws.value) {
      ws.value.close(1000, 'Client disconnect')
      ws.value = null
    }
    
    console.log('🔌 WebSocket已断开')
  }
  
  /**
   * 发送消息
   */
  const send = (message) => {
    if (ws.value && isConnected.value) {
      try {
        ws.value.send(JSON.stringify(message))
        return true
      } catch (error) {
        console.error('❌ 发送WebSocket消息失败:', error)
        return false
      }
    } else {
      // 如果未连接，将消息加入队列
      messageQueue.value.push(message)
      return false
    }
  }
  
  /**
   * 发送队列中的消息
   */
  const flushMessageQueue = () => {
    while (messageQueue.value.length > 0) {
      const message = messageQueue.value.shift()
      send(message)
    }
  }
  
  /**
   * 判断是否应该尝试重连
   * 🔧 新增：智能重连判断
   */
  const shouldAttemptReconnect = (code, reason) => {
    // 不重连的情况
    const noReconnectCodes = [
      1000, // 正常关闭
      1001, // 端点离开
      1005, // 没有状态码
      4000, // 自定义：认证失败
      4001, // 自定义：权限不足
      4002  // 自定义：服务器拒绝
    ]

    if (noReconnectCodes.includes(code)) {
      console.log(`🚫 不重连 - 关闭码: ${code}, 原因: ${reason}`)
      return false
    }

    // 对于大文件处理，即使是"正常"关闭也可能需要重连
    if (code === 1000 && reason && reason.includes('timeout')) {
      console.log(`🔄 检测到超时关闭，尝试重连`)
      return true
    }

    console.log(`🔄 可以重连 - 关闭码: ${code}, 原因: ${reason}`)
    return true
  }

  /**
   * 安排重连 - 🔧 优化重连策略
   */
  const scheduleReconnect = () => {
    reconnectAttempts.value++

    // 🔧 改进的指数退避算法，使用新的配置
    const baseDelay = config.reconnectInterval
    const backoffMultiplier = Math.min(reconnectAttempts.value - 1, 5) // 最多5次指数增长
    const delay = Math.min(baseDelay * Math.pow(1.5, backoffMultiplier), config.maxBackoffDelay)

    console.log(`🔄 ${delay}ms后尝试第${reconnectAttempts.value}次重连... (最大${config.maxReconnectAttempts}次)`)

    reconnectTimer = setTimeout(() => {
      if (!isConnected.value && reconnectAttempts.value <= config.maxReconnectAttempts) {
        console.log(`🔄 执行第${reconnectAttempts.value}次重连尝试`)
        connect().catch(error => {
          console.error('❌ WebSocket重连失败:', error)

          // 如果还有重连机会，继续尝试
          if (reconnectAttempts.value < config.maxReconnectAttempts) {
            scheduleReconnect()
          } else {
            console.error('❌ 已达到最大重连次数，停止重连')
          }
        })
      }
    }, delay)
  }
  
  /**
   * 启动心跳 - 🔧 增强连接健康检测
   */
  const startHeartbeat = () => {
    // 重置心跳计数
    missedHeartbeats.value = 0

    heartbeatTimer = setInterval(() => {
      if (isConnected.value) {
        // 检查连接状态
        if (ws.value && ws.value.readyState !== WebSocket.OPEN) {
          console.warn('⚠️ WebSocket连接状态异常，尝试重连')
          disconnect()
          return
        }

        send({ type: 'ping', timestamp: Date.now() })

        // 🔧 增强心跳超时处理
        heartbeatTimeout = setTimeout(() => {
          missedHeartbeats.value++
          console.warn(`⚠️ 心跳超时 (${missedHeartbeats.value}/${maxMissedHeartbeats})`)

          if (missedHeartbeats.value >= maxMissedHeartbeats) {
            console.warn('⚠️ 连续心跳超时，重连WebSocket')
            disconnect()
          }
        }, config.heartbeatTimeout)
      }
    }, config.heartbeatInterval)
  }
  
  /**
   * 停止心跳
   */
  const stopHeartbeat = () => {
    if (heartbeatTimer) {
      clearInterval(heartbeatTimer)
      heartbeatTimer = null
    }
    
    if (heartbeatTimeout) {
      clearTimeout(heartbeatTimeout)
      heartbeatTimeout = null
    }
  }
  
  /**
   * 处理消息 - 🔧 增强错误处理
   */
  const handleMessage = (data) => {
    try {
      // 🔧 消息验证
      if (!data || typeof data !== 'object') {
        console.error('❌ 无效的消息格式:', data)
        return
      }

      if (!data.type) {
        console.error('❌ 消息缺少type字段:', data)
        return
      }

      console.log('📨 处理WebSocket消息:', {
        type: data.type,
        task_id: data.task_id,
        hasData: !!data.data
      })

    // 触发通用消息事件
    emit('message', data)

    // 处理特定类型的消息
    switch (data.type) {
      case 'connection_established':
        // 连接建立确认
        console.log('🔗 WebSocket连接已确认:', data)
        emit('connection_established', data)
        break

      case 'heartbeat':
      case 'pong':
        // 🔧 心跳响应 - 重置健康检测计数
        if (heartbeatTimeout) {
          clearTimeout(heartbeatTimeout)
          heartbeatTimeout = null
        }
        // 重置错过的心跳计数
        missedHeartbeats.value = 0
        console.log('💓 心跳响应 - 连接健康')
        break

      case 'progress':
      case 'progress_update':
        // 🔧 修复：正确处理后端发送的消息格式（进度数据在根级别）
        const progressData = {
          task_id: data.task_id,
          state: data.state,
          ready: data.ready,
          successful: data.successful,
          failed: data.failed,
          result: data.result,
          traceback: data.traceback,
          user_id: data.user_id,
          progress: data.progress  // 🔧 关键修复：直接提取progress对象
        }
        console.log('📈 文档进度更新:', {
          taskId: data.task_id,
          messageType: data.type,
          progressData: progressData
        })

        // 🔧 确保进度数据包含必要字段
        if (data.task_id) {
          emit('progress', progressData)
        } else {
          console.warn('⚠️ 进度消息缺少task_id:', data)
        }
        break

      case 'task_completed':
        // 🔧 修复：统一处理新的消息格式 {type, task_id, data}
        const completedData = {
          task_id: data.task_id,
          ...data.data
        }
        console.log('📨 WebSocket完成payload:', completedData)
        console.log('✅ WebSocket任务完成:', completedData)
        emit('task_completed', completedData)
        ElMessage.success('任务处理完成')
        break

      case 'task_failed':
        // 🔧 修复：统一处理新的消息格式 {type, task_id, data}
        const failedData = {
          task_id: data.task_id,
          ...data.data
        }
        console.log('❌ 任务失败:', failedData)
        emit('task_failed', failedData)
        ElMessage.error(`任务处理失败: ${failedData.error || failedData.error_message || '未知错误'}`)
        break

      case 'error':
        // 错误消息
        console.log('⚠️ 服务器错误:', data)
        emit('error', data)
        ElMessage.error(`服务器错误: ${data.message || '未知错误'}`)
        break

      default:
        console.warn('⚠️ 未知的WebSocket消息类型:', data.type)
        console.log('📋 完整消息内容:', data)
    }

    } catch (error) {
      // 🔧 消息处理错误
      console.error('❌ 处理WebSocket消息失败:', {
        error: error.message,
        data: data,
        timestamp: new Date().toISOString()
      })

      // 触发处理错误事件
      emit('handle_error', { error, data })
    }
  }
  
  /**
   * 添加事件监听器
   */
  const on = (eventType, callback) => {
    if (!listeners.has(eventType)) {
      listeners.set(eventType, new Set())
    }
    listeners.get(eventType).add(callback)
    
    // 返回取消监听的函数
    return () => {
      const eventListeners = listeners.get(eventType)
      if (eventListeners) {
        eventListeners.delete(callback)
      }
    }
  }
  
  /**
   * 移除事件监听器
   */
  const off = (eventType, callback) => {
    const eventListeners = listeners.get(eventType)
    if (eventListeners) {
      eventListeners.delete(callback)
    }
  }
  
  /**
   * 触发事件
   */
  const emit = (eventType, data = null) => {
    const eventListeners = listeners.get(eventType)
    if (eventListeners) {
      eventListeners.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error(`事件监听器错误 (${eventType}):`, error)
        }
      })
    }
  }
  
  /**
   * 订阅任务进度
   */
  const subscribeTask = (taskId) => {
    console.log(`🔔 订阅任务: ${taskId}`)
    console.log(`🔍 WebSocket状态: 连接=${isConnected.value}, 状态=${connectionStatus.value}`)
    console.log(`📦 消息队列长度: ${messageQueue.value.length}`)

    const message = {
      type: 'subscribe',
      task_id: taskId
    }
    console.log(`📤 准备发送订阅消息:`, message)

    const result = send(message)
    console.log(`📤 订阅消息发送结果: ${result}`)

    if (!result) {
      console.log(`⚠️ 订阅消息未立即发送，已加入队列`)
    }

    return result
  }

  /**
   * 取消订阅任务进度
   */
  const unsubscribeTask = (taskId) => {
    return send({
      type: 'unsubscribe',
      task_id: taskId
    })
  }

  return {
    // 状态
    connectionStatus,
    reconnectAttempts,
    lastError,

    // 计算属性
    isConnected,
    isConnecting,
    isDisconnected,
    hasError,

    // 方法
    connect,
    disconnect,
    send,
    subscribeTask,
    unsubscribeTask,
    on,
    off,
    getStatus: () => ({
      status: connectionStatus.value,
      isConnected: isConnected.value,
      isConnecting: isConnecting.value,
      isDisconnected: isDisconnected.value,
      hasError: hasError.value,
      reconnectAttempts: reconnectAttempts.value,
      lastError: lastError.value
    })
  }
})
