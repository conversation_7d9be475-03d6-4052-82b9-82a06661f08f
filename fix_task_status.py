#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复任务状态 - 自动检测并更新已完成的任务
"""

try:
    print("🔧 开始任务状态修复...")

    from backend.models.task_models import TaskRecord
    from backend.models.audio import AudioFile
    from backend.core.database import get_db
    from backend.services.task_persistence_service import get_task_persistence_service, TaskStatus

    db = next(get_db())
    persistence_service = get_task_persistence_service()

    print("\n🔍 分析当前任务状态...")

    # 查找所有音频处理任务
    all_audio_tasks = db.query(TaskRecord).filter(
        TaskRecord.task_type.in_(['audio_processing', 'speech_recognition', 'speaker_recognition', 'vad_detection'])
    ).all()

    print(f"总音频任务数: {len(all_audio_tasks)}")

    # 统计各种状态
    status_counts = {}
    for task in all_audio_tasks:
        status = task.status
        status_counts[status] = status_counts.get(status, 0) + 1

    print("当前状态分布:")
    for status, count in status_counts.items():
        print(f"  {status}: {count}")

    print("\n🔧 修复进度100%但状态不正确的任务...")

    # 查找进度100%但状态不是SUCCESS的任务
    tasks_to_fix = db.query(TaskRecord).filter(
        TaskRecord.task_type.in_(['audio_processing', 'speech_recognition', 'speaker_recognition', 'vad_detection']),
        TaskRecord.progress_percentage >= 100,
        TaskRecord.status.in_(['STARTED', 'PENDING'])
    ).all()

    print(f"找到 {len(tasks_to_fix)} 个需要修复的任务")

    fixed_count = 0
    for task in tasks_to_fix:
        print(f"修复任务: {task.task_id[:8]}... (进度: {task.progress_percentage}%, 状态: {task.status})")

        # 更新任务状态为成功
        persistence_service.update_task_status(
            db=db,
            task_id=task.task_id,
            status=TaskStatus.SUCCESS,
            progress_percentage=100.0,
            progress_detail="任务完成",
            progress_stage="completed"
        )
        fixed_count += 1

    print(f"✅ 已修复 {fixed_count} 个任务状态")

    print("\n🔧 修复音频文件状态...")

    # 查找状态为analyzed的文件，改为completed
    files_to_fix = db.query(AudioFile).filter(
        AudioFile.status == "analyzed"
    ).all()

    print(f"找到 {len(files_to_fix)} 个需要修复的文件")

    fixed_file_count = 0
    for file in files_to_fix:
        print(f"修复文件: {file.original_filename} (状态: {file.status})")
        file.status = "completed"
        fixed_file_count += 1

    # 提交文件状态更改
    db.commit()
    print(f"✅ 已修复 {fixed_file_count} 个文件状态")

    print("\n📊 验证修复结果...")

    # 重新统计状态
    all_audio_tasks_after = db.query(TaskRecord).filter(
        TaskRecord.task_type.in_(['audio_processing', 'speech_recognition', 'speaker_recognition', 'vad_detection'])
    ).all()

    status_counts_after = {}
    for task in all_audio_tasks_after:
        status = task.status
        status_counts_after[status] = status_counts_after.get(status, 0) + 1

    print("修复后状态分布:")
    for status, count in status_counts_after.items():
        print(f"  {status}: {count}")

    # 查询活跃任务
    active_tasks = db.query(TaskRecord).filter(
        TaskRecord.task_type.in_(['audio_processing', 'speech_recognition', 'speaker_recognition', 'vad_detection']),
        TaskRecord.status.in_(['PENDING', 'STARTED', 'RETRY'])
    ).count()

    print(f"\n活跃任务数量: {active_tasks}")

    # 查询已完成任务
    completed_tasks = db.query(TaskRecord).filter(
        TaskRecord.task_type.in_(['audio_processing', 'speech_recognition', 'speaker_recognition', 'vad_detection']),
        TaskRecord.status == "SUCCESS"
    ).count()

    print(f"已完成任务数量: {completed_tasks}")

    # 查询已完成文件
    completed_files = db.query(AudioFile).filter(
        AudioFile.status == "completed"
    ).count()

    print(f"已完成文件数量: {completed_files}")

    db.close()
    print("\n✅ 任务状态修复完成")

except Exception as e:
    print(f"❌ 修复失败: {e}")
    import traceback
    traceback.print_exc()
