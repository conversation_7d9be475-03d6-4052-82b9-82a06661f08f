# ===========================================
# 语音处理智能平台 Backend 服务 Dockerfile
# 专门解决向量维度不匹配问题的环境一致性方案
# ===========================================

# 使用Python 3.11官方镜像作为基础镜像
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PIP_NO_CACHE_DIR=1 \
    PIP_DISABLE_PIP_VERSION_CHECK=1 \
    PYTHONPATH=/app \
    DOCKER_ENV=true

# 安装系统依赖和uv
RUN apt-get update && apt-get install -y \
    # 编译工具
    gcc \
    g++ \
    make \
    cmake \
    build-essential \
    # 系统库
    libffi-dev \
    libssl-dev \
    libsqlite3-dev \
    # 音频处理依赖
    libasound2-dev \
    libportaudio2 \
    libportaudiocpp0 \
    portaudio19-dev \
    libsndfile1-dev \
    # 多媒体处理
    ffmpeg \
    # OCR相关依赖
    tesseract-ocr \
    tesseract-ocr-chi-sim \
    tesseract-ocr-chi-tra \
    tesseract-ocr-eng \
    libtesseract-dev \
    # 网络和工具
    git \
    curl \
    wget \
    # 清理缓存
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean \
    # 安装uv (指定与本地相同的版本 0.7.1)
    && curl -LsSf https://astral.sh/uv/0.7.1/install.sh | sh \
    && mv /root/.local/bin/uv /usr/local/bin/uv \
    && mv /root/.local/bin/uvx /usr/local/bin/uvx

# 创建必要的目录结构
RUN mkdir -p /app/backend \
    /app/data \
    /app/data/chroma_db \
    /app/data/uploads \
    /app/logs \
    /app/models \
    /app/cache

# 安装Python依赖 (不复制虚拟环境，而是重新安装)
# 这避免了Windows/Linux虚拟环境路径不兼容的问题
COPY requirements.txt /app/

# 使用uv创建虚拟环境并安装依赖
RUN uv venv /app/.venv

# 设置uv使用虚拟环境
ENV UV_VENV=/app/.venv

# 设置uv网络配置 - 解决大文件下载和网络连接问题
ENV UV_HTTP_TIMEOUT=1200
ENV UV_HTTP_RETRIES=5
ENV UV_CONCURRENT_DOWNLOADS=1

# 设置AI库缓存目录环境变量 - 使用挂载的volumes目录
ENV NLTK_DATA=/root/nltk_data
ENV HF_HOME=/root/.cache/huggingface
ENV TORCH_HOME=/root/.cache/torch
ENV TRANSFORMERS_CACHE=/root/.cache/huggingface/transformers
ENV HF_DATASETS_CACHE=/root/.cache/huggingface/datasets

# 设置Tesseract OCR环境变量
ENV TESSDATA_PREFIX=/usr/share/tesseract-ocr/5/tessdata/
ENV TESSERACT_CMD=/usr/bin/tesseract

# 验证Tesseract安装并显示支持的语言
RUN tesseract --version && \
    tesseract --list-langs && \
    echo "Tesseract OCR安装成功，支持中英文识别"

# 第一步：安装PyTorch (使用官方CUDA索引，增加重试机制和容错处理)
RUN for i in 1 2 3; do \
        uv pip install torch==2.6.0 torchvision==0.21.0 torchaudio==2.6.0 \
        --index-url https://download.pytorch.org/whl/cu124 \
        --no-cache-dir && break || sleep 30; \
    done

# 第二步：安装PyTorch相关的AI库
RUN uv pip install \
    sentence-transformers==4.1.0 \
    accelerate==1.7.0 \
    datasets==3.6.0 \
    evaluate==0.4.3 \
    scikit-learn==1.7.0 \
    numba==0.61.2 \
    llvmlite==0.44.0 \
    --index-url https://mirrors.aliyun.com/pypi/simple/ \
    --trusted-host mirrors.aliyun.com

# 第三步：安装FunASR和其他语音处理库
RUN uv pip install \
    funasr==1.2.6 \
    kaldiio==2.18.1 \
    --index-url https://mirrors.aliyun.com/pypi/simple/ \
    --trusted-host mirrors.aliyun.com

# 第四步：单独安装LlamaIndex相关包（使用官方PyPI源，兼容版本组合）
RUN uv pip install \
    llama-index==0.10.68 \
    llama-index-core==0.10.68.post1 \
    llama-index-embeddings-huggingface==0.2.3 \
    llama-index-embeddings-ollama==0.2.0 \
    llama-index-embeddings-openai==0.1.11 \
    llama-index-llms-ollama==0.2.2 \
    llama-index-llms-openai==0.1.31 \
    llama-index-vector-stores-chroma==0.1.10 \
    llama-index-readers-file==0.1.33

# 第五步：安装其他基础依赖
RUN uv pip install -r /app/requirements.txt \
    --index-url https://mirrors.aliyun.com/pypi/simple/ \
    --trusted-host mirrors.aliyun.com

# 第六步：安装Docker环境特定依赖
RUN uv pip install \
    gunicorn==21.2.0 \
    uvicorn[standard]==0.24.0 \
    httpx==0.25.0 \
    psutil==5.9.6 \
    redis==5.0.1 \
    celery==5.3.4 \
    --index-url https://mirrors.aliyun.com/pypi/simple/ \
    --trusted-host mirrors.aliyun.com

# 设置虚拟环境路径
ENV PATH="/app/.venv/bin:$PATH" \
    VIRTUAL_ENV="/app/.venv"

# 复制后端代码
COPY backend/ /app/backend/
COPY config/ /app/config/
COPY utils/ /app/utils/

# 注意：pyproject.toml文件不存在，跳过复制

# 创建启动脚本
RUN echo '#!/bin/bash' > /app/start_backend.sh && \
    echo 'set -e' >> /app/start_backend.sh && \
    echo 'echo "🚀 启动语音处理智能平台 Backend 服务..."' >> /app/start_backend.sh && \
    echo 'export PATH="/app/.venv/bin:$PATH"' >> /app/start_backend.sh && \
    echo 'echo "📦 验证关键依赖..."' >> /app/start_backend.sh && \
    echo '/app/.venv/bin/python -c "import chromadb; print(f\"ChromaDB版本: {chromadb.__version__}\")"' >> /app/start_backend.sh && \
    echo 'echo "📁 检查数据目录..."' >> /app/start_backend.sh && \
    echo 'mkdir -p /app/data/chroma_db /app/data/uploads /app/logs' >> /app/start_backend.sh && \
    echo 'echo "🌟 启动Backend服务..."' >> /app/start_backend.sh && \
    echo 'cd /app' >> /app/start_backend.sh && \
    echo 'exec /app/.venv/bin/python -m uvicorn backend.main:app --host ${BACKEND_HOST:-0.0.0.0} --port ${BACKEND_PORT:-8002} --workers 1 --log-level ${LOG_LEVEL:-info}' >> /app/start_backend.sh

# 设置启动脚本权限
RUN chmod +x /app/start_backend.sh

# 创建健康检查脚本
RUN echo '#!/bin/bash' > /app/health_check.sh && \
    echo 'source /app/.venv/bin/activate' >> /app/health_check.sh && \
    echo 'curl -f http://localhost:${BACKEND_PORT:-8002}/health || exit 1' >> /app/health_check.sh

RUN chmod +x /app/health_check.sh

# 设置文件权限
RUN chown -R root:root /app && \
    chmod -R 755 /app

# 暴露端口
EXPOSE 8002

# 健康检查
HEALTHCHECK --interval=30s --timeout=30s --start-period=60s --retries=3 \
    CMD /app/health_check.sh

# 设置工作目录
WORKDIR /app

# 启动命令
CMD ["/app/start_backend.sh"]
