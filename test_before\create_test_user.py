#!/usr/bin/env python3
"""
创建测试用户
"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from backend.core.database import get_db_session
from backend.models.user import User
from backend.core.security import get_password_hash

def create_test_user():
    """创建测试用户"""
    print("🔧 创建测试用户...")
    
    try:
        db = get_db_session()
        
        # 检查用户是否已存在
        existing_user = db.query(User).filter(User.username == "testuser").first()
        
        if existing_user:
            print("   ⚠️ 测试用户已存在，删除旧用户...")
            db.delete(existing_user)
            db.commit()
        
        # 创建新的测试用户
        test_user = User(
            username="testuser",
            full_name_pinyin="ceshiyonghu",
            hashed_password=get_password_hash("testpass123"),
            full_name="测试用户",
            is_active=True,
            is_superuser=False
        )
        
        db.add(test_user)
        db.commit()
        db.refresh(test_user)
        
        print(f"   ✅ 测试用户创建成功:")
        print(f"      ID: {test_user.id}")
        print(f"      用户名: {test_user.username}")
        print(f"      姓名全拼: {test_user.full_name_pinyin}")
        print(f"      全名: {test_user.full_name}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"   ❌ 创建测试用户失败: {e}")
        if 'db' in locals():
            db.rollback()
            db.close()
        return False

def list_users():
    """列出所有用户"""
    print("\n📋 当前数据库中的用户:")
    
    try:
        db = get_db_session()
        users = db.query(User).all()
        
        if not users:
            print("   📭 数据库中没有用户")
        else:
            for user in users:
                print(f"   - ID: {user.id}, 用户名: {user.username}, 全名: {user.full_name}")
        
        db.close()
        
    except Exception as e:
        print(f"   ❌ 获取用户列表失败: {e}")

if __name__ == "__main__":
    print("🚀 开始创建测试用户...")
    
    # 列出现有用户
    list_users()
    
    # 创建测试用户
    success = create_test_user()
    
    if success:
        print("\n🎉 测试用户创建完成！")
        print("\n📝 登录信息:")
        print("   用户名: testuser")
        print("   密码: testpass123")
        
        # 再次列出用户确认
        list_users()
    else:
        print("\n❌ 测试用户创建失败！")
