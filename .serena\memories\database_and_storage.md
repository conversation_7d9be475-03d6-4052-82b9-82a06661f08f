# 数据库和存储结构

## 数据库配置
- **类型**: SQLite
- **文件位置**: `data/speech_platform.db`
- **ORM**: SQLAlchemy
- **连接池**: 支持并发访问
- **备份**: 定期数据备份机制
- **共享访问**: Celery和后端共享同一数据库

## 核心数据表

### 用户管理
```sql
-- 用户表
User:
  - id: 主键
  - username: 用户名 (唯一) - 支持全拼格式
  - email: 邮箱 (可选)
  - password_hash: 密码哈希
  - is_active: 激活状态
  - created_at: 创建时间
  - updated_at: 更新时间
  - role: 用户角色 (admin/user)
```

### 音频处理
```sql
-- 音频文件表
AudioFile:
  - id: 主键
  - filename: 文件名
  - file_path: 存储路径
  - file_size: 文件大小
  - duration: 音频时长
  - format: 音频格式 (.wav, .mp3, .m4a, .flac)
  - owner_id: 用户ID (外键)
  - upload_time: 上传时间
  - status: 处理状态 (pending/processing/completed/error)
  - processed_at: 处理完成时间

-- 处理结果表
ProcessingResult:
  - id: 主键
  - audio_file_id: 音频文件ID (外键)
  - task_type: 任务类型 (speech_recognition/vad_detection/speaker_recognition/meeting_transcription)
  - result_data: 结果数据 (JSON格式)
  - confidence_score: 置信度
  - processing_time: 处理耗时 (秒)
  - model_version: 使用的模型版本
  - created_at: 创建时间

-- 说话人档案表
SpeakerProfile:
  - id: 主键
  - speaker_id: 说话人标识 (说话人1/说话人2)
  - embedding_vector: 声纹特征向量 (BLOB)
  - audio_file_id: 关联音频文件
  - confidence: 识别置信度
  - segment_count: 语音段数量
  - total_duration: 总说话时长
```

### 文档管理
```sql
-- 文档表
ManagedDocument:
  - id: 主键
  - title: 文档标题
  - file_path: 文件路径
  - file_type: 文件类型 (pdf/docx/txt/etc)
  - file_size: 文件大小
  - upload_time: 上传时间
  - processing_status: 处理状态
  - user_id: 用户ID (外键)
  - ocr_completed: OCR处理完成标志
  - vectorized: 向量化完成标志

-- 文档段落表
DocumentSection:
  - id: 主键
  - document_id: 文档ID (外键)
  - section_title: 段落标题
  - content: 段落内容
  - page_number: 页码
  - position: 位置信息 (JSON)
  - vector_id: ChromaDB向量ID
  - chunk_index: 分块索引
```

### 任务管理 (增强版)
```sql
-- 任务记录表
TaskRecord:
  - id: 主键
  - task_id: Celery任务ID (唯一)
  - task_type: 任务类型 (meeting_transcription/speech_recognition/etc)
  - status: 任务状态 (pending/running/success/failure/retry)
  - user_id: 用户ID (外键)
  - file_ids: 关联文件ID列表 (JSON)
  - config: 任务配置参数 (JSON)
  - result: 任务结果 (JSON)
  - created_at: 创建时间
  - started_at: 开始时间
  - completed_at: 完成时间
  - error_message: 错误信息
  - retry_count: 重试次数

-- 任务进度表
TaskProgressLog:
  - id: 主键
  - task_id: 任务ID (外键)
  - progress_percentage: 进度百分比 (0-100)
  - progress_stage: 当前阶段 (initializing/model_loading/processing/completed)
  - progress_detail: 进度详细信息
  - timestamp: 时间戳
  - websocket_sent: WebSocket通知发送状态
```

### 系统管理
```sql
-- 系统配置表
SystemConfig:
  - id: 主键
  - config_key: 配置键
  - config_value: 配置值 (JSON)
  - description: 配置描述
  - updated_at: 更新时间

-- 系统日志表
SystemLog:
  - id: 主键
  - level: 日志级别 (INFO/WARNING/ERROR)
  - module: 模块名称
  - message: 日志消息
  - details: 详细信息 (JSON)
  - timestamp: 时间戳

-- 系统指标表
SystemMetrics:
  - id: 主键
  - metric_name: 指标名称
  - metric_value: 指标值
  - metric_unit: 单位
  - timestamp: 时间戳
```

## 文件存储结构
```
data/
├── speech_platform.db     # 主数据库
├── uploads/               # 上传文件
│   ├── audio/            # 音频文件 (按用户ID分组)
│   │   ├── user_1/
│   │   └── user_2/
│   ├── documents/        # 文档文件
│   └── temp/             # 临时文件
├── processed/             # 处理结果
│   ├── transcripts/      # 转录文本
│   │   ├── meeting/      # 会议转录结果
│   │   └── speech/       # 语音识别结果
│   ├── embeddings/       # 向量嵌入
│   └── analysis/         # 分析结果
├── cache/                 # 缓存文件
│   ├── models/           # 模型缓存
│   └── vectors/          # 向量缓存
└── logs/                  # 日志文件
    ├── app.log           # 应用日志
    ├── celery.log        # Celery任务日志
    └── system.log        # 系统日志
```

## 向量数据库 (ChromaDB)
- **存储位置**: `chroma_db/`
- **集合管理**: 按知识库和用户分组
- **嵌入维度**: 根据sentence-transformers模型配置
- **索引策略**: HNSW算法优化
- **持久化**: 自动持久化到磁盘
- **备份**: 定期向量数据备份

## Redis缓存结构
```
Redis Keys:
├── task_progress:{task_id}     # 任务进度缓存
├── task_status:{task_id}       # 任务状态缓存
├── user_session:{user_id}      # 用户会话缓存
├── model_cache:{model_name}    # 模型加载状态缓存
├── websocket_clients           # WebSocket客户端列表
└── system_metrics              # 系统指标缓存
```

## 数据一致性和完整性

### 事务管理
- **数据库事务**: SQLAlchemy事务管理
- **任务状态同步**: Celery任务与数据库状态同步
- **文件状态一致性**: 文件系统与数据库状态一致
- **错误回滚**: 处理失败时的数据回滚机制

### 数据验证
- **输入验证**: Pydantic模型验证
- **文件完整性**: 上传文件MD5校验
- **数据类型**: 严格的数据类型检查
- **外键约束**: 数据库外键约束保证引用完整性

## 性能优化

### 数据库优化
- **索引策略**: 关键字段建立索引
- **查询优化**: 避免N+1查询问题
- **连接池**: 数据库连接池管理
- **批量操作**: 批量插入和更新操作

### 存储优化
- **文件压缩**: 音频文件适当压缩
- **缓存策略**: 热点数据Redis缓存
- **清理机制**: 定期清理临时文件和过期数据
- **分区存储**: 按时间和用户分区存储

## 备份和恢复

### 数据备份
- **数据库备份**: 定期SQLite数据库备份
- **文件备份**: 重要文件定期备份
- **向量数据备份**: ChromaDB数据备份
- **配置备份**: 系统配置和模型配置备份

### 恢复策略
- **数据恢复**: 从备份恢复数据库
- **文件恢复**: 文件系统恢复机制
- **任务恢复**: 中断任务的恢复和重试
- **状态重建**: 系统状态重建机制

## 监控和维护

### 数据监控
- **存储使用**: 磁盘空间使用监控
- **数据库性能**: 查询性能监控
- **文件完整性**: 定期文件完整性检查
- **数据一致性**: 数据一致性验证

### 维护任务
- **数据清理**: 定期清理过期数据
- **索引重建**: 定期重建数据库索引
- **统计更新**: 更新数据库统计信息
- **日志轮转**: 日志文件轮转和压缩