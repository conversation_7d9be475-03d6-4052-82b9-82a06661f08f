### 修复方案
在DocumentManager的updateDocumentProgress函数中，正确提取双重嵌套的progress对象：

```javascript
const innerProgress = progressData.progress.progress || {}
actualProgressData = {
  task_id: progressData.task_id || taskId,
  percentage: innerProgress.percentage || 0,
  detail: innerProgress.detail || '处理中...',
  stage: innerProgress.stage || 'processing',
  status: progressData.progress.state || 'progress',
  ready: progressData.progress.ready || false,
  successful: progressData.progress.successful || false
}
```