#!/usr/bin/env python3
"""
集成测试实时监控脚本
监控后端服务器、Celery Worker和系统资源状态
"""

import os
import sys
import time
import psutil
import torch
import requests
import json
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class IntegrationTestMonitor:
    """集成测试监控器"""
    
    def __init__(self):
        self.backend_url = "http://localhost:8002"
        self.start_time = time.time()
        self.test_results = []
        
    def get_system_status(self):
        """获取系统状态"""
        # 系统内存
        memory = psutil.virtual_memory()
        
        # CPU使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        
        # GPU状态
        gpu_info = {}
        if torch.cuda.is_available():
            gpu_info = {
                'available': True,
                'device_count': torch.cuda.device_count(),
                'current_device': torch.cuda.current_device(),
                'device_name': torch.cuda.get_device_name(0),
                'total_memory_gb': torch.cuda.get_device_properties(0).total_memory / 1024**3,
                'allocated_memory_gb': torch.cuda.memory_allocated() / 1024**3,
                'reserved_memory_gb': torch.cuda.memory_reserved() / 1024**3
            }
        else:
            gpu_info = {'available': False}
        
        # 进程信息
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'memory_info', 'cpu_percent']):
            try:
                if any(keyword in proc.info['name'].lower() for keyword in ['python', 'uvicorn', 'celery']):
                    processes.append({
                        'pid': proc.info['pid'],
                        'name': proc.info['name'],
                        'memory_mb': proc.info['memory_info'].rss / 1024 / 1024,
                        'cpu_percent': proc.info['cpu_percent']
                    })
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        return {
            'timestamp': datetime.now().strftime('%H:%M:%S'),
            'system_memory': {
                'total_gb': memory.total / 1024**3,
                'used_gb': memory.used / 1024**3,
                'available_gb': memory.available / 1024**3,
                'percent': memory.percent
            },
            'cpu_percent': cpu_percent,
            'gpu_info': gpu_info,
            'processes': processes
        }
    
    def check_backend_health(self):
        """检查后端服务健康状态"""
        try:
            response = requests.get(f"{self.backend_url}/health", timeout=5)
            return {
                'status': 'healthy' if response.status_code == 200 else 'unhealthy',
                'response_time': response.elapsed.total_seconds(),
                'status_code': response.status_code
            }
        except Exception as e:
            return {
                'status': 'error',
                'error': str(e)
            }
    
    def submit_audio_task(self, task_type="speech_recognition"):
        """提交音频处理任务"""
        try:
            # 检查测试音频文件
            audio_file = "resource/对话.mp3"
            if not os.path.exists(audio_file):
                return {'status': 'error', 'error': '测试音频文件不存在'}
            
            # 提交任务（这里模拟API调用）
            task_data = {
                'task_type': task_type,
                'file_path': audio_file,
                'config': {
                    'language': 'auto',
                    'use_itn': True
                }
            }
            
            # 实际应该调用API，这里返回模拟结果
            return {
                'status': 'submitted',
                'task_id': f'test_{int(time.time())}',
                'timestamp': datetime.now().strftime('%H:%M:%S')
            }
            
        except Exception as e:
            return {'status': 'error', 'error': str(e)}
    
    def print_status_report(self, status):
        """打印状态报告"""
        print(f"\n{'='*60}")
        print(f"🕐 时间: {status['timestamp']}")
        print(f"{'='*60}")
        
        # 系统内存
        mem = status['system_memory']
        print(f"💾 系统内存: {mem['percent']:.1f}% ({mem['used_gb']:.1f}GB/{mem['total_gb']:.1f}GB)")
        print(f"   可用内存: {mem['available_gb']:.1f}GB")
        
        # CPU
        print(f"🖥️  CPU使用率: {status['cpu_percent']:.1f}%")
        
        # GPU
        if status['gpu_info']['available']:
            gpu = status['gpu_info']
            print(f"🎯 GPU状态: {gpu['device_name']}")
            print(f"   总显存: {gpu['total_memory_gb']:.1f}GB")
            print(f"   已分配: {gpu['allocated_memory_gb']:.1f}GB")
            print(f"   已缓存: {gpu['reserved_memory_gb']:.1f}GB")
            print(f"   可用显存: {gpu['total_memory_gb'] - gpu['reserved_memory_gb']:.1f}GB")
        else:
            print("🎯 GPU: 不可用")
        
        # 进程信息
        print(f"🔄 相关进程:")
        for proc in status['processes']:
            print(f"   PID {proc['pid']:5d}: {proc['name']:15s} "
                  f"内存: {proc['memory_mb']:6.1f}MB CPU: {proc['cpu_percent']:5.1f}%")
    
    def run_continuous_monitoring(self, duration_minutes=10):
        """运行连续监控"""
        print(f"🚀 开始集成测试监控 (持续{duration_minutes}分钟)")
        print(f"监控目标:")
        print(f"  - 后端服务器: {self.backend_url}")
        print(f"  - Celery Worker进程")
        print(f"  - 系统资源使用")
        print(f"  - GPU显存管理")
        
        end_time = time.time() + (duration_minutes * 60)
        
        while time.time() < end_time:
            # 获取系统状态
            status = self.get_system_status()
            
            # 检查后端健康状态
            backend_health = self.check_backend_health()
            status['backend_health'] = backend_health
            
            # 打印状态报告
            self.print_status_report(status)
            
            # 后端健康状态
            if backend_health['status'] == 'healthy':
                print(f"✅ 后端服务: 健康 (响应时间: {backend_health['response_time']:.3f}s)")
            else:
                print(f"❌ 后端服务: {backend_health.get('error', '不健康')}")
            
            # 保存状态数据
            self.test_results.append(status)
            
            # 等待下一次检查
            print(f"⏳ 等待30秒后继续监控...")
            time.sleep(30)
        
        # 生成最终报告
        self.generate_final_report()
    
    def generate_final_report(self):
        """生成最终测试报告"""
        print(f"\n{'='*60}")
        print(f"📊 集成测试最终报告")
        print(f"{'='*60}")
        
        if not self.test_results:
            print("❌ 没有收集到测试数据")
            return
        
        # 内存使用分析
        memory_percents = [r['system_memory']['percent'] for r in self.test_results]
        memory_used_gb = [r['system_memory']['used_gb'] for r in self.test_results]
        
        print(f"💾 内存使用分析:")
        print(f"   初始使用率: {memory_percents[0]:.1f}%")
        print(f"   最终使用率: {memory_percents[-1]:.1f}%")
        print(f"   最高使用率: {max(memory_percents):.1f}%")
        print(f"   平均使用率: {sum(memory_percents)/len(memory_percents):.1f}%")
        print(f"   内存变化: {memory_percents[-1] - memory_percents[0]:+.1f}%")
        
        # GPU使用分析
        if self.test_results[0]['gpu_info']['available']:
            gpu_allocated = [r['gpu_info']['allocated_memory_gb'] for r in self.test_results]
            gpu_reserved = [r['gpu_info']['reserved_memory_gb'] for r in self.test_results]
            
            print(f"🎯 GPU显存分析:")
            print(f"   初始分配: {gpu_allocated[0]:.1f}GB")
            print(f"   最终分配: {gpu_allocated[-1]:.1f}GB")
            print(f"   最高分配: {max(gpu_allocated):.1f}GB")
            print(f"   初始缓存: {gpu_reserved[0]:.1f}GB")
            print(f"   最终缓存: {gpu_reserved[-1]:.1f}GB")
            print(f"   最高缓存: {max(gpu_reserved):.1f}GB")
        
        # 后端健康状态
        healthy_count = sum(1 for r in self.test_results if r.get('backend_health', {}).get('status') == 'healthy')
        print(f"🏥 后端服务健康率: {healthy_count}/{len(self.test_results)} ({healthy_count/len(self.test_results)*100:.1f}%)")
        
        # 稳定性评估
        memory_growth = memory_percents[-1] - memory_percents[0]
        if memory_growth < 2:
            print(f"✅ 内存稳定性: 优秀 (增长{memory_growth:.1f}%)")
        elif memory_growth < 5:
            print(f"⚠️ 内存稳定性: 良好 (增长{memory_growth:.1f}%)")
        else:
            print(f"❌ 内存稳定性: 需要改进 (增长{memory_growth:.1f}%)")
        
        # 保存详细数据
        report_file = f"integration_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False)
        print(f"📄 详细报告已保存: {report_file}")

def main():
    """主函数"""
    monitor = IntegrationTestMonitor()
    
    try:
        # 运行5分钟的连续监控
        monitor.run_continuous_monitoring(duration_minutes=5)
    except KeyboardInterrupt:
        print("\n⏹️ 监控被用户中断")
        monitor.generate_final_report()
    except Exception as e:
        print(f"\n❌ 监控过程中发生错误: {e}")

if __name__ == "__main__":
    main()
