"""
调试任务状态更新问题的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.models.task_models import TaskRecord, TaskStatus
from backend.core.database import get_db
from backend.services.task_persistence_service import TaskPersistenceService
import redis

def debug_task_status():
    print("🔍 调试任务状态更新问题...")
    
    # 连接数据库
    db = next(get_db())
    persistence_service = TaskPersistenceService()
    
    # 连接Redis
    redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
    
    # 查找最新的会议转录任务
    task_id = "meeting_transcription_a8c695b2c7d1"
    
    print(f"\n📋 检查任务: {task_id}")
    print("=" * 80)
    
    # 1. 检查数据库状态
    task_record = db.query(TaskRecord).filter(TaskRecord.task_id == task_id).first()
    if task_record:
        print(f"💾 数据库状态:")
        print(f"  - 状态: {task_record.status}")
        print(f"  - 进度: {task_record.progress_percentage}%")
        print(f"  - 详情: {task_record.progress_detail}")
        print(f"  - 阶段: {task_record.progress_stage}")
        print(f"  - 创建时间: {task_record.created_at}")
        print(f"  - 更新时间: {task_record.updated_at}")
        print(f"  - 开始时间: {task_record.started_at}")
        print(f"  - 完成时间: {task_record.completed_at}")
    else:
        print("❌ 数据库中未找到任务记录")
    
    # 2. 检查Redis状态
    progress_key = f"task_progress:{task_id}"
    redis_data = redis_client.hgetall(progress_key)
    if redis_data:
        print(f"\n📊 Redis状态:")
        for key, value in redis_data.items():
            print(f"  - {key}: {value}")
    else:
        print("\n❌ Redis中未找到进度数据")
    
    # 3. 尝试手动更新数据库状态
    print(f"\n🔧 尝试手动更新数据库状态...")
    try:
        updated_record = persistence_service.update_task_status(
            db=db,
            task_id=task_id,
            status=TaskStatus.SUCCESS,
            progress_percentage=100.0,
            progress_detail="手动更新：任务完成",
            progress_stage="completed"
        )
        if updated_record:
            print(f"✅ 数据库状态更新成功")
            print(f"  - 新状态: {updated_record.status}")
            print(f"  - 新进度: {updated_record.progress_percentage}%")
        else:
            print("❌ 数据库状态更新失败")
    except Exception as e:
        print(f"❌ 数据库状态更新异常: {e}")
    
    # 4. 再次检查数据库状态
    task_record = db.query(TaskRecord).filter(TaskRecord.task_id == task_id).first()
    if task_record:
        print(f"\n💾 更新后数据库状态:")
        print(f"  - 状态: {task_record.status}")
        print(f"  - 进度: {task_record.progress_percentage}%")
        print(f"  - 详情: {task_record.progress_detail}")
        print(f"  - 阶段: {task_record.progress_stage}")
        print(f"  - 更新时间: {task_record.updated_at}")
        print(f"  - 完成时间: {task_record.completed_at}")

    # 5. 检查Celery任务状态
    print(f"\n🔄 检查Celery任务状态:")
    from celery.result import AsyncResult
    celery_result = AsyncResult(task_id)
    print(f"  - Celery状态: {celery_result.state}")
    print(f"  - 是否就绪: {celery_result.ready()}")
    print(f"  - 是否成功: {celery_result.successful()}")
    print(f"  - 是否失败: {celery_result.failed()}")
    if celery_result.ready():
        try:
            result = celery_result.result
            print(f"  - 结果类型: {type(result)}")
            if isinstance(result, dict):
                print(f"  - 结果键: {list(result.keys())}")
        except Exception as e:
            print(f"  - 获取结果失败: {e}")

    db.close()

if __name__ == "__main__":
    debug_task_status()
