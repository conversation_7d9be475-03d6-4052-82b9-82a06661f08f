#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Redis中的任务进度数据
"""

import redis
import json
from datetime import datetime

# Redis连接配置
redis_client = redis.Redis(
    host='localhost',
    port=6379,
    db=0,
    decode_responses=True
)

def check_redis_progress():
    """检查Redis中的进度数据"""
    print("=" * 80)
    print("Redis任务进度数据检查")
    print("=" * 80)
    
    try:
        # 查找所有任务进度键
        progress_keys = redis_client.keys("task_progress:*")
        
        if not progress_keys:
            print("❌ 没有找到任何任务进度数据")
            return
        
        print(f"✅ 找到 {len(progress_keys)} 个任务进度记录\n")
        
        for key in progress_keys:
            task_id = key.split(":")[-1]
            print(f"📋 任务ID: {task_id}")
            print(f"🔑 Redis键: {key}")
            
            # 获取进度数据
            progress_data = redis_client.hgetall(key)
            
            if progress_data:
                print("📊 进度数据:")
                for field, value in progress_data.items():
                    if field in ['start_time', 'update_time'] and value:
                        try:
                            timestamp = float(value)
                            readable_time = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                            print(f"  {field}: {value} ({readable_time})")
                        except:
                            print(f"  {field}: {value}")
                    else:
                        print(f"  {field}: {value}")
                
                # 检查TTL
                ttl = redis_client.ttl(key)
                if ttl > 0:
                    print(f"⏰ TTL: {ttl}秒 ({ttl//60}分钟)")
                elif ttl == -1:
                    print("⏰ TTL: 永不过期")
                else:
                    print("⏰ TTL: 已过期")
            else:
                print("❌ 无法获取进度数据")
            
            print("-" * 60)
        
        # 检查其他相关的Redis键
        print("\n🔍 检查其他相关键:")
        
        # 检查文件键
        file_keys = redis_client.keys("file:*")
        print(f"📁 文件键数量: {len(file_keys)}")
        
        # 检查用户任务键
        user_task_keys = redis_client.keys("user_tasks:*")
        print(f"👤 用户任务键数量: {len(user_task_keys)}")
        
        # 检查队列信息
        queue_info = redis_client.info('memory')
        print(f"💾 Redis内存使用: {queue_info.get('used_memory_human', 'N/A')}")
        
    except Exception as e:
        print(f"❌ 检查Redis数据失败: {e}")

def monitor_redis_changes():
    """监控Redis数据变化"""
    print("\n" + "=" * 80)
    print("Redis数据变化监控 (按Ctrl+C停止)")
    print("=" * 80)
    
    try:
        # 使用Redis的MONITOR命令监控所有操作
        print("⚠️ 注意: 这会显示所有Redis操作，可能很多")
        print("正在监控Redis操作...")
        
        # 简化版：只监控task_progress相关的键
        import time
        last_keys = set()
        
        while True:
            current_keys = set(redis_client.keys("task_progress:*"))
            
            # 检查新增的键
            new_keys = current_keys - last_keys
            if new_keys:
                for key in new_keys:
                    print(f"🆕 新任务: {key}")
                    progress_data = redis_client.hgetall(key)
                    print(f"   数据: {progress_data}")
            
            # 检查更新的键
            for key in current_keys:
                if key in last_keys:
                    # 检查是否有更新
                    progress_data = redis_client.hgetall(key)
                    if progress_data.get('update_time'):
                        print(f"🔄 更新任务: {key}")
                        print(f"   进度: {progress_data.get('percentage', 0)}%")
                        print(f"   详情: {progress_data.get('detail', '')}")
                        print(f"   阶段: {progress_data.get('stage', '')}")
            
            # 检查删除的键
            deleted_keys = last_keys - current_keys
            if deleted_keys:
                for key in deleted_keys:
                    print(f"🗑️ 删除任务: {key}")
            
            last_keys = current_keys
            time.sleep(2)  # 每2秒检查一次
            
    except KeyboardInterrupt:
        print("\n⏹️ 监控已停止")
    except Exception as e:
        print(f"❌ 监控失败: {e}")

def test_redis_connection():
    """测试Redis连接"""
    print("🔗 测试Redis连接...")
    try:
        # 测试连接
        redis_client.ping()
        print("✅ Redis连接正常")
        
        # 测试读写
        test_key = "test_connection"
        redis_client.set(test_key, "test_value", ex=10)
        value = redis_client.get(test_key)
        
        if value == "test_value":
            print("✅ Redis读写正常")
            redis_client.delete(test_key)
        else:
            print("❌ Redis读写异常")
            
    except Exception as e:
        print(f"❌ Redis连接失败: {e}")

if __name__ == "__main__":
    print("Redis任务进度检查工具")
    print("选择操作:")
    print("1. 检查当前进度数据")
    print("2. 监控数据变化")
    print("3. 测试Redis连接")
    print("4. 全部执行")
    
    choice = input("\n请输入选择 (1-4): ").strip()
    
    if choice == "1":
        check_redis_progress()
    elif choice == "2":
        monitor_redis_changes()
    elif choice == "3":
        test_redis_connection()
    elif choice == "4":
        test_redis_connection()
        check_redis_progress()
        print("\n是否继续监控数据变化? (y/n): ", end="")
        if input().lower() == 'y':
            monitor_redis_changes()
    else:
        print("无效选择，执行默认检查...")
        check_redis_progress()
