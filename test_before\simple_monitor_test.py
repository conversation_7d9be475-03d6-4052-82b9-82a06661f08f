#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的监控组件验证脚本
"""

import sys
import os

# 添加utils目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))

def test_imports():
    """测试导入"""
    try:
        print("🧪 开始测试监控组件导入...")
        
        # 基础导入测试
        from utils.monitoring_components import ProcessingMonitor
        print("✅ ProcessingMonitor 导入成功")
        
        from utils.monitoring_components import MonitoringComponents
        print("✅ MonitoringComponents 导入成功")
        
        from utils.monitoring_components import IntegratedMonitorWidget
        print("✅ IntegratedMonitorWidget 导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False

def test_basic_monitor():
    """测试基础监控功能"""
    try:
        print("\n🔍 测试基础监控功能...")
        
        from utils.monitoring_components import ProcessingMonitor
        
        # 创建监控器
        monitor = ProcessingMonitor("test_monitor")
        print("✅ 监控器创建成功")
        
        # 测试任务更新
        monitor.update_task("test_task_1", "running", 50.0, {"test": "data"})
        print("✅ 任务状态更新成功")
        
        # 测试统计获取
        stats = monitor.get_stats()
        print(f"✅ 统计信息获取成功: {stats['total_tasks']} 个任务")
        
        # 测试错误添加
        monitor.add_error("测试错误", "test_task_1", "TEST_ERROR")
        print("✅ 错误记录成功")
        
        # 测试性能指标
        monitor.add_performance_metric("test_metric", 123.45, "test_task_1")
        print("✅ 性能指标记录成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 基础监控测试失败: {e}")
        return False

def test_widget():
    """测试监控小部件"""
    try:
        print("\n🎨 测试监控小部件...")
        
        from utils.monitoring_components import IntegratedMonitorWidget
        
        # 创建监控小部件
        widget = IntegratedMonitorWidget("test_widget")
        print("✅ 监控小部件创建成功")
        
        # 测试进度更新
        widget.update_progress("widget_task", 75.0, "running", {"stage": "测试阶段"})
        print("✅ 进度更新成功")
        
        # 测试任务完成
        widget.complete_task("widget_task", 2.5, True, {"result": "成功"})
        print("✅ 任务完成记录成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 监控小部件测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 监控组件简化验证测试")
    print("=" * 40)
    
    success_count = 0
    total_tests = 3
    
    # 测试导入
    if test_imports():
        success_count += 1
    
    # 测试基础功能
    if test_basic_monitor():
        success_count += 1
    
    # 测试小部件
    if test_widget():
        success_count += 1
    
    print("\n" + "=" * 40)
    print(f"📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！监控组件工作正常")
        print("\n✅ 主要功能验证:")
        print("  - 监控器创建和管理")
        print("  - 任务状态跟踪")
        print("  - 性能指标收集")
        print("  - 错误日志记录")
        print("  - 监控小部件集成")
        
        print("\n🚀 可以在Streamlit应用中使用监控功能:")
        print("  1. 启动语音处理分析页面")
        print("  2. 勾选'显示实时监控'选项")
        print("  3. 上传音频文件进行处理")
        print("  4. 观察实时监控效果")
        
    else:
        print("❌ 部分测试失败，请检查环境配置")
    
    return success_count == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 