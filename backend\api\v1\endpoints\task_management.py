"""
任务管理API端点
提供任务状态查询、重试、恢复等功能
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.security import HTTPAuthorizationCredentials
from pydantic import BaseModel

from backend.core.security import get_current_user_id
from fastapi.security import HTTPBearer

security = HTTPBearer()
from backend.services.task_persistence_service import get_task_persistence_service
from backend.services.task_recovery_service import get_task_recovery_service
from backend.core.database import get_db_session
from backend.models.task_models import TaskStatus
from loguru import logger

router = APIRouter()


class TaskRetryRequest(BaseModel):
    """任务重试请求"""
    task_ids: List[str]


class TaskQueryParams(BaseModel):
    """任务查询参数"""
    status: Optional[str] = None
    task_type: Optional[str] = None
    limit: int = 100
    offset: int = 0


@router.get("/tasks")
async def get_user_tasks(
    status: Optional[str] = Query(None, description="任务状态过滤"),
    task_type: Optional[str] = Query(None, description="任务类型过滤"),
    limit: int = Query(100, description="返回数量限制"),
    offset: int = Query(0, description="偏移量"),
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """获取用户的任务列表"""
    try:
        user_id = get_current_user_id(credentials)
        persistence_service = get_task_persistence_service()
        
        db = get_db_session()
        try:
            # 验证状态参数
            task_status = None
            if status:
                try:
                    task_status = TaskStatus(status.upper())
                except ValueError:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"无效的任务状态: {status}"
                    )
            
            # 获取任务列表
            tasks = persistence_service.get_user_tasks(
                db=db,
                user_id=user_id,
                status=task_status,
                task_type=task_type,
                limit=limit,
                offset=offset
            )
            
            return {
                "success": True,
                "tasks": [task.to_dict() for task in tasks],
                "total": len(tasks),
                "limit": limit,
                "offset": offset
            }
            
        finally:
            db.close()
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取用户任务列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取任务列表失败"
        )


@router.get("/tasks/{task_id}")
async def get_task_detail(
    task_id: str,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """获取任务详细信息"""
    try:
        user_id = get_current_user_id(credentials)
        persistence_service = get_task_persistence_service()
        
        db = get_db_session()
        try:
            # 获取任务记录
            task_record = persistence_service.get_task_record(db, task_id)
            
            if not task_record:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="任务不存在"
                )
            
            # 检查权限
            if task_record.user_id != user_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权访问此任务"
                )
            
            # 获取进度历史
            progress_history = persistence_service.get_task_progress_history(db, task_id)
            
            return {
                "success": True,
                "task": task_record.to_dict(),
                "progress_history": [log.to_dict() for log in progress_history]
            }
            
        finally:
            db.close()
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务详情失败: {task_id}, {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取任务详情失败"
        )


@router.get("/tasks/{task_id}/progress")
async def get_task_progress(
    task_id: str,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """获取任务实时进度"""
    try:
        user_id = get_current_user_id(credentials)
        persistence_service = get_task_persistence_service()

        db = get_db_session()
        try:
            # 获取任务记录
            task_record = persistence_service.get_task_record(db, task_id)

            if not task_record:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="任务不存在"
                )

            # 检查权限
            if task_record.user_id != user_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权访问此任务"
                )

            # 获取最新进度
            latest_progress = persistence_service.get_latest_task_progress(db, task_id)

            # 构建进度响应
            progress_data = {
                "task_id": task_id,
                "status": task_record.status.value if task_record.status else "unknown",
                "percentage": latest_progress.percentage if latest_progress else 0.0,
                "detail": latest_progress.detail if latest_progress else "",
                "stage": latest_progress.stage if latest_progress else "initializing",
                "start_time": task_record.created_at.isoformat() if task_record.created_at else None,
                "updated_at": latest_progress.created_at.isoformat() if latest_progress else None
            }

            # 如果任务完成，添加结果信息
            if task_record.status and task_record.status.value in ['completed', 'failed']:
                progress_data["result"] = task_record.result
                progress_data["error_message"] = task_record.error_message

            return {
                "success": True,
                "progress": progress_data
            }

        finally:
            db.close()

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取任务进度失败: {task_id}, {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取任务进度失败"
        )


@router.post("/tasks/{task_id}/retry")
async def retry_task(
    task_id: str,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """重试失败的任务"""
    try:
        user_id = get_current_user_id(credentials)
        persistence_service = get_task_persistence_service()
        recovery_service = get_task_recovery_service()
        
        db = get_db_session()
        try:
            # 检查任务是否存在和权限
            task_record = persistence_service.get_task_record(db, task_id)
            
            if not task_record:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="任务不存在"
                )
            
            if task_record.user_id != user_id:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="无权操作此任务"
                )
            
        finally:
            db.close()
        
        # 执行重试
        success = recovery_service.retry_failed_task(task_id)
        
        if success:
            return {
                "success": True,
                "message": "任务重试成功",
                "task_id": task_id
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="任务重试失败，请检查任务状态和重试次数"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"重试任务失败: {task_id}, {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="重试任务失败"
        )


@router.post("/tasks/batch-retry")
async def batch_retry_tasks(
    request: TaskRetryRequest,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """批量重试失败的任务"""
    try:
        user_id = get_current_user_id(credentials)
        recovery_service = get_task_recovery_service()
        
        # 执行批量重试
        results = recovery_service.batch_retry_failed_tasks(user_id)
        
        return {
            "success": True,
            "message": "批量重试完成",
            "results": results
        }
        
    except Exception as e:
        logger.error(f"批量重试任务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="批量重试失败"
        )


@router.get("/tasks/statistics")
async def get_task_statistics(
    days: int = Query(7, description="统计天数"),
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """获取任务统计信息"""
    try:
        user_id = get_current_user_id(credentials)
        persistence_service = get_task_persistence_service()
        
        db = get_db_session()
        try:
            # 获取任务统计
            stats = persistence_service.get_task_statistics(db, user_id, days)
            
            return {
                "success": True,
                "statistics": stats,
                "period_days": days
            }
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"获取任务统计失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取统计信息失败"
        )


@router.get("/tasks/recovery-status")
async def get_recovery_status(
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """获取任务恢复状态"""
    try:
        user_id = get_current_user_id(credentials)
        recovery_service = get_task_recovery_service()
        
        # 获取恢复状态
        status_info = recovery_service.get_task_recovery_status(user_id)
        
        return {
            "success": True,
            "recovery_status": status_info
        }
        
    except Exception as e:
        logger.error(f"获取恢复状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取恢复状态失败"
        )


@router.post("/tasks/recover-orphaned")
async def recover_orphaned_tasks(
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """恢复孤儿任务（管理员功能）"""
    try:
        user_id = get_current_user_id(credentials)
        
        # 这里可以添加管理员权限检查
        # if not is_admin(user_id):
        #     raise HTTPException(status_code=403, detail="需要管理员权限")
        
        recovery_service = get_task_recovery_service()
        
        # 执行孤儿任务恢复
        results = recovery_service.recover_orphaned_tasks()
        
        return {
            "success": True,
            "message": "孤儿任务恢复完成",
            "results": results
        }
        
    except Exception as e:
        logger.error(f"恢复孤儿任务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="恢复孤儿任务失败"
        )


@router.delete("/tasks/cleanup")
async def cleanup_old_tasks(
    days: int = Query(30, description="清理多少天前的任务"),
    keep_failed: bool = Query(True, description="是否保留失败的任务"),
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """清理旧任务记录（管理员功能）"""
    try:
        user_id = get_current_user_id(credentials)
        
        # 这里可以添加管理员权限检查
        # if not is_admin(user_id):
        #     raise HTTPException(status_code=403, detail="需要管理员权限")
        
        persistence_service = get_task_persistence_service()
        
        db = get_db_session()
        try:
            # 执行清理
            deleted_count = persistence_service.cleanup_old_tasks(db, days, keep_failed)
            
            return {
                "success": True,
                "message": f"清理完成，删除了 {deleted_count} 个任务记录",
                "deleted_count": deleted_count,
                "days": days,
                "keep_failed": keep_failed
            }
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"清理旧任务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="清理任务失败"
        )
