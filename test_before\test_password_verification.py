#!/usr/bin/env python3
"""
测试密码验证
"""

import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from backend.core.database import get_db_session
from backend.models.user import User
from backend.core.security import verify_password, get_password_hash

def test_password_verification():
    """测试密码验证"""
    print("🔍 测试密码验证...")
    
    try:
        db = get_db_session()
        
        # 获取测试用户
        test_user = db.query(User).filter(User.username == "testuser").first()
        if not test_user:
            print("   ❌ 测试用户不存在")
            return False
        
        print(f"   📋 找到用户: {test_user.username}")
        print(f"   存储的密码哈希: {test_user.hashed_password[:50]}...")
        
        # 测试密码验证
        test_password = "testpass123"
        print(f"   🔑 测试密码: {test_password}")
        
        # 验证密码
        is_valid = verify_password(test_password, test_user.hashed_password)
        print(f"   ✅ 密码验证结果: {is_valid}")
        
        if not is_valid:
            # 重新生成密码哈希并更新
            print("   🔧 重新生成密码哈希...")
            new_hash = get_password_hash(test_password)
            print(f"   新密码哈希: {new_hash[:50]}...")
            
            test_user.hashed_password = new_hash
            db.commit()
            
            # 再次验证
            is_valid_new = verify_password(test_password, new_hash)
            print(f"   ✅ 新密码验证结果: {is_valid_new}")
        
        # 同样检查admin用户
        print("\n   📋 检查admin用户...")
        admin_user = db.query(User).filter(User.username == "admin").first()
        if admin_user:
            print(f"   找到admin用户: {admin_user.username}")
            admin_password = "admin123"
            is_admin_valid = verify_password(admin_password, admin_user.hashed_password)
            print(f"   admin密码验证结果: {is_admin_valid}")
            
            if not is_admin_valid:
                print("   🔧 重新生成admin密码哈希...")
                admin_user.hashed_password = get_password_hash(admin_password)
                db.commit()
                print("   ✅ admin密码已更新")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"   ❌ 密码验证测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_password_verification()
    
    if success:
        print("\n🎉 密码验证测试完成！")
    else:
        print("\n❌ 密码验证测试失败！")
