import{_ as x,u as k,g as z,h as f,i as R,r as u,a as U,c as P,b as i,d as s,w as a,j as h,f as g,k as q,t as C,E as _}from"./index-2c134546.js";const E={class:"register-container"},F={class:"register-card"},S={class:"register-footer"},A={__name:"Register",setup(B){const c=k(),w=z(),m=f(),r=R({username:"",full_name_pinyin:"",full_name:"",password:"",confirmPassword:""}),v={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"用户名长度在 3 到 20 个字符",trigger:"blur"},{pattern:/^[a-zA-Z0-9_]+$/,message:"用户名只能包含字母、数字和下划线",trigger:"blur"}],full_name_pinyin:[{required:!0,message:"请输入姓名全拼",trigger:"blur"},{min:2,max:50,message:"姓名全拼长度在 2 到 50 个字符",trigger:"blur"},{pattern:/^[a-zA-Z]+$/,message:"姓名全拼只能包含字母",trigger:"blur"}],full_name:[{required:!0,message:"请输入真实姓名",trigger:"blur"},{min:2,max:20,message:"真实姓名长度在 2 到 20 个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"密码长度在 6 到 20 个字符",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认密码",trigger:"blur"},{validator:(t,e,o)=>{e!==r.password?o(new Error("两次输入的密码不一致")):o()},trigger:"blur"}]},d=f(!1),p=async()=>{if(m.value)try{await m.value.validate(),d.value=!0;const t=await w.register({username:r.username,full_name_pinyin:r.full_name_pinyin,full_name:r.full_name,password:r.password});t.success?(_.success("注册成功，请登录"),c.push("/login")):_.error(t.message||"注册失败")}catch(t){console.error("注册错误:",t),_.error("注册过程中出现错误")}finally{d.value=!1}};return(t,e)=>{const o=u("el-input"),n=u("el-form-item"),y=u("el-button"),V=u("el-form"),b=u("router-link");return U(),P("div",E,[i("div",F,[e[7]||(e[7]=i("div",{class:"register-header"},[i("h1",null,"🎙️ 语音处理智能平台"),i("p",null,"创建您的账户")],-1)),s(V,{ref_key:"registerFormRef",ref:m,model:r,rules:v,class:"register-form",onSubmit:h(p,["prevent"])},{default:a(()=>[s(n,{prop:"username"},{default:a(()=>[s(o,{modelValue:r.username,"onUpdate:modelValue":e[0]||(e[0]=l=>r.username=l),placeholder:"请输入用户名",size:"large","prefix-icon":"User"},null,8,["modelValue"])]),_:1}),s(n,{prop:"full_name_pinyin"},{default:a(()=>[s(o,{modelValue:r.full_name_pinyin,"onUpdate:modelValue":e[1]||(e[1]=l=>r.full_name_pinyin=l),placeholder:"请输入姓名全拼（如：zhangsan）",size:"large","prefix-icon":"UserFilled"},null,8,["modelValue"])]),_:1}),s(n,{prop:"full_name"},{default:a(()=>[s(o,{modelValue:r.full_name,"onUpdate:modelValue":e[2]||(e[2]=l=>r.full_name=l),placeholder:"请输入真实姓名",size:"large","prefix-icon":"Avatar"},null,8,["modelValue"])]),_:1}),s(n,{prop:"password"},{default:a(()=>[s(o,{modelValue:r.password,"onUpdate:modelValue":e[3]||(e[3]=l=>r.password=l),type:"password",placeholder:"请输入密码",size:"large","prefix-icon":"Lock","show-password":""},null,8,["modelValue"])]),_:1}),s(n,{prop:"confirmPassword"},{default:a(()=>[s(o,{modelValue:r.confirmPassword,"onUpdate:modelValue":e[4]||(e[4]=l=>r.confirmPassword=l),type:"password",placeholder:"请确认密码",size:"large","prefix-icon":"Lock","show-password":"",onKeyup:q(p,["enter"])},null,8,["modelValue"])]),_:1}),s(n,null,{default:a(()=>[s(y,{type:"primary",size:"large",loading:d.value,onClick:p,class:"register-button"},{default:a(()=>[g(C(d.value?"注册中...":"注册"),1)]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"]),i("div",S,[i("p",null,[e[6]||(e[6]=g(" 已有账户？ ")),s(b,{to:"/login",class:"login-link"},{default:a(()=>e[5]||(e[5]=[g("立即登录")])),_:1,__:[5]})])])])])}}},L=x(A,[["__scopeId","data-v-29ed9146"]]);export{L as default};
