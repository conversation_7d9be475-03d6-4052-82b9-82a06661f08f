#!/usr/bin/env python3
"""
清理无效的任务记录
- 清理长期处于PENDING/STARTED状态的无效任务
- 保留有效的任务记录
- 生成清理报告
"""

import sqlite3
import json
from datetime import datetime, timedelta
import shutil
import os

def backup_database():
    """备份数据库"""
    source = 'data/speech_platform.db'
    backup = f'data/speech_platform_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}.db'
    
    try:
        shutil.copy2(source, backup)
        print(f"✅ 数据库已备份到: {backup}")
        return backup
    except Exception as e:
        print(f"❌ 数据库备份失败: {e}")
        return None

def analyze_invalid_tasks():
    """分析无效任务"""
    print("=== 分析无效任务 ===")
    
    try:
        conn = sqlite3.connect('data/speech_platform.db')
        cursor = conn.cursor()
        
        # 查找长期PENDING的任务 (超过1天)
        cutoff_date = datetime.now() - timedelta(days=1)
        cursor.execute("""
            SELECT task_id, status, progress_percentage, progress_detail, created_at, updated_at
            FROM task_records 
            WHERE status = 'PENDING' AND created_at < ?
            ORDER BY created_at
        """, (cutoff_date.strftime('%Y-%m-%d %H:%M:%S'),))
        
        pending_tasks = cursor.fetchall()
        print(f"发现 {len(pending_tasks)} 个长期PENDING任务:")
        for task in pending_tasks:
            print(f"  - {task[0]} | 创建: {task[4]} | 状态: {task[1]}")
        
        # 查找长期STARTED但失败的任务
        cursor.execute("""
            SELECT task_id, status, progress_percentage, progress_detail, created_at, updated_at
            FROM task_records 
            WHERE status = 'STARTED' AND (
                progress_detail LIKE '%失败%' OR 
                progress_detail LIKE '%error%' OR
                progress_detail LIKE '%没有找到%'
            )
            ORDER BY created_at
        """)
        
        failed_started_tasks = cursor.fetchall()
        print(f"发现 {len(failed_started_tasks)} 个失败的STARTED任务:")
        for task in failed_started_tasks:
            print(f"  - {task[0]} | 创建: {task[4]} | 详情: {task[3]}")
        
        conn.close()
        return pending_tasks, failed_started_tasks
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return [], []

def cleanup_invalid_tasks(pending_tasks, failed_started_tasks):
    """清理无效任务"""
    print("\n=== 清理无效任务 ===")
    
    if not pending_tasks and not failed_started_tasks:
        print("✅ 没有需要清理的无效任务")
        return
    
    try:
        conn = sqlite3.connect('data/speech_platform.db')
        cursor = conn.cursor()
        
        cleaned_count = 0
        
        # 清理长期PENDING任务
        for task in pending_tasks:
            task_id = task[0]
            cursor.execute("DELETE FROM task_records WHERE task_id = ?", (task_id,))
            print(f"  ✅ 已删除PENDING任务: {task_id}")
            cleaned_count += 1
        
        # 清理失败的STARTED任务
        for task in failed_started_tasks:
            task_id = task[0]
            cursor.execute("DELETE FROM task_records WHERE task_id = ?", (task_id,))
            print(f"  ✅ 已删除失败STARTED任务: {task_id}")
            cleaned_count += 1
        
        conn.commit()
        conn.close()
        
        print(f"\n✅ 总共清理了 {cleaned_count} 个无效任务")
        
    except Exception as e:
        print(f"❌ 清理失败: {e}")

def verify_cleanup():
    """验证清理结果"""
    print("\n=== 验证清理结果 ===")
    
    try:
        conn = sqlite3.connect('data/speech_platform.db')
        cursor = conn.cursor()
        
        # 检查剩余的任务状态分布
        cursor.execute("""
            SELECT status, COUNT(*) as count
            FROM task_records 
            GROUP BY status
            ORDER BY count DESC
        """)
        
        status_counts = cursor.fetchall()
        print("剩余任务状态分布:")
        for status, count in status_counts:
            print(f"  - {status}: {count} 个")
        
        # 检查最近的任务
        cursor.execute("""
            SELECT task_id, status, progress_percentage, created_at
            FROM task_records 
            ORDER BY created_at DESC
            LIMIT 5
        """)
        
        recent_tasks = cursor.fetchall()
        print("\n最近的任务:")
        for task in recent_tasks:
            print(f"  - {task[0]} | {task[1]} | {task[2]}% | {task[3]}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")

def generate_cleanup_report(backup_file, pending_tasks, failed_started_tasks):
    """生成清理报告"""
    report = {
        "cleanup_time": datetime.now().isoformat(),
        "backup_file": backup_file,
        "cleaned_tasks": {
            "pending_tasks": len(pending_tasks),
            "failed_started_tasks": len(failed_started_tasks),
            "total_cleaned": len(pending_tasks) + len(failed_started_tasks)
        },
        "pending_task_details": [
            {
                "task_id": task[0],
                "status": task[1],
                "created_at": task[4]
            } for task in pending_tasks
        ],
        "failed_started_task_details": [
            {
                "task_id": task[0],
                "status": task[1],
                "error_detail": task[3],
                "created_at": task[4]
            } for task in failed_started_tasks
        ]
    }
    
    report_file = f"logs/cleanup_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    try:
        os.makedirs('logs', exist_ok=True)
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        print(f"\n📋 清理报告已保存到: {report_file}")
    except Exception as e:
        print(f"❌ 报告保存失败: {e}")

def main():
    print("🧹 开始清理无效任务记录")
    print("=" * 50)
    
    # 1. 备份数据库
    backup_file = backup_database()
    if not backup_file:
        print("❌ 数据库备份失败，停止清理操作")
        return
    
    # 2. 分析无效任务
    pending_tasks, failed_started_tasks = analyze_invalid_tasks()
    
    # 3. 确认清理
    total_to_clean = len(pending_tasks) + len(failed_started_tasks)
    if total_to_clean > 0:
        print(f"\n⚠️  将要清理 {total_to_clean} 个无效任务")
        print("继续清理操作...")
        
        # 4. 执行清理
        cleanup_invalid_tasks(pending_tasks, failed_started_tasks)
        
        # 5. 验证结果
        verify_cleanup()
        
        # 6. 生成报告
        generate_cleanup_report(backup_file, pending_tasks, failed_started_tasks)
    else:
        print("✅ 没有发现需要清理的无效任务")
    
    print("\n🎉 清理操作完成")

if __name__ == "__main__":
    main()
