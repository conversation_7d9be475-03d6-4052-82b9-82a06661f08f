#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的语音识别测试脚本
用于验证长音频识别修复
"""

import os
import sys
import time
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(name)s:%(funcName)s:%(lineno)d - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/test_simple_recognition.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def test_simple_recognition():
    """测试简化的语音识别"""
    logger.info("开始简化语音识别测试")
    
    try:
        # 导入语音识别模块
        from backend.utils.audio.speech_recognition_core import SpeechRecognitionManager
        
        # 测试文件路径
        test_file = "data/uploads/1/63068893-0fcc-4d5b-8574-bc6da581d1e8.mp3"
        if not os.path.exists(test_file):
            logger.error(f"测试文件不存在: {test_file}")
            return False
            
        logger.info(f"测试文件: {test_file}")
        
        # 初始化语音识别管理器
        logger.info("初始化语音识别管理器...")
        start_time = time.time()
        
        manager = SpeechRecognitionManager()
        model_path = "models/SenseVoiceSmall"
        manager.initialize(model_path=model_path)
        
        init_time = time.time() - start_time
        logger.info(f"语音识别管理器初始化成功，耗时: {init_time:.2f}秒")
        
        # 开始识别测试
        logger.info("开始语音识别测试...")
        recognition_start = time.time()
        
        # 使用超时机制
        from concurrent.futures import ThreadPoolExecutor, TimeoutError as FutureTimeoutError
        
        def run_recognition():
            return manager.recognize(test_file)
        
        with ThreadPoolExecutor(max_workers=1) as executor:
            future = executor.submit(run_recognition)
            try:
                result = future.result(timeout=120)  # 2分钟超时
                recognition_time = time.time() - recognition_start
                
                logger.info(f"识别完成，耗时: {recognition_time:.2f}秒")
                
                if result.error:
                    logger.error(f"识别失败: {result.error}")
                    return False
                else:
                    logger.info(f"识别成功，文本长度: {len(result.text)}字符")
                    logger.info(f"识别文本预览: {result.text[:100]}...")
                    return True
                    
            except FutureTimeoutError:
                logger.error("识别超时（2分钟）")
                return False
                
    except Exception as e:
        logger.error(f"测试异常: {e}")
        import traceback
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_simple_recognition()
    if success:
        print("✅ 测试成功")
    else:
        print("❌ 测试失败")
