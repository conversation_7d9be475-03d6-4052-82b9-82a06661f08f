#!/usr/bin/env python3
"""检查音频处理任务状态"""

import sqlite3
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_tasks():
    """检查最近的音频处理任务"""
    db_path = project_root / "data" / "speech_platform.db"
    
    if not db_path.exists():
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # 检查最近的任务
        cursor.execute("""
            SELECT task_id, filename, processing_mode, status, progress, 
                   created_at, updated_at, error_message
            FROM audio_processing_tasks 
            ORDER BY created_at DESC 
            LIMIT 10
        """)
        
        results = cursor.fetchall()
        
        if not results:
            print("没有找到任何音频处理任务")
            return
        
        print("最近的音频处理任务:")
        print("-" * 100)
        print(f"{'任务ID':<25} {'文件名':<15} {'模式':<12} {'状态':<8} {'进度':<6} {'创建时间':<20} {'错误信息'}")
        print("-" * 100)
        
        for row in results:
            task_id, filename, mode, status, progress, created_at, updated_at, error = row
            error_msg = error[:30] + "..." if error and len(error) > 30 else (error or "")
            print(f"{task_id:<25} {filename:<15} {mode:<12} {status:<8} {progress:<6} {created_at:<20} {error_msg}")
        
        # 检查特定的会议转录任务
        cursor.execute("""
            SELECT task_id, filename, status, progress, result_data, error_message
            FROM audio_processing_tasks 
            WHERE processing_mode = 'meeting_transcription' 
            AND filename = '对话.mp3'
            ORDER BY created_at DESC 
            LIMIT 1
        """)
        
        meeting_task = cursor.fetchone()
        if meeting_task:
            task_id, filename, status, progress, result_data, error = meeting_task
            print(f"\n最新的会议转录任务 ({filename}):")
            print(f"  任务ID: {task_id}")
            print(f"  状态: {status}")
            print(f"  进度: {progress}%")
            print(f"  错误信息: {error or '无'}")
            print(f"  结果数据长度: {len(result_data) if result_data else 0} 字符")
            
            if result_data:
                print(f"  结果数据预览: {result_data[:200]}...")
        
        conn.close()
        
    except Exception as e:
        print(f"检查任务时出错: {e}")

if __name__ == "__main__":
    check_tasks()
