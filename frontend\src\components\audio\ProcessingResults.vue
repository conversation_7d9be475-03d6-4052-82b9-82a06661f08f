<template>
  <div class="processing-results">
    <div v-if="!hasResults" class="no-results">
      <el-empty description="暂无处理结果" />
    </div>

    <div v-else class="results-container">
      <!-- 结果统计 -->
      <div class="results-summary">
        <el-card shadow="hover">
          <div class="summary-content">
            <div class="summary-item">
              <span class="label">总文件数:</span>
              <span class="value">{{ results.length }}</span>
            </div>
            <div class="summary-item">
              <span class="label">成功处理:</span>
              <span class="value success">{{ successCount }}</span>
            </div>
            <div class="summary-item">
              <span class="label">处理失败:</span>
              <span class="value error">{{ failureCount }}</span>
            </div>
            <div class="summary-item">
              <span class="label">总时长:</span>
              <span class="value">{{ totalDuration }}</span>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 结果列表 -->
      <div class="results-list">
        <el-card v-for="(result, index) in results" :key="index" class="result-item" shadow="hover">
          <div class="result-header">
            <div class="file-info">
              <el-icon class="file-icon"><Document /></el-icon>
              <span class="file-name">{{ result.fileName }}</span>
              <el-tag 
                :type="result.status === 'success' ? 'success' : 'danger'" 
                size="small"
              >
                {{ result.status === 'success' ? '成功' : '失败' }}
              </el-tag>
            </div>
            <div class="result-actions">
              <el-button 
                v-if="result.status === 'success'" 
                type="primary" 
                size="small" 
                @click="viewDetails(result)"
              >
                查看详情
              </el-button>
              <el-button 
                v-if="result.status === 'success'" 
                type="success" 
                size="small" 
                @click="downloadResult(result)"
              >
                下载结果
              </el-button>
              <el-button 
                type="info" 
                size="small" 
                @click="playAudio(result)"
                :disabled="!result.audioUrl"
              >
                播放音频
              </el-button>
            </div>
          </div>

          <div v-if="result.status === 'success'" class="result-content">
            <!-- 语音识别结果 -->
            <div v-if="processingMode === 'speech_recognition'" class="speech-result">
              <h4>识别结果</h4>
              <div class="transcript">
                {{ result.transcript || '无识别内容' }}
              </div>
              <div class="metadata">
                <span>时长: {{ result.duration || 'N/A' }}</span>
                <span>置信度: {{ result.confidence ? (result.confidence * 100).toFixed(1) + '%' : 'N/A' }}</span>
              </div>
            </div>

            <!-- 说话人识别结果 -->
            <div v-else-if="processingMode === 'speaker_recognition'" class="speaker-result">
              <h4>说话人识别</h4>
              <div class="speakers-info">
                <div class="speaker-count">
                  检测到 {{ result.speakerCount || 0 }} 个说话人
                </div>
                <div v-if="result.speakers" class="speakers-list">
                  <div 
                    v-for="speaker in result.speakers" 
                    :key="speaker.id" 
                    class="speaker-item"
                  >
                    <span class="speaker-id">说话人{{ speaker.id }}</span>
                    <span class="speaker-duration">{{ speaker.duration }}</span>
                    <el-progress 
                      :percentage="Math.round(speaker.confidence * 100)" 
                      :color="getConfidenceColor(speaker.confidence)"
                      :show-text="false"
                      size="small"
                    />
                  </div>
                </div>
              </div>
            </div>

            <!-- 会议转录结果 -->
            <div v-else-if="processingMode === 'meeting_transcription'" class="meeting-result">
              <h4>会议转录</h4>
              <div class="meeting-info">
                <div class="meeting-stats">
                  <span>说话人数: {{ result.speakerCount || 0 }}</span>
                  <span>对话轮次: {{ result.turnCount || 0 }}</span>
                </div>
                <div v-if="result.dialogue" class="dialogue-preview">
                  <div 
                    v-for="(turn, idx) in result.dialogue.slice(0, 3)" 
                    :key="idx" 
                    class="dialogue-turn"
                  >
                    <span class="speaker">{{ turn.speaker }}:</span>
                    <span class="text">{{ turn.text }}</span>
                  </div>
                  <div v-if="result.dialogue.length > 3" class="more-indicator">
                    还有 {{ result.dialogue.length - 3 }} 条对话...
                  </div>
                </div>
              </div>
            </div>

            <!-- 通用音频分析结果 -->
            <div v-else class="general-result">
              <h4>处理结果</h4>
              <div class="result-data">
                <pre>{{ JSON.stringify(result.data, null, 2) }}</pre>
              </div>
            </div>
          </div>

          <div v-else class="error-content">
            <el-alert 
              :title="result.error || '处理失败'" 
              type="error" 
              :closable="false"
            />
          </div>
        </el-card>
      </div>
    </div>

    <!-- 详情对话框 -->
    <el-dialog 
      v-model="detailDialogVisible" 
      :title="selectedResult?.fileName" 
      width="80%"
      top="5vh"
    >
      <div v-if="selectedResult" class="detail-content">
        <!-- 详细结果展示 -->
        <component 
          :is="getDetailComponent()" 
          :result="selectedResult" 
          :processing-mode="processingMode"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Document } from '@element-plus/icons-vue'

export default {
  name: 'ProcessingResults',
  components: {
    Document
  },
  props: {
    results: {
      type: Array,
      default: () => []
    },
    processingMode: {
      type: String,
      default: 'speech_recognition'
    }
  },
  emits: ['result-selected'],
  setup(props, { emit }) {
    const detailDialogVisible = ref(false)
    const selectedResult = ref(null)

    // 计算属性
    const hasResults = computed(() => props.results.length > 0)
    
    const successCount = computed(() => 
      props.results.filter(r => r.status === 'success').length
    )
    
    const failureCount = computed(() => 
      props.results.filter(r => r.status === 'error').length
    )
    
    const totalDuration = computed(() => {
      const total = props.results.reduce((sum, r) => {
        if (r.status === 'success' && r.duration) {
          return sum + parseFloat(r.duration)
        }
        return sum
      }, 0)
      return total > 0 ? `${total.toFixed(1)}s` : 'N/A'
    })

    // 方法
    const viewDetails = (result) => {
      selectedResult.value = result
      detailDialogVisible.value = true
      emit('result-selected', result)
    }

    const downloadResult = (result) => {
      if (result.downloadUrl) {
        const link = document.createElement('a')
        link.href = result.downloadUrl
        link.download = `${result.fileName}_result.json`
        link.click()
      } else {
        ElMessage.warning('暂无下载链接')
      }
    }

    const playAudio = (result) => {
      if (result.audioUrl) {
        const audio = new Audio(result.audioUrl)
        audio.play().catch(() => {
          ElMessage.error('音频播放失败')
        })
      } else {
        ElMessage.warning('暂无音频文件')
      }
    }

    const getConfidenceColor = (confidence) => {
      if (confidence >= 0.8) return '#67c23a'
      if (confidence >= 0.6) return '#e6a23c'
      return '#f56c6c'
    }

    const getDetailComponent = () => {
      // 根据处理模式返回不同的详情组件
      // 这里可以扩展为动态组件
      return 'div'
    }

    return {
      detailDialogVisible,
      selectedResult,
      hasResults,
      successCount,
      failureCount,
      totalDuration,
      viewDetails,
      downloadResult,
      playAudio,
      getConfidenceColor,
      getDetailComponent
    }
  }
}
</script>

<style scoped>
.processing-results {
  width: 100%;
}

.no-results {
  padding: 40px 0;
}

.results-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.results-summary .summary-content {
  display: flex;
  justify-content: space-around;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.summary-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.summary-item .label {
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.summary-item .value {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.summary-item .value.success {
  color: var(--el-color-success);
}

.summary-item .value.error {
  color: var(--el-color-danger);
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.result-item {
  width: 100%;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.file-icon {
  color: var(--el-color-primary);
}

.file-name {
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.result-actions {
  display: flex;
  gap: 8px;
}

.result-content {
  border-top: 1px solid var(--el-border-color-light);
  padding-top: 16px;
}

.result-content h4 {
  margin: 0 0 12px 0;
  color: var(--el-text-color-primary);
}

.transcript {
  background: var(--el-fill-color-light);
  padding: 12px;
  border-radius: 4px;
  margin-bottom: 8px;
  line-height: 1.6;
}

.metadata {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.speakers-info .speaker-count {
  font-weight: 500;
  margin-bottom: 12px;
}

.speakers-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.speaker-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px;
  background: var(--el-fill-color-lighter);
  border-radius: 4px;
}

.speaker-id {
  min-width: 80px;
  font-weight: 500;
}

.speaker-duration {
  min-width: 60px;
  font-size: 12px;
  color: var(--el-text-color-regular);
}

.meeting-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 12px;
  font-size: 14px;
}

.dialogue-preview {
  max-height: 200px;
  overflow-y: auto;
}

.dialogue-turn {
  padding: 8px;
  border-bottom: 1px solid var(--el-border-color-lighter);
}

.dialogue-turn .speaker {
  font-weight: 500;
  color: var(--el-color-primary);
  margin-right: 8px;
}

.more-indicator {
  text-align: center;
  padding: 8px;
  color: var(--el-text-color-regular);
  font-style: italic;
}

.general-result .result-data {
  background: var(--el-fill-color-light);
  padding: 12px;
  border-radius: 4px;
  max-height: 300px;
  overflow-y: auto;
}

.error-content {
  margin-top: 8px;
}

.detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

@media (max-width: 768px) {
  .result-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .result-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .summary-content {
    justify-content: center;
  }
}
</style>
