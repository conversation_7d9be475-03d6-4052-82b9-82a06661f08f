<template>
  <div class="task-scheduler">
    <!-- 调度器头部 -->
    <div class="scheduler-header">
      <div class="header-info">
        <h3>🧠 智能任务调度器</h3>
        <div class="scheduler-status">
          <el-tag :type="schedulerStatusType" size="small">
            {{ schedulerStatusText }}
          </el-tag>
          <span class="load-indicator">负载: {{ systemLoad }}%</span>
        </div>
      </div>
      
      <div class="header-actions">
        <el-button 
          @click="toggleScheduler" 
          :type="isEnabled ? 'danger' : 'primary'"
          size="small"
        >
          {{ isEnabled ? '停用调度器' : '启用调度器' }}
        </el-button>
        
        <el-dropdown @command="handleSchedulerCommand">
          <el-button :icon="Setting" size="small" circle />
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="optimize">优化策略</el-dropdown-item>
              <el-dropdown-item command="reset">重置调度器</el-dropdown-item>
              <el-dropdown-item command="export">导出日志</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 调度策略配置 -->
    <div class="scheduler-config" v-if="showConfig">
      <div class="config-header">
        <h4>⚙️ 调度策略配置</h4>
        <el-button text @click="showConfig = false" :icon="Close" size="small" />
      </div>
      
      <div class="config-content">
        <el-row :gutter="16">
          <el-col :span="12">
            <div class="config-section">
              <label>调度算法</label>
              <el-select v-model="config.algorithm" @change="updateSchedulingAlgorithm">
                <el-option label="先进先出 (FIFO)" value="fifo" />
                <el-option label="最短作业优先 (SJF)" value="sjf" />
                <el-option label="优先级调度" value="priority" />
                <el-option label="轮转调度" value="round_robin" />
                <el-option label="智能调度" value="intelligent" />
              </el-select>
            </div>
          </el-col>
          
          <el-col :span="12">
            <div class="config-section">
              <label>最大并发数</label>
              <el-input-number
                v-model="config.maxConcurrency"
                :min="1"
                :max="20"
                @change="updateMaxConcurrency"
              />
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="16">
          <el-col :span="12">
            <div class="config-section">
              <label>负载阈值 (%)</label>
              <el-input-number
                v-model="config.loadThreshold"
                :min="50"
                :max="95"
                @change="updateLoadThreshold"
              />
            </div>
          </el-col>
          
          <el-col :span="12">
            <div class="config-section">
              <label>时间片 (秒)</label>
              <el-input-number
                v-model="config.timeSlice"
                :min="5"
                :max="300"
                :disabled="config.algorithm !== 'round_robin'"
              />
            </div>
          </el-col>
        </el-row>
        
        <div class="config-switches">
          <el-switch
            v-model="config.enableLoadBalancing"
            active-text="启用负载均衡"
            @change="updateLoadBalancing"
          />
          
          <el-switch
            v-model="config.enablePredictiveScheduling"
            active-text="启用预测调度"
            @change="updatePredictiveScheduling"
          />
          
          <el-switch
            v-model="config.enableResourceOptimization"
            active-text="启用资源优化"
            @change="updateResourceOptimization"
          />
        </div>
      </div>
    </div>

    <!-- 任务队列可视化 -->
    <div class="queue-visualization">
      <div class="queue-header">
        <h4>📊 任务队列状态</h4>
        <div class="queue-stats">
          <span>等待: {{ waitingTasks.length }}</span>
          <span>运行: {{ runningTasks.length }}</span>
          <span>完成: {{ completedTasks.length }}</span>
        </div>
      </div>
      
      <div class="queue-lanes">
        <!-- 高优先级队列 -->
        <div class="queue-lane high-priority">
          <div class="lane-header">
            <span class="lane-title">🔥 高优先级</span>
            <span class="lane-count">{{ highPriorityTasks.length }}</span>
          </div>
          <div class="lane-tasks">
            <div
              v-for="task in highPriorityTasks.slice(0, 5)"
              :key="task.id"
              class="task-item high"
              @click="selectTask(task)"
            >
              <div class="task-info">
                <span class="task-name">{{ task.name }}</span>
                <span class="task-eta">ETA: {{ formatETA(task.estimatedTime) }}</span>
              </div>
              <div class="task-status">
                <el-icon v-if="task.status === 'running'" class="is-loading">
                  <Loading />
                </el-icon>
                <el-icon v-else><Clock /></el-icon>
              </div>
            </div>
            <div v-if="highPriorityTasks.length > 5" class="more-tasks">
              +{{ highPriorityTasks.length - 5 }} 更多
            </div>
          </div>
        </div>
        
        <!-- 普通优先级队列 -->
        <div class="queue-lane normal-priority">
          <div class="lane-header">
            <span class="lane-title">📋 普通优先级</span>
            <span class="lane-count">{{ normalPriorityTasks.length }}</span>
          </div>
          <div class="lane-tasks">
            <div
              v-for="task in normalPriorityTasks.slice(0, 5)"
              :key="task.id"
              class="task-item normal"
              @click="selectTask(task)"
            >
              <div class="task-info">
                <span class="task-name">{{ task.name }}</span>
                <span class="task-eta">ETA: {{ formatETA(task.estimatedTime) }}</span>
              </div>
              <div class="task-status">
                <el-icon v-if="task.status === 'running'" class="is-loading">
                  <Loading />
                </el-icon>
                <el-icon v-else><Clock /></el-icon>
              </div>
            </div>
            <div v-if="normalPriorityTasks.length > 5" class="more-tasks">
              +{{ normalPriorityTasks.length - 5 }} 更多
            </div>
          </div>
        </div>
        
        <!-- 低优先级队列 -->
        <div class="queue-lane low-priority">
          <div class="lane-header">
            <span class="lane-title">📝 低优先级</span>
            <span class="lane-count">{{ lowPriorityTasks.length }}</span>
          </div>
          <div class="lane-tasks">
            <div
              v-for="task in lowPriorityTasks.slice(0, 5)"
              :key="task.id"
              class="task-item low"
              @click="selectTask(task)"
            >
              <div class="task-info">
                <span class="task-name">{{ task.name }}</span>
                <span class="task-eta">ETA: {{ formatETA(task.estimatedTime) }}</span>
              </div>
              <div class="task-status">
                <el-icon v-if="task.status === 'running'" class="is-loading">
                  <Loading />
                </el-icon>
                <el-icon v-else><Clock /></el-icon>
              </div>
            </div>
            <div v-if="lowPriorityTasks.length > 5" class="more-tasks">
              +{{ lowPriorityTasks.length - 5 }} 更多
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 性能监控 -->
    <div class="performance-monitor">
      <div class="monitor-header">
        <h4>📈 性能监控</h4>
        <el-button @click="showConfig = !showConfig" text size="small">
          {{ showConfig ? '隐藏' : '显示' }}配置
        </el-button>
      </div>
      
      <div class="monitor-content">
        <el-row :gutter="16">
          <el-col :span="6">
            <div class="metric-card">
              <div class="metric-value">{{ throughput.toFixed(1) }}</div>
              <div class="metric-label">任务/分钟</div>
              <div class="metric-trend" :class="throughputTrend">
                <el-icon><TrendCharts /></el-icon>
              </div>
            </div>
          </el-col>
          
          <el-col :span="6">
            <div class="metric-card">
              <div class="metric-value">{{ averageWaitTime.toFixed(1) }}s</div>
              <div class="metric-label">平均等待时间</div>
              <div class="metric-trend" :class="waitTimeTrend">
                <el-icon><TrendCharts /></el-icon>
              </div>
            </div>
          </el-col>
          
          <el-col :span="6">
            <div class="metric-card">
              <div class="metric-value">{{ resourceUtilization.toFixed(1) }}%</div>
              <div class="metric-label">资源利用率</div>
              <div class="metric-trend" :class="utilizationTrend">
                <el-icon><TrendCharts /></el-icon>
              </div>
            </div>
          </el-col>
          
          <el-col :span="6">
            <div class="metric-card">
              <div class="metric-value">{{ errorRate.toFixed(1) }}%</div>
              <div class="metric-label">错误率</div>
              <div class="metric-trend" :class="errorRateTrend">
                <el-icon><TrendCharts /></el-icon>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 调度日志 -->
    <div class="scheduler-logs" v-if="showLogs">
      <div class="logs-header">
        <h4>📋 调度日志</h4>
        <div class="logs-actions">
          <el-button @click="clearLogs" size="small" type="danger" plain>
            清空日志
          </el-button>
          <el-button @click="showLogs = false" text size="small">
            隐藏
          </el-button>
        </div>
      </div>
      
      <div class="logs-content">
        <div
          v-for="log in recentLogs"
          :key="log.id"
          class="log-item"
          :class="`log-${log.level}`"
        >
          <span class="log-time">{{ formatLogTime(log.timestamp) }}</span>
          <span class="log-level">{{ log.level.toUpperCase() }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Setting,
  Close,
  Loading,
  Clock,
  TrendCharts
} from '@element-plus/icons-vue'

// Props
const props = defineProps({
  tasks: {
    type: Array,
    default: () => []
  },
  maxConcurrency: {
    type: Number,
    default: 3
  },
  enableIntelligentScheduling: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits([
  'task-scheduled',
  'task-started',
  'task-completed',
  'scheduler-updated',
  'performance-alert'
])

// 响应式数据
const isEnabled = ref(true)
const showConfig = ref(false)
const showLogs = ref(false)
const systemLoad = ref(0)
const selectedTask = ref(null)

// 调度器配置
const config = ref({
  algorithm: 'intelligent',
  maxConcurrency: 3,
  loadThreshold: 80,
  timeSlice: 30,
  enableLoadBalancing: true,
  enablePredictiveScheduling: true,
  enableResourceOptimization: true
})

// 性能指标
const performanceMetrics = ref({
  throughput: 0,
  averageWaitTime: 0,
  resourceUtilization: 0,
  errorRate: 0,
  completedTasks: 0,
  failedTasks: 0,
  totalProcessingTime: 0
})

// 调度日志
const schedulerLogs = ref([])
const maxLogEntries = ref(100)

// 任务历史数据（用于性能分析）
const taskHistory = ref([])
const performanceHistory = ref([])

// 计算属性
const schedulerStatusType = computed(() => {
  if (!isEnabled.value) return 'info'
  if (systemLoad.value > config.value.loadThreshold) return 'danger'
  if (systemLoad.value > 60) return 'warning'
  return 'success'
})

const schedulerStatusText = computed(() => {
  if (!isEnabled.value) return '已停用'
  if (systemLoad.value > config.value.loadThreshold) return '高负载'
  if (systemLoad.value > 60) return '中等负载'
  return '正常运行'
})

const waitingTasks = computed(() =>
  props.tasks.filter(t => t.status === 'waiting' || t.status === 'queued')
)

const runningTasks = computed(() =>
  props.tasks.filter(t => t.status === 'running' || t.status === 'processing')
)

const completedTasks = computed(() =>
  props.tasks.filter(t => t.status === 'completed')
)

const highPriorityTasks = computed(() =>
  waitingTasks.value.filter(t => (t.priority || 0) >= 7)
)

const normalPriorityTasks = computed(() =>
  waitingTasks.value.filter(t => (t.priority || 0) >= 3 && (t.priority || 0) < 7)
)

const lowPriorityTasks = computed(() =>
  waitingTasks.value.filter(t => (t.priority || 0) < 3)
)

const throughput = computed(() => performanceMetrics.value.throughput)
const averageWaitTime = computed(() => performanceMetrics.value.averageWaitTime)
const resourceUtilization = computed(() => performanceMetrics.value.resourceUtilization)
const errorRate = computed(() => performanceMetrics.value.errorRate)

// 趋势计算
const throughputTrend = computed(() => {
  const recent = performanceHistory.value.slice(-5)
  if (recent.length < 2) return 'stable'
  const current = recent[recent.length - 1]?.throughput || 0
  const previous = recent[recent.length - 2]?.throughput || 0
  return current > previous ? 'up' : current < previous ? 'down' : 'stable'
})

const waitTimeTrend = computed(() => {
  const recent = performanceHistory.value.slice(-5)
  if (recent.length < 2) return 'stable'
  const current = recent[recent.length - 1]?.averageWaitTime || 0
  const previous = recent[recent.length - 2]?.averageWaitTime || 0
  return current > previous ? 'up' : current < previous ? 'down' : 'stable'
})

const utilizationTrend = computed(() => {
  const recent = performanceHistory.value.slice(-5)
  if (recent.length < 2) return 'stable'
  const current = recent[recent.length - 1]?.resourceUtilization || 0
  const previous = recent[recent.length - 2]?.resourceUtilization || 0
  return current > previous ? 'up' : current < previous ? 'down' : 'stable'
})

const errorRateTrend = computed(() => {
  const recent = performanceHistory.value.slice(-5)
  if (recent.length < 2) return 'stable'
  const current = recent[recent.length - 1]?.errorRate || 0
  const previous = recent[recent.length - 2]?.errorRate || 0
  return current > previous ? 'up' : current < previous ? 'down' : 'stable'
})

const recentLogs = computed(() =>
  schedulerLogs.value.slice(-20).reverse()
)

// 调度器方法
const toggleScheduler = () => {
  isEnabled.value = !isEnabled.value

  if (isEnabled.value) {
    startScheduler()
    addLog('info', '调度器已启用')
  } else {
    stopScheduler()
    addLog('info', '调度器已停用')
  }

  emit('scheduler-updated', { enabled: isEnabled.value })
}

const startScheduler = () => {
  // 启动调度器逻辑
  scheduleNextTasks()
  addLog('info', `调度器启动，算法: ${config.value.algorithm}`)
}

const stopScheduler = () => {
  // 停止调度器逻辑
  addLog('info', '调度器已停止')
}

const scheduleNextTasks = () => {
  if (!isEnabled.value || runningTasks.value.length >= config.value.maxConcurrency) {
    return
  }

  const availableSlots = config.value.maxConcurrency - runningTasks.value.length
  const tasksToSchedule = selectTasksToSchedule(availableSlots)

  tasksToSchedule.forEach(task => {
    scheduleTask(task)
  })
}

const selectTasksToSchedule = (maxTasks) => {
  const waiting = [...waitingTasks.value]

  switch (config.value.algorithm) {
    case 'fifo':
      return waiting
        .sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt))
        .slice(0, maxTasks)

    case 'sjf':
      return waiting
        .sort((a, b) => (a.estimatedTime || 0) - (b.estimatedTime || 0))
        .slice(0, maxTasks)

    case 'priority':
      return waiting
        .sort((a, b) => (b.priority || 0) - (a.priority || 0))
        .slice(0, maxTasks)

    case 'round_robin':
      // 轮转调度的简化实现
      return waiting.slice(0, maxTasks)

    case 'intelligent':
      return intelligentScheduling(waiting, maxTasks)

    default:
      return waiting.slice(0, maxTasks)
  }
}

const intelligentScheduling = (tasks, maxTasks) => {
  // 智能调度算法
  const scoredTasks = tasks.map(task => ({
    ...task,
    score: calculateTaskScore(task)
  }))

  return scoredTasks
    .sort((a, b) => b.score - a.score)
    .slice(0, maxTasks)
}

const calculateTaskScore = (task) => {
  let score = 0

  // 优先级权重 (40%)
  score += (task.priority || 0) * 4

  // 等待时间权重 (30%)
  const waitTime = Date.now() - new Date(task.createdAt).getTime()
  score += Math.min(waitTime / (1000 * 60), 30) * 3 // 最多30分钟

  // 预估处理时间权重 (20%) - 短任务优先
  const estimatedTime = task.estimatedTime || 60
  score += Math.max(0, 20 - estimatedTime / 60) * 2

  // 资源需求权重 (10%) - 低资源需求优先
  const resourceNeed = task.resourceRequirement || 1
  score += Math.max(0, 10 - resourceNeed) * 1

  return score
}

const scheduleTask = (task) => {
  task.status = 'scheduled'
  task.scheduledAt = new Date()

  addLog('info', `任务已调度: ${task.name}`)
  emit('task-scheduled', task)

  // 模拟任务开始
  setTimeout(() => {
    startTask(task)
  }, 100)
}

const startTask = (task) => {
  task.status = 'running'
  task.startedAt = new Date()

  addLog('info', `任务开始执行: ${task.name}`)
  emit('task-started', task)

  // 更新性能指标
  updatePerformanceMetrics()
}

// 配置更新方法
const updateSchedulingAlgorithm = (algorithm) => {
  addLog('info', `调度算法已更改为: ${algorithm}`)
  emit('scheduler-updated', { algorithm })
}

const updateMaxConcurrency = (concurrency) => {
  addLog('info', `最大并发数已更改为: ${concurrency}`)
  emit('scheduler-updated', { maxConcurrency: concurrency })
}

const updateLoadThreshold = (threshold) => {
  addLog('info', `负载阈值已更改为: ${threshold}%`)
  emit('scheduler-updated', { loadThreshold: threshold })
}

const updateLoadBalancing = (enabled) => {
  addLog('info', `负载均衡已${enabled ? '启用' : '禁用'}`)
  emit('scheduler-updated', { loadBalancing: enabled })
}

const updatePredictiveScheduling = (enabled) => {
  addLog('info', `预测调度已${enabled ? '启用' : '禁用'}`)
  emit('scheduler-updated', { predictiveScheduling: enabled })
}

const updateResourceOptimization = (enabled) => {
  addLog('info', `资源优化已${enabled ? '启用' : '禁用'}`)
  emit('scheduler-updated', { resourceOptimization: enabled })
}

// 性能监控方法
const updatePerformanceMetrics = () => {
  const now = Date.now()
  const oneMinuteAgo = now - 60 * 1000

  // 计算吞吐量（任务/分钟）
  const recentCompleted = taskHistory.value.filter(t =>
    t.completedAt && new Date(t.completedAt).getTime() > oneMinuteAgo
  )
  performanceMetrics.value.throughput = recentCompleted.length

  // 计算平均等待时间
  const waitingTasksWithTime = waitingTasks.value.filter(t => t.createdAt)
  if (waitingTasksWithTime.length > 0) {
    const totalWaitTime = waitingTasksWithTime.reduce((sum, task) => {
      return sum + (now - new Date(task.createdAt).getTime())
    }, 0)
    performanceMetrics.value.averageWaitTime = totalWaitTime / waitingTasksWithTime.length / 1000
  }

  // 计算资源利用率
  performanceMetrics.value.resourceUtilization =
    (runningTasks.value.length / config.value.maxConcurrency) * 100

  // 计算错误率
  const recentTasks = taskHistory.value.filter(t =>
    t.completedAt && new Date(t.completedAt).getTime() > oneMinuteAgo
  )
  if (recentTasks.length > 0) {
    const failedCount = recentTasks.filter(t => t.status === 'failed').length
    performanceMetrics.value.errorRate = (failedCount / recentTasks.length) * 100
  }

  // 保存性能历史
  performanceHistory.value.push({
    timestamp: now,
    ...performanceMetrics.value
  })

  // 限制历史记录数量
  if (performanceHistory.value.length > 100) {
    performanceHistory.value = performanceHistory.value.slice(-100)
  }

  // 检查性能警报
  checkPerformanceAlerts()
}

const checkPerformanceAlerts = () => {
  const metrics = performanceMetrics.value

  if (metrics.resourceUtilization > config.value.loadThreshold) {
    emit('performance-alert', {
      type: 'high_load',
      message: `资源利用率过高: ${metrics.resourceUtilization.toFixed(1)}%`,
      severity: 'warning'
    })
  }

  if (metrics.errorRate > 10) {
    emit('performance-alert', {
      type: 'high_error_rate',
      message: `错误率过高: ${metrics.errorRate.toFixed(1)}%`,
      severity: 'error'
    })
  }

  if (metrics.averageWaitTime > 300) { // 5分钟
    emit('performance-alert', {
      type: 'long_wait_time',
      message: `平均等待时间过长: ${(metrics.averageWaitTime / 60).toFixed(1)}分钟`,
      severity: 'warning'
    })
  }
}

// 任务操作方法
const selectTask = (task) => {
  selectedTask.value = task
}

// 调度器命令处理
const handleSchedulerCommand = (command) => {
  switch (command) {
    case 'optimize':
      optimizeScheduler()
      break
    case 'reset':
      resetScheduler()
      break
    case 'export':
      exportLogs()
      break
  }
}

const optimizeScheduler = () => {
  // 基于历史数据优化调度器配置
  const avgWaitTime = performanceMetrics.value.averageWaitTime
  const utilization = performanceMetrics.value.resourceUtilization

  if (avgWaitTime > 120 && utilization < 70) {
    // 等待时间长但利用率低，增加并发数
    config.value.maxConcurrency = Math.min(config.value.maxConcurrency + 1, 10)
    addLog('info', '自动优化: 增加并发数以减少等待时间')
  } else if (utilization > 90) {
    // 利用率过高，可能需要减少并发数
    config.value.maxConcurrency = Math.max(config.value.maxConcurrency - 1, 1)
    addLog('info', '自动优化: 减少并发数以降低系统负载')
  }

  ElMessage.success('调度器已自动优化')
}

const resetScheduler = () => {
  config.value = {
    algorithm: 'intelligent',
    maxConcurrency: 3,
    loadThreshold: 80,
    timeSlice: 30,
    enableLoadBalancing: true,
    enablePredictiveScheduling: true,
    enableResourceOptimization: true
  }

  addLog('info', '调度器配置已重置')
  ElMessage.success('调度器配置已重置')
}

const exportLogs = () => {
  const logData = schedulerLogs.value.map(log => ({
    timestamp: new Date(log.timestamp).toISOString(),
    level: log.level,
    message: log.message
  }))

  const blob = new Blob([JSON.stringify(logData, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `scheduler-logs-${new Date().toISOString().split('T')[0]}.json`
  a.click()
  URL.revokeObjectURL(url)

  ElMessage.success('调度日志已导出')
}

// 日志管理
const addLog = (level, message) => {
  const log = {
    id: Date.now() + Math.random(),
    timestamp: Date.now(),
    level,
    message
  }

  schedulerLogs.value.push(log)

  // 限制日志数量
  if (schedulerLogs.value.length > maxLogEntries.value) {
    schedulerLogs.value = schedulerLogs.value.slice(-maxLogEntries.value)
  }
}

const clearLogs = () => {
  schedulerLogs.value = []
  ElMessage.success('调度日志已清空')
}

// 工具方法
const formatETA = (estimatedTime) => {
  if (!estimatedTime) return '未知'

  const minutes = Math.floor(estimatedTime / 60)
  const seconds = estimatedTime % 60

  if (minutes > 0) {
    return `${minutes}分${seconds}秒`
  }
  return `${seconds}秒`
}

const formatLogTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString()
}

// 生命周期
onMounted(() => {
  // 启动性能监控
  const metricsInterval = setInterval(() => {
    updatePerformanceMetrics()
  }, 5000) // 每5秒更新一次

  // 模拟系统负载
  const loadInterval = setInterval(() => {
    systemLoad.value = Math.min(100, Math.max(0,
      systemLoad.value + (Math.random() - 0.5) * 10
    ))
  }, 2000)

  onUnmounted(() => {
    clearInterval(metricsInterval)
    clearInterval(loadInterval)
  })
})

// 监听任务变化
watch(() => props.tasks, (newTasks, oldTasks) => {
  // 检测新完成的任务
  const newCompleted = newTasks.filter(t =>
    t.status === 'completed' &&
    !oldTasks?.find(ot => ot.id === t.id && ot.status === 'completed')
  )

  newCompleted.forEach(task => {
    taskHistory.value.push(task)
    addLog('info', `任务完成: ${task.name}`)
    emit('task-completed', task)
  })

  // 调度下一批任务
  if (isEnabled.value) {
    scheduleNextTasks()
  }
}, { deep: true })

// 暴露方法
defineExpose({
  toggleScheduler,
  optimizeScheduler,
  resetScheduler,
  addLog,
  clearLogs,
  isEnabled: computed(() => isEnabled.value),
  performanceMetrics: computed(() => performanceMetrics.value)
})
</script>

<style scoped>
.task-scheduler {
  background: var(--card-bg);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

/* 调度器头部 */
.scheduler-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  background: var(--surface-bg);
  border-bottom: 1px solid var(--border-color);
}

.header-info h3 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
  font-size: 1.1rem;
}

.scheduler-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.load-indicator {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

/* 配置面板 */
.scheduler-config {
  background: var(--input-bg);
  border-bottom: 1px solid var(--border-color);
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.config-header h4 {
  margin: 0;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.config-content {
  padding: var(--spacing-lg);
}

.config-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  margin-bottom: var(--spacing-md);
}

.config-section label {
  font-size: 0.85rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.config-switches {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

/* 队列可视化 */
.queue-visualization {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.queue-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.queue-header h4 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1rem;
}

.queue-stats {
  display: flex;
  gap: var(--spacing-md);
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.queue-lanes {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--spacing-lg);
}

.queue-lane {
  background: var(--surface-bg);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

.lane-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  background: var(--input-bg);
  border-bottom: 1px solid var(--border-color);
}

.lane-title {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.lane-count {
  background: var(--accent-primary);
  color: white;
  padding: 2px 6px;
  border-radius: var(--radius-xs);
  font-size: 0.8rem;
  font-weight: 500;
}

.lane-tasks {
  padding: var(--spacing-sm);
  min-height: 120px;
}

.task-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm);
  margin-bottom: var(--spacing-xs);
  background: var(--card-bg);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
  cursor: pointer;
  transition: all 0.2s ease;
}

.task-item:hover {
  border-color: var(--accent-primary);
  box-shadow: 0 2px 4px rgba(88, 166, 255, 0.2);
}

.task-item.high {
  border-left: 3px solid var(--danger-color);
}

.task-item.normal {
  border-left: 3px solid var(--warning-color);
}

.task-item.low {
  border-left: 3px solid var(--info-color);
}

.task-info {
  flex: 1;
  min-width: 0;
}

.task-name {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.85rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
  margin-bottom: var(--spacing-xs);
}

.task-eta {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.task-status {
  flex-shrink: 0;
  font-size: 1rem;
}

.more-tasks {
  text-align: center;
  padding: var(--spacing-sm);
  color: var(--text-muted);
  font-size: 0.8rem;
  border: 1px dashed var(--border-color);
  border-radius: var(--radius-sm);
}

/* 性能监控 */
.performance-monitor {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-lg);
}

.monitor-header h4 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1rem;
}

.metric-card {
  position: relative;
  background: var(--surface-bg);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  padding: var(--spacing-lg);
  text-align: center;
  transition: all 0.2s ease;
}

.metric-card:hover {
  border-color: var(--accent-primary);
  box-shadow: 0 2px 8px rgba(88, 166, 255, 0.2);
}

.metric-value {
  font-size: 1.8rem;
  font-weight: 600;
  color: var(--accent-primary);
  margin-bottom: var(--spacing-xs);
}

.metric-label {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.metric-trend {
  position: absolute;
  top: var(--spacing-sm);
  right: var(--spacing-sm);
  font-size: 1rem;
}

.metric-trend.up {
  color: var(--success-color);
  transform: rotate(-45deg);
}

.metric-trend.down {
  color: var(--danger-color);
  transform: rotate(45deg);
}

.metric-trend.stable {
  color: var(--text-muted);
}

/* 调度日志 */
.scheduler-logs {
  background: var(--input-bg);
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.logs-header h4 {
  margin: 0;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.logs-actions {
  display: flex;
  gap: var(--spacing-sm);
}

.logs-content {
  max-height: 200px;
  overflow-y: auto;
  padding: var(--spacing-sm);
}

.log-item {
  display: flex;
  gap: var(--spacing-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  margin-bottom: var(--spacing-xs);
  border-radius: var(--radius-sm);
  font-size: 0.8rem;
  font-family: var(--font-mono);
}

.log-item.log-info {
  background: rgba(88, 166, 255, 0.1);
  color: var(--info-color);
}

.log-item.log-warning {
  background: rgba(255, 193, 7, 0.1);
  color: var(--warning-color);
}

.log-item.log-error {
  background: rgba(248, 81, 73, 0.1);
  color: var(--danger-color);
}

.log-time {
  color: var(--text-muted);
  min-width: 80px;
}

.log-level {
  font-weight: 500;
  min-width: 60px;
}

.log-message {
  flex: 1;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .queue-lanes {
    grid-template-columns: 1fr;
  }

  .metric-card {
    margin-bottom: var(--spacing-md);
  }
}

@media (max-width: 768px) {
  .scheduler-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }

  .queue-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: flex-start;
  }

  .config-content .el-row {
    flex-direction: column;
  }

  .config-content .el-col {
    width: 100%;
    margin-bottom: var(--spacing-md);
  }
}

/* 动画效果 */
.task-item {
  animation: slideInUp 0.3s ease;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.is-loading {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 滚动条样式 */
.logs-content::-webkit-scrollbar {
  width: 6px;
}

.logs-content::-webkit-scrollbar-track {
  background: var(--input-bg);
}

.logs-content::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.logs-content::-webkit-scrollbar-thumb:hover {
  background: var(--accent-primary);
}
</style>
