#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试音频API接口
"""

import requests
import json

BASE_URL = "http://localhost:8002/api/v1/audio"

def test_system_status():
    """测试系统状态接口"""
    try:
        response = requests.get(f"{BASE_URL}/system/status")
        print(f"系统状态接口: {response.status_code}")
        if response.status_code == 200:
            print(f"响应: {response.json()}")
        else:
            print(f"错误: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")

def test_stats_files():
    """测试文件统计接口"""
    try:
        # 需要认证token，先测试无认证的情况
        response = requests.get(f"{BASE_URL}/stats/files")
        print(f"文件统计接口: {response.status_code}")
        print(f"响应: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")

def test_active_tasks():
    """测试活跃任务接口"""
    try:
        response = requests.get(f"{BASE_URL}/tasks/active")
        print(f"活跃任务接口: {response.status_code}")
        print(f"响应: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")

def test_online_users():
    """测试在线用户接口"""
    try:
        response = requests.get(f"{BASE_URL}/users/online")
        print(f"在线用户接口: {response.status_code}")
        print(f"响应: {response.text}")
    except Exception as e:
        print(f"请求失败: {e}")

if __name__ == "__main__":
    print("测试音频API接口...")
    print("=" * 50)
    
    test_system_status()
    print("-" * 30)
    
    test_stats_files()
    print("-" * 30)
    
    test_active_tasks()
    print("-" * 30)
    
    test_online_users()
    print("-" * 30)
    
    print("测试完成")
