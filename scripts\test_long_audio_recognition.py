#!/usr/bin/env python3
"""
长音频识别专项测试脚本
用于调试和优化长音频识别功能
"""

import os
import sys
import time
import logging
import traceback
import psutil
import torch
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(levelname)s | %(name)s:%(funcName)s:%(lineno)d - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('logs/test_long_audio_recognition.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

def log_performance_metrics(stage: str):
    """记录性能指标"""
    try:
        process = psutil.Process()
        memory_mb = process.memory_info().rss / 1024 / 1024
        cpu_percent = process.cpu_percent()
        
        gpu_info = ""
        if torch.cuda.is_available():
            gpu_memory_mb = torch.cuda.memory_allocated() / 1024 / 1024
            gpu_cached_mb = torch.cuda.memory_reserved() / 1024 / 1024
            gpu_info = f", GPU已分配={gpu_memory_mb:.1f}MB, GPU已缓存={gpu_cached_mb:.1f}MB"
        
        logger.info(f"[性能监控] {stage}: CPU={cpu_percent:.1f}%, 内存={memory_mb:.1f}MB{gpu_info}")
    except Exception as e:
        logger.warning(f"性能监控失败: {e}")

def test_long_audio_recognition():
    """测试长音频识别功能"""
    logger.info("开始长音频识别专项测试")
    
    try:
        # 导入必要模块
        from backend.utils.audio.speech_recognition_core import SpeechRecognitionManager
        
        log_performance_metrics("模块导入完成")
        
        # 测试文件路径
        test_file = "data/uploads/1/63068893-0fcc-4d5b-8574-bc6da581d1e8.mp3"
        if not os.path.exists(test_file):
            logger.error(f"测试文件不存在: {test_file}")
            return False
        
        logger.info(f"测试文件: {test_file}")
        
        # 初始化语音识别管理器
        logger.info("初始化语音识别管理器...")
        start_time = time.time()

        recognizer = SpeechRecognitionManager()

        init_success = recognizer.initialize(
            model_path="models/SenseVoiceSmall",
            device="auto",
            trust_remote_code=True,
            local_files_only=True,
            disable_update=True,
            language="auto",
            use_itn=True,
            merge_vad=True,
            merge_length_s=15.0,
            ban_emo_unk=False,
            batch_size_s=60,
            max_workers=4,
            chunk_size=30.0,
            enable_cache=True,
            memory_limit_gb=8.0,
            min_audio_duration=0.1,
            max_audio_duration=600.0,
            confidence_threshold=0.5
        )
        init_time = time.time() - start_time
        
        if not init_success:
            logger.error("语音识别管理器初始化失败")
            return False
        
        logger.info(f"语音识别管理器初始化成功，耗时: {init_time:.2f}秒")
        log_performance_metrics("初始化完成")
        
        # 测试长音频识别
        logger.info("开始长音频识别测试...")
        recognition_start = time.time()

        # 使用线程超时机制（Windows兼容）
        import threading
        from concurrent.futures import ThreadPoolExecutor, TimeoutError as FutureTimeoutError

        def run_recognition():
            return recognizer.recognize(test_file)

        try:
            # 使用线程池执行，设置60秒超时
            with ThreadPoolExecutor(max_workers=1) as executor:
                future = executor.submit(run_recognition)
                try:
                    result = future.result(timeout=60)  # 60秒超时

                    recognition_time = time.time() - recognition_start
                    logger.info(f"长音频识别完成，耗时: {recognition_time:.2f}秒")
                    log_performance_metrics("识别完成")

                    # 检查结果
                    if result.success:
                        logger.info(f"[SUCCESS] 识别成功:")
                        logger.info(f"   文本长度: {len(result.text)}字符")
                        logger.info(f"   文本内容: {result.text[:100]}...")
                        logger.info(f"   置信度: {result.confidence:.3f}")
                        logger.info(f"   语言: {result.language}")
                        logger.info(f"   音频时长: {result.duration:.2f}秒")
                        logger.info(f"   处理时间: {result.processing_time:.2f}秒")
                        return True
                    else:
                        logger.error(f"[FAILED] 识别失败: {result.error}")
                        return False

                except FutureTimeoutError:
                    logger.error("[TIMEOUT] 长音频识别超时（60秒）")
                    return False

        except Exception as e:
            logger.error(f"[ERROR] 长音频识别异常: {e}")
            logger.error(f"详细错误: {traceback.format_exc()}")
            return False

    except Exception as e:
        logger.error(f"[ERROR] 测试失败: {e}")
        logger.error(f"详细错误: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_long_audio_recognition()
    if success:
        logger.info("[SUCCESS] 长音频识别测试通过")
        sys.exit(0)
    else:
        logger.error("[FAILED] 长音频识别测试失败")
        sys.exit(1)
