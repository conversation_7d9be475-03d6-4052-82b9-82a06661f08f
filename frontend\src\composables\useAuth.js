/**
 * 认证相关的组合式函数
 */

import { computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { useRouter } from 'vue-router'

export function useAuth() {
  const userStore = useUserStore()
  const router = useRouter()

  // 计算属性
  const isAuthenticated = computed(() => userStore.isAuthenticated)
  const user = computed(() => userStore.user)
  const token = computed(() => userStore.token)
  const loading = computed(() => userStore.loading)

  // 登录
  const login = async (credentials) => {
    try {
      await userStore.login(credentials)
      return { success: true }
    } catch (error) {
      return { 
        success: false, 
        error: error.message || '登录失败' 
      }
    }
  }

  // 登出
  const logout = async () => {
    try {
      await userStore.logout()
      router.push('/login')
      return { success: true }
    } catch (error) {
      return { 
        success: false, 
        error: error.message || '登出失败' 
      }
    }
  }

  // 检查认证状态
  const checkAuth = async () => {
    try {
      return await userStore.checkAuth()
    } catch (error) {
      console.error('检查认证状态失败:', error)
      return false
    }
  }

  // 刷新token
  const refreshToken = async () => {
    try {
      return await userStore.refreshToken()
    } catch (error) {
      console.error('刷新token失败:', error)
      return false
    }
  }

  // 需要认证的路由守卫
  const requireAuth = () => {
    if (!isAuthenticated.value) {
      router.push('/login')
      return false
    }
    return true
  }

  // 需要管理员权限的路由守卫
  const requireAdmin = () => {
    if (!isAuthenticated.value) {
      router.push('/login')
      return false
    }
    if (!userStore.isAdmin) {
      router.push('/dashboard')
      return false
    }
    return true
  }

  // 获取token
  const getToken = () => {
    return userStore.token
  }

  return {
    // 状态
    isAuthenticated,
    user,
    token,
    loading,

    // 方法
    login,
    logout,
    checkAuth,
    refreshToken,
    requireAuth,
    requireAdmin,
    getToken
  }
}
