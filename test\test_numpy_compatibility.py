#!/usr/bin/env python3
"""
NumPy 2.x兼容性测试
验证音频处理模块在最新numpy版本下的兼容性
"""

import sys
import os
import tempfile
import numpy as np
import soundfile as sf
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from backend.utils.audio.enhanced_audio_processor import EnhancedAudioProcessor, ProcessingConfig
from backend.utils.audio.audio_preprocessing import AudioPreprocessor, check_audio_quality

def create_test_audio(duration=2.0, sample_rate=16000):
    """创建测试音频文件"""
    t = np.linspace(0, duration, int(sample_rate * duration))
    
    # 生成包含语音特征的复合信号
    signal = (
        0.3 * np.sin(2 * np.pi * 440 * t) +  # 基频
        0.2 * np.sin(2 * np.pi * 880 * t) +  # 二次谐波
        0.1 * np.sin(2 * np.pi * 1320 * t) + # 三次谐波
        0.05 * np.random.normal(0, 0.1, len(t))  # 噪声
    )
    
    # 添加包络以模拟语音
    envelope = np.exp(-0.5 * (t - 1)**2)
    signal = signal * envelope
    
    # 保存为临时文件
    temp_file = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
    sf.write(temp_file.name, signal, sample_rate)
    temp_file.close()
    
    return temp_file.name

def test_numpy_version():
    """测试numpy版本"""
    print(f"NumPy版本: {np.__version__}")
    
    # 测试基本numpy操作
    arr = np.array([1, 2, 3, 4, 5], dtype=np.float32)
    
    # 测试numpy 2.x的新特性和兼容性
    try:
        # 测试astype的copy参数
        arr_copy = arr.astype(np.float64, copy=False)
        print("✅ astype copy参数测试通过")
    except Exception as e:
        print(f"❌ astype copy参数测试失败: {e}")
        return False
    
    try:
        # 测试power函数
        arr_squared = np.power(arr, 2)
        print("✅ np.power函数测试通过")
    except Exception as e:
        print(f"❌ np.power函数测试失败: {e}")
        return False
    
    try:
        # 测试mean函数
        mean_val = np.mean(arr_squared)
        print("✅ np.mean函数测试通过")
    except Exception as e:
        print(f"❌ np.mean函数测试失败: {e}")
        return False
    
    return True

def test_enhanced_audio_processor():
    """测试增强音频处理器"""
    print("\n--- 测试增强音频处理器 ---")
    
    try:
        # 创建测试音频
        test_file = create_test_audio()
        print(f"✅ 创建测试音频文件: {test_file}")
        
        # 创建处理器
        processor = EnhancedAudioProcessor()
        
        # 测试音频加载
        audio, sr = processor.load_audio(test_file)
        if audio is not None:
            print(f"✅ 音频加载成功: 长度={len(audio)}, 采样率={sr}")
        else:
            print("❌ 音频加载失败")
            return False
        
        # 测试音频标准化
        normalized = processor.normalize_audio(audio, target_db=-20.0)
        print(f"✅ 音频标准化成功: 原始RMS={np.sqrt(np.mean(np.power(audio, 2))):.4f}, 标准化RMS={np.sqrt(np.mean(np.power(normalized, 2))):.4f}")
        
        # 测试音频降噪
        denoised = processor.denoise_audio(audio, sr)
        print(f"✅ 音频降噪成功: 原始长度={len(audio)}, 降噪后长度={len(denoised)}")
        
        # 测试音频质量分析
        quality_metrics = processor.analyze_audio_quality(audio, sr)
        if quality_metrics:
            print(f"✅ 音频质量分析成功: 质量评分={quality_metrics.get('quality_score', 0):.1f}")
        else:
            print("❌ 音频质量分析失败")
            return False
        
        # 测试完整处理流程
        output_file = tempfile.NamedTemporaryFile(suffix='.wav', delete=False).name
        processed_file = processor.process_audio(test_file, output_file)
        if processed_file:
            print(f"✅ 完整音频处理成功: {processed_file}")
        else:
            print("❌ 完整音频处理失败")
            return False
        
        # 清理临时文件
        processor.cleanup_temp_files()
        os.unlink(test_file)
        if os.path.exists(output_file):
            os.unlink(output_file)
        
        return True
        
    except Exception as e:
        print(f"❌ 增强音频处理器测试失败: {e}")
        return False

def test_audio_preprocessor():
    """测试音频预处理器"""
    print("\n--- 测试音频预处理器 ---")
    
    try:
        # 创建测试音频
        test_file = create_test_audio()
        print(f"✅ 创建测试音频文件: {test_file}")
        
        # 创建预处理器
        preprocessor = AudioPreprocessor()
        
        # 测试音频格式检查
        format_info = preprocessor.check_audio_format(test_file)
        if format_info.get('is_supported'):
            print(f"✅ 音频格式检查通过: {format_info}")
        else:
            print(f"❌ 音频格式检查失败: {format_info}")
            return False
        
        # 测试音频加载
        audio, sr = preprocessor.load_audio(test_file)
        if audio is not None:
            print(f"✅ 音频加载成功: 长度={len(audio)}, 采样率={sr}")
        else:
            print("❌ 音频加载失败")
            return False
        
        # 测试音量标准化
        normalized = preprocessor.normalize_volume(audio, target_db=-20.0)
        print(f"✅ 音量标准化成功: 原始峰值={np.max(np.abs(audio)):.4f}, 标准化峰值={np.max(np.abs(normalized)):.4f}")
        
        # 测试音频降噪
        denoised = preprocessor.denoise_audio(audio, sr, method='simple')
        print(f"✅ 音频降噪成功: 原始长度={len(audio)}, 降噪后长度={len(denoised)}")
        
        # 测试完整预处理流程
        output_file = tempfile.NamedTemporaryFile(suffix='.wav', delete=False).name
        processed_file = preprocessor.preprocess_audio(test_file, output_file)
        if processed_file:
            print(f"✅ 完整音频预处理成功: {processed_file}")
        else:
            print("❌ 完整音频预处理失败")
            return False
        
        # 清理临时文件
        preprocessor.cleanup_temp_files()
        os.unlink(test_file)
        if os.path.exists(output_file):
            os.unlink(output_file)
        
        return True
        
    except Exception as e:
        print(f"❌ 音频预处理器测试失败: {e}")
        return False

def test_audio_quality_check():
    """测试音频质量检查"""
    print("\n--- 测试音频质量检查 ---")
    
    try:
        # 创建测试音频
        test_file = create_test_audio()
        print(f"✅ 创建测试音频文件: {test_file}")
        
        # 测试质量检查函数
        quality_info = check_audio_quality(test_file)
        if quality_info and not quality_info.get('error'):
            print(f"✅ 音频质量检查成功:")
            print(f"   - 时长: {quality_info.get('duration', 0):.2f}秒")
            print(f"   - 采样率: {quality_info.get('sample_rate', 0)}Hz")
            print(f"   - RMS: {quality_info.get('rms', 0):.4f}")
            print(f"   - 峰值: {quality_info.get('peak', 0):.4f}")
            print(f"   - 质量评分: {quality_info.get('quality_score', 0):.1f}")
            print(f"   - 质量良好: {quality_info.get('is_good_quality', False)}")
        else:
            print(f"❌ 音频质量检查失败: {quality_info.get('error', '未知错误')}")
            return False
        
        # 清理临时文件
        os.unlink(test_file)
        
        return True
        
    except Exception as e:
        print(f"❌ 音频质量检查测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 开始NumPy 2.x兼容性测试")
    print("=" * 60)
    
    tests = [
        ("NumPy版本检查", test_numpy_version),
        ("增强音频处理器", test_enhanced_audio_processor),
        ("音频预处理器", test_audio_preprocessor),
        ("音频质量检查", test_audio_quality_check)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("NumPy 2.x兼容性测试结果:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有NumPy 2.x兼容性测试通过！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查兼容性问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
