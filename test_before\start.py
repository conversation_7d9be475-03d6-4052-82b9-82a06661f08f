#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语音处理智能平台 - 启动脚本
支持开发模式和生产模式启动
"""

import os
import sys
import argparse
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))


def check_environment():
    """检查运行环境"""
    print("🔍 检查运行环境...")
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ Python版本过低，需要Python 3.8+")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    
    # 检查必要的目录
    required_dirs = ["backend", "frontend", "data", "logs", "models"]
    for dir_name in required_dirs:
        dir_path = project_root / dir_name
        if not dir_path.exists():
            print(f"📁 创建目录: {dir_path}")
            dir_path.mkdir(parents=True, exist_ok=True)
    
    # 检查环境变量文件
    env_file = project_root / ".env"
    env_template = project_root / "env.template.txt"
    
    if not env_file.exists() and env_template.exists():
        print("📋 复制环境变量模板...")
        import shutil
        shutil.copy(env_template, env_file)
        print("⚠️  请编辑 .env 文件配置相关参数")
    
    return True


def install_dependencies(mode="backend"):
    """安装依赖"""
    print(f"📦 安装{mode}依赖...")
    
    if mode == "backend":
        # 安装后端依赖
        requirements_file = project_root / "backend" / "requirements.txt"
        if requirements_file.exists():
            cmd = [sys.executable, "-m", "pip", "install", "-r", str(requirements_file)]
            subprocess.run(cmd, check=True)
        else:
            print("❌ 未找到backend/requirements.txt")
            return False
    
    elif mode == "frontend":
        # 安装前端依赖
        frontend_dir = project_root / "frontend"
        if (frontend_dir / "package.json").exists():
            os.chdir(frontend_dir)
            subprocess.run(["npm", "install"], check=True)
            os.chdir(project_root)
        else:
            print("❌ 未找到frontend/package.json")
            return False
    
    return True


def start_backend(dev_mode=True):
    """启动后端服务"""
    print("🚀 启动后端服务...")
    
    backend_dir = project_root / "backend"
    os.chdir(backend_dir)
    
    if dev_mode:
        # 开发模式
        cmd = [
            sys.executable, "-m", "uvicorn", 
            "main:app", 
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload",
            "--log-level", "info"
        ]
    else:
        # 生产模式
        cmd = [
            sys.executable, "-m", "uvicorn",
            "main:app",
            "--host", "0.0.0.0", 
            "--port", "8000",
            "--workers", "4",
            "--log-level", "warning"
        ]
    
    subprocess.run(cmd)


def start_frontend(dev_mode=True):
    """启动前端服务"""
    print("🌐 启动前端服务...")
    
    frontend_dir = project_root / "frontend"
    os.chdir(frontend_dir)
    
    if dev_mode:
        # 开发模式
        subprocess.run(["npm", "run", "dev"])
    else:
        # 生产模式
        subprocess.run(["npm", "run", "build"])
        subprocess.run(["npm", "run", "serve"])


def start_docker():
    """使用Docker启动"""
    print("🐳 使用Docker启动服务...")
    
    os.chdir(project_root)
    
    # 检查docker-compose文件
    compose_file = project_root / "docker-compose.new.yml"
    if not compose_file.exists():
        print("❌ 未找到docker-compose.new.yml")
        return False
    
    # 启动Docker服务
    cmd = ["docker-compose", "-f", "docker-compose.new.yml", "up", "--build"]
    subprocess.run(cmd)
    
    return True


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="语音处理智能平台启动脚本")
    parser.add_argument("--mode", choices=["dev", "prod", "docker"], default="dev",
                       help="启动模式: dev(开发), prod(生产), docker(Docker)")
    parser.add_argument("--service", choices=["backend", "frontend", "all"], default="all",
                       help="启动服务: backend(后端), frontend(前端), all(全部)")
    parser.add_argument("--install", action="store_true",
                       help="安装依赖")
    parser.add_argument("--check", action="store_true",
                       help="仅检查环境")
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("🎵 语音处理智能平台")
    print("=" * 60)
    
    # 检查环境
    if not check_environment():
        sys.exit(1)
    
    if args.check:
        print("✅ 环境检查完成")
        return
    
    # 安装依赖
    if args.install:
        if args.service in ["backend", "all"]:
            install_dependencies("backend")
        if args.service in ["frontend", "all"]:
            install_dependencies("frontend")
        print("✅ 依赖安装完成")
        return
    
    # 启动服务
    dev_mode = args.mode == "dev"
    
    if args.mode == "docker":
        start_docker()
    else:
        if args.service == "backend":
            start_backend(dev_mode)
        elif args.service == "frontend":
            start_frontend(dev_mode)
        elif args.service == "all":
            print("⚠️  全部模式需要分别启动后端和前端")
            print("后端: python start.py --service backend")
            print("前端: python start.py --service frontend")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 服务已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)
