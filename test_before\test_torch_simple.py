#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的torch测试脚本
"""

def test_torch_basic():
    """测试基础torch功能"""
    print("=" * 50)
    print("测试基础torch功能")
    print("=" * 50)
    
    try:
        import torch
        print(f"✅ torch导入成功")
        print(f"   版本: {torch.__version__}")
        print(f"   路径: {torch.__file__}")
        print(f"   CUDA可用: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            print(f"   CUDA设备数量: {torch.cuda.device_count()}")
            print(f"   当前CUDA设备: {torch.cuda.current_device()}")
            print(f"   设备名称: {torch.cuda.get_device_name()}")
        
        # 测试基本操作
        x = torch.tensor([1.0, 2.0, 3.0])
        y = torch.tensor([4.0, 5.0, 6.0])
        z = x + y
        print(f"   基本运算测试: {x.tolist()} + {y.tolist()} = {z.tolist()}")
        
        # 测试GPU操作（如果可用）
        if torch.cuda.is_available():
            x_gpu = x.cuda()
            y_gpu = y.cuda()
            z_gpu = x_gpu + y_gpu
            print(f"   GPU运算测试: {z_gpu.cpu().tolist()}")
        
        return True
        
    except Exception as e:
        print(f"❌ torch测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_torchaudio():
    """测试torchaudio"""
    print("=" * 50)
    print("测试torchaudio")
    print("=" * 50)
    
    try:
        import torchaudio
        print(f"✅ torchaudio导入成功")
        print(f"   版本: {torchaudio.__version__}")
        print(f"   路径: {torchaudio.__file__}")
        
        # 测试基本功能
        print("   可用后端:", torchaudio.list_audio_backends())
        
        return True
        
    except Exception as e:
        print(f"❌ torchaudio测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_speech_utils():
    """测试语音处理工具"""
    print("=" * 50)
    print("测试语音处理工具导入")
    print("=" * 50)
    
    try:
        import sys
        import os
        
        # 添加utils路径
        utils_path = os.path.join(os.path.dirname(__file__), 'utils')
        if utils_path not in sys.path:
            sys.path.append(utils_path)
        
        print("尝试导入speech_recognition_utils...")
        from utils.speech_recognition_utils import load_vad_model
        print("✅ speech_recognition_utils导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 语音处理工具导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 简化torch功能测试")
    print(f"⏰ 时间: {__import__('time').strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = []
    
    # 测试torch基础功能
    results.append(("torch_basic", test_torch_basic()))
    
    # 测试torchaudio
    results.append(("torchaudio", test_torchaudio()))
    
    # 测试语音处理工具
    results.append(("speech_utils", test_speech_utils()))
    
    # 显示结果
    print("\n" + "=" * 50)
    print("测试结果总结")
    print("=" * 50)
    
    success_count = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            success_count += 1
    
    print(f"\n总体结果: {success_count}/{len(results)} 测试通过")
    
    if success_count == len(results):
        print("🎉 所有测试通过！torch环境正常工作")
    elif success_count >= len(results) - 1:
        print("⚠️ 大部分测试通过，可能有轻微问题")
    else:
        print("❌ 存在严重问题，需要进一步修复")

if __name__ == "__main__":
    main()
