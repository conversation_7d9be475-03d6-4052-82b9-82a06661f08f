import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { authAPI } from '@/api/auth'
import { getToken, setToken, removeToken } from '@/utils/auth'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref(getToken())
  const userInfo = ref(null)
  const isLoading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value)
  const userName = computed(() => userInfo.value?.username || '')
  const isAdmin = computed(() => userInfo.value?.is_admin || false)

  // 登录
  const login = async (credentials) => {
    try {
      isLoading.value = true
      const response = await authAPI.login(credentials)
      
      if (response.data.access_token) {
        token.value = response.data.access_token
        setToken(response.data.access_token)
        
        // 获取用户信息
        await getUserInfo()
        
        return { success: true, data: response.data }
      } else {
        throw new Error('登录失败：未获取到访问令牌')
      }
    } catch (error) {
      console.error('登录失败:', error)
      return { 
        success: false, 
        message: error.response?.data?.detail || error.message || '登录失败'
      }
    } finally {
      isLoading.value = false
    }
  }

  // 注册
  const register = async (userData) => {
    try {
      isLoading.value = true
      const response = await authAPI.register(userData)
      return { success: true, data: response.data }
    } catch (error) {
      console.error('注册失败:', error)
      return { 
        success: false, 
        message: error.response?.data?.detail || error.message || '注册失败'
      }
    } finally {
      isLoading.value = false
    }
  }

  // 获取用户信息
  const getUserInfo = async () => {
    try {
      if (!token.value) return null

      const response = await authAPI.getCurrentUser()
      userInfo.value = response.data
      return response.data
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 如果获取用户信息失败，可能是token过期，清除登录状态
      logout()
      return null
    }
  }

  // 登出
  const logout = () => {
    token.value = null
    userInfo.value = null
    removeToken()
  }

  // 刷新token
  const refreshToken = async () => {
    try {
      const response = await authAPI.refreshToken()
      if (response.data.access_token) {
        token.value = response.data.access_token
        setToken(response.data.access_token)
        return true
      }
      return false
    } catch (error) {
      console.error('刷新token失败:', error)
      logout()
      return false
    }
  }

  // 检查认证状态
  const checkAuth = async () => {
    if (!token.value) return false
    
    try {
      await getUserInfo()
      return true
    } catch (error) {
      logout()
      return false
    }
  }

  // 初始化
  const init = async () => {
    if (token.value) {
      await checkAuth()
    }
  }

  return {
    // 状态
    token,
    userInfo,
    isLoading,
    
    // 计算属性
    isAuthenticated,
    userName,
    isAdmin,
    
    // 方法
    login,
    register,
    logout,
    getUserInfo,
    refreshToken,
    checkAuth,
    init
  }
})
