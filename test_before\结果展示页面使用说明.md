# 结果展示页面使用说明

## 🎯 问题解决

您遇到的"导入模块失败: No module named 'utils'"错误已经解决！

## ✅ 解决方案

导入问题的原因是当Streamlit从`pages/`子目录运行时，Python的工作目录发生了变化，导致无法找到`utils`模块。

**解决方法**：确保从**项目根目录**运行Streamlit命令。

## 🚀 正确的启动方式

在项目根目录 `D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0` 运行：

```bash
streamlit run "pages/结果展示和编辑.py" --server.port 8503
```

## 📍 访问地址

- **结果展示页面**: http://localhost:8503
- **主应用**: http://localhost:8501 (如果同时运行)

## 🎭 功能使用指南

### 1. 创建演示数据
- 首次访问页面时，点击"创建演示数据"按钮
- 系统会自动生成包含多个说话人和语音片段的测试数据

### 2. 查看结果概览
- **基本信息**: 音频文件名、总时长、说话人数、片段数
- **详细统计**: 有效语音时长、平均置信度、总字数

### 3. 时间轴可视化
- **Gantt图**: 显示各说话人的语音片段时间分布
- **置信度图**: 展示识别置信度随时间的变化
- **交互功能**: 悬停查看详细信息

### 4. 语音片段详情
- **表格视图**: 序号、时间、说话人、文本内容、置信度
- **完整信息**: 包含情感标签等扩展信息

### 5. 转录文本管理
- **格式选项**: 可选包含/排除时间戳和说话人信息
- **在线编辑**: 实时修改转录内容
- **导出功能**: 支持TXT和JSON格式下载

## 🎛️ 侧边栏控制

- **当前结果信息**: 显示加载的数据基本信息
- **数据管理**: 清除当前数据，重新开始

## 📊 数据结构特性

页面基于新的统一结果数据结构，支持：
- ✅ 时间区间管理
- ✅ 说话人档案信息
- ✅ 语音识别结果
- ✅ 情感和事件标签
- ✅ 性能指标统计
- ✅ 完整的序列化支持

## 🔧 技术栈

- **前端**: Streamlit + Plotly图表
- **数据处理**: Pandas + NumPy
- **数据结构**: 自定义统一结果类
- **可视化**: 交互式图表和表格

## 💡 使用建议

1. **首次使用**: 先创建演示数据熟悉界面
2. **数据导入**: 后续可导入真实的语音识别结果
3. **功能探索**: 尝试不同的可视化和导出选项
4. **编辑功能**: 利用在线编辑功能优化识别结果

## 🚨 注意事项

- 确保在项目根目录运行Streamlit命令
- 虚拟环境需要正确激活
- 端口8503和8501可能同时被占用，这是正常的

---

**现在您可以正常使用结果展示和编辑功能了！** 🎉 