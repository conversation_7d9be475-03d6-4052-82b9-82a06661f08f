import{b as i}from"./audioProcessing-868a9059.js";import{u as q}from"./auth-e6295339.js";import{h as b,n as z,E as y,i as H}from"./index-2c134546.js";function C(){const S=q(),u=b(!1),_=b(!1),f=b(0),p=b(null);let c=null,k=null,d=null,l=null;const g={maxReconnectAttempts:10,reconnectInterval:3e3,heartbeatInterval:9e4,heartbeatTimeout:45e3,connectionTimeout:15e3,maxIdleTime:6e5,maxBackoffDelay:6e4},T=new Map,$=async()=>_.value||u.value?Promise.resolve():new Promise((s,a)=>{try{_.value=!0,p.value=null;const o=S.token;if(!o)throw new Error("没有有效的认证token");const w=window.location.protocol==="https:"?"wss:":"ws:",x={}.VITE_WS_HOST||window.location.hostname,O={}.VITE_WS_PORT||"8002",R=`${w}//${x}:${O}/ws/progress?token=${o}`;console.log("🔌 连接WebSocket:",R),c=new WebSocket(R),c.onopen=()=>{console.log("✅ WebSocket连接成功"),_.value=!1,u.value=!0,f.value=0,r(),v("connected"),s()},c.onmessage=m=>{try{const D=JSON.parse(m.data);M(D)}catch(D){console.error("❌ WebSocket消息解析失败:",D)}},c.onclose=m=>{console.log("🔌 WebSocket连接关闭:",m.code,m.reason),_.value=!1,u.value=!1,n(),v("disconnected",{code:m.code,reason:m.reason}),m.code!==1e3&&f.value<g.maxReconnectAttempts&&e()},c.onerror=m=>{console.error("❌ WebSocket连接错误:",m),_.value=!1,u.value=!1,p.value=m,v("error",m),a(m)}}catch(o){_.value=!1,p.value=o,a(o)}}),W=()=>{u.value=!1,_.value=!1,k&&(clearTimeout(k),k=null),n(),c&&(c.close(1e3,"Client disconnect"),c=null),console.log("🔌 WebSocket已断开")},h=s=>{if(c&&u.value)try{return c.send(JSON.stringify(s)),!0}catch(a){return console.error("❌ 发送WebSocket消息失败:",a),!1}return!1},E=s=>h({type:"subscribe",task_id:s}),P=s=>h({type:"unsubscribe",task_id:s}),A=s=>{const a=Date.now()+Math.random();return T.set(a,s),()=>{T.delete(a)}},M=s=>{switch(console.log("📨 收到WebSocket消息:",s),T.forEach(a=>{try{a(s)}catch(o){console.error("❌ WebSocket消息处理器错误:",o)}}),s.type){case"heartbeat":l&&(clearTimeout(l),l=null);break;case"progress":case"progress_update":const a={task_id:s.task_id,...s.data};v("progress",a);break;case"task_completed":const o={task_id:s.task_id,...s.data};v("task_completed",o),y.success("任务处理完成");break;case"task_failed":const w={task_id:s.task_id,...s.data};v("task_failed",w),y.error(`任务处理失败: ${w.error||"未知错误"}`);break;case"error":v("error",s),y.error(`服务器错误: ${s.message||"未知错误"}`);break;default:console.log("🔍 未处理的消息类型:",s.type)}},v=(s,a=null)=>{console.log(`📡 WebSocket事件: ${s}`,a)},e=()=>{if(k)return;f.value++;const s=g.reconnectInterval*Math.pow(2,f.value-1);console.log(`🔄 ${s}ms后尝试第${f.value}次重连...`),k=setTimeout(()=>{k=null,u.value||$().catch(a=>{console.error("❌ WebSocket重连失败:",a)})},s)},r=()=>{n(),d=setInterval(()=>{u.value&&h({type:"ping"})&&(l=setTimeout(()=>{console.warn("⚠️ 心跳超时，重连WebSocket"),u.value&&(console.warn("⚠️ 心跳超时，断开连接"),c.close(1006,"Heartbeat timeout"))},g.heartbeatTimeout))},g.heartbeatInterval)},n=()=>{d&&(clearInterval(d),d=null),l&&(clearTimeout(l),l=null)},t=()=>({connected:u.value,connecting:_.value,reconnectAttempts:f.value,lastError:p.value});return z(()=>{W()}),{connected:u,connecting:_,reconnectAttempts:f,lastError:p,connect:$,disconnect:W,send:h,subscribeTask:E,unsubscribeTask:P,onMessage:A,getStatus:t}}function J(){const{connected:S,subscribeTask:u,unsubscribeTask:_,onMessage:f}=C(),p=b(!1),c=b(""),k=b([]),d=b([]),l=H({overall_progress:0,current_stage:null,estimated_time:0,performance_stats:null,errors:[]}),g=new Map,T=async e=>{try{const{files:r,mode:n,config:t={}}=e;if(!r||r.length===0)throw new Error("请选择要处理的音频文件");p.value=!0,d.value=[],Object.assign(l,{overall_progress:0,current_stage:null,estimated_time:0,performance_stats:null,errors:[]});let s;const a=r.map(w=>w.uid||w.id);switch(n){case"vad_detection":s=await i.vadDetection({file_ids:a,config:t});break;case"speech_recognition":s=await i.speechRecognition({file_ids:a,language:t.language||"auto",use_itn:t.use_itn!==!1,ban_emo_unk:t.ban_emo_unk===!0,config:t});break;case"speaker_recognition":s=await i.speakerRecognition({file_ids:a,clustering_method:t.clustering_method||"auto",expected_speakers:t.expected_speakers||2,similarity_threshold:t.similarity_threshold||.7,config:t});break;case"meeting_transcription":s=await i.meetingTranscription({file_ids:a,language:t.language||"auto",output_format:t.output_format||"timeline",include_timestamps:t.include_timestamps!==!1,speaker_labeling:t.speaker_labeling!==!1,expected_speakers:t.expected_speakers||2,similarity_threshold:t.similarity_threshold||.15,clustering_method:t.clustering_method||"auto",config:t});break;case"audio_preprocessing":s=await i.audioPreprocessing({file_ids:a,target_sr:t.target_sr||16e3,target_channels:t.target_channels||1,normalize:t.normalize!==!1,denoise:t.denoise!==!1,config:t});break;case"audio_enhancement":s=await i.audioEnhancement({file_ids:a,language:t.language||"auto",use_itn:t.use_itn!==!1,ban_emo_unk:t.ban_emo_unk===!0,normalize:t.normalize!==!1,denoise:t.denoise!==!1,config:t});break;case"comprehensive_analysis":s=await $(a,t);break;default:throw new Error(`不支持的处理模式: ${n}`)}const o=s.data||s;if(o.success)return c.value=o.task_id,S.value&&u(o.task_id),W(o.task_id),y.success("音频处理任务已启动"),o;throw new Error(o.message||"启动处理任务失败")}catch(r){throw p.value=!1,d.value.push({timestamp:Date.now(),message:r.message||"未知错误"}),y.error(`启动处理失败: ${r.message}`),r}},$=async(e,r)=>{const n=[{name:"preprocessing",weight:.2},{name:"vad_detection",weight:.2},{name:"speech_recognition",weight:.3},{name:"speaker_recognition",weight:.2},{name:"quality_analysis",weight:.1}],t=[];let s=0;for(const a of n)try{l.current_stage={stage:a.name,percentage:0,detail:`执行${a.name}...`};let o;switch(a.name){case"preprocessing":o=await i.audioPreprocessing({file_ids:e,config:r.preprocessing||{}});break;case"vad_detection":o=await i.vadDetection({file_ids:e,config:r.vad||{}});break;case"speech_recognition":o=await i.speechRecognition({file_ids:e,config:r.speech||{}});break;case"speaker_recognition":o=await i.speakerRecognition({file_ids:e,config:r.speaker||{}});break;case"quality_analysis":o=await i.qualityAnalysis({file_ids:e,config:r.quality||{}});break}t.push({step:a.name,result:o,status:"success"}),s+=a.weight*100,l.overall_progress=s}catch(o){t.push({step:a.name,error:o.message,status:"error"}),d.value.push({timestamp:Date.now(),message:`${a.name}步骤失败: ${o.message}`})}return{success:!0,task_id:`comprehensive_${Date.now()}`,message:"综合分析完成",results:t}},W=e=>{const r=f(n=>{n.task_id===e&&h(n)});g.set(e,r),S.value||E(e)},h=e=>{switch(e.type){case"progress":l.overall_progress=e.progress||0,l.current_stage=e.stage_info||null,l.estimated_time=e.estimated_time||0,l.performance_stats=e.performance||null;break;case"task_completed":p.value=!1,l.overall_progress=100,k.value.push({task_id:e.task_id,result:e.result,timestamp:Date.now(),status:"completed"}),P(e.task_id);break;case"task_failed":p.value=!1,d.value.push({timestamp:Date.now(),message:e.error||"任务处理失败"}),P(e.task_id);break}},E=e=>{const r=setInterval(async()=>{try{const n=await i.getTaskStatus(e);n.status==="completed"?(clearInterval(r),h({type:"task_completed",task_id:e,result:n.result})):n.status==="failed"?(clearInterval(r),h({type:"task_failed",task_id:e,error:n.error})):h({type:"progress",task_id:e,progress:n.progress||0})}catch(n){console.error("轮询任务状态失败:",n)}},2e3);g.set(`poll_${e}`,()=>clearInterval(r))},P=e=>{const r=g.get(e);r&&(r(),g.delete(e));const n=g.get(`poll_${e}`);n&&(n(),g.delete(`poll_${e}`)),S.value&&_(e)};return{isProcessing:p,currentTaskId:c,processingResults:k,processingErrors:d,taskStatus:l,wsConnected:S,startAudioProcessing:T,cancelProcessing:async()=>{if(c.value)try{await i.cancelTask(c.value),p.value=!1,P(c.value),c.value="",y.info("任务已取消")}catch(e){y.error(`取消任务失败: ${e.message}`)}},getProcessingStatus:async e=>{try{return await i.getTaskStatus(e)}catch(r){return console.error("获取任务状态失败:",r),null}},cleanup:()=>{g.forEach(e=>e()),g.clear(),p.value=!1,c.value=""}}}export{J as a,C as u};
