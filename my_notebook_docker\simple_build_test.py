#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的Docker构建测试脚本 - Windows兼容版本
快速验证Docker构建准备情况
"""

import os
import sys
import subprocess

def check_files():
    """检查必需文件"""
    print("🔍 检查必需文件...")
    
    files = {
        ".venv/": "Python虚拟环境",
        "frontend/": "前端源码",
        "backend/": "后端源码", 
        "config/": "配置文件",
        "utils/": "工具模块",
        "requirements.txt": "Python依赖",
        ".dockerignore": "Docker忽略文件",
        "my_notebook_docker/Dockerfile.frontend": "前端Dockerfile",
        "my_notebook_docker/Dockerfile.backend": "后端Dockerfile",
        "my_notebook_docker/Dockerfile.celery": "Celery Dockerfile",
        "my_notebook_docker/docker-compose.yml": "Docker Compose配置"
    }
    
    missing = []
    for file_path, description in files.items():
        if os.path.exists(file_path):
            print(f"  ✅ {description}: {file_path}")
        else:
            print(f"  ❌ {description}: {file_path} (缺失)")
            missing.append(file_path)
    
    return len(missing) == 0, missing

def check_docker():
    """检查Docker环境"""
    print("\n🐳 检查Docker环境...")
    
    try:
        # 检查Docker
        result = subprocess.run(['docker', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"  ✅ Docker: {result.stdout.strip()}")
        else:
            print("  ❌ Docker不可用")
            return False
        
        # 检查Docker Compose
        result = subprocess.run(['docker-compose', '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"  ✅ Docker Compose: {result.stdout.strip()}")
        else:
            print("  ⚠️ Docker Compose不可用")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Docker检查失败: {e}")
        return False

def check_dockerignore():
    """检查.dockerignore配置"""
    print("\n📋 检查.dockerignore配置...")
    
    if not os.path.exists('.dockerignore'):
        print("  ❌ .dockerignore文件不存在")
        return False
    
    try:
        with open('.dockerignore', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查关键排除项
        good_exclusions = ['__pycache__', '*.log', '.git', 'test/']
        for exclusion in good_exclusions:
            if exclusion in content:
                print(f"  ✅ 正确排除: {exclusion}")
            else:
                print(f"  ⚠️ 可能遗漏: {exclusion}")
        
        # 检查不应该被排除的项
        should_not_exclude = ['.venv/', 'frontend/', 'backend/', 'config/', 'utils/']
        for item in should_not_exclude:
            # 检查是否被错误排除（不在注释中）
            lines = content.split('\n')
            excluded = False
            for line in lines:
                line = line.strip()
                if line == item and not line.startswith('#'):
                    excluded = True
                    break
            
            if excluded:
                print(f"  ❌ 错误排除: {item}")
                return False
            else:
                print(f"  ✅ 正确保留: {item}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 读取.dockerignore失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 Docker构建准备检查")
    print("=" * 50)
    
    # 切换到项目根目录
    if os.path.basename(os.getcwd()) == 'my_notebook_docker':
        os.chdir('..')
    
    # 执行检查
    files_ok, missing_files = check_files()
    docker_ok = check_docker()
    dockerignore_ok = check_dockerignore()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 检查结果总结:")
    
    if files_ok:
        print("  ✅ 必需文件检查: 通过")
    else:
        print("  ❌ 必需文件检查: 失败")
        print("    缺失文件:", ", ".join(missing_files))
    
    if docker_ok:
        print("  ✅ Docker环境检查: 通过")
    else:
        print("  ❌ Docker环境检查: 失败")
    
    if dockerignore_ok:
        print("  ✅ .dockerignore配置: 正确")
    else:
        print("  ❌ .dockerignore配置: 有问题")
    
    # 最终结果
    all_ok = files_ok and docker_ok and dockerignore_ok
    
    print("\n" + "=" * 50)
    if all_ok:
        print("🎉 所有检查通过！可以执行Docker构建:")
        print("   cd my_notebook_docker")
        print("   docker-compose build")
    else:
        print("❌ 检查失败，请修复上述问题后再构建")
    
    print("\n💡 提示:")
    print("   - 如果Docker环境正常，可以尝试直接构建")
    print("   - 构建命令: docker-compose build")
    print("   - 单独构建: docker-compose build frontend")
    
    return all_ok

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
