#!/usr/bin/env python3
"""
直接检查数据库中的任务状态
"""
import sqlite3
import json
import sys

def check_database():
    """检查数据库中的任务状态"""
    try:
        conn = sqlite3.connect('data/speech_platform.db')
        cursor = conn.cursor()
        
        print("🔍 检查数据库中的音频处理任务...")
        
        # 查询所有任务
        cursor.execute("""
            SELECT id, status, progress, result, error_message, created_at, updated_at, processing_mode
            FROM audio_processing_tasks 
            ORDER BY created_at DESC
            LIMIT 10
        """)
        
        tasks = cursor.fetchall()
        
        if not tasks:
            print("❌ 数据库中没有找到任何任务")
            return
            
        print(f"✅ 找到 {len(tasks)} 个任务:")
        print("-" * 100)
        
        for i, task in enumerate(tasks, 1):
            task_id, status, progress, result, error_msg, created_at, updated_at, processing_mode = task
            
            print(f"\n📋 任务 {i}:")
            print(f"  ID: {task_id}")
            print(f"  状态: {status}")
            print(f"  进度: {progress}%")
            print(f"  处理模式: {processing_mode}")
            print(f"  创建时间: {created_at}")
            print(f"  更新时间: {updated_at}")
            
            if error_msg:
                print(f"  ❌ 错误信息: {error_msg}")
            
            if result:
                print(f"  📊 结果数据:")
                try:
                    result_data = json.loads(result)
                    print(f"    类型: {result_data.get('task_type', 'unknown')}")
                    print(f"    总文件数: {result_data.get('total_files', 0)}")
                    print(f"    成功数: {result_data.get('success_count', 0)}")
                    print(f"    失败数: {result_data.get('error_count', 0)}")
                    
                    # 检查说话人识别特有的字段
                    if result_data.get('task_type') == 'speaker_recognition':
                        print(f"    聚类方法: {result_data.get('clustering_method', 'unknown')}")
                        print(f"    期望说话人数: {result_data.get('expected_speakers', 'auto')}")
                        
                        results = result_data.get('results', [])
                        if results:
                            for j, file_result in enumerate(results):
                                print(f"    文件 {j+1}:")
                                print(f"      文件路径: {file_result.get('file_path', 'unknown')}")
                                print(f"      状态: {file_result.get('status', 'unknown')}")
                                
                                file_result_data = file_result.get('result', {})
                                if file_result_data:
                                    print(f"      说话人数: {file_result_data.get('total_speakers', 0)}")
                                    print(f"      音频时长: {file_result_data.get('audio_duration', 0):.2f}秒")
                                    
                                    # 检查是否有segments数据
                                    segments = file_result_data.get('segments', [])
                                    if segments:
                                        print(f"      语音片段数: {len(segments)}")
                                        print(f"      前3个片段:")
                                        for k, segment in enumerate(segments[:3]):
                                            print(f"        片段{k+1}: {segment.get('start', 0):.2f}s-{segment.get('end', 0):.2f}s, 说话人: {segment.get('speaker', 'unknown')}")
                                    else:
                                        print(f"      ❌ 没有找到语音片段数据")
                                        
                                    # 检查说话人统计
                                    speakers = file_result_data.get('speakers', [])
                                    if speakers:
                                        print(f"      说话人统计:")
                                        for speaker in speakers:
                                            print(f"        {speaker.get('speaker_id', 'unknown')}: {speaker.get('total_duration', 0):.2f}秒, {speaker.get('segment_count', 0)}个片段")
                                    else:
                                        print(f"      ❌ 没有找到说话人统计数据")
                except Exception as e:
                    print(f"    ❌ 解析结果数据失败: {e}")
                    print(f"    原始数据: {result[:200]}...")
            
            print("-" * 50)
            
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")
        return
    finally:
        if 'conn' in locals():
            conn.close()

def check_celery_status():
    """检查Celery任务状态"""
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        
        print("\n🔍 检查Redis中的Celery任务...")
        
        # 检查活跃任务
        active_tasks = r.llen('celery')
        print(f"活跃任务数: {active_tasks}")
        
        # 检查所有键
        keys = r.keys('*')
        celery_keys = [k.decode() for k in keys if b'celery' in k]
        
        if celery_keys:
            print(f"Celery相关键: {celery_keys}")
        else:
            print("没有找到Celery相关的键")
            
    except Exception as e:
        print(f"❌ 检查Redis失败: {e}")

if __name__ == "__main__":
    print("🚀 开始检查数据库和Celery状态...")
    check_database()
    check_celery_status()
    print("\n🎉 检查完成！")
