import streamlit as st
import tempfile
import os
from pathlib import Path
import json

from utils.ui_exception_handler import (
    UIExceptionHandler,
    ui_exception_handler,
    streamlit_exception_handler,
    show_error,
    show_success,
    show_warning,
    show_info
)
from utils.exception_handler import (
    AudioProcessingError,
    ModelLoadingError,
    FileOperationError,
    MemoryError,
    GPUError,
    ValidationError,
    ConfigurationError,
    ErrorSeverity,
    ErrorCategory
)

class TestUIExceptionHandler:
    """UI异常处理器测试"""
    
    def __init__(self):
        self.handler = UIExceptionHandler()
    
    def test_basic_functionality(self):
        """测试基本功能"""
        st.header("🧪 UI异常处理器测试")
        
        # 测试各种异常类型
        test_exceptions = [
            AudioProcessingError(
                "音频文件格式不支持",
                severity=ErrorSeverity.HIGH,
                suggestion="请使用WAV、MP3或FLAC格式"
            ),
            ModelLoadingError(
                "模型文件未找到",
                severity=ErrorSeverity.CRITICAL,
                suggestion="请检查模型文件路径"
            ),
            FileOperationError(
                "文件权限被拒绝",
                severity=ErrorSeverity.MEDIUM,
                suggestion="请检查文件权限设置"
            ),
            MemoryError(
                "系统内存不足",
                severity=ErrorSeverity.HIGH,
                suggestion="请关闭其他应用程序"
            ),
            GPUError(
                "GPU不可用",
                severity=ErrorSeverity.MEDIUM,
                suggestion="已切换到CPU模式"
            ),
            ValidationError(
                "输入参数无效",
                severity=ErrorSeverity.LOW,
                suggestion="请检查输入格式"
            )
        ]
        
        for i, exception in enumerate(test_exceptions):
            with st.expander(f"测试 {i+1}: {exception.__class__.__name__}", expanded=False):
                self.handler.show_error(
                    exception,
                    context={"test_id": i+1, "function": "test_function"},
                    show_details=True
                )
                
                # 显示恢复选项
                self.handler.create_error_recovery_widget(exception)
    
    def test_standard_exceptions(self):
        """测试标准Python异常"""
        st.subheader("标准异常测试")
        
        standard_exceptions = [
            FileNotFoundError("文件不存在"),
            PermissionError("权限被拒绝"),
            ValueError("值错误"),
            ConnectionError("连接失败"),
            TimeoutError("操作超时")
        ]
        
        for i, exception in enumerate(standard_exceptions):
            with st.expander(f"标准异常 {i+1}: {exception.__class__.__name__}", expanded=False):
                self.handler.show_error(
                    exception,
                    context={"test_type": "standard_exception"},
                    show_details=True
                )
    
    def test_message_types(self):
        """测试不同类型的消息"""
        st.subheader("消息类型测试")
        
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("测试成功消息"):
                show_success(
                    "操作成功完成！", 
                    "所有音频文件已成功处理并保存到指定目录。"
                )
        
        with col2:
            if st.button("测试警告消息"):
                show_warning(
                    "检测到潜在问题", 
                    "建议检查音频质量，某些文件可能包含噪音。"
                )
        
        col3, col4 = st.columns(2)
        
        with col3:
            if st.button("测试信息消息"):
                show_info(
                    "处理进度更新", 
                    "当前正在处理第3个文件，共5个文件。"
                )
        
        with col4:
            if st.button("测试错误消息"):
                show_error(
                    AudioProcessingError(
                        "音频处理失败",
                        severity=ErrorSeverity.HIGH,
                        context={"file": "test.wav"},
                        suggestion="请检查音频文件完整性"
                    ),
                    show_details=True
                )
    
    def test_decorator_functionality(self):
        """测试装饰器功能"""
        st.subheader("装饰器测试")
        
        @streamlit_exception_handler(show_details=True, show_traceback=True)
        def failing_function():
            """故意失败的函数用于测试"""
            raise AudioProcessingError(
                "装饰器测试异常",
                severity=ErrorSeverity.MEDIUM,
                suggestion="这是一个测试异常，用于验证装饰器功能"
            )
        
        @streamlit_exception_handler(show_details=True)
        def another_failing_function():
            """另一个失败函数"""
            raise ValueError("这是一个标准Python异常")
        
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("测试自定义异常装饰器"):
                failing_function()
        
        with col2:
            if st.button("测试标准异常装饰器"):
                another_failing_function()
    
    def test_error_messages_customization(self):
        """测试错误消息自定义"""
        st.subheader("错误消息自定义测试")
        
        # 显示当前错误消息
        with st.expander("当前错误消息配置", expanded=False):
            st.json(self.handler.error_messages)
        
        # 自定义错误消息
        st.write("自定义错误消息：")
        
        col1, col2 = st.columns(2)
        
        with col1:
            error_key = st.selectbox(
                "选择错误类型",
                options=list(self.handler.error_messages.keys())
            )
        
        with col2:
            custom_message = st.text_input(
                "自定义消息",
                value=self.handler.error_messages.get(error_key, "")
            )
        
        if st.button("更新错误消息"):
            if error_key and custom_message:
                self.handler.error_messages[error_key] = custom_message
                self.handler._save_error_messages()
                st.success(f"已更新错误消息: {error_key}")
                st.rerun()
    
    def test_feedback_system(self):
        """测试反馈系统"""
        st.subheader("错误反馈系统测试")
        
        # 模拟一个错误并显示反馈表单
        test_error = AudioProcessingError(
            "这是一个测试错误，用于演示反馈系统",
            severity=ErrorSeverity.MEDIUM,
            suggestion="这是一个建议消息"
        )
        
        self.handler.show_error(test_error, show_details=True)
        self.handler.create_error_feedback_form()
    
    def test_error_statistics(self):
        """测试错误统计"""
        st.subheader("错误统计")
        
        # 显示反馈文件内容（如果存在）
        feedback_file = Path("logs/error_feedback.jsonl")
        if feedback_file.exists():
            st.write("用户反馈记录：")
            try:
                feedbacks = []
                with open(feedback_file, 'r', encoding='utf-8') as f:
                    for line in f:
                        if line.strip():
                            feedbacks.append(json.loads(line))
                
                if feedbacks:
                    for i, feedback in enumerate(feedbacks[-5:]):  # 显示最近5条
                        with st.expander(f"反馈 {i+1} - {feedback['timestamp'][:19]}", expanded=False):
                            st.write(f"**描述**: {feedback['description']}")
                            st.write(f"**邮箱**: {feedback['email']}")
                            st.write(f"**时间**: {feedback['timestamp']}")
                else:
                    st.info("暂无用户反馈")
            except Exception as e:
                st.error(f"读取反馈文件失败: {e}")
        else:
            st.info("暂无反馈文件")

def run_comprehensive_test():
    """运行综合测试"""
    st.title("🎛️ UI异常处理器综合测试")
    
    # 创建测试实例
    tester = TestUIExceptionHandler()
    
    # 侧边栏测试选择
    with st.sidebar:
        st.header("测试选项")
        
        test_options = {
            "基本功能测试": tester.test_basic_functionality,
            "标准异常测试": tester.test_standard_exceptions,
            "消息类型测试": tester.test_message_types,
            "装饰器功能测试": tester.test_decorator_functionality,
            "错误消息自定义": tester.test_error_messages_customization,
            "反馈系统测试": tester.test_feedback_system,
            "错误统计": tester.test_error_statistics
        }
        
        selected_tests = st.multiselect(
            "选择要运行的测试",
            options=list(test_options.keys()),
            default=["基本功能测试", "消息类型测试"]
        )
        
        if st.button("运行选中的测试"):
            st.session_state.run_tests = True
            st.session_state.selected_tests = selected_tests
    
    # 运行测试
    if st.session_state.get("run_tests", False):
        for test_name in st.session_state.get("selected_tests", []):
            if test_name in test_options:
                st.markdown(f"## {test_name}")
                try:
                    test_options[test_name]()
                except Exception as e:
                    st.error(f"测试 '{test_name}' 执行失败: {e}")
                st.markdown("---")
        
        st.session_state.run_tests = False
    
    # 显示使用说明
    with st.expander("📖 使用说明", expanded=False):
        st.markdown("""
        ### UI异常处理器功能说明
        
        **主要功能：**
        1. **友好错误提示**: 将技术错误转换为用户友好的消息
        2. **分级错误显示**: 根据错误严重程度使用不同的显示样式
        3. **错误恢复**: 提供自动恢复选项（适用时）
        4. **用户反馈**: 收集用户对错误消息的反馈
        5. **装饰器支持**: 通过装饰器简化异常处理
        
        **错误级别：**
        - 🚨 **严重错误 (CRITICAL)**: 系统无法继续运行
        - ❌ **高级错误 (HIGH)**: 影响主要功能
        - ⚠️ **中级错误 (MEDIUM)**: 影响部分功能
        - ℹ️ **低级错误 (LOW)**: 轻微问题或提示
        
        **使用方法：**
        ```python
        from utils.ui_exception_handler import show_error, streamlit_exception_handler
        
        # 直接显示错误
        show_error(exception, context={"file": "test.wav"})
        
        # 使用装饰器
        @streamlit_exception_handler(show_details=True)
        def my_function():
            # 函数代码
            pass
        ```
        """)

if __name__ == "__main__":
    run_comprehensive_test() 