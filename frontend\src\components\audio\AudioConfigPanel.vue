<template>
  <div class="audio-config-panel">
    <!-- VAD配置 -->
    <div v-if="showVadConfig" class="config-group">
      <div class="group-header">
        <h4>🎯 VAD语音活动检测配置</h4>
      </div>
      <div class="config-items">
        <div class="config-item">
          <label>合并长度 (秒)</label>
          <el-input-number
            :model-value="audioConfig.merge_length_s"
            @update:model-value="val => handleConfigChange('merge_length_s', val)"
            :min="1"
            :max="60"
            :step="1"
            size="small"
          />
        </div>

        <div class="config-item">
          <label>最小语音时长 (秒)</label>
          <el-input-number
            :model-value="audioConfig.min_speech_duration"
            @update:model-value="val => handleConfigChange('min_speech_duration', val)"
            :min="0.1"
            :max="10"
            :step="0.1"
            :precision="1"
            size="small"
          />
        </div>

        <div class="config-item">
          <label>最大语音时长 (秒)</label>
          <el-input-number
            :model-value="audioConfig.max_speech_duration"
            @update:model-value="val => handleConfigChange('max_speech_duration', val)"
            :min="10"
            :max="300"
            :step="5"
            size="small"
          />
        </div>

        <div class="config-item">
          <label>检测阈值</label>
          <el-slider
            :model-value="audioConfig.threshold"
            @update:model-value="val => handleConfigChange('threshold', val)"
            :min="0.1"
            :max="0.9"
            :step="0.05"
            :format-tooltip="formatThresholdTooltip"
          />
          <div class="config-hint">阈值越高，检测越严格 (当前: {{ audioConfig.threshold }})</div>
        </div>
      </div>
    </div>

    <!-- 语音识别配置 -->
    <div v-if="showSpeechConfig" class="config-group">
      <div class="group-header">
        <h4>🗣️ 语音识别配置</h4>
      </div>
      <div class="config-items">
        <div class="config-item">
          <label>识别语言</label>
          <el-select v-model="speechConfig.language">
            <el-option
              v-for="lang in languages"
              :key="lang.value"
              :label="lang.label"
              :value="lang.value"
            />
          </el-select>
        </div>
        
        <div class="config-item">
          <el-checkbox v-model="speechConfig.use_itn">
            使用逆文本正则化
          </el-checkbox>
        </div>
        
        <div class="config-item">
          <el-checkbox v-model="speechConfig.ban_emo_unk">
            禁用未知情感标签
          </el-checkbox>
        </div>
        
        <div class="config-item">
          <label>合并VAD结果</label>
          <el-switch v-model="speechConfig.merge_vad" />
        </div>
        
        <div class="config-item">
          <label>VAD合并长度 (秒)</label>
          <el-input-number
            v-model="speechConfig.merge_length_s"
            :min="5"
            :max="60"
            :step="5"
            size="small"
          />
        </div>
      </div>
    </div>

    <!-- 说话人识别配置 -->
    <div v-if="showSpeakerConfig" class="config-group">
      <div class="group-header">
        <h4>👥 说话人识别配置</h4>
      </div>
      <div class="config-items">
        <div class="config-item">
          <label>聚类方法</label>
          <el-select v-model="speakerConfig.clustering_method">
            <el-option label="K-Means" value="kmeans" />
            <el-option label="层次聚类" value="hierarchical" />
            <el-option label="DBSCAN" value="dbscan" />
            <el-option label="自动检测" value="auto" />
          </el-select>
        </div>
        
        <div class="config-item">
          <label>预期说话人数</label>
          <el-input-number
            v-model="speakerConfig.expected_speakers"
            :min="1"
            :max="10"
            :step="1"
            size="small"
          />
        </div>
        
        <div class="config-item">
          <label>相似度阈值</label>
          <el-slider
            v-model="speakerConfig.similarity_threshold"
            :min="0.1"
            :max="1.0"
            :step="0.05"
            :format-tooltip="(val) => val.toFixed(2)"
            show-input
          />
        </div>
        
        <div class="config-item">
          <el-checkbox v-model="speakerConfig.enable_gender_detection">
            启用性别检测
          </el-checkbox>
        </div>
      </div>
    </div>

    <!-- 音频预处理配置 -->
    <div v-if="showPreprocessConfig" class="config-group">
      <div class="group-header">
        <h4>🔧 音频预处理配置</h4>
      </div>
      <div class="config-items">
        <div class="config-item">
          <label>目标采样率 (Hz)</label>
          <el-select v-model="preprocessConfig.target_sr">
            <el-option label="16000 Hz" :value="16000" />
            <el-option label="22050 Hz" :value="22050" />
            <el-option label="44100 Hz" :value="44100" />
            <el-option label="48000 Hz" :value="48000" />
          </el-select>
        </div>
        
        <div class="config-item">
          <label>目标声道数</label>
          <el-radio-group v-model="preprocessConfig.target_channels">
            <el-radio :label="1">单声道</el-radio>
            <el-radio :label="2">立体声</el-radio>
          </el-radio-group>
        </div>
        
        <div class="config-item">
          <el-checkbox v-model="preprocessConfig.normalize">
            音量标准化
          </el-checkbox>
        </div>
        
        <div class="config-item" v-if="preprocessConfig.normalize">
          <label>目标音量 (dB)</label>
          <el-slider
            v-model="preprocessConfig.target_db"
            :min="-40"
            :max="0"
            :step="1"
            :format-tooltip="(val) => `${val} dB`"
            show-input
          />
        </div>
        
        <div class="config-item">
          <el-checkbox v-model="preprocessConfig.denoise">
            音频降噪
          </el-checkbox>
        </div>
        
        <div class="config-item" v-if="preprocessConfig.denoise">
          <label>降噪方法</label>
          <el-select v-model="preprocessConfig.denoise_method">
            <el-option label="频谱门控" value="spectral_gating" />
            <el-option label="维纳滤波" value="wiener" />
            <el-option label="简单滤波" value="simple" />
          </el-select>
        </div>
      </div>
    </div>

    <!-- 会议转录配置 -->
    <div v-if="showMeetingConfig" class="config-group">
      <div class="group-header">
        <h4>🎤 会议转录配置</h4>
      </div>
      <div class="config-items">
        <div class="config-item">
          <label>输出格式</label>
          <el-select v-model="meetingConfig.output_format">
            <el-option label="按时间排序" value="timeline" />
            <el-option label="按说话人分组" value="speaker_grouped" />
            <el-option label="对话格式" value="dialogue" />
          </el-select>
        </div>
        
        <div class="config-item">
          <el-checkbox v-model="meetingConfig.include_timestamps">
            包含时间戳
          </el-checkbox>
        </div>
        
        <div class="config-item">
          <el-checkbox v-model="meetingConfig.include_confidence">
            包含置信度分数
          </el-checkbox>
        </div>
        
        <div class="config-item">
          <el-checkbox v-model="meetingConfig.speaker_labeling">
            说话人标注
          </el-checkbox>
        </div>

        <!-- 说话人识别配置分组 -->
        <div v-if="meetingConfig.speaker_labeling" class="config-subgroup">
          <div class="subgroup-header">
            <h5>👥 说话人识别设置</h5>
          </div>

          <div class="config-item">
            <label>预期说话人数量</label>
            <el-input-number
              v-model="meetingConfig.expected_speakers"
              :min="1"
              :max="10"
              :step="1"
              size="small"
              style="width: 120px"
            />
            <el-tooltip content="设置会议中预期的说话人数量，有助于提高识别准确性" placement="top">
              <el-icon class="help-icon"><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>

          <div class="config-item">
            <label>聚类方法</label>
            <el-select v-model="meetingConfig.clustering_method" size="small">
              <el-option label="自动选择" value="auto" />
              <el-option label="层次聚类" value="agglomerative" />
              <el-option label="DBSCAN" value="dbscan" />
            </el-select>
            <el-tooltip content="选择说话人聚类算法，自动选择适合大多数场景" placement="top">
              <el-icon class="help-icon"><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>

          <div class="config-item">
            <label>聚类阈值</label>
            <el-slider
              v-model="meetingConfig.similarity_threshold"
              :min="0.1"
              :max="0.8"
              :step="0.05"
              :format-tooltip="formatThresholdTooltip"
              style="width: 150px; margin-right: 10px"
            />
            <span class="threshold-value">{{ meetingConfig.similarity_threshold }}</span>
            <el-tooltip content="调整说话人相似度阈值，值越小越容易区分不同说话人" placement="top">
              <el-icon class="help-icon"><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>

          <!-- 场景预设 -->
          <div class="config-item">
            <label>场景预设</label>
            <el-select
              v-model="selectedSpeakerPreset"
              @change="applySpeakerPreset"
              size="small"
              placeholder="选择场景预设"
            >
              <el-option label="两人对话" value="two_person" />
              <el-option label="小组讨论(3-5人)" value="small_group" />
              <el-option label="大型会议(6-10人)" value="large_meeting" />
              <el-option label="自定义" value="custom" />
            </el-select>
            <el-tooltip content="根据会议场景快速应用推荐配置" placement="top">
              <el-icon class="help-icon"><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>

          <!-- 智能推荐 -->
          <div class="config-item">
            <el-button
              @click="applySmartRecommendation"
              size="small"
              type="primary"
              :icon="Setting"
            >
              智能推荐配置
            </el-button>
            <el-tooltip content="基于当前说话人数量自动推荐最佳配置" placement="top">
              <el-icon class="help-icon"><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>

          <!-- 配置建议显示 -->
          <div v-if="showConfigRecommendation" class="config-recommendation">
            <el-alert
              :title="currentRecommendation.description"
              type="info"
              :closable="false"
              show-icon
            >
              <template #default>
                <div class="recommendation-details">
                  <p><strong>推荐阈值:</strong> {{ currentRecommendation.similarity_threshold }}</p>
                  <p><strong>推荐算法:</strong> {{ getClusteringMethodName(currentRecommendation.clustering_method) }}</p>
                </div>
              </template>
            </el-alert>
          </div>
        </div>
      </div>
    </div>

    <!-- 配置操作 -->
    <div class="config-actions">
      <el-button @click="resetToDefaults" size="small">
        🔄 重置默认
      </el-button>
      <el-button @click="saveAsPreset" size="small" type="primary">
        💾 保存预设
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { QuestionFilled, Setting } from '@element-plus/icons-vue'

// Props
const props = defineProps({
  processingMode: {
    type: String,
    default: 'speech_recognition'
  },
  audioConfig: {
    type: Object,
    required: true
  },
  recordingConfig: {
    type: Object,
    required: true
  },
  recordingStatus: {
    type: String,
    default: 'idle'
  },
  recordingDuration: {
    type: Number,
    default: 0
  },
  recordedAudioUrl: {
    type: String,
    default: ''
  }
})

// Emits
const emit = defineEmits([
  'config-change',
  'processing-mode-change',
  'recording-start',
  'recording-pause',
  'recording-resume',
  'recording-stop',
  'recording-save',
  'recording-discard',
  'apply-optimal-config'
])

// 事件处理函数
const handleConfigChange = (key, value) => {
  emit('config-change', { key, value })
}

const handleProcessingModeChange = (mode) => {
  emit('processing-mode-change', mode)
}

// 预设配置
const vadPresets = [
  { value: 'meeting_optimized', label: '会议优化' },
  { value: 'fast_dialogue', label: '快速对话' },
  { value: 'long_dialogue', label: '长对话' },
  { value: 'noisy_environment', label: '噪音环境' }
]

const languages = [
  { value: 'auto', label: '自动检测' },
  { value: 'zh', label: '中文' },
  { value: 'en', label: '英文' },
  { value: 'yue', label: '粤语' },
  { value: 'ja', label: '日语' },
  { value: 'ko', label: '韩语' }
]

// 说话人识别相关数据
const selectedSpeakerPreset = ref('custom')
const showConfigRecommendation = ref(false)
const currentRecommendation = ref({})

// 说话人场景预设配置
const speakerPresets = {
  two_person: {
    expected_speakers: 2,
    similarity_threshold: 0.15,
    clustering_method: 'agglomerative'
  },
  small_group: {
    expected_speakers: 4,
    similarity_threshold: 0.20,
    clustering_method: 'agglomerative'
  },
  large_meeting: {
    expected_speakers: 8,
    similarity_threshold: 0.25,
    clustering_method: 'auto'
  },
  custom: {
    expected_speakers: 2,
    similarity_threshold: 0.15,
    clustering_method: 'auto'
  }
}

// 计算属性 - 根据处理模式显示相应配置
const showVadConfig = computed(() => 
  ['vad_detection', 'meeting_transcription', 'comprehensive_analysis'].includes(props.processingMode)
)

const showSpeechConfig = computed(() => 
  ['speech_recognition', 'meeting_transcription', 'comprehensive_analysis'].includes(props.processingMode)
)

const showSpeakerConfig = computed(() => 
  ['speaker_recognition', 'meeting_transcription', 'comprehensive_analysis'].includes(props.processingMode)
)

const showPreprocessConfig = computed(() => 
  ['audio_preprocessing', 'comprehensive_analysis'].includes(props.processingMode)
)

const showMeetingConfig = computed(() => 
  props.processingMode === 'meeting_transcription'
)

// 方法
const applyVadPreset = (preset) => {
  const presets = {
    meeting_optimized: {
      merge_length_s: 15,
      min_speech_duration: 0.5,
      threshold: 0.5,
      optimize_two_person: true
    },
    fast_dialogue: {
      merge_length_s: 5,
      min_speech_duration: 0.3,
      threshold: 0.4,
      optimize_two_person: true
    },
    long_dialogue: {
      merge_length_s: 30,
      min_speech_duration: 1.0,
      threshold: 0.6,
      optimize_two_person: false
    },
    noisy_environment: {
      merge_length_s: 10,
      min_speech_duration: 0.4,
      threshold: 0.7,
      optimize_two_person: false
    }
  }
  
  if (presets[preset]) {
    Object.assign(vadConfig.value, presets[preset])
  }
}

// 应用说话人场景预设
const applySpeakerPreset = (preset) => {
  if (speakerPresets[preset]) {
    Object.assign(meetingConfig.value, speakerPresets[preset])
    ElMessage.success(`已应用${preset === 'two_person' ? '两人对话' : preset === 'small_group' ? '小组讨论' : preset === 'large_meeting' ? '大型会议' : '自定义'}场景配置`)
  }
}

// 格式化阈值提示
const formatThresholdTooltip = (value) => {
  if (value <= 0.15) return `${value} (容易区分)`
  if (value <= 0.25) return `${value} (适中)`
  if (value <= 0.4) return `${value} (较难区分)`
  return `${value} (很难区分)`
}

// 配置验证
const validateSpeakerConfig = () => {
  const errors = []

  // 验证说话人数量
  if (meetingConfig.value.expected_speakers < 1 || meetingConfig.value.expected_speakers > 10) {
    errors.push('说话人数量应在1-10之间')
  }

  // 验证聚类阈值
  if (meetingConfig.value.similarity_threshold < 0.1 || meetingConfig.value.similarity_threshold > 0.8) {
    errors.push('聚类阈值应在0.1-0.8之间')
  }

  // 验证配置组合合理性
  if (meetingConfig.value.expected_speakers > 5 && meetingConfig.value.similarity_threshold < 0.2) {
    errors.push('大型会议(>5人)建议使用较高的聚类阈值(≥0.2)')
  }

  if (meetingConfig.value.expected_speakers <= 2 && meetingConfig.value.similarity_threshold > 0.3) {
    errors.push('小型对话(≤2人)建议使用较低的聚类阈值(≤0.3)')
  }

  return errors
}

// 智能配置推荐
const getConfigRecommendation = () => {
  const speakers = meetingConfig.value.expected_speakers

  if (speakers <= 2) {
    return {
      similarity_threshold: 0.15,
      clustering_method: 'agglomerative',
      description: '两人对话推荐配置：低阈值，层次聚类'
    }
  } else if (speakers <= 5) {
    return {
      similarity_threshold: 0.20,
      clustering_method: 'agglomerative',
      description: '小组讨论推荐配置：中等阈值，层次聚类'
    }
  } else {
    return {
      similarity_threshold: 0.25,
      clustering_method: 'auto',
      description: '大型会议推荐配置：高阈值，自动选择算法'
    }
  }
}

// 应用智能推荐配置
const applySmartRecommendation = () => {
  const recommendation = getConfigRecommendation()

  // 应用推荐配置
  meetingConfig.value.similarity_threshold = recommendation.similarity_threshold
  meetingConfig.value.clustering_method = recommendation.clustering_method

  // 显示推荐信息
  currentRecommendation.value = recommendation
  showConfigRecommendation.value = true

  // 3秒后自动隐藏推荐信息
  setTimeout(() => {
    showConfigRecommendation.value = false
  }, 3000)

  ElMessage.success('已应用智能推荐配置')
}

// 获取聚类方法显示名称
const getClusteringMethodName = (method) => {
  const methodNames = {
    'auto': '自动选择',
    'agglomerative': '层次聚类',
    'dbscan': 'DBSCAN聚类'
  }
  return methodNames[method] || method
}

const resetToDefaults = () => {
  vadConfig.value = {
    preset: 'meeting_optimized',
    merge_length_s: 15,
    min_speech_duration: 0.5,
    max_speech_duration: 60,
    threshold: 0.5,
    optimize_two_person: true
  }
  
  speechConfig.value = {
    language: 'auto',
    use_itn: true,
    ban_emo_unk: false,
    merge_vad: true,
    merge_length_s: 15
  }
  
  speakerConfig.value = {
    clustering_method: 'auto',
    expected_speakers: 2,
    similarity_threshold: 0.7,
    enable_gender_detection: false
  }
  
  preprocessConfig.value = {
    target_sr: 16000,
    target_channels: 1,
    normalize: true,
    target_db: -20,
    denoise: true,
    denoise_method: 'spectral_gating'
  }
  
  meetingConfig.value = {
    output_format: 'timeline',
    include_timestamps: true,
    include_confidence: false,
    speaker_labeling: true,
    // 说话人识别配置
    expected_speakers: 2,
    similarity_threshold: 0.15,
    clustering_method: 'auto'
  }

  // 重置说话人预设选择
  selectedSpeakerPreset.value = 'custom'
  
  ElMessage.success('配置已重置为默认值')
}

const saveAsPreset = () => {
  // 保存当前配置为预设
  ElMessage.success('配置预设已保存')
}

// 监听配置变化
const updateConfig = () => {
  // 验证会议转录配置
  if (props.processingMode === 'meeting_transcription') {
    const validationErrors = validateSpeakerConfig()
    if (validationErrors.length > 0) {
      console.warn('配置验证警告:', validationErrors)
      // 可以选择显示警告消息，但不阻止配置更新
      // ElMessage.warning(validationErrors[0])
    }
  }

  const config = {
    vad: vadConfig.value,
    speech: speechConfig.value,
    speaker: speakerConfig.value,
    preprocess: preprocessConfig.value,
    meeting: meetingConfig.value
  }

  emit('update:modelValue', config)
  emit('config-changed', config)
}

// 监听所有配置变化
watch([vadConfig, speechConfig, speakerConfig, preprocessConfig, meetingConfig], updateConfig, { deep: true })

// 初始化配置
updateConfig()
</script>

<style scoped>
.audio-config-panel {
  width: 100%;
}

.config-group {
  margin-bottom: var(--spacing-lg);
  border: 1px solid var(--border-color);
  border-radius: var(--radius-md);
  background: var(--surface-bg);
}

.group-header {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background: var(--card-bg);
}

.group-header h4 {
  margin: 0;
  color: var(--text-primary);
  font-size: 1rem;
}

.config-items {
  padding: var(--spacing-lg);
}

.config-item {
  margin-bottom: var(--spacing-md);
}

.config-item:last-child {
  margin-bottom: 0;
}

.config-item label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: 500;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.config-item .el-select,
.config-item .el-input-number {
  width: 100%;
}

.config-item .el-slider {
  margin: var(--spacing-sm) 0;
}

.config-actions {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-md) var(--spacing-lg);
  border-top: 1px solid var(--border-color);
  background: var(--card-bg);
}

.config-actions .el-button {
  flex: 1;
}

/* 子配置组样式 */
.config-subgroup {
  margin-top: var(--spacing-md);
  padding: var(--spacing-md);
  border: 1px solid var(--border-color-light);
  border-radius: var(--radius-sm);
  background: var(--surface-bg-light);
}

.subgroup-header {
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color-light);
}

.subgroup-header h5 {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
  font-weight: 500;
}

/* 帮助图标样式 */
.help-icon {
  margin-left: var(--spacing-sm);
  color: var(--text-placeholder);
  cursor: help;
  font-size: 14px;
}

.help-icon:hover {
  color: var(--primary-color);
}

/* 阈值显示样式 */
.threshold-value {
  display: inline-block;
  min-width: 40px;
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.9rem;
}

/* 配置项内联布局 */
.config-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.config-item label {
  flex-shrink: 0;
  min-width: 120px;
  margin-bottom: 0;
}

/* 配置推荐样式 */
.config-recommendation {
  margin-top: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.recommendation-details {
  margin-top: var(--spacing-sm);
}

.recommendation-details p {
  margin: var(--spacing-xs) 0;
  font-size: 0.9rem;
}

.recommendation-details strong {
  color: var(--color-primary);
}
</style>
