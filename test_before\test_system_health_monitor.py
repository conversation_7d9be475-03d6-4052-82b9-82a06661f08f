#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统健康监控器测试脚本
测试所有监控功能、告警生成和自动恢复机制
"""

import sys
import time
import json
import unittest
from pathlib import Path
from datetime import datetime, timedelta

# 导入待测试模块
try:
    from utils.system_health_monitor import (
        SystemHealthMonitor,
        SystemHealthChecker,
        MemoryHealthChecker,
        GPUHealthChecker,
        ProcessHealthChecker,
        ModelHealthChecker,
        AutoRecoveryManager,
        HealthStatus,
        ComponentType,
        start_system_monitoring,
        stop_system_monitoring,
        get_system_status,
        get_system_alerts,
        run_health_check_once
    )
    HEALTH_MONITOR_AVAILABLE = True
    print("✅ 成功导入系统健康监控模块")
except ImportError as e:
    print(f"❌ 导入系统健康监控模块失败: {e}")
    HEALTH_MONITOR_AVAILABLE = False

def test_basic_functionality():
    """测试基本功能"""
    print("\n🔍 测试1: 基本功能验证")
    
    if not HEALTH_MONITOR_AVAILABLE:
        print("❌ 健康监控模块不可用，跳过测试")
        return False
    
    try:
        # 测试健康检查器创建
        system_checker = SystemHealthChecker()
        memory_checker = MemoryHealthChecker()
        gpu_checker = GPUHealthChecker()
        process_checker = ProcessHealthChecker()
        model_checker = ModelHealthChecker()
        print("✅ 所有健康检查器创建成功")
        
        # 测试恢复管理器
        recovery_manager = AutoRecoveryManager()
        print("✅ 自动恢复管理器创建成功")
        
        # 测试监控器主类
        monitor = SystemHealthMonitor(check_interval=30)
        print("✅ 系统健康监控器创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

def test_health_checkers():
    """测试各个健康检查器"""
    print("\n🔍 测试2: 健康检查器功能")
    
    if not HEALTH_MONITOR_AVAILABLE:
        print("❌ 健康监控模块不可用，跳过测试")
        return False
    
    try:
        checkers = [
            SystemHealthChecker(),
            MemoryHealthChecker(),
            GPUHealthChecker(),
            ProcessHealthChecker(),
            ModelHealthChecker()
        ]
        
        results = []
        for checker in checkers:
            print(f"📊 测试 {checker.name} 检查器...")
            metric = checker.check_health()
            
            # 验证返回的指标
            assert hasattr(metric, 'timestamp'), f"{checker.name}: 缺少时间戳"
            assert hasattr(metric, 'status'), f"{checker.name}: 缺少状态"
            assert hasattr(metric, 'value'), f"{checker.name}: 缺少数值"
            assert hasattr(metric, 'message'), f"{checker.name}: 缺少消息"
            
            print(f"   状态: {metric.status.value}")
            print(f"   数值: {metric.value} {metric.unit}")
            print(f"   消息: {metric.message}")
            
            results.append(metric)
        
        print(f"✅ 所有{len(checkers)}个检查器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 健康检查器测试失败: {e}")
        return False

def test_monitoring_lifecycle():
    """测试监控生命周期"""
    print("\n🔍 测试3: 监控生命周期")
    
    if not HEALTH_MONITOR_AVAILABLE:
        print("❌ 健康监控模块不可用，跳过测试")
        return False
    
    try:
        # 测试启动监控
        print("🚀 启动系统监控...")
        start_system_monitoring(check_interval=10)  # 10秒间隔用于测试
        time.sleep(2)  # 等待启动
        
        # 检查监控状态
        from utils.system_health_monitor import system_health_monitor
        if system_health_monitor.running:
            print("✅ 监控已成功启动")
        else:
            print("❌ 监控启动失败")
            return False
        
        # 运行单次健康检查
        print("🔍 运行单次健康检查...")
        metrics = run_health_check_once()
        print(f"✅ 检查了{len(metrics)}个组件")
        
        # 获取系统状态
        print("📊 获取系统状态...")
        status = get_system_status()
        if status:
            print(f"   整体状态: {status.get('overall_status', 'unknown')}")
            print(f"   活跃告警: {status.get('active_alerts_count', 0)}")
            print(f"   组件数量: {len(status.get('components', {}))}")
            print("✅ 系统状态获取成功")
        else:
            print("❌ 系统状态获取失败")
        
        # 等待一个监控周期
        print("⏳ 等待监控周期完成...")
        time.sleep(12)  # 等待超过检查间隔
        
        # 停止监控
        print("⏹️ 停止系统监控...")
        stop_system_monitoring()
        time.sleep(2)  # 等待停止
        
        if not system_health_monitor.running:
            print("✅ 监控已成功停止")
        else:
            print("❌ 监控停止失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 监控生命周期测试失败: {e}")
        return False

def test_alert_system():
    """测试告警系统"""
    print("\n🔍 测试4: 告警系统")
    
    if not HEALTH_MONITOR_AVAILABLE:
        print("❌ 健康监控模块不可用，跳过测试")
        return False
    
    try:
        from utils.system_health_monitor import system_health_monitor
        
        # 清除现有告警
        system_health_monitor.clear_alerts()
        print("🗑️ 清除现有告警")
        
        # 获取告警列表
        alerts = get_system_alerts(include_resolved=False)
        print(f"📋 当前活跃告警数: {len(alerts)}")
        
        # 启动监控以生成告警
        start_system_monitoring(check_interval=5)
        print("🚀 启动监控以生成告警...")
        
        # 等待可能生成告警
        time.sleep(10)
        
        # 检查告警
        alerts = get_system_alerts(include_resolved=False)
        resolved_alerts = get_system_alerts(include_resolved=True)
        total_alerts = len([a for a in resolved_alerts if not a.get('resolved', True)])
        
        print(f"📊 告警统计:")
        print(f"   活跃告警: {len(alerts)}")
        print(f"   历史告警: {len(resolved_alerts)}")
        
        # 显示告警详情
        if alerts:
            print("🚨 活跃告警详情:")
            for alert in alerts[:3]:  # 显示前3个
                print(f"   - {alert['component_name']}: {alert['message']}")
                print(f"     严重程度: {alert.get('severity', 'UNKNOWN')}")
                print(f"     恢复尝试: {alert.get('auto_recovery_attempted', False)}")
        
        stop_system_monitoring()
        print("✅ 告警系统测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 告警系统测试失败: {e}")
        return False

def test_configuration():
    """测试配置管理"""
    print("\n🔍 测试5: 配置管理")
    
    if not HEALTH_MONITOR_AVAILABLE:
        print("❌ 健康监控模块不可用，跳过测试")
        return False
    
    try:
        from utils.system_health_monitor import system_health_monitor
        
        # 获取默认配置
        original_config = system_health_monitor.config.copy()
        print("📋 获取原始配置")
        
        # 修改配置
        test_config = {
            "check_interval": 30,
            "alert_thresholds": {
                "memory_warning": 75,
                "memory_critical": 90,
                "cpu_warning": 85,
                "cpu_critical": 95
            },
            "auto_recovery": {
                "enabled": True,
                "max_attempts": 5,
                "cooldown_seconds": 180
            }
        }
        
        system_health_monitor.config.update(test_config)
        system_health_monitor.save_config()
        print("💾 保存测试配置")
        
        # 重新加载配置验证
        system_health_monitor.load_config()
        loaded_config = system_health_monitor.config
        
        # 验证配置
        assert loaded_config["check_interval"] == 30, "检查间隔配置错误"
        assert loaded_config["alert_thresholds"]["memory_warning"] == 75, "内存警告阈值配置错误"
        assert loaded_config["auto_recovery"]["max_attempts"] == 5, "最大恢复尝试次数配置错误"
        
        print("✅ 配置验证通过")
        
        # 恢复原始配置
        system_health_monitor.config = original_config
        system_health_monitor.save_config()
        print("🔄 恢复原始配置")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置管理测试失败: {e}")
        return False

def test_recovery_system():
    """测试自动恢复系统"""
    print("\n🔍 测试6: 自动恢复系统")
    
    if not HEALTH_MONITOR_AVAILABLE:
        print("❌ 健康监控模块不可用，跳过测试")
        return False
    
    try:
        from utils.system_health_monitor import AutoRecoveryManager
        from utils.exception_handler import ErrorSeverity
        
        # 创建恢复管理器
        recovery_manager = AutoRecoveryManager()
        print("🔧 创建恢复管理器")
        
        # 创建模拟告警
        from utils.system_health_monitor import SystemAlert
        import uuid
        
        test_alert = SystemAlert(
            id=str(uuid.uuid4()),
            timestamp=datetime.now(),
            component_type=ComponentType.MEMORY,
            component_name="Memory",
            severity=ErrorSeverity.MEDIUM,
            status=HealthStatus.WARNING,
            message="内存使用率过高",
            details={"memory_percent": 85}
        )
        
        print("🚨 创建测试告警")
        
        # 测试恢复操作
        print("🔄 测试内存恢复...")
        recovery_success = recovery_manager.attempt_recovery(test_alert)
        print(f"   恢复结果: {'成功' if recovery_success else '失败'}")
        
        # 测试恢复冷却
        print("❄️ 测试恢复冷却机制...")
        immediate_retry = recovery_manager.attempt_recovery(test_alert)
        print(f"   立即重试结果: {'成功' if immediate_retry else '被冷却阻止'}")
        
        print("✅ 自动恢复系统测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 自动恢复系统测试失败: {e}")
        return False

def test_data_persistence():
    """测试数据持久化"""
    print("\n🔍 测试7: 数据持久化")
    
    if not HEALTH_MONITOR_AVAILABLE:
        print("❌ 健康监控模块不可用，跳过测试")
        return False
    
    try:
        from utils.system_health_monitor import system_health_monitor
        
        # 确保日志目录存在
        log_dir = Path("logs/health_monitor")
        log_dir.mkdir(parents=True, exist_ok=True)
        print("📁 创建日志目录")
        
        # 运行健康检查并保存
        metrics = run_health_check_once()
        print(f"📊 运行健康检查，获得{len(metrics)}个指标")
        
        # 检查指标文件
        today = datetime.now().strftime('%Y%m%d')
        metrics_file = log_dir / f"metrics_{today}.jsonl"
        
        if metrics_file.exists():
            with open(metrics_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            print(f"📄 指标文件存在，包含{len(lines)}行数据")
            
            # 验证数据格式
            if lines:
                try:
                    data = json.loads(lines[-1])
                    assert 'timestamp' in data, "缺少时间戳字段"
                    assert 'component_name' in data, "缺少组件名称字段"
                    assert 'status' in data, "缺少状态字段"
                    print("✅ 数据格式验证通过")
                except json.JSONDecodeError:
                    print("❌ 数据格式验证失败")
                    return False
        else:
            print("⚠️ 指标文件未创建")
        
        # 测试配置文件持久化
        config_file = Path("config/health_monitor_config.json")
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            print("📄 配置文件存在并可读取")
            
            # 验证配置结构
            required_keys = ["check_interval", "alert_thresholds", "auto_recovery"]
            for key in required_keys:
                assert key in config_data, f"配置缺少必需键: {key}"
            print("✅ 配置结构验证通过")
        else:
            print("⚠️ 配置文件不存在")
        
        print("✅ 数据持久化测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据持久化测试失败: {e}")
        return False

def generate_test_report():
    """生成测试报告"""
    print("\n📋 生成测试报告")
    
    test_functions = [
        ("基本功能验证", test_basic_functionality),
        ("健康检查器功能", test_health_checkers),
        ("监控生命周期", test_monitoring_lifecycle),
        ("告警系统", test_alert_system),
        ("配置管理", test_configuration),
        ("自动恢复系统", test_recovery_system),
        ("数据持久化", test_data_persistence)
    ]
    
    results = []
    passed = 0
    failed = 0
    
    print("=" * 60)
    print("🧪 系统健康监控器测试报告")
    print("=" * 60)
    
    for test_name, test_func in test_functions:
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
            failed += 1
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试总结")
    print("=" * 60)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{status}: {test_name}")
    
    print(f"\n🎯 总计: {len(test_functions)} 个测试")
    print(f"✅ 通过: {passed} 个")
    print(f"❌ 失败: {failed} 个")
    print(f"📈 通过率: {(passed/len(test_functions)*100):.1f}%")
    
    # 保存报告
    report_data = {
        "test_time": datetime.now().isoformat(),
        "total_tests": len(test_functions),
        "passed": passed,
        "failed": failed,
        "pass_rate": passed/len(test_functions)*100,
        "results": [{"name": name, "passed": result} for name, result in results],
        "system_info": {
            "python_version": sys.version,
            "platform": sys.platform
        }
    }
    
    report_file = Path(f"logs/health_monitor_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
    report_file.parent.mkdir(parents=True, exist_ok=True)
    
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report_data, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 测试报告已保存: {report_file}")
    
    return passed == len(test_functions)

def main():
    """主函数"""
    print("🏥 系统健康监控器测试脚本")
    print("=" * 60)
    
    if not HEALTH_MONITOR_AVAILABLE:
        print("❌ 系统健康监控模块不可用，无法进行测试")
        return False
    
    # 运行所有测试
    success = generate_test_report()
    
    if success:
        print("\n🎉 所有测试通过！系统健康监控功能正常。")
    else:
        print("\n⚠️ 部分测试失败，请检查相关功能。")
    
    return success

if __name__ == "__main__":
    main() 