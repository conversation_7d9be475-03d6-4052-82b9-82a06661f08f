#!/usr/bin/env python3
"""
前端API集成测试
验证前端与后端API的集成情况
"""

import requests
import json
import time
import tempfile
import os
from pathlib import Path

# 测试配置
FRONTEND_URL = "http://localhost:3000"
BACKEND_URL = "http://localhost:8002"
API_BASE = f"{BACKEND_URL}/api/v1"

# 测试用户认证信息
TEST_USER = {
    "username": "admin",
    "password": "admin123"
}

def get_auth_token():
    """获取认证token"""
    try:
        response = requests.post(
            f"{API_BASE}/auth/login",
            json=TEST_USER,
            timeout=5
        )
        if response.status_code == 200:
            result = response.json()
            return result.get('access_token')
        else:
            print(f"❌ 登录失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def get_auth_headers():
    """获取认证头"""
    token = get_auth_token()
    if token:
        return {"Authorization": f"Bearer {token}"}
    return {}

def create_test_audio_file():
    """创建测试音频文件"""
    import wave
    import numpy as np
    
    # 创建临时音频文件
    temp_file = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
    
    # 生成1秒的测试音频数据
    sample_rate = 16000
    duration = 1.0
    frequency = 440  # A4音符
    
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    audio_data = np.sin(frequency * 2 * np.pi * t)
    audio_data = (audio_data * 32767).astype(np.int16)
    
    # 写入WAV文件
    with wave.open(temp_file.name, 'w') as wav_file:
        wav_file.setnchannels(1)  # 单声道
        wav_file.setsampwidth(2)  # 16位
        wav_file.setframerate(sample_rate)
        wav_file.writeframes(audio_data.tobytes())
    
    return temp_file.name

def test_backend_health():
    """测试后端健康检查"""
    print("\n=== 测试后端健康检查 ===")
    try:
        response = requests.get(f"{BACKEND_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务正常")
            return True
        else:
            print(f"❌ 后端服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 后端连接失败: {e}")
        return False

def test_frontend_access():
    """测试前端访问"""
    print("\n=== 测试前端访问 ===")
    try:
        response = requests.get(FRONTEND_URL, timeout=5)
        if response.status_code == 200:
            print("✅ 前端服务正常")
            return True
        else:
            print(f"❌ 前端服务异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 前端连接失败: {e}")
        return False

def test_audio_upload_api():
    """测试音频上传API"""
    print("\n=== 测试音频上传API ===")
    try:
        # 获取认证头
        headers = get_auth_headers()
        if not headers:
            print("❌ 无法获取认证token，跳过上传测试")
            return None

        # 创建测试音频文件
        audio_file = create_test_audio_file()

        # 准备上传数据
        with open(audio_file, 'rb') as f:
            files = {'file': ('test.wav', f, 'audio/wav')}

            # 发送上传请求
            response = requests.post(
                f"{API_BASE}/audio/upload",
                files=files,
                headers=headers,
                timeout=10
            )
        
        # 清理临时文件
        os.unlink(audio_file)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 音频上传成功: {result.get('file_id', 'Unknown')}")
            return result.get('file_id')
        else:
            print(f"❌ 音频上传失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 音频上传异常: {e}")
        return None

def test_audio_list_api():
    """测试音频列表API"""
    print("\n=== 测试音频列表API ===")
    try:
        headers = get_auth_headers()
        response = requests.get(f"{API_BASE}/audio/", headers=headers, timeout=5)
        
        if response.status_code == 200:
            result = response.json()
            file_count = len(result.get('files', []))
            print(f"✅ 获取音频列表成功: {file_count} 个文件")
            return True
        else:
            print(f"❌ 获取音频列表失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 音频列表API异常: {e}")
        return False

def test_speech_processing_api(file_id):
    """测试语音处理API"""
    print("\n=== 测试语音处理API ===")
    if not file_id:
        print("❌ 没有有效的文件ID，跳过语音处理测试")
        return False
    
    try:
        # 测试VAD检测
        vad_data = {
            "file_ids": [file_id],
            "config": {}
        }
        
        headers = get_auth_headers()
        response = requests.post(
            f"{API_BASE}/speech/vad-detection",
            json=vad_data,
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            task_id = result.get('task_id')
            print(f"✅ VAD检测任务创建成功: {task_id}")
            return task_id
        else:
            print(f"❌ VAD检测任务创建失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 语音处理API异常: {e}")
        return None

def test_task_status_api(task_id):
    """测试任务状态API"""
    print("\n=== 测试任务状态API ===")
    if not task_id:
        print("❌ 没有有效的任务ID，跳过任务状态测试")
        return False
    
    try:
        headers = get_auth_headers()
        response = requests.get(f"{API_BASE}/speech/task/{task_id}", headers=headers, timeout=5)
        
        if response.status_code == 200:
            result = response.json()
            state = result.get('state', 'Unknown')
            print(f"✅ 任务状态查询成功: {state}")
            return True
        else:
            print(f"❌ 任务状态查询失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 任务状态API异常: {e}")
        return False

def cleanup_test_data(file_id, task_id):
    """清理测试数据"""
    print("\n=== 清理测试数据 ===")
    
    # 清理文件
    if file_id:
        try:
            response = requests.delete(f"{API_BASE}/audio/{file_id}", timeout=5)
            if response.status_code == 200:
                print(f"✅ 清理文件成功: {file_id}")
            else:
                print(f"⚠️ 清理文件失败: {response.status_code}")
        except Exception as e:
            print(f"⚠️ 清理文件异常: {e}")
    
    # 清理任务
    if task_id:
        try:
            response = requests.delete(f"{API_BASE}/speech/task/{task_id}", timeout=5)
            if response.status_code == 200:
                print(f"✅ 清理任务成功: {task_id}")
            else:
                print(f"⚠️ 清理任务失败: {response.status_code}")
        except Exception as e:
            print(f"⚠️ 清理任务异常: {e}")

def main():
    """主测试函数"""
    print("🧪 前端API集成测试")
    print("=" * 50)
    
    results = []
    file_id = None
    task_id = None
    
    # 1. 测试后端健康检查
    results.append(("后端健康检查", test_backend_health()))
    
    # 2. 测试前端访问
    results.append(("前端访问", test_frontend_access()))
    
    # 3. 测试音频上传API
    file_id = test_audio_upload_api()
    results.append(("音频上传API", file_id is not None))
    
    # 4. 测试音频列表API
    results.append(("音频列表API", test_audio_list_api()))
    
    # 5. 测试语音处理API
    task_id = test_speech_processing_api(file_id)
    results.append(("语音处理API", task_id is not None))
    
    # 6. 测试任务状态API
    results.append(("任务状态API", test_task_status_api(task_id)))
    
    # 清理测试数据
    cleanup_test_data(file_id, task_id)
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！前端API集成正常")
    else:
        print("⚠️ 部分测试失败，请检查系统配置")
    
    return passed == total

if __name__ == "__main__":
    main()
