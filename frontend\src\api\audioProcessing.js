/**
 * 音频处理API模块
 * 提供音频处理相关的API接口调用
 */

import request from '@/utils/request'

// API基础路径
const AUDIO_API_BASE = '/api/v1/audio'
const SPEECH_API_BASE = '/api/v1/speech'

/**
 * 音频文件管理API
 */
export const audioFileAPI = {
  /**
   * 获取音频文件列表
   */
  getAudioFiles: () => {
    return request({
      url: `${AUDIO_API_BASE}/`,
      method: 'get'
    })
  },

  /**
   * 获取用户音频文件列表
   */
  getUserFiles: () => {
    return request({
      url: `${AUDIO_API_BASE}/user/files`,
      method: 'get'
    })
  },

  /**
   * 上传单个音频文件
   */
  uploadAudio: (file, onProgress) => {
    const formData = new FormData()
    formData.append('file', file)

    return request({
      url: `${AUDIO_API_BASE}/upload`,
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress) {
          const percentCompleted = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          )
          onProgress(percentCompleted)
        }
      }
    })
  },

  /**
   * 批量上传音频文件
   */
  uploadAudioBatch: (files, onProgress) => {
    const formData = new FormData()
    files.forEach(file => {
      formData.append('files', file)
    })

    return request({
      url: `${AUDIO_API_BASE}/upload/batch`,
      method: 'post',
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      onUploadProgress: (progressEvent) => {
        if (onProgress) {
          const percentCompleted = Math.round(
            (progressEvent.loaded * 100) / progressEvent.total
          )
          onProgress(percentCompleted)
        }
      }
    })
  },

  /**
   * 获取音频文件信息
   */
  getAudioFile: (fileId) => {
    return request({
      url: `${AUDIO_API_BASE}/${fileId}`,
      method: 'get'
    })
  },

  /**
   * 删除音频文件
   */
  deleteAudio: (fileId) => {
    return request({
      url: `${AUDIO_API_BASE}/${fileId}`,
      method: 'delete'
    })
  },

  /**
   * 删除音频文件（别名）
   */
  deleteAudioFile: (fileId) => {
    return request({
      url: `${AUDIO_API_BASE}/${fileId}`,
      method: 'delete'
    })
  },

  /**
   * 下载音频文件
   */
  downloadAudioFile: (fileId) => {
    return request({
      url: `${AUDIO_API_BASE}/${fileId}/download`,
      method: 'get',
      responseType: 'blob'
    })
  },

  /**
   * 批量删除音频文件
   */
  deleteFiles: (fileIds) => {
    return request({
      url: `${AUDIO_API_BASE}/batch/delete`,
      method: 'post',
      data: { file_ids: fileIds }
    })
  },

  /**
   * 提交批量处理任务
   */
  submitBatchProcessingTask: (taskData) => {
    return request({
      url: `${AUDIO_API_BASE}/batch/process`,
      method: 'post',
      data: taskData
    })
  },

  /**
   * 获取系统状态
   */
  getSystemStatus: () => {
    return request({
      url: `${AUDIO_API_BASE}/system/status`,
      method: 'get'
    })
  },

  /**
   * 获取已处理文件数量
   */
  getProcessedFilesCount: () => {
    return request({
      url: `${AUDIO_API_BASE}/stats/files`,
      method: 'get'
    })
  },

  /**
   * 获取活跃任务
   */
  getActiveTasks: () => {
    return request({
      url: `${AUDIO_API_BASE}/tasks/active`,
      method: 'get'
    })
  },

  /**
   * 获取在线用户
   */
  getOnlineUsers: () => {
    return request({
      url: `${AUDIO_API_BASE}/users/online`,
      method: 'get'
    })
  },

  /**
   * 创建处理任务
   */
  createProcessingTask: (taskConfig) => {
    return request({
      url: `${AUDIO_API_BASE}/tasks`,
      method: 'post',
      data: taskConfig
    })
  },

  /**
   * 提交处理任务 (单文件处理)
   */
  submitProcessingTask: (taskData) => {
    return request({
      url: `${AUDIO_API_BASE}/process`,
      method: 'post',
      data: taskData
    })
  },

  /**
   * 暂停任务
   */
  pauseTask: (taskId) => {
    return request({
      url: `${AUDIO_API_BASE}/tasks/${taskId}/pause`,
      method: 'post'
    })
  },

  /**
   * 恢复任务
   */
  resumeTask: (taskId) => {
    return request({
      url: `${AUDIO_API_BASE}/tasks/${taskId}/resume`,
      method: 'post'
    })
  },

  /**
   * 获取处理结果列表
   */
  getProcessingResults: (params = {}) => {
    return request({
      url: `${AUDIO_API_BASE}/results`,
      method: 'get',
      params: {
        limit: params.limit || 50,
        offset: params.offset || 0
      }
    })
  },

  /**
   * 获取单个处理结果详情
   */
  getProcessingResultDetail: (resultId) => {
    return request({
      url: `${AUDIO_API_BASE}/results/${resultId}`,
      method: 'get'
    })
  },

  /**
   * 删除处理结果
   */
  deleteProcessingResult: (resultId) => {
    return request({
      url: `${AUDIO_API_BASE}/results/${resultId}`,
      method: 'delete'
    })
  },

  /**
   * 获取系统资源状态
   */
  getSystemResources: () => {
    return request({
      url: '/api/v1/resource/system/resources',
      method: 'get'
    })
  },

  /**
   * 获取系统健康状态
   */
  getSystemHealth: () => {
    return request({
      url: '/api/v1/resource/system/health',
      method: 'get'
    })
  },

  /**
   * 获取队列统计信息
   */
  getQueueStats: () => {
    return request({
      url: '/api/v1/task/queue/statistics',
      method: 'get'
    })
  },

  /**
   * 获取活动任务列表
   */
  getActiveTasksList: () => {
    return request({
      url: '/api/v1/task/active',
      method: 'get'
    })
  },

  /**
   * 获取系统日志
   */
  getSystemLogs: (params = {}) => {
    return request({
      url: '/api/v1/error/logs',
      method: 'get',
      params: {
        limit: params.limit || 20,
        level: params.level || 'all'
      }
    })
  },

  /**
   * 重命名文件
   */
  renameFile: (fileId, newName) => {
    return request({
      url: `${AUDIO_API_BASE}/${fileId}/rename`,
      method: 'put',
      data: { new_name: newName }
    })
  }
}

/**
 * 音频处理API
 */
export const audioProcessingAPI = {
  /**
   * VAD语音活动检测
   */
  vadDetection: (data) => {
    return request({
      url: `${SPEECH_API_BASE}/vad-detection`,
      method: 'post',
      data
    })
  },

  /**
   * 语音识别
   */
  speechRecognition: (data) => {
    return request({
      url: `${SPEECH_API_BASE}/speech-recognition`,
      method: 'post',
      data
    })
  },

  /**
   * 说话人识别
   */
  speakerRecognition: (data) => {
    return request({
      url: `${SPEECH_API_BASE}/speaker-recognition`,
      method: 'post',
      data
    })
  },

  /**
   * 会议语音转录
   */
  meetingTranscription: (data) => {
    return request({
      url: `${SPEECH_API_BASE}/meeting-transcription`,
      method: 'post',
      data
    })
  },

  /**
   * 音频预处理
   */
  audioPreprocessing: (data) => {
    return request({
      url: `${SPEECH_API_BASE}/preprocessing`,
      method: 'post',
      data
    })
  },

  /**
   * 音频增强（预处理+语音识别）
   */
  audioEnhancement: (data) => {
    return request({
      url: `${SPEECH_API_BASE}/audio-enhancement`,
      method: 'post',
      data
    })
  },

  /**
   * 音频质量分析
   */
  qualityAnalysis: (data) => {
    return request({
      url: `${SPEECH_API_BASE}/quality-analysis`,
      method: 'post',
      data
    })
  },

  /**
   * 获取任务状态
   */
  getTaskStatus: (taskId) => {
    return request({
      url: `${SPEECH_API_BASE}/task/${taskId}`,
      method: 'get'
    })
  },

  /**
   * 取消任务
   */
  cancelTask: (taskId) => {
    return request({
      url: `${SPEECH_API_BASE}/task/${taskId}`,
      method: 'delete'
    })
  },

  /**
   * 获取任务结果
   */
  getTaskResult: (taskId) => {
    return request({
      url: `${SPEECH_API_BASE}/task/${taskId}/result`,
      method: 'get'
    })
  },

  /**
   * 下载处理结果
   */
  downloadResult: (taskId, format = 'json') => {
    return request({
      url: `${AUDIO_API_BASE}/results/${taskId}/download`,
      method: 'get',
      params: { format },
      responseType: 'blob'
    })
  },

  /**
   * 创建音频处理任务
   */
  createTask: (taskConfig) => {
    return request({
      url: `${AUDIO_API_BASE}/tasks`,
      method: 'post',
      data: taskConfig
    })
  }
}

/**
 * 音频分析API
 */
export const audioAnalysisAPI = {
  /**
   * 获取音频波形数据
   */
  getWaveformData: (fileId, options = {}) => {
    return request({
      url: `${AUDIO_API_BASE}/${fileId}/waveform`,
      method: 'get',
      params: {
        width: options.width || 800,
        height: options.height || 200,
        samples: options.samples || 1000
      }
    })
  },

  /**
   * 获取音频频谱数据
   */
  getSpectrumData: (fileId, options = {}) => {
    return request({
      url: `${AUDIO_API_BASE}/${fileId}/spectrum`,
      method: 'get',
      params: {
        width: options.width || 800,
        height: options.height || 200,
        fft_size: options.fftSize || 1024
      }
    })
  },

  /**
   * 获取音频统计信息
   */
  getAudioStats: (fileId) => {
    return request({
      url: `${AUDIO_API_BASE}/${fileId}/stats`,
      method: 'get'
    })
  }
}

/**
 * 模型管理API
 */
export const modelAPI = {
  /**
   * 获取可用模型列表
   */
  getAvailableModels: () => {
    return request({
      url: `${SPEECH_API_BASE}/models`,
      method: 'get'
    })
  },

  /**
   * 获取模型信息
   */
  getModelInfo: (modelType) => {
    return request({
      url: `${SPEECH_API_BASE}/models/${modelType}`,
      method: 'get'
    })
  },

  /**
   * 检查模型状态
   */
  checkModelStatus: (modelType) => {
    return request({
      url: `${SPEECH_API_BASE}/models/${modelType}/status`,
      method: 'get'
    })
  }
}

/**
 * 配置管理API
 */
export const configAPI = {
  /**
   * 获取默认配置
   */
  getDefaultConfig: (processingMode) => {
    return request({
      url: `${SPEECH_API_BASE}/config/default`,
      method: 'get',
      params: { mode: processingMode }
    })
  },

  /**
   * 保存用户配置
   */
  saveUserConfig: (configName, config) => {
    return request({
      url: `${SPEECH_API_BASE}/config/user`,
      method: 'post',
      data: {
        name: configName,
        config
      }
    })
  },

  /**
   * 获取用户配置列表
   */
  getUserConfigs: () => {
    return request({
      url: `${SPEECH_API_BASE}/config/user`,
      method: 'get'
    })
  },

  /**
   * 删除用户配置
   */
  deleteUserConfig: (configId) => {
    return request({
      url: `${SPEECH_API_BASE}/config/user/${configId}`,
      method: 'delete'
    })
  }
}

/**
 * 统计API
 */
export const statsAPI = {
  /**
   * 获取处理统计
   */
  getProcessingStats: (timeRange = '7d') => {
    return request({
      url: `${SPEECH_API_BASE}/stats/processing`,
      method: 'get',
      params: { range: timeRange }
    })
  },

  /**
   * 获取用户使用统计
   */
  getUserStats: () => {
    return request({
      url: `${SPEECH_API_BASE}/stats/user`,
      method: 'get'
    })
  },

  /**
   * 获取系统性能统计
   */
  getSystemStats: () => {
    return request({
      url: `${SPEECH_API_BASE}/stats/system`,
      method: 'get'
    })
  }
}

// 导出所有API
export default {
  audioFile: audioFileAPI,
  processing: audioProcessingAPI,
  analysis: audioAnalysisAPI,
  model: modelAPI,
  config: configAPI,
  stats: statsAPI
}
