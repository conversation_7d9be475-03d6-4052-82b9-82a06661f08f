import sqlite3
import json
from datetime import datetime

def check_tasks():
    try:
        conn = sqlite3.connect('data/speech_platform.db')
        cursor = conn.cursor()
        
        # 检查表结构
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print("数据库表:", tables)
        
        # 检查最近的任务记录
        cursor.execute("SELECT task_id, status, created_at FROM task_records ORDER BY created_at DESC LIMIT 10;")
        recent_tasks = cursor.fetchall()
        print("\n最近的任务:")
        for task in recent_tasks:
            print(f"  任务ID: {task[0]}, 状态: {task[1]}, 创建时间: {task[2]}")
        
        # 检查特定任务
        task_id = "meeting_transcription_3424c703e257"
        cursor.execute("SELECT * FROM task_records WHERE task_id = ?", (task_id,))
        task_info = cursor.fetchone()
        if task_info:
            print(f"\n任务 {task_id} 信息:")
            print(f"  状态: {task_info[2]}")
            print(f"  创建时间: {task_info[3]}")
            print(f"  更新时间: {task_info[4]}")
        else:
            print(f"\n未找到任务: {task_id}")
        
        conn.close()
        
    except Exception as e:
        print(f"检查数据库时出错: {e}")

if __name__ == "__main__":
    check_tasks()
