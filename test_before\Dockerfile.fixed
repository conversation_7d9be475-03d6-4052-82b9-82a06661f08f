# 使用官方Python基础镜像
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV STREAMLIT_SERVER_PORT=8501
ENV STREAMLIT_SERVER_ADDRESS=0.0.0.0
ENV STREAMLIT_SERVER_HEADLESS=true
ENV STREAMLIT_BROWSER_GATHER_USAGE_STATS=false

# 智能配置阿里云APT源
RUN apt-get update && apt-get install -y lsb-release && \
    DEBIAN_VERSION=$(lsb_release -cs) && \
    echo "检测到Debian版本: $DEBIAN_VERSION" && \
    if [ -f /etc/apt/sources.list ]; then \
        cp /etc/apt/sources.list /etc/apt/sources.list.bak; \
    fi && \
    echo "deb https://mirrors.aliyun.com/debian/ $DEBIAN_VERSION main" > /etc/apt/sources.list && \
    echo "deb https://mirrors.aliyun.com/debian-security/ $DEBIAN_VERSION-security main" >> /etc/apt/sources.list && \
    echo "deb https://mirrors.aliyun.com/debian/ $DEBIAN_VERSION-updates main" >> /etc/apt/sources.list

# 配置pip使用阿里云镜像源
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip config set install.trusted-host mirrors.aliyun.com && \
    pip config set global.timeout 120

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    software-properties-common \
    git \
    tesseract-ocr \
    tesseract-ocr-chi-sim \
    tesseract-ocr-chi-tra \
    poppler-utils \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements.txt并安装Python依赖
COPY requirements.txt .
RUN pip install  --timeout 300 -r requirements.txt

# 分批安装LlamaIndex组件以避免超时
RUN pip install  --timeout 300 \
    llama-index \
    llama-index-embeddings-huggingface \
    llama-index-embeddings-ollama

RUN pip install  --timeout 300 \
    llama-index-llms-ollama \
    llama-index-vector-stores-chroma \
    chromadb

RUN pip install  --timeout 300 \
    sentence-transformers \
    PyPDF2 \
    langchain \
    langchain-core

# 复制项目文件
COPY . .

# 创建必要的目录
RUN mkdir -p /app/chroma_db /app/data/documents /app/knowledge_base /app/models

# 设置文件权限
RUN chmod -R 755 /app

# 暴露Streamlit默认端口
EXPOSE 8501

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl --fail http://localhost:8501/_stcore/health || exit 1

# 启动命令
CMD ["streamlit", "run", "Home.py", "--server.port=8501", "--server.address=0.0.0.0"] 