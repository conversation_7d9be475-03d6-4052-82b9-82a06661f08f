@echo off
echo ========================================
echo 语音处理智能平台 - 服务停止脚本
echo ========================================
echo.

echo 🛑 停止前端服务...
taskkill /f /im node.exe >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 前端服务已停止
) else (
    echo ℹ️ 前端服务未运行或已停止
)

echo.
echo 🛑 停止后端服务...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8000') do (
    taskkill /f /pid %%a >nul 2>&1
)
echo ✅ 后端服务已停止

echo.
echo 🛑 停止 Celery Worker...
taskkill /f /im python.exe /fi "WINDOWTITLE eq Celery Worker*" >nul 2>&1
wmic process where "commandline like '%%celery%%worker%%'" delete >nul 2>&1
echo ✅ Celery Worker 已停止

echo.
echo 🛑 停止 Redis 服务...
docker stop redis-server >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Redis 服务已停止
    docker rm redis-server >nul 2>&1
    echo ✅ Redis 容器已删除
) else (
    echo ℹ️ Redis 服务未运行或已停止
)

echo.
echo ========================================
echo 🎉 所有服务已停止！
echo ========================================
echo.
echo 💡 提示:
echo   - 如果有服务仍在运行，请手动关闭相应的终端窗口
echo   - 数据已保存，下次启动时会自动恢复
echo.
pause
