#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
兼容性包安装脚本
解决Python 3.11环境下的依赖冲突问题
"""

import subprocess
import sys
import os

def run_uv_install(packages, description=""):
    """使用uv安装包"""
    print(f"\n{'='*60}")
    print(f"安装: {description}")
    print(f"包: {packages}")
    print(f"{'='*60}")
    
    cmd = f"uv pip install {packages}"
    try:
        result = subprocess.run(cmd, shell=True, check=True, capture_output=True, text=True)
        print("✅ 安装成功")
        if result.stdout:
            print("输出:", result.stdout[-500:])  # 只显示最后500字符
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败: {e}")
        if e.stderr:
            print("错误:", e.stderr[-500:])
        return False

def install_packages_step_by_step():
    """分步安装包，避免依赖冲突"""
    
    # 第1步: 安装基础科学计算包
    basic_packages = [
        "numpy==1.24.3",
        "scipy",
        "matplotlib>=3.7.2",
        "pandas",
        "scikit-learn>=1.3.0"
    ]
    
    print("🔧 第1步: 安装基础科学计算包")
    for pkg in basic_packages:
        if not run_uv_install(pkg, f"基础包 {pkg}"):
            print(f"⚠️ {pkg} 安装失败，继续安装其他包...")
    
    # 第2步: 安装PyTorch (使用CPU版本避免CUDA兼容问题)
    print("\n🔧 第2步: 安装PyTorch")
    torch_packages = "torch==2.1.0 torchaudio==2.1.0 --index-url https://download.pytorch.org/whl/cpu"
    run_uv_install(torch_packages, "PyTorch CPU版本")
    
    # 第3步: 安装Streamlit和Web框架
    print("\n🔧 第3步: 安装Web框架")
    web_packages = [
        "streamlit==1.28.0",
        "streamlit-extras>=0.3.0",
        "requests"
    ]
    for pkg in web_packages:
        run_uv_install(pkg, f"Web框架 {pkg}")
    
    # 第4步: 安装文档处理
    print("\n🔧 第4步: 安装文档处理")
    doc_packages = [
        "python-docx>=0.8.11",
        "PyMuPDF>=1.21.1", 
        "Pillow>=9.5.0",
        "openpyxl",
        "python-pptx",
        "PyPDF2"
    ]
    for pkg in doc_packages:
        run_uv_install(pkg, f"文档处理 {pkg}")
    
    # 第5步: 安装语音处理 (避免funasr)
    print("\n🔧 第5步: 安装语音处理")
    audio_packages = [
        "SpeechRecognition>=3.10.0",
        "pydub>=0.25.1", 
        "soundfile>=0.12.1",
        "librosa>=0.10.0"
    ]
    for pkg in audio_packages:
        run_uv_install(pkg, f"语音处理 {pkg}")
    
    # 第6步: 安装机器学习 (避免有问题的transformers版本)
    print("\n🔧 第6步: 安装机器学习")
    ml_packages = [
        "transformers>=4.36.0,<4.40.0",  # 使用更保守的版本范围
        "accelerate>=0.21.0",
        "safetensors>=0.3.1"
    ]
    for pkg in ml_packages:
        run_uv_install(pkg, f"机器学习 {pkg}")
    
    # 第7步: 尝试安装sentence-transformers
    print("\n🔧 第7步: 安装sentence-transformers")
    run_uv_install("sentence-transformers", "句子嵌入模型")
    
    # 第8步: 安装ChromaDB (可能需要特殊处理)
    print("\n🔧 第8步: 安装向量数据库")
    run_uv_install("chromadb>=0.4.0,<0.5.0", "向量数据库")
    
    # 第9步: 安装LangChain
    print("\n🔧 第9步: 安装LangChain")
    langchain_packages = [
        "langchain",
        "langchain-core"
    ]
    for pkg in langchain_packages:
        run_uv_install(pkg, f"LangChain {pkg}")

def install_funasr_alternative():
    """安装funasr的替代方案"""
    print("\n🔧 尝试安装funasr替代方案")
    
    # 方案1: 尝试安装更新版本的funasr
    alternatives = [
        "funasr>=1.0.0",  # 更新版本可能支持Python 3.11
        "modelscope>=1.9.5",  # ModelScope作为备选
    ]
    
    for alt in alternatives:
        print(f"\n尝试安装: {alt}")
        if run_uv_install(alt, f"funasr替代方案 {alt}"):
            print(f"✅ {alt} 安装成功")
            break
        else:
            print(f"❌ {alt} 安装失败，尝试下一个...")

def test_installation():
    """测试安装结果"""
    print("\n" + "="*60)
    print("🧪 测试安装结果")
    print("="*60)
    
    test_imports = [
        ("numpy", "import numpy as np; print(f'NumPy: {np.__version__}')"),
        ("torch", "import torch; print(f'PyTorch: {torch.__version__}')"),
        ("streamlit", "import streamlit as st; print(f'Streamlit: {st.__version__}')"),
        ("soundfile", "import soundfile as sf; print('SoundFile: OK')"),
        ("speech_recognition", "import speech_recognition as sr; print('SpeechRecognition: OK')"),
        ("transformers", "import transformers; print(f'Transformers: {transformers.__version__}')"),
    ]
    
    success_count = 0
    for name, test_code in test_imports:
        try:
            result = subprocess.run([sys.executable, "-c", test_code], 
                                  capture_output=True, text=True, timeout=30)
            if result.returncode == 0:
                print(f"✅ {name}: {result.stdout.strip()}")
                success_count += 1
            else:
                print(f"❌ {name}: 导入失败")
                if result.stderr:
                    print(f"   错误: {result.stderr.strip()[:100]}")
        except Exception as e:
            print(f"❌ {name}: 测试异常 - {e}")
    
    print(f"\n📊 测试结果: {success_count}/{len(test_imports)} 包可用")
    
    if success_count >= len(test_imports) - 1:
        print("🎉 安装基本成功！")
        return True
    else:
        print("⚠️ 部分包安装失败，但核心功能可能仍可使用")
        return False

def main():
    """主安装流程"""
    print("🚀 Python 3.11兼容性包安装脚本")
    print("解决llvmlite和funasr的兼容性问题")
    print("="*60)
    
    # 检查环境
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    
    # 确认继续
    response = input("\n是否继续安装? (y/N): ").strip().lower()
    if response != 'y':
        print("安装已取消")
        return
    
    # 执行安装
    install_packages_step_by_step()
    
    # 尝试安装funasr替代方案
    install_funasr_alternative()
    
    # 测试安装
    test_installation()
    
    print("\n" + "="*60)
    print("📋 安装完成！")
    print("="*60)
    print("""
下一步:
1. 测试语音处理功能: python test_torch_simple.py
2. 启动Streamlit应用: streamlit run pages/语音处理分析.py
3. 如果funasr不可用，可以使用其他语音识别库

注意: 由于funasr在Python 3.11下有兼容性问题，
建议使用SpeechRecognition + 其他语音模型作为替代方案。
    """)

if __name__ == "__main__":
    main()
