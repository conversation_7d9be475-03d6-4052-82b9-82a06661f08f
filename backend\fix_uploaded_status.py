#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复文档状态不一致问题
专门处理状态为"uploaded"但实际已有节点数据的文档
"""

import logging
from datetime import datetime, timezone
from backend.core.database import get_db_session
from backend.models.document_management import ManagedDocument, DocumentSection
from backend.services.document_db_service import document_db_service

logger = logging.getLogger(__name__)

def fix_uploaded_documents():
    """修复状态为uploaded但已有节点的文档"""
    db = get_db_session()
    fixed_count = 0
    
    try:
        print("=" * 80)
        print("修复文档状态工具 - 专门处理uploaded状态问题")
        print("=" * 80)
        
        # 查找状态为uploaded但有节点的文档
        uploaded_docs = db.query(ManagedDocument)\
            .filter(ManagedDocument.status == 'uploaded')\
            .all()
        
        print(f"找到 {len(uploaded_docs)} 个状态为'uploaded'的文档")
        
        for doc in uploaded_docs:
            print(f"\n检查文档: ID={doc.id}, 文件名={doc.filename}")
            print(f"  当前状态: {doc.status}")
            print(f"  处理进度: {doc.processing_progress}")
            print(f"  记录的节点数: {doc.sections_count}")
            
            # 检查实际节点数量
            actual_sections = db.query(DocumentSection)\
                .filter(DocumentSection.document_id == doc.id)\
                .count()
            
            print(f"  实际节点数: {actual_sections}")
            
            if actual_sections > 0:
                print("  🔧 发现节点数据，修复状态为'completed'...")
                
                try:
                    # 计算统计信息
                    sections = db.query(DocumentSection)\
                        .filter(DocumentSection.document_id == doc.id)\
                        .all()
                    
                    total_chars = sum(len(s.content or "") for s in sections)
                    total_words = sum(len((s.content or "").split()) for s in sections)
                    avg_length = total_chars // max(len(sections), 1)
                    
                    # 更新文档状态和统计信息
                    update_data = {
                        "status": "completed",
                        "processing_progress": 1.0,
                        "processed_at": datetime.now(timezone.utc),
                        "sections_count": actual_sections,
                        "total_characters": total_chars,
                        "total_words": total_words,
                        "average_section_length": avg_length
                    }
                    
                    document_db_service.update_document(
                        db, doc.id, doc.user_id, update_data
                    )
                    
                    # 添加处理日志
                    document_db_service.add_processing_log(
                        db, doc.id, "INFO",
                        f"状态修复：从uploaded修复为completed（{actual_sections}个节点）",
                        "status_fix"
                    )
                    
                    print("  ✅ 修复成功")
                    fixed_count += 1
                    
                except Exception as e:
                    print(f"  ❌ 修复失败: {e}")
            else:
                print("  ⚠️  没有节点数据，保持当前状态")
            
            print("-" * 40)
        
        print(f"\n修复完成！成功修复 {fixed_count} 个文档")
        return fixed_count
        
    except Exception as e:
        logger.error(f"修复文档状态失败: {e}")
        print(f"❌ 修复过程中出现错误: {e}")
        return 0
    finally:
        db.close()

def check_document_consistency():
    """检查文档数据一致性"""
    db = get_db_session()
    
    try:
        print("\n" + "=" * 80)
        print("文档数据一致性检查")
        print("=" * 80)
        
        # 检查各种状态的文档数量
        status_counts = {}
        for status in ['uploaded', 'processing', 'completed', 'failed']:
            count = db.query(ManagedDocument)\
                .filter(ManagedDocument.status == status)\
                .count()
            status_counts[status] = count
            print(f"{status}: {count} 个文档")
        
        # 检查有节点但状态不是completed的文档
        inconsistent_docs = db.query(ManagedDocument)\
            .filter(ManagedDocument.status != 'completed')\
            .filter(ManagedDocument.sections_count > 0)\
            .count()
        
        print(f"\n不一致的文档（有节点但状态不是completed）: {inconsistent_docs} 个")
        
        # 检查状态是completed但没有节点的文档
        completed_no_sections = db.query(ManagedDocument)\
            .filter(ManagedDocument.status == 'completed')\
            .filter(ManagedDocument.sections_count == 0)\
            .count()
        
        print(f"状态是completed但没有节点的文档: {completed_no_sections} 个")
        
        return {
            "status_counts": status_counts,
            "inconsistent_docs": inconsistent_docs,
            "completed_no_sections": completed_no_sections
        }
        
    except Exception as e:
        logger.error(f"检查文档一致性失败: {e}")
        return None
    finally:
        db.close()

if __name__ == "__main__":
    print("开始修复文档状态问题...")
    
    # 检查当前状态
    check_document_consistency()
    
    # 修复uploaded状态的文档
    fixed_count = fix_uploaded_documents()
    
    # 再次检查状态
    print("\n修复后的状态:")
    check_document_consistency()
    
    print(f"\n修复完成！共修复了 {fixed_count} 个文档")
    print("建议重新刷新前端页面以查看修复结果。")
