# ===========================================
# 语音处理智能平台 Docker 忽略文件
# 优化构建上下文，提高构建速度和安全性
# ===========================================
#
# ⚠️ 重要提醒：以下目录/文件是Docker构建必需的，不能排除：
# - .venv/                    (Backend和Celery需要)
# - frontend/                 (Frontend构建需要)
# - backend/                  (Backend和Celery需要)
# - config/                   (Backend和Celery需要)
# - utils/                    (Backend和Celery需要)
# - requirements.txt          (可选，用于验证)
# - pyproject.toml           (可选，用于配置)
# - my_notebook_docker/nginx.conf (Frontend需要)
#

# ===========================================
# 版本控制系统
# ===========================================
.git
.gitignore
.gitattributes
.gitmodules

# ===========================================
# Python 相关
# ===========================================
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境 (注意：.venv需要被Docker复制，所以不排除)
env/
venv/
# .venv/  # 注释掉，Docker需要复制这个目录
ENV/
env.bak/
venv.bak/
python==3.11/

# PyInstaller
*.manifest
*.spec

# 单元测试和覆盖率
.pytest_cache/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# ===========================================
# Node.js 相关 (前端构建时会单独处理)
# ===========================================
node_modules/
frontend/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity
package-lock.json
frontend/package-lock.json

# ===========================================
# IDE 和编辑器
# ===========================================
.vscode/
.idea/
*.swp
*.swo
*~
.cursor/
*.sublime-project
*.sublime-workspace

# ===========================================
# 操作系统
# ===========================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# ===========================================
# 日志文件
# ===========================================
*.log
logs/
*.log.*
worker_*.log

# ===========================================
# 临时文件
# ===========================================
*.tmp
*.temp
*.bak
*.backup
*.old
*.orig

# ===========================================
# 环境配置文件 (包含敏感信息)
# ===========================================
.env
.env.local
.env.development
.env.test
.env.production
.env.*
!.env.template

# ===========================================
# Docker 相关文件 (避免递归)
# ===========================================
# Dockerfile* - 注释掉，Docker构建需要这些文件
# docker-compose*.yml - 注释掉，Docker构建需要这些文件
.dockerignore
# my_notebook_docker/ - 注释掉，Docker构建需要这个目录

# ===========================================
# 数据库文件 (运行时生成)
# ===========================================
*.db
*.sqlite
*.sqlite3
data/speech_platform*.db
backend/speech_platform.db

# ===========================================
# 大型数据文件和模型
# ===========================================
# AI模型文件 (通过数据卷挂载)
models/
*.bin
*.safetensors
*.onnx
*.pt
*.pth
*.h5
*.pkl
*.joblib

# 音频文件
*.wav
*.mp3
*.m4a
*.flac
*.aac
*.ogg
resource/
data/uploads/

# 向量数据库
data/chroma_db/
chroma_db/

# ===========================================
# 缓存和构建产物
# ===========================================
cache/
.cache/
docker-cache/
.pytest_cache/
.mypy_cache/
.ruff_cache/

# 前端构建产物 (排除构建产物，但保留源码)
frontend/dist/
frontend/build/
frontend/.nuxt/
frontend/.next/
# 注意：不排除frontend/源码目录，Docker需要它

# ===========================================
# 文档和说明文件
# ===========================================
*.md
!README.md
docs/
documentation/

# ===========================================
# 测试相关
# ===========================================
test/
tests/
test_*/
*_test.py
test_*.py
*test*.html
*test*.js
*test*.json
test_before/
backup/
backup_before_fix/

# ===========================================
# 开发和调试文件
# ===========================================
debug_*.py
debug_*.log
debug_*.md
debug_*.html
debug_*.json
check_*.py
diagnose_*.py
fix_*.py
monitor_*.py
validate_*.py
*_debug_*.py
*_debug_*.md
*_debug_*.json

# 性能报告和分析
performance_reports/
optimization_results/
memory_debug_*.json
integration_test_report_*.json

# ===========================================
# 项目特定文件
# ===========================================
# 任务和计划文件
Tasks_*.md
tasks/
实施总结/
custom_reports/

# 配置和脚本
scripts/
setup.bat
start_*.py
start_*.bat
start_*.sh
stop_*.sh
restart_*.sh
check_*.bat

# 页面组件 (Streamlit相关)
pages/
Home.py
static/

# 工具和实用程序 (Docker需要utils/目录，所以不排除)
# utils/  # 注释掉，Docker需要复制这个目录
# !backend/utils/  # 不再需要这个例外

# 项目文档
PROJECT_*.md
DEPLOYMENT_*.md
*_GUIDE.md
*_SUMMARY.md

# ===========================================
# 安全相关 (绝对不能包含)
# ===========================================
*.key
*.pem
*.crt
*.p12
*.pfx
secrets/
.secrets/
credentials/
.credentials/

# JWT密钥和API密钥
*secret*
*SECRET*
*api_key*
*API_KEY*

# ===========================================
# 其他排除项
# ===========================================
# 压缩文件
*.zip
*.tar
*.tar.gz
*.rar
*.7z

# 媒体文件
*.jpg
*.jpeg
*.png
*.gif
*.svg
*.ico
*.pdf

# 配置文件
*.ini
*.cfg
# *.conf  # 注释掉，因为Docker需要nginx.conf
!my_notebook_docker/nginx.conf
# *.toml - 注释掉，Docker构建可能需要toml文件
!pyproject.toml
