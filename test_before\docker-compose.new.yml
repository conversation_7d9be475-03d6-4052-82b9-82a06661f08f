version: '3.8'

services:
  # 后端API服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: speech-platform-backend
    restart: unless-stopped
    ports:
      - "${BACKEND_PORT:-8000}:8000"
    environment:
      - BACKEND_HOST=0.0.0.0
      - BACKEND_PORT=8000
      - DATABASE_URL=sqlite:///./data/speech_platform.db
      - DOCKER_ENV=true
      - CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://localhost
    volumes:
      # 数据持久化
      - ./data:/app/data
      - ./logs:/app/logs
      - ./models:/app/models
      # 配置文件
      - ./.env:/app/.env:ro
    networks:
      - speech-platform
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # 前端Web服务
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: speech-platform-frontend
    restart: unless-stopped
    ports:
      - "${FRONTEND_PORT:-3000}:80"
    depends_on:
      - backend
    networks:
      - speech-platform
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  # Redis缓存服务（可选）
  redis:
    image: redis:7-alpine
    container_name: speech-platform-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-speech123}
    volumes:
      - redis_data:/data
    networks:
      - speech-platform
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    profiles:
      - with-redis

  # 数据库备份服务
  db-backup:
    image: alpine:latest
    container_name: speech-platform-backup
    restart: "no"
    volumes:
      - ./data:/data
      - ./backups:/backups
    command: >
      sh -c "
        apk add --no-cache sqlite &&
        mkdir -p /backups &&
        sqlite3 /data/speech_platform.db '.backup /backups/speech_platform_$(date +%Y%m%d_%H%M%S).db' &&
        find /backups -name '*.db' -mtime +7 -delete
      "
    networks:
      - speech-platform
    profiles:
      - backup

# 网络配置
networks:
  speech-platform:
    driver: bridge
    name: speech-platform-network

# 数据卷
volumes:
  redis_data:
    driver: local
    name: speech-platform-redis-data
