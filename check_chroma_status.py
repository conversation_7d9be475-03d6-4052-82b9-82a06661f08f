#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查ChromaDB状态脚本
"""

import os
import chromadb
from pathlib import Path

def check_chroma_status():
    """检查ChromaDB状态"""
    
    db_path = "./data/chroma_db"
    collection_name = "knowledge_base"
    
    print("=" * 50)
    print("ChromaDB状态检查")
    print("=" * 50)
    print(f"数据库路径: {os.path.abspath(db_path)}")
    print(f"集合名称: {collection_name}")
    print()
    
    # 检查目录是否存在
    if not os.path.exists(db_path):
        print("❌ 数据库目录不存在")
        return False
    
    # 列出目录内容
    files = list(Path(db_path).rglob("*"))
    print(f"📁 目录包含 {len(files)} 个文件/目录")
    
    try:
        # 连接ChromaDB
        print("\n🔗 连接ChromaDB...")
        client = chromadb.PersistentClient(path=db_path)
        
        # 列出所有集合
        collections = client.list_collections()
        print(f"📚 找到 {len(collections)} 个集合:")
        
        target_collection = None
        for collection in collections:
            doc_count = collection.count()
            print(f"   - {collection.name}: {doc_count} 个文档")
            
            if collection.name == collection_name:
                target_collection = collection
        
        if not target_collection:
            print(f"❌ 未找到目标集合: {collection_name}")
            return False
        
        print(f"\n✅ 找到目标集合: {collection_name}")
        print(f"📊 文档数量: {target_collection.count()}")
        
        # 尝试查询测试
        print("\n🧪 测试查询功能...")
        try:
            # 简单的peek测试
            results = target_collection.peek(limit=1)
            if results and 'ids' in results and results['ids']:
                print("✅ 数据读取正常")
                print(f"   样本ID: {results['ids'][0]}")
                
                # 尝试向量查询
                if 'embeddings' in results and results['embeddings']:
                    test_embedding = results['embeddings'][0]
                    query_results = target_collection.query(
                        query_embeddings=[test_embedding],
                        n_results=1
                    )
                    if query_results and 'ids' in query_results and query_results['ids']:
                        print("✅ 向量查询正常")
                    else:
                        print("❌ 向量查询失败")
                        return False
                else:
                    print("⚠️ 没有嵌入向量数据")
            else:
                print("❌ 数据读取失败")
                return False
                
        except Exception as e:
            print(f"❌ 查询测试失败: {e}")
            return False
        
        print("\n🎉 ChromaDB状态检查完成，一切正常！")
        return True
        
    except Exception as e:
        print(f"❌ ChromaDB连接失败: {e}")
        print(f"错误类型: {type(e)}")
        return False

if __name__ == "__main__":
    success = check_chroma_status()
    
    if not success:
        print("\n💡 可能的解决方案:")
        print("1. 检查ChromaDB版本兼容性")
        print("2. 重新构建向量数据库")
        print("3. 检查文件权限")
        print("4. 清空并重新初始化数据库")
