#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VAD参数优化器
任务4.4: 优化VAD参数配置

功能：
1. 参数网格搜索
2. 性能评估
3. 最优参数推荐
4. 参数对比分析
5. 配置保存和加载
"""

import os
import sys
import json
import time
import tempfile
import traceback
import numpy as np
import matplotlib.pyplot as plt
import soundfile as sf
from pathlib import Path
from typing import List, Tuple, Dict, Optional, Any
from itertools import product
import pandas as pd

# 添加utils目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))

try:
    from utils.speech_recognition_utils import (
        load_vad_model, vad_segment, vad_segment_for_two_person,
        get_optimized_vad_params_for_two_person, set_offline_mode
    )
    from utils.audio_preprocessing import AudioPreprocessor, check_audio_quality
    UTILS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 警告：无法导入utils模块: {str(e)}")
    UTILS_AVAILABLE = False

# 设置离线模式
if UTILS_AVAILABLE:
    set_offline_mode()

class VADParameterOptimizer:
    """VAD参数优化器"""
    
    def __init__(self, test_audio_dir: Optional[str] = None):
        """
        初始化VAD参数优化器
        
        Args:
            test_audio_dir: 测试音频目录
        """
        self.test_audio_dir = test_audio_dir or "test_audio"
        self.results_dir = os.path.join(self.test_audio_dir, "optimization_results")
        self.vad_model = None
        self.optimization_results = []
        
        # 创建必要的目录
        os.makedirs(self.test_audio_dir, exist_ok=True)
        os.makedirs(self.results_dir, exist_ok=True)
        
        # 初始化VAD模型
        self._load_vad_model()
    
    def _load_vad_model(self):
        """加载VAD模型"""
        try:
            if UTILS_AVAILABLE:
                self.vad_model = load_vad_model()
                if self.vad_model:
                    print("✅ VAD模型加载成功")
                else:
                    print("❌ VAD模型加载失败")
            else:
                print("❌ utils模块不可用，无法加载VAD模型")
        except Exception as e:
            print(f"❌ VAD模型加载异常: {str(e)}")
    
    def define_parameter_grid(self) -> Dict[str, List]:
        """定义参数网格搜索空间"""
        return {
            "max_single_segment_time": [10000, 15000, 20000, 30000],  # 最大单个片段时间(ms)
            "merge_length_s": [5, 8, 10, 15],                        # 合并长度(秒)
            "speech_pad_ms": [100, 200, 300, 500],                   # 语音填充(ms)
            "min_speech_duration": [0.2, 0.3, 0.5, 0.8],            # 最小语音持续时间(秒)
            "max_speech_duration": [20, 30, 60, 120],                # 最大语音持续时间(秒)
            "threshold": [0.3, 0.5, 0.7, 0.8]                       # VAD阈值
        }
    
    def create_test_audio_scenarios(self) -> Dict[str, str]:
        """创建不同场景的测试音频"""
        print("🔊 创建测试音频场景...")
        
        scenarios = {}
        sample_rate = 16000
        
        # 场景1: 短对话（快速交替）
        print("  创建场景1: 短对话")
        audio1 = self._create_dialogue_audio(
            sample_rate, duration=15, 
            speech_segments=[(0, 2), (3, 5), (6, 7), (8, 10), (11, 13)],
            frequencies=[440, 880, 440, 880, 440],
            description="短对话-快速交替"
        )
        scenarios["short_dialogue"] = audio1
        
        # 场景2: 长对话（长段语音）
        print("  创建场景2: 长对话")
        audio2 = self._create_dialogue_audio(
            sample_rate, duration=30,
            speech_segments=[(0, 8), (10, 18), (20, 28)],
            frequencies=[440, 880, 440],
            description="长对话-长段语音"
        )
        scenarios["long_dialogue"] = audio2
        
        # 场景3: 噪音环境
        print("  创建场景3: 噪音环境")
        audio3 = self._create_noisy_dialogue_audio(
            sample_rate, duration=20,
            speech_segments=[(1, 4), (6, 9), (12, 15), (17, 19)],
            noise_level=0.1,
            description="噪音环境对话"
        )
        scenarios["noisy_dialogue"] = audio3
        
        # 场景4: 安静环境
        print("  创建场景4: 安静环境")
        audio4 = self._create_dialogue_audio(
            sample_rate, duration=25,
            speech_segments=[(2, 6), (8, 12), (15, 19), (21, 24)],
            frequencies=[330, 660, 330, 660],
            description="安静环境对话"
        )
        scenarios["quiet_dialogue"] = audio4
        
        print(f"✅ 创建了 {len(scenarios)} 个测试场景")
        return scenarios
    
    def _create_dialogue_audio(self, sample_rate: int, duration: int, 
                              speech_segments: List[Tuple[float, float]], 
                              frequencies: List[float], description: str) -> str:
        """创建对话音频"""
        t = np.linspace(0, duration, duration * sample_rate)
        audio = np.zeros_like(t)
        
        # 添加背景噪音
        audio += 0.005 * np.random.randn(len(t))
        
        # 添加语音段
        for i, ((start, end), freq) in enumerate(zip(speech_segments, frequencies)):
            mask = (t >= start) & (t < end)
            # 创建更真实的语音信号
            speech_signal = 0.3 * np.sin(2 * np.pi * freq * t[mask])
            speech_signal += 0.1 * np.sin(2 * np.pi * freq * 1.5 * t[mask])  # 谐波
            speech_signal += 0.05 * np.random.randn(np.sum(mask))  # 语音噪音
            
            # 添加包络
            envelope_len = int(0.1 * sample_rate)  # 渐入渐出长度
            if envelope_len <= len(speech_signal):
                envelope = np.linspace(0, 1, envelope_len)
                speech_signal[:envelope_len] *= envelope
                speech_signal[-envelope_len:] *= envelope[::-1]  # 渐出
            
            audio[mask] = speech_signal
        
        # 保存音频
        filename = f"{description.replace(' ', '_').replace('-', '_')}.wav"
        filepath = os.path.join(self.test_audio_dir, filename)
        sf.write(filepath, audio, sample_rate)
        
        print(f"    保存: {filepath}")
        return filepath
    
    def _create_noisy_dialogue_audio(self, sample_rate: int, duration: int,
                                   speech_segments: List[Tuple[float, float]],
                                   noise_level: float, description: str) -> str:
        """创建噪音环境对话音频"""
        t = np.linspace(0, duration, duration * sample_rate)
        audio = np.zeros_like(t)
        
        # 添加较强的背景噪音
        audio += noise_level * np.random.randn(len(t))
        
        # 添加语音段（信噪比较低）
        for i, (start, end) in enumerate(speech_segments):
            mask = (t >= start) & (t < end)
            freq = 440 if i % 2 == 0 else 880
            
            # 较弱的语音信号
            speech_signal = 0.2 * np.sin(2 * np.pi * freq * t[mask])
            speech_signal += 0.08 * np.random.randn(np.sum(mask))
            
            audio[mask] += speech_signal
        
        # 保存音频
        filename = f"{description.replace(' ', '_').replace('-', '_')}.wav"
        filepath = os.path.join(self.test_audio_dir, filename)
        sf.write(filepath, audio, sample_rate)
        
        print(f"    保存: {filepath}")
        return filepath
    
    def evaluate_parameters(self, params: Dict, test_audio_path: str, 
                          ground_truth_segments: List[Tuple[float, float]]) -> Dict:
        """评估特定参数配置的性能"""
        try:
            if not self.vad_model:
                return {"error": "VAD模型不可用"}
            
            # 执行VAD分割
            start_time = time.time()
            
            # 修改参数格式以适配现有函数
            vad_params = {
                "max_single_segment_time": params.get("max_single_segment_time", 15000),
                "merge_length_s": params.get("merge_length_s", 8),
                "speech_pad_ms": params.get("speech_pad_ms", 200),
                "min_speech_duration": params.get("min_speech_duration", 0.3),
                "max_speech_duration": params.get("max_speech_duration", 60),
                "threshold": params.get("threshold", 0.5)
            }
            
            segments = vad_segment_for_two_person(test_audio_path, self.vad_model, vad_params)
            processing_time = time.time() - start_time
            
            if not segments:
                return {
                    "num_segments": 0,
                    "processing_time": processing_time,
                    "precision": 0,
                    "recall": 0,
                    "f1_score": 0,
                    "accuracy": 0,
                    "error": "未检测到语音段"
                }
            
            # 计算性能指标
            metrics = self._calculate_metrics(segments, ground_truth_segments)
            metrics.update({
                "num_segments": len(segments),
                "processing_time": processing_time,
                "total_speech_time": sum((end - start) / 1000 for start, end in segments)
            })
            
            return metrics
            
        except Exception as e:
            return {"error": f"参数评估失败: {str(e)}"}
    
    def _calculate_metrics(self, predicted_segments: List[Tuple[float, float]], 
                          ground_truth_segments: List[Tuple[float, float]]) -> Dict:
        """计算VAD性能指标"""
        try:
            # 转换时间单位为秒
            pred_segments_sec = []
            for start, end in predicted_segments:
                if start > 100:  # 假设毫秒单位
                    pred_segments_sec.append((start / 1000, end / 1000))
                else:
                    pred_segments_sec.append((start, end))
            
            # 计算时间轴覆盖
            max_time = max(
                max(end for _, end in pred_segments_sec) if pred_segments_sec else 0,
                max(end for _, end in ground_truth_segments) if ground_truth_segments else 0
            )
            
            if max_time == 0:
                return {"precision": 0, "recall": 0, "f1_score": 0, "accuracy": 0}
            
            # 创建时间轴（100ms分辨率）
            time_resolution = 0.1
            time_axis = np.arange(0, max_time, time_resolution)
            
            # 创建标签数组
            pred_labels = np.zeros(len(time_axis), dtype=bool)
            gt_labels = np.zeros(len(time_axis), dtype=bool)
            
            # 填充预测标签
            for start, end in pred_segments_sec:
                start_idx = int(start / time_resolution)
                end_idx = int(end / time_resolution)
                pred_labels[start_idx:min(end_idx, len(pred_labels))] = True
            
            # 填充真实标签
            for start, end in ground_truth_segments:
                start_idx = int(start / time_resolution)
                end_idx = int(end / time_resolution)
                gt_labels[start_idx:min(end_idx, len(gt_labels))] = True
            
            # 计算混淆矩阵
            tp = np.sum(pred_labels & gt_labels)
            fp = np.sum(pred_labels & ~gt_labels)
            fn = np.sum(~pred_labels & gt_labels)
            tn = np.sum(~pred_labels & ~gt_labels)
            
            # 计算指标
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0
            f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
            accuracy = (tp + tn) / len(time_axis)
            
            return {
                "precision": precision,
                "recall": recall,
                "f1_score": f1_score,
                "accuracy": accuracy,
                "true_positive": int(tp),
                "false_positive": int(fp),
                "false_negative": int(fn),
                "true_negative": int(tn)
            }
            
        except Exception as e:
            print(f"计算指标失败: {str(e)}")
            return {"precision": 0, "recall": 0, "f1_score": 0, "accuracy": 0}
    
    def run_grid_search(self, max_combinations: Optional[int] = 50) -> List[Dict]:
        """运行网格搜索优化"""
        print("🚀 开始VAD参数网格搜索优化...")
        
        # 创建测试音频
        test_scenarios = self.create_test_audio_scenarios()
        
        # 定义真实语音段（对应测试音频）
        ground_truth = {
            "short_dialogue": [(0, 2), (3, 5), (6, 7), (8, 10), (11, 13)],
            "long_dialogue": [(0, 8), (10, 18), (20, 28)],
            "noisy_dialogue": [(1, 4), (6, 9), (12, 15), (17, 19)],
            "quiet_dialogue": [(2, 6), (8, 12), (15, 19), (21, 24)]
        }
        
        # 生成参数组合
        param_grid = self.define_parameter_grid()
        param_combinations = list(product(*param_grid.values()))
        param_names = list(param_grid.keys())
        
        # 限制组合数量
        if max_combinations and len(param_combinations) > max_combinations:
            print(f"参数组合数量 ({len(param_combinations)}) 超过限制，随机选择 {max_combinations} 个")
            np.random.seed(42)
            selected_indices = np.random.choice(len(param_combinations), max_combinations, replace=False)
            param_combinations = [param_combinations[i] for i in selected_indices]
        
        print(f"将测试 {len(param_combinations)} 个参数组合")
        
        results = []
        for i, param_values in enumerate(param_combinations):
            print(f"\n进度: {i+1}/{len(param_combinations)}")
            
            # 构建参数字典
            params = dict(zip(param_names, param_values))
            print(f"测试参数: {params}")
            
            # 对每个测试场景进行评估
            scenario_results = {}
            for scenario_name, audio_path in test_scenarios.items():
                gt_segments = ground_truth[scenario_name]
                result = self.evaluate_parameters(params, audio_path, gt_segments)
                scenario_results[scenario_name] = result
            
            # 计算综合得分
            overall_score = self._calculate_overall_score(scenario_results)
            
            result_entry = {
                "params": params,
                "scenario_results": scenario_results,
                "overall_score": overall_score,
                "timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
            }
            
            results.append(result_entry)
            
            # 打印当前结果
            print(f"综合得分: {overall_score:.4f}")
        
        # 保存结果
        self.optimization_results = results
        self._save_results(results)
        
        print(f"\n✅ 网格搜索完成！测试了 {len(results)} 个参数组合")
        return results
    
    def _calculate_overall_score(self, scenario_results: Dict) -> float:
        """计算综合得分"""
        try:
            total_score = 0
            valid_scenarios = 0
            
            # 权重设置
            weights = {
                "f1_score": 0.4,
                "precision": 0.3,
                "recall": 0.2,
                "processing_time": 0.1  # 时间越短越好
            }
            
            for scenario_name, result in scenario_results.items():
                if "error" not in result:
                    scenario_score = 0
                    
                    # F1分数
                    scenario_score += result.get("f1_score", 0) * weights["f1_score"]
                    
                    # 精确率
                    scenario_score += result.get("precision", 0) * weights["precision"]
                    
                    # 召回率
                    scenario_score += result.get("recall", 0) * weights["recall"]
                    
                    # 处理时间（归一化，1秒为基准）
                    processing_time = result.get("processing_time", 1)
                    time_score = max(0, 1 - processing_time / 10)  # 10秒以上得分为0
                    scenario_score += time_score * weights["processing_time"]
                    
                    total_score += scenario_score
                    valid_scenarios += 1
            
            return total_score / valid_scenarios if valid_scenarios > 0 else 0
        except Exception as e:
            print(f"计算综合得分失败: {str(e)}")
            return 0
    
    def _save_results(self, results: List[Dict]):
        """保存优化结果"""
        try:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            results_file = os.path.join(self.results_dir, f"vad_optimization_{timestamp}.json")
            
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False, default=str)
            
            print(f"结果已保存到: {results_file}")
            
            # 保存最佳参数
            best_result = max(results, key=lambda x: x["overall_score"])
            best_params_file = os.path.join(self.results_dir, f"best_vad_params_{timestamp}.json")
            
            with open(best_params_file, 'w', encoding='utf-8') as f:
                json.dump({
                    "best_params": best_result["params"],
                    "overall_score": best_result["overall_score"],
                    "optimization_timestamp": timestamp
                }, f, indent=2, ensure_ascii=False)
            
            print(f"最佳参数已保存到: {best_params_file}")
            
        except Exception as e:
            print(f"保存结果失败: {str(e)}")
    
    def analyze_results(self, results: Optional[List[Dict]] = None) -> Dict:
        """分析优化结果"""
        if results is None:
            results = self.optimization_results
        
        if not results:
            print("❌ 没有可分析的结果")
            return {}
        
        print("\n📊 分析VAD参数优化结果")
        print("=" * 50)
        
        # 找到最佳参数
        best_result = max(results, key=lambda x: x["overall_score"])
        
        print(f"🏆 最佳综合得分: {best_result['overall_score']:.4f}")
        print(f"🔧 最佳参数配置:")
        for param, value in best_result["params"].items():
            print(f"  - {param}: {value}")
        
        # 参数影响分析
        print(f"\n📈 参数影响分析:")
        param_impact = self._analyze_parameter_impact(results)
        for param, impact in param_impact.items():
            print(f"  - {param}: 影响权重 {impact:.4f}")
        
        # Top 5 配置
        print(f"\n🥇 Top 5 参数配置:")
        top_results = sorted(results, key=lambda x: x["overall_score"], reverse=True)[:5]
        for i, result in enumerate(top_results):
            print(f"  {i+1}. 得分: {result['overall_score']:.4f}")
            print(f"     参数: {result['params']}")
        
        return {
            "best_params": best_result["params"],
            "best_score": best_result["overall_score"],
            "parameter_impact": param_impact,
            "top_results": top_results[:5]
        }
    
    def _analyze_parameter_impact(self, results: List[Dict]) -> Dict[str, float]:
        """分析参数对性能的影响"""
        try:
            param_correlations = {}
            
            # 获取所有参数名
            param_names = list(results[0]["params"].keys())
            
            for param_name in param_names:
                # 收集参数值和对应得分
                param_values = []
                scores = []
                
                for result in results:
                    param_values.append(result["params"][param_name])
                    scores.append(result["overall_score"])
                
                # 计算相关性（简化版）
                if len(set(param_values)) > 1:  # 确保有变化
                    correlation = np.corrcoef(param_values, scores)[0, 1]
                    param_correlations[param_name] = abs(correlation) if not np.isnan(correlation) else 0
                else:
                    param_correlations[param_name] = 0
            
            return param_correlations
        except Exception as e:
            print(f"参数影响分析失败: {str(e)}")
            return {}
    
    def recommend_parameters(self, scenario_type: str = "general") -> Dict:
        """基于优化结果推荐参数配置"""
        if not self.optimization_results:
            print("❌ 没有优化结果可用于推荐")
            return {}
        
        print(f"\n💡 为 '{scenario_type}' 场景推荐VAD参数配置")
        
        # 根据场景类型筛选最佳参数
        if scenario_type == "short_dialogue":
            # 短对话：优先考虑精确分割
            best_result = max(self.optimization_results, 
                            key=lambda x: x["scenario_results"].get("short_dialogue", {}).get("f1_score", 0))
        elif scenario_type == "long_dialogue":
            # 长对话：优先考虑召回率
            best_result = max(self.optimization_results, 
                            key=lambda x: x["scenario_results"].get("long_dialogue", {}).get("recall", 0))
        elif scenario_type == "noisy":
            # 噪音环境：优先考虑精确率
            best_result = max(self.optimization_results, 
                            key=lambda x: x["scenario_results"].get("noisy_dialogue", {}).get("precision", 0))
        else:
            # 通用场景：使用综合得分
            best_result = max(self.optimization_results, key=lambda x: x["overall_score"])
        
        recommended_params = best_result["params"]
        performance = best_result["overall_score"]
        
        print(f"🎯 推荐参数配置 (综合得分: {performance:.4f}):")
        for param, value in recommended_params.items():
            print(f"  - {param}: {value}")
        
        return {
            "recommended_params": recommended_params,
            "performance_score": performance,
            "scenario_type": scenario_type
        }

def main():
    """主函数"""
    print("🎯 VAD参数优化器")
    print("=" * 50)
    
    # 创建优化器
    optimizer = VADParameterOptimizer()
    
    # 运行网格搜索
    results = optimizer.run_grid_search(max_combinations=20)  # 限制组合数量以节省时间
    
    if results:
        # 分析结果
        analysis = optimizer.analyze_results(results)
        
        # 推荐参数
        print("\n" + "=" * 50)
        optimizer.recommend_parameters("general")
        optimizer.recommend_parameters("short_dialogue")
        optimizer.recommend_parameters("noisy")
    
    print("\n✅ VAD参数优化完成！")

if __name__ == "__main__":
    main()
