# 音频前端自动化测试计划

## 测试概述

### 测试目标
使用Playwright MCP工具对音频处理中心进行全面的自动化测试，验证用户认证、音频上传、录音、处理等核心功能的完整性和稳定性。重点验证最新的会议转录功能优化和离线模型配置修复效果。

### 测试环境
- **前端地址**: http://localhost:3000
- **后端地址**: http://localhost:8002
- **测试账号**: 用户名 admin，密码 admin123
- **主要测试路由**: /audio-center (音频处理中心页面)
- **测试工具**: Playwright MCP

### 测试音频文件
- **主要测试文件**: D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\resource\对话.mp3
- **格式测试文件**: .wav, .mp3, .m4a, .flac 格式的音频文件
- **边界测试文件**: 超大文件(>100MB)、空文件、损坏文件
- **会议转录专用**: 对话.mp3 (验证2说话人识别效果)

## 核心功能测试重点

### 1. 会议转录功能验证 (重点测试)
**目标**: 验证最新修复的会议转录功能完整性
- **文本-说话人关联**: 验证文本正确分配到对应说话人片段
- **对话格式输出**: 确认生成说话人1/说话人2的时间线格式
- **离线模型加载**: 验证无网络请求，完全本地化处理
- **性能提升验证**: 确认处理时间从15.5秒降至约3.8秒
- **错误处理**: 测试空文本片段和异常情况的处理

### 2. 离线模型配置测试
**目标**: 验证完全离线运行能力
- **网络隔离**: 断网环境下测试模型加载和处理
- **模型路径验证**: 确认所有模型(SenseVoice、VAD、CAM++)使用本地路径
- **环境变量**: 验证HF_HUB_OFFLINE等离线变量正确设置
- **启动时间**: 测试模型加载时间优化效果

### 3. 实时进度监控测试
**目标**: 验证WebSocket实时通信优化效果
- **进度精确性**: 验证进度从0%到100%的准确更新
- **状态同步**: 测试任务状态变化的实时推送
- **完成通知**: 验证任务完成后的WebSocket通知机制
- **错误推送**: 测试异常情况下的错误消息推送

## 测试模块详细说明

### 1. 测试环境准备和配置
**目标**: 确保测试环境就绪，所有依赖服务正常运行
- 验证前端(localhost:3000)和后端(localhost:8002)服务状态
- 检查API接口可访问性 (/docs, /health)
- 准备各种格式的测试音频文件
- 配置Playwright测试环境和浏览器设置
- **新增**: 验证Celery worker正常运行和Redis连接

### 2. 用户认证流程测试
**目标**: 验证用户登录、会话管理和权限控制
- **登录功能验证**: 使用admin/admin123测试登录流程
- **页面跳转测试**: 验证登录成功后跳转到/audio-center
- **会话保持测试**: 刷新页面后保持登录状态
- **Token验证**: 测试JWT token的有效性和失效处理

### 3. 音频上传功能测试
**目标**: 验证文件上传的完整性和可靠性
- **单文件上传**: 使用对话.mp3测试基本上传功能
- **批量上传**: 同时上传多个音频文件
- **格式验证**: 测试支持(.wav, .mp3, .m4a, .flac)和不支持格式
- **大小限制**: 验证100MB文件大小限制
- **进度显示**: 验证上传进度条和WebSocket实时更新

### 4. 音频录音功能测试
**目标**: 验证浏览器录音功能的完整性
- **权限申请**: 测试麦克风权限申请和授权流程
- **录音控制**: 测试开始、暂停、停止按钮功能
- **时长显示**: 验证实时录音时长显示
- **质量设置**: 测试不同录音质量参数配置
- **文件保存**: 测试录音完成后的保存和预览功能

### 5. 音频处理功能测试 (核心重点)
**目标**: 验证AI音频处理功能的准确性和稳定性
- **ASR语音识别**: 使用对话.mp3测试转录准确性
- **说话人识别**: 验证多说话人场景下的识别效果
- **VAD检测**: 测试语音活动检测和静音段处理
- **会议转录**: 验证时间戳对齐和说话人标识
- **批量处理**: 测试批量任务的提交、执行和监控
- **新增**: 验证文本-时间-说话人三元组正确关联
- **新增**: 测试离线模型加载无网络依赖

### 6. 界面交互和视觉效果验证
**目标**: 确保用户界面的美观性和易用性
- **页面布局**: 验证三栏布局(左侧配置、中央文件管理、右侧监控)
- **滚动功能**: 测试文件列表的垂直滚动和滚动条
- **进度指示**: 验证各种进度条和状态指示器的实时更新
- **响应式设计**: 测试不同屏幕尺寸下的自适应效果
- **主题一致性**: 验证Element Plus组件的主题统一性
- **新增**: 验证空文本片段的前端容错处理

### 7. 实时通信功能测试
**目标**: 验证WebSocket实时通信的稳定性
- **连接状态**: 测试WebSocket连接建立、保持和重连
- **进度更新**: 验证任务执行过程中的实时进度推送
- **状态通知**: 测试任务状态变化的实时通知
- **错误推送**: 验证异常情况下的错误消息推送
- **新增**: 验证任务完成后的WebSocket完成通知

### 8. 错误处理和边界测试
**目标**: 验证系统的健壮性和用户体验
- **上传异常**: 测试网络中断、服务器错误等异常处理
- **处理失败**: 验证音频处理失败时的错误提示和重试机制
- **用户体验**: 确保错误提示友好且提供操作指引
- **并发测试**: 测试多用户同时操作的系统稳定性
- **边界情况**: 测试极大文件、空文件等边界条件
- **新增**: 测试模型路径缺失时的错误处理

## 性能基准测试

### 处理时间验证
- **会议转录**: 对话.mp3文件处理时间应在3-5秒范围内
- **模型加载**: 首次加载时间应显著减少
- **内存使用**: 监控处理过程中的内存占用情况
- **并发处理**: 测试多任务并发时的性能表现

### 离线能力验证
- **断网测试**: 在无网络环境下完整测试所有功能
- **模型依赖**: 确认无任何ModelScope或HuggingFace网络请求
- **缓存机制**: 验证模型缓存和重用机制

## 成功标准

### 功能性标准
- ✅ 所有核心功能正常工作无报错
- ✅ 音频上传成功率 > 95%
- ✅ 语音识别准确率符合预期
- ✅ 实时进度更新延迟 < 2秒
- ✅ 错误恢复机制有效
- ✅ **新增**: 会议转录生成正确的说话人对话格式
- ✅ **新增**: 处理时间相比优化前提升70%以上

### 用户体验标准
- ✅ 界面布局符合设计要求
- ✅ 响应式设计在各尺寸下正常
- ✅ 用户操作流程顺畅无阻塞
- ✅ 错误提示清晰且用户友好
- ✅ 加载和处理时间在可接受范围内
- ✅ **新增**: 空文本片段不影响用户体验

### 技术性标准
- ✅ WebSocket连接稳定可靠
- ✅ 内存使用无明显泄漏
- ✅ 并发处理能力满足需求
- ✅ 系统在异常情况下能正常恢复
- ✅ 所有测试用例通过率 > 90%
- ✅ **新增**: 完全离线运行无网络依赖
- ✅ **新增**: 模型加载和处理性能达到优化目标

## 测试执行流程

1. **环境准备**: 启动前后端服务，准备测试数据
2. **基础功能**: 按模块顺序执行核心功能测试
3. **重点验证**: 专门测试会议转录和离线配置功能
4. **集成测试**: 测试完整的用户操作流程
5. **性能测试**: 验证处理时间和离线能力
6. **压力测试**: 执行并发和边界条件测试
7. **结果分析**: 记录测试结果，分析发现的问题
8. **报告生成**: 生成详细的测试报告和改进建议

## 关键测试场景

### 会议转录完整流程测试
1. 上传对话.mp3文件
2. 选择会议转录功能，设置2个说话人
3. 监控实时进度更新
4. 验证处理时间在3-5秒范围内
5. 检查输出格式为说话人1/说话人2对话形式
6. 确认时间戳和文本内容正确对应

### 离线环境验证测试
1. 断开网络连接
2. 重启后端服务和Celery worker
3. 执行完整的音频处理流程
4. 监控后端日志确认无网络请求
5. 验证所有功能正常工作