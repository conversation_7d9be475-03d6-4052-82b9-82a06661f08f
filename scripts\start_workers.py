"""
启动Celery Worker进程的脚本
支持多队列和多进程配置
"""

import os
import sys
import subprocess
import signal
import time
from pathlib import Path
from typing import List, Dict
import psutil
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

class WorkerManager:
    """Worker进程管理器"""
    
    def __init__(self):
        self.workers: List[subprocess.Popen] = []
        self.worker_configs = [
            {
                "name": "document_worker",
                "queues": ["document_processing"],
                "concurrency": 2,
                "max_tasks_per_child": 10
            },
            {
                "name": "vectorization_worker", 
                "queues": ["vectorization"],
                "concurrency": 1,
                "max_tasks_per_child": 5
            },
            {
                "name": "ocr_worker",
                "queues": ["ocr_processing"],
                "concurrency": 2,
                "max_tasks_per_child": 10
            },
            {
                "name": "default_worker",
                "queues": ["default"],
                "concurrency": 1,
                "max_tasks_per_child": 20
            }
        ]
    
    def start_worker(self, config: Dict) -> subprocess.Popen:
        """启动单个Worker进程"""
        
        cmd = [
            sys.executable, "-m", "celery",
            "-A", "backend.core.task_queue:celery_app",
            "worker",
            "--loglevel=info",
            f"--hostname={config['name']}@%h",
            f"--queues={','.join(config['queues'])}",
            f"--concurrency={config['concurrency']}",
            f"--max-tasks-per-child={config['max_tasks_per_child']}",
            "--without-gossip",
            "--without-mingle",
            "--without-heartbeat"
        ]
        
        logger.info(f"启动Worker: {config['name']}")
        logger.info(f"命令: {' '.join(cmd)}")
        
        try:
            process = subprocess.Popen(
                cmd,
                cwd=project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 等待一下确保进程启动
            time.sleep(2)
            
            if process.poll() is None:
                logger.info(f"Worker {config['name']} 启动成功，PID: {process.pid}")
                return process
            else:
                stdout, stderr = process.communicate()
                logger.error(f"Worker {config['name']} 启动失败")
                logger.error(f"stdout: {stdout}")
                logger.error(f"stderr: {stderr}")
                return None
                
        except Exception as e:
            logger.error(f"启动Worker {config['name']} 时出错: {e}")
            return None
    
    def start_all_workers(self):
        """启动所有Worker进程"""
        logger.info("开始启动所有Worker进程...")
        
        for config in self.worker_configs:
            worker = self.start_worker(config)
            if worker:
                self.workers.append(worker)
            else:
                logger.error(f"无法启动Worker: {config['name']}")
        
        logger.info(f"成功启动 {len(self.workers)} 个Worker进程")
    
    def stop_all_workers(self):
        """停止所有Worker进程"""
        logger.info("开始停止所有Worker进程...")
        
        for worker in self.workers:
            try:
                if worker.poll() is None:  # 进程仍在运行
                    logger.info(f"停止Worker进程 PID: {worker.pid}")
                    worker.terminate()
                    
                    # 等待进程优雅退出
                    try:
                        worker.wait(timeout=10)
                    except subprocess.TimeoutExpired:
                        logger.warning(f"Worker PID {worker.pid} 未能优雅退出，强制终止")
                        worker.kill()
                        worker.wait()
                    
                    logger.info(f"Worker PID {worker.pid} 已停止")
                    
            except Exception as e:
                logger.error(f"停止Worker进程时出错: {e}")
        
        self.workers.clear()
        logger.info("所有Worker进程已停止")
    
    def monitor_workers(self):
        """监控Worker进程状态"""
        while True:
            try:
                active_workers = []
                
                for worker in self.workers:
                    if worker.poll() is None:  # 进程仍在运行
                        active_workers.append(worker)
                    else:
                        logger.warning(f"Worker进程 PID {worker.pid} 已退出")
                
                self.workers = active_workers
                
                if not self.workers:
                    logger.error("所有Worker进程都已退出")
                    break
                
                # 每30秒检查一次
                time.sleep(30)
                
            except KeyboardInterrupt:
                logger.info("收到中断信号，停止监控")
                break
            except Exception as e:
                logger.error(f"监控Worker进程时出错: {e}")
                time.sleep(5)
    
    def get_worker_status(self) -> Dict:
        """获取Worker状态信息"""
        status = {
            "total_workers": len(self.workers),
            "active_workers": 0,
            "workers": []
        }
        
        for i, worker in enumerate(self.workers):
            worker_info = {
                "index": i,
                "pid": worker.pid,
                "active": worker.poll() is None
            }
            
            if worker_info["active"]:
                status["active_workers"] += 1
                
                # 获取进程信息
                try:
                    process = psutil.Process(worker.pid)
                    worker_info.update({
                        "cpu_percent": process.cpu_percent(),
                        "memory_mb": process.memory_info().rss / 1024 / 1024,
                        "create_time": process.create_time()
                    })
                except:
                    pass
            
            status["workers"].append(worker_info)
        
        return status


def signal_handler(signum, frame):
    """信号处理器"""
    logger.info(f"收到信号 {signum}，准备退出...")
    if 'manager' in globals():
        manager.stop_all_workers()
    sys.exit(0)


def main():
    """主函数"""
    global manager
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 创建Worker管理器
    manager = WorkerManager()
    
    try:
        # 启动所有Worker
        manager.start_all_workers()
        
        if not manager.workers:
            logger.error("没有成功启动任何Worker进程")
            return 1
        
        # 监控Worker进程
        logger.info("开始监控Worker进程...")
        manager.monitor_workers()
        
    except KeyboardInterrupt:
        logger.info("收到中断信号")
    except Exception as e:
        logger.error(f"运行时出错: {e}")
    finally:
        # 清理资源
        manager.stop_all_workers()
    
    return 0


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
