#!/usr/bin/env python3
"""
使用Playwright测试前端数据同步修复
验证页面刷新时任务状态的一致性
"""

import asyncio
import sys
import os
from playwright.async_api import async_playwright

async def test_frontend_task_status():
    """测试前端任务状态显示"""
    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(headless=False)
        context = await browser.new_context()
        page = await context.new_page()
        
        try:
            print("🌐 测试前端任务状态显示")
            print("=" * 60)
            
            # 访问登录页面
            await page.goto("http://localhost:3000/login")
            await page.wait_for_load_state("networkidle")
            
            # 登录
            print("🔐 执行登录...")
            await page.fill('input[placeholder="请输入用户名"]', "admin")
            await page.fill('input[placeholder="请输入密码"]', "admin123")
            await page.click('button:has-text("登录")')
            
            # 等待登录完成
            await page.wait_for_url("**/dashboard", timeout=10000)
            print("✅ 登录成功")
            
            # 导航到音频中心
            await page.goto("http://localhost:3000/audio-center")
            await page.wait_for_load_state("networkidle")
            print("✅ 进入音频中心")
            
            # 等待页面加载完成
            await page.wait_for_timeout(3000)
            
            # 检查是否有活动任务显示
            print("\n📊 检查任务状态显示...")
            
            # 查找任务状态元素
            task_elements = await page.query_selector_all('.task-item, .processing-item, .active-task')
            
            if task_elements:
                print(f"✅ 找到 {len(task_elements)} 个任务元素")
                
                for i, element in enumerate(task_elements):
                    try:
                        task_text = await element.text_content()
                        print(f"   任务 {i+1}: {task_text[:100]}...")
                    except:
                        print(f"   任务 {i+1}: 无法获取文本")
            else:
                print("⚠️ 未找到任务状态元素")
            
            # 检查右侧面板的处理结果
            print("\n📋 检查处理结果显示...")
            
            # 查找处理结果元素
            result_elements = await page.query_selector_all('.result-item, .processing-result')
            
            if result_elements:
                print(f"✅ 找到 {len(result_elements)} 个结果元素")
                
                for i, element in enumerate(result_elements):
                    try:
                        result_text = await element.text_content()
                        print(f"   结果 {i+1}: {result_text[:100]}...")
                    except:
                        print(f"   结果 {i+1}: 无法获取文本")
            else:
                print("⚠️ 未找到处理结果元素")
            
            # 模拟页面刷新测试
            print("\n🔄 测试页面刷新后的状态一致性...")
            
            # 记录刷新前的状态
            before_refresh = await page.evaluate("""
                () => {
                    const tasks = Array.from(document.querySelectorAll('.task-item, .processing-item, .active-task'))
                        .map(el => el.textContent);
                    const results = Array.from(document.querySelectorAll('.result-item, .processing-result'))
                        .map(el => el.textContent);
                    return { tasks, results };
                }
            """)
            
            # 刷新页面
            await page.reload()
            await page.wait_for_load_state("networkidle")
            await page.wait_for_timeout(3000)
            
            # 记录刷新后的状态
            after_refresh = await page.evaluate("""
                () => {
                    const tasks = Array.from(document.querySelectorAll('.task-item, .processing-item, .active-task'))
                        .map(el => el.textContent);
                    const results = Array.from(document.querySelectorAll('.result-item, .processing-result'))
                        .map(el => el.textContent);
                    return { tasks, results };
                }
            """)
            
            # 比较刷新前后的状态
            print("📊 刷新前后状态对比:")
            print(f"   刷新前任务数: {len(before_refresh['tasks'])}")
            print(f"   刷新后任务数: {len(after_refresh['tasks'])}")
            print(f"   刷新前结果数: {len(before_refresh['results'])}")
            print(f"   刷新后结果数: {len(after_refresh['results'])}")
            
            # 检查控制台错误
            print("\n🐛 检查控制台错误...")
            
            # 监听控制台消息
            console_messages = []
            
            def handle_console(msg):
                if msg.type in ['error', 'warning']:
                    console_messages.append(f"{msg.type}: {msg.text}")
            
            page.on("console", handle_console)
            
            # 等待一段时间收集控制台消息
            await page.wait_for_timeout(2000)
            
            if console_messages:
                print("⚠️ 发现控制台错误/警告:")
                for msg in console_messages[-5:]:  # 只显示最后5条
                    print(f"   {msg}")
            else:
                print("✅ 无控制台错误")
            
            print("\n✅ 前端测试完成")
            
        except Exception as e:
            print(f"❌ 前端测试失败: {e}")
            
        finally:
            await browser.close()

async def main():
    """主测试函数"""
    print("🧪 前端数据同步修复测试")
    print("=" * 80)
    
    await test_frontend_task_status()
    
    print("\n" + "=" * 80)
    print("🎉 测试完成")

if __name__ == "__main__":
    asyncio.run(main())
