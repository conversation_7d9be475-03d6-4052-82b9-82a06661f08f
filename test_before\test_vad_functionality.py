#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VAD功能测试脚本
任务4.1: 创建VAD功能测试脚本

测试内容：
1. VAD分割准确性测试
2. 时间戳精度验证
3. 不同音频质量的处理效果
4. 性能基准测试
5. 与参考实现对比

参考资料：
- https://github.com/MorenoLaQuatra/vad - 基于能量阈值的VAD算法
- https://github.com/marsbroshok/VAD-python - 基于语音频带能量比例的VAD
"""

import os
import sys
import time
import tempfile
import traceback
import numpy as np
import matplotlib.pyplot as plt
import soundfile as sf
from pathlib import Path
from typing import List, Tuple, Dict, Optional, Union
import json

# 添加utils目录到Python路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))

try:
    from utils.speech_recognition_utils import (
        load_vad_model, vad_segment, vad_segment_for_two_person,
        get_optimized_vad_params_for_two_person, check_audio_quality_for_speaker_recognition,
        set_offline_mode
    )
    from utils.audio_preprocessing import AudioPreprocessor, check_audio_quality
    UTILS_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 警告：无法导入utils模块: {str(e)}")
    UTILS_AVAILABLE = False

# 设置离线模式
if UTILS_AVAILABLE:
    set_offline_mode()

class SimpleEnergyVAD:
    """
    简单的基于能量的VAD实现
    参考：https://github.com/MorenoLaQuatra/vad
    """
    
    def __init__(self, sample_rate: int = 16000, frame_length: int = 25, 
                 frame_shift: int = 20, energy_threshold: float = 0.05, 
                 pre_emphasis: float = 0.95):
        """
        初始化基于能量的VAD
        
        Args:
            sample_rate: 采样率 (Hz)
            frame_length: 帧长度 (ms)
            frame_shift: 帧移 (ms)
            energy_threshold: 能量阈值
            pre_emphasis: 预加重系数
        """
        self.sample_rate = sample_rate
        self.frame_length = int(frame_length * sample_rate / 1000)  # 转换为样本数
        self.frame_shift = int(frame_shift * sample_rate / 1000)    # 转换为样本数
        self.energy_threshold = energy_threshold
        self.pre_emphasis = pre_emphasis
    
    def apply_pre_emphasis(self, audio: np.ndarray) -> np.ndarray:
        """应用预加重滤波"""
        if len(audio) == 0:
            return audio
        return np.append(audio[0], audio[1:] - self.pre_emphasis * audio[:-1])
    
    def compute_energy(self, frame: np.ndarray) -> float:
        """计算帧能量"""
        if len(frame) == 0:
            return 0.0
        return np.sum(frame ** 2) / len(frame)
    
    def detect_voice_activity(self, audio: np.ndarray) -> np.ndarray:
        """
        检测语音活动
        
        Args:
            audio: 音频信号
            
        Returns:
            布尔数组，表示每帧是否为语音
        """
        # 预加重
        audio = self.apply_pre_emphasis(audio)
        
        # 分帧
        num_frames = (len(audio) - self.frame_length) // self.frame_shift + 1
        voice_activity = np.zeros(num_frames, dtype=bool)
        
        for i in range(num_frames):
            start = i * self.frame_shift
            end = start + self.frame_length
            frame = audio[start:end]
            
            # 计算能量
            energy = self.compute_energy(frame)
            
            # 判断是否为语音
            voice_activity[i] = energy > self.energy_threshold
        
        return voice_activity
    
    def get_speech_segments(self, audio: np.ndarray, min_speech_duration: float = 0.1,
                          min_silence_duration: float = 0.05) -> List[Tuple[float, float]]:
        """
        获取语音片段的时间戳
        
        Args:
            audio: 音频信号
            min_speech_duration: 最小语音持续时间 (秒)
            min_silence_duration: 最小静音持续时间 (秒)
            
        Returns:
            语音片段列表 [(start_time, end_time), ...]
        """
        voice_activity = self.detect_voice_activity(audio)
        
        # 转换为时间
        frame_time = self.frame_shift / self.sample_rate
        
        # 找到语音段
        segments = []
        in_speech = False
        speech_start = 0
        
        for i, is_speech in enumerate(voice_activity):
            current_time = i * frame_time
            
            if is_speech and not in_speech:
                # 语音开始
                speech_start = current_time
                in_speech = True
            elif not is_speech and in_speech:
                # 语音结束
                speech_duration = current_time - speech_start
                if speech_duration >= min_speech_duration:
                    segments.append((speech_start, current_time))
                in_speech = False
        
        # 处理最后一个语音段
        if in_speech:
            speech_duration = len(voice_activity) * frame_time - speech_start
            if speech_duration >= min_speech_duration:
                segments.append((speech_start, len(voice_activity) * frame_time))
        
        return segments

class VADTester:
    """VAD功能测试器"""
    
    def __init__(self, test_audio_dir: Optional[str] = None):
        """
        初始化VAD测试器
        
        Args:
            test_audio_dir: 测试音频目录
        """
        self.test_audio_dir = test_audio_dir or "test_audio"
        self.results = {}
        self.preprocessor = AudioPreprocessor() if UTILS_AVAILABLE else None
        self.simple_vad = SimpleEnergyVAD()
        
        # 创建测试音频目录
        os.makedirs(self.test_audio_dir, exist_ok=True)
    
    def create_test_audio(self) -> str:
        """
        创建测试音频文件
        
        Returns:
            测试音频文件路径
        """
        print("🔊 创建测试音频文件...")
        
        # 生成测试音频：语音-静音-语音-静音模式
        sample_rate = 16000
        duration = 10  # 10秒
        t = np.linspace(0, duration, duration * sample_rate)
        
        # 创建复合信号
        audio = np.zeros_like(t)
        
        # 语音段1: 0-2秒（正弦波 + 噪音）
        speech1_mask = (t >= 0) & (t < 2)
        audio[speech1_mask] = 0.5 * np.sin(2 * np.pi * 440 * t[speech1_mask])  # 440Hz
        audio[speech1_mask] += 0.1 * np.random.randn(np.sum(speech1_mask))  # 噪音
        
        # 静音段1: 2-3秒（低噪音）
        silence1_mask = (t >= 2) & (t < 3)
        audio[silence1_mask] = 0.02 * np.random.randn(np.sum(silence1_mask))
        
        # 语音段2: 3-6秒（多频率）
        speech2_mask = (t >= 3) & (t < 6)
        audio[speech2_mask] = 0.3 * np.sin(2 * np.pi * 880 * t[speech2_mask])  # 880Hz
        audio[speech2_mask] += 0.2 * np.sin(2 * np.pi * 220 * t[speech2_mask])  # 220Hz
        audio[speech2_mask] += 0.08 * np.random.randn(np.sum(speech2_mask))
        
        # 静音段2: 6-7秒
        silence2_mask = (t >= 6) & (t < 7)
        audio[silence2_mask] = 0.015 * np.random.randn(np.sum(silence2_mask))
        
        # 语音段3: 7-10秒（低频语音）
        speech3_mask = (t >= 7) & (t <= 10)
        audio[speech3_mask] = 0.4 * np.sin(2 * np.pi * 330 * t[speech3_mask])  # 330Hz
        audio[speech3_mask] += 0.05 * np.random.randn(np.sum(speech3_mask))
        
        # 保存测试音频
        test_file = os.path.join(self.test_audio_dir, "test_vad_audio.wav")
        sf.write(test_file, audio, sample_rate)
        
        print(f"✅ 测试音频已创建: {test_file}")
        print(f"   - 语音段1: 0-2秒")
        print(f"   - 静音段1: 2-3秒") 
        print(f"   - 语音段2: 3-6秒")
        print(f"   - 静音段2: 6-7秒")
        print(f"   - 语音段3: 7-10秒")
        
        return test_file
    
    def test_simple_energy_vad(self, audio_file: str) -> Dict:
        """测试简单能量VAD"""
        print("\n🔍 测试简单能量VAD...")
        
        try:
            # 加载音频
            audio, sr = sf.read(audio_file)
            
            # 测试不同的阈值
            thresholds = [0.01, 0.05, 0.1, 0.2]
            results = {}
            
            for threshold in thresholds:
                print(f"  测试阈值: {threshold}")
                
                # 创建VAD实例
                vad = SimpleEnergyVAD(
                    sample_rate=sr,
                    energy_threshold=threshold
                )
                
                # 检测语音活动
                start_time = time.time()
                segments = vad.get_speech_segments(audio)
                processing_time = time.time() - start_time
                
                results[f"threshold_{threshold}"] = {
                    "segments": segments,
                    "num_segments": len(segments),
                    "total_speech_duration": sum(end - start for start, end in segments),
                    "processing_time": processing_time
                }
                
                print(f"    发现 {len(segments)} 个语音段")
                for i, (start, end) in enumerate(segments):
                    print(f"      段{i+1}: {start:.2f}s - {end:.2f}s ({end-start:.2f}s)")
            
            return {
                "method": "SimpleEnergyVAD",
                "status": "success",
                "results": results
            }
            
        except Exception as e:
            return {
                "method": "SimpleEnergyVAD",
                "status": "error",
                "error": str(e),
                "traceback": traceback.format_exc()
            }
    
    def test_funasr_vad(self, audio_file: str, vad_model_path: Optional[str] = None) -> Dict:
        """测试FunASR VAD"""
        print("\n🔍 测试FunASR VAD...")
        
        if not UTILS_AVAILABLE:
            return {
                "method": "FunASR_VAD",
                "status": "skipped",
                "reason": "utils模块不可用"
            }
        
        try:
            # 加载VAD模型
            if vad_model_path and os.path.exists(vad_model_path):
                print(f"  加载本地VAD模型: {vad_model_path}")
                vad_model = load_vad_model(vad_model_path)
            else:
                print("  本地VAD模型路径无效，跳过FunASR VAD测试")
                return {
                    "method": "FunASR_VAD",
                    "status": "skipped",
                    "reason": "无有效的本地VAD模型路径"
                }
            
            if vad_model is None:
                return {
                    "method": "FunASR_VAD",
                    "status": "error",
                    "error": "VAD模型加载失败"
                }
            
            # 基础VAD分割
            print("  执行基础VAD分割...")
            start_time = time.time()
            segments_basic = vad_segment(audio_file, vad_model)
            basic_time = time.time() - start_time
            
            # 优化VAD分割（针对两人对话）
            print("  执行优化VAD分割...")
            start_time = time.time()
            segments_optimized = vad_segment_for_two_person(audio_file, vad_model)
            optimized_time = time.time() - start_time
            
            return {
                "method": "FunASR_VAD",
                "status": "success",
                "basic_segments": {
                    "segments": segments_basic,
                    "num_segments": len(segments_basic),
                    "processing_time": basic_time
                },
                "optimized_segments": {
                    "segments": segments_optimized,
                    "num_segments": len(segments_optimized),
                    "processing_time": optimized_time
                }
            }
            
        except Exception as e:
            return {
                "method": "FunASR_VAD",
                "status": "error",
                "error": str(e),
                "traceback": traceback.format_exc()
            }
    
    def test_audio_quality(self, audio_file: str) -> Dict:
        """测试音频质量检查"""
        print("\n🔍 测试音频质量检查...")
        
        try:
            results = {}
            
            # 使用预处理器的质量检查
            if self.preprocessor:
                results["preprocessor_quality"] = check_audio_quality(audio_file)
            
            # 使用语音识别的质量检查
            if UTILS_AVAILABLE:
                results["speech_quality"] = check_audio_quality_for_speaker_recognition(audio_file)
            
            return {
                "method": "AudioQuality",
                "status": "success",
                "results": results
            }
            
        except Exception as e:
            return {
                "method": "AudioQuality",
                "status": "error",
                "error": str(e),
                "traceback": traceback.format_exc()
            }
    
    def evaluate_vad_accuracy(self, predicted_segments: List[Tuple[float, float]], 
                             ground_truth_segments: List[Tuple[float, float]]) -> Dict:
        """
        评估VAD准确性
        
        Args:
            predicted_segments: 预测的语音段
            ground_truth_segments: 真实的语音段
            
        Returns:
            准确性评估结果
        """
        try:
            # 将段转换为时间轴
            max_time = max(
                max(end for _, end in predicted_segments) if predicted_segments else 0,
                max(end for _, end in ground_truth_segments) if ground_truth_segments else 0
            )
            
            if max_time == 0:
                return {"error": "无有效时间段"}
            
            # 创建时间轴（100ms分辨率）
            time_resolution = 0.1  # 100ms
            time_axis = np.arange(0, max_time, time_resolution)
            
            # 创建标签数组
            predicted_labels = np.zeros(len(time_axis), dtype=bool)
            ground_truth_labels = np.zeros(len(time_axis), dtype=bool)
            
            # 填充预测标签
            for start, end in predicted_segments:
                start_idx = int(start / time_resolution)
                end_idx = int(end / time_resolution)
                predicted_labels[start_idx:end_idx] = True
            
            # 填充真实标签
            for start, end in ground_truth_segments:
                start_idx = int(start / time_resolution)
                end_idx = int(end / time_resolution)
                ground_truth_labels[start_idx:end_idx] = True
            
            # 计算混淆矩阵
            tp = np.sum(predicted_labels & ground_truth_labels)  # 真正例
            fp = np.sum(predicted_labels & ~ground_truth_labels)  # 假正例
            fn = np.sum(~predicted_labels & ground_truth_labels)  # 假负例
            tn = np.sum(~predicted_labels & ~ground_truth_labels)  # 真负例
            
            # 计算指标
            precision = tp / (tp + fp) if (tp + fp) > 0 else 0
            recall = tp / (tp + fn) if (tp + fn) > 0 else 0
            f1_score = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0
            accuracy = (tp + tn) / len(time_axis)
            
            return {
                "precision": precision,
                "recall": recall,
                "f1_score": f1_score,
                "accuracy": accuracy,
                "confusion_matrix": {
                    "true_positive": int(tp),
                    "false_positive": int(fp),
                    "false_negative": int(fn),
                    "true_negative": int(tn)
                }
            }
            
        except Exception as e:
            return {"error": f"准确性评估失败: {str(e)}"}
    
    def plot_vad_results(self, audio_file: str, results: Dict, output_dir: str):
        """绘制VAD结果图"""
        print("\n📊 绘制VAD结果图...")
        
        try:
            # 加载音频
            audio, sr = sf.read(audio_file)
            time_axis = np.linspace(0, len(audio) / sr, len(audio))
            
            # 创建图形
            fig, axes = plt.subplots(3, 1, figsize=(15, 10))
            fig.suptitle('VAD测试结果对比', fontsize=16)
            
            # 绘制原始音频
            axes[0].plot(time_axis, audio, 'b-', alpha=0.7, linewidth=0.5)
            axes[0].set_title('原始音频波形')
            axes[0].set_ylabel('振幅')
            axes[0].grid(True, alpha=0.3)
            
            # 绘制简单能量VAD结果
            if "simple_energy_vad" in results and results["simple_energy_vad"]["status"] == "success":
                # 选择最佳阈值的结果
                best_result = results["simple_energy_vad"]["results"]["threshold_0.05"]
                segments = best_result["segments"]
                
                axes[1].plot(time_axis, audio, 'b-', alpha=0.3, linewidth=0.5)
                for start, end in segments:
                    axes[1].axvspan(start, end, alpha=0.3, color='red', label='语音段')
                axes[1].set_title('简单能量VAD结果')
                axes[1].set_ylabel('振幅')
                axes[1].grid(True, alpha=0.3)
                axes[1].legend()
            
            # 绘制FunASR VAD结果
            if "funasr_vad" in results and results["funasr_vad"]["status"] == "success":
                if "basic_segments" in results["funasr_vad"]:
                    segments = results["funasr_vad"]["basic_segments"]["segments"]
                    
                    axes[2].plot(time_axis, audio, 'b-', alpha=0.3, linewidth=0.5)
                    for start, end in segments:
                        start_sec = start / 1000  # 转换为秒
                        end_sec = end / 1000
                        axes[2].axvspan(start_sec, end_sec, alpha=0.3, color='green', label='语音段')
                    axes[2].set_title('FunASR VAD结果')
                    axes[2].set_ylabel('振幅')
                    axes[2].set_xlabel('时间 (秒)')
                    axes[2].grid(True, alpha=0.3)
                    axes[2].legend()
            
            plt.tight_layout()
            
            # 保存图像
            output_file = os.path.join(output_dir, "vad_results.png")
            plt.savefig(output_file, dpi=300, bbox_inches='tight')
            plt.close()
            
            print(f"✅ VAD结果图已保存: {output_file}")
            
        except Exception as e:
            print(f"❌ 绘制VAD结果图失败: {str(e)}")
    
    def run_comprehensive_test(self, vad_model_path: Optional[str] = None) -> Dict:
        """运行综合VAD测试"""
        print("🚀 开始VAD综合测试...")
        
        # 创建测试音频
        test_audio = self.create_test_audio()
        
        # 定义真实语音段（基于生成的测试音频）
        ground_truth_segments = [
            (0.0, 2.0),    # 语音段1
            (3.0, 6.0),    # 语音段2
            (7.0, 10.0)    # 语音段3
        ]
        
        results = {
            "test_audio": test_audio,
            "ground_truth_segments": ground_truth_segments,
            "test_timestamp": time.strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # 测试音频质量
        results["audio_quality"] = self.test_audio_quality(test_audio)
        
        # 测试简单能量VAD
        results["simple_energy_vad"] = self.test_simple_energy_vad(test_audio)
        
        # 测试FunASR VAD
        results["funasr_vad"] = self.test_funasr_vad(test_audio, vad_model_path)
        
        # 评估准确性
        if results["simple_energy_vad"]["status"] == "success":
            # 使用最佳阈值的结果
            best_segments = results["simple_energy_vad"]["results"]["threshold_0.05"]["segments"]
            results["simple_energy_vad"]["accuracy"] = self.evaluate_vad_accuracy(
                best_segments, ground_truth_segments
            )
        
        if results["funasr_vad"]["status"] == "success" and "basic_segments" in results["funasr_vad"]:
            # 转换FunASR结果为秒
            funasr_segments = [(s/1000, e/1000) for s, e in results["funasr_vad"]["basic_segments"]["segments"]]
            results["funasr_vad"]["accuracy"] = self.evaluate_vad_accuracy(
                funasr_segments, ground_truth_segments
            )
        
        # 绘制结果图
        self.plot_vad_results(test_audio, results, self.test_audio_dir)
        
        # 保存结果
        results_file = os.path.join(self.test_audio_dir, "vad_test_results.json")
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"✅ 测试结果已保存: {results_file}")
        
        return results
    
    def print_test_summary(self, results: Dict):
        """打印测试摘要"""
        print("\n" + "="*60)
        print("📋 VAD测试摘要")
        print("="*60)
        
        print(f"📅 测试时间: {results.get('test_timestamp', 'Unknown')}")
        print(f"🎵 测试音频: {results.get('test_audio', 'Unknown')}")
        print(f"📊 真实语音段: {len(results.get('ground_truth_segments', []))} 个")
        
        # 音频质量摘要
        if "audio_quality" in results and results["audio_quality"]["status"] == "success":
            print("\n🎧 音频质量:")
            quality_results = results["audio_quality"]["results"]
            if "preprocessor_quality" in quality_results:
                pq = quality_results["preprocessor_quality"]
                print(f"  - 质量分数: {pq.get('quality_score', 0)}/100")
                print(f"  - 是否良好: {'是' if pq.get('is_good_quality', False) else '否'}")
        
        # 简单能量VAD摘要
        if "simple_energy_vad" in results and results["simple_energy_vad"]["status"] == "success":
            print("\n⚡ 简单能量VAD:")
            sev = results["simple_energy_vad"]
            best_result = sev["results"]["threshold_0.05"]
            print(f"  - 检测到语音段: {best_result['num_segments']} 个")
            print(f"  - 总语音时长: {best_result['total_speech_duration']:.2f} 秒")
            print(f"  - 处理时间: {best_result['processing_time']:.3f} 秒")
            
            if "accuracy" in sev:
                acc = sev["accuracy"]
                print(f"  - 准确率: {acc.get('accuracy', 0):.3f}")
                print(f"  - F1分数: {acc.get('f1_score', 0):.3f}")
        
        # FunASR VAD摘要
        if "funasr_vad" in results:
            fv = results["funasr_vad"]
            if fv["status"] == "success":
                print("\n🤖 FunASR VAD:")
                if "basic_segments" in fv:
                    bs = fv["basic_segments"]
                    print(f"  - 检测到语音段: {bs['num_segments']} 个")
                    print(f"  - 处理时间: {bs['processing_time']:.3f} 秒")
                
                if "accuracy" in fv:
                    acc = fv["accuracy"]
                    print(f"  - 准确率: {acc.get('accuracy', 0):.3f}")
                    print(f"  - F1分数: {acc.get('f1_score', 0):.3f}")
            else:
                print(f"\n🤖 FunASR VAD: {fv['status']} - {fv.get('reason', fv.get('error', 'Unknown'))}")
        
        print("\n" + "="*60)

def main():
    """主测试函数"""
    print("🎯 VAD功能测试脚本")
    print("="*50)
    
    # 检查命令行参数
    import argparse
    parser = argparse.ArgumentParser(description="VAD功能测试脚本")
    parser.add_argument("--vad-model", type=str, help="VAD模型路径")
    parser.add_argument("--output-dir", type=str, default="test_results", help="输出目录")
    args = parser.parse_args()
    
    # 创建测试器
    tester = VADTester(args.output_dir)
    
    # 运行综合测试
    results = tester.run_comprehensive_test(args.vad_model)
    
    # 打印摘要
    tester.print_test_summary(results)
    
    print(f"\n✅ 测试完成！详细结果请查看: {args.output_dir}")

if __name__ == "__main__":
    main() 