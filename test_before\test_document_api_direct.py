#!/usr/bin/env python3
"""
直接测试文档管理API
"""

import requests
import json

def test_document_api():
    """测试文档管理API"""
    print("🔍 测试文档管理API...")
    
    # 1. 先登录获取token
    print("\n1. 登录获取token...")
    login_data = {"username": "admin", "password": "admin123"}
    
    try:
        response = requests.post("http://localhost:8000/api/v1/auth/login", json=login_data, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            token = data.get("access_token")
            print(f"   ✅ 登录成功，获取到token")
            
            if not token:
                print("   ❌ 未获取到token")
                return False
                
            # 2. 测试文档列表API
            print("\n2. 测试文档列表API...")
            headers = {"Authorization": f"Bearer {token}"}
            
            try:
                doc_response = requests.get("http://localhost:8000/api/v1/documents/documents", headers=headers, timeout=10)
                
                print(f"   状态码: {doc_response.status_code}")
                
                if doc_response.status_code == 200:
                    doc_data = doc_response.json()
                    print(f"   ✅ 文档列表API正常")
                    print(f"   文档数量: {doc_data.get('total', 0)}")
                    print(f"   响应数据: {json.dumps(doc_data, indent=2, ensure_ascii=False)[:300]}...")
                    return True
                else:
                    print(f"   ❌ 文档列表API失败")
                    print(f"   响应内容: {doc_response.text}")
                    return False
                    
            except Exception as e:
                print(f"   ❌ 文档列表API请求失败: {e}")
                return False
                
        else:
            print(f"   ❌ 登录失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 登录请求失败: {e}")
        return False

def test_frontend_document_api():
    """测试前端代理的文档管理API"""
    print("\n🔍 测试前端代理的文档管理API...")
    
    # 1. 通过前端代理登录
    print("\n1. 通过前端代理登录...")
    login_data = {"username": "admin", "password": "admin123"}
    
    try:
        response = requests.post("http://localhost:3000/api/v1/auth/login", json=login_data, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            token = data.get("access_token")
            print(f"   ✅ 前端代理登录成功")
            
            if not token:
                print("   ❌ 未获取到token")
                return False
                
            # 2. 通过前端代理测试文档列表API
            print("\n2. 通过前端代理测试文档列表API...")
            headers = {"Authorization": f"Bearer {token}"}
            
            try:
                doc_response = requests.get("http://localhost:3000/api/v1/documents/documents", headers=headers, timeout=10)
                
                print(f"   状态码: {doc_response.status_code}")
                
                if doc_response.status_code == 200:
                    doc_data = doc_response.json()
                    print(f"   ✅ 前端代理文档列表API正常")
                    print(f"   文档数量: {doc_data.get('total', 0)}")
                    return True
                else:
                    print(f"   ❌ 前端代理文档列表API失败")
                    print(f"   响应内容: {doc_response.text}")
                    return False
                    
            except Exception as e:
                print(f"   ❌ 前端代理文档列表API请求失败: {e}")
                return False
                
        else:
            print(f"   ❌ 前端代理登录失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 前端代理请求失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始文档管理API测试...")
    print("=" * 50)
    
    # 测试直接后端API
    backend_ok = test_document_api()
    
    # 测试前端代理API
    frontend_ok = test_frontend_document_api()
    
    print("\n" + "=" * 50)
    print("🎉 测试完成！")
    
    print(f"\n📊 测试结果:")
    print(f"   后端直接API: {'✅ 正常' if backend_ok else '❌ 失败'}")
    print(f"   前端代理API: {'✅ 正常' if frontend_ok else '❌ 失败'}")
    
    if backend_ok and frontend_ok:
        print("\n💡 API测试正常，问题可能在前端路由或页面组件")
    elif backend_ok and not frontend_ok:
        print("\n💡 后端API正常，前端代理有问题")
    elif not backend_ok:
        print("\n💡 后端API有问题，需要检查:")
        print("   1. 文档管理路由是否正确注册")
        print("   2. 数据库连接是否正常")
        print("   3. Worker是否正常运行")

if __name__ == "__main__":
    main()
