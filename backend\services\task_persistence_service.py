"""
任务持久化服务
负责任务状态的数据库持久化、查询和恢复
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc, asc
from datetime import datetime, timezone, timedelta
from loguru import logger

from backend.models.task_models import TaskRecord, TaskProgressLog, TaskDependency, TaskStatus, TaskType
from backend.core.database import get_db_session


class TaskPersistenceService:
    """任务持久化服务"""
    
    def __init__(self):
        pass
    
    def create_task_record(
        self,
        db: Session,
        task_id: str,
        user_id: str,
        task_type: str,
        task_name: str,
        task_args: Dict = None,
        task_kwargs: Dict = None,
        queue_name: str = None,
        metadata: Dict = None
    ) -> TaskRecord:
        """创建任务记录"""
        try:
            # 验证和标准化任务类型
            validated_task_type = task_type.strip() if task_type else "unknown"

            task_record = TaskRecord(
                task_id=task_id,
                user_id=user_id,
                task_type=validated_task_type,
                task_name=task_name,
                status=TaskStatus.PENDING,
                task_args=task_args or {},
                task_kwargs=task_kwargs or {},
                queue_name=queue_name,
                task_metadata=metadata or {}
            )

            db.add(task_record)
            db.commit()
            db.refresh(task_record)

            logger.info(f"✅ 创建任务记录: {task_id}, 类型: {validated_task_type}, 名称: {task_name}")
            return task_record
            
        except Exception as e:
            db.rollback()
            logger.error(f"创建任务记录失败: {task_id}, {e}")
            raise
    
    def update_task_status(
        self,
        db: Session,
        task_id: str,
        status: TaskStatus,
        progress_percentage: float = None,
        progress_detail: str = None,
        progress_stage: str = None,
        worker_name: str = None,
        error_message: str = None,
        traceback: str = None,
        result: Dict = None
    ) -> Optional[TaskRecord]:
        """更新任务状态"""
        try:
            task_record = db.query(TaskRecord).filter(TaskRecord.task_id == task_id).first()

            if not task_record:
                logger.warning(f"任务记录不存在: {task_id}")
                return None

            # 记录状态变更
            old_status = task_record.status
            old_progress = task_record.progress_percentage

            # 更新状态
            task_record.status = status

            if progress_percentage is not None:
                task_record.progress_percentage = progress_percentage

            if progress_detail is not None:
                task_record.progress_detail = progress_detail

            if progress_stage is not None:
                task_record.progress_stage = progress_stage
            
            if worker_name is not None:
                task_record.worker_name = worker_name
            
            if error_message is not None:
                task_record.error_message = error_message
            
            if traceback is not None:
                task_record.traceback = traceback
            
            if result is not None:
                task_record.result = result
            
            # 更新时间戳
            if status == TaskStatus.STARTED and not task_record.started_at:
                task_record.started_at = datetime.now(timezone.utc)
            elif status in [TaskStatus.SUCCESS, TaskStatus.FAILURE, TaskStatus.REVOKED]:
                task_record.completed_at = datetime.now(timezone.utc)

            db.commit()
            db.refresh(task_record)

            # 详细的状态变更日志
            logger.info(f"📝 任务状态更新: {task_id}")
            logger.info(f"   状态变更: {old_status} -> {status}")
            if progress_percentage is not None:
                logger.info(f"   进度变更: {old_progress}% -> {progress_percentage}%")
            if progress_detail:
                logger.info(f"   详情: {progress_detail}")

            return task_record
            
        except Exception as e:
            db.rollback()
            logger.error(f"更新任务状态失败: {task_id}, {e}")
            raise
    
    def log_task_progress(
        self,
        db: Session,
        task_id: str,
        percentage: float,
        detail: str = None,
        stage: str = None,
        metadata: Dict = None
    ) -> TaskProgressLog:
        """记录任务进度日志"""
        try:
            progress_log = TaskProgressLog(
                task_id=task_id,
                percentage=percentage,
                detail=detail,
                stage=stage,
                log_metadata=metadata or {}
            )
            
            db.add(progress_log)
            db.commit()
            db.refresh(progress_log)
            
            # 同时更新任务记录的进度
            self.update_task_status(
                db, task_id, TaskStatus.STARTED,
                progress_percentage=percentage,
                progress_detail=detail,
                progress_stage=stage
            )
            
            return progress_log
            
        except Exception as e:
            db.rollback()
            logger.error(f"记录任务进度失败: {task_id}, {e}")
            raise
    
    def get_task_record(self, db: Session, task_id: str) -> Optional[TaskRecord]:
        """获取任务记录"""
        try:
            return db.query(TaskRecord).filter(TaskRecord.task_id == task_id).first()
        except Exception as e:
            logger.error(f"获取任务记录失败: {task_id}, {e}")
            return None
    
    def get_user_tasks(
        self,
        db: Session,
        user_id: str,
        status: TaskStatus = None,
        task_type: str = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[TaskRecord]:
        """获取用户的任务列表"""
        try:
            query = db.query(TaskRecord).filter(TaskRecord.user_id == user_id)
            
            if status:
                query = query.filter(TaskRecord.status == status)
            
            if task_type:
                query = query.filter(TaskRecord.task_type == task_type)
            
            return query.order_by(desc(TaskRecord.created_at)).offset(offset).limit(limit).all()
            
        except Exception as e:
            logger.error(f"获取用户任务列表失败: {user_id}, {e}")
            return []
    
    def get_task_progress_history(
        self,
        db: Session,
        task_id: str,
        limit: int = 100
    ) -> List[TaskProgressLog]:
        """获取任务进度历史"""
        try:
            return db.query(TaskProgressLog).filter(
                TaskProgressLog.task_id == task_id
            ).order_by(asc(TaskProgressLog.timestamp)).limit(limit).all()
            
        except Exception as e:
            logger.error(f"获取任务进度历史失败: {task_id}, {e}")
            return []
    
    def get_failed_tasks(
        self,
        db: Session,
        user_id: str = None,
        hours: int = 24
    ) -> List[TaskRecord]:
        """获取失败的任务"""
        try:
            since = datetime.now(timezone.utc) - timedelta(hours=hours)
            query = db.query(TaskRecord).filter(
                and_(
                    TaskRecord.status == TaskStatus.FAILURE,
                    TaskRecord.created_at >= since
                )
            )
            
            if user_id:
                query = query.filter(TaskRecord.user_id == user_id)
            
            return query.order_by(desc(TaskRecord.created_at)).all()
            
        except Exception as e:
            logger.error(f"获取失败任务失败: {e}")
            return []
    
    def get_retryable_tasks(
        self,
        db: Session,
        user_id: str = None
    ) -> List[TaskRecord]:
        """获取可重试的任务"""
        try:
            query = db.query(TaskRecord).filter(
                and_(
                    TaskRecord.status == TaskStatus.FAILURE,
                    TaskRecord.retry_count < TaskRecord.max_retries
                )
            )
            
            if user_id:
                query = query.filter(TaskRecord.user_id == user_id)
            
            return query.order_by(desc(TaskRecord.created_at)).all()
            
        except Exception as e:
            logger.error(f"获取可重试任务失败: {e}")
            return []
    
    def increment_retry_count(self, db: Session, task_id: str) -> Optional[TaskRecord]:
        """增加重试次数"""
        try:
            task_record = db.query(TaskRecord).filter(TaskRecord.task_id == task_id).first()
            
            if task_record:
                task_record.retry_count += 1
                db.commit()
                db.refresh(task_record)
                
                logger.info(f"增加任务重试次数: {task_id} -> {task_record.retry_count}")
                return task_record
            
            return None
            
        except Exception as e:
            db.rollback()
            logger.error(f"增加重试次数失败: {task_id}, {e}")
            raise
    
    def cleanup_old_tasks(
        self,
        db: Session,
        days: int = 30,
        keep_failed: bool = True
    ) -> int:
        """清理旧任务记录"""
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days)
            
            query = db.query(TaskRecord).filter(TaskRecord.created_at < cutoff_date)
            
            if keep_failed:
                query = query.filter(TaskRecord.status != TaskStatus.FAILURE)
            
            # 先删除相关的进度日志
            task_ids = [task.task_id for task in query.all()]
            if task_ids:
                db.query(TaskProgressLog).filter(
                    TaskProgressLog.task_id.in_(task_ids)
                ).delete(synchronize_session=False)
            
            # 删除任务记录
            deleted_count = query.delete(synchronize_session=False)
            db.commit()
            
            logger.info(f"清理了 {deleted_count} 个旧任务记录")
            return deleted_count
            
        except Exception as e:
            db.rollback()
            logger.error(f"清理旧任务记录失败: {e}")
            return 0
    
    def get_task_statistics(
        self,
        db: Session,
        user_id: str = None,
        days: int = 7
    ) -> Dict[str, Any]:
        """获取任务统计信息"""
        try:
            since = datetime.now(timezone.utc) - timedelta(days=days)
            query = db.query(TaskRecord).filter(TaskRecord.created_at >= since)
            
            if user_id:
                query = query.filter(TaskRecord.user_id == user_id)
            
            all_tasks = query.all()
            
            stats = {
                "total_tasks": len(all_tasks),
                "success_tasks": len([t for t in all_tasks if t.status == TaskStatus.SUCCESS]),
                "failed_tasks": len([t for t in all_tasks if t.status == TaskStatus.FAILURE]),
                "pending_tasks": len([t for t in all_tasks if t.status == TaskStatus.PENDING]),
                "running_tasks": len([t for t in all_tasks if t.status == TaskStatus.STARTED]),
                "task_types": {},
                "average_duration": 0
            }
            
            # 按任务类型统计
            for task in all_tasks:
                task_type = task.task_type
                if task_type not in stats["task_types"]:
                    stats["task_types"][task_type] = 0
                stats["task_types"][task_type] += 1
            
            # 计算平均执行时间
            completed_tasks = [t for t in all_tasks if t.completed_at and t.started_at]
            if completed_tasks:
                total_duration = sum([
                    (t.completed_at - t.started_at).total_seconds()
                    for t in completed_tasks
                ])
                stats["average_duration"] = total_duration / len(completed_tasks)
            
            return stats
            
        except Exception as e:
            logger.error(f"获取任务统计失败: {e}")
            return {}


# 全局任务持久化服务实例
task_persistence_service = TaskPersistenceService()


def get_task_persistence_service() -> TaskPersistenceService:
    """获取任务持久化服务实例"""
    return task_persistence_service
