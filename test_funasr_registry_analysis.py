#!/usr/bin/env python3
"""
FunASR注册机制深度分析脚本
用于分析VAD模型注册问题的根本原因
"""

import os
import sys
import logging
from pathlib import Path

# 设置项目根目录
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def analyze_funasr_registry():
    """分析FunASR注册机制"""
    print("🔍 开始分析FunASR注册机制...")
    
    try:
        # 导入FunASR
        from funasr import AutoModel
        from funasr.register import tables
        
        print("✅ FunASR导入成功")
        
        # 1. 打印所有注册表
        print("\n📋 FunASR注册表信息:")
        print("=" * 50)
        tables.print()
        print("=" * 50)
        
        # 2. 检查VAD相关的注册信息
        print("\n🔍 检查VAD相关注册信息:")
        
        # 尝试获取各种注册表
        registry_tables = [
            'model_classes',
            'frontend_classes', 
            'specaug_classes',
            'normalize_classes',
            'encoder_classes',
            'decoder_classes',
            'predictor_classes',
            'postprocessor_classes',
            'preprocessor_classes'
        ]
        
        for table_name in registry_tables:
            try:
                table = getattr(tables, table_name, None)
                if table:
                    print(f"\n📊 {table_name}:")
                    for key in table.keys():
                        if 'vad' in key.lower() or 'fsmn' in key.lower():
                            print(f"  - {key}")
            except Exception as e:
                print(f"  ❌ 无法访问 {table_name}: {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ FunASR导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def test_vad_model_loading_methods():
    """测试不同的VAD模型加载方法"""
    print("\n🧪 测试不同的VAD模型加载方法...")
    
    # VAD模型路径
    vad_model_path = project_root / "models" / "fsmn_vad_zh"
    
    if not vad_model_path.exists():
        print(f"❌ VAD模型路径不存在: {vad_model_path}")
        return False
    
    print(f"📁 VAD模型路径: {vad_model_path}")
    
    # 检查模型文件
    required_files = ['config.yaml', 'model.pt', 'configuration.json']
    for file_name in required_files:
        file_path = vad_model_path / file_name
        if file_path.exists():
            print(f"  ✅ {file_name}")
        else:
            print(f"  ❌ {file_name} (缺失)")
    
    try:
        from funasr import AutoModel
        
        # 方法1: 使用模型标识符
        print("\n🔬 方法1: 使用模型标识符 'fsmn-vad'")
        try:
            vad_model_1 = AutoModel(
                model="fsmn-vad",
                device="cpu",
                local_files_only=True,
                disable_update=True,
                offline=True
            )
            print("  ✅ 方法1成功")
        except Exception as e:
            print(f"  ❌ 方法1失败: {e}")
        
        # 方法2: 使用本地路径
        print("\n🔬 方法2: 使用本地路径")
        try:
            vad_model_2 = AutoModel(
                model=str(vad_model_path),
                device="cpu",
                local_files_only=True,
                disable_update=True,
                offline=True
            )
            print("  ✅ 方法2成功")
        except Exception as e:
            print(f"  ❌ 方法2失败: {e}")
        
        # 方法3: 使用ModelScope标识符
        print("\n🔬 方法3: 使用ModelScope标识符")
        try:
            # 从configuration.json读取标识符
            import json
            config_file = vad_model_path / "configuration.json"
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    ms_model_id = config.get('model_name_in_hub', {}).get('ms', '')
                    if ms_model_id:
                        print(f"  📋 ModelScope标识符: {ms_model_id}")
                        vad_model_3 = AutoModel(
                            model=ms_model_id,
                            device="cpu",
                            local_files_only=True,
                            disable_update=True,
                            offline=True
                        )
                        print("  ✅ 方法3成功")
                    else:
                        print("  ❌ 未找到ModelScope标识符")
        except Exception as e:
            print(f"  ❌ 方法3失败: {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ FunASR导入失败: {e}")
        return False

def test_sensevoice_with_different_vad_configs():
    """测试SenseVoice与不同VAD配置的组合"""
    print("\n🎯 测试SenseVoice与不同VAD配置的组合...")
    
    sensevoice_path = project_root / "models" / "SenseVoiceSmall"
    vad_model_path = project_root / "models" / "fsmn_vad_zh"
    
    if not sensevoice_path.exists():
        print(f"❌ SenseVoice模型路径不存在: {sensevoice_path}")
        return False
    
    if not vad_model_path.exists():
        print(f"❌ VAD模型路径不存在: {vad_model_path}")
        return False
    
    try:
        from funasr import AutoModel
        
        # 配置1: 不使用VAD
        print("\n🔬 配置1: 不使用VAD")
        try:
            model_1 = AutoModel(
                model=str(sensevoice_path),
                trust_remote_code=True,
                device="cpu",
                local_files_only=True,
                disable_update=True,
                offline=True
            )
            print("  ✅ 配置1成功 - 无VAD")
        except Exception as e:
            print(f"  ❌ 配置1失败: {e}")
        
        # 配置2: 使用VAD标识符
        print("\n🔬 配置2: 使用VAD标识符")
        try:
            model_2 = AutoModel(
                model=str(sensevoice_path),
                trust_remote_code=True,
                device="cpu",
                local_files_only=True,
                disable_update=True,
                offline=True,
                vad_model="fsmn-vad",
                vad_kwargs={"max_single_segment_time": 30000}
            )
            print("  ✅ 配置2成功 - VAD标识符")
        except Exception as e:
            print(f"  ❌ 配置2失败: {e}")
        
        # 配置3: 使用VAD本地路径
        print("\n🔬 配置3: 使用VAD本地路径")
        try:
            model_3 = AutoModel(
                model=str(sensevoice_path),
                trust_remote_code=True,
                device="cpu",
                local_files_only=True,
                disable_update=True,
                offline=True,
                vad_model=str(vad_model_path),
                vad_kwargs={"max_single_segment_time": 30000}
            )
            print("  ✅ 配置3成功 - VAD本地路径")
        except Exception as e:
            print(f"  ❌ 配置3失败: {e}")
        
        return True
        
    except ImportError as e:
        print(f"❌ FunASR导入失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 FunASR注册机制深度分析")
    print("=" * 60)
    
    # 设置离线环境
    os.environ['HF_HUB_OFFLINE'] = '1'
    os.environ['HF_DATASETS_OFFLINE'] = '1'
    os.environ['TRANSFORMERS_OFFLINE'] = '1'
    
    print("🔧 离线环境变量已设置")
    
    # 分析注册机制
    if analyze_funasr_registry():
        print("\n✅ 注册机制分析完成")
    else:
        print("\n❌ 注册机制分析失败")
        return
    
    # 测试VAD模型加载
    if test_vad_model_loading_methods():
        print("\n✅ VAD模型加载测试完成")
    else:
        print("\n❌ VAD模型加载测试失败")
    
    # 测试SenseVoice+VAD组合
    if test_sensevoice_with_different_vad_configs():
        print("\n✅ SenseVoice+VAD组合测试完成")
    else:
        print("\n❌ SenseVoice+VAD组合测试失败")
    
    print("\n🎉 分析完成！")

if __name__ == "__main__":
    main()
