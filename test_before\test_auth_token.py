#!/usr/bin/env python3
"""
测试认证token问题
"""

import requests
import json

def test_auth_flow():
    """测试完整的认证流程"""
    print("🔍 测试认证流程...")
    
    # 1. 登录获取token
    print("\n1. 登录获取token...")
    login_data = {"username": "admin", "password": "admin123"}
    
    try:
        response = requests.post("http://localhost:8000/api/v1/auth/login", json=login_data, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            token = data.get("access_token")
            print(f"   ✅ 登录成功")
            print(f"   Token: {token[:50]}..." if token else "   ❌ 未获取到token")
            
            if not token:
                return False
                
            # 2. 使用token访问 /me 接口
            print("\n2. 使用token访问用户信息...")
            headers = {"Authorization": f"Bearer {token}"}
            
            try:
                me_response = requests.get("http://localhost:8000/api/v1/auth/me", headers=headers, timeout=10)
                
                print(f"   状态码: {me_response.status_code}")
                print(f"   响应头: {dict(me_response.headers)}")
                
                if me_response.status_code == 200:
                    user_data = me_response.json()
                    print(f"   ✅ 用户信息获取成功: {user_data.get('username')}")
                    return True
                else:
                    print(f"   ❌ 用户信息获取失败")
                    print(f"   响应内容: {me_response.text}")
                    return False
                    
            except Exception as e:
                print(f"   ❌ 用户信息请求失败: {e}")
                return False
                
        else:
            print(f"   ❌ 登录失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 登录请求失败: {e}")
        return False

def test_frontend_auth():
    """测试前端认证流程"""
    print("\n🔍 测试前端认证流程...")
    
    # 通过前端代理测试
    try:
        # 1. 通过前端代理登录
        print("\n1. 通过前端代理登录...")
        login_data = {"username": "admin", "password": "admin123"}
        
        response = requests.post("http://localhost:3001/api/v1/auth/login", json=login_data, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            token = data.get("access_token")
            print(f"   ✅ 前端代理登录成功")
            print(f"   Token: {token[:50]}..." if token else "   ❌ 未获取到token")
            
            if not token:
                return False
                
            # 2. 通过前端代理访问用户信息
            print("\n2. 通过前端代理访问用户信息...")
            headers = {"Authorization": f"Bearer {token}"}
            
            me_response = requests.get("http://localhost:3001/api/v1/auth/me", headers=headers, timeout=10)
            
            print(f"   状态码: {me_response.status_code}")
            
            if me_response.status_code == 200:
                user_data = me_response.json()
                print(f"   ✅ 前端代理用户信息获取成功: {user_data.get('username')}")
                return True
            else:
                print(f"   ❌ 前端代理用户信息获取失败")
                print(f"   响应内容: {me_response.text}")
                return False
                
        else:
            print(f"   ❌ 前端代理登录失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 前端代理请求失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始认证token测试...")
    print("=" * 50)
    
    # 测试直接后端认证
    backend_ok = test_auth_flow()
    
    # 测试前端代理认证
    frontend_ok = test_frontend_auth()
    
    print("\n" + "=" * 50)
    print("🎉 测试完成！")
    
    print(f"\n📊 测试结果:")
    print(f"   后端直接认证: {'✅ 正常' if backend_ok else '❌ 失败'}")
    print(f"   前端代理认证: {'✅ 正常' if frontend_ok else '❌ 失败'}")
    
    if not backend_ok:
        print("\n💡 后端认证失败，可能的原因：")
        print("   1. JWT密钥配置问题")
        print("   2. Token格式或编码问题")
        print("   3. 数据库用户数据问题")
        print("   4. 认证中间件配置问题")
    
    if not frontend_ok:
        print("\n💡 前端代理认证失败，可能的原因：")
        print("   1. 前端代理配置问题")
        print("   2. CORS配置问题")
        print("   3. 前端token存储问题")

if __name__ == "__main__":
    main()
