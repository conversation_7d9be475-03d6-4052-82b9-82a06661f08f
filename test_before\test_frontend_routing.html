<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端路由测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-item {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔍 前端路由和认证测试</h1>
        
        <div class="test-item">
            <h3>1. 测试前端服务状态</h3>
            <button class="test-button" onclick="testFrontendStatus()">检查前端服务</button>
            <div id="frontend-status-result"></div>
        </div>

        <div class="test-item">
            <h3>2. 测试认证API</h3>
            <button class="test-button" onclick="testLogin()">测试登录</button>
            <button class="test-button" onclick="testUserInfo()">测试用户信息</button>
            <div id="auth-result"></div>
        </div>

        <div class="test-item">
            <h3>3. 测试文档管理API</h3>
            <button class="test-button" onclick="testDocumentAPI()">测试文档列表</button>
            <div id="document-api-result"></div>
        </div>

        <div class="test-item">
            <h3>4. 测试前端路由</h3>
            <button class="test-button" onclick="testRoutes()">测试路由访问</button>
            <div id="route-result"></div>
        </div>

        <div class="test-item">
            <h3>5. 直接跳转测试</h3>
            <button class="test-button" onclick="window.open('http://localhost:3001/documents', '_blank')">
                直接访问文档管理页面
            </button>
            <button class="test-button" onclick="window.open('http://localhost:3001/dashboard', '_blank')">
                访问控制台页面
            </button>
            <button class="test-button" onclick="window.open('http://localhost:3001/knowledge', '_blank')">
                访问知识库页面
            </button>
        </div>
    </div>

    <script>
        let authToken = null;

        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result ${type}">${message}</div>`;
        }

        async function testFrontendStatus() {
            try {
                const response = await fetch('http://localhost:3001/');
                if (response.ok) {
                    showResult('frontend-status-result', '✅ 前端服务正常运行', 'success');
                } else {
                    showResult('frontend-status-result', `❌ 前端服务异常: ${response.status}`, 'error');
                }
            } catch (error) {
                showResult('frontend-status-result', `❌ 前端服务连接失败: ${error.message}`, 'error');
            }
        }

        async function testLogin() {
            try {
                const response = await fetch('http://localhost:3001/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });

                if (response.ok) {
                    const data = await response.json();
                    authToken = data.access_token;
                    showResult('auth-result', `✅ 登录成功，获取到token: ${authToken.substring(0, 30)}...`, 'success');
                } else {
                    const errorData = await response.json();
                    showResult('auth-result', `❌ 登录失败: ${errorData.detail}`, 'error');
                }
            } catch (error) {
                showResult('auth-result', `❌ 登录请求失败: ${error.message}`, 'error');
            }
        }

        async function testUserInfo() {
            if (!authToken) {
                showResult('auth-result', '❌ 请先登录获取token', 'error');
                return;
            }

            try {
                const response = await fetch('http://localhost:3001/api/v1/auth/me', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    showResult('auth-result', `✅ 用户信息获取成功: ${data.username}`, 'success');
                } else {
                    const errorData = await response.json();
                    showResult('auth-result', `❌ 用户信息获取失败: ${errorData.detail}`, 'error');
                }
            } catch (error) {
                showResult('auth-result', `❌ 用户信息请求失败: ${error.message}`, 'error');
            }
        }

        async function testDocumentAPI() {
            if (!authToken) {
                showResult('document-api-result', '❌ 请先登录获取token', 'error');
                return;
            }

            try {
                const response = await fetch('http://localhost:3001/api/v1/documents/documents', {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    showResult('document-api-result', `✅ 文档API正常，文档数量: ${data.total}`, 'success');
                } else {
                    const errorData = await response.json();
                    showResult('document-api-result', `❌ 文档API失败: ${errorData.detail}`, 'error');
                }
            } catch (error) {
                showResult('document-api-result', `❌ 文档API请求失败: ${error.message}`, 'error');
            }
        }

        async function testRoutes() {
            const routes = [
                '/dashboard',
                '/documents',
                '/knowledge',
                '/speech',
                '/audio'
            ];

            let results = [];
            
            for (const route of routes) {
                try {
                    const response = await fetch(`http://localhost:3001${route}`);
                    if (response.ok) {
                        results.push(`✅ ${route}: 正常`);
                    } else {
                        results.push(`❌ ${route}: ${response.status}`);
                    }
                } catch (error) {
                    results.push(`❌ ${route}: 连接失败`);
                }
            }

            showResult('route-result', results.join('<br>'), 'info');
        }

        // 页面加载时自动测试前端状态
        window.onload = function() {
            testFrontendStatus();
        };
    </script>
</body>
</html>
