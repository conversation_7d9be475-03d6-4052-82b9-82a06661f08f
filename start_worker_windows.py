#!/usr/bin/env python3
"""
Windows兼容的Celery Worker启动脚本
解决Windows下的权限和多进程问题
"""

import os
import sys
import signal
import time
import gc
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_environment():
    """设置环境变量"""
    # 设置Python路径
    os.environ['PYTHONPATH'] = str(project_root)

    # 设置Celery配置 - 使用多线程模式
    os.environ['CELERY_POOL'] = 'threads'  # 使用线程池
    os.environ['CELERY_CONCURRENCY'] = '4'  # 4个线程并发

    # 禁用一些可能导致权限问题的功能
    os.environ['CELERY_DISABLE_RATE_LIMITS'] = 'true'
    os.environ['CELERY_WORKER_HIJACK_ROOT_LOGGER'] = 'false'

    print("✅ 环境变量设置完成（多线程模式）")

def check_redis_connection():
    """检查Redis连接"""
    try:
        import redis
        redis_client = redis.Redis.from_url("redis://localhost:6379/0")
        redis_client.ping()
        print("✅ Redis连接正常")
        return True
    except Exception as e:
        print(f"❌ Redis连接失败: {e}")
        print("请确保Redis服务正在运行")
        return False


def cleanup_resources():
    """清理系统资源"""
    try:
        import torch
        import gc

        # 清理CUDA缓存
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.synchronize()
            print("🧹 CUDA缓存已清理")

        # 强制垃圾回收
        gc.collect()
        print("🧹 内存垃圾回收完成")

    except Exception as e:
        print(f"⚠️ 资源清理失败: {e}")


def start_worker():
    """启动Celery Worker"""
    try:
        from backend.core.task_queue import celery_app
        import psutil
        import gc

        # 获取系统资源信息
        cpu_count = psutil.cpu_count()
        memory_gb = psutil.virtual_memory().total / (1024**3)

        # 动态调整并发数（保守策略）
        concurrency = min(max(2, cpu_count // 2), 4)  # 最少2个，最多4个线程

        print("🚀 启动Celery Worker (Windows优化多线程模式)...")
        print("配置信息:")
        print(f"  - Pool: threads (多线程)")
        print(f"  - Concurrency: {concurrency} (基于CPU核心数: {cpu_count})")
        print(f"  - 系统内存: {memory_gb:.1f}GB")
        print(f"  - Queues: document_processing, vectorization, ocr_processing, audio_processing, default, celery")
        print(f"  - Log Level: info")
        print(f"  - Max tasks per thread: 10")  # 降低以减少内存占用
        print(f"  - 内存优化: 启用")
        print("-" * 50)

        # 设置内存优化环境变量
        os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:512'
        os.environ['CUDA_LAUNCH_BLOCKING'] = '0'

        # 强制垃圾回收
        gc.collect()

        # 使用线程池启动worker
        celery_app.worker_main([
            'worker',
            '--pool=threads',  # 使用线程池
            f'--concurrency={concurrency}',  # 动态并发数
            '--loglevel=info',
            '--queues=document_processing,vectorization,ocr_processing,audio_processing,default,celery',  # 添加celery队列
            '--hostname=windows_optimized_worker@%h',
            '--max-tasks-per-child=10',  # 降低任务数以防止内存泄漏
            '--max-memory-per-child=1000000',  # 1GB内存限制
            '--without-gossip',  # 禁用gossip协议
            '--without-mingle',  # 禁用mingle
            '--without-heartbeat',  # 禁用心跳
            '--time-limit=1800',  # 30分钟任务超时
            '--soft-time-limit=1500',  # 25分钟软超时
        ])

    except KeyboardInterrupt:
        print("\n🛑 收到停止信号，正在关闭worker...")
        cleanup_resources()
        sys.exit(0)
    except Exception as e:
        print(f"❌ Worker启动失败: {e}")
        cleanup_resources()
        sys.exit(1)

def main():
    """主函数"""
    print("🔧 Windows Celery Worker 启动器")
    print("=" * 50)
    
    # 设置环境
    setup_environment()
    
    # 检查Redis连接
    if not check_redis_connection():
        print("\n💡 解决方案:")
        print("1. 启动Redis服务: docker run -d -p 6379:6379 redis:latest")
        print("2. 或者安装本地Redis服务")
        sys.exit(1)
    
    # 启动worker
    start_worker()

if __name__ == "__main__":
    main()
