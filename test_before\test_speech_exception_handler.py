#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语音处理异常处理测试脚本
测试语音识别、VAD检测、说话人识别等组件的异常处理功能
"""

import os
import sys
import tempfile
import time
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from utils.speech_exception_handler import (
        SpeechExceptionHandler, SpeechProcessingContext,
        SpeechAudioProcessingError, SpeechModelLoadingError, 
        SpeechVADError, SpeechRecognitionError, SpeakerIdentificationError,
        speech_exception_handler_decorator, handle_speech_operation_safely,
        validate_audio_file, safe_load_speech_model, safe_recognize_audio,
        safe_detect_vad, safe_identify_speaker, speech_exception_handler
    )
    print("✓ 成功导入语音处理异常处理模块")
except ImportError as e:
    print(f"✗ 导入失败: {e}")
    sys.exit(1)


def test_speech_processing_context():
    """测试语音处理上下文"""
    print("\n=== 测试语音处理上下文 ===")
    
    # 创建上下文
    context = SpeechProcessingContext(
        operation="recognition",
        audio_file="test.wav",
        model_name="SenseVoice",
        device="cuda",
        chunk_size=30.0,
        language="zh",
        duration=120.5,
        sample_rate=16000,
        channels=1
    )
    
    # 转换为字典
    context_dict = context.to_dict()
    print("✓ 语音处理上下文创建成功:")
    for key, value in context_dict.items():
        print(f"  {key}: {value}")
    
    return True


def test_speech_exceptions():
    """测试语音处理异常类"""
    print("\n=== 测试语音处理异常类 ===")
    
    context = SpeechProcessingContext(
        operation="test",
        audio_file="test.wav",
        model_name="test_model"
    )
    
    # 测试音频处理异常
    try:
        raise SpeechAudioProcessingError(
            "音频文件格式不支持",
            context=context
        )
    except SpeechAudioProcessingError as e:
        print(f"✓ 音频处理异常: {e}")
        print(f"  错误代码: {e.error_code}")
        print(f"  建议: {e.suggestion}")
    
    # 测试模型加载异常
    try:
        raise SpeechModelLoadingError(
            "模型文件丢失",
            context=context
        )
    except SpeechModelLoadingError as e:
        print(f"✓ 模型加载异常: {e}")
        print(f"  可恢复: {e.recoverable}")
    
    # 测试VAD异常
    try:
        raise SpeechVADError(
            "VAD检测失败",
            context=context
        )
    except SpeechVADError as e:
        print(f"✓ VAD异常: {e}")
    
    # 测试语音识别异常
    try:
        raise SpeechRecognitionError(
            "语音识别超时",
            context=context
        )
    except SpeechRecognitionError as e:
        print(f"✓ 语音识别异常: {e}")
    
    # 测试说话人识别异常
    try:
        raise SpeakerIdentificationError(
            "说话人识别失败",
            context=context
        )
    except SpeakerIdentificationError as e:
        print(f"✓ 说话人识别异常: {e}")
    
    print("所有语音异常类型测试完成")
    return True


def test_speech_exception_handler():
    """测试语音异常处理器"""
    print("\n=== 测试语音异常处理器 ===")
    
    # 创建语音异常处理器
    with tempfile.TemporaryDirectory() as temp_dir:
        log_file = os.path.join(temp_dir, "test_speech_exceptions.log")
        handler = SpeechExceptionHandler(log_file)
        
        # 测试处理语音异常
        context = SpeechProcessingContext(
            operation="recognition",
            audio_file="nonexistent.wav"
        )
        
        try:
            raise SpeechAudioProcessingError("测试音频异常", context)
        except Exception as e:
            recovery_success = handler.handle_speech_exception(e, context)
            print(f"✓ 处理音频异常，恢复成功: {recovery_success}")
        
        # 测试处理标准异常
        try:
            raise FileNotFoundError("模型文件未找到")
        except Exception as e:
            context = SpeechProcessingContext(operation="load_model", model_path="missing_model.bin")
            recovery_success = handler.handle_speech_exception(e, context)
            print(f"✓ 处理标准异常，恢复成功: {recovery_success}")
        
        # 获取统计信息
        stats = handler.get_speech_stats()
        print("✓ 语音异常统计:")
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        # 检查日志文件
        if os.path.exists(log_file):
            print(f"✓ 日志文件创建成功: {log_file}")
        
    return True


def test_speech_decorator():
    """测试语音异常处理装饰器"""
    print("\n=== 测试语音异常处理装饰器 ===")
    
    @speech_exception_handler_decorator(
        operation="test_recognition",
        fallback_result={'text': '', 'success': False},
        max_retries=2
    )
    def test_recognition_function(audio_file, should_fail=True):
        if should_fail:
            raise SpeechRecognitionError(f"识别失败: {audio_file}")
        return {'text': '识别成功', 'success': True}
    
    # 测试失败情况
    result1 = test_recognition_function("test.wav", should_fail=True)
    print(f"✓ 装饰器失败处理: {result1}")
    
    # 测试成功情况
    result2 = test_recognition_function("test.wav", should_fail=False)
    print(f"✓ 装饰器成功执行: {result2}")
    
    return True


def test_safe_operations():
    """测试安全操作函数"""
    print("\n=== 测试安全操作函数 ===")
    
    # 测试安全操作执行
    def failing_operation():
        raise SpeechAudioProcessingError("测试操作失败")
    
    result = handle_speech_operation_safely(
        "test_operation",
        failing_operation,
        fallback_result="默认结果",
        max_retries=1
    )
    print(f"✓ 安全操作执行: {result}")
    
    return True


def test_audio_validation():
    """测试音频文件验证"""
    print("\n=== 测试音频文件验证 ===")
    
    # 测试不存在的文件
    try:
        validate_audio_file("nonexistent.wav")
        print("✗ 应该抛出异常")
        return False
    except SpeechAudioProcessingError as e:
        print(f"✓ 正确检测到文件不存在: {e.error_code}")
    
    # 创建临时音频文件进行测试
    with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as tmp_file:
        tmp_path = tmp_file.name
        # 创建空文件
        tmp_file.write(b'')
    
    try:
        try:
            validate_audio_file(tmp_path)
            print("✗ 应该检测到空文件")
            return False
        except SpeechAudioProcessingError as e:
            print(f"✓ 正确检测到空文件: {e.error_code}")
        
        # 写入一些测试数据
        with open(tmp_path, 'wb') as f:
            f.write(b'RIFF' + b'\x00' * 100)  # 模拟音频文件头
        
        try:
            info = validate_audio_file(tmp_path)
            print(f"✓ 音频文件验证结果: {info}")
        except SpeechAudioProcessingError as e:
            print(f"✓ 检测到格式错误: {e.error_code}")
    
    finally:
        # 清理临时文件
        try:
            os.unlink(tmp_path)
        except:
            pass
    
    return True


def test_convenience_functions():
    """测试便捷函数"""
    print("\n=== 测试便捷函数 ===")
    
    # 测试安全模型加载
    result1 = safe_load_speech_model("nonexistent_model.bin")
    print(f"✓ 安全模型加载: {result1}")
    
    # 测试安全语音识别
    result2 = safe_recognize_audio("nonexistent.wav")
    print(f"✓ 安全语音识别: {result2}")
    
    # 测试安全VAD检测
    result3 = safe_detect_vad("nonexistent.wav")
    print(f"✓ 安全VAD检测: {result3}")
    
    # 测试安全说话人识别
    result4 = safe_identify_speaker("nonexistent.wav")
    print(f"✓ 安全说话人识别: {result4}")
    
    return True


def test_exception_conversion():
    """测试异常转换"""
    print("\n=== 测试异常转换 ===")
    
    handler = speech_exception_handler
    
    # 测试文件未找到异常（模型）
    try:
        raise FileNotFoundError("model.bin not found")
    except Exception as e:
        context = {'model_path': 'model.bin', 'operation': 'load_model'}
        converted = handler._convert_speech_exception(e, context)
        print(f"✓ 模型文件异常转换: {type(converted).__name__}")
    
    # 测试文件未找到异常（音频）
    try:
        raise FileNotFoundError("audio.wav not found")
    except Exception as e:
        context = {'audio_file': 'audio.wav', 'operation': 'recognition'}
        converted = handler._convert_speech_exception(e, context)
        print(f"✓ 音频文件异常转换: {type(converted).__name__}")
    
    # 测试CUDA异常
    try:
        raise RuntimeError("CUDA out of memory")
    except Exception as e:
        context = {'device': 'cuda', 'operation': 'recognition'}
        converted = handler._convert_speech_exception(e, context)
        print(f"✓ CUDA异常转换: {type(converted).__name__}")
    
    # 测试内存异常
    try:
        raise MemoryError("Not enough memory")
    except Exception as e:
        context = {'operation': 'recognition'}
        converted = handler._convert_speech_exception(e, context)
        print(f"✓ 内存异常转换: {type(converted).__name__}")
    
    return True


def test_recovery_strategies():
    """测试恢复策略"""
    print("\n=== 测试恢复策略 ===")
    
    handler = speech_exception_handler
    
    # 测试模型恢复策略
    model_exception = SpeechModelLoadingError(
        "模型加载失败",
        SpeechProcessingContext(operation="load_model", model_path="test_model.bin", device="cuda")
    )
    recovery_success = handler.recovery_manager.attempt_recovery(model_exception)
    print(f"✓ 模型恢复策略测试: {recovery_success}")
    
    # 测试音频恢复策略
    audio_exception = SpeechAudioProcessingError(
        "音频处理失败",
        SpeechProcessingContext(operation="recognition", audio_file="test.wav")
    )
    recovery_success = handler.recovery_manager.attempt_recovery(audio_exception)
    print(f"✓ 音频恢复策略测试: {recovery_success}")
    
    return True


def test_statistics():
    """测试统计功能"""
    print("\n=== 测试统计功能 ===")
    
    handler = speech_exception_handler
    
    # 重置统计
    handler.reset_speech_stats()
    
    # 模拟一些异常
    context = SpeechProcessingContext(operation="test")
    
    handler.handle_speech_exception(
        SpeechAudioProcessingError("测试异常1", context), context
    )
    handler.handle_speech_exception(
        SpeechRecognitionError("测试异常2", context), context
    )
    
    # 获取统计
    stats = handler.get_speech_stats()
    print("✓ 语音异常统计:")
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    return True


def run_all_tests():
    """运行所有测试"""
    print("开始语音处理异常处理测试...")
    
    tests = [
        test_speech_processing_context,
        test_speech_exceptions,
        test_speech_exception_handler,
        test_speech_decorator,
        test_safe_operations,
        test_audio_validation,
        test_convenience_functions,
        test_exception_conversion,
        test_recovery_strategies,
        test_statistics
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            success = test_func()
            if success:
                passed += 1
                print(f"✓ {test_func.__name__} 通过")
            else:
                print(f"✗ {test_func.__name__} 失败")
        except Exception as e:
            print(f"✗ {test_func.__name__} 失败: {e}")
    
    print(f"\n=== 测试结果 ===")
    print(f"总测试数: {total}")
    print(f"通过: {passed}")
    print(f"失败: {total - passed}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    return passed == total


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)