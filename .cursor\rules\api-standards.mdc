---
description: 
globs: 
alwaysApply: true
---
# API设计规范

## FastAPI架构标准

### 核心配置
- 主入口：[backend/main.py](mdc:backend/main.py)
- 配置管理：[backend/core/config.py](mdc:backend/core/config.py)
- 简化配置：[backend/core/config_simple.py](mdc:backend/core/config_simple.py)
- 数据库配置：[backend/core/database.py](mdc:backend/core/database.py)

### API路由结构
```
/api/v1/
├── auth/          # 认证相关
├── users/         # 用户管理
├── audio/         # 音频处理
├── speech/        # 语音识别
├── knowledge/     # 知识库管理
├── documents/     # 文档管理
├── tasks/         # 任务管理
├── system/        # 系统管理
└── resources/     # 资源管理
```

### 主要API端点
参考以下实现：
- 认证API：[backend/api/v1/endpoints/auth.py](mdc:backend/api/v1/endpoints/auth.py)
- 语音API：[backend/api/v1/endpoints/speech.py](mdc:backend/api/v1/endpoints/speech.py)
- 音频API：[backend/api/v1/endpoints/audio.py](mdc:backend/api/v1/endpoints/audio.py)
- 知识库API：[backend/api/v1/endpoints/knowledge.py](mdc:backend/api/v1/endpoints/knowledge.py)
- 文档管理：[backend/api/v1/endpoints/document_manager.py](mdc:backend/api/v1/endpoints/document_manager.py)
- 任务管理：[backend/api/v1/endpoints/task_management.py](mdc:backend/api/v1/endpoints/task_management.py)

### RESTful API设计原则

#### HTTP方法规范
```python
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel

router = APIRouter()

# GET - 获取资源
@router.get("/items/")
async def list_items(skip: int = 0, limit: int = 100):
    """获取资源列表"""
    pass

@router.get("/items/{item_id}")
async def get_item(item_id: int):
    """获取单个资源"""
    pass

# POST - 创建资源
@router.post("/items/")
async def create_item(item: ItemCreate):
    """创建新资源"""
    pass

# PUT - 完整更新资源
@router.put("/items/{item_id}")
async def update_item(item_id: int, item: ItemUpdate):
    """完整更新资源"""
    pass

# PATCH - 部分更新资源
@router.patch("/items/{item_id}")
async def partial_update_item(item_id: int, item: ItemPartialUpdate):
    """部分更新资源"""
    pass

# DELETE - 删除资源
@router.delete("/items/{item_id}")
async def delete_item(item_id: int):
    """删除资源"""
    pass
```

#### 响应格式规范
```python
from pydantic import BaseModel
from typing import Optional, Any, List

class APIResponse(BaseModel):
    """标准API响应格式"""
    success: bool
    message: str
    data: Optional[Any] = None
    code: int = 200
    timestamp: str

class PaginatedResponse(BaseModel):
    """分页响应格式"""
    success: bool
    data: List[Any]
    total: int
    page: int
    page_size: int
    total_pages: int

class ErrorResponse(BaseModel):
    """错误响应格式"""
    success: bool = False
    error: str
    error_code: str
    details: Optional[dict] = None
```

### 认证和授权
- 安全模块：[backend/core/security.py](mdc:backend/core/security.py)
- JWT Token认证
- 角色权限管理
- API密钥认证

```python
from fastapi import Depends, HTTPException, status
from fastapi.security import HTTPBearer
from backend.core.security import verify_token

security = HTTPBearer()

async def get_current_user(token: str = Depends(security)):
    """获取当前用户"""
    user = await verify_token(token.credentials)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials"
        )
    return user

# 使用认证装饰器
@router.get("/protected/")
async def protected_endpoint(current_user = Depends(get_current_user)):
    """需要认证的端点"""
    return {"user": current_user}
```

### 数据验证和序列化
```python
from pydantic import BaseModel, Field, validator
from typing import Optional, List
from datetime import datetime

class ItemBase(BaseModel):
    """基础模型"""
    name: str = Field(..., min_length=1, max_length=100)
    description: Optional[str] = Field(None, max_length=500)
    
    @validator('name')
    def validate_name(cls, v):
        if not v.strip():
            raise ValueError('名称不能为空')
        return v.strip()

class ItemCreate(ItemBase):
    """创建模型"""
    pass

class ItemUpdate(ItemBase):
    """更新模型"""
    name: Optional[str] = None

class ItemResponse(ItemBase):
    """响应模型"""
    id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True
```

### 错误处理规范
- 错误管理：[backend/api/v1/endpoints/error_management.py](mdc:backend/api/v1/endpoints/error_management.py)
- 统一异常处理器
- 错误码定义
- 日志记录

```python
from fastapi import HTTPException

class APIError(Exception):
    """自定义API异常"""
    def __init__(self, message: str, code: str, status_code: int = 400):
        self.message = message
        self.code = code
        self.status_code = status_code

# 异常处理器
@app.exception_handler(APIError)
async def api_error_handler(request, exc: APIError):
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "success": False,
            "error": exc.message,
            "error_code": exc.code
        }
    )
```

### WebSocket规范
- WebSocket端点：[backend/api/websocket.py](mdc:backend/api/websocket.py)
- 实时通信标准
- 心跳检测机制
- 重连策略

### API文档规范
- 自动生成Swagger文档：`/docs`
- ReDoc文档：`/redoc`
- 详细的接口描述和示例
- 错误码说明

### 版本控制
- API版本前缀：`/api/v1/`
- 向后兼容原则
- 废弃通知机制

### 性能优化
- 异步处理：使用async/await
- 连接池：数据库连接池
- 缓存策略：Redis缓存
- 限流控制：API调用频率限制

### CORS配置
```python
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],  # 前端地址
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
    allow_headers=["*"],
    expose_headers=["*"],
)
```

### 健康检查
```python
@app.get("/health")
async def health_check():
    """健康检查端点"""
    return {
        "status": "healthy",
        "service": "语音处理智能平台后端",
        "version": "1.0.0",
        "timestamp": datetime.utcnow().isoformat()
    }
```

