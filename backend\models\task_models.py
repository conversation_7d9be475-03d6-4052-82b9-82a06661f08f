"""
任务状态持久化数据模型
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, Float, JSON
from sqlalchemy.sql import func
from datetime import datetime
from enum import Enum

from backend.core.database import Base


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "PENDING"
    STARTED = "STARTED"
    RETRY = "RETRY"
    FAILURE = "FAILURE"
    SUCCESS = "SUCCESS"
    REVOKED = "REVOKED"


class TaskType(str, Enum):
    """任务类型枚举"""
    DOCUMENT_PROCESSING = "document_processing"
    VECTORIZATION = "vectorization"
    OCR_PROCESSING = "ocr_processing"
    BATCH_PROCESSING = "batch_processing"


class TaskRecord(Base):
    """任务记录表"""
    __tablename__ = "task_records"

    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String(255), unique=True, index=True, nullable=False)
    user_id = Column(String(255), index=True, nullable=False)
    task_type = Column(String(50), nullable=False)
    task_name = Column(String(255), nullable=False)
    
    # 任务状态
    status = Column(String(20), default=TaskStatus.PENDING, nullable=False)
    progress_percentage = Column(Float, default=0.0)
    progress_detail = Column(Text)
    progress_stage = Column(String(100))
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    started_at = Column(DateTime(timezone=True))
    completed_at = Column(DateTime(timezone=True))
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 任务参数和结果
    task_args = Column(JSON)  # 任务参数
    task_kwargs = Column(JSON)  # 任务关键字参数
    result = Column(JSON)  # 任务结果
    error_message = Column(Text)  # 错误信息
    traceback = Column(Text)  # 错误堆栈
    
    # 重试相关
    retry_count = Column(Integer, default=0)
    max_retries = Column(Integer, default=3)
    
    # 元数据
    task_metadata = Column(JSON)  # 额外的元数据
    worker_name = Column(String(255))  # 执行的Worker名称
    queue_name = Column(String(100))  # 队列名称
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "task_id": self.task_id,
            "user_id": self.user_id,
            "task_type": self.task_type,
            "task_name": self.task_name,
            "status": self.status,
            "progress_percentage": self.progress_percentage,
            "progress_detail": self.progress_detail,
            "progress_stage": self.progress_stage,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "task_args": self.task_args,
            "task_kwargs": self.task_kwargs,
            "result": self.result,
            "error_message": self.error_message,
            "retry_count": self.retry_count,
            "max_retries": self.max_retries,
            "metadata": self.task_metadata,
            "worker_name": self.worker_name,
            "queue_name": self.queue_name
        }


class TaskProgressLog(Base):
    """任务进度日志表"""
    __tablename__ = "task_progress_logs"

    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String(255), index=True, nullable=False)
    
    # 进度信息
    percentage = Column(Float, nullable=False)
    detail = Column(Text)
    stage = Column(String(100))
    
    # 时间戳
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
    
    # 元数据
    log_metadata = Column(JSON)
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "task_id": self.task_id,
            "percentage": self.percentage,
            "detail": self.detail,
            "stage": self.stage,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None,
            "metadata": self.log_metadata
        }


class TaskDependency(Base):
    """任务依赖关系表"""
    __tablename__ = "task_dependencies"

    id = Column(Integer, primary_key=True, index=True)
    task_id = Column(String(255), index=True, nullable=False)  # 当前任务
    depends_on_task_id = Column(String(255), index=True, nullable=False)  # 依赖的任务
    
    # 依赖类型
    dependency_type = Column(String(50), default="sequential")  # sequential, parallel
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "task_id": self.task_id,
            "depends_on_task_id": self.depends_on_task_id,
            "dependency_type": self.dependency_type,
            "created_at": self.created_at.isoformat() if self.created_at else None
        }


class TaskSchedule(Base):
    """任务调度表"""
    __tablename__ = "task_schedules"

    id = Column(Integer, primary_key=True, index=True)
    schedule_name = Column(String(255), unique=True, nullable=False)
    task_type = Column(String(50), nullable=False)
    
    # 调度配置
    cron_expression = Column(String(100))  # Cron表达式
    interval_seconds = Column(Integer)  # 间隔秒数
    
    # 任务参数
    task_args = Column(JSON)
    task_kwargs = Column(JSON)
    
    # 状态
    is_active = Column(Boolean, default=True)
    last_run_at = Column(DateTime(timezone=True))
    next_run_at = Column(DateTime(timezone=True))
    
    # 时间戳
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # 元数据
    schedule_metadata = Column(JSON)
    
    def to_dict(self):
        """转换为字典"""
        return {
            "id": self.id,
            "schedule_name": self.schedule_name,
            "task_type": self.task_type,
            "cron_expression": self.cron_expression,
            "interval_seconds": self.interval_seconds,
            "task_args": self.task_args,
            "task_kwargs": self.task_kwargs,
            "is_active": self.is_active,
            "last_run_at": self.last_run_at.isoformat() if self.last_run_at else None,
            "next_run_at": self.next_run_at.isoformat() if self.next_run_at else None,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "metadata": self.schedule_metadata
        }
