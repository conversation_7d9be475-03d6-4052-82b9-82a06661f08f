import request from '@/utils/request'

export const collaborationAPI = {
  // 工作区分享
  async createShareLink(workspaceId) {
    return await request({
      url: `/api/v1/collaboration/workspaces/${workspaceId}/share`,
      method: 'post'
    })
  },

  async getShareInfo(shareToken) {
    return await request({
      url: `/api/v1/collaboration/share/${shareToken}`,
      method: 'get'
    })
  },

  async joinWorkspace(shareToken, userInfo) {
    return await request({
      url: `/api/v1/collaboration/share/${shareToken}/join`,
      method: 'post',
      data: userInfo
    })
  },

  // 用户邀请
  async sendEmailInvite(inviteData) {
    return await request({
      url: '/api/v1/collaboration/invites/email',
      method: 'post',
      data: inviteData
    })
  },

  async getInviteInfo(inviteToken) {
    return await request({
      url: `/api/v1/collaboration/invites/${inviteToken}`,
      method: 'get'
    })
  },

  async acceptInvite(inviteToken, userInfo) {
    return await request({
      url: `/api/v1/collaboration/invites/${inviteToken}/accept`,
      method: 'post',
      data: userInfo
    })
  },

  // 在线用户管理
  async getOnlineUsers(workspaceId) {
    return await request({
      url: `/api/v1/collaboration/workspaces/${workspaceId}/users`,
      method: 'get'
    })
  },

  async updateUserActivity(workspaceId, activity) {
    return await request({
      url: `/api/v1/collaboration/workspaces/${workspaceId}/activity`,
      method: 'put',
      data: { activity }
    })
  },

  async updateUserStatus(workspaceId, status) {
    return await request({
      url: `/api/v1/collaboration/workspaces/${workspaceId}/status`,
      method: 'put',
      data: { status }
    })
  },

  // 权限管理
  async getPermissions(workspaceId) {
    return await request({
      url: `/api/v1/collaboration/workspaces/${workspaceId}/permissions`,
      method: 'get'
    })
  },

  async updatePermissions(workspaceId, permissions) {
    return await request({
      url: `/api/v1/collaboration/workspaces/${workspaceId}/permissions`,
      method: 'put',
      data: { permissions }
    })
  },

  async removeUser(workspaceId, userId) {
    return await request({
      url: `/api/v1/collaboration/workspaces/${workspaceId}/users/${userId}`,
      method: 'delete'
    })
  },

  // 共享任务管理
  async getSharedTasks(workspaceId) {
    return await request({
      url: `/api/v1/collaboration/workspaces/${workspaceId}/tasks`,
      method: 'get'
    })
  },

  async createTask(taskData) {
    return await request({
      url: '/api/v1/collaboration/tasks',
      method: 'post',
      data: taskData
    })
  },

  async updateTask(taskId, updates) {
    return await request({
      url: `/api/v1/collaboration/tasks/${taskId}`,
      method: 'put',
      data: updates
    })
  },

  async assignTask(taskId, userId) {
    return await request({
      url: `/api/v1/collaboration/tasks/${taskId}/assign`,
      method: 'post',
      data: { userId }
    })
  },

  async completeTask(taskId) {
    return await request({
      url: `/api/v1/collaboration/tasks/${taskId}/complete`,
      method: 'post'
    })
  },

  async deleteTask(taskId) {
    return await request({
      url: `/api/v1/collaboration/tasks/${taskId}`,
      method: 'delete'
    })
  },

  // 实时聊天
  async getChatMessages(workspaceId, limit = 50) {
    return await request({
      url: `/api/v1/collaboration/workspaces/${workspaceId}/messages`,
      method: 'get',
      params: { limit }
    })
  },

  async sendMessage(messageData) {
    return await request({
      url: '/api/v1/collaboration/messages',
      method: 'post',
      data: messageData
    })
  },

  async deleteMessage(messageId) {
    return await request({
      url: `/api/v1/collaboration/messages/${messageId}`,
      method: 'delete'
    })
  },

  // 文件共享
  async shareFile(workspaceId, fileId, shareOptions) {
    return await request({
      url: `/api/v1/collaboration/workspaces/${workspaceId}/files/${fileId}/share`,
      method: 'post',
      data: shareOptions
    })
  },

  async getSharedFiles(workspaceId) {
    return await request({
      url: `/api/v1/collaboration/workspaces/${workspaceId}/files`,
      method: 'get'
    })
  },

  async updateFilePermissions(workspaceId, fileId, permissions) {
    return await request({
      url: `/api/v1/collaboration/workspaces/${workspaceId}/files/${fileId}/permissions`,
      method: 'put',
      data: { permissions }
    })
  },

  // 操作日志
  async getActivityLog(workspaceId, options = {}) {
    return await request({
      url: `/api/v1/collaboration/workspaces/${workspaceId}/activity`,
      method: 'get',
      params: options
    })
  },

  async logActivity(workspaceId, activityData) {
    return await request({
      url: `/api/v1/collaboration/workspaces/${workspaceId}/activity`,
      method: 'post',
      data: activityData
    })
  },

  // 实时同步
  async syncWorkspace(workspaceId, lastSyncTime) {
    return await request({
      url: `/api/v1/collaboration/workspaces/${workspaceId}/sync`,
      method: 'get',
      params: { since: lastSyncTime }
    })
  },

  async broadcastUpdate(workspaceId, updateData) {
    return await request({
      url: `/api/v1/collaboration/workspaces/${workspaceId}/broadcast`,
      method: 'post',
      data: updateData
    })
  },

  // 冲突解决
  async getConflicts(workspaceId) {
    return await request({
      url: `/api/v1/collaboration/workspaces/${workspaceId}/conflicts`,
      method: 'get'
    })
  },

  async resolveConflict(conflictId, resolution) {
    return await request({
      url: `/api/v1/collaboration/conflicts/${conflictId}/resolve`,
      method: 'post',
      data: resolution
    })
  },

  // 版本控制
  async getVersionHistory(workspaceId, resourceId) {
    return await request({
      url: `/api/v1/collaboration/workspaces/${workspaceId}/versions/${resourceId}`,
      method: 'get'
    })
  },

  async createSnapshot(workspaceId, snapshotData) {
    return await request({
      url: `/api/v1/collaboration/workspaces/${workspaceId}/snapshots`,
      method: 'post',
      data: snapshotData
    })
  },

  async restoreSnapshot(workspaceId, snapshotId) {
    return await request({
      url: `/api/v1/collaboration/workspaces/${workspaceId}/snapshots/${snapshotId}/restore`,
      method: 'post'
    })
  },

  // 协作统计
  async getCollaborationStats(workspaceId, timeRange = '24h') {
    return await request({
      url: `/api/v1/collaboration/workspaces/${workspaceId}/stats`,
      method: 'get',
      params: { range: timeRange }
    })
  },

  async getUserStats(workspaceId, userId, timeRange = '24h') {
    return await request({
      url: `/api/v1/collaboration/workspaces/${workspaceId}/users/${userId}/stats`,
      method: 'get',
      params: { range: timeRange }
    })
  },

  // 通知管理
  async getNotifications(workspaceId) {
    return await request({
      url: `/api/v1/collaboration/workspaces/${workspaceId}/notifications`,
      method: 'get'
    })
  },

  async markNotificationRead(notificationId) {
    return await request({
      url: `/api/v1/collaboration/notifications/${notificationId}/read`,
      method: 'put'
    })
  },

  async updateNotificationSettings(workspaceId, settings) {
    return await request({
      url: `/api/v1/collaboration/workspaces/${workspaceId}/notification-settings`,
      method: 'put',
      data: settings
    })
  },

  // 会话管理
  async createSession(workspaceId, sessionData) {
    return await request({
      url: `/api/v1/collaboration/workspaces/${workspaceId}/sessions`,
      method: 'post',
      data: sessionData
    })
  },

  async joinSession(sessionId) {
    return await request({
      url: `/api/v1/collaboration/sessions/${sessionId}/join`,
      method: 'post'
    })
  },

  async leaveSession(sessionId) {
    return await request({
      url: `/api/v1/collaboration/sessions/${sessionId}/leave`,
      method: 'post'
    })
  },

  async getActiveSessions(workspaceId) {
    return await request({
      url: `/api/v1/collaboration/workspaces/${workspaceId}/sessions`,
      method: 'get'
    })
  },

  // 屏幕共享
  async startScreenShare(workspaceId, shareData) {
    return await request({
      url: `/api/v1/collaboration/workspaces/${workspaceId}/screen-share`,
      method: 'post',
      data: shareData
    })
  },

  async stopScreenShare(shareId) {
    return await request({
      url: `/api/v1/collaboration/screen-shares/${shareId}`,
      method: 'delete'
    })
  },

  async getScreenShares(workspaceId) {
    return await request({
      url: `/api/v1/collaboration/workspaces/${workspaceId}/screen-shares`,
      method: 'get'
    })
  },

  // 语音通话
  async startVoiceCall(workspaceId, callData) {
    return await request({
      url: `/api/v1/collaboration/workspaces/${workspaceId}/voice-calls`,
      method: 'post',
      data: callData
    })
  },

  async joinVoiceCall(callId) {
    return await request({
      url: `/api/v1/collaboration/voice-calls/${callId}/join`,
      method: 'post'
    })
  },

  async leaveVoiceCall(callId) {
    return await request({
      url: `/api/v1/collaboration/voice-calls/${callId}/leave`,
      method: 'post'
    })
  },

  async endVoiceCall(callId) {
    return await request({
      url: `/api/v1/collaboration/voice-calls/${callId}`,
      method: 'delete'
    })
  }
}

export default collaborationAPI
