#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================"
echo -e "语音处理智能平台 - 重启服务脚本"
echo -e "========================================${NC}"
echo

echo -e "${YELLOW}🔄 正在重启所有服务...${NC}"
echo

# 停止所有服务
echo -e "${YELLOW}第一步: 停止现有服务${NC}"
./stop_all_services.sh

echo
echo -e "${YELLOW}等待服务完全停止...${NC}"
sleep 3

# 启动所有服务
echo
echo -e "${YELLOW}第二步: 启动所有服务${NC}"
./start_all_services.sh

echo
echo -e "${GREEN}🎉 服务重启完成！${NC}"
