#!/usr/bin/env python
"""
Celery Worker 启动文件
解决模块导入路径问题
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ.setdefault('PYTHONPATH', str(project_root))

# 导入 celery 应用
from backend.core.task_queue import celery_app

if __name__ == '__main__':
    # 启动 celery worker
    celery_app.start()
