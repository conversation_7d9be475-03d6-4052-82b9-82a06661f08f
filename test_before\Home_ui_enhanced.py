import streamlit as st
import os
import sys
from pathlib import Path

# 🔧 设置完全离线模式，避免任何网络请求
def set_complete_offline_mode():
    """设置完全离线模式，禁用所有网络连接"""
    # FunASR相关 - 最重要的设置
    os.environ['DISABLE_MODEL_DOWNLOAD'] = '1'
    os.environ['FUNASR_CACHE_OFFLINE'] = '1'
    os.environ['FUNASR_OFFLINE_MODE'] = '1'
    
    # ModelScope相关
    os.environ['MODELSCOPE_OFFLINE_MODE'] = '1'
    os.environ['MODELSCOPE_CACHE_DIR'] = '/tmp/modelscope_cache'
    os.environ['MODELSCOPE_DISABLE_UPDATE'] = '1'
    
    # HuggingFace相关
    os.environ['HF_HUB_OFFLINE'] = '1'
    os.environ['HF_DATASETS_OFFLINE'] = '1'
    os.environ['TRANSFORMERS_OFFLINE'] = '1'
    os.environ['HF_HUB_DISABLE_TELEMETRY'] = '1'
    os.environ['HF_HUB_DISABLE_PROGRESS_BARS'] = '1'
    
    # 网络代理相关
    os.environ['NO_PROXY'] = '*'
    os.environ['no_proxy'] = '*'
    os.environ['REQUESTS_CA_BUNDLE'] = ''
    os.environ['CURL_CA_BUNDLE'] = ''
    os.environ['SSL_VERIFY'] = '0'
    
    # PyTorch相关
    os.environ['TORCH_HOME'] = '/tmp/torch_cache'
    os.environ['TORCH_HUB_OFFLINE'] = '1'
    
    # 通用网络禁用
    os.environ['OFFLINE_MODE'] = '1'
    os.environ['DISABLE_INTERNET'] = '1'

# 在应用启动时设置离线模式
set_complete_offline_mode()

# 导入UI异常处理器
try:
    sys.path.append("utils")
    from utils.ui_exception_handler import (
        ui_exception_handler,
        streamlit_exception_handler,
        show_error,
        show_success,
        show_warning,
        show_info
    )
    UI_EXCEPTION_HANDLER_AVAILABLE = True
except ImportError as e:
    st.error(f"⚠️ UI异常处理器不可用: {str(e)}")
    UI_EXCEPTION_HANDLER_AVAILABLE = False

# 页面配置
st.set_page_config(
    page_title="语音处理智能平台",
    page_icon="🎙️",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS样式
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #1e3a8a 0%, #3b82f6 100%);
        padding: 2rem;
        border-radius: 10px;
        color: white;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    .feature-card {
        background: #f8fafc;
        border: 1px solid #e2e8f0;
        border-radius: 8px;
        padding: 1.5rem;
        margin: 1rem 0;
        transition: all 0.3s ease;
    }
    
    .feature-card:hover {
        border-color: #3b82f6;
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
    }
    
    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }
    
    .status-online { background-color: #10b981; }
    .status-offline { background-color: #ef4444; }
    .status-warning { background-color: #f59e0b; }
    
    .metric-card {
        background: white;
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 1rem;
        text-align: center;
    }
    
    .error-notification {
        border-left: 4px solid #ef4444;
        background-color: #fef2f2;
        padding: 1rem;
        margin: 1rem 0;
        border-radius: 4px;
    }
    
    .warning-notification {
        border-left: 4px solid #f59e0b;
        background-color: #fffbeb;
        padding: 1rem;
        margin: 1rem 0;
        border-radius: 4px;
    }
    
    .success-notification {
        border-left: 4px solid #10b981;
        background-color: #f0fdf4;
        padding: 1rem;
        margin: 1rem 0;
        border-radius: 4px;
    }
</style>
""", unsafe_allow_html=True)

def show_main_header():
    """显示主页面头部"""
    st.markdown("""
    <div class="main-header">
        <h1>🎙️ 语音处理智能平台</h1>
        <p>基于FunASR的多功能语音处理解决方案</p>
        <p><strong>VAD语音检测 | 语音识别 | 说话人识别 | 智能分析</strong></p>
    </div>
    """, unsafe_allow_html=True)

@streamlit_exception_handler(show_details=True, show_recovery=True)
def show_feature_overview():
    """显示功能概览（集成异常处理）"""
    st.markdown("## 🚀 功能模块")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("""
        <div class="feature-card">
            <h3>📁 音频上传配置</h3>
            <p>• 多文件上传支持</p>
            <p>• 智能参数配置</p>
            <p>• 音频预览播放</p>
            <p>• 批量处理管理</p>
        </div>
        """, unsafe_allow_html=True)
        
        if st.button("📁 音频上传配置", use_container_width=True, key="main_upload_config"):
            try:
                st.switch_page("pages/音频上传和配置页面.py")
            except Exception as e:
                if UI_EXCEPTION_HANDLER_AVAILABLE:
                    show_error(e, context={"action": "页面跳转", "target": "音频上传配置"})
                else:
                    st.error(f"跳转失败: {e}")
    
    with col2:
        st.markdown("""
        <div class="feature-card">
            <h3>🎵 语音处理分析</h3>
            <p>• VAD语音活动检测</p>
            <p>• 音频预处理与优化</p>
            <p>• 质量分析与评估</p>
            <p>• 综合处理流水线</p>
        </div>
        """, unsafe_allow_html=True)
        
        if st.button("🎵 进入语音处理", use_container_width=True, key="main_speech_processing"):
            try:
                st.switch_page("pages/语音处理分析.py")
            except Exception as e:
                if UI_EXCEPTION_HANDLER_AVAILABLE:
                    show_error(e, context={"action": "页面跳转", "target": "语音处理分析"})
                else:
                    st.error(f"跳转失败: {e}")
    
    with col3:
        st.markdown("""
        <div class="feature-card">
            <h3>📚 本地RAG知识库</h3>
            <p>• 文档向量化处理</p>
            <p>• 智能问答系统</p>
            <p>• 知识检索与管理</p>
            <p>• 多模态数据支持</p>
        </div>
        """, unsafe_allow_html=True)
        
        if st.button("📚 进入知识库", use_container_width=True, key="main_knowledge_base"):
            try:
                st.switch_page("pages/本地RAG大模型知识库.py")
            except Exception as e:
                if UI_EXCEPTION_HANDLER_AVAILABLE:
                    show_error(e, context={"action": "页面跳转", "target": "本地RAG知识库"})
                else:
                    st.error(f"跳转失败: {e}")

@streamlit_exception_handler(show_details=True)
def show_system_status():
    """显示系统状态（集成异常处理）"""
    st.markdown("## 📊 系统状态")
    
    col1, col2, col3, col4 = st.columns(4)
    
    # 检查模型状态
    try:
        from utils.speech_recognition_utils import check_model_availability
        vad_status = check_model_availability()
        
        if vad_status and UI_EXCEPTION_HANDLER_AVAILABLE:
            show_success("VAD模型检查完成", "模型已就绪，可以开始语音处理")
    except Exception as e:
        vad_status = False
        if UI_EXCEPTION_HANDLER_AVAILABLE:
            show_warning("VAD模型检查失败", "模型可能未安装或配置不正确")
        else:
            st.warning("⚠️ VAD模型检查失败")
    
    with col1:
        status_color = "status-online" if vad_status else "status-offline"
        status_text = "在线" if vad_status else "离线"
        st.markdown(f"""
        <div class="metric-card">
            <p><span class="{status_color} status-indicator"></span><strong>VAD模型</strong></p>
            <p>{status_text}</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        # 检查音频处理器状态
        try:
            from utils.audio_preprocessing import AudioPreprocessor
            processor_status = True
            if UI_EXCEPTION_HANDLER_AVAILABLE:
                show_info("音频处理器检查", "音频预处理模块已就绪")
        except Exception as e:
            processor_status = False
            if UI_EXCEPTION_HANDLER_AVAILABLE:
                show_warning("音频处理器检查失败", "预处理模块可能不可用")
        
        status_color = "status-online" if processor_status else "status-offline"
        status_text = "就绪" if processor_status else "未就绪"
        st.markdown(f"""
        <div class="metric-card">
            <p><span class="{status_color} status-indicator"></span><strong>音频处理器</strong></p>
            <p>{status_text}</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col3:
        # 检查批处理器状态
        try:
            from utils.enhanced_batch_processor import EnhancedBatchProcessor
            batch_status = True
            if UI_EXCEPTION_HANDLER_AVAILABLE:
                show_info("批处理器检查", "增强批处理器可用")
        except Exception as e:
            try:
                from utils.batch_optimizer import BatchOptimizer
                batch_status = True
                if UI_EXCEPTION_HANDLER_AVAILABLE:
                    show_info("批处理器检查", "标准批处理器可用")
            except:
                batch_status = False
                if UI_EXCEPTION_HANDLER_AVAILABLE:
                    show_warning("批处理器检查失败", "批处理功能可能不可用")
        
        status_color = "status-online" if batch_status else "status-offline"
        status_text = "可用" if batch_status else "不可用"
        st.markdown(f"""
        <div class="metric-card">
            <p><span class="{status_color} status-indicator"></span><strong>批处理器</strong></p>
            <p>{status_text}</p>
        </div>
        """, unsafe_allow_html=True)
    
    with col4:
        # 检查文件恢复系统状态
        try:
            from utils.file_recovery_handler import FileRecoveryHandler
            recovery_status = True
            if UI_EXCEPTION_HANDLER_AVAILABLE:
                show_info("文件恢复系统", "文件恢复和备份系统已就绪")
        except Exception as e:
            recovery_status = False
            if UI_EXCEPTION_HANDLER_AVAILABLE:
                show_warning("文件恢复系统检查失败", "文件恢复功能可能不可用")
        
        status_color = "status-online" if recovery_status else "status-offline"
        status_text = "就绪" if recovery_status else "不可用"
        st.markdown(f"""
        <div class="metric-card">
            <p><span class="{status_color} status-indicator"></span><strong>文件恢复</strong></p>
            <p>{status_text}</p>
        </div>
        """, unsafe_allow_html=True)

@streamlit_exception_handler(show_details=True)
def show_quick_actions():
    """显示快速操作面板（集成异常处理）"""
    st.markdown("## ⚡ 快速操作")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("🎧 实时监控", use_container_width=True, key="quick_monitor"):
            try:
                st.switch_page("pages/实时处理监控界面.py")
            except Exception as e:
                if UI_EXCEPTION_HANDLER_AVAILABLE:
                    show_error(e, context={"action": "快速跳转", "target": "实时监控"})
                else:
                    st.error(f"跳转失败: {e}")
    
    with col2:
        if st.button("📊 结果展示", use_container_width=True, key="quick_results"):
            try:
                st.switch_page("pages/结果展示和编辑.py")
            except Exception as e:
                if UI_EXCEPTION_HANDLER_AVAILABLE:
                    show_error(e, context={"action": "快速跳转", "target": "结果展示"})
                else:
                    st.error(f"跳转失败: {e}")
    
    with col3:
        if st.button("🔧 系统测试", use_container_width=True, key="quick_test"):
            if UI_EXCEPTION_HANDLER_AVAILABLE:
                test_ui_exception_system()
            else:
                st.warning("UI异常处理器不可用，无法进行系统测试")
    
    with col4:
        if st.button("📋 使用帮助", use_container_width=True, key="quick_help"):
            show_help_information()

def test_ui_exception_system():
    """测试UI异常处理系统"""
    st.subheader("🧪 UI异常处理系统测试")
    
    col1, col2 = st.columns(2)
    
    with col1:
        if st.button("测试成功消息"):
            show_success("测试成功", "UI异常处理系统正常工作")
        
        if st.button("测试警告消息"):
            show_warning("测试警告", "这是一个警告消息示例")
    
    with col2:
        if st.button("测试信息消息"):
            show_info("测试信息", "这是一个信息消息示例")
        
        if st.button("测试错误消息"):
            test_error = Exception("这是一个测试错误")
            show_error(test_error, context={"test": True, "component": "UI系统测试"})

def show_help_information():
    """显示帮助信息"""
    st.subheader("📋 系统帮助")
    
    with st.expander("🚀 快速开始", expanded=True):
        st.markdown("""
        ### 使用流程：
        1. **上传音频文件** - 在"音频上传配置"页面选择音频文件
        2. **配置参数** - 根据需要调整处理参数
        3. **开始处理** - 使用"语音处理分析"进行音频处理
        4. **查看结果** - 在"结果展示"页面查看处理结果
        5. **实时监控** - 使用监控界面查看处理进度
        """)
    
    with st.expander("⚠️ 常见问题", expanded=False):
        st.markdown("""
        ### 故障排除：
        
        **模型加载失败**
        - 检查模型文件是否存在
        - 确保有足够的内存
        - 尝试重启应用程序
        
        **文件上传失败**
        - 检查文件格式是否支持
        - 确认文件大小在限制范围内
        - 检查磁盘空间
        
        **处理速度慢**
        - 尝试使用GPU加速
        - 减少批处理文件数量
        - 关闭其他占用内存的程序
        """)
    
    with st.expander("🔧 系统要求", expanded=False):
        st.markdown("""
        ### 系统配置建议：
        
        **最低要求：**
        - RAM: 4GB
        - 存储: 2GB可用空间
        - Python: 3.8+
        
        **推荐配置：**
        - RAM: 8GB+
        - GPU: NVIDIA显卡（支持CUDA）
        - 存储: 5GB+可用空间
        """)

@streamlit_exception_handler(show_details=True)
def show_sidebar_config():
    """显示侧边栏配置（集成异常处理）"""
    with st.sidebar:
        st.header("⚙️ 系统配置")
        
        # API模型测试
        try:
            from config.models.api_models import API_MODELS, test_api_connection
            
            st.subheader("🔗 API连接测试")
            
            selected_model = st.selectbox(
                "选择API模型",
                options=list(API_MODELS.keys()),
                key="api_model_select"
            )
            
            if st.button("测试连接", key="test_api_connection"):
                with st.spinner("测试连接中..."):
                    try:
                        result = test_api_connection(selected_model)
                        if result:
                            show_success("API连接成功", f"{selected_model} 连接正常")
                        else:
                            show_warning("API连接失败", f"{selected_model} 连接异常")
                    except Exception as e:
                        show_error(e, context={"api_model": selected_model, "action": "连接测试"})
            
        except Exception as e:
            if UI_EXCEPTION_HANDLER_AVAILABLE:
                show_warning("API模型配置不可用", "可能缺少相关配置文件")
        
        st.markdown("---")
        
        # 系统信息
        st.subheader("📊 系统信息")
        
        try:
            import psutil
            cpu_usage = psutil.cpu_percent()
            memory_usage = psutil.virtual_memory().percent
            
            st.metric("CPU使用率", f"{cpu_usage:.1f}%")
            st.metric("内存使用率", f"{memory_usage:.1f}%")
            
            if memory_usage > 80:
                show_warning("内存使用率较高", "建议关闭其他程序释放内存")
            
        except Exception as e:
            if UI_EXCEPTION_HANDLER_AVAILABLE:
                show_info("系统监控不可用", "无法获取系统资源信息")
        
        st.markdown("---")
        
        # 清理选项
        st.subheader("🧹 系统清理")
        
        if st.button("清理临时文件", key="cleanup_temp"):
            try:
                import tempfile
                import shutil
                temp_dir = tempfile.gettempdir()
                # 这里可以添加具体的清理逻辑
                show_success("清理完成", "临时文件已清理")
            except Exception as e:
                if UI_EXCEPTION_HANDLER_AVAILABLE:
                    show_error(e, context={"action": "系统清理"})
        
        if st.button("重置配置", key="reset_config"):
            try:
                # 这里可以添加重置配置的逻辑
                show_success("配置重置", "系统配置已重置为默认值")
            except Exception as e:
                if UI_EXCEPTION_HANDLER_AVAILABLE:
                    show_error(e, context={"action": "配置重置"})

def main():
    """主函数（集成UI异常处理）"""
    try:
        # 显示主页面标题
        show_main_header()
        
        # 系统状态检查
        show_system_status()
        
        # 功能模块概览
        show_feature_overview()
        
        # 快速操作面板
        show_quick_actions()
        
        # 侧边栏配置
        show_sidebar_config()
        
        # 页面底部信息
        with st.expander("ℹ️ 关于系统", expanded=False):
            st.markdown("""
            ### 🎙️ 语音处理智能平台
            
            **版本**: v0.1.0  
            **更新时间**: 2024年12月
            
            **核心功能**:
            - 基于FunASR的高精度语音识别
            - VAD语音活动检测
            - 批量音频处理
            - 实时处理监控
            - 智能异常处理和恢复
            
            **技术栈**:
            - FunASR: 语音识别核心
            - Streamlit: Web界面
            - PyTorch: 深度学习框架
            - ChromaDB: 向量数据库
            """)
        
        # 显示异常反馈表单
        if UI_EXCEPTION_HANDLER_AVAILABLE:
            ui_exception_handler.create_error_feedback_form()
        
    except Exception as e:
        if UI_EXCEPTION_HANDLER_AVAILABLE:
            show_error(e, context={"function": "main", "page": "主页"}, show_details=True)
        else:
            st.error(f"❌ 系统错误: {e}")
            st.error("请刷新页面重试，如果问题持续存在请联系技术支持。")

if __name__ == "__main__":
    main() 