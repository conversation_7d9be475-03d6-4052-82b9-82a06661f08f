#!/usr/bin/env python3
"""
测试弃用用法修复
验证datetime.utcnow()和@validator装饰器的修复
"""

import sys
import os
import re
import importlib
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_datetime_utcnow_fixes():
    """测试datetime.utcnow()修复"""
    print("🔍 检查datetime.utcnow()修复...")
    
    issues_found = []
    
    # 查找所有Python文件
    for root, dirs, files in os.walk(project_root):
        # 跳过虚拟环境和缓存目录
        dirs[:] = [d for d in dirs if d not in ['.venv', '__pycache__', '.git', 'node_modules']]
        
        for file in files:
            if file.endswith('.py'):
                file_path = os.path.join(root, file)

                # 跳过修复脚本和测试脚本本身
                if file in ['fix_deprecated_datetime.py', 'test_deprecated_fixes.py']:
                    continue

                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # 检查是否还有datetime.utcnow()
                    if 'datetime.utcnow()' in content:
                        issues_found.append(f"❌ {file_path}: 仍然使用datetime.utcnow()")
                        
                except Exception as e:
                    print(f"⚠️ 无法读取文件 {file_path}: {e}")
    
    if issues_found:
        print("❌ 发现未修复的datetime.utcnow()用法:")
        for issue in issues_found:
            print(f"   {issue}")
        return False
    else:
        print("✅ 所有datetime.utcnow()用法已修复")
        return True

def test_pydantic_validator_fixes():
    """测试Pydantic @validator装饰器修复"""
    print("\n🔍 检查@validator装饰器修复...")
    
    try:
        # 测试auth.py中的修复
        from backend.schemas.auth import LoginRequest, RegisterRequest
        
        # 测试LoginRequest验证
        try:
            # 测试用户名验证
            login_req = LoginRequest(username="  test  ", password="password")
            assert login_req.username == "test", "用户名trim失败"
            
            # 测试空用户名
            try:
                LoginRequest(username="  ", password="password")
                assert False, "应该抛出用户名为空的错误"
            except ValueError as e:
                assert "用户名不能为空" in str(e)
            
            print("✅ LoginRequest验证器工作正常")
            
        except Exception as e:
            print(f"❌ LoginRequest验证器测试失败: {e}")
            return False
        
        # 测试RegisterRequest验证
        try:
            # 测试正常注册
            reg_req = RegisterRequest(
                username="testuser",
                full_name_pinyin="zhangsan",
                password="password123"
            )
            assert reg_req.username == "testuser"
            assert reg_req.full_name_pinyin == "zhangsan"
            
            # 测试用户名验证
            try:
                RegisterRequest(
                    username="ab",  # 太短
                    full_name_pinyin="zhangsan",
                    password="password123"
                )
                assert False, "应该抛出用户名太短的错误"
            except ValueError as e:
                assert "用户名至少3个字符" in str(e)
            
            # 测试拼音验证
            try:
                RegisterRequest(
                    username="testuser",
                    full_name_pinyin="Zhang San",  # 包含空格
                    password="password123"
                )
                assert False, "应该抛出拼音格式错误"
            except ValueError as e:
                assert "只能包含小写字母" in str(e)
            
            print("✅ RegisterRequest验证器工作正常")
            
        except Exception as e:
            print(f"❌ RegisterRequest验证器测试失败: {e}")
            return False
        
        return True
        
    except ImportError as e:
        print(f"❌ 无法导入schemas模块: {e}")
        return False

def test_datetime_timezone_usage():
    """测试新的datetime.now(timezone.utc)用法"""
    print("\n🔍 测试新的datetime用法...")
    
    try:
        from datetime import datetime, timezone
        
        # 测试新的用法
        now_utc = datetime.now(timezone.utc)
        assert now_utc.tzinfo is not None, "时区信息应该存在"
        assert now_utc.tzinfo == timezone.utc, "应该是UTC时区"
        
        print("✅ 新的datetime.now(timezone.utc)用法正常")
        return True
        
    except Exception as e:
        print(f"❌ 新的datetime用法测试失败: {e}")
        return False

def test_model_imports():
    """测试模型文件的导入"""
    print("\n🔍 测试模型文件导入...")
    
    try:
        # 测试用户模型
        from backend.models.user import User
        print("✅ User模型导入成功")
        
        # 测试音频模型
        from backend.models.audio import AudioFile, ProcessingResult, SpeakerProfile
        print("✅ Audio模型导入成功")
        
        # 测试系统模型
        from backend.models.system import SystemConfig, SystemMetrics
        print("✅ System模型导入成功")
        
        # 测试对话模型
        from backend.models.conversation import Conversation, Message
        print("✅ Conversation模型导入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 模型导入失败: {e}")
        return False

def test_security_module():
    """测试安全模块"""
    print("\n🔍 测试安全模块...")
    
    try:
        from backend.core.security import create_access_token, create_refresh_token
        
        # 测试创建访问令牌
        token_data = {"sub": "1", "username": "test"}
        access_token = create_access_token(token_data)
        assert access_token is not None, "访问令牌应该被创建"
        
        # 测试创建刷新令牌
        refresh_token = create_refresh_token(token_data)
        assert refresh_token is not None, "刷新令牌应该被创建"
        
        print("✅ 安全模块工作正常")
        return True
        
    except Exception as e:
        print(f"❌ 安全模块测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 开始弃用用法修复验证测试")
    print("=" * 60)
    
    tests = [
        ("datetime.utcnow()修复检查", test_datetime_utcnow_fixes),
        ("Pydantic @validator修复", test_pydantic_validator_fixes),
        ("新datetime用法测试", test_datetime_timezone_usage),
        ("模型文件导入测试", test_model_imports),
        ("安全模块测试", test_security_module)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("弃用用法修复验证结果:")
    print("=" * 60)
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name}: {status}")
        if success:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有弃用用法修复验证测试通过！")
        print("\n📋 修复总结:")
        print("✅ datetime.utcnow() → datetime.now(timezone.utc)")
        print("✅ @validator → @field_validator + @classmethod")
        print("✅ 所有模型和服务正常工作")
        return True
    else:
        print("⚠️ 部分测试失败，请检查修复情况")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
