import{_ as e,o as d,r as i,a as o,c as n,b as t,d as l,w as v,e as c,f as r}from"./index-2c134546.js";const _={class:"settings-container"},b={class:"page-header"},p={class:"header-content"},u={class:"header-actions"},g={__name:"Settings",setup(h){return d(()=>{console.log("⚙️ 系统设置页面加载完成")}),(m,a)=>{const s=i("router-link");return o(),n("div",_,[t("div",b,[t("div",p,[a[1]||(a[1]=t("div",{class:"header-brand"},[t("div",{class:"brand-logo"},"⚙️"),t("span",{class:"brand-text"},"系统设置")],-1)),t("div",u,[l(s,{to:"/dashboard",class:"btn-outline"},{default:v(()=>a[0]||(a[0]=[r("返回控制台")])),_:1,__:[0]})])])]),a[2]||(a[2]=c('<div class="page-main" data-v-345a618b><div class="container" data-v-345a618b><div class="settings-card" data-v-345a618b><div class="settings-header" data-v-345a618b><h2 data-v-345a618b>系统设置</h2><p data-v-345a618b>管理系统配置和参数</p></div><div class="settings-content" data-v-345a618b><h3 data-v-345a618b>🚧 功能开发中</h3><p data-v-345a618b>系统设置功能正在开发中，敬请期待！</p><p data-v-345a618b>预计功能包括：</p><ul data-v-345a618b><li data-v-345a618b>系统参数配置</li><li data-v-345a618b>用户权限管理</li><li data-v-345a618b>日志管理</li><li data-v-345a618b>备份与恢复</li><li data-v-345a618b>性能监控</li></ul></div></div></div></div>',1))])}}},f=e(g,[["__scopeId","data-v-345a618b"]]);export{f as default};
