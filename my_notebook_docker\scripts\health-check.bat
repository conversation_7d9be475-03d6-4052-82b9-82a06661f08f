@echo off
REM ===========================================
REM 语音处理智能平台 Docker 健康检查脚本 (Windows)
REM ===========================================

setlocal enabledelayedexpansion

echo 🔍 语音处理智能平台 Docker 健康检查
echo ========================================

REM 切换到脚本目录的上级目录
cd /d "%~dp0\.."

REM 检查Docker服务状态
echo [INFO] 检查Docker服务状态...
echo.
echo [INFO] 容器状态:
docker-compose ps

echo.
echo [INFO] 检查各个服务...

REM 检查Redis服务
echo [INFO] 检查 redis 服务...
docker-compose ps redis | findstr "Up" >nul
if errorlevel 1 (
    echo [ERROR] redis 服务未运行
) else (
    echo [SUCCESS] redis 服务运行正常
)

REM 检查Backend服务
echo [INFO] 检查 backend 服务...
docker-compose ps backend | findstr "Up" >nul
if errorlevel 1 (
    echo [ERROR] backend 服务未运行
) else (
    echo [SUCCESS] backend 服务运行正常
)

REM 检查Celery Worker服务
echo [INFO] 检查 celery-worker 服务...
docker-compose ps celery-worker | findstr "Up" >nul
if errorlevel 1 (
    echo [ERROR] celery-worker 服务未运行
) else (
    echo [SUCCESS] celery-worker 服务运行正常
)

REM 检查API端点
echo.
echo [INFO] 检查API端点...

REM 读取端口配置 (简化版本，假设使用默认端口8002)
set BACKEND_PORT=8002

echo [INFO] 检查Backend健康检查端点...
curl -s -f "http://localhost:%BACKEND_PORT%/health" >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Backend健康检查端点无响应
) else (
    echo [SUCCESS] Backend健康检查端点响应正常
    echo 健康检查地址: http://localhost:%BACKEND_PORT%/health
)

echo [INFO] 检查API文档端点...
curl -s -f "http://localhost:%BACKEND_PORT%/docs" >nul 2>&1
if errorlevel 1 (
    echo [WARNING] API文档端点无法访问
) else (
    echo [SUCCESS] API文档端点可访问
    echo API文档地址: http://localhost:%BACKEND_PORT%/docs
)

REM 检查Redis连接
echo.
echo [INFO] 检查Redis连接...
docker-compose exec -T redis redis-cli ping 2>nul | findstr "PONG" >nul
if errorlevel 1 (
    echo [ERROR] Redis连接失败
) else (
    echo [SUCCESS] Redis连接正常
)

REM 检查ChromaDB状态
echo.
echo [INFO] 检查ChromaDB状态...
docker-compose exec -T backend /app/.venv/bin/python -c "import chromadb; import os; client = chromadb.PersistentClient(path='/app/data/chroma_db'); collections = client.list_collections(); print(f'ChromaDB连接正常，集合数: {len(collections)}'); kb_collection = client.get_collection('knowledge_base'); print(f'knowledge_base集合存在，文档数: {kb_collection.count()}')" 2>nul
if errorlevel 1 (
    echo [WARNING] ChromaDB检查失败，可能需要初始化
) else (
    echo [SUCCESS] ChromaDB检查完成
)

REM 显示系统资源使用情况
echo.
echo [INFO] 容器资源使用:
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}" 2>nul

echo.
echo [INFO] 数据卷使用情况:
if exist "volumes" (
    for /d %%i in (volumes\*) do (
        echo %%i
    )
) else (
    echo [WARNING] 无法获取数据卷信息
)

echo.
echo [SUCCESS] 🎉 健康检查完成！
echo.
echo [INFO] 如果发现问题，请查看详细日志:
echo   docker-compose logs backend
echo   docker-compose logs celery-worker
echo   docker-compose logs redis

pause
