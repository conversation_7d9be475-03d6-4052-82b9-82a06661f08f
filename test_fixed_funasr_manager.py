#!/usr/bin/env python3
"""
测试修复后的FunASR管理器
验证基于最新FunASR最佳实践的内存管理效果
"""

import os
import sys
import gc
import time
import psutil
import torch
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def monitor_memory(label: str):
    """监控内存使用"""
    memory = psutil.virtual_memory()
    process = psutil.Process()
    process_memory = process.memory_info().rss / 1024**3  # GB
    
    gpu_info = ""
    if torch.cuda.is_available():
        allocated = torch.cuda.memory_allocated() / 1024**3
        reserved = torch.cuda.memory_reserved() / 1024**3
        total = torch.cuda.get_device_properties(0).total_memory / 1024**3
        gpu_info = f", GPU: {allocated:.1f}GB/{reserved:.1f}GB (总计{total:.1f}GB)"
    
    print(f"[{label}] 系统: {memory.percent:.1f}%, 进程: {process_memory:.1f}GB{gpu_info}")
    return process_memory

def test_original_vs_optimized():
    """对比原始方式和优化方式"""
    print("🔄 对比测试：原始方式 vs 优化方式")
    print("=" * 60)
    
    model_path = "./models/SenseVoiceSmall"
    test_audio = "resource/对话.mp3"
    
    if not os.path.exists(model_path):
        print(f"⚠️ 模型路径不存在: {model_path}")
        return
    
    if not os.path.exists(test_audio):
        print(f"⚠️ 测试音频不存在: {test_audio}")
        return
    
    # 测试1: 原始CPU方式
    print("\n🔴 测试原始CPU方式...")
    initial_memory = monitor_memory("原始-开始")
    
    try:
        from funasr import AutoModel
        
        # 原始CPU加载
        model_cpu = AutoModel(
            model=model_path,
            trust_remote_code=True,
            device='cpu',
            local_files_only=True,
            disable_update=True
        )
        
        cpu_load_memory = monitor_memory("原始-CPU加载后")
        
        # CPU推理
        result_cpu = model_cpu.generate(input=test_audio)
        cpu_inference_memory = monitor_memory("原始-CPU推理后")
        
        # 清理
        del model_cpu
        gc.collect()
        cpu_cleanup_memory = monitor_memory("原始-CPU清理后")
        
        print(f"📊 原始CPU方式内存增长: {cpu_cleanup_memory - initial_memory:.1f}GB")
        
    except Exception as e:
        print(f"❌ 原始CPU方式失败: {e}")
    
    # 等待内存稳定
    time.sleep(5)
    gc.collect()
    
    # 测试2: 优化GPU方式
    print("\n🟢 测试优化GPU方式...")
    optimized_start_memory = monitor_memory("优化-开始")
    
    try:
        from backend.utils.audio.optimized_funasr_manager import get_funasr_manager, cleanup_funasr_manager
        
        # 获取优化管理器
        funasr_manager = get_funasr_manager()
        
        # GPU加载
        success = funasr_manager.load_model(model_path)
        if not success:
            print("❌ 优化管理器加载失败")
            return
        
        gpu_load_memory = monitor_memory("优化-GPU加载后")
        
        # GPU推理
        result_gpu = funasr_manager.generate(test_audio)
        gpu_inference_memory = monitor_memory("优化-GPU推理后")
        
        # 清理
        cleanup_funasr_manager()
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        gpu_cleanup_memory = monitor_memory("优化-GPU清理后")
        
        print(f"📊 优化GPU方式内存增长: {gpu_cleanup_memory - optimized_start_memory:.1f}GB")
        
        # 对比结果
        print(f"\n📋 结果对比:")
        if result_cpu and result_gpu:
            print(f"  CPU结果长度: {len(str(result_cpu))}")
            print(f"  GPU结果长度: {len(str(result_gpu))}")
        
    except Exception as e:
        print(f"❌ 优化GPU方式失败: {e}")
        import traceback
        traceback.print_exc()

def test_memory_stability():
    """测试内存稳定性"""
    print("\n🔍 内存稳定性测试")
    print("=" * 60)
    
    try:
        from backend.utils.audio.optimized_funasr_manager import get_funasr_manager, cleanup_funasr_manager
        
        model_path = "./models/SenseVoiceSmall"
        test_audio = "resource/对话.mp3"
        
        baseline_memory = monitor_memory("稳定性测试-基线")
        
        # 执行10次推理循环
        for i in range(10):
            print(f"\n--- 第{i+1}次推理 ---")
            
            # 加载模型
            funasr_manager = get_funasr_manager()
            funasr_manager.load_model(model_path)
            
            load_memory = monitor_memory(f"第{i+1}次-加载后")
            
            # 执行推理
            if os.path.exists(test_audio):
                result = funasr_manager.generate(test_audio)
                if result:
                    print(f"  推理成功，结果长度: {len(str(result))}")
            
            inference_memory = monitor_memory(f"第{i+1}次-推理后")
            
            # 清理
            cleanup_funasr_manager()
            gc.collect()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            cleanup_memory = monitor_memory(f"第{i+1}次-清理后")
            
            # 检查内存增长
            memory_growth = cleanup_memory - baseline_memory
            if memory_growth > 0.5:  # 超过500MB
                print(f"⚠️ 检测到内存增长: {memory_growth:.1f}GB")
            
            time.sleep(1)  # 短暂等待
        
        # 最终检查
        final_memory = monitor_memory("稳定性测试-最终")
        total_growth = final_memory - baseline_memory
        
        print(f"\n📊 稳定性测试结果:")
        print(f"  总内存增长: {total_growth:.1f}GB")
        
        if total_growth < 0.2:  # 小于200MB
            print("🎉 内存管理优秀！")
        elif total_growth < 0.5:  # 小于500MB
            print("✅ 内存管理良好")
        elif total_growth < 1.0:  # 小于1GB
            print("⚠️ 内存管理可接受")
        else:
            print("🚨 仍存在内存泄漏问题")
        
    except Exception as e:
        print(f"❌ 稳定性测试失败: {e}")

def test_gpu_memory_optimization():
    """测试GPU内存优化"""
    print("\n🎯 GPU内存优化测试")
    print("=" * 60)
    
    if not torch.cuda.is_available():
        print("⚠️ GPU不可用，跳过GPU测试")
        return
    
    try:
        from backend.utils.audio.optimized_funasr_manager import get_funasr_manager, cleanup_funasr_manager
        
        model_path = "./models/SenseVoiceSmall"
        test_audio = "resource/对话.mp3"
        
        # 检查初始GPU状态
        initial_allocated = torch.cuda.memory_allocated() / 1024**3
        initial_reserved = torch.cuda.memory_reserved() / 1024**3
        total_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        
        print(f"🔍 GPU初始状态:")
        print(f"  总显存: {total_memory:.1f}GB")
        print(f"  已分配: {initial_allocated:.1f}GB")
        print(f"  已缓存: {initial_reserved:.1f}GB")
        print(f"  可用: {total_memory - initial_reserved:.1f}GB")
        
        # 加载模型到GPU
        funasr_manager = get_funasr_manager()
        success = funasr_manager.load_model(model_path)
        
        if success:
            load_allocated = torch.cuda.memory_allocated() / 1024**3
            load_reserved = torch.cuda.memory_reserved() / 1024**3
            
            print(f"\n📊 模型加载后:")
            print(f"  已分配: {load_allocated:.1f}GB (+{load_allocated - initial_allocated:.1f}GB)")
            print(f"  已缓存: {load_reserved:.1f}GB (+{load_reserved - initial_reserved:.1f}GB)")
            print(f"  可用: {total_memory - load_reserved:.1f}GB")
            
            # 执行推理
            if os.path.exists(test_audio):
                result = funasr_manager.generate(test_audio)
                
                inference_allocated = torch.cuda.memory_allocated() / 1024**3
                inference_reserved = torch.cuda.memory_reserved() / 1024**3
                
                print(f"\n📊 推理后:")
                print(f"  已分配: {inference_allocated:.1f}GB")
                print(f"  已缓存: {inference_reserved:.1f}GB")
            
            # 清理模型
            cleanup_funasr_manager()
            torch.cuda.empty_cache()
            
            final_allocated = torch.cuda.memory_allocated() / 1024**3
            final_reserved = torch.cuda.memory_reserved() / 1024**3
            
            print(f"\n📊 清理后:")
            print(f"  已分配: {final_allocated:.1f}GB")
            print(f"  已缓存: {final_reserved:.1f}GB")
            print(f"  可用: {total_memory - final_reserved:.1f}GB")
            
            # 检查GPU内存是否完全释放
            if final_allocated < 0.1 and final_reserved < 0.1:
                print("🎉 GPU内存完全释放！")
            elif final_allocated < 0.5:
                print("✅ GPU内存基本释放")
            else:
                print("⚠️ GPU内存未完全释放")
        
    except Exception as e:
        print(f"❌ GPU内存优化测试失败: {e}")

def main():
    """主函数"""
    print("🚀 修复后的FunASR管理器测试")
    print("基于FunASR最新最佳实践")
    print("=" * 60)
    
    # 系统信息
    print(f"🖥️ 系统信息:")
    print(f"  Python: {sys.version.split()[0]}")
    print(f"  PyTorch: {torch.__version__}")
    
    if torch.cuda.is_available():
        print(f"  GPU: {torch.cuda.get_device_name(0)}")
        print(f"  CUDA: {torch.version.cuda}")
    else:
        print("  GPU: 不可用")
    
    # 初始内存状态
    initial_memory = monitor_memory("系统初始状态")
    
    # 测试1: 对比原始方式和优化方式
    test_original_vs_optimized()
    
    # 等待内存稳定
    print("\n⏳ 等待内存稳定...")
    time.sleep(5)
    gc.collect()
    
    # 测试2: 内存稳定性测试
    test_memory_stability()
    
    # 等待内存稳定
    print("\n⏳ 等待内存稳定...")
    time.sleep(5)
    gc.collect()
    
    # 测试3: GPU内存优化测试
    test_gpu_memory_optimization()
    
    # 最终状态
    print("\n" + "=" * 60)
    final_memory = monitor_memory("测试完成")
    
    total_growth = final_memory - initial_memory
    print(f"\n📋 总体测试结果:")
    print(f"  总内存增长: {total_growth:.1f}GB")
    
    if total_growth < 0.5:
        print("🎉 内存优化效果优秀！")
    elif total_growth < 1.0:
        print("✅ 内存优化效果良好")
    else:
        print("⚠️ 仍需进一步优化")

if __name__ == "__main__":
    main()
