# 语音处理识别系统配置文件
# 自动生成于环境配置过程

[models]
base_path = C:\Users\<USER>\Documents\my_project\models\model_dir
sensevoice_path = C:\Users\<USER>\Documents\my_project\models\model_dir\SenseVoiceSmall
campplus_path = C:\Users\<USER>\Documents\my_project\models\model_dir\cam++
vad_path = C:\Users\<USER>\Documents\my_project\models\model_dir\fsmn_vad_zh

[processing]
# 并行处理线程数（建议为CPU核心数的2倍）
max_workers = 8
# 音频分块大小（秒）
chunk_size = 30
# 缓存启用
enable_cache = true

[performance]
# GPU加速
use_gpu = auto
# 内存限制（GB）
memory_limit = 8
# 批处理大小
batch_size = 4

[output]
# 默认输出格式
default_format = json
# 输出目录
output_dir = ./output
# 文件命名模式
filename_pattern = speech_result_{timestamp}
