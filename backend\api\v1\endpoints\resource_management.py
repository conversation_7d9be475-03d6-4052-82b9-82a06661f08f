"""
资源管理API端点
提供系统资源监控和并发控制功能
"""

from typing import Optional
from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.security import HTTPAuthorizationCredentials
from pydantic import BaseModel

from backend.core.security import get_current_user_id
from fastapi.security import HTTPBearer

security = HTTPBearer()
from backend.services.resource_monitor import get_resource_monitor
from backend.services.concurrency_control import get_concurrency_controller
from loguru import logger

router = APIRouter()


class ConcurrencyLimitsUpdate(BaseModel):
    """并发限制更新请求"""
    max_total_tasks: Optional[int] = None
    max_per_user: Optional[int] = None
    max_per_queue: Optional[dict] = None
    resource_based_scaling: Optional[bool] = None


@router.get("/system/resources")
async def get_system_resources(
    minutes: int = Query(60, description="统计时间范围（分钟）"),
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """获取系统资源使用情况"""
    try:
        user_id = get_current_user_id(credentials)
        resource_monitor = get_resource_monitor()
        
        # 获取当前资源指标
        current_metrics = resource_monitor.get_current_metrics()
        
        # 获取资源摘要
        resource_summary = resource_monitor.get_resource_summary(minutes)
        
        # 检查资源健康状态
        health_status = resource_monitor.check_resource_health(current_metrics)
        
        return {
            "success": True,
            "current_metrics": current_metrics.to_dict(),
            "summary": resource_summary,
            "health": health_status,
            "thresholds": {
                "cpu_warning": resource_monitor.thresholds.cpu_warning,
                "cpu_critical": resource_monitor.thresholds.cpu_critical,
                "memory_warning": resource_monitor.thresholds.memory_warning,
                "memory_critical": resource_monitor.thresholds.memory_critical,
                "disk_warning": resource_monitor.thresholds.disk_warning,
                "disk_critical": resource_monitor.thresholds.disk_critical
            }
        }
        
    except Exception as e:
        logger.error(f"获取系统资源信息失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取系统资源信息失败"
        )


@router.get("/system/resources/history")
async def get_resource_history(
    count: int = Query(60, description="获取记录数量"),
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """获取资源使用历史"""
    try:
        user_id = get_current_user_id(credentials)
        resource_monitor = get_resource_monitor()
        
        # 获取历史指标
        history = resource_monitor.get_recent_metrics(count)
        
        return {
            "success": True,
            "history": [metrics.to_dict() for metrics in history],
            "count": len(history)
        }
        
    except Exception as e:
        logger.error(f"获取资源历史失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取资源历史失败"
        )


@router.get("/concurrency/status")
async def get_concurrency_status(
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """获取并发控制状态"""
    try:
        user_id = get_current_user_id(credentials)
        concurrency_controller = get_concurrency_controller()
        
        # 获取当前并发状态
        current_status = concurrency_controller.get_current_concurrency()
        
        # 获取队列统计
        queue_stats = concurrency_controller.get_queue_statistics()
        
        return {
            "success": True,
            "current_status": current_status,
            "statistics": queue_stats
        }
        
    except Exception as e:
        logger.error(f"获取并发状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取并发状态失败"
        )


@router.post("/concurrency/check")
async def check_task_acceptance(
    queue_name: str = Query(..., description="队列名称"),
    required_memory_mb: float = Query(100, description="所需内存（MB）"),
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """检查是否可以接受新任务"""
    try:
        user_id = get_current_user_id(credentials)
        concurrency_controller = get_concurrency_controller()
        
        # 检查任务接受条件
        result = concurrency_controller.can_accept_task(
            user_id=user_id,
            queue_name=queue_name,
            required_memory_mb=required_memory_mb
        )
        
        return {
            "success": True,
            "check_result": result
        }
        
    except Exception as e:
        logger.error(f"检查任务接受条件失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="检查任务接受条件失败"
        )


@router.put("/concurrency/limits")
async def update_concurrency_limits(
    limits: ConcurrencyLimitsUpdate,
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """更新并发限制（管理员功能）"""
    try:
        user_id = get_current_user_id(credentials)
        
        # 这里可以添加管理员权限检查
        # if not is_admin(user_id):
        #     raise HTTPException(status_code=403, detail="需要管理员权限")
        
        concurrency_controller = get_concurrency_controller()
        
        # 更新限制
        if limits.max_total_tasks is not None:
            concurrency_controller.limits.max_total_tasks = limits.max_total_tasks
        
        if limits.max_per_user is not None:
            concurrency_controller.limits.max_per_user = limits.max_per_user
        
        if limits.max_per_queue is not None:
            concurrency_controller.limits.max_per_queue.update(limits.max_per_queue)
        
        if limits.resource_based_scaling is not None:
            concurrency_controller.limits.resource_based_scaling = limits.resource_based_scaling
        
        # 获取更新后的状态
        updated_status = concurrency_controller.get_current_concurrency()
        
        return {
            "success": True,
            "message": "并发限制已更新",
            "updated_limits": updated_status["limits"]
        }
        
    except Exception as e:
        logger.error(f"更新并发限制失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="更新并发限制失败"
        )


@router.post("/concurrency/adjust")
async def adjust_limits_by_resources(
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """根据系统资源动态调整并发限制"""
    try:
        user_id = get_current_user_id(credentials)
        concurrency_controller = get_concurrency_controller()
        
        # 执行动态调整
        concurrency_controller.adjust_limits_based_on_resources()
        
        # 获取调整后的状态
        updated_status = concurrency_controller.get_current_concurrency()
        
        return {
            "success": True,
            "message": "并发限制已根据资源情况调整",
            "updated_limits": updated_status["limits"]
        }
        
    except Exception as e:
        logger.error(f"动态调整并发限制失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="动态调整并发限制失败"
        )


@router.post("/concurrency/cleanup")
async def cleanup_stale_tasks(
    max_age_hours: int = Query(2, description="清理多少小时前的任务"),
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """清理过期的任务记录"""
    try:
        user_id = get_current_user_id(credentials)
        concurrency_controller = get_concurrency_controller()
        
        # 执行清理
        cleaned_count = concurrency_controller.cleanup_stale_tasks(max_age_hours)
        
        return {
            "success": True,
            "message": f"清理了 {cleaned_count} 个过期任务记录",
            "cleaned_count": cleaned_count,
            "max_age_hours": max_age_hours
        }
        
    except Exception as e:
        logger.error(f"清理过期任务失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="清理过期任务失败"
        )


@router.get("/system/health")
async def get_system_health(
    credentials: HTTPAuthorizationCredentials = Depends(security)
):
    """获取系统整体健康状态"""
    try:
        user_id = get_current_user_id(credentials)
        resource_monitor = get_resource_monitor()
        concurrency_controller = get_concurrency_controller()
        
        # 获取资源健康状态
        current_metrics = resource_monitor.get_current_metrics()
        resource_health = resource_monitor.check_resource_health(current_metrics)
        
        # 获取并发状态
        concurrency_status = concurrency_controller.get_current_concurrency()
        
        # 计算整体健康评分
        health_score = 100
        
        if resource_health["overall"] == "critical":
            health_score -= 50
        elif resource_health["overall"] == "warning":
            health_score -= 20
        
        # 检查并发利用率
        total_utilization = 0
        queue_count = 0
        for queue_name, queue_info in concurrency_status.get("queue_counters", {}).items():
            max_count = concurrency_status["limits"]["max_per_queue"].get(queue_name, 1)
            utilization = queue_info / max_count if max_count > 0 else 0
            total_utilization += utilization
            queue_count += 1
        
        avg_utilization = total_utilization / queue_count if queue_count > 0 else 0
        
        if avg_utilization > 0.9:
            health_score -= 20
        elif avg_utilization > 0.8:
            health_score -= 10
        
        # 确定健康等级
        if health_score >= 90:
            health_level = "excellent"
        elif health_score >= 70:
            health_level = "good"
        elif health_score >= 50:
            health_level = "fair"
        else:
            health_level = "poor"
        
        return {
            "success": True,
            "health": {
                "score": health_score,
                "level": health_level,
                "resource_health": resource_health,
                "concurrency_utilization": avg_utilization,
                "recommendations": resource_health.get("recommendations", [])
            },
            "metrics": current_metrics.to_dict(),
            "concurrency": concurrency_status
        }
        
    except Exception as e:
        logger.error(f"获取系统健康状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取系统健康状态失败"
        )
