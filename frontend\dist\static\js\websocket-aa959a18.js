import{l as Q,h as v,m as w,E as $}from"./index-2c134546.js";import{u as V}from"./auth-e6295339.js";const F=Q("websocket",()=>{const n=v(null),r=v("disconnected"),c=v(0),b=v(null),d=v([]),p=v(0),D=3,i={maxReconnectAttempts:10,reconnectInterval:3e3,heartbeatInterval:9e4,heartbeatTimeout:45e3,connectionTimeout:15e3,maxIdleTime:6e5,maxBackoffDelay:6e4};let S=null,h=null,m=null;const k=new Map,g=w(()=>r.value==="connected"),_=w(()=>r.value==="connecting"),T=w(()=>r.value==="disconnected"),I=w(()=>r.value==="error"),A=async()=>_.value||g.value?Promise.resolve():new Promise((e,o)=>{try{r.value="connecting",b.value=null;const a=V().token;if(!a)throw new Error("没有有效的认证token");const W=window.location.protocol==="https:"?"wss:":"ws:",L={}.VITE_WS_HOST||window.location.hostname,B={}.VITE_WS_PORT||"8002",O=`${W}//${L}:${B}/ws/progress?token=${a}`;console.log("🔌 连接WebSocket:",O),n.value=new WebSocket(O),n.value.onopen=()=>{console.log("✅ WebSocket连接成功"),r.value="connected",c.value=0,H(),M(),l("connected"),e()},n.value.onmessage=s=>{try{const u=JSON.parse(s.data);console.log("📨 收到WebSocket消息:",{type:u.type,task_id:u.task_id,timestamp:new Date().toISOString(),dataSize:JSON.stringify(u).length}),N(u)}catch(u){console.error("❌ WebSocket消息解析失败:",{error:u.message,rawData:s.data,timestamp:new Date().toISOString()}),l("parse_error",{error:u,rawData:s.data})}},n.value.onclose=s=>{console.log("🔌 WebSocket连接关闭:",s.code,s.reason),r.value="disconnected",x(),l("disconnected",{code:s.code,reason:s.reason}),C(s.code,s.reason)&&c.value<i.maxReconnectAttempts&&E()},n.value.onerror=s=>{var u,R;console.error("❌ WebSocket连接错误:",{error:s,readyState:(u=n.value)==null?void 0:u.readyState,url:(R=n.value)==null?void 0:R.url,timestamp:new Date().toISOString(),reconnectAttempts:c.value}),r.value="error",b.value={error:s,timestamp:Date.now(),reconnectAttempts:c.value},l("error",s),o(s)}}catch(t){r.value="error",b.value=t,o(t)}}),y=()=>{r.value="disconnected",S&&(clearTimeout(S),S=null),x(),n.value&&(n.value.close(1e3,"Client disconnect"),n.value=null),console.log("🔌 WebSocket已断开")},f=e=>{if(n.value&&g.value)try{return n.value.send(JSON.stringify(e)),!0}catch(o){return console.error("❌ 发送WebSocket消息失败:",o),!1}else return d.value.push(e),!1},M=()=>{for(;d.value.length>0;){const e=d.value.shift();f(e)}},C=(e,o)=>[1e3,1001,1005,4e3,4001,4002].includes(e)?(console.log(`🚫 不重连 - 关闭码: ${e}, 原因: ${o}`),!1):e===1e3&&o&&o.includes("timeout")?(console.log("🔄 检测到超时关闭，尝试重连"),!0):(console.log(`🔄 可以重连 - 关闭码: ${e}, 原因: ${o}`),!0),E=()=>{c.value++;const e=i.reconnectInterval,o=Math.min(c.value-1,5),t=Math.min(e*Math.pow(1.5,o),i.maxBackoffDelay);console.log(`🔄 ${t}ms后尝试第${c.value}次重连... (最大${i.maxReconnectAttempts}次)`),S=setTimeout(()=>{!g.value&&c.value<=i.maxReconnectAttempts&&(console.log(`🔄 执行第${c.value}次重连尝试`),A().catch(a=>{console.error("❌ WebSocket重连失败:",a),c.value<i.maxReconnectAttempts?E():console.error("❌ 已达到最大重连次数，停止重连")}))},t)},H=()=>{p.value=0,h=setInterval(()=>{if(g.value){if(n.value&&n.value.readyState!==WebSocket.OPEN){console.warn("⚠️ WebSocket连接状态异常，尝试重连"),y();return}f({type:"ping",timestamp:Date.now()}),m=setTimeout(()=>{p.value++,console.warn(`⚠️ 心跳超时 (${p.value}/${D})`),p.value>=D&&(console.warn("⚠️ 连续心跳超时，重连WebSocket"),y())},i.heartbeatTimeout)}},i.heartbeatInterval)},x=()=>{h&&(clearInterval(h),h=null),m&&(clearTimeout(m),m=null)},N=e=>{try{if(!e||typeof e!="object"){console.error("❌ 无效的消息格式:",e);return}if(!e.type){console.error("❌ 消息缺少type字段:",e);return}switch(console.log("📨 处理WebSocket消息:",{type:e.type,task_id:e.task_id,hasData:!!e.data}),l("message",e),e.type){case"connection_established":console.log("🔗 WebSocket连接已确认:",e),l("connection_established",e);break;case"heartbeat":case"pong":m&&(clearTimeout(m),m=null),p.value=0,console.log("💓 心跳响应 - 连接健康");break;case"progress":case"progress_update":const o={task_id:e.task_id,state:e.state,ready:e.ready,successful:e.successful,failed:e.failed,result:e.result,traceback:e.traceback,user_id:e.user_id,progress:e.progress};console.log("📈 文档进度更新:",{taskId:e.task_id,messageType:e.type,progressData:o}),e.task_id?l("progress",o):console.warn("⚠️ 进度消息缺少task_id:",e);break;case"task_completed":const t={task_id:e.task_id,...e.data};console.log("📨 WebSocket完成payload:",t),console.log("✅ WebSocket任务完成:",t),l("task_completed",t),$.success("任务处理完成");break;case"task_failed":const a={task_id:e.task_id,...e.data};console.log("❌ 任务失败:",a),l("task_failed",a),$.error(`任务处理失败: ${a.error||a.error_message||"未知错误"}`);break;case"error":console.log("⚠️ 服务器错误:",e),l("error",e),$.error(`服务器错误: ${e.message||"未知错误"}`);break;default:console.warn("⚠️ 未知的WebSocket消息类型:",e.type),console.log("📋 完整消息内容:",e)}}catch(o){console.error("❌ 处理WebSocket消息失败:",{error:o.message,data:e,timestamp:new Date().toISOString()}),l("handle_error",{error:o,data:e})}},P=(e,o)=>(k.has(e)||k.set(e,new Set),k.get(e).add(o),()=>{const t=k.get(e);t&&t.delete(o)}),J=(e,o)=>{const t=k.get(e);t&&t.delete(o)},l=(e,o=null)=>{const t=k.get(e);t&&t.forEach(a=>{try{a(o)}catch(W){console.error(`事件监听器错误 (${e}):`,W)}})};return{connectionStatus:r,reconnectAttempts:c,lastError:b,isConnected:g,isConnecting:_,isDisconnected:T,hasError:I,connect:A,disconnect:y,send:f,subscribeTask:e=>{console.log(`🔔 订阅任务: ${e}`),console.log(`🔍 WebSocket状态: 连接=${g.value}, 状态=${r.value}`),console.log(`📦 消息队列长度: ${d.value.length}`);const o={type:"subscribe",task_id:e};console.log("📤 准备发送订阅消息:",o);const t=f(o);return console.log(`📤 订阅消息发送结果: ${t}`),t||console.log("⚠️ 订阅消息未立即发送，已加入队列"),t},unsubscribeTask:e=>f({type:"unsubscribe",task_id:e}),on:P,off:J,getStatus:()=>({status:r.value,isConnected:g.value,isConnecting:_.value,isDisconnected:T.value,hasError:I.value,reconnectAttempts:c.value,lastError:b.value})}});export{F as useWebSocketStore};
