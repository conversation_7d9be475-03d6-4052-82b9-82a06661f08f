#!/usr/bin/env python3
"""
创建一个简单的测试音频文件
"""
import numpy as np
import wave
import os

def create_test_audio(filename="test_audio.wav", duration=3, sample_rate=44100):
    """创建一个简单的测试音频文件"""
    # 生成一个简单的正弦波音频
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    # 440Hz的A音符
    frequency = 440
    audio_data = np.sin(2 * np.pi * frequency * t)
    
    # 转换为16位整数
    audio_data = (audio_data * 32767).astype(np.int16)
    
    # 保存为WAV文件
    with wave.open(filename, 'w') as wav_file:
        wav_file.setnchannels(1)  # 单声道
        wav_file.setsampwidth(2)  # 16位
        wav_file.setframerate(sample_rate)
        wav_file.writeframes(audio_data.tobytes())
    
    print(f"✅ 测试音频文件已创建: {filename}")
    print(f"   时长: {duration}秒")
    print(f"   采样率: {sample_rate}Hz")
    print(f"   文件大小: {os.path.getsize(filename)} bytes")

if __name__ == "__main__":
    create_test_audio()
