#!/usr/bin/env python3
"""检查最近的任务记录"""

import sqlite3
import sys
import json
from pathlib import Path
from datetime import datetime, timedelta

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_recent_tasks():
    """检查最近的任务记录"""
    db_path = project_root / "data" / "speech_platform.db"
    
    if not db_path.exists():
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # 查找最近24小时的任务
        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S')
        
        cursor.execute("""
            SELECT task_id, task_type, task_name, status, progress_percentage, 
                   created_at, completed_at, error_message
            FROM task_records 
            WHERE created_at > ?
            ORDER BY created_at DESC 
            LIMIT 10
        """, (yesterday,))
        
        results = cursor.fetchall()
        
        if not results:
            print("没有找到最近24小时的任务")
            # 查找所有任务
            cursor.execute("""
                SELECT task_id, task_type, task_name, status, progress_percentage, 
                       created_at, completed_at, error_message
                FROM task_records 
                ORDER BY created_at DESC 
                LIMIT 10
            """)
            results = cursor.fetchall()
            print("显示最近10个任务:")
        else:
            print("最近24小时的任务:")
        
        print("-" * 120)
        
        for row in results:
            task_id, task_type, task_name, status, progress, created_at, completed_at, error = row
            print(f"任务ID: {task_id}")
            print(f"任务类型: {task_type}")
            print(f"任务名称: {task_name}")
            print(f"状态: {status}")
            print(f"进度: {progress}%")
            print(f"创建时间: {created_at}")
            print(f"完成时间: {completed_at}")
            print(f"错误信息: {error or '无'}")
            print("-" * 120)
        
        # 检查audio_files表
        cursor.execute("""
            SELECT id, filename, original_filename, status, created_at, updated_at
            FROM audio_files 
            WHERE original_filename = '对话.mp3'
            ORDER BY created_at DESC
        """)
        
        audio_files = cursor.fetchall()
        
        if audio_files:
            print("\n对话.mp3 文件记录:")
            print("-" * 120)
            
            for row in audio_files:
                file_id, filename, orig_filename, status, created_at, updated_at = row
                print(f"文件ID: {file_id}")
                print(f"文件名: {filename}")
                print(f"原始文件名: {orig_filename}")
                print(f"状态: {status}")
                print(f"创建时间: {created_at}")
                print(f"更新时间: {updated_at}")
                print("-" * 120)
        
        conn.close()
        
    except Exception as e:
        print(f"检查任务时出错: {e}")

if __name__ == "__main__":
    check_recent_tasks()
