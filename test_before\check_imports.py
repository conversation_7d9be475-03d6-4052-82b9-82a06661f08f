#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语音处理分析页面导入检查脚本
检查所有依赖模块的可用性
"""

import sys
import os
import traceback
from pathlib import Path

def check_basic_imports():
    """检查基础导入"""
    print("🔍 检查基础导入...")
    
    basic_modules = [
        ("streamlit", "import streamlit as st"),
        ("numpy", "import numpy as np"),
        ("pandas", "import pandas as pd"),
        ("matplotlib", "import matplotlib.pyplot as plt"),
        ("soundfile", "import soundfile as sf"),
        ("pathlib", "from pathlib import Path"),
        ("tempfile", "import tempfile"),
        ("json", "import json"),
        ("time", "import time"),
        ("traceback", "import traceback"),
    ]
    
    success_count = 0
    for name, import_code in basic_modules:
        try:
            exec(import_code)
            print(f"✅ {name}: 导入成功")
            success_count += 1
        except Exception as e:
            print(f"❌ {name}: 导入失败 - {e}")
    
    print(f"基础模块: {success_count}/{len(basic_modules)} 成功\n")
    return success_count == len(basic_modules)

def check_utils_imports():
    """检查utils模块导入"""
    print("🔍 检查utils模块导入...")
    
    # 添加utils路径
    current_dir = Path(__file__).parent
    utils_dir = current_dir / "utils"
    if utils_dir.exists():
        sys.path.insert(0, str(current_dir))
        print(f"✅ 添加路径: {current_dir}")
    else:
        print(f"❌ utils目录不存在: {utils_dir}")
        return False
    
    utils_modules = [
        ("speech_recognition_utils", "from utils.speech_recognition_utils import load_vad_model, vad_segment"),
        ("audio_preprocessing", "from utils.audio_preprocessing import AudioPreprocessor, check_audio_quality"),
        ("monitoring_components", "from utils.monitoring_components import ProcessingMonitor, MonitoringComponents"),
        ("file_upload_component", "from utils.file_upload_component import create_audio_uploader"),
        ("enhanced_batch_processor", "from utils.enhanced_batch_processor import create_audio_batch_processor"),
        ("progress_component", "from utils.progress_component import ProgressComponent"),
        ("speaker_recognition", "from utils.speaker_recognition import SpeakerRecognition"),
        ("speech_recognition_core", "from utils.speech_recognition_core import SenseVoiceRecognizer"),
    ]
    
    success_count = 0
    available_modules = []
    
    for name, import_code in utils_modules:
        try:
            exec(import_code)
            print(f"✅ {name}: 导入成功")
            success_count += 1
            available_modules.append(name)
        except ImportError as e:
            print(f"⚠️ {name}: 模块不存在 - {e}")
        except Exception as e:
            print(f"❌ {name}: 导入失败 - {e}")
    
    print(f"Utils模块: {success_count}/{len(utils_modules)} 成功")
    print(f"可用模块: {', '.join(available_modules)}\n")
    
    return success_count, available_modules

def check_ml_imports():
    """检查机器学习相关导入"""
    print("🔍 检查机器学习模块导入...")
    
    ml_modules = [
        ("torch", "import torch"),
        ("torchaudio", "import torchaudio"),
        ("transformers", "import transformers"),
        ("sklearn", "from sklearn.cluster import AgglomerativeClustering"),
        ("scipy", "import scipy"),
        ("librosa", "import librosa"),
        ("speech_recognition", "import speech_recognition as sr"),
        ("pydub", "from pydub import AudioSegment"),
    ]
    
    success_count = 0
    for name, import_code in ml_modules:
        try:
            exec(import_code)
            print(f"✅ {name}: 导入成功")
            success_count += 1
        except Exception as e:
            print(f"❌ {name}: 导入失败 - {e}")
    
    print(f"机器学习模块: {success_count}/{len(ml_modules)} 成功\n")
    return success_count >= len(ml_modules) - 2  # 允许2个失败

def check_document_processing():
    """检查文档处理模块"""
    print("🔍 检查文档处理模块导入...")
    
    doc_modules = [
        ("PyPDF2", "from PyPDF2 import PdfReader"),
        ("docx", "from docx import Document"),
        ("openpyxl", "import openpyxl"),
        ("PIL", "from PIL import Image"),
        ("cv2", "import cv2"),
    ]
    
    success_count = 0
    for name, import_code in doc_modules:
        try:
            exec(import_code)
            print(f"✅ {name}: 导入成功")
            success_count += 1
        except Exception as e:
            print(f"❌ {name}: 导入失败 - {e}")
    
    print(f"文档处理模块: {success_count}/{len(doc_modules)} 成功\n")
    return success_count >= 3

def check_specific_functions():
    """检查特定功能函数"""
    print("🔍 检查特定功能函数...")
    
    # 检查VAD相关功能
    try:
        from utils.speech_recognition_utils import (
            load_vad_model, vad_segment, vad_segment_for_two_person,
            get_optimized_vad_params_for_two_person,
            check_audio_quality_for_speaker_recognition,
            set_offline_mode
        )
        print("✅ VAD功能: 所有函数可用")
        vad_available = True
    except Exception as e:
        print(f"❌ VAD功能: 部分函数不可用 - {e}")
        vad_available = False
    
    # 检查音频预处理功能
    try:
        from utils.audio_preprocessing import AudioPreprocessor, check_audio_quality
        print("✅ 音频预处理: 功能可用")
        preprocessing_available = True
    except Exception as e:
        print(f"❌ 音频预处理: 功能不可用 - {e}")
        preprocessing_available = False
    
    # 检查说话人识别功能
    try:
        from utils.speaker_recognition import SpeakerRecognition
        print("✅ 说话人识别: 功能可用")
        speaker_available = True
    except Exception as e:
        print(f"❌ 说话人识别: 功能不可用 - {e}")
        speaker_available = False
    
    # 检查语音识别核心功能
    try:
        from utils.speech_recognition_core import SenseVoiceRecognizer, SpeechRecognitionConfig
        print("✅ 语音识别核心: 功能可用")
        asr_available = True
    except Exception as e:
        print(f"❌ 语音识别核心: 功能不可用 - {e}")
        asr_available = False
    
    print()
    return {
        'vad': vad_available,
        'preprocessing': preprocessing_available,
        'speaker': speaker_available,
        'asr': asr_available
    }

def check_model_paths():
    """检查模型路径"""
    print("🔍 检查模型路径...")
    
    model_dirs = [
        "models",
        "models/model_dir",
        "models/vad_models",
        "models/speaker_models",
        "models/asr_models"
    ]
    
    for model_dir in model_dirs:
        path = Path(model_dir)
        if path.exists():
            files = list(path.glob("*"))
            print(f"✅ {model_dir}: 存在 ({len(files)} 个文件)")
        else:
            print(f"❌ {model_dir}: 不存在")
    
    print()

def generate_recommendations(results):
    """生成修复建议"""
    print("📋 修复建议:")
    print("=" * 50)
    
    basic_ok, utils_count, utils_modules, ml_ok, doc_ok, functions = results
    
    if not basic_ok:
        print("🔧 基础模块问题:")
        print("   - 运行: uv pip install streamlit numpy pandas matplotlib soundfile")
    
    if utils_count < 4:
        print("🔧 Utils模块问题:")
        print("   - 检查utils目录是否存在")
        print("   - 确保所有utils模块文件完整")
        missing_modules = set([
            "speech_recognition_utils", "audio_preprocessing", "monitoring_components",
            "file_upload_component", "enhanced_batch_processor", "progress_component",
            "speaker_recognition", "speech_recognition_core"
        ]) - set(utils_modules)
        if missing_modules:
            print(f"   - 缺失模块: {', '.join(missing_modules)}")
    
    if not ml_ok:
        print("🔧 机器学习模块问题:")
        print("   - 运行: uv pip install torch torchaudio transformers scikit-learn")
        print("   - 运行: uv pip install librosa SpeechRecognition pydub")
    
    if not doc_ok:
        print("🔧 文档处理模块问题:")
        print("   - 运行: uv pip install PyPDF2 python-docx openpyxl Pillow opencv-python")
    
    # 功能特定建议
    if not functions['vad']:
        print("🔧 VAD功能问题:")
        print("   - 检查utils/speech_recognition_utils.py文件")
        print("   - 确保VAD模型文件存在")
    
    if not functions['preprocessing']:
        print("🔧 音频预处理问题:")
        print("   - 检查utils/audio_preprocessing.py文件")
    
    if not functions['speaker']:
        print("🔧 说话人识别问题:")
        print("   - 检查utils/speaker_recognition.py文件")
        print("   - 确保说话人模型文件存在")
    
    if not functions['asr']:
        print("🔧 语音识别核心问题:")
        print("   - 检查utils/speech_recognition_core.py文件")
        print("   - 确保ASR模型文件存在")

def main():
    """主检查函数"""
    print("🔍 语音处理分析页面导入检查")
    print("=" * 50)
    print(f"Python版本: {sys.version}")
    print(f"工作目录: {os.getcwd()}")
    print()
    
    # 执行各项检查
    basic_ok = check_basic_imports()
    utils_count, utils_modules = check_utils_imports()
    ml_ok = check_ml_imports()
    doc_ok = check_document_processing()
    functions = check_specific_functions()
    check_model_paths()
    
    # 生成总结
    print("📊 检查结果总结:")
    print("=" * 50)
    print(f"基础模块: {'✅ 正常' if basic_ok else '❌ 有问题'}")
    print(f"Utils模块: {utils_count}/8 可用")
    print(f"机器学习: {'✅ 正常' if ml_ok else '❌ 有问题'}")
    print(f"文档处理: {'✅ 正常' if doc_ok else '❌ 有问题'}")
    print(f"VAD功能: {'✅ 可用' if functions['vad'] else '❌ 不可用'}")
    print(f"音频预处理: {'✅ 可用' if functions['preprocessing'] else '❌ 不可用'}")
    print(f"说话人识别: {'✅ 可用' if functions['speaker'] else '❌ 不可用'}")
    print(f"语音识别: {'✅ 可用' if functions['asr'] else '❌ 不可用'}")
    print()
    
    # 生成建议
    results = (basic_ok, utils_count, utils_modules, ml_ok, doc_ok, functions)
    generate_recommendations(results)
    
    # 总体评估
    total_score = sum([
        basic_ok,
        utils_count >= 4,
        ml_ok,
        doc_ok,
        functions['vad'],
        functions['preprocessing']
    ])
    
    print(f"\n总体评分: {total_score}/6")
    if total_score >= 5:
        print("🎉 系统基本就绪，可以运行语音处理功能")
    elif total_score >= 3:
        print("⚠️ 系统部分就绪，某些功能可能不可用")
    else:
        print("❌ 系统存在严重问题，需要修复后才能使用")

if __name__ == "__main__":
    main()
