#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的测试应用
"""

import streamlit as st
import os

# 页面配置
st.set_page_config(
    page_title="测试应用",
    page_icon="🎙️",
    layout="wide"
)

def main():
    st.title("🎙️ 测试应用")
    st.write("这是一个简化的测试应用，用于验证基本功能。")
    
    # 测试模型路径
    st.subheader("📁 模型路径测试")
    
    model_paths = {
        "VAD模型": r"C:\Users\<USER>\Documents\my_project\models\model_dir\fsmn_vad_zh",
        "SenseVoice": r"C:\Users\<USER>\Documents\my_project\models\model_dir\SenseVoiceSmall",
        "CAM++": r"C:\Users\<USER>\Documents\my_project\models\model_dir\cam++"
    }
    
    for name, path in model_paths.items():
        exists = os.path.exists(path)
        status = "✅" if exists else "❌"
        st.write(f"{status} **{name}**: {exists}")
        st.text(f"   路径: {path}")
    
    # 测试配置文件
    st.subheader("📄 配置文件测试")
    config_file = "speech_config.ini"
    config_exists = os.path.exists(config_file)
    status = "✅" if config_exists else "❌"
    st.write(f"{status} **配置文件**: {config_exists}")
    
    if config_exists:
        try:
            import configparser
            config = configparser.ConfigParser()
            config.read(config_file, encoding='utf-8')
            
            st.write("**配置内容:**")
            for section in config.sections():
                st.write(f"- [{section}]")
                for key, value in config.items(section):
                    st.write(f"  - {key} = {value}")
        except Exception as e:
            st.error(f"读取配置文件失败: {e}")
    
    # 测试导入
    st.subheader("📦 模块导入测试")
    
    modules_to_test = [
        ("streamlit", "Streamlit"),
        ("torch", "PyTorch"),
        ("soundfile", "SoundFile"),
        ("numpy", "NumPy"),
        ("pandas", "Pandas")
    ]
    
    for module_name, display_name in modules_to_test:
        try:
            __import__(module_name)
            st.write(f"✅ **{display_name}**: 导入成功")
        except ImportError as e:
            st.write(f"❌ **{display_name}**: 导入失败 - {e}")
    
    # 测试自定义模块
    st.subheader("🔧 自定义模块测试")
    
    try:
        from utils.speech_recognition_utils import check_model_availability
        result = check_model_availability()
        st.write(f"✅ **模型可用性检查**: {result}")
    except Exception as e:
        st.write(f"❌ **模型可用性检查**: 失败 - {e}")
    
    try:
        from pages.语音处理分析 import get_model_path
        vad_path = get_model_path('vad')
        st.write(f"✅ **获取VAD模型路径**: {vad_path}")
    except Exception as e:
        st.write(f"❌ **获取VAD模型路径**: 失败 - {e}")

if __name__ == "__main__":
    main()
