#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Docker修复验证脚本
验证Redis连接和FunASR环境修复效果
"""

import os
import sys
import logging
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def test_redis_connection():
    """测试Redis连接"""
    logger.info("=== 测试Redis连接 ===")
    
    try:
        # 测试环境变量读取
        from backend.services.concurrency_control import _get_redis_url
        redis_url = _get_redis_url()
        logger.info(f"Redis URL: {redis_url}")
        
        # 测试Redis连接
        import redis
        r = redis.Redis.from_url(redis_url, decode_responses=True)
        r.ping()
        logger.info("✅ Redis连接成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ Redis连接失败: {e}")
        return False

def test_funasr_environment():
    """测试FunASR环境"""
    logger.info("=== 测试FunASR环境 ===")
    
    try:
        from backend.utils.audio.audio_preprocessing import check_funasr_environment
        available, info = check_funasr_environment()
        
        if available:
            logger.info(f"✅ FunASR可用: {info}")
        else:
            logger.warning(f"❌ FunASR不可用: {info}")
            
        return available
        
    except Exception as e:
        logger.error(f"❌ FunASR测试失败: {e}")
        return False

def test_concurrency_controller():
    """测试并发控制器"""
    logger.info("=== 测试并发控制器 ===")
    
    try:
        from backend.services.concurrency_control import get_concurrency_controller
        controller = get_concurrency_controller()
        
        # 测试获取当前状态
        status = controller.get_current_concurrency()
        logger.info(f"并发状态: {status}")
        
        logger.info("✅ 并发控制器工作正常")
        return True
        
    except Exception as e:
        logger.error(f"❌ 并发控制器测试失败: {e}")
        return False

def test_audio_preprocessing():
    """测试音频预处理"""
    logger.info("=== 测试音频预处理 ===")
    
    try:
        from backend.utils.audio.audio_preprocessing import AudioPreprocessor, log_environment_info
        
        # 记录环境信息
        log_environment_info()
        
        # 创建预处理器实例
        preprocessor = AudioPreprocessor()
        logger.info("✅ 音频预处理器创建成功")
        return True
        
    except Exception as e:
        logger.error(f"❌ 音频预处理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🚀 开始Docker修复验证测试")
    
    results = {
        'redis': test_redis_connection(),
        'funasr': test_funasr_environment(),
        'concurrency': test_concurrency_controller(),
        'audio': test_audio_preprocessing()
    }
    
    logger.info("=== 测试结果汇总 ===")
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        logger.info(f"{test_name}: {status}")
    
    all_passed = all(results.values())
    if all_passed:
        logger.info("🎉 所有测试通过！修复成功")
    else:
        logger.warning("⚠️ 部分测试失败，需要进一步检查")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
