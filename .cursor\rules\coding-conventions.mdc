---
description: 
globs: 
alwaysApply: true
---
# 代码编写命名规范

## Python后端编码规范

### 文件和目录命名
- 文件名：使用小写字母和下划线，如 `user_service.py`
- 目录名：使用小写字母和下划线，如 `api/v1/endpoints/`
- 包名：使用小写字母，如 `backend.core.config`

### 变量和函数命名
```python
# 变量命名：snake_case
user_name = "张三"
audio_file_path = "/path/to/audio.wav"
task_result_data = {"status": "success"}

# 函数命名：snake_case
def process_audio_file(file_path: str) -> dict:
    """处理音频文件"""
    pass

async def get_user_profile(user_id: int) -> UserProfile:
    """获取用户资料"""
    pass

# 私有函数：以下划线开头
def _validate_input_data(data: dict) -> bool:
    """内部验证函数"""
    pass
```

### 类命名
```python
# 类名：PascalCase
class UserService:
    """用户服务类"""
    pass

class AudioProcessor:
    """音频处理器"""
    pass

class RAGQueryEngine:
    """RAG查询引擎"""
    pass

# 异常类：以Error结尾
class ValidationError(Exception):
    """验证错误"""
    pass

class AudioProcessingError(Exception):
    """音频处理错误"""
    pass
```

### 常量命名
```python
# 常量：UPPER_CASE
MAX_FILE_SIZE = 100 * 1024 * 1024  # 100MB
DEFAULT_TIMEOUT = 30
API_VERSION = "v1"

# 枚举值：UPPER_CASE
class TaskStatus(Enum):
    PENDING = "PENDING"
    RUNNING = "RUNNING"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"
```

## Vue.js前端编码规范

### 文件命名
```
# 组件文件：PascalCase
UserProfile.vue
AudioPlayer.vue
DocumentManager.vue

# 工具文件：camelCase
audioUtils.js
httpRequest.js
dateFormatter.js

# 页面文件：PascalCase
Home.vue
Dashboard.vue
KnowledgeBase.vue
```

### 变量和函数命名
```javascript
// 变量命名：camelCase
const userName = '张三'
const audioFilePath = '/path/to/audio.wav'
const taskResultData = { status: 'success' }

// 函数命名：camelCase
function processAudioFile(filePath) {
  // 处理音频文件
}

const getUserProfile = async (userId) => {
  // 获取用户资料
}

// 事件处理函数：以handle开头
const handleFileUpload = (file) => {
  // 处理文件上传
}

const handleSubmitForm = async (formData) => {
  // 处理表单提交
}
```

### 组件命名
```vue
<!-- 组件命名：PascalCase -->
<template>
  <div class="user-profile">
    <AudioPlayer :src="audioSrc" />
    <DocumentViewer :document="document" />
  </div>
</template>

<script setup>
// 组件引入：PascalCase
import AudioPlayer from '@/components/AudioPlayer.vue'
import DocumentViewer from '@/components/DocumentViewer.vue'

// Props命名：camelCase
const props = defineProps({
  userId: Number,
  userName: String,
  audioSrc: String
})
</script>
```

### CSS类命名
```scss
// BEM命名规范
.user-profile {
  &__header {
    // 用户资料头部
  }
  
  &__content {
    // 用户资料内容
  }
  
  &--loading {
    // 加载状态
  }
}

.audio-player {
  &__controls {
    // 控制按钮
  }
  
  &__progress {
    // 进度条
  }
  
  &--paused {
    // 暂停状态
  }
}
```

## 通用编码规范

### 注释规范
```python
"""
模块级别文档字符串
描述模块的主要功能和用途
"""

class UserService:
    """
    用户服务类
    
    提供用户相关的业务逻辑处理，包括：
    - 用户注册和登录
    - 用户信息管理
    - 用户权限验证
    """
    
    def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """
        用户认证
        
        Args:
            username: 用户名
            password: 密码
            
        Returns:
            User: 认证成功返回用户对象，失败返回None
            
        Raises:
            ValidationError: 输入参数验证失败
        """
        pass
```

```javascript
/**
 * 音频处理工具函数
 * @param {File} audioFile - 音频文件对象
 * @param {Object} options - 处理选项
 * @param {number} options.sampleRate - 采样率
 * @param {string} options.format - 输出格式
 * @returns {Promise<Blob>} 处理后的音频数据
 */
async function processAudio(audioFile, options = {}) {
  // 处理音频文件
}
```

### 类型注解规范
```python
from typing import List, Dict, Optional, Union

# 函数类型注解
def get_users(
    skip: int = 0, 
    limit: int = 100,
    filters: Optional[Dict[str, Any]] = None
) -> List[User]:
    """获取用户列表"""
    pass

# 变量类型注解
users: List[User] = []
user_cache: Dict[int, User] = {}
current_user: Optional[User] = None
```

### 错误处理规范
```python
# 具体的异常处理
try:
    result = process_audio_file(file_path)
except FileNotFoundError:
    logger.error(f"音频文件不存在: {file_path}")
    raise AudioProcessingError("音频文件不存在")
except PermissionError:
    logger.error(f"无权限访问文件: {file_path}")
    raise AudioProcessingError("无权限访问文件")
except Exception as e:
    logger.error(f"音频处理失败: {e}")
    raise AudioProcessingError(f"音频处理失败: {e}")
```

```javascript
// Promise错误处理
try {
  const result = await processAudio(audioFile)
  return result
} catch (error) {
  console.error('音频处理失败:', error)
  ElMessage.error('音频处理失败，请重试')
  throw error
}
```

### 导入排序规范
```python
# 标准库导入
import os
import sys
from pathlib import Path
from typing import List, Dict, Optional

# 第三方库导入
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
import numpy as np

# 项目内部导入
from backend.core.config import settings
from backend.services.user_service import UserService
from backend.models.user import User
```

```javascript
// 第三方库导入
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import axios from 'axios'

// 项目内部导入
import { useUserStore } from '@/stores/user'
import { formatDate } from '@/utils/dateFormatter'
import AudioPlayer from '@/components/AudioPlayer.vue'
```

### Git提交信息规范
```
格式：<type>(<scope>): <subject>

type类型：
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式化
- refactor: 重构代码
- test: 测试相关
- chore: 构建工具或辅助工具变动

示例：
feat(auth): 添加JWT认证功能
fix(audio): 修复音频播放器暂停问题
docs(api): 更新API文档
style(frontend): 代码格式化
refactor(rag): 重构RAG查询引擎
test(speech): 添加语音识别单元测试
chore(deps): 升级依赖包版本
```

