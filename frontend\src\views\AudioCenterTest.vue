<template>
  <div class="audio-center-test">
    <h1>音频处理中心测试页面</h1>
    <p>如果您能看到这个页面，说明基本路由和组件渲染正常。</p>
    
    <div class="test-section">
      <h2>CSS变量测试</h2>
      <div class="test-card">
        <p>这是一个测试卡片，用于验证CSS变量是否正常工作。</p>
      </div>
    </div>
    
    <div class="test-section">
      <h2>Element Plus组件测试</h2>
      <el-button type="primary">主要按钮</el-button>
      <el-button type="success">成功按钮</el-button>
      <el-button type="warning">警告按钮</el-button>
      <el-button type="danger">危险按钮</el-button>
    </div>
    
    <div class="test-section">
      <h2>API连接测试</h2>
      <el-button @click="testAPI" :loading="loading">测试后端连接</el-button>
      <p v-if="apiResult">{{ apiResult }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { setToken, getToken } from '@/utils/auth'
import { useUserStore } from '@/stores/user'
import request from '@/utils/request'

const loading = ref(false)
const apiResult = ref('')
const userStore = useUserStore()

// 设置测试token
onMounted(() => {
  console.log('🧪 测试页面初始化')
  console.log('当前token:', getToken())
  console.log('用户store状态:', {
    isAuthenticated: userStore.isAuthenticated,
    hasToken: !!userStore.token,
    hasUser: !!userStore.user,
    user: userStore.user
  })

  // 如果没有token，设置测试token
  if (!getToken()) {
    setToken('demo-token')
    ElMessage.info('已设置测试token，可以测试API连接')
  } else {
    ElMessage.info('发现现有token，可以测试API连接')
  }
})

const testAPI = async () => {
  loading.value = true
  try {
    const response = await request.get('/audio/')
    apiResult.value = '✅ 后端API连接成功'
    ElMessage.success('后端连接正常')
    console.log('API响应:', response.data)
  } catch (error) {
    console.error('API测试失败:', error)
    if (error.response) {
      apiResult.value = `❌ 后端API连接失败 (${error.response.status}): ${error.response.data?.detail || error.response.statusText}`
    } else {
      apiResult.value = `❌ 连接错误: ${error.message}`
    }
    ElMessage.error('后端连接失败')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.audio-center-test {
  padding: var(--spacing-lg, 24px);
  background: var(--primary-bg, #0d1117);
  color: var(--text-primary, #f0f6fc);
  min-height: 100vh;
}

.test-section {
  margin-bottom: var(--spacing-xl, 48px);
  padding: var(--spacing-lg, 24px);
  background: var(--surface-bg, #30363d);
  border-radius: var(--radius-md, 12px);
  border: 1px solid var(--border-color, #30363d);
}

.test-card {
  padding: var(--spacing-md, 16px);
  background: var(--card-bg, rgba(33, 38, 45, 0.95));
  border-radius: var(--radius-sm, 6px);
  border: 1px solid var(--border-color, #30363d);
}

h1 {
  color: var(--text-primary, #f0f6fc);
  margin-bottom: var(--spacing-lg, 24px);
}

h2 {
  color: var(--text-primary, #f0f6fc);
  margin-bottom: var(--spacing-md, 16px);
}

p {
  color: var(--text-secondary, #8b949e);
  line-height: 1.6;
}
</style>
