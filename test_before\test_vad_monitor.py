#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
VAD监控组件综合测试脚本
测试VAD处理流程和监控组件的集成效果
"""

import time
import numpy as np
from utils.monitoring_components import ProcessingMonitor

def simulate_vad_processing_with_monitor():
    """模拟VAD处理过程，包含完整的监控集成"""
    print("🎯 开始VAD处理监控测试")
    print("=" * 50)
    
    # 创建监控器
    monitor = ProcessingMonitor()
    
    try:
        # 1. 初始化任务
        task_id = "vad_test_001"
        monitor.start_monitoring()
        print(f"📝 创建任务: {task_id}")
        
        # 2. 模拟音频文件读取
        monitor.update_task(task_id, "running", 20, {"step": "读取音频文件中..."})
        print("🎵 读取音频文件...")
        time.sleep(1)
        
        # 3. 模拟模型加载
        monitor.update_task(task_id, "running", 40, {"step": "加载VAD模型..."})
        print("🤖 加载VAD模型...")
        time.sleep(1.5)
        
        # 4. 模拟VAD处理
        monitor.update_task(task_id, "running", 60, {"step": "执行语音活动检测..."})
        print("🔍 执行VAD检测...")
        time.sleep(2)
        
        # 添加性能指标
        monitor.add_performance_metric("audio_duration_sec", 30.5, task_id)
        monitor.add_performance_metric("segments_detected", 8, task_id)
        monitor.add_performance_metric("processing_time_sec", 4.2, task_id)
        
        # 5. 结果处理
        monitor.update_task(task_id, "running", 90, {"step": "处理检测结果..."})
        print("📊 处理检测结果...")
        time.sleep(0.5)
        
        # 6. 完成任务
        monitor.update_task(task_id, "completed", 100, {
            "status": "success",
            "segments_count": 8,
            "audio_duration": "30.5秒",
            "processing_time": "4.2秒"
        }, processing_time=4.2)
        
        print("✅ VAD处理完成!")
        
        # 显示统计信息
        stats = monitor.get_stats()
        print("\n📈 处理统计:")
        print(f"   总任务数: {len(monitor.current_tasks)}")
        print(f"   已完成: {len(monitor.completed_tasks)}")
        print(f"   运行中: {len([t for t in monitor.current_tasks.values() if t['status'] == 'running'])}")
        print(f"   失败数: {len(monitor.failed_tasks)}")
        
        # 显示任务详情
        task_info = monitor.current_tasks.get(task_id)
        if task_info:
            print(f"\n🔍 任务详情 [{task_id}]:")
            print(f"   状态: {task_info['status']}")
            print(f"   进度: {task_info['progress']}%")
            print(f"   处理时间: {task_info.get('processing_time', 'N/A')}秒")
            
            # 显示性能指标
            metrics = [m for m in monitor.performance_data if m['task_id'] == task_id]
            if metrics:
                print(f"   性能指标:")
                for metric in metrics:
                    print(f"     {metric['metric']}: {metric['value']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        monitor.add_error(f"VAD_TEST_ERROR: {str(e)}", task_id, "VAD_TEST_ERROR")
        return False

def test_monitor_error_handling():
    """测试监控器的错误处理功能"""
    print("\n🧪 测试错误处理功能")
    print("-" * 30)
    
    monitor = ProcessingMonitor()
    task_id = "error_test_001"
    monitor.start_monitoring()
    
    # 模拟各种错误类型
    error_types = [
        ("AUDIO_READ_ERROR", "无法读取音频文件"),
        ("MODEL_LOAD_ERROR", "模型加载失败"),
        ("VAD_PROCESSING_ERROR", "VAD处理异常")
    ]
    
    for error_type, error_msg in error_types:
        monitor.add_error(f"{error_type}: {error_msg}", task_id, error_type)
        print(f"   记录错误: {error_type}")
    
    # 获取错误统计
    error_logs = monitor.error_log
    print(f"\n📋 错误日志数量: {len(error_logs)}")
    
    return True

def test_system_monitoring():
    """测试系统资源监控"""
    print("\n💻 测试系统资源监控")
    print("-" * 30)
    
    monitor = ProcessingMonitor()
    monitor.start_monitoring()
    
    # 让系统监控运行一会儿
    time.sleep(3)
    
    # 获取系统信息
    if monitor.system_stats:
        latest_stats = monitor.system_stats[-1]
        print(f"   内存使用: {latest_stats.get('memory_percent', 'N/A')}%")
        print(f"   CPU使用: {latest_stats.get('cpu_percent', 'N/A')}%")
        print(f"   进程内存: {latest_stats.get('process_memory_mb', 'N/A'):.1f} MB")
        print(f"   可用内存: {latest_stats.get('memory_available_gb', 'N/A'):.1f} GB")
    else:
        print("   系统监控数据暂未收集")
    
    monitor.stop_monitoring()
    return True

def main():
    """主测试函数"""
    print("🎯 VAD监控组件综合测试")
    print("=" * 50)
    
    test_results = []
    
    # 测试1: VAD处理监控
    print("\n📊 测试1: VAD处理流程监控")
    result1 = simulate_vad_processing_with_monitor()
    test_results.append(("VAD处理监控", result1))
    
    # 测试2: 错误处理
    result2 = test_monitor_error_handling()
    test_results.append(("错误处理", result2))
    
    # 测试3: 系统监控
    result3 = test_system_monitoring()
    test_results.append(("系统监控", result3))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("🏁 测试结果总结:")
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    all_passed = all(result for _, result in test_results)
    
    if all_passed:
        print("\n🎉 所有测试通过! VAD监控组件可以正常使用")
        print("\n💡 下一步建议:")
        print("   1. 运行 'streamlit run Home.py' 启动应用")
        print("   2. 进入'语音处理分析'页面")
        print("   3. 勾选'显示实时监控'选项")
        print("   4. 上传音频文件测试真实的VAD处理监控")
    else:
        print("\n⚠️  部分测试失败，请检查环境配置")
    
    return all_passed

if __name__ == "__main__":
    main() 