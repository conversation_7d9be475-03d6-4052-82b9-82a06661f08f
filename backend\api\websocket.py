"""
WebSocket实时通信端点
用于实时推送任务进度更新
"""

import json
import asyncio
from typing import Dict, Set
from fastapi import WebSocket, WebSocketDisconnect, Depends, HTTPException, Query
from fastapi.security import HTTPAuthorizationCredentials
from loguru import logger
from backend.core.security import get_current_user_id, security
from backend.core.task_queue import get_task_manager
from backend.services.enhanced_progress_service import get_enhanced_progress_service

# 🔧 导入WebSocket异常类
try:
    from websockets.exceptions import ConnectionClosedError, ConnectionClosedOK
except ImportError:
    # 如果websockets库不可用，创建模拟异常类
    class ConnectionClosedError(Exception):
        pass
    class ConnectionClosedOK(Exception):
        pass


class ConnectionManager:
    """WebSocket连接管理器"""
    
    def __init__(self):
        # 存储活跃连接: {user_id: {websocket, subscribed_tasks}}
        self.active_connections: Dict[str, Dict] = {}
        # 任务订阅映射: {task_id: {user_ids}}
        self.task_subscriptions: Dict[str, Set[str]] = {}
    
    async def connect(self, websocket: WebSocket, user_id: str):
        """接受WebSocket连接"""
        await websocket.accept()
        self.active_connections[user_id] = {
            'websocket': websocket,
            'subscribed_tasks': set()
        }
        logger.info(f"用户 {user_id} WebSocket连接成功")
    
    def disconnect(self, user_id: str):
        """断开WebSocket连接"""
        if user_id in self.active_connections:
            # 清理任务订阅
            subscribed_tasks = self.active_connections[user_id]['subscribed_tasks']
            for task_id in subscribed_tasks:
                if task_id in self.task_subscriptions:
                    self.task_subscriptions[task_id].discard(user_id)
                    if not self.task_subscriptions[task_id]:
                        del self.task_subscriptions[task_id]
            
            del self.active_connections[user_id]
            logger.info(f"用户 {user_id} WebSocket连接断开")
    
    async def send_personal_message(self, message: dict, user_id: str, retry_count: int = 0):
        """发送个人消息 - 🔧 增强错误处理和重试机制"""
        max_retries = 3

        if user_id in self.active_connections:
            try:
                websocket = self.active_connections[user_id]['websocket']

                # 🔧 详细日志记录
                logger.debug(f"📤 发送消息给用户 {user_id}: {message.get('type', 'unknown')}")

                # 🔧 检查WebSocket连接状态
                if websocket.client_state.name != 'CONNECTED':
                    logger.warning(f"⚠️ WebSocket连接状态异常: {user_id}, 状态: {websocket.client_state.name}")
                    self.disconnect(user_id)
                    return False

                # 🔧 序列化检查
                try:
                    message_json = json.dumps(message)
                except (TypeError, ValueError) as e:
                    logger.error(f"❌ 消息序列化失败: {user_id}, 错误: {e}, 消息: {message}")
                    return False

                await websocket.send_text(message_json)
                logger.debug(f"✅ 消息发送成功: {user_id}")
                return True

            except ConnectionClosedError:
                logger.warning(f"🔌 WebSocket连接已关闭: {user_id}")
                self.disconnect(user_id)
                return False
            except ConnectionClosedOK:
                logger.info(f"🔌 WebSocket连接正常关闭: {user_id}")
                self.disconnect(user_id)
                return False
            except Exception as e:
                logger.error(f"❌ 发送消息给用户 {user_id} 失败 (尝试 {retry_count + 1}/{max_retries + 1}): {e}")

                # 🔧 重试机制
                if retry_count < max_retries:
                    logger.info(f"🔄 重试发送消息给用户 {user_id}")
                    await asyncio.sleep(0.5)  # 等待0.5秒后重试
                    return await self.send_personal_message(message, user_id, retry_count + 1)
                else:
                    logger.error(f"❌ 发送消息给用户 {user_id} 最终失败，断开连接")
                    self.disconnect(user_id)
                    return False
        else:
            logger.debug(f"⚠️ 用户 {user_id} 不在活跃连接中")
            return False
    
    async def broadcast_task_update(self, task_id: str, message: dict):
        """广播任务更新给所有订阅者 - 🔧 增强错误处理和重试机制"""
        logger.info(f"📡 广播任务更新: {task_id}, 消息类型: {message.get('type')}")

        # 🔧 详细日志记录
        logger.debug(f"📋 广播消息内容: {message}")

        success_count = 0
        failure_count = 0

        if task_id in self.task_subscriptions:
            user_ids = list(self.task_subscriptions[task_id])
            logger.info(f"📡 找到 {len(user_ids)} 个订阅者: {user_ids}")

            for user_id in user_ids:
                try:
                    success = await self.send_personal_message(message, user_id)
                    if success:
                        success_count += 1
                        logger.debug(f"✅ 消息已发送给用户: {user_id}")
                    else:
                        failure_count += 1
                        logger.warning(f"⚠️ 消息发送失败: {user_id}")
                except Exception as e:
                    failure_count += 1
                    logger.error(f"❌ 发送消息给用户 {user_id} 异常: {e}")
        else:
            logger.warning(f"⚠️ 任务 {task_id} 没有订阅者，当前订阅任务: {list(self.task_subscriptions.keys())}")

            # 🔧 增强备用广播机制
            if message.get('type') in ['task_completed', 'task_failed']:
                logger.info(f"📡 任务完成/失败消息，广播给所有用户")
                all_user_ids = list(self.active_connections.keys())
                logger.info(f"📡 尝试广播给 {len(all_user_ids)} 个活跃用户")

                for user_id in all_user_ids:
                    try:
                        success = await self.send_personal_message(message, user_id)
                        if success:
                            success_count += 1
                            logger.debug(f"✅ 备用广播消息已发送给用户: {user_id}")
                        else:
                            failure_count += 1
                            logger.warning(f"⚠️ 备用广播消息发送失败: {user_id}")
                    except Exception as e:
                        failure_count += 1
                        logger.error(f"❌ 备用广播发送给用户 {user_id} 异常: {e}")

        # 🔧 广播结果统计
        total_attempts = success_count + failure_count
        if total_attempts > 0:
            success_rate = (success_count / total_attempts) * 100
            logger.info(f"📊 广播结果: 成功 {success_count}/{total_attempts} ({success_rate:.1f}%)")

            if failure_count > 0:
                logger.warning(f"⚠️ 有 {failure_count} 个用户未收到消息")
        else:
            logger.warning(f"⚠️ 没有找到任何用户来广播消息")
    
    def subscribe_task(self, user_id: str, task_id: str):
        """订阅任务进度"""
        if user_id in self.active_connections:
            self.active_connections[user_id]['subscribed_tasks'].add(task_id)
            
            if task_id not in self.task_subscriptions:
                self.task_subscriptions[task_id] = set()
            self.task_subscriptions[task_id].add(user_id)
            
            logger.info(f"用户 {user_id} 订阅任务 {task_id}")
    
    def unsubscribe_task(self, user_id: str, task_id: str):
        """取消订阅任务进度"""
        if user_id in self.active_connections:
            self.active_connections[user_id]['subscribed_tasks'].discard(task_id)
            
            if task_id in self.task_subscriptions:
                self.task_subscriptions[task_id].discard(user_id)
                if not self.task_subscriptions[task_id]:
                    del self.task_subscriptions[task_id]
            
            logger.info(f"用户 {user_id} 取消订阅任务 {task_id}")
    
    def get_connection_count(self) -> int:
        """获取活跃连接数"""
        return len(self.active_connections)
    
    def get_task_subscriber_count(self, task_id: str) -> int:
        """获取任务订阅者数量"""
        return len(self.task_subscriptions.get(task_id, set()))


# 全局连接管理器
manager = ConnectionManager()


async def get_current_user_from_token(token: str = Query(None)):
    """从查询参数中获取用户ID"""
    try:
        # 如果没有token，使用默认用户
        if not token:
            logger.warning("WebSocket连接没有提供token，使用默认用户")
            return "anonymous_user"

        # 这里应该验证token并返回用户ID
        # 为了简化，我们假设token就是用户ID
        if token == 'demo-token':
            return "demo_user"

        # 尝试验证JWT token
        try:
            from backend.core.security import verify_token
            payload = verify_token(token)
            if payload and 'sub' in payload:
                return payload['sub']
        except Exception as jwt_error:
            logger.warning(f"JWT验证失败，使用token作为用户ID: {jwt_error}")

        # 如果JWT验证失败，直接使用token作为用户ID
        return token if token else "anonymous_user"

    except Exception as e:
        logger.error(f"Token验证失败: {e}")
        # 不抛出异常，而是返回匿名用户
        return "anonymous_user"


async def websocket_endpoint(websocket: WebSocket, user_id: str):
    """WebSocket端点 - 🔧 增强错误处理和重连机制"""
    try:
        await manager.connect(websocket, user_id)
        logger.info(f"🔌 WebSocket连接建立: {user_id}")

        # 🔧 发送连接确认消息
        await manager.send_personal_message({
            'type': 'connection_established',
            'user_id': user_id,
            'timestamp': asyncio.get_event_loop().time()
        }, user_id)

        while True:
            try:
                # 接收客户端消息
                data = await websocket.receive_text()
                message = json.loads(data)

                await handle_websocket_message(websocket, user_id, message)

            except json.JSONDecodeError as e:
                logger.warning(f"WebSocket消息JSON解析失败: {user_id}, {e}")
                await manager.send_personal_message({
                    'type': 'error',
                    'message': 'Invalid JSON format'
                }, user_id)
            except asyncio.TimeoutError:
                logger.warning(f"WebSocket消息接收超时: {user_id}")
                # 发送心跳检测
                await manager.send_personal_message({
                    'type': 'ping',
                    'timestamp': asyncio.get_event_loop().time()
                }, user_id)

    except WebSocketDisconnect:
        logger.info(f"🔌 WebSocket正常断开: {user_id}")
        manager.disconnect(user_id)
    except ConnectionClosedError:
        logger.warning(f"🔌 WebSocket连接异常关闭: {user_id}")
        manager.disconnect(user_id)
    except ConnectionClosedOK:
        logger.info(f"🔌 WebSocket连接正常关闭: {user_id}")
        manager.disconnect(user_id)
    except Exception as e:
        logger.error(f"🔌 WebSocket未知错误: {user_id}, {e}")
        manager.disconnect(user_id)
        # 🔧 发送错误信息给客户端（如果连接仍然可用）
        try:
            await websocket.send_text(json.dumps({
                'type': 'error',
                'message': 'Server error occurred'
            }))
        except:
            pass  # 忽略发送错误


async def handle_websocket_message(websocket: WebSocket, user_id: str, message: dict):
    """处理WebSocket消息"""
    message_type = message.get('type')
    
    if message_type == 'subscribe':
        # 订阅任务进度
        task_id = message.get('task_id')
        if task_id:
            manager.subscribe_task(user_id, task_id)
            
            # 立即发送当前进度
            await send_current_progress(user_id, task_id)
            
    elif message_type == 'unsubscribe':
        # 取消订阅任务进度
        task_id = message.get('task_id')
        if task_id:
            manager.unsubscribe_task(user_id, task_id)
            
    elif message_type == 'ping':
        # 心跳检测
        await manager.send_personal_message({
            'type': 'heartbeat',
            'timestamp': asyncio.get_event_loop().time()
        }, user_id)
        
    else:
        logger.warning(f"未知的WebSocket消息类型: {message_type}")


async def send_current_progress(user_id: str, task_id: str):
    """发送当前任务进度 - 统一消息格式"""
    try:
        task_manager = get_task_manager()
        task_status = task_manager.get_task_status(task_id)

        if task_status and task_status.get('state') != 'UNKNOWN':
            # 🔧 修复：统一消息格式为 {type, task_id, data}
            await manager.send_personal_message({
                'type': 'progress_update',
                'task_id': task_id,
                'data': task_status
            }, user_id)
    except Exception as e:
        logger.error(f"发送当前进度失败: {e}")


class ProgressNotifier:
    """进度通知器"""
    
    @staticmethod
    async def notify_progress_update(task_id: str, progress_data: dict):
        """通知进度更新 - 🔧 增强错误处理和重试机制"""
        logger.debug(f"📈 准备发送进度更新: {task_id}")

        try:
            # 🔧 数据验证
            if not isinstance(progress_data, dict):
                logger.error(f"❌ 进度数据格式错误: {task_id}, 数据类型: {type(progress_data)}")
                return False

            # 🔧 修复：统一消息格式为 {type, task_id, data}
            message = {
                'type': 'progress_update',
                'task_id': task_id,
                'data': progress_data,
                'timestamp': asyncio.get_event_loop().time()
            }

            logger.debug(f"📤 发送进度更新消息: {task_id}, 数据: {progress_data}")
            await manager.broadcast_task_update(task_id, message)
            return True

        except Exception as e:
            logger.error(f"❌ 发送进度更新失败: {task_id}, 错误: {e}")
            return False
    
    @staticmethod
    async def notify_task_completed(task_id: str, result: dict):
        """通知任务完成 - 统一消息格式"""
        logger.info(f"🎉 发送任务完成通知: {task_id}")

        # 🔧 新增：确保result数据可序列化
        try:
            import json
            json.dumps(result)  # 测试序列化
        except (TypeError, ValueError) as e:
            logger.warning(f"⚠️ 任务结果不能序列化，使用简化版本: {e}")
            result = {
                'status': 'completed',
                'task_id': task_id,
                'message': '任务已完成',
                'original_error': str(e)
            }

        # 🔧 修复：统一消息格式为 {type, task_id, data}
        message = {
            'type': 'task_completed',
            'task_id': task_id,
            'data': result
        }

        try:
            await manager.broadcast_task_update(task_id, message)
            logger.info(f"✅ 任务完成通知已发送: {task_id}")
            return True
        except Exception as e:
            logger.error(f"❌ 发送任务完成通知失败: {task_id}, {e}")

            # 🔧 重试机制
            try:
                logger.info(f"🔄 重试发送任务完成通知: {task_id}")
                await asyncio.sleep(1)
                await manager.broadcast_task_update(task_id, message)
                logger.info(f"✅ 任务完成通知重试成功: {task_id}")
                return True
            except Exception as retry_error:
                logger.error(f"❌ 任务完成通知重试失败: {task_id}, {retry_error}")
                return False

    @staticmethod
    async def notify_task_failed(task_id: str, error_message: str):
        """通知任务失败 - 统一消息格式"""
        logger.info(f"❌ 发送任务失败通知: {task_id}")

        # 🔧 修复：统一消息格式为 {type, task_id, data}
        message = {
            'type': 'task_failed',
            'task_id': task_id,
            'data': {
                'error': error_message,
                'status': 'failed',
                'task_id': task_id
            }
        }

        try:
            await manager.broadcast_task_update(task_id, message)
            logger.info(f"✅ 任务失败通知已发送: {task_id}")
            return True
        except Exception as e:
            logger.error(f"❌ 发送任务失败通知失败: {task_id}, {e}")

            # 🔧 重试机制
            try:
                logger.info(f"🔄 重试发送任务失败通知: {task_id}")
                await asyncio.sleep(1)
                await manager.broadcast_task_update(task_id, message)
                logger.info(f"✅ 任务失败通知重试成功: {task_id}")
                return True
            except Exception as retry_error:
                logger.error(f"❌ 任务失败通知重试失败: {task_id}, {retry_error}")
                return False
    
    @staticmethod
    async def notify_task_failed(task_id: str, error: str):
        """通知任务失败"""
        message = {
            'type': 'task_failed',
            'payload': {
                'task_id': task_id,
                'error': error
            }
        }
        await manager.broadcast_task_update(task_id, message)


# 全局进度通知器
progress_notifier = ProgressNotifier()


def get_connection_manager() -> ConnectionManager:
    """获取连接管理器"""
    return manager


def get_progress_notifier() -> ProgressNotifier:
    """获取进度通知器"""
    return progress_notifier


async def start_progress_monitor():
    """启动进度监控任务"""
    logger.info("启动WebSocket进度监控...")

    # 记录已完成任务的时间戳，用于延迟清理
    completed_tasks = {}

    while True:
        try:
            # 检查所有活跃的任务订阅
            for task_id, user_ids in list(manager.task_subscriptions.items()):
                if user_ids:  # 如果有用户订阅
                    # 获取任务状态
                    task_manager = get_task_manager()
                    task_status = task_manager.get_task_status(task_id)

                    if task_status and task_status.get('state') != 'UNKNOWN':
                        # 检查任务是否已完成，避免推送过期数据
                        task_state = task_status.get('state')
                        if task_state not in ['SUCCESS', 'FAILURE'] or task_status.get('progress', {}).get('percentage', 0) == 100:
                            # 广播进度更新
                            await progress_notifier.notify_progress_update(task_id, task_status)

                        # [FIX] 改进任务完成检测机制
                        if task_status.get('ready', False):
                            # [FIX] 验证任务确实完成，避免误判
                            if task_status.get('successful', False):
                                # 确保进度为100%才发送完成通知
                                progress_percentage = task_status.get('progress', {}).get('percentage', 0)
                                if progress_percentage >= 100 or task_status.get('state') == 'SUCCESS':
                                    await progress_notifier.notify_task_completed(task_id, task_status.get('result', {}))
                                    logger.info(f"✅ 任务完成通知已发送: {task_id} (进度: {progress_percentage}%)")
                                else:
                                    logger.debug(f"⏳ 任务未完全完成，跳过完成通知: {task_id} (进度: {progress_percentage}%)")
                                    continue
                            elif task_status.get('failed', False):
                                await progress_notifier.notify_task_failed(task_id, task_status.get('traceback', 'Unknown error'))
                                logger.info(f"❌ 任务失败通知已发送: {task_id}")

                            # 记录完成时间，延迟清理订阅
                            if task_id not in completed_tasks:
                                completed_tasks[task_id] = asyncio.get_event_loop().time()
                                logger.info(f"📝 任务标记为完成，将延迟清理订阅: {task_id}")

            # 清理超过30秒的已完成任务订阅
            current_time = asyncio.get_event_loop().time()
            tasks_to_cleanup = []

            for task_id, completion_time in completed_tasks.items():
                if current_time - completion_time > 30:  # 30秒后清理
                    tasks_to_cleanup.append(task_id)

            for task_id in tasks_to_cleanup:
                if task_id in manager.task_subscriptions:
                    user_ids = manager.task_subscriptions[task_id]
                    for user_id in list(user_ids):
                        manager.unsubscribe_task(user_id, task_id)
                    logger.info(f"延迟清理任务订阅: {task_id}")

                # 从完成任务记录中移除
                completed_tasks.pop(task_id, None)

            # 每2秒检查一次
            await asyncio.sleep(2)

        except Exception as e:
            logger.error(f"进度监控错误: {e}")
            await asyncio.sleep(5)  # 出错时等待更长时间
