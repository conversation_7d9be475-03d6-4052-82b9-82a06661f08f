<template>
  <div class="processing-progress">
    <div class="progress-header">
      <div class="header-info">
        <h4>🔄 处理进度</h4>
        <!-- 使用统一的WebSocket状态组件 -->
        <WebSocketStatus :show-details="false" :show-actions="false" />
      </div>
      
      <div class="header-actions">
        <el-button @click="toggleDetails" text size="small">
          {{ showDetails ? '隐藏详情' : '显示详情' }}
        </el-button>
      </div>
    </div>

    <!-- 总体进度 -->
    <div class="overall-progress">
      <div class="progress-info">
        <span class="progress-label">总体进度</span>
        <span class="progress-percentage">{{ Math.round(overallProgress) }}%</span>
      </div>
      
      <el-progress
        :percentage="overallProgress"
        :stroke-width="12"
        :show-text="false"
        :color="progressColor"
        class="progress-bar"
      />
      
      <div class="progress-meta">
        <span v-if="currentTask">任务ID: {{ currentTask }}</span>
        <span v-if="estimatedTime">预计剩余: {{ formatTime(estimatedTime) }}</span>
      </div>
    </div>

    <!-- 当前阶段 -->
    <div v-if="currentStage" class="current-stage">
      <div class="stage-header">
        <div class="stage-icon">{{ getStageIcon(currentStage.stage) }}</div>
        <div class="stage-info">
          <h5>{{ getStageTitle(currentStage.stage) }}</h5>
          <p>{{ currentStage.detail || '正在处理...' }}</p>
        </div>
      </div>
      
      <div class="stage-progress">
        <el-progress
          :percentage="currentStage.percentage || 0"
          :stroke-width="6"
          :show-text="false"
          color="var(--accent-secondary)"
        />
      </div>
    </div>

    <!-- 详细信息 -->
    <div v-if="showDetails" class="progress-details">
      <div class="details-header">
        <h5>📊 详细信息</h5>
      </div>
      
      <!-- 处理阶段列表 -->
      <div class="stage-list">
        <div
          v-for="stage in processStages"
          :key="stage.name"
          class="stage-item"
          :class="{
            'is-completed': stage.status === 'completed',
            'is-current': stage.status === 'current',
            'is-pending': stage.status === 'pending',
            'is-error': stage.status === 'error'
          }"
        >
          <div class="stage-indicator">
            <el-icon v-if="stage.status === 'completed'" color="var(--success-color)">
              <CircleCheckFilled />
            </el-icon>
            <el-icon v-else-if="stage.status === 'current'" color="var(--accent-primary)" class="is-loading">
              <Loading />
            </el-icon>
            <el-icon v-else-if="stage.status === 'error'" color="var(--danger-color)">
              <CircleCloseFilled />
            </el-icon>
            <el-icon v-else color="var(--text-muted)">
              <Clock />
            </el-icon>
          </div>
          
          <div class="stage-content">
            <div class="stage-name">{{ stage.title }}</div>
            <div class="stage-description">{{ stage.description }}</div>
            <div v-if="stage.status === 'current' && stage.progress" class="stage-mini-progress">
              <el-progress
                :percentage="stage.progress"
                :stroke-width="4"
                :show-text="false"
                color="var(--accent-primary)"
              />
            </div>
          </div>
          
          <div class="stage-time">
            <span v-if="stage.startTime">{{ formatTime(stage.duration || 0) }}</span>
          </div>
        </div>
      </div>

      <!-- 性能统计 -->
      <div v-if="performanceStats" class="performance-stats">
        <h6>⚡ 性能统计</h6>
        <div class="stats-grid">
          <div class="stat-item">
            <label>处理速度</label>
            <span>{{ performanceStats.processingSpeed || 'N/A' }}</span>
          </div>
          <div class="stat-item">
            <label>内存使用</label>
            <span>{{ performanceStats.memoryUsage || 'N/A' }}</span>
          </div>
          <div class="stat-item">
            <label>CPU使用率</label>
            <span>{{ performanceStats.cpuUsage || 'N/A' }}</span>
          </div>
          <div class="stat-item">
            <label>已用时间</label>
            <span>{{ formatTime(elapsedTime) }}</span>
          </div>
        </div>
      </div>

      <!-- 多任务监控 -->
      <div v-if="enableMultiTask && activeTasks.size > 0" class="multi-task-monitor">
        <h6>🔄 活跃任务 ({{ activeTasks.size }})</h6>
        <div class="task-list">
          <div
            v-for="[taskId, task] in activeTasks"
            :key="taskId"
            class="task-item"
            :class="{ 'is-current': taskId === currentTask }"
          >
            <div class="task-header">
              <div class="task-id">{{ taskId.substring(0, 8) }}...</div>
              <div class="task-status" :class="`status-${task.status}`">
                {{ task.status }}
              </div>
            </div>
            <div class="task-progress">
              <el-progress
                :percentage="task.progress || 0"
                :stroke-width="4"
                :show-text="false"
                color="var(--accent-primary)"
              />
              <span class="progress-text">{{ task.progress || 0 }}%</span>
            </div>
            <div class="task-stage" v-if="task.currentStage">
              {{ getStageTitle(task.currentStage.stage) }}
            </div>
          </div>
        </div>
      </div>

      <!-- 错误信息 -->
      <div v-if="errorMessages.length > 0" class="error-messages">
        <h6>⚠️ 错误信息</h6>
        <div class="error-list">
          <div
            v-for="(error, index) in errorMessages"
            :key="index"
            class="error-item"
          >
            <div class="error-time">{{ formatTimestamp(error.timestamp) }}</div>
            <div class="error-message">{{ error.message }}</div>
            <div v-if="error.taskId" class="error-task">任务: {{ error.taskId.substring(0, 8) }}...</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="progress-actions">
      <el-button @click="pauseProcessing" :disabled="!canPause" size="small">
        {{ isPaused ? '继续' : '暂停' }}
      </el-button>
      <el-button @click="cancelProcessing" type="danger" size="small">
        取消处理
      </el-button>
      <el-button @click="refreshStatus" :loading="refreshing" size="small">
        刷新状态
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  CircleCheckFilled,
  CircleCloseFilled,
  Loading,
  Clock,
  Refresh,
  Warning
} from '@element-plus/icons-vue'
import { useWebSocketStore } from '@/stores/websocket'
import WebSocketStatus from '@/components/common/WebSocketStatus.vue'

// Props
const props = defineProps({
  currentTask: {
    type: String,
    default: ''
  },
  overallProgress: {
    type: Number,
    default: 0
  },
  taskDetails: {
    type: Object,
    default: () => ({})
  },

  enableMultiTask: {
    type: Boolean,
    default: true
  },
  maxRetries: {
    type: Number,
    default: 3
  }
})

// Emits
const emit = defineEmits([
  'pause-processing',
  'cancel-processing',
  'refresh-status',
  'websocket-connected',
  'websocket-disconnected',
  'websocket-error',
  'websocket-message',
  'task-update',
  'task-complete',
  'task-error'
])

// 响应式数据
const showDetails = ref(false)
const isPaused = ref(false)
const refreshing = ref(false)
const startTime = ref(Date.now())
const elapsedTime = ref(0)
const errorMessages = ref([])

// 使用统一的WebSocket状态管理
const wsStore = useWebSocketStore()

// 多任务管理
const activeTasks = ref(new Map())
const taskHistory = ref([])
const maxHistorySize = ref(50)

// 计算属性
const progressColor = computed(() => {
  if (props.overallProgress < 30) return 'var(--danger-color)'
  if (props.overallProgress < 70) return 'var(--warning-color)'
  return 'var(--success-color)'
})

const currentStage = computed(() => {
  return props.taskDetails.currentStage || null
})

const estimatedTime = computed(() => {
  return props.taskDetails.estimatedTime || 0
})

const performanceStats = computed(() => {
  return props.taskDetails.performance || null
})

const canPause = computed(() => {
  return props.currentTask && props.overallProgress > 0 && props.overallProgress < 100
})

const processStages = computed(() => {
  const stages = [
    {
      name: 'initializing',
      title: '初始化',
      description: '准备处理环境和加载模型',
      status: 'pending'
    },
    {
      name: 'file_validation',
      title: '文件验证',
      description: '检查文件格式和完整性',
      status: 'pending'
    },
    {
      name: 'model_loading',
      title: '模型加载',
      description: '加载AI处理模型',
      status: 'pending'
    },
    {
      name: 'processing',
      title: '音频处理',
      description: '执行音频分析和处理',
      status: 'pending'
    },
    {
      name: 'result_generation',
      title: '结果生成',
      description: '生成处理结果和报告',
      status: 'pending'
    },
    {
      name: 'completed',
      title: '处理完成',
      description: '所有处理任务已完成',
      status: 'pending'
    }
  ]

  // 根据当前进度更新阶段状态
  const currentStageInfo = props.taskDetails.currentStage
  if (currentStageInfo) {
    const currentIndex = stages.findIndex(s => s.name === currentStageInfo.stage)
    
    stages.forEach((stage, index) => {
      if (index < currentIndex) {
        stage.status = 'completed'
      } else if (index === currentIndex) {
        stage.status = 'current'
        stage.progress = currentStageInfo.percentage
      } else {
        stage.status = 'pending'
      }
    })
  }

  return stages
})

// 工具函数
const formatTime = (seconds) => {
  if (!seconds || seconds < 0) return '00:00'
  
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)
  
  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

const formatTimestamp = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString()
}

const getStageIcon = (stage) => {
  const icons = {
    initializing: '🚀',
    file_validation: '📁',
    model_loading: '🤖',
    processing: '⚙️',
    result_generation: '📊',
    completed: '✅'
  }
  return icons[stage] || '⏳'
}

const getStageTitle = (stage) => {
  const titles = {
    initializing: '初始化处理',
    file_validation: '验证文件',
    model_loading: '加载模型',
    processing: '处理音频',
    result_generation: '生成结果',
    completed: '处理完成'
  }
  return titles[stage] || '处理中'
}

// 事件处理
const toggleDetails = () => {
  showDetails.value = !showDetails.value
}

const pauseProcessing = () => {
  isPaused.value = !isPaused.value
  emit('pause-processing', isPaused.value)
  
  ElMessage.info(isPaused.value ? '处理已暂停' : '处理已继续')
}

const cancelProcessing = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要取消当前处理任务吗？已处理的进度将会丢失。',
      '确认取消',
      {
        confirmButtonText: '取消任务',
        cancelButtonText: '继续处理',
        type: 'warning'
      }
    )
    
    emit('cancel-processing')
    
  } catch {
    // 用户取消操作
  }
}

const refreshStatus = async () => {
  refreshing.value = true
  
  try {
    emit('refresh-status')
    await new Promise(resolve => setTimeout(resolve, 1000)) // 模拟刷新延迟
    ElMessage.success('状态已刷新')
  } catch (error) {
    ElMessage.error('刷新状态失败')
  } finally {
    refreshing.value = false
  }
}

// WebSocket消息处理
const handleProgressMessage = (data) => {
  console.log('📈 ProcessingProgress收到进度消息:', data)

  // 提取任务ID（兼容不同的数据结构）
  const taskId = data.task_id || data.taskId

  if (taskId === props.currentTask) {
    // 更新当前任务进度
    const progressData = {
      task_id: taskId,
      progress: data.progress || data.percentage || 0,
      detail: data.detail || data.message || '',
      stage: data.stage || data.status || 'processing'
    }

    console.log('🔄 更新当前任务进度:', progressData)
    emit('task-update', progressData)
  }

  // 更新多任务监控
  if (props.enableMultiTask) {
    const progress = data.progress || data.percentage || 0
    const status = data.stage || data.status || 'processing'
    const detail = data.detail || data.message || ''

    activeTasks.value.set(taskId, {
      progress: progress,
      status: status,
      currentStage: detail ? { stage: status, detail: detail } : null,
      lastUpdate: Date.now()
    })

    console.log('📊 更新多任务监控:', taskId, { progress, status, detail })
  }
}

const handleTaskCompleted = (data) => {
  console.log('✅ ProcessingProgress收到完成消息:', data)

  const taskId = data.task_id || data.taskId

  if (taskId === props.currentTask) {
    emit('task-complete', data)
  }

  // 从活跃任务中移除
  activeTasks.value.delete(taskId)

  // 添加到历史记录
  taskHistory.value.unshift({
    taskId: taskId,
    status: 'completed',
    completedAt: Date.now(),
    result: data.result || data
  })

  // 限制历史记录大小
  if (taskHistory.value.length > maxHistorySize.value) {
    taskHistory.value = taskHistory.value.slice(0, maxHistorySize.value)
  }

  console.log('📝 任务完成记录已添加:', taskId)
}

const handleTaskFailed = (data) => {
  console.log('❌ ProcessingProgress收到失败消息:', data)

  const taskId = data.task_id || data.taskId
  const errorMessage = data.error || data.error_message || '任务处理失败'

  if (taskId === props.currentTask) {
    emit('task-error', data)
  }

  // 从活跃任务中移除
  activeTasks.value.delete(taskId)

  // 添加错误信息
  errorMessages.value.unshift({
    taskId: taskId,
    message: errorMessage,
    timestamp: Date.now()
  })

  // 添加到历史记录
  taskHistory.value.unshift({
    taskId: taskId,
    status: 'error',
    completedAt: Date.now(),
    error: errorMessage
  })

  console.log('📝 任务失败记录已添加:', taskId, errorMessage)
}

// 定时器更新已用时间
let timeInterval = null

// 进度轮询备用机制
let pollingInterval = null

const startProgressPolling = (taskId) => {
  if (pollingInterval) {
    clearInterval(pollingInterval)
  }

  // 确保taskId是字符串类型
  const actualTaskId = typeof taskId === 'string' ? taskId : (taskId?.id || null)

  if (!actualTaskId) {
    console.warn('⚠️ 无效的任务ID，跳过轮询:', taskId)
    return
  }

  console.log('🔄 启动进度轮询:', actualTaskId, '(原始值:', taskId, ')')

  pollingInterval = setInterval(async () => {
    try {
      const response = await fetch(`/api/v1/tasks/${actualTaskId}/progress`)
      if (response.ok) {
        const progressData = await response.json()
        console.log('📊 轮询获取进度:', progressData)

        // 模拟WebSocket消息格式
        handleProgressMessage({
          task_id: actualTaskId,
          progress: progressData.percentage || 0,
          detail: progressData.detail || '',
          stage: progressData.status || 'processing'
        })

        // 检查任务是否完成
        if (progressData.status === 'completed') {
          handleTaskCompleted({
            task_id: actualTaskId,
            result: progressData.result || {}
          })
          clearInterval(pollingInterval)
          pollingInterval = null
        } else if (progressData.status === 'failed') {
          handleTaskFailed({
            task_id: actualTaskId,
            error: progressData.error_message || '任务执行失败'
          })
          clearInterval(pollingInterval)
          pollingInterval = null
        }
      }
    } catch (error) {
      console.error('❌ 进度轮询失败:', error)
    }
  }, 2000) // 每2秒轮询一次
}

const stopProgressPolling = () => {
  if (pollingInterval) {
    clearInterval(pollingInterval)
    pollingInterval = null
    console.log('⏹️ 停止进度轮询')
  }
}

onMounted(() => {
  timeInterval = setInterval(() => {
    elapsedTime.value = (Date.now() - startTime.value) / 1000
  }, 1000)

  // 监听WebSocket进度消息
  wsStore.on('progress', handleProgressMessage)
  wsStore.on('task_completed', handleTaskCompleted)
  wsStore.on('task_failed', handleTaskFailed)

  // 如果有当前任务且WebSocket未连接，启动轮询
  if (props.currentTask && !wsStore.isConnected) {
    console.log('⚠️ WebSocket未连接，启动轮询备用机制')
    startProgressPolling(props.currentTask)
  }
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }

  // 停止轮询
  stopProgressPolling()

  // 移除WebSocket事件监听
  wsStore.off('progress', handleProgressMessage)
  wsStore.off('task_completed', handleTaskCompleted)
  wsStore.off('task_failed', handleTaskFailed)
})

// 监听任务变化，重置开始时间并管理轮询
watch(() => props.currentTask, (newTask, oldTask) => {
  console.log('🔄 任务变化:', oldTask, '->', newTask)

  // 停止旧任务的轮询
  if (oldTask && pollingInterval) {
    stopProgressPolling()
  }

  if (newTask) {
    startTime.value = Date.now()
    elapsedTime.value = 0
    errorMessages.value = []

    // 如果WebSocket未连接，启动新任务的轮询
    if (!wsStore.isConnected) {
      console.log('⚠️ WebSocket未连接，为新任务启动轮询:', newTask)
      startProgressPolling(newTask)
    }
  }
})

// 监听WebSocket连接状态变化
watch(() => wsStore.isConnected, (connected) => {
  console.log('🔌 WebSocket连接状态变化:', connected)

  if (connected) {
    // WebSocket连接成功，停止轮询
    if (pollingInterval) {
      console.log('✅ WebSocket已连接，停止轮询')
      stopProgressPolling()
    }
  } else {
    // WebSocket断开，如果有当前任务则启动轮询
    if (props.currentTask && !pollingInterval) {
      console.log('⚠️ WebSocket断开，启动轮询备用机制')
      startProgressPolling(props.currentTask)
    }
  }
})

// WebSocket连接管理
const connectWebSocket = () => {
  if (!props.enableWebSocket || !props.websocketUrl) return

  try {
    websocket.value = new WebSocket(props.websocketUrl)

    websocket.value.onopen = () => {
      wsConnected.value = true
      wsReconnectAttempts.value = 0
      emit('websocket-connected')

      // 开始心跳检测
      startHeartbeat()

      // 发送队列中的消息
      flushMessageQueue()

      ElMessage.success('WebSocket连接已建立')
    }

    websocket.value.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        handleWebSocketMessage(data)
      } catch (error) {
        console.error('WebSocket消息解析失败:', error)
      }
    }

    websocket.value.onclose = (event) => {
      wsConnected.value = false
      stopHeartbeat()
      emit('websocket-disconnected', event)

      // 自动重连
      if (wsReconnectAttempts.value < props.maxRetries) {
        scheduleReconnect()
      } else {
        ElMessage.error('WebSocket连接失败，已达到最大重试次数')
      }
    }

    websocket.value.onerror = (error) => {
      console.error('WebSocket错误:', error)
      emit('websocket-error', error)
    }

  } catch (error) {
    console.error('WebSocket连接失败:', error)
    ElMessage.error('WebSocket连接失败')
  }
}

const disconnectWebSocket = () => {
  if (websocket.value) {
    websocket.value.close()
    websocket.value = null
  }

  stopHeartbeat()
  clearReconnectTimer()
  wsConnected.value = false
}

const scheduleReconnect = () => {
  clearReconnectTimer()

  const delay = Math.min(1000 * Math.pow(2, wsReconnectAttempts.value), 30000) // 指数退避，最大30秒
  wsReconnectAttempts.value++

  wsReconnectTimer.value = setTimeout(() => {
    console.log(`WebSocket重连尝试 ${wsReconnectAttempts.value}/${props.maxRetries}`)
    connectWebSocket()
  }, delay)
}

const clearReconnectTimer = () => {
  if (wsReconnectTimer.value) {
    clearTimeout(wsReconnectTimer.value)
    wsReconnectTimer.value = null
  }
}

const startHeartbeat = () => {
  stopHeartbeat()

  wsHeartbeatTimer.value = setInterval(() => {
    if (wsConnected.value && websocket.value?.readyState === WebSocket.OPEN) {
      sendWebSocketMessage({ type: 'ping', timestamp: Date.now() })
    }
  }, 30000) // 30秒心跳
}

const stopHeartbeat = () => {
  if (wsHeartbeatTimer.value) {
    clearInterval(wsHeartbeatTimer.value)
    wsHeartbeatTimer.value = null
  }
}

const sendWebSocketMessage = (message) => {
  if (wsConnected.value && websocket.value?.readyState === WebSocket.OPEN) {
    websocket.value.send(JSON.stringify(message))
  } else {
    // 连接断开时，将消息加入队列
    wsMessageQueue.value.push(message)
  }
}

const flushMessageQueue = () => {
  while (wsMessageQueue.value.length > 0) {
    const message = wsMessageQueue.value.shift()
    sendWebSocketMessage(message)
  }
}

const handleWebSocketMessage = (data) => {
  emit('websocket-message', data)

  switch (data.type) {
    case 'task_update':
      handleTaskUpdate(data.payload)
      break
    case 'task_complete':
      handleTaskComplete(data.payload)
      break
    case 'task_error':
      handleTaskError(data.payload)
      break
    case 'progress_update':
      handleProgressUpdate(data.payload)
      break
    case 'pong':
      // 心跳响应，无需处理
      break
    default:
      console.log('未知WebSocket消息类型:', data.type)
  }
}

// 多任务管理
const handleTaskUpdate = (taskData) => {
  activeTasks.value.set(taskData.taskId, {
    ...taskData,
    lastUpdate: Date.now()
  })

  emit('task-update', taskData)
}

const handleTaskComplete = (taskData) => {
  const task = activeTasks.value.get(taskData.taskId)
  if (task) {
    task.status = 'completed'
    task.completedAt = Date.now()

    // 移动到历史记录
    addToHistory(task)
    activeTasks.value.delete(taskData.taskId)
  }

  emit('task-complete', taskData)
}

const handleTaskError = (taskData) => {
  const task = activeTasks.value.get(taskData.taskId)
  if (task) {
    task.status = 'error'
    task.error = taskData.error
    task.errorAt = Date.now()

    // 添加错误消息
    errorMessages.value.push({
      timestamp: Date.now(),
      message: taskData.error,
      taskId: taskData.taskId
    })

    // 移动到历史记录
    addToHistory(task)
    activeTasks.value.delete(taskData.taskId)
  }

  emit('task-error', taskData)
}

const handleProgressUpdate = (progressData) => {
  const task = activeTasks.value.get(progressData.taskId)
  if (task) {
    task.progress = progressData.progress
    task.currentStage = progressData.currentStage
    task.lastUpdate = Date.now()
  }
}

const addToHistory = (task) => {
  taskHistory.value.unshift(task)

  // 限制历史记录大小
  if (taskHistory.value.length > maxHistorySize.value) {
    taskHistory.value = taskHistory.value.slice(0, maxHistorySize.value)
  }
}

// 监听错误信息
watch(() => props.taskDetails.errors, (newErrors) => {
  if (newErrors && Array.isArray(newErrors)) {
    errorMessages.value = newErrors
  }
}, { deep: true })

// 监听WebSocket连接状态
watch(() => props.websocketConnected, (connected) => {
  wsConnected.value = connected
})

// 暴露方法给父组件
defineExpose({
  connectWebSocket,
  disconnectWebSocket,
  sendWebSocketMessage,
  activeTasks: computed(() => activeTasks.value),
  taskHistory: computed(() => taskHistory.value),
  wsConnected: computed(() => wsConnected.value)
})
</script>

<style scoped>
.processing-progress {
  background: var(--card-bg);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  background: var(--surface-bg);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.header-info h4 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
}

.connection-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.overall-progress {
  padding: var(--spacing-lg);
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.progress-label {
  font-weight: 500;
  color: var(--text-primary);
}

.progress-percentage {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--accent-primary);
}

.progress-bar {
  margin-bottom: var(--spacing-md);
}

.progress-meta {
  display: flex;
  justify-content: space-between;
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.current-stage {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
  background: var(--surface-bg);
}

.stage-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.stage-icon {
  font-size: 1.5rem;
}

.stage-info h5 {
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--text-primary);
}

.stage-info p {
  margin: 0;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.progress-details {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
}

.details-header h5 {
  margin: 0 0 var(--spacing-lg) 0;
  color: var(--text-primary);
}

.stage-list {
  margin-bottom: var(--spacing-lg);
}

.stage-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-sm);
}

.stage-item.is-completed {
  background: rgba(40, 167, 69, 0.1);
}

.stage-item.is-current {
  background: rgba(88, 166, 255, 0.1);
}

.stage-item.is-error {
  background: rgba(248, 81, 73, 0.1);
}

.stage-indicator {
  flex-shrink: 0;
}

.stage-content {
  flex: 1;
}

.stage-name {
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.stage-description {
  font-size: 0.85rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.stage-mini-progress {
  width: 100%;
}

.stage-time {
  flex-shrink: 0;
  font-size: 0.85rem;
  color: var(--text-muted);
}

.performance-stats,
.error-messages,
.multi-task-monitor {
  margin-bottom: var(--spacing-lg);
}

.performance-stats h6,
.error-messages h6,
.multi-task-monitor h6 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--text-primary);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: var(--spacing-md);
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm);
  background: var(--surface-bg);
  border-radius: var(--radius-sm);
}

.stat-item label {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.stat-item span {
  font-weight: 500;
  color: var(--text-primary);
}

.error-list {
  max-height: 200px;
  overflow-y: auto;
}

.error-item {
  padding: var(--spacing-sm);
  background: rgba(248, 81, 73, 0.1);
  border-radius: var(--radius-sm);
  margin-bottom: var(--spacing-sm);
}

.error-time {
  font-size: 0.8rem;
  color: var(--text-muted);
  margin-bottom: var(--spacing-xs);
}

.error-message {
  color: var(--danger-color);
  font-size: 0.9rem;
}

.error-task {
  font-size: 0.8rem;
  color: var(--text-muted);
  margin-top: var(--spacing-xs);
}

/* 多任务监控样式 */
.task-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  max-height: 300px;
  overflow-y: auto;
}

.task-item {
  padding: var(--spacing-sm);
  background: var(--surface-bg);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-color);
  transition: all 0.2s ease;
}

.task-item.is-current {
  border-color: var(--accent-primary);
  background: rgba(88, 166, 255, 0.1);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xs);
}

.task-id {
  font-family: var(--font-mono);
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.task-status {
  padding: 2px 6px;
  border-radius: var(--radius-xs);
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
}

.task-status.status-running {
  background: var(--info-color);
  color: white;
}

.task-status.status-completed {
  background: var(--success-color);
  color: white;
}

.task-status.status-error {
  background: var(--danger-color);
  color: white;
}

.task-status.status-paused {
  background: var(--warning-color);
  color: white;
}

.task-progress {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-xs);
}

.task-progress .el-progress {
  flex: 1;
}

.task-progress .progress-text {
  font-size: 0.8rem;
  color: var(--text-secondary);
  min-width: 35px;
}

.task-stage {
  font-size: 0.8rem;
  color: var(--text-muted);
}

.progress-actions {
  display: flex;
  gap: var(--spacing-sm);
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
  background: var(--surface-bg);
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
}
</style>
