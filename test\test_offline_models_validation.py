#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
离线模型配置验证测试
验证我们修改后的离线配置是否正确工作
"""

import os
import sys
import socket
from pathlib import Path
from contextlib import contextmanager

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ.update({
    'HF_HUB_OFFLINE': '1',
    'HF_DATASETS_OFFLINE': '1', 
    'TRANSFORMERS_OFFLINE': '1',
    'HF_HUB_DISABLE_TELEMETRY': '1',
    'TOKENIZERS_PARALLELISM': 'false'
})

class OfflineModelsValidator:
    """离线模型配置验证器"""
    
    def __init__(self):
        self.original_getaddrinfo = socket.getaddrinfo
        
    @contextmanager
    def block_network_access(self):
        """阻止网络访问的上下文管理器"""
        print("🚫 阻止网络访问...")
        
        def blocked_getaddrinfo(host, port, family=0, type=0, proto=0, flags=0):
            # 允许本地连接
            if host in ['localhost', '127.0.0.1', '0.0.0.0']:
                return self.original_getaddrinfo(host, port, family, type, proto, flags)
            
            # 阻止外部连接，特别是ModelScope
            if 'modelscope' in host.lower() or 'huggingface' in host.lower():
                raise socket.gaierror(f"Network access blocked for {host}")
            
            # 阻止其他外部连接
            raise socket.gaierror(f"Network access blocked for {host}")
        
        socket.getaddrinfo = blocked_getaddrinfo
        
        try:
            yield
        finally:
            socket.getaddrinfo = self.original_getaddrinfo
            print("✅ 网络访问已恢复")
    
    def test_model_paths_exist(self) -> bool:
        """测试模型路径是否存在"""
        print("📁 检查模型路径...")
        
        from backend.core.config import settings
        
        model_paths = {
            'SenseVoice': settings.SENSEVOICE_MODEL_PATH,
            'VAD': settings.VAD_MODEL_PATH,
            'CAM++': settings.SPEAKER_MODEL_PATH,
        }
        
        all_exist = True
        for name, path in model_paths.items():
            if Path(path).exists():
                print(f"✅ {name} 模型路径存在: {path}")
            else:
                print(f"❌ {name} 模型路径不存在: {path}")
                all_exist = False
        
        return all_exist
    
    def test_funasr_offline_config(self) -> bool:
        """测试FunASR离线配置"""
        print("\n🎯 测试FunASR离线配置...")

        try:
            with self.block_network_access():
                from backend.utils.audio.optimized_funasr_manager import OptimizedFunASRManager
                from backend.core.config import settings

                # 创建管理器实例
                manager = OptimizedFunASRManager()

                # 检查配置 - 提供必需的参数
                model_path = str(settings.SENSEVOICE_MODEL_PATH)
                device = manager._determine_device()
                config = manager._create_optimized_config(model_path, device)

                # 验证VAD模型配置
                vad_model = config.get('vad_model')
                if vad_model and isinstance(vad_model, str) and Path(vad_model).exists():
                    print(f"✅ VAD模型配置正确: {vad_model}")
                    return True
                else:
                    print(f"❌ VAD模型配置错误: {vad_model}")
                    return False

        except Exception as e:
            print(f"❌ FunASR离线配置测试失败: {e}")
            return False
    
    def test_speaker_recognition_offline(self) -> bool:
        """测试说话人识别离线配置"""
        print("\n👥 测试说话人识别离线配置...")

        try:
            with self.block_network_access():
                from backend.utils.audio.speaker_recognition import SpeakerRecognition

                # 创建说话人识别实例
                recognizer = SpeakerRecognition()

                # 检查模型配置 - 通过campplus_model访问
                model_config = recognizer.campplus_model.model_config
                if model_config and model_config.get("is_local", False):
                    model_path = model_config.get("model_path")
                    if model_path and Path(model_path).exists():
                        print(f"✅ CAM++模型配置正确: {model_path}")
                        return True
                    else:
                        print(f"❌ CAM++模型路径不存在: {model_path}")
                        return False
                else:
                    print("❌ CAM++模型未配置为本地模式")
                    return False

        except Exception as e:
            print(f"❌ 说话人识别离线配置测试失败: {e}")
            return False
    
    def test_task_validation_functions(self) -> bool:
        """测试任务验证函数"""
        print("\n⚙️ 测试任务验证函数...")
        
        try:
            from backend.tasks.audio_processing_tasks import _validate_all_model_paths, _setup_offline_environment
            
            # 测试离线环境设置
            _setup_offline_environment()
            print("✅ 离线环境设置函数正常")
            
            # 测试模型路径验证
            validation_result = _validate_all_model_paths()
            if validation_result:
                print("✅ 所有模型路径验证通过")
                return True
            else:
                print("❌ 模型路径验证失败")
                return False
                
        except Exception as e:
            print(f"❌ 任务验证函数测试失败: {e}")
            return False
    
    def test_complete_offline_workflow(self) -> bool:
        """测试完整的离线工作流"""
        print("\n🔄 测试完整的离线工作流...")
        
        try:
            with self.block_network_access():
                # 导入任务处理模块
                from backend.tasks.audio_processing_tasks import _validate_all_model_paths, _setup_offline_environment
                
                # 设置离线环境
                _setup_offline_environment()
                
                # 验证模型路径
                if not _validate_all_model_paths():
                    print("❌ 模型路径验证失败")
                    return False
                
                # 测试FunASR管理器
                from backend.utils.audio.optimized_funasr_manager import OptimizedFunASRManager
                from backend.core.config import settings
                manager = OptimizedFunASRManager()

                # 验证FunASR配置
                model_path = str(settings.SENSEVOICE_MODEL_PATH)
                device = manager._determine_device()
                config = manager._create_optimized_config(model_path, device)
                if not config.get('vad_model') or not Path(config['vad_model']).exists():
                    print("❌ FunASR VAD模型配置失败")
                    return False

                # 测试说话人识别
                from backend.utils.audio.speaker_recognition import SpeakerRecognition
                recognizer = SpeakerRecognition()

                # 验证说话人识别配置
                model_config = recognizer.campplus_model.model_config
                if not model_config or not model_config.get("is_local", False):
                    print("❌ 说话人识别模型配置失败")
                    return False

                print("✅ 完整的离线工作流测试通过")
                return True
                
        except Exception as e:
            print(f"❌ 完整的离线工作流测试失败: {e}")
            return False
    
    def run_validation(self) -> bool:
        """运行完整的验证"""
        print("🔒 开始离线模型配置验证")
        print("=" * 60)
        
        tests = [
            ("模型路径存在性检查", self.test_model_paths_exist),
            ("FunASR离线配置", self.test_funasr_offline_config),
            ("说话人识别离线配置", self.test_speaker_recognition_offline),
            ("任务验证函数", self.test_task_validation_functions),
            ("完整离线工作流", self.test_complete_offline_workflow)
        ]
        
        results = []
        for test_name, test_func in tests:
            print(f"\n🧪 执行测试: {test_name}")
            try:
                result = test_func()
                results.append((test_name, result))
                if result:
                    print(f"✅ {test_name} 通过")
                else:
                    print(f"❌ {test_name} 失败")
            except Exception as e:
                print(f"❌ {test_name} 异常: {e}")
                results.append((test_name, False))
        
        # 汇总结果
        print("\n📊 验证结果汇总:")
        print("=" * 60)
        
        passed = 0
        total = len(results)
        
        for test_name, result in results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n总计: {passed}/{total} 个测试通过")
        
        if passed == total:
            print("🎉 所有离线模型配置验证测试通过！")
            return True
        else:
            print("⚠️ 部分测试失败，请检查配置")
            return False


def main():
    """主函数"""
    validator = OfflineModelsValidator()
    success = validator.run_validation()
    
    if success:
        print("\n🔒 离线模型配置验证完成，系统可以在完全离线环境下运行！")
        sys.exit(0)
    else:
        print("\n❌ 离线模型配置验证失败，请检查配置！")
        sys.exit(1)


if __name__ == "__main__":
    main()
