#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CAM++模型集成测试脚本
测试说话人识别模块的功能完整性
"""

import os
import sys
import tempfile
import time
import numpy as np
import soundfile as sf
from typing import List, Dict, Optional

# 添加utils目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))

try:
    from utils.speaker_recognition import SpeakerRecognition, CAMPlusModel, create_test_audio
    print("✅ 成功导入说话人识别模块")
except ImportError as e:
    print(f"❌ 导入说话人识别模块失败: {e}")
    sys.exit(1)

class CAMPlusIntegrationTester:
    """CAM++模型集成测试类"""
    
    def __init__(self):
        self.test_results = {}
        self.temp_files = []
    
    def cleanup(self):
        """清理临时文件"""
        for file_path in self.temp_files:
            try:
                if os.path.exists(file_path):
                    os.unlink(file_path)
                    print(f"删除临时文件: {file_path}")
            except Exception as e:
                print(f"删除文件失败 {file_path}: {e}")
    
    def create_test_audio_files(self, num_files: int = 3) -> List[str]:
        """创建多个测试音频文件模拟不同说话人"""
        audio_files = []
        
        # 创建不同频率的音频文件模拟不同说话人
        frequencies = [440, 554, 659]  # C, C#, E 音符
        
        for i in range(num_files):
            try:
                # 创建临时文件
                temp_fd, temp_path = tempfile.mkstemp(suffix=f"_speaker_{i+1}.wav")
                os.close(temp_fd)  # 关闭文件描述符
                
                # 生成音频数据
                frequency = frequencies[i % len(frequencies)]
                duration = 3.0 + i * 0.5  # 不同长度
                sample_rate = 16000
                
                # 创建正弦波音频
                t = np.linspace(0, duration, int(duration * sample_rate), endpoint=False)
                
                # 添加一些变化使音频更真实
                audio = np.sin(2 * np.pi * frequency * t) * 0.5
                audio += np.sin(2 * np.pi * frequency * 2 * t) * 0.1  # 添加泛音
                audio += np.random.normal(0, 0.05, len(audio))  # 添加噪声
                
                # 应用包络使音频更自然
                envelope = np.exp(-t / duration * 2)  # 指数衰减
                audio *= envelope
                
                # 保存音频文件
                sf.write(temp_path, audio, sample_rate)
                audio_files.append(temp_path)
                self.temp_files.append(temp_path)
                
                print(f"✅ 创建测试音频 {i+1}: {temp_path} (频率: {frequency}Hz, 时长: {duration:.1f}s)")
                
            except Exception as e:
                print(f"❌ 创建测试音频 {i+1} 失败: {e}")
        
        return audio_files
    
    def test_campplus_model_loading(self) -> bool:
        """测试CAM++模型加载"""
        print("\n=== 测试CAM++模型加载 ===")
        
        try:
            # 测试使用在线模型
            model = CAMPlusModel()
            load_success = model.load_model()
            
            self.test_results['model_loading'] = {
                'success': load_success,
                'model_config': model.model_config,
                'device': model.device
            }
            
            if load_success:
                print("✅ CAM++模型加载成功")
                print(f"   设备: {model.device}")
                print(f"   配置: {model.model_config}")
                return True
            else:
                print("❌ CAM++模型加载失败")
                return False
                
        except Exception as e:
            print(f"❌ CAM++模型加载测试异常: {e}")
            self.test_results['model_loading'] = {'success': False, 'error': str(e)}
            return False
    
    def test_embedding_extraction(self, audio_files: List[str]) -> bool:
        """测试嵌入向量提取"""
        print("\n=== 测试嵌入向量提取 ===")
        
        try:
            model = CAMPlusModel()
            if not model.load_model():
                print("❌ 模型加载失败，跳过嵌入向量测试")
                return False
            
            embeddings = []
            extraction_times = []
            
            for i, audio_file in enumerate(audio_files):
                print(f"\n处理音频文件 {i+1}: {os.path.basename(audio_file)}")
                
                start_time = time.time()
                embedding = model.extract_embedding(audio_file)
                extraction_time = time.time() - start_time
                
                if embedding is not None:
                    embeddings.append(embedding)
                    extraction_times.append(extraction_time)
                    print(f"✅ 嵌入向量提取成功")
                    print(f"   维度: {len(embedding)}")
                    print(f"   处理时间: {extraction_time:.3f}秒")
                    print(f"   向量范围: [{embedding.min():.4f}, {embedding.max():.4f}]")
                    print(f"   向量范数: {np.linalg.norm(embedding):.4f}")
                else:
                    print(f"❌ 嵌入向量提取失败")
            
            self.test_results['embedding_extraction'] = {
                'success': len(embeddings) > 0,
                'num_successful': len(embeddings),
                'num_total': len(audio_files),
                'avg_extraction_time': np.mean(extraction_times) if extraction_times else 0,
                'embedding_dimensions': len(embeddings[0]) if embeddings else 0
            }
            
            if embeddings:
                print(f"\n✅ 成功提取 {len(embeddings)}/{len(audio_files)} 个嵌入向量")
                print(f"   平均处理时间: {np.mean(extraction_times):.3f}秒")
                return True
            else:
                print("\n❌ 所有嵌入向量提取均失败")
                return False
                
        except Exception as e:
            print(f"❌ 嵌入向量提取测试异常: {e}")
            self.test_results['embedding_extraction'] = {'success': False, 'error': str(e)}
            return False
    
    def test_speaker_clustering(self, audio_files: List[str]) -> bool:
        """测试说话人聚类"""
        print("\n=== 测试说话人聚类 ===")
        
        try:
            speaker_rec = SpeakerRecognition()
            
            # 处理音频片段
            result = speaker_rec.process_audio_segments(audio_files, num_speakers=2)
            
            if result['embeddings']:
                print(f"✅ 说话人聚类成功")
                print(f"   处理音频数量: {len(result['segments'])}")
                print(f"   识别说话人数量: {result['num_speakers']}")
                print(f"   聚类标签: {result['labels']}")
                
                # 评估聚类质量
                if len(result['embeddings']) > 1 and len(np.unique(result['labels'])) > 1:
                    metrics = speaker_rec.evaluate_clustering_quality(
                        result['embeddings'], 
                        result['labels']
                    )
                    
                    if 'error' not in metrics:
                        print(f"   聚类质量评估:")
                        print(f"     轮廓系数: {metrics['silhouette_score']:.3f}")
                        print(f"     类内平均距离: {metrics['avg_intra_cluster_distance']:.3f}")
                        print(f"     类间平均距离: {metrics['avg_inter_cluster_distance']:.3f}")
                
                self.test_results['speaker_clustering'] = {
                    'success': True,
                    'num_speakers': result['num_speakers'],
                    'num_segments': len(result['segments']),
                    'labels': result['labels'].tolist() if len(result['labels']) > 0 else []
                }
                
                return True
            else:
                print("❌ 说话人聚类失败：无法提取嵌入向量")
                self.test_results['speaker_clustering'] = {'success': False, 'error': '无法提取嵌入向量'}
                return False
                
        except Exception as e:
            print(f"❌ 说话人聚类测试异常: {e}")
            self.test_results['speaker_clustering'] = {'success': False, 'error': str(e)}
            return False
    
    def test_performance_metrics(self, audio_files: List[str]) -> bool:
        """测试性能指标"""
        print("\n=== 测试性能指标 ===")
        
        try:
            speaker_rec = SpeakerRecognition()
            
            # 测试处理速度
            start_time = time.time()
            result = speaker_rec.process_audio_segments(audio_files)
            total_time = time.time() - start_time
            
            # 计算音频总时长
            total_audio_duration = 0
            for audio_file in audio_files:
                try:
                    data, sr = sf.read(audio_file)
                    total_audio_duration += len(data) / sr
                except:
                    pass
            
            # 计算实时因子 (RTF)
            rtf = total_time / total_audio_duration if total_audio_duration > 0 else float('inf')
            
            print(f"✅ 性能测试完成")
            print(f"   总处理时间: {total_time:.3f}秒")
            print(f"   总音频时长: {total_audio_duration:.3f}秒")
            print(f"   实时因子 (RTF): {rtf:.3f}")
            print(f"   平均每秒处理音频: {total_audio_duration/total_time:.2f}秒" if total_time > 0 else "")
            
            self.test_results['performance'] = {
                'success': True,
                'total_processing_time': total_time,
                'total_audio_duration': total_audio_duration,
                'rtf': rtf,
                'throughput': total_audio_duration/total_time if total_time > 0 else 0
            }
            
            return True
            
        except Exception as e:
            print(f"❌ 性能测试异常: {e}")
            self.test_results['performance'] = {'success': False, 'error': str(e)}
            return False
    
    def generate_test_report(self) -> str:
        """生成测试报告"""
        report = "\n" + "="*60 + "\n"
        report += "CAM++模型集成测试报告\n"
        report += "="*60 + "\n"
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result.get('success', False))
        
        report += f"测试概览: {passed_tests}/{total_tests} 通过\n\n"
        
        for test_name, result in self.test_results.items():
            status = "✅ 通过" if result.get('success', False) else "❌ 失败"
            report += f"{test_name}: {status}\n"
            
            if not result.get('success', False) and 'error' in result:
                report += f"  错误: {result['error']}\n"
            
            # 添加详细信息
            if test_name == 'embedding_extraction' and result.get('success', False):
                report += f"  成功率: {result['num_successful']}/{result['num_total']}\n"
                report += f"  平均处理时间: {result['avg_extraction_time']:.3f}秒\n"
                report += f"  嵌入向量维度: {result['embedding_dimensions']}\n"
            
            elif test_name == 'speaker_clustering' and result.get('success', False):
                report += f"  识别说话人数: {result['num_speakers']}\n"
                report += f"  处理音频片段数: {result['num_segments']}\n"
            
            elif test_name == 'performance' and result.get('success', False):
                report += f"  实时因子 (RTF): {result['rtf']:.3f}\n"
                report += f"  处理吞吐量: {result['throughput']:.2f}x实时\n"
            
            report += "\n"
        
        # 添加建议
        report += "建议和后续步骤:\n"
        if passed_tests == total_tests:
            report += "✅ 所有测试通过，CAM++模型集成成功\n"
            report += "  可以继续进行下一阶段的开发\n"
        else:
            report += "⚠️ 部分测试失败，需要调试和优化\n"
            if not self.test_results.get('model_loading', {}).get('success', False):
                report += "  - 检查模型依赖和网络连接\n"
            if not self.test_results.get('embedding_extraction', {}).get('success', False):
                report += "  - 验证音频格式和模型输入要求\n"
            if not self.test_results.get('speaker_clustering', {}).get('success', False):
                report += "  - 调整聚类参数和阈值\n"
        
        return report
    
    def run_all_tests(self) -> bool:
        """运行所有测试"""
        print("开始CAM++模型集成测试")
        print("="*60)
        
        try:
            # 1. 创建测试音频文件
            print("创建测试音频文件...")
            audio_files = self.create_test_audio_files(3)
            
            if not audio_files:
                print("❌ 无法创建测试音频文件")
                return False
            
            # 2. 测试模型加载
            model_load_success = self.test_campplus_model_loading()
            
            # 3. 测试嵌入向量提取
            embedding_success = self.test_embedding_extraction(audio_files)
            
            # 4. 测试说话人聚类
            clustering_success = self.test_speaker_clustering(audio_files)
            
            # 5. 测试性能指标
            performance_success = self.test_performance_metrics(audio_files)
            
            # 6. 生成测试报告
            report = self.generate_test_report()
            print(report)
            
            # 保存测试报告
            report_file = "campplus_integration_test_report.txt"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"测试报告已保存到: {report_file}")
            
            # 返回总体测试结果
            all_passed = all(result.get('success', False) for result in self.test_results.values())
            return all_passed
            
        except Exception as e:
            print(f"❌ 测试过程中发生异常: {e}")
            import traceback
            traceback.print_exc()
            return False
        
        finally:
            # 清理临时文件
            self.cleanup()

def main():
    """主函数"""
    print("CAM++模型集成测试工具")
    print("此测试将验证说话人识别功能的完整性")
    print("")
    
    tester = CAMPlusIntegrationTester()
    
    try:
        success = tester.run_all_tests()
        
        if success:
            print("\n🎉 所有测试通过！CAM++模型集成成功")
            return 0
        else:
            print("\n⚠️ 部分测试失败，请查看报告了解详情")
            return 1
            
    except KeyboardInterrupt:
        print("\n用户中断测试")
        return 2
    except Exception as e:
        print(f"\n❌ 测试过程中发生严重错误: {e}")
        import traceback
        traceback.print_exc()
        return 3

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 