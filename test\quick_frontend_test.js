/**
 * 快速前端API测试
 */

const BASE_URL = 'http://localhost:8002'

async function quickTest() {
    console.log('🚀 快速API测试开始...\n')
    
    // 1. 测试健康检查
    console.log('1. 测试健康检查...')
    try {
        const response = await fetch(`${BASE_URL}/health`, {
            signal: AbortSignal.timeout(5000)  // 5秒超时
        })
        const data = await response.json()
        console.log('✅ 健康检查通过:', data.status)
    } catch (error) {
        console.log('❌ 健康检查失败:', error.message)
        return
    }
    
    // 2. 测试音频列表API
    console.log('\n2. 测试音频列表API...')
    try {
        const response = await fetch(`${BASE_URL}/api/v1/audio/`, {
            headers: {
                'Authorization': 'Bearer demo-token'
            },
            signal: AbortSignal.timeout(5000)
        })
        
        if (response.ok) {
            const data = await response.json()
            console.log('✅ 音频列表获取成功, 数量:', data.length)
        } else {
            console.log('❌ 音频列表获取失败, 状态:', response.status)
        }
    } catch (error) {
        console.log('❌ 音频列表请求异常:', error.message)
    }
    
    // 3. 测试任务状态API
    console.log('\n3. 测试任务状态API...')
    try {
        const testTaskId = 'test_task_12345'
        const response = await fetch(`${BASE_URL}/api/v1/speech/task/${testTaskId}`, {
            headers: {
                'Authorization': 'Bearer demo-token'
            },
            signal: AbortSignal.timeout(5000)
        })
        
        console.log('任务状态API响应码:', response.status)
        
        if (response.status === 403) {
            console.log('✅ 权限验证正常')
        } else if (response.status === 404) {
            console.log('✅ 任务不存在响应正常')
        } else if (response.ok) {
            const data = await response.json()
            console.log('✅ 任务状态:', data.status)
        } else {
            console.log('❌ 未知响应状态')
        }
        
    } catch (error) {
        console.log('❌ 任务状态请求异常:', error.message)
    }
    
    // 4. 简单WebSocket测试
    console.log('\n4. 测试WebSocket连接...')
    try {
        const ws = new WebSocket('ws://localhost:8002/ws/progress')
        
        const wsPromise = new Promise((resolve) => {
            const timeout = setTimeout(() => {
                ws.close()
                console.log('❌ WebSocket连接超时')
                resolve(false)
            }, 3000)
            
            ws.onopen = () => {
                clearTimeout(timeout)
                console.log('✅ WebSocket连接成功')
                ws.close()
                resolve(true)
            }
            
            ws.onerror = () => {
                clearTimeout(timeout)
                console.log('❌ WebSocket连接失败')
                resolve(false)
            }
        })
        
        await wsPromise
        
    } catch (error) {
        console.log('❌ WebSocket测试异常:', error.message)
    }
    
    console.log('\n🎉 快速测试完成！')
}

quickTest().catch(console.error) 