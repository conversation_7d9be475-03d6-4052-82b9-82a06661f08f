#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Streamlit UI 端到端测试脚本 - 任务14.3
测试Streamlit应用的完整用户界面流程，包括多页面交互和异常处理
"""

import os
import sys
import importlib.util
from datetime import datetime

def print_header(title):
    """打印标题"""
    print("\n" + "="*60)
    print(f"🖥️ {title}")
    print("="*60)

def print_section(title):
    """打印小节标题"""
    print(f"\n📋 {title}")
    print("-" * 40)

def test_main_app_import():
    """测试主应用导入"""
    print_section("主应用导入测试")
    
    try:
        import Home
        print("✅ Home.py: 导入成功")
        return True
    except Exception as e:
        print(f"❌ Home.py: 导入失败 - {str(e)}")
        return False

def test_pages_structure():
    """测试页面结构"""
    print_section("页面结构测试")
    
    pages_dir = "pages"
    success_count = 0
    total_count = 0
    
    if os.path.exists(pages_dir):
        for file in os.listdir(pages_dir):
            if file.endswith('.py'):
                total_count += 1
                try:
                    # 创建模块规范
                    module_name = file[:-3]  # 移除.py扩展名
                    file_path = os.path.join(pages_dir, file)
                    spec = importlib.util.spec_from_file_location(module_name, file_path)
                    if spec is None:
                        print(f"❌ {file}: 无法创建模块规范")
                        continue
                    
                    # 检查语法（不实际导入）
                    with open(file_path, 'r', encoding='utf-8') as f:
                        code = f.read()
                        compile(code, file_path, 'exec')
                    
                    print(f"✅ {file}: 语法正确")
                    success_count += 1
                except SyntaxError as e:
                    print(f"❌ {file}: 语法错误 - {str(e)}")
                except Exception as e:
                    print(f"⚠️ {file}: 其他问题 - {str(e)}")
    else:
        print("❌ pages/ 目录不存在")
        return False
    
    print(f"\n📊 页面结构测试结果: {success_count}/{total_count} 页面正常")
    return success_count >= total_count * 0.8

def test_ui_components():
    """测试UI组件"""
    print_section("UI组件测试")
    
    components = [
        ('utils.ui_exception_handler', 'UI异常处理器'),
        ('utils.monitoring_components', '监控组件'),
        ('utils.system_health_monitor', '系统健康监控'),
        ('utils.file_recovery_handler', '文件恢复处理器')
    ]
    
    success_count = 0
    
    for component, description in components:
        try:
            __import__(component)
            print(f"✅ {description}: 可用")
            success_count += 1
        except Exception as e:
            print(f"❌ {description}: 不可用 - {str(e)}")
    
    print(f"\n📊 UI组件测试结果: {success_count}/{len(components)} 组件可用")
    return success_count >= len(components) * 0.8

def test_configuration_files():
    """测试配置文件"""
    print_section("配置文件测试")
    
    config_files = [
        'config/ui_error_messages.json',
        'speech_config.ini',
        'requirements.txt'
    ]
    
    success_count = 0
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"✅ {config_file}: 存在")
            success_count += 1
        else:
            print(f"❌ {config_file}: 不存在")
    
    print(f"\n📊 配置文件测试结果: {success_count}/{len(config_files)} 文件存在")
    return success_count >= len(config_files) * 0.8

def test_core_functionality():
    """测试核心功能"""
    print_section("核心功能测试")
    
    tests = []
    
    # 测试异常处理器
    try:
        from utils.ui_exception_handler import UIExceptionHandler
        handler = UIExceptionHandler()
        print("✅ UI异常处理器: 创建成功")
        tests.append(True)
    except Exception as e:
        print(f"❌ UI异常处理器: 创建失败 - {str(e)}")
        tests.append(False)
    
    # 测试监控组件
    try:
        from utils.monitoring_components import ProcessingMonitor
        # Mock模式下创建监控器
        import streamlit as st
        if not hasattr(st, 'session_state'):
            # 在非Streamlit环境中模拟
            class MockSessionState:
                def __init__(self):
                    self.data = {}
                def __getitem__(self, key):
                    return self.data.get(key)
                def __setitem__(self, key, value):
                    self.data[key] = value
                def get(self, key, default=None):
                    return self.data.get(key, default)
            st.session_state = MockSessionState()
        
        monitor = ProcessingMonitor()
        print("✅ 处理监控器: 创建成功")
        tests.append(True)
    except Exception as e:
        print(f"❌ 处理监控器: 创建失败 - {str(e)}")
        tests.append(False)
    
    # 测试系统健康监控
    try:
        from utils.system_health_monitor import SystemHealthMonitor
        health_monitor = SystemHealthMonitor()
        print("✅ 系统健康监控器: 创建成功")
        tests.append(True)
    except Exception as e:
        print(f"❌ 系统健康监控器: 创建失败 - {str(e)}")
        tests.append(False)
    
    success_count = sum(tests)
    print(f"\n📊 核心功能测试结果: {success_count}/{len(tests)} 功能正常")
    return success_count >= len(tests) * 0.8

def main():
    """主函数"""
    print("🖥️ Streamlit UI端到端测试")
    print(f"⏰ 开始时间: {datetime.now()}")
    
    # 执行所有测试
    results = []
    
    try:
        results.append(("主应用导入", test_main_app_import()))
        results.append(("页面结构", test_pages_structure()))
        results.append(("UI组件", test_ui_components()))
        results.append(("配置文件", test_configuration_files()))
        results.append(("核心功能", test_core_functionality()))
        
        # 总结结果
        print_header("测试结果总结")
        
        success_count = sum(1 for _, result in results if result)
        total_count = len(results)
        
        for test_name, result in results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
        
        print(f"\n📊 总体结果: {success_count}/{total_count} 项测试通过")
        
        if success_count >= total_count * 0.8:
            print("🎉 Streamlit UI端到端测试通过！应用已准备好进行用户交互")
            return True
        else:
            print("⚠️ UI测试未完全通过，建议修复问题后重新测试")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    finally:
        print(f"\n⏰ 结束时间: {datetime.now()}")

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 