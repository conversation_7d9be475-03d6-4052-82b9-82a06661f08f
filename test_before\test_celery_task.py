#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Celery任务调用
"""

from backend.tasks.document_tasks import process_document_task

def test_celery_task():
    """测试Celery任务调用"""
    print("=" * 60)
    print("Celery任务调用测试")
    print("=" * 60)
    
    # 测试不同数量的参数
    test_cases = [
        {
            "name": "6个参数（旧格式）",
            "args": [
                "doc_proc_test123",  # task_id
                "user123",           # user_id  
                "test.txt",          # filename
                "file:test123",      # file_key
                False,               # use_ocr
                None                 # ocr_config
            ]
        },
        {
            "name": "7个参数（新格式）",
            "args": [
                "doc_proc_test123",  # task_id
                "user123",           # user_id  
                "test.txt",          # filename
                "file:test123",      # file_key
                123,                 # document_id
                False,               # use_ocr
                None                 # ocr_config
            ]
        }
    ]
    
    for test_case in test_cases:
        print(f"\n测试: {test_case['name']}")
        print(f"参数: {test_case['args']}")
        print(f"参数数量: {len(test_case['args'])}")
        
        try:
            # 尝试创建任务（不执行）
            task = process_document_task.s(*test_case['args'])
            print("✅ 任务创建成功")
            print(f"任务参数: {task.args}")
        except Exception as e:
            print(f"❌ 任务创建失败: {e}")
    
    # 检查任务的实际签名
    print(f"\n任务函数: {process_document_task}")
    print(f"任务类型: {type(process_document_task)}")
    
    # 检查原始函数
    if hasattr(process_document_task, '__wrapped__'):
        original_func = process_document_task.__wrapped__
        print(f"原始函数: {original_func}")
        
        import inspect
        sig = inspect.signature(original_func)
        print(f"原始函数签名: {sig}")

if __name__ == "__main__":
    test_celery_task()
