"""
OCR处理异步任务
"""

import asyncio
from typing import Dict, Any, Optional
from celery import current_app
from backend.core.task_queue import celery_app
from backend.tasks.base_task import BaseTask, ProgressCallback
from backend.utils.file_processing import perform_ocr
from PIL import Image
import io
from loguru import logger


@celery_app.task(bind=True, base=BaseTask)
def process_ocr_task(
    self,
    task_id: str,
    user_id: str,
    filename: str,
    image_key: str,
    ocr_config: Dict[str, Any]
):
    """异步OCR处理任务"""
    
    progress_callback = ProgressCallback(task_id, self)
    
    try:
        # 1. 初始化
        progress_callback(5, "开始OCR处理...", "initializing")
        
        # 2. 从Redis获取图像数据
        progress_callback(10, "读取图像数据...", "loading")
        image_data = self.redis_client.get(image_key)
        if not image_data:
            raise Exception("图像数据不存在或已过期")
        
        # 转换为bytes
        if isinstance(image_data, str):
            image_data = image_data.encode('latin-1')
        
        # 3. 验证图像
        progress_callback(15, "验证图像格式...", "validating")
        try:
            image = Image.open(io.BytesIO(image_data))
            image.verify()  # 验证图像完整性
            
            # 重新打开图像（verify后需要重新打开）
            image = Image.open(io.BytesIO(image_data))
            
        except Exception as e:
            raise Exception(f"无效的图像文件: {e}")
        
        # 4. 预处理图像
        progress_callback(25, "预处理图像...", "preprocessing")
        
        # 转换为RGB模式（如果需要）
        if image.mode != 'RGB':
            image = image.convert('RGB')
        
        # 检查图像尺寸
        width, height = image.size
        max_dimension = 4000  # 最大尺寸限制
        
        if width > max_dimension or height > max_dimension:
            # 等比例缩放
            ratio = min(max_dimension / width, max_dimension / height)
            new_width = int(width * ratio)
            new_height = int(height * ratio)
            image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            logger.info(f"图像已缩放: {width}x{height} -> {new_width}x{new_height}")
        
        # 5. 执行OCR
        progress_callback(40, "执行OCR识别...", "ocr_processing")
        
        # 创建进度回调函数
        def ocr_progress(percentage: float, detail: str = ""):
            # 将OCR进度映射到40%-90%的范围
            mapped_percentage = 40 + (percentage / 100) * 50
            progress_callback(mapped_percentage, detail, "ocr_processing")
        
        # 执行OCR
        try:
            ocr_result = perform_ocr(image, ocr_config, progress_callback=ocr_progress)
            
            if not ocr_result or not ocr_result.strip():
                raise Exception("OCR未能识别出任何文本")
            
        except Exception as e:
            raise Exception(f"OCR处理失败: {e}")
        
        # 6. 后处理文本
        progress_callback(90, "后处理文本...", "postprocessing")
        
        # 清理文本
        cleaned_text = ocr_result.strip()
        
        # 统计信息
        char_count = len(cleaned_text)
        line_count = len(cleaned_text.split('\n'))
        word_count = len(cleaned_text.split())
        
        # 7. 完成
        progress_callback(100, "OCR处理完成！", "completed")
        
        return {
            "success": True,
            "filename": filename,
            "text": cleaned_text,
            "char_count": char_count,
            "line_count": line_count,
            "word_count": word_count,
            "image_size": f"{image.size[0]}x{image.size[1]}",
            "ocr_config": ocr_config,
            "message": "OCR处理完成"
        }
        
    except Exception as e:
        error_message = str(e)
        logger.error(f"OCR任务失败: {task_id}, {error_message}")
        progress_callback(0, f"OCR处理失败: {error_message}", "failed")
        raise e


@celery_app.task(bind=True, base=BaseTask)
def batch_ocr_task(
    self,
    task_id: str,
    user_id: str,
    image_list: list,
    ocr_config: Dict[str, Any]
):
    """批量OCR处理任务"""
    
    progress_callback = ProgressCallback(task_id, self)
    
    try:
        progress_callback(5, "开始批量OCR处理...", "initializing")
        
        total_images = len(image_list)
        processed_images = []
        failed_images = []
        
        for i, image_info in enumerate(image_list):
            try:
                # 计算当前图像的进度范围
                image_start = 10 + (i * 80 // total_images)
                image_end = 10 + ((i + 1) * 80 // total_images)
                
                progress_callback(
                    image_start,
                    f"处理图像 {i+1}/{total_images}: {image_info['filename']}",
                    "processing"
                )
                
                # 处理单个图像
                result = process_ocr_task.apply(
                    args=[
                        f"{task_id}_image_{i}",
                        user_id,
                        image_info['filename'],
                        image_info['image_key'],
                        ocr_config
                    ]
                ).get()
                
                processed_images.append({
                    "filename": image_info['filename'],
                    "text": result['text'],
                    "char_count": result['char_count'],
                    "success": True
                })
                
                progress_callback(image_end, f"完成图像: {image_info['filename']}", "processing")
                
            except Exception as e:
                failed_images.append({
                    "filename": image_info['filename'],
                    "error": str(e),
                    "success": False
                })
                
                logger.error(f"批量OCR中图像失败: {image_info['filename']}, {e}")
        
        # 完成
        progress_callback(100, "批量OCR处理完成！", "completed")
        
        return {
            "success": True,
            "total_images": total_images,
            "processed_count": len(processed_images),
            "failed_count": len(failed_images),
            "processed_images": processed_images,
            "failed_images": failed_images,
            "total_text_length": sum(img['char_count'] for img in processed_images),
            "message": f"批量OCR完成，成功 {len(processed_images)} 个，失败 {len(failed_images)} 个"
        }
        
    except Exception as e:
        error_message = str(e)
        logger.error(f"批量OCR任务失败: {task_id}, {error_message}")
        progress_callback(0, f"批量OCR失败: {error_message}", "failed")
        raise e


@celery_app.task(bind=True, base=BaseTask)
def pdf_ocr_task(
    self,
    task_id: str,
    user_id: str,
    filename: str,
    pdf_key: str,
    ocr_config: Dict[str, Any]
):
    """PDF OCR处理任务"""
    
    progress_callback = ProgressCallback(task_id, self)
    
    try:
        # 1. 初始化
        progress_callback(5, "开始PDF OCR处理...", "initializing")
        
        # 2. 从Redis获取PDF数据
        progress_callback(10, "读取PDF数据...", "loading")
        pdf_data = self.redis_client.get(pdf_key)
        if not pdf_data:
            raise Exception("PDF数据不存在或已过期")
        
        # 转换为bytes
        if isinstance(pdf_data, str):
            pdf_data = pdf_data.encode('latin-1')
        
        # 3. 处理PDF
        progress_callback(15, "解析PDF文档...", "parsing")
        
        import fitz  # PyMuPDF
        
        try:
            doc = fitz.open(stream=pdf_data, filetype="pdf")
            total_pages = len(doc)
            
            if total_pages == 0:
                raise Exception("PDF文档为空")
            
        except Exception as e:
            raise Exception(f"无法打开PDF文件: {e}")
        
        # 4. 逐页OCR处理
        all_text = []
        
        try:
            for page_num in range(total_pages):
                # 计算页面进度
                page_progress = 20 + (page_num / total_pages) * 70
                progress_callback(
                    page_progress,
                    f"OCR处理第 {page_num + 1}/{total_pages} 页...",
                    "ocr_processing"
                )
                
                page = doc.load_page(page_num)
                
                # 尝试直接提取文本
                text = page.get_text()
                if len(text.strip()) > 100:
                    all_text.append(text)
                    continue
                
                # 需要OCR处理
                try:
                    # 获取页面图像
                    zoom_factor = ocr_config.get("dpi", 300) / 72
                    matrix = fitz.Matrix(zoom_factor, zoom_factor)
                    pix = page.get_pixmap(matrix=matrix, alpha=False)
                    
                    # 转换为PIL图像
                    img_data = pix.samples
                    img = Image.frombytes("RGB", [pix.width, pix.height], img_data)
                    
                    # OCR处理
                    ocr_text = perform_ocr(img, ocr_config)
                    
                    if ocr_text.strip():
                        all_text.append(ocr_text)
                    else:
                        logger.warning(f"页面{page_num+1} OCR无法提取文本内容")
                        
                except Exception as e:
                    logger.warning(f"页面{page_num+1} OCR处理出错: {str(e)}")
                    continue
        
        finally:
            doc.close()
        
        # 5. 合并文本
        progress_callback(95, "合并处理结果...", "finalizing")
        
        final_text = "\n\n".join(all_text)
        
        if not final_text.strip():
            raise Exception("PDF OCR未能提取到任何文本")
        
        # 统计信息
        char_count = len(final_text)
        line_count = len(final_text.split('\n'))
        word_count = len(final_text.split())
        
        # 6. 完成
        progress_callback(100, "PDF OCR处理完成！", "completed")
        
        return {
            "success": True,
            "filename": filename,
            "text": final_text,
            "char_count": char_count,
            "line_count": line_count,
            "word_count": word_count,
            "total_pages": total_pages,
            "ocr_config": ocr_config,
            "message": f"PDF OCR处理完成，共处理 {total_pages} 页"
        }
        
    except Exception as e:
        error_message = str(e)
        logger.error(f"PDF OCR任务失败: {task_id}, {error_message}")
        progress_callback(0, f"PDF OCR处理失败: {error_message}", "failed")
        raise e
