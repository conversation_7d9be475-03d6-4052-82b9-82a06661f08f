"""
增强说话人识别系统
实现多算法聚类、后处理优化和时间连续性平滑
解决说话人识别准确性问题
"""

import os
import tempfile
import shutil
import logging
import traceback
from typing import List, Tuple, Dict, Optional
import numpy as np
import librosa
import soundfile as sf
from sklearn.cluster import AgglomerativeClustering, DBSCAN
from sklearn.metrics.pairwise import cosine_similarity
from sklearn.metrics import silhouette_score

try:
    import torch
    HAS_TORCH = True
except ImportError:
    HAS_TORCH = False

logger = logging.getLogger(__name__)


class EnhancedSpeakerRecognition:
    """增强的说话人识别系统"""
    
    def __init__(self, config: Dict):
        """
        初始化增强说话人识别系统
        
        Args:
            config: 配置字典，包含以下参数：
                - expected_speakers: 期望的说话人数量
                - similarity_threshold: 相似度阈值
                - clustering_method: 聚类方法
                - min_segment_duration: 最小片段时长
        """
        self.expected_speakers = config.get('expected_speakers', 2)
        self.similarity_threshold = config.get('similarity_threshold', 0.15)
        self.clustering_method = config.get('clustering_method', 'auto')
        self.min_segment_duration = config.get('min_segment_duration', 0.5)
        
        logger.info(f"增强说话人识别初始化: 期望{self.expected_speakers}个说话人, "
                   f"相似度阈值: {self.similarity_threshold}, 聚类方法: {self.clustering_method}")
    
    def recognize_speakers(self, audio_path: str, segments: List[Tuple[float, float]], 
                          speaker_model) -> Dict:
        """
        执行说话人识别
        
        Args:
            audio_path: 音频文件路径
            segments: 分段列表
            speaker_model: 说话人识别模型
            
        Returns:
            说话人识别结果字典
        """
        logger.info(f"开始说话人识别，处理{len(segments)}个片段")
        
        try:
            # 第一步：提取所有片段的说话人特征
            embeddings, valid_segments = self._extract_embeddings(
                audio_path, segments, speaker_model
            )
            
            if len(embeddings) < 2:
                logger.warning(f"嵌入向量不足：{len(embeddings)}个，无法进行说话人聚类")
                return self._create_single_speaker_result(segments)
            
            logger.info(f"成功提取{len(embeddings)}个说话人嵌入向量")
            
            # 第二步：智能聚类
            speaker_labels = self._intelligent_clustering(embeddings)
            
            # 第三步：后处理优化
            optimized_labels = self._post_process_labels(
                speaker_labels, valid_segments, embeddings
            )
            
            # 第四步：构建结果
            result = self._build_speaker_result(valid_segments, optimized_labels)
            
            # 记录最终结果统计
            unique_speakers = len(set(optimized_labels))
            logger.info(f"说话人识别完成：识别出{unique_speakers}个说话人")
            
            return result
            
        except Exception as e:
            logger.error(f"说话人识别失败: {e}")
            return self._create_single_speaker_result(segments)
    
    def _extract_embeddings(self, audio_path: str, segments: List[Tuple[float, float]], 
                           speaker_model) -> Tuple[List[np.ndarray], List[Dict]]:
        """提取说话人嵌入向量"""
        embeddings = []
        valid_segments = []
        
        temp_dir = tempfile.mkdtemp()
        try:
            logger.debug(f"创建临时目录: {temp_dir}")
            
            for i, (start, end) in enumerate(segments):
                # 检查片段时长
                duration = end - start
                if duration < self.min_segment_duration:
                    logger.debug(f"跳过过短片段{i}: {duration:.2f}s < {self.min_segment_duration}s")
                    continue
                
                # 提取音频片段
                segment_path = os.path.join(temp_dir, f"segment_{i}.wav")
                success = self._extract_audio_segment(audio_path, start, end, segment_path)
                
                if not success:
                    logger.warning(f"音频片段提取失败: {i}")
                    continue
                
                # 提取嵌入向量
                embedding = self._extract_single_embedding(segment_path, speaker_model)
                if embedding is not None:
                    embeddings.append(embedding)
                    valid_segments.append({
                        'start_time': start,
                        'end_time': end,
                        'duration': duration,
                        'segment_index': i,
                        'original_index': i
                    })
                    logger.debug(f"成功提取片段{i}的嵌入向量")
                else:
                    logger.warning(f"嵌入向量提取失败: 片段{i}")
                    
        finally:
            # 清理临时文件
            shutil.rmtree(temp_dir, ignore_errors=True)
        
        logger.info(f"嵌入向量提取完成: {len(embeddings)}/{len(segments)}个有效片段")
        return embeddings, valid_segments
    
    def _extract_audio_segment(self, audio_path: str, start: float, end: float, output_path: str) -> bool:
        """提取音频片段"""
        try:
            # 使用librosa加载音频
            y, sr = librosa.load(audio_path, sr=None)
            
            # 计算样本索引
            start_sample = int(start * sr)
            end_sample = int(end * sr)
            
            # 提取片段
            segment = y[start_sample:end_sample]
            
            # 保存片段
            sf.write(output_path, segment, sr)
            return True
            
        except Exception as e:
            logger.error(f"音频片段提取失败: {e}")
            return False
    
    def _extract_single_embedding(self, audio_path: str, speaker_model) -> Optional[np.ndarray]:
        """提取单个音频文件的说话人嵌入向量"""
        try:
            # 🔧 修复API接口：使用正确的方法名
            # SpeakerRecognition类使用extract_speaker_embedding方法，不是generate方法
            embedding_array = speaker_model.extract_speaker_embedding(audio_path)

            if embedding_array is None:
                logger.warning(f"嵌入向量提取失败 - 无有效结果: {os.path.basename(audio_path)}")
                return None

            # 转换为numpy数组（如果需要）
            if HAS_TORCH and hasattr(embedding_array, 'detach'):
                # 如果是torch.Tensor
                embedding_array = embedding_array.detach().cpu().numpy()
            elif not isinstance(embedding_array, np.ndarray):
                embedding_array = np.array(embedding_array)

            # 确保是一维数组
            if embedding_array.ndim > 1:
                embedding_array = embedding_array.flatten()

            # 🔧 添加详细的嵌入向量调试信息
            logger.debug(f"嵌入向量提取成功 - 文件: {os.path.basename(audio_path)}")
            logger.debug(f"  - 维度: {embedding_array.shape}")
            logger.debug(f"  - 数据类型: {embedding_array.dtype}")
            logger.debug(f"  - 数值范围: [{np.min(embedding_array):.6f}, {np.max(embedding_array):.6f}]")
            logger.debug(f"  - 均值: {np.mean(embedding_array):.6f}")
            logger.debug(f"  - 标准差: {np.std(embedding_array):.6f}")
            logger.debug(f"  - L2范数: {np.linalg.norm(embedding_array):.6f}")

            # 检查异常值
            if np.any(np.isnan(embedding_array)):
                logger.warning(f"嵌入向量包含NaN值: {os.path.basename(audio_path)}")
                return None
            if np.any(np.isinf(embedding_array)):
                logger.warning(f"嵌入向量包含无穷值: {os.path.basename(audio_path)}")
                return None
            if np.allclose(embedding_array, 0):
                logger.warning(f"嵌入向量全为零: {os.path.basename(audio_path)}")
                return None

            return embedding_array

        except Exception as e:
            logger.error(f"嵌入向量提取失败: {e}")
            import traceback
            logger.debug(f"详细错误信息: {traceback.format_exc()}")
            return None
    
    def _intelligent_clustering(self, embeddings: List[np.ndarray]) -> List[int]:
        """智能聚类算法 - 优化版本"""

        # 转换为numpy数组
        embedding_matrix = np.array(embeddings)
        logger.debug(f"嵌入向量矩阵形状: {embedding_matrix.shape}")

        # 计算相似度矩阵
        similarity_matrix = cosine_similarity(embedding_matrix)
        distance_matrix = 1 - similarity_matrix

        # 记录相似度统计
        upper_triangle = similarity_matrix[np.triu_indices_from(similarity_matrix, k=1)]
        avg_sim = np.mean(upper_triangle)
        min_sim = np.min(upper_triangle)
        max_sim = np.max(upper_triangle)
        std_sim = np.std(upper_triangle)

        logger.info(f"相似度统计 - 平均: {avg_sim:.4f}, 最小: {min_sim:.4f}, 最大: {max_sim:.4f}, 标准差: {std_sim:.4f}")

        # 🔧 添加详细的相似度矩阵分析
        logger.debug("=== 详细相似度矩阵分析 ===")
        logger.debug(f"相似度矩阵形状: {similarity_matrix.shape}")

        # 打印相似度矩阵（如果不太大）
        if len(embeddings) <= 20:
            logger.debug("相似度矩阵:")
            for i in range(len(embeddings)):
                row_str = " ".join([f"{similarity_matrix[i][j]:.4f}" for j in range(len(embeddings))])
                logger.debug(f"  行{i}: {row_str}")

        # 分析每个嵌入向量的特征
        logger.debug("=== 嵌入向量特征分析 ===")
        for i, embedding in enumerate(embeddings):
            logger.debug(f"向量{i}: 均值={np.mean(embedding):.6f}, 标准差={np.std(embedding):.6f}, "
                        f"L2范数={np.linalg.norm(embedding):.6f}")

        # 分析向量间的差异
        logger.debug("=== 向量差异分析 ===")
        for i in range(len(embeddings)):
            for j in range(i+1, len(embeddings)):
                diff = embeddings[i] - embeddings[j]
                l2_diff = np.linalg.norm(diff)
                cosine_sim = similarity_matrix[i][j]
                logger.debug(f"向量{i}与向量{j}: L2距离={l2_diff:.6f}, 余弦相似度={cosine_sim:.6f}")

        # 检查相似度分布，如果过于相似则使用强制策略
        if std_sim < 0.05 and avg_sim > 0.85:
            logger.warning(f"所有嵌入向量过于相似（标准差: {std_sim:.4f}, 平均相似度: {avg_sim:.4f}），使用强制分割策略")
            return self._force_split_clustering(embeddings)

        # 尝试多种聚类方法
        clustering_results = []

        # 方法1：强制层次聚类（指定聚类数，更严格的参数）
        if self.expected_speakers > 0:
            try:
                # 使用更严格的linkage方法
                for linkage_method in ['ward', 'complete', 'average']:
                    try:
                        if linkage_method == 'ward':
                            # Ward需要欧几里得距离
                            clustering = AgglomerativeClustering(
                                n_clusters=self.expected_speakers,
                                linkage='ward'
                            )
                        else:
                            clustering = AgglomerativeClustering(
                                n_clusters=self.expected_speakers,
                                metric='cosine',
                                linkage=linkage_method
                            )

                        labels = clustering.fit_predict(embedding_matrix)
                        score = self._evaluate_clustering(embedding_matrix, labels)
                        clustering_results.append((f'agglomerative_{linkage_method}', labels, score))
                        logger.debug(f"层次聚类（{linkage_method}）: {len(np.unique(labels))}个聚类, 得分: {score:.4f}")

                    except Exception as e:
                        logger.debug(f"层次聚类（{linkage_method}）失败: {e}")

            except Exception as e:
                logger.warning(f"层次聚类失败: {e}")

        # 方法2：自适应阈值聚类
        try:
            # 使用更严格的阈值
            strict_threshold = min(self.similarity_threshold * 0.5, 0.1)
            clustering = AgglomerativeClustering(
                n_clusters=None,
                distance_threshold=strict_threshold,
                metric='cosine',
                linkage='complete'  # 使用complete linkage更严格
            )
            labels2 = clustering.fit_predict(embedding_matrix)
            score2 = self._evaluate_clustering(embedding_matrix, labels2)
            clustering_results.append(('agglomerative_strict', labels2, score2))
            logger.debug(f"严格阈值聚类: {len(np.unique(labels2))}个聚类, 得分: {score2:.4f}")
        except Exception as e:
            logger.warning(f"严格阈值聚类失败: {e}")

        # 方法3：多尺度DBSCAN
        for eps_factor in [0.5, 0.3, 0.1]:
            try:
                eps = self.similarity_threshold * eps_factor
                clustering = DBSCAN(
                    eps=eps,
                    min_samples=max(1, len(embeddings) // (self.expected_speakers * 2)),
                    metric='precomputed'
                )
                labels3 = clustering.fit_predict(distance_matrix)
                score3 = self._evaluate_clustering(embedding_matrix, labels3)
                clustering_results.append((f'dbscan_{eps_factor}', labels3, score3))
                unique_clusters = len(np.unique(labels3[labels3 >= 0]))
                logger.debug(f"DBSCAN(eps={eps:.3f}): {unique_clusters}个聚类, 得分: {score3:.4f}")
            except Exception as e:
                logger.debug(f"DBSCAN(eps_factor={eps_factor})失败: {e}")

        # 选择最佳聚类结果
        if clustering_results:
            # 🔧 添加详细的聚类结果分析
            logger.debug("=== 所有聚类方法结果分析 ===")
            for method, labels, score in clustering_results:
                unique_speakers = len(np.unique(labels[labels >= 0]))
                logger.debug(f"{method}: {unique_speakers}个聚类, 得分: {score:.4f}")
                logger.debug(f"  标签分布: {np.bincount(labels[labels >= 0])}")
                logger.debug(f"  标签序列: {labels}")

            # 优先选择聚类数量接近预期的结果
            best_result = self._select_best_clustering(clustering_results)
            best_method, best_labels, best_score = best_result
            unique_speakers = len(np.unique(best_labels[best_labels >= 0]))
            logger.info(f"选择最佳聚类方法: {best_method}, 识别出 {unique_speakers} 个说话人, 得分: {best_score:.4f}")

            # 🔧 详细分析最佳聚类结果
            logger.debug("=== 最佳聚类结果详细分析 ===")
            logger.debug(f"最佳标签序列: {best_labels}")
            for speaker_id in np.unique(best_labels[best_labels >= 0]):
                speaker_segments = np.where(best_labels == speaker_id)[0]
                logger.debug(f"说话人{speaker_id}: 分配到{len(speaker_segments)}个片段 - {speaker_segments}")

            # 如果仍然只有一个聚类且预期多个说话人，使用强制分割
            if unique_speakers == 1 and self.expected_speakers > 1:
                logger.warning("最佳聚类仍只识别出1个说话人，使用强制分割策略")
                logger.debug(f"期望说话人数: {self.expected_speakers}, 实际识别: {unique_speakers}")
                return self._force_split_clustering(embeddings)

            return best_labels.tolist()
        else:
            logger.warning("所有聚类方法都失败，使用强制分割策略")
            return self._force_split_clustering(embeddings)
    
    def _select_best_clustering(self, clustering_results: List[Tuple[str, np.ndarray, float]]) -> Tuple[str, np.ndarray, float]:
        """选择最佳聚类结果，优先考虑聚类数量接近预期的结果"""

        # 按聚类质量和数量匹配度评分
        scored_results = []
        for method, labels, score in clustering_results:
            unique_speakers = len(np.unique(labels[labels >= 0]))

            # 计算聚类数量匹配度
            if self.expected_speakers > 0:
                count_match_score = 1.0 - abs(unique_speakers - self.expected_speakers) / max(unique_speakers, self.expected_speakers)
            else:
                count_match_score = 0.5  # 中性分数

            # 综合评分：聚类质量 * 0.6 + 数量匹配度 * 0.4
            combined_score = score * 0.6 + count_match_score * 0.4

            scored_results.append((method, labels, score, combined_score, unique_speakers))
            logger.debug(f"{method}: 质量={score:.4f}, 数量匹配={count_match_score:.4f}, 综合={combined_score:.4f}, 聚类数={unique_speakers}")

        # 选择综合得分最高的结果
        best_result = max(scored_results, key=lambda x: x[3])
        return best_result[0], best_result[1], best_result[2]

    def _force_split_clustering(self, embeddings: List[np.ndarray]) -> List[int]:
        """强制分割聚类策略 - 当所有方法都失败时使用"""

        if self.expected_speakers <= 1:
            return [0] * len(embeddings)

        logger.info(f"使用强制分割策略，将{len(embeddings)}个分段分配给{self.expected_speakers}个说话人")

        # 策略1：基于时间顺序交替分配
        if len(embeddings) <= self.expected_speakers * 2:
            # 简单交替分配
            labels = [i % self.expected_speakers for i in range(len(embeddings))]
            logger.debug("使用简单交替分配策略")
            return labels

        # 策略2：基于相似度的智能分割
        try:
            embedding_matrix = np.array(embeddings)

            # 选择最不相似的点作为初始聚类中心
            centers = self._select_diverse_centers(embedding_matrix, self.expected_speakers)

            # 将每个点分配给最相似的中心
            labels = []
            for i, embedding in enumerate(embeddings):
                similarities = [cosine_similarity([embedding], [embeddings[center]])[0][0] for center in centers]
                best_center = np.argmax(similarities)
                labels.append(best_center)

            logger.debug(f"使用智能分割策略，选择的中心点: {centers}")
            return labels

        except Exception as e:
            logger.warning(f"智能分割策略失败: {e}，回退到交替分配")
            return [i % self.expected_speakers for i in range(len(embeddings))]

    def _select_diverse_centers(self, embeddings: np.ndarray, n_centers: int) -> List[int]:
        """选择最具多样性的点作为聚类中心"""

        if len(embeddings) <= n_centers:
            return list(range(len(embeddings)))

        # 计算所有点之间的相似度
        similarity_matrix = cosine_similarity(embeddings)

        # 选择第一个中心（随机或选择最中心的点）
        centers = [0]

        # 贪心选择剩余中心
        for _ in range(n_centers - 1):
            max_min_distance = -1
            best_candidate = -1

            for candidate in range(len(embeddings)):
                if candidate in centers:
                    continue

                # 计算候选点到已选中心的最小距离
                min_distance = min(1 - similarity_matrix[candidate][center] for center in centers)

                if min_distance > max_min_distance:
                    max_min_distance = min_distance
                    best_candidate = candidate

            if best_candidate != -1:
                centers.append(best_candidate)

        return centers

    def _evaluate_clustering(self, embeddings: np.ndarray, labels: np.ndarray) -> float:
        """评估聚类质量"""
        try:
            # 过滤噪声点
            valid_mask = labels >= 0
            if np.sum(valid_mask) < 2:
                return 0.0
            
            valid_embeddings = embeddings[valid_mask]
            valid_labels = labels[valid_mask]
            
            # 检查聚类数量
            unique_labels = np.unique(valid_labels)
            if len(unique_labels) < 2:
                return 0.0
            
            # 计算轮廓系数
            score = silhouette_score(valid_embeddings, valid_labels, metric='cosine')
            return max(0.0, score)  # 确保非负
            
        except Exception as e:
            logger.warning(f"聚类评估失败: {e}")
            return 0.0
    
    def _post_process_labels(self, labels: List[int], segments: List[Dict], 
                           embeddings: List[np.ndarray]) -> List[int]:
        """后处理优化标签"""
        
        # 处理噪声点（DBSCAN产生的-1标签）
        processed_labels = []
        for i, label in enumerate(labels):
            if label == -1:
                # 为噪声点分配最相似的聚类
                label = self._assign_noise_point(i, embeddings, labels)
            processed_labels.append(label)
        
        # 时间连续性优化
        optimized_labels = self._temporal_smoothing(processed_labels, segments)
        
        return optimized_labels
    
    def _assign_noise_point(self, noise_index: int, embeddings: List[np.ndarray], 
                           labels: List[int]) -> int:
        """为噪声点分配最相似的聚类"""
        try:
            noise_embedding = embeddings[noise_index]
            
            # 找到所有非噪声点
            valid_indices = [i for i, label in enumerate(labels) if label >= 0]
            if not valid_indices:
                return 0  # 如果没有有效聚类，分配为0
            
            # 计算与所有有效点的相似度
            similarities = []
            for idx in valid_indices:
                sim = cosine_similarity([noise_embedding], [embeddings[idx]])[0][0]
                similarities.append((sim, labels[idx]))
            
            # 选择最相似的聚类
            best_similarity, best_label = max(similarities, key=lambda x: x[0])
            logger.debug(f"噪声点{noise_index}分配到聚类{best_label}，相似度: {best_similarity:.4f}")
            
            return best_label
            
        except Exception as e:
            logger.warning(f"噪声点分配失败: {e}")
            return 0
    
    def _temporal_smoothing(self, labels: List[int], segments: List[Dict]) -> List[int]:
        """时间连续性平滑"""
        if len(labels) <= 2:
            return labels
        
        smoothed = labels.copy()
        
        for i in range(1, len(labels) - 1):
            current_segment = segments[i]
            prev_segment = segments[i-1]
            next_segment = segments[i+1]
            
            # 检查时间间隔
            gap_before = current_segment['start_time'] - prev_segment['end_time']
            gap_after = next_segment['start_time'] - current_segment['end_time']
            
            # 如果前后都是同一个说话人，且时间间隔很短，当前片段也很短
            if (labels[i-1] == labels[i+1] and 
                gap_before < 1.0 and gap_after < 1.0 and
                current_segment['duration'] < 2.0):
                smoothed[i] = labels[i-1]
                logger.debug(f"时间平滑：片段{i}从说话人{labels[i]}调整为{labels[i-1]}")
        
        return smoothed
    
    def _create_single_speaker_result(self, segments: List[Tuple[float, float]]) -> Dict:
        """创建单说话人结果"""
        logger.info("创建单说话人结果")
        
        speaker_segments = []
        for i, (start, end) in enumerate(segments):
            speaker_segments.append({
                'start_time': start,
                'end_time': end,
                'duration': end - start,
                'speaker_id': 0,
                'speaker_label': '说话人1',
                'segment_index': i
            })
        
        return {
            'speaker_segments': speaker_segments,
            'speaker_count': 1,
            'speaker_statistics': {
                '说话人1': {
                    'total_duration': sum(end - start for start, end in segments),
                    'segment_count': len(segments)
                }
            }
        }
    
    def _build_speaker_result(self, segments: List[Dict], labels: List[int]) -> Dict:
        """构建说话人识别结果"""
        speaker_segments = []
        speaker_stats = {}
        
        # 重新映射标签，确保从0开始连续
        unique_labels = sorted(list(set(labels)))
        label_mapping = {old_label: new_label for new_label, old_label in enumerate(unique_labels)}
        
        for i, (segment, label) in enumerate(zip(segments, labels)):
            mapped_label = label_mapping[label]
            speaker_name = f'说话人{mapped_label + 1}'
            
            speaker_segments.append({
                'start_time': segment['start_time'],
                'end_time': segment['end_time'],
                'duration': segment['duration'],
                'speaker_id': mapped_label,
                'speaker_label': speaker_name,
                'segment_index': segment['segment_index']
            })
            
            # 统计信息
            if speaker_name not in speaker_stats:
                speaker_stats[speaker_name] = {
                    'total_duration': 0.0,
                    'segment_count': 0
                }
            
            speaker_stats[speaker_name]['total_duration'] += segment['duration']
            speaker_stats[speaker_name]['segment_count'] += 1
        
        return {
            'speaker_segments': speaker_segments,
            'speaker_count': len(unique_labels),
            'speaker_statistics': speaker_stats
        }

    def get_clustering_debug_info(self, embeddings: List[np.ndarray]) -> Dict:
        """获取聚类调试信息"""
        if len(embeddings) < 2:
            return {'error': '嵌入向量不足'}

        embedding_matrix = np.array(embeddings)
        similarity_matrix = cosine_similarity(embedding_matrix)

        # 计算统计信息
        upper_triangle = similarity_matrix[np.triu_indices_from(similarity_matrix, k=1)]

        debug_info = {
            'embedding_count': len(embeddings),
            'embedding_dimension': embeddings[0].shape[0] if embeddings else 0,
            'similarity_stats': {
                'mean': float(np.mean(upper_triangle)),
                'std': float(np.std(upper_triangle)),
                'min': float(np.min(upper_triangle)),
                'max': float(np.max(upper_triangle)),
                'median': float(np.median(upper_triangle))
            },
            'similarity_matrix': similarity_matrix.tolist(),
            'threshold_analysis': self._analyze_threshold_effectiveness(similarity_matrix)
        }

        return debug_info

    def _analyze_threshold_effectiveness(self, similarity_matrix: np.ndarray) -> Dict:
        """分析阈值有效性"""
        thresholds = [0.1, 0.12, 0.15, 0.18, 0.2, 0.25, 0.3]
        analysis = {}

        for threshold in thresholds:
            try:
                clustering = AgglomerativeClustering(
                    n_clusters=None,
                    distance_threshold=threshold,
                    metric='cosine',
                    linkage='average'
                )
                labels = clustering.fit_predict(similarity_matrix)
                unique_clusters = len(np.unique(labels))

                analysis[f'threshold_{threshold}'] = {
                    'cluster_count': unique_clusters,
                    'threshold': threshold
                }
            except Exception as e:
                analysis[f'threshold_{threshold}'] = {
                    'error': str(e),
                    'threshold': threshold
                }

        return analysis
