import{l as ts,h as i,m as R,_ as es,u as as,g as ns,o as os,n as ls,r as O,a as h,c as k,b as s,f as C,t as g,d as F,w as I,F as is,p as rs,q as cs,E as ds,s as us}from"./index-2c134546.js";import{a as A}from"./audioProcessing-868a9059.js";import{k as vs}from"./knowledge-a7f85df9.js";const ps=ts("audio",()=>{const m=i(new Map),y=i([]),b=i(null),d=i(new Map),p=i([]),S=i([]),r=i({mode:"speech-recognition",language:"zh-CN",enableVAD:!0,enableTimestamp:!0,enableSpeakerDiarization:!1}),u=i([]),f=i(new Map),D=i([]),_=i(new Map),T=i("default"),M=R(()=>Array.from(m.value.values())),$=R(()=>Array.from(d.value.values()).filter(e=>e.status==="running")),l=R(()=>Array.from(d.value.values()).filter(e=>e.status==="completed")),t=R(()=>Array.from(d.value.values()).filter(e=>e.status==="error")),w=e=>{const a=K(),n={id:a,file:e,status:"ready",progress:0,uploadedAt:new Date,metadata:{}};return m.value.set(a,n),n},c=e=>{var n;m.value.delete(e);const a=y.value.findIndex(o=>o.id===e);a>-1&&y.value.splice(a,1),((n=b.value)==null?void 0:n.id)===e&&(b.value=null)},v=(e,a,n={})=>{const o=m.value.get(e);o&&(o.status=a,Object.assign(o,n))},U=(e,a)=>{const n=m.value.get(e);n&&(n.progress=a)},z=async e=>{try{const a=await A.createProcessingTask(e),n={...a,createdAt:new Date,status:"pending",progress:0,stages:[]};return d.value.set(a.id,n),p.value.push(a.id),n}catch(a){throw new Error(`创建任务失败: ${a.message}`)}},N=(e,a,n={})=>{const o=d.value.get(e);if(o&&(o.status=a,o.lastUpdate=new Date,Object.assign(o,n),a==="completed"||a==="error")){S.value.unshift({...o}),d.value.delete(e);const x=p.value.indexOf(e);x>-1&&p.value.splice(x,1)}},B=(e,a,n=null)=>{const o=d.value.get(e);if(o&&(o.progress=a,o.lastUpdate=new Date,n)){o.currentStage=n,o.stages||(o.stages=[]);const x=o.stages.find(ss=>ss.stage===n.stage);x?Object.assign(x,n):o.stages.push({...n,timestamp:new Date})}},E=async e=>{try{return await A.cancelTask(e),N(e,"cancelled"),!0}catch(a){throw new Error(`取消任务失败: ${a.message}`)}},W=e=>{r.value={...r.value,...e}},j=(e,a=null)=>{const n={id:X(),name:e,config:a||{...r.value},createdAt:new Date};return u.value.push(n),localStorage.setItem("audio-config-templates",JSON.stringify(u.value)),n},G=e=>{const a=u.value.find(n=>n.id===e);return a?(r.value={...a.config},a):null},H=e=>{const a=u.value.findIndex(n=>n.id===e);return a>-1?(u.value.splice(a,1),localStorage.setItem("audio-config-templates",JSON.stringify(u.value)),!0):!1},J=e=>{const a=Y(),n={id:a,...e,createdAt:new Date};return f.value.set(a,n),D.value.unshift(n),n},V=e=>f.value.get(e),q=e=>{f.value.delete(e);const a=D.value.findIndex(n=>n.id===e);a>-1&&D.value.splice(a,1)},P=e=>{const a=Z(),n={id:a,name:e,files:[],config:{...r.value},createdAt:new Date};return _.value.set(a,n),n},L=e=>{if(_.value.has(e)){T.value=e;const a=_.value.get(e);return r.value={...a.config},a}return null},Q=(e=null)=>{const a=e||T.value,n=_.value.get(a);return n?(n.config={...r.value},n.files=[...y.value],n.lastSaved=new Date,localStorage.setItem(`workspace-${a}`,JSON.stringify(n)),n):null},K=()=>`file-${Date.now()}-${Math.random().toString(36).substring(2)}`,X=()=>`template-${Date.now()}-${Math.random().toString(36).substring(2)}`,Y=()=>`result-${Date.now()}-${Math.random().toString(36).substring(2)}`,Z=()=>`workspace-${Date.now()}-${Math.random().toString(36).substring(2)}`;return{uploadedFiles:m,selectedFiles:y,currentPreviewFile:b,processingTasks:d,taskQueue:p,taskHistory:S,currentConfig:r,configTemplates:u,processingResults:f,resultHistory:D,workspaces:_,currentWorkspace:T,activeFiles:M,runningTasks:$,completedTasks:l,failedTasks:t,addFile:w,removeFile:c,updateFileStatus:v,updateFileProgress:U,createTask:z,updateTaskStatus:N,updateTaskProgress:B,cancelTask:E,updateConfig:W,saveConfigTemplate:j,loadConfigTemplate:G,deleteConfigTemplate:H,addResult:J,getResult:V,removeResult:q,createWorkspace:P,switchWorkspace:L,saveWorkspace:Q,clearAll:()=>{m.value.clear(),y.value=[],b.value=null,d.value.clear(),p.value=[],f.value.clear()},clearHistory:()=>{S.value=[],D.value=[]},initialize:()=>{const e=localStorage.getItem("audio-config-templates");if(e)try{u.value=JSON.parse(e)}catch(a){console.error("加载配置模板失败:",a)}if(!_.value.has("default")){const a=P("默认工作区");T.value=a.id}}}});const fs={class:"dashboard-container"},gs={class:"dashboard-header"},ms={class:"header-content"},_s={class:"header-actions"},ws={class:"welcome-text"},hs={class:"user-name"},ks={class:"dashboard-main"},ys={class:"quick-nav"},bs={class:"nav-grid"},Ss={class:"system-status"},Ds={class:"status-grid"},Ts={class:"status-card"},As={class:"status-info"},xs={class:"status-card"},Fs={class:"status-info"},Is={class:"audio-stats"},Rs={class:"stats-grid"},Ms={class:"stat-card"},$s={class:"stat-info"},Cs={class:"stat-number"},Ns={class:"stat-card"},Ps={class:"stat-info"},Os={class:"stat-number"},Us={class:"stat-card"},zs={class:"stat-info"},Bs={class:"stat-number"},Es={class:"stat-card"},Ws={class:"stat-info"},js={class:"stat-number"},Gs={class:"recent-activity"},Hs={class:"activity-list"},Js={class:"activity-icon"},Vs={key:0},qs={key:1},Ls={key:2},Qs={key:3},Ks={key:4},Xs={class:"activity-time"},Ys={class:"activity-content"},Zs={class:"activity-footer"},st={__name:"Dashboard",setup(m){const y=as(),b=ns();ps();const d=R(()=>b.userName||"用户"),p=i("检查中..."),S=i("检查中..."),r=i({totalFiles:0,activeTasks:0,onlineUsers:0,successRate:0}),u=i([{id:1,type:"user_login",description:"用户登录系统",timestamp:new Date},{id:2,type:"audio_upload",description:"上传了3个音频文件到处理队列",timestamp:new Date(Date.now()-5*60*1e3)},{id:3,type:"task_complete",description:"批量语音识别任务完成",timestamp:new Date(Date.now()-15*60*1e3)},{id:4,type:"user_join",description:"新用户加入协作工作区",timestamp:new Date(Date.now()-30*60*1e3)},{id:5,type:"batch_process",description:"启动了包含12个文件的批量处理任务",timestamp:new Date(Date.now()-45*60*1e3)}]);let f=null;const D=async()=>{try{const l=await vs.getStats();p.value=l.data.initialized?"已初始化":"未初始化"}catch{p.value="连接失败"}},_=async()=>{try{const l=await A.getSystemStatus();S.value=l.data.status==="healthy"?"运行正常":"服务异常"}catch{S.value="连接失败"}},T=async()=>{try{const[l,t,w]=await Promise.all([A.getProcessedFilesCount(),A.getActiveTasks(),A.getOnlineUsers()]);r.value={totalFiles:l.data.count||0,activeTasks:t.data.length||0,onlineUsers:w.data.length||0,successRate:Math.round(l.data.successCount/Math.max(l.data.count,1)*100)||0}}catch(l){console.error("更新音频统计失败:",l)}},M=l=>{const t=new Date,w=new Date(l),c=t-w;return c<6e4?"刚刚":c<36e5?`${Math.floor(c/6e4)}分钟前`:c<864e5?`${Math.floor(c/36e5)}小时前`:w.toLocaleDateString()},$=async()=>{try{await cs.confirm("确定要退出登录吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await b.logout(),ds.success("已退出登录"),y.push("/login")}catch(l){l!=="cancel"&&console.error("退出登录失败:",l)}};return os(()=>{D(),_(),T(),f=setInterval(()=>{T()},3e4)}),ls(()=>{f&&clearInterval(f)}),(l,t)=>{const w=O("el-button"),c=O("router-link");return h(),k("div",fs,[s("div",gs,[s("div",ms,[t[2]||(t[2]=s("div",{class:"header-brand"},[s("div",{class:"brand-logo"},"🧠"),s("span",{class:"brand-text"},"RAG智能问答平台")],-1)),s("div",_s,[s("span",ws,[t[0]||(t[0]=C("欢迎，")),s("span",hs,g(d.value),1)]),F(w,{onClick:$,class:"logout-btn"},{default:I(()=>t[1]||(t[1]=[C("退出登录")])),_:1,__:[1]})])])]),s("div",ks,[s("div",ys,[t[6]||(t[6]=s("h2",{class:"section-title"},"快速导航",-1)),s("div",bs,[F(c,{to:"/knowledge",class:"nav-card gradient-border"},{default:I(()=>t[3]||(t[3]=[s("div",{class:"nav-icon"},[s("div",{class:"icon-gradient"},"🧠")],-1),s("div",{class:"nav-content"},[s("h3",null,"RAG知识库"),s("p",null,"智能问答系统，支持文档上传和向量检索")],-1),s("div",{class:"nav-arrow"},"→",-1)])),_:1,__:[3]}),F(c,{to:"/documents",class:"nav-card gradient-border"},{default:I(()=>t[4]||(t[4]=[s("div",{class:"nav-icon"},[s("div",{class:"icon-gradient"},"📄")],-1),s("div",{class:"nav-content"},[s("h3",null,"文档管理"),s("p",null,"上传、管理和处理各种格式的文档")],-1),s("div",{class:"nav-arrow"},"→",-1)])),_:1,__:[4]}),F(c,{to:"/audio-center",class:"nav-card gradient-border"},{default:I(()=>t[5]||(t[5]=[s("div",{class:"nav-icon"},[s("div",{class:"icon-gradient"},"🎵")],-1),s("div",{class:"nav-content"},[s("h3",null,"音频处理中心"),s("p",null,"语音识别、说话人识别、会议转录一体化处理")],-1),s("div",{class:"nav-arrow"},"→",-1)])),_:1,__:[5]})])]),s("div",Ss,[t[13]||(t[13]=s("h2",null,"系统状态",-1)),s("div",Ds,[t[11]||(t[11]=s("div",{class:"status-card"},[s("div",{class:"status-icon online"},"●"),s("div",{class:"status-info"},[s("h4",null,"后端服务"),s("p",null,"运行正常")])],-1)),s("div",Ts,[t[8]||(t[8]=s("div",{class:"status-icon online"},"●",-1)),s("div",As,[t[7]||(t[7]=s("h4",null,"RAG服务",-1)),s("p",null,g(p.value),1)])]),s("div",xs,[t[10]||(t[10]=s("div",{class:"status-icon online"},"●",-1)),s("div",Fs,[t[9]||(t[9]=s("h4",null,"音频处理",-1)),s("p",null,g(S.value),1)])]),t[12]||(t[12]=s("div",{class:"status-card"},[s("div",{class:"status-icon online"},"●"),s("div",{class:"status-info"},[s("h4",null,"数据库"),s("p",null,"连接正常")])],-1))])]),s("div",Is,[t[22]||(t[22]=s("h2",null,"音频处理统计",-1)),s("div",Rs,[s("div",Ms,[t[15]||(t[15]=s("div",{class:"stat-icon"},"🎵",-1)),s("div",$s,[s("div",Cs,g(r.value.totalFiles),1),t[14]||(t[14]=s("div",{class:"stat-label"},"总处理文件",-1))])]),s("div",Ns,[t[17]||(t[17]=s("div",{class:"stat-icon"},"⚡",-1)),s("div",Ps,[s("div",Os,g(r.value.activeTasks),1),t[16]||(t[16]=s("div",{class:"stat-label"},"活跃任务",-1))])]),s("div",Us,[t[19]||(t[19]=s("div",{class:"stat-icon"},"👥",-1)),s("div",zs,[s("div",Bs,g(r.value.onlineUsers),1),t[18]||(t[18]=s("div",{class:"stat-label"},"在线用户",-1))])]),s("div",Es,[t[21]||(t[21]=s("div",{class:"stat-icon"},"📊",-1)),s("div",Ws,[s("div",js,g(r.value.successRate)+"%",1),t[20]||(t[20]=s("div",{class:"stat-label"},"成功率",-1))])])])]),s("div",Gs,[t[24]||(t[24]=s("h2",null,"最近活动",-1)),s("div",Hs,[(h(!0),k(is,null,rs(u.value,v=>(h(),k("div",{key:v.id,class:us(["activity-item",`activity-${v.type}`])},[s("div",Js,[v.type==="audio_upload"?(h(),k("span",Vs,"🎵")):v.type==="task_complete"?(h(),k("span",qs,"✅")):v.type==="user_join"?(h(),k("span",Ls,"👥")):v.type==="batch_process"?(h(),k("span",Qs,"⚡")):(h(),k("span",Ks,"📝"))]),s("div",Xs,g(M(v.timestamp)),1),s("div",Ys,g(v.description),1)],2))),128))]),s("div",Zs,[F(c,{to:"/audio-center",class:"view-all-link"},{default:I(()=>t[23]||(t[23]=[C(" 查看音频处理中心 → ")])),_:1,__:[23]})])])])])}}},it=es(st,[["__scopeId","data-v-fbde3fbb"]]);export{it as default};
