#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}========================================"
echo -e "语音处理智能平台 - 一键启动脚本"
echo -e "========================================${NC}"
echo

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}❌ Python3未安装或未添加到PATH${NC}"
    echo "请安装Python 3.11+并添加到系统PATH"
    exit 1
fi

# 检查Node.js环境
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js未安装或未添加到PATH${NC}"
    echo "请安装Node.js 16.0+并添加到系统PATH"
    exit 1
fi

# 检查Docker环境
if ! command -v docker &> /dev/null; then
    echo -e "${YELLOW}⚠️ Docker未安装，将跳过Redis自动启动${NC}"
    echo "请手动安装并启动Redis服务"
    SKIP_REDIS=true
fi

# 检查虚拟环境
if [ ! -f ".venv/bin/activate" ]; then
    echo -e "${YELLOW}🔧 创建Python虚拟环境...${NC}"
    python3 -m venv .venv
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 虚拟环境创建失败${NC}"
        exit 1
    fi
fi

# 激活虚拟环境
echo -e "${YELLOW}🔧 激活虚拟环境...${NC}"
source .venv/bin/activate

# 安装后端依赖
if [ ! -d ".venv/lib/python*/site-packages/fastapi" ]; then
    echo -e "${YELLOW}📦 安装后端依赖...${NC}"
    pip install -r backend/requirements.txt
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 后端依赖安装失败${NC}"
        exit 1
    fi
fi

# 检查前端依赖
if [ ! -d "frontend/node_modules" ]; then
    echo -e "${YELLOW}📦 安装前端依赖...${NC}"
    cd frontend
    npm install
    if [ $? -ne 0 ]; then
        echo -e "${RED}❌ 前端依赖安装失败${NC}"
        exit 1
    fi
    cd ..
fi

# 检查Redis
if [ "$SKIP_REDIS" != "true" ]; then
    echo -e "${YELLOW}🔍 检查Redis服务...${NC}"
    if ! docker ps | grep -q redis; then
        echo -e "${YELLOW}🚀 启动Redis容器...${NC}"
        docker run -d --name redis -p 6379:6379 redis:latest
        if [ $? -ne 0 ]; then
            echo -e "${RED}❌ Redis启动失败，请确保Docker已安装并运行${NC}"
            exit 1
        fi
        sleep 3
    fi
fi

# 初始化数据库
if [ ! -f "data/speech_platform.db" ]; then
    echo -e "${YELLOW}🗄️ 初始化数据库...${NC}"
    python -c "from backend.core.database import init_db; init_db()"
fi

echo
echo -e "${GREEN}✅ 环境检查完成，开始启动服务...${NC}"
echo

# 创建日志目录
mkdir -p logs

# 启动后端服务
echo -e "${YELLOW}🚀 启动后端服务 (端口: 8002)...${NC}"
nohup python -m uvicorn backend.main:app --host 0.0.0.0 --port 8002 --reload > logs/backend.log 2>&1 &
BACKEND_PID=$!
echo "后端服务PID: $BACKEND_PID"

# 等待后端启动
sleep 5

# 启动Celery Worker
echo -e "${YELLOW}🚀 启动Celery Worker...${NC}"
if [[ "$OSTYPE" == "darwin"* ]]; then
    # macOS
    nohup celery -A backend.celery_app worker --loglevel=info --concurrency=4 > logs/celery.log 2>&1 &
else
    # Linux
    nohup python start_worker_windows.py > logs/celery.log 2>&1 &
fi
CELERY_PID=$!
echo "Celery Worker PID: $CELERY_PID"

# 等待Worker启动
sleep 3

# 启动前端服务
echo -e "${YELLOW}🚀 启动前端服务 (端口: 3000)...${NC}"
cd frontend
nohup npm run dev > ../logs/frontend.log 2>&1 &
FRONTEND_PID=$!
cd ..
echo "前端服务PID: $FRONTEND_PID"

# 等待前端启动
sleep 5

# 保存PID到文件
echo "$BACKEND_PID" > .backend.pid
echo "$CELERY_PID" > .celery.pid
echo "$FRONTEND_PID" > .frontend.pid

echo
echo -e "${GREEN}========================================"
echo -e "🎉 所有服务启动完成！"
echo -e "========================================${NC}"
echo
echo -e "${BLUE}📱 前端界面: http://localhost:3000${NC}"
echo -e "${BLUE}🔧 后端API: http://localhost:8002${NC}"
echo -e "${BLUE}📚 API文档: http://localhost:8002/docs${NC}"
echo
echo -e "${YELLOW}默认登录账户:${NC}"
echo "  用户名: admin"
echo "  密码: admin123"
echo
echo -e "${YELLOW}💡 提示:${NC}"
echo "  - 首次启动可能需要下载AI模型，请耐心等待"
echo "  - 查看日志: tail -f logs/backend.log"
echo "  - 停止服务: ./stop_all_services.sh"
echo "  - 重启服务: ./restart_all_services.sh"
echo
echo -e "${YELLOW}🔗 更多帮助:${NC}"
echo "  - 项目文档: README.md"
echo "  - 部署指南: DEPLOYMENT_GUIDE.md"
echo "  - 问题反馈: GitHub Issues"
echo

# 检查服务状态
echo -e "${YELLOW}🔍 检查服务状态...${NC}"
sleep 2

# 检查后端
if curl -s http://localhost:8002/health > /dev/null; then
    echo -e "${GREEN}✅ 后端服务运行正常${NC}"
else
    echo -e "${RED}❌ 后端服务可能未正常启动，请检查日志${NC}"
fi

# 检查前端
if curl -s http://localhost:3000 > /dev/null; then
    echo -e "${GREEN}✅ 前端服务运行正常${NC}"
else
    echo -e "${YELLOW}⚠️ 前端服务可能还在启动中...${NC}"
fi

echo
echo -e "${GREEN}🚀 启动完成！浏览器将自动打开前端界面...${NC}"

# 自动打开浏览器
if command -v xdg-open &> /dev/null; then
    xdg-open http://localhost:3000
elif command -v open &> /dev/null; then
    open http://localhost:3000
fi

echo
echo "按Ctrl+C退出监控，服务将继续在后台运行"
echo "使用 ./stop_all_services.sh 停止所有服务"

# 监控服务状态
trap 'echo -e "\n${YELLOW}退出监控，服务继续运行...${NC}"; exit 0' INT

while true; do
    sleep 30
    if ! kill -0 $BACKEND_PID 2>/dev/null; then
        echo -e "${RED}❌ 后端服务已停止${NC}"
        break
    fi
    if ! kill -0 $CELERY_PID 2>/dev/null; then
        echo -e "${RED}❌ Celery Worker已停止${NC}"
        break
    fi
    if ! kill -0 $FRONTEND_PID 2>/dev/null; then
        echo -e "${RED}❌ 前端服务已停止${NC}"
        break
    fi
done
