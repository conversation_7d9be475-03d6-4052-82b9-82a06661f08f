#!/usr/bin/env python3
"""
优化的FunASR模型管理器
解决内存泄漏问题，实现高效的GPU内存管理
支持完全离线运行模式
"""

import os
import gc
import time
import threading
import logging
from typing import Optional, Dict, Any, List
from pathlib import Path
import torch

# 🔧 设置完全离线环境变量 - 基于Context7研究成果
os.environ.update({
    'HF_HUB_OFFLINE': '1',
    'HF_DATASETS_OFFLINE': '1',
    'TRANSFORMERS_OFFLINE': '1',
    'HF_HUB_DISABLE_TELEMETRY': '1',
    'HF_HUB_DISABLE_PROGRESS_BARS': '1',
    'HF_HUB_DISABLE_SYMLINKS_WARNING': '1',
    'HF_HUB_DISABLE_EXPERIMENTAL_WARNING': '1',
    'TOKENIZERS_PARALLELISM': 'false'  # 避免多线程警告
})

logger = logging.getLogger(__name__)

class OptimizedFunASRManager:
    """优化的FunASR模型管理器"""
    
    def __init__(self):
        self.model = None
        self.model_path = None
        self.device = None
        self.model_lock = threading.Lock()
        self.last_used = None
        self.model_loaded = False
        
        # 内存管理配置
        self.max_idle_time = 300  # 5分钟空闲后卸载模型
        self.enable_auto_cleanup = True
        
        # GPU优化配置
        self.gpu_memory_fraction = 0.8
        self.enable_mixed_precision = False
        
    def _setup_gpu_optimization(self):
        """设置GPU优化"""
        if not torch.cuda.is_available():
            return
            
        try:
            # 设置GPU内存分配策略
            os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:512,expandable_segments:True'
            
            # 清理GPU缓存
            torch.cuda.empty_cache()
            
            # 设置内存使用比例
            if hasattr(torch.cuda, 'set_per_process_memory_fraction'):
                torch.cuda.set_per_process_memory_fraction(self.gpu_memory_fraction)
            
            logger.info(f"GPU优化设置完成，内存使用比例: {self.gpu_memory_fraction}")
            
        except Exception as e:
            logger.warning(f"GPU优化设置失败: {e}")
    
    def _determine_device(self) -> str:
        """确定使用的设备"""
        if torch.cuda.is_available():
            device = "cuda:0"
            logger.info(f"使用GPU设备: {device}")
        else:
            device = "cpu"
            logger.info(f"使用CPU设备: {device}")
        return device
    
    def _create_optimized_config(self, model_path: str, device: str) -> Dict[str, Any]:
        """创建优化的模型配置 - 基于FunASR最佳实践和完全离线模式"""

        # 🔧 获取本地VAD模型路径
        from backend.core.config import settings
        vad_model_path = str(settings.VAD_MODEL_PATH)

        # 🔧 验证VAD模型路径存在
        if not os.path.exists(vad_model_path):
            logger.error(f"VAD模型路径不存在: {vad_model_path}")
            logger.info("尝试使用环境变量VAD_MODEL_PATH...")

            # 尝试从环境变量获取
            env_vad_path = os.getenv('VAD_MODEL_PATH')
            if env_vad_path and os.path.exists(env_vad_path):
                vad_model_path = env_vad_path
                logger.info(f"使用环境变量VAD模型路径: {vad_model_path}")
            else:
                raise FileNotFoundError(f"VAD模型路径不存在: {vad_model_path}，请检查配置")

        logger.info(f"🔧 使用本地VAD模型: {vad_model_path}")

        # 🔧 设置FunASR模型缓存路径，让FunASR能找到本地模型
        models_cache_dir = str(Path(vad_model_path).parent)
        os.environ['FUNASR_CACHE_DIR'] = models_cache_dir
        logger.info(f"🔧 设置FunASR缓存目录: {models_cache_dir}")

        # 🔧 创建ModelScope缓存目录结构，让FunASR能找到本地VAD模型
        import json
        config_file = Path(vad_model_path) / "configuration.json"
        vad_model_identifier = "fsmn-vad"  # 默认标识符

        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    ms_model_id = config_data.get('model_name_in_hub', {}).get('ms', '')
                    if ms_model_id:
                        # 创建ModelScope缓存目录结构
                        cache_dir = Path.home() / ".cache" / "modelscope" / "hub" / "models" / ms_model_id.replace('/', os.sep)
                        cache_dir.mkdir(parents=True, exist_ok=True)

                        # 如果缓存目录不存在模型文件，创建软链接
                        cache_model_pt = cache_dir / "model.pt"
                        cache_config_yaml = cache_dir / "config.yaml"
                        cache_config_json = cache_dir / "configuration.json"

                        source_model_pt = Path(vad_model_path) / "model.pt"
                        source_config_yaml = Path(vad_model_path) / "config.yaml"
                        source_config_json = Path(vad_model_path) / "configuration.json"

                        # 创建软链接或复制文件
                        for cache_file, source_file in [
                            (cache_model_pt, source_model_pt),
                            (cache_config_yaml, source_config_yaml),
                            (cache_config_json, source_config_json)
                        ]:
                            if source_file.exists() and not cache_file.exists():
                                try:
                                    # 尝试创建软链接
                                    cache_file.symlink_to(source_file)
                                    logger.info(f"🔗 创建软链接: {cache_file} -> {source_file}")
                                except OSError:
                                    # 如果软链接失败，复制文件
                                    import shutil
                                    shutil.copy2(source_file, cache_file)
                                    logger.info(f"📋 复制文件: {source_file} -> {cache_file}")

                        vad_model_identifier = "fsmn-vad"  # 使用标准标识符
                        logger.info(f"🔧 VAD模型标识符: {vad_model_identifier}")

            except Exception as e:
                logger.warning(f"处理VAD模型配置失败: {e}，使用本地路径")
                vad_model_identifier = str(vad_model_path)

        config = {
            'model': model_path,
            'trust_remote_code': True,
            'device': device,
            # 🔧 完全离线配置 - 基于深度分析结果
            'local_files_only': True,
            'disable_update': True,
            'offline': True,
            'use_auth_token': False,
            'force_download': False,
            'resume_download': False,
            # 🔧 使用VAD模型本地路径，强制离线加载
            'vad_model': str(vad_model_path),  # 直接使用本地路径强制离线
            'vad_kwargs': {
                "max_single_segment_time": 30000,  # 30秒最大分段
                # 🔧 为VAD模型也设置离线参数
                "local_files_only": True,
                "disable_update": True,
                "offline": True,
            },
        }

        # 检查是否有model.py文件
        model_py_path = os.path.join(model_path, "model.py")
        if os.path.exists(model_py_path):
            config['remote_code'] = model_py_path
            logger.info(f"使用本地model.py: {model_py_path}")

        # 验证模型路径存在
        if not os.path.exists(model_path):
            raise FileNotFoundError(f"模型路径不存在: {model_path}")

        # 检查必要的模型文件
        required_files = ['config.yaml', 'model.pt']
        for file_name in required_files:
            file_path = os.path.join(model_path, file_name)
            if not os.path.exists(file_path):
                logger.warning(f"模型文件可能缺失: {file_path}")

        logger.info(f"🔧 完全离线模式配置完成: {model_path}")
        return config
    
    def load_model(self, model_path: str, force_reload: bool = False) -> bool:
        """
        加载模型
        
        Args:
            model_path: 模型路径
            force_reload: 是否强制重新加载
            
        Returns:
            bool: 加载是否成功
        """
        with self.model_lock:
            # 检查是否需要重新加载
            if (self.model_loaded and 
                self.model_path == model_path and 
                not force_reload):
                self.last_used = time.time()
                logger.info("模型已加载，跳过重复加载")
                return True
            
            # 卸载现有模型
            if self.model_loaded:
                self._unload_model_internal()
            
            try:
                # 设置GPU优化
                self._setup_gpu_optimization()
                
                # 确定设备
                device = self._determine_device()
                
                # 创建优化配置
                config = self._create_optimized_config(model_path, device)
                
                logger.info(f"开始加载FunASR模型: {model_path}")
                start_time = time.time()
                
                # 导入FunASR
                try:
                    from funasr import AutoModel
                except ImportError as e:
                    logger.error(f"FunASR导入失败: {e}")
                    return False

                # 🔧 验证离线环境设置
                logger.info("🔧 验证离线环境配置...")
                offline_vars = ['HF_HUB_OFFLINE', 'HF_DATASETS_OFFLINE', 'TRANSFORMERS_OFFLINE']
                for var in offline_vars:
                    value = os.environ.get(var, 'NOT_SET')
                    logger.info(f"  {var}: {value}")

                # 加载模型 - 完全离线模式
                logger.info(f"🔧 开始离线加载FunASR模型: {model_path}")
                self.model = AutoModel(**config)
                
                # 更新状态
                self.model_path = model_path
                self.device = device
                self.model_loaded = True
                self.last_used = time.time()
                
                load_time = time.time() - start_time
                logger.info(f"FunASR模型加载成功，耗时: {load_time:.1f}秒")
                
                # 记录内存使用情况
                self._log_memory_usage("模型加载后")
                
                return True
                
            except Exception as e:
                logger.error(f"FunASR模型加载失败: {e}")
                self.model = None
                self.model_loaded = False
                return False
    
    def generate(self, input_audio: str, **kwargs) -> Optional[List[Dict[str, Any]]]:
        """
        生成语音识别结果 - 基于FunASR最佳实践

        Args:
            input_audio: 输入音频路径
            **kwargs: 其他参数

        Returns:
            识别结果
        """
        if not self.model_loaded or self.model is None:
            logger.error("模型未加载")
            return None

        try:
            self.last_used = time.time()

            # 基于FunASR示例的默认参数
            default_kwargs = {
                'cache': {},
                'language': 'auto',  # "zn", "en", "yue", "ja", "ko", "nospeech"
                'use_itn': True,
                'batch_size_s': 60,
                'merge_vad': True,  # 合并VAD分段
                'merge_length_s': 15,  # 合并长度15秒
            }
            default_kwargs.update(kwargs)

            # 执行推理
            logger.info(f"开始语音识别: {input_audio}")
            start_time = time.time()

            result = self.model.generate(input=input_audio, **default_kwargs)

            inference_time = time.time() - start_time
            logger.info(f"语音识别完成，耗时: {inference_time:.1f}秒")

            # 处理结果格式
            if result and len(result) > 0:
                # 如果是SenseVoice，可能需要后处理
                try:
                    from funasr.utils.postprocess_utils import rich_transcription_postprocess
                    if 'text' in result[0]:
                        result[0]['processed_text'] = rich_transcription_postprocess(result[0]['text'])
                except ImportError:
                    logger.debug("rich_transcription_postprocess不可用，跳过后处理")

            return result

        except Exception as e:
            logger.error(f"语音识别失败: {e}")
            return None
    
    def _unload_model_internal(self):
        """内部模型卸载方法"""
        if self.model is not None:
            logger.info("卸载FunASR模型")
            
            # 删除模型引用
            del self.model
            self.model = None
            
            # 强制垃圾回收
            gc.collect()
            
            # GPU内存清理
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.synchronize()
            
            # 更新状态
            self.model_loaded = False
            self.model_path = None
            self.device = None
            
            # 记录内存使用情况
            self._log_memory_usage("模型卸载后")
    
    def unload_model(self):
        """卸载模型"""
        with self.model_lock:
            self._unload_model_internal()
    
    def cleanup_if_idle(self):
        """如果空闲时间过长则清理模型"""
        if not self.enable_auto_cleanup or not self.model_loaded:
            return
        
        current_time = time.time()
        if (self.last_used and 
            current_time - self.last_used > self.max_idle_time):
            logger.info(f"模型空闲超过{self.max_idle_time}秒，自动卸载")
            self.unload_model()
    
    def _log_memory_usage(self, label: str):
        """记录内存使用情况"""
        try:
            import psutil
            
            # 系统内存
            memory = psutil.virtual_memory()
            process = psutil.Process()
            process_memory = process.memory_info().rss / 1024**3  # GB
            
            log_msg = f"[{label}] 进程内存: {process_memory:.1f}GB, 系统内存: {memory.percent:.1f}%"
            
            # GPU内存
            if torch.cuda.is_available():
                allocated = torch.cuda.memory_allocated() / 1024**3
                reserved = torch.cuda.memory_reserved() / 1024**3
                log_msg += f", GPU已分配: {allocated:.1f}GB, GPU已缓存: {reserved:.1f}GB"
            
            logger.info(log_msg)
            
        except Exception as e:
            logger.warning(f"内存使用记录失败: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """获取管理器状态"""
        return {
            'model_loaded': self.model_loaded,
            'model_path': self.model_path,
            'device': self.device,
            'last_used': self.last_used,
            'idle_time': time.time() - self.last_used if self.last_used else None
        }
    
    def __del__(self):
        """析构函数，确保资源清理"""
        try:
            if self.model_loaded:
                self._unload_model_internal()
        except Exception as e:
            logger.warning(f"析构函数清理失败: {e}")


# 全局单例实例
_funasr_manager = None
_manager_lock = threading.Lock()

def get_funasr_manager() -> OptimizedFunASRManager:
    """获取FunASR管理器单例"""
    global _funasr_manager
    
    with _manager_lock:
        if _funasr_manager is None:
            _funasr_manager = OptimizedFunASRManager()
        return _funasr_manager

def cleanup_funasr_manager():
    """清理FunASR管理器"""
    global _funasr_manager
    
    with _manager_lock:
        if _funasr_manager is not None:
            _funasr_manager.unload_model()
            _funasr_manager = None
