#!/usr/bin/env python3
"""
音频处理内存监控调试脚本
用于实时监控音频处理任务的内存使用情况
"""

import os
import sys
import time
import psutil
import threading
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class MemoryMonitor:
    """内存监控器"""
    
    def __init__(self):
        self.monitoring = False
        self.monitor_thread = None
        self.memory_data = []
        self.start_time = None
        
    def start_monitoring(self, interval: float = 1.0):
        """开始监控"""
        if self.monitoring:
            return
            
        self.monitoring = True
        self.start_time = time.time()
        self.memory_data = []
        
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop, 
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        print(f"🔍 开始内存监控，间隔: {interval}秒")
        
    def stop_monitoring(self):
        """停止监控"""
        if not self.monitoring:
            return
            
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2.0)
        print("🛑 内存监控已停止")
        
    def _monitor_loop(self, interval: float):
        """监控循环"""
        while self.monitoring:
            try:
                # 获取系统内存信息
                memory = psutil.virtual_memory()
                
                # 获取Python进程信息
                python_processes = []
                for proc in psutil.process_iter(['pid', 'name', 'memory_info']):
                    try:
                        if 'python' in proc.info['name'].lower():
                            python_processes.append({
                                'pid': proc.info['pid'],
                                'name': proc.info['name'],
                                'memory_mb': proc.info['memory_info'].rss / 1024 / 1024
                            })
                    except (psutil.NoSuchProcess, psutil.AccessDenied):
                        continue
                
                # 记录数据
                data_point = {
                    'timestamp': time.time() - self.start_time,
                    'datetime': datetime.now().strftime('%H:%M:%S'),
                    'system_memory': {
                        'total_gb': memory.total / 1024**3,
                        'used_gb': memory.used / 1024**3,
                        'available_gb': memory.available / 1024**3,
                        'percent': memory.percent
                    },
                    'python_processes': python_processes
                }
                
                self.memory_data.append(data_point)
                
                # 实时输出
                print(f"[{data_point['datetime']}] "
                      f"系统内存: {memory.percent:.1f}% "
                      f"({memory.used/1024**3:.1f}GB/{memory.total/1024**3:.1f}GB), "
                      f"Python进程数: {len(python_processes)}")
                
                time.sleep(interval)
                
            except Exception as e:
                print(f"监控错误: {e}")
                time.sleep(interval)
    
    def get_memory_summary(self) -> Dict[str, Any]:
        """获取内存使用摘要"""
        if not self.memory_data:
            return {}
            
        # 计算统计信息
        memory_percents = [d['system_memory']['percent'] for d in self.memory_data]
        
        return {
            'duration_seconds': self.memory_data[-1]['timestamp'],
            'data_points': len(self.memory_data),
            'memory_usage': {
                'min_percent': min(memory_percents),
                'max_percent': max(memory_percents),
                'avg_percent': sum(memory_percents) / len(memory_percents),
                'final_percent': memory_percents[-1]
            },
            'memory_increase': memory_percents[-1] - memory_percents[0] if len(memory_percents) > 1 else 0
        }
    
    def save_data(self, filename: str):
        """保存监控数据"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump({
                'summary': self.get_memory_summary(),
                'data': self.memory_data
            }, f, indent=2, ensure_ascii=False)
        print(f"📊 监控数据已保存到: {filename}")

def test_audio_processing_memory():
    """测试音频处理的内存使用"""
    monitor = MemoryMonitor()

    try:
        # 开始监控
        monitor.start_monitoring(interval=2.0)

        print("🎵 开始音频处理内存测试...")
        print("测试文件: resource/对话.mp3")

        # 等待一段时间建立基线
        print("📊 建立内存基线...")
        time.sleep(10)

        # 直接测试音频处理核心功能，避免Celery任务的复杂性
        print("🔧 测试音频处理核心功能...")

        # 测试1: 音频加载和预处理
        print("测试1: 音频加载和预处理")
        test_audio_loading()
        time.sleep(5)

        # 测试2: 模型加载（如果可用）
        print("测试2: 模型加载测试")
        test_model_loading()
        time.sleep(5)

        # 测试3: 大量音频数据处理
        print("测试3: 大量音频数据处理")
        test_bulk_audio_processing()
        time.sleep(5)

        # 等待内存稳定
        print("⏳ 等待内存稳定...")
        time.sleep(15)
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 停止监控并保存数据
        monitor.stop_monitoring()
        
        # 保存监控数据
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"memory_debug_{timestamp}.json"
        monitor.save_data(filename)
        
        # 输出摘要
        summary = monitor.get_memory_summary()
        print("\n" + "="*50)
        print("📊 内存使用摘要:")
        print(f"监控时长: {summary.get('duration_seconds', 0):.1f}秒")
        print(f"数据点数: {summary.get('data_points', 0)}")
        
        memory_usage = summary.get('memory_usage', {})
        print(f"内存使用率: {memory_usage.get('min_percent', 0):.1f}% - {memory_usage.get('max_percent', 0):.1f}%")
        print(f"平均使用率: {memory_usage.get('avg_percent', 0):.1f}%")
        print(f"内存增长: {summary.get('memory_increase', 0):.1f}%")
        
        if summary.get('memory_increase', 0) > 5:
            print("⚠️  检测到显著的内存增长，可能存在内存泄漏")
        else:
            print("✅ 内存使用相对稳定")


def test_audio_loading():
    """测试音频加载功能"""
    try:
        import soundfile as sf
        import librosa
        import numpy as np

        audio_file = "resource/对话.mp3"
        if not os.path.exists(audio_file):
            print(f"⚠️ 测试文件不存在: {audio_file}")
            return

        print(f"📁 加载音频文件: {audio_file}")

        # 测试多次加载同一文件（模拟内存泄漏场景）
        for i in range(5):
            try:
                audio, sr = sf.read(audio_file)
                print(f"  第{i+1}次加载: 长度={len(audio)}, 采样率={sr}")

                # 模拟音频处理
                audio_processed = audio * 0.8  # 简单的音量调整

                # 强制删除引用
                del audio, audio_processed

            except Exception as e:
                print(f"  第{i+1}次加载失败: {e}")

        print("✅ 音频加载测试完成")

    except Exception as e:
        print(f"❌ 音频加载测试失败: {e}")


def test_model_loading():
    """测试模型加载功能"""
    try:
        import torch
        import gc

        print("🤖 测试PyTorch模型加载...")

        # 创建一个简单的测试模型来模拟内存使用
        class TestModel(torch.nn.Module):
            def __init__(self):
                super().__init__()
                self.linear = torch.nn.Linear(1000, 1000)
                self.conv = torch.nn.Conv1d(100, 200, 3)

            def forward(self, x):
                return self.linear(x)

        # 测试多次模型创建和销毁
        for i in range(3):
            print(f"  创建测试模型 {i+1}")
            model = TestModel()

            # 模拟推理
            with torch.no_grad():
                test_input = torch.randn(10, 1000)
                output = model(test_input)

            # 清理
            del model, test_input, output
            gc.collect()

            if torch.cuda.is_available():
                torch.cuda.empty_cache()

        print("✅ 模型加载测试完成")

    except Exception as e:
        print(f"❌ 模型加载测试失败: {e}")


def test_bulk_audio_processing():
    """测试大量音频数据处理"""
    try:
        import numpy as np

        print("📊 测试大量音频数据处理...")

        # 创建大量音频数据来测试内存使用
        sample_rate = 16000
        duration = 10  # 10秒音频

        for i in range(3):
            print(f"  处理音频数据块 {i+1}")

            # 生成大量音频数据
            audio_data = np.random.randn(sample_rate * duration).astype(np.float32)

            # 模拟音频处理操作
            # 1. 音频标准化
            normalized = audio_data / np.max(np.abs(audio_data))

            # 2. 简单滤波
            filtered = np.convolve(normalized, np.ones(100)/100, mode='same')

            # 3. 特征提取（模拟）
            features = np.abs(np.fft.fft(filtered[:8192]))

            print(f"    数据大小: {audio_data.nbytes / 1024 / 1024:.1f}MB")

            # 强制清理
            del audio_data, normalized, filtered, features

        print("✅ 大量音频数据处理测试完成")

    except Exception as e:
        print(f"❌ 大量音频数据处理测试失败: {e}")


if __name__ == "__main__":
    test_audio_processing_memory()
