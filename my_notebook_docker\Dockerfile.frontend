# ===========================================
# 语音处理智能平台 - 前端服务 Dockerfile
# 多阶段构建：构建阶段 + 生产阶段
# ===========================================

# ===========================================
# 构建阶段 - Node.js环境
# ===========================================
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装构建依赖
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    git

# 复制package文件
COPY frontend/package*.json ./

# 设置npm镜像源（提高下载速度）
RUN npm config set registry https://registry.npmmirror.com

# 安装依赖（包括开发依赖，因为需要构建）
RUN npm install --silent

# 复制源代码（排除node_modules）
COPY frontend/src ./src
COPY frontend/public ./public
COPY frontend/index.html ./
COPY frontend/vite.config.js ./
# 注意：此项目使用JavaScript，不需要TypeScript配置文件

# 构建应用
RUN npm run build

# 验证构建结果
RUN ls -la dist/ && \
    echo "Frontend build completed successfully"

# ===========================================
# 生产阶段 - Nginx服务器
# ===========================================
FROM nginx:1.25-alpine

# 安装必要工具
RUN apk add --no-cache \
    curl \
    bash \
    tzdata

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建nginx运行所需的目录
RUN mkdir -p /var/cache/nginx/client_temp \
    /var/cache/nginx/proxy_temp \
    /var/cache/nginx/fastcgi_temp \
    /var/cache/nginx/uwsgi_temp \
    /var/cache/nginx/scgi_temp \
    /var/log/nginx \
    /etc/nginx/conf.d

# 复制构建产物到nginx目录
COPY --from=builder /app/dist /usr/share/nginx/html

# 复制nginx配置文件
COPY my_notebook_docker/nginx.conf /etc/nginx/nginx.conf

# 创建健康检查脚本
RUN echo '#!/bin/bash\n\
curl -f http://localhost/health || exit 1' > /usr/local/bin/health-check.sh && \
    chmod +x /usr/local/bin/health-check.sh

# 设置权限
RUN chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /usr/share/nginx/html && \
    chown -R nginx:nginx /var/log/nginx && \
    chmod -R 755 /usr/share/nginx/html

# 创建非特权用户运行nginx
RUN addgroup -g 1001 -S appgroup && \
    adduser -S -D -H -u 1001 -h /var/cache/nginx -s /sbin/nologin -G appgroup -g appgroup appuser

# 暴露端口
EXPOSE 80

# 添加标签
LABEL maintainer="Speech Platform Team" \
      version="1.0.0" \
      description="语音处理智能平台前端服务" \
      service="frontend"

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD /usr/local/bin/health-check.sh

# 启动nginx（前台运行）
CMD ["nginx", "-g", "daemon off;"]
