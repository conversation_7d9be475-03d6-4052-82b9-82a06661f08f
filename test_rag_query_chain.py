#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
RAG查询链路测试脚本
测试完整的RAG查询流程并诊断嵌入模型配置
"""

import os
import sys
import asyncio
from datetime import datetime

# 添加backend路径
sys.path.append('./backend')

def test_rag_service_config():
    """测试RAG服务配置"""
    print("=" * 60)
    print("RAG查询链路诊断报告")
    print("=" * 60)
    print(f"诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    try:
        # 导入RAG服务
        from backend.services.rag_service import rag_service
        
        print("🔧 RAG服务配置检查:")
        print(f"   嵌入模型: {rag_service.config.get('embedding_model', '未配置')}")
        print(f"   API基础URL: {rag_service.config.get('api_base_url', '未配置')}")
        print(f"   LLM模型: {rag_service.config.get('api_model', '未配置')}")
        print(f"   向量数据库路径: {rag_service.persist_directory}")
        print(f"   集合名称: {rag_service.collection_name}")
        
        # 检查服务初始化状态
        print(f"\n📊 服务状态:")
        print(f"   ChromaDB客户端: {'✅ 已初始化' if rag_service.chroma_client else '❌ 未初始化'}")
        print(f"   集合: {'✅ 已创建' if rag_service.collection else '❌ 未创建'}")
        print(f"   向量存储: {'✅ 已创建' if rag_service.vector_store else '❌ 未创建'}")
        print(f"   嵌入模型: {'✅ 已加载' if rag_service.embedding_model else '❌ 未加载'}")
        print(f"   查询引擎: {'✅ 已创建' if rag_service.query_engine else '❌ 未创建'}")
        
        return True
        
    except Exception as e:
        print(f"❌ RAG服务配置检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_embedding_model():
    """测试嵌入模型"""
    print(f"\n🧪 嵌入模型测试:")
    
    try:
        from backend.services.rag_service import rag_service
        
        if not rag_service.embedding_model:
            print("   ❌ 嵌入模型未加载")
            return False
        
        # 测试嵌入生成
        test_text = "这是一个测试文本"
        print(f"   测试文本: '{test_text}'")
        
        # 创建事件循环来运行异步代码
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # 生成嵌入向量
            embedding = loop.run_until_complete(
                rag_service.embedding_model.aget_text_embedding(test_text)
            )
            
            if embedding:
                print(f"   ✅ 嵌入生成成功")
                print(f"   向量维度: {len(embedding)}")
                print(f"   向量范围: [{min(embedding):.6f}, {max(embedding):.6f}]")
                
                # 检查向量质量
                import statistics
                mean_val = statistics.mean(embedding)
                std_val = statistics.stdev(embedding)
                print(f"   向量均值: {mean_val:.6f}")
                print(f"   向量标准差: {std_val:.6f}")
                
                return len(embedding)  # 返回维度
            else:
                print("   ❌ 嵌入生成失败")
                return False
                
        finally:
            loop.close()
        
    except Exception as e:
        print(f"   ❌ 嵌入模型测试失败: {e}")
        return False

def test_collection_embedding_dimension():
    """测试集合中存储的向量维度"""
    print(f"\n📏 存储向量维度检查:")
    
    try:
        import chromadb
        
        # 直接连接ChromaDB
        db_path = "./data/chroma_db"
        collection_name = "knowledge_base"
        
        client = chromadb.PersistentClient(path=db_path)
        collection = client.get_collection(name=collection_name)
        
        # 获取样本向量
        results = collection.peek(limit=1)
        
        if results and results.get('embeddings') and results['embeddings'][0]:
            stored_embedding = results['embeddings'][0]
            stored_dimension = len(stored_embedding)
            
            print(f"   ✅ 存储向量维度: {stored_dimension}")
            print(f"   向量范围: [{min(stored_embedding):.6f}, {max(stored_embedding):.6f}]")
            
            return stored_dimension
        else:
            print("   ❌ 无法获取存储的向量")
            return None
            
    except Exception as e:
        print(f"   ❌ 存储向量检查失败: {e}")
        return None

def diagnose_dimension_mismatch():
    """诊断维度不匹配问题"""
    print(f"\n🔍 维度不匹配诊断:")
    
    # 获取当前嵌入模型维度
    current_dimension = test_embedding_model()
    
    # 获取存储的向量维度
    stored_dimension = test_collection_embedding_dimension()
    
    if current_dimension and stored_dimension:
        print(f"\n📊 维度对比:")
        print(f"   当前嵌入模型维度: {current_dimension}")
        print(f"   存储向量维度: {stored_dimension}")
        
        if current_dimension == stored_dimension:
            print(f"   ✅ 维度匹配")
            return True
        else:
            print(f"   ❌ 维度不匹配！")
            print(f"   差异: {abs(current_dimension - stored_dimension)}")
            
            # 提供解决方案
            print(f"\n💡 解决方案:")
            if current_dimension < stored_dimension:
                print(f"   1. 更换为768维的嵌入模型")
                print(f"   2. 或者重新向量化所有文档使用384维模型")
            else:
                print(f"   1. 更换为384维的嵌入模型")
                print(f"   2. 或者重新向量化所有文档使用{current_dimension}维模型")
            
            return False
    else:
        print(f"   ❌ 无法获取维度信息进行对比")
        return False

def check_embedding_model_config():
    """检查嵌入模型配置"""
    print(f"\n⚙️ 嵌入模型配置检查:")
    
    try:
        # 检查配置文件
        from backend.config.models.vector_store import get_embedding_settings
        
        embedding_config = get_embedding_settings()
        print(f"   配置文件中的模型: {embedding_config.get('model_name', '未配置')}")
        print(f"   基础URL: {embedding_config.get('base_url', '未配置')}")
        
        # 检查RAG服务实际使用的配置
        from backend.services.rag_service import rag_service
        
        actual_model = rag_service.config.get('embedding_model', '未知')
        print(f"   RAG服务使用的模型: {actual_model}")
        
        # 分析模型名称和预期维度
        model_dimensions = {
            'nomic-embed-text': 768,
            'nomic-embed-text:latest': 768,
            'bge-small-en-v1.5': 384,
            'bge-base-en-v1.5': 768,
            'text-embedding-ada-002': 1536,
        }
        
        expected_dim = None
        for model_name, dim in model_dimensions.items():
            if model_name in actual_model:
                expected_dim = dim
                break
        
        if expected_dim:
            print(f"   预期维度: {expected_dim}")
        else:
            print(f"   预期维度: 未知模型")
        
        return expected_dim
        
    except Exception as e:
        print(f"   ❌ 配置检查失败: {e}")
        return None

async def test_rag_initialization():
    """测试RAG服务初始化"""
    print(f"\n🚀 RAG服务初始化测试:")
    
    try:
        from backend.services.rag_service import rag_service
        
        # 尝试初始化RAG服务
        print("   正在初始化RAG服务...")
        success = await rag_service.initialize()
        
        if success:
            print("   ✅ RAG服务初始化成功")
            
            # 获取集合统计信息
            stats = await rag_service.get_collection_stats()
            print(f"   集合统计: {stats}")
            
            return True
        else:
            print("   ❌ RAG服务初始化失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 初始化测试失败: {e}")
        return False

def main():
    """主函数"""
    try:
        # 检查RAG服务配置
        config_ok = test_rag_service_config()
        
        if not config_ok:
            print("\n❌ RAG服务配置检查失败，无法继续")
            return False
        
        # 检查嵌入模型配置
        expected_dim = check_embedding_model_config()
        
        # 诊断维度不匹配
        dimension_ok = diagnose_dimension_mismatch()
        
        # 测试RAG初始化
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            init_ok = loop.run_until_complete(test_rag_initialization())
        finally:
            loop.close()
        
        print("\n" + "=" * 60)
        print("RAG查询链路诊断完成")
        print("=" * 60)
        
        # 总结
        print(f"\n📋 诊断总结:")
        print(f"   配置检查: {'✅ 通过' if config_ok else '❌ 失败'}")
        print(f"   维度匹配: {'✅ 通过' if dimension_ok else '❌ 失败'}")
        print(f"   服务初始化: {'✅ 通过' if init_ok else '❌ 失败'}")
        
        if not dimension_ok:
            print(f"\n🎯 主要问题: 嵌入模型维度不匹配")
            print(f"   这是导致RAG检索失败的根本原因")
        
        return config_ok and dimension_ok and init_ok
        
    except Exception as e:
        print(f"\n❌ 诊断过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
