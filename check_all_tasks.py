#!/usr/bin/env python3
"""
检查所有相关的任务记录
"""

import sqlite3
from datetime import datetime

def check_all_tasks():
    print(f"=== 检查所有任务记录 ===")
    print(f"检查时间: {datetime.now()}")
    print()
    
    try:
        conn = sqlite3.connect('data/speech_platform.db')
        cursor = conn.cursor()
        
        # 1. 检查所有会议转录任务
        print("1. 所有会议转录任务:")
        cursor.execute("""
            SELECT task_id, status, progress_percentage, progress_detail, created_at, updated_at
            FROM task_records 
            WHERE task_id LIKE 'meeting_transcription_%'
            ORDER BY created_at DESC
            LIMIT 10
        """)
        
        tasks = cursor.fetchall()
        for task in tasks:
            print(f"   任务ID: {task[0]}")
            print(f"   状态: {task[1]} | 进度: {task[2]}% | 详情: {task[3]}")
            print(f"   创建: {task[4]} | 更新: {task[5]}")
            print()
            
        # 2. 检查所有音频文件
        print("2. 所有音频文件:")
        cursor.execute("""
            SELECT id, filename, status, created_at, updated_at
            FROM audio_files 
            ORDER BY created_at DESC
            LIMIT 5
        """)
        
        files = cursor.fetchall()
        for file in files:
            print(f"   文件ID: {file[0]} | 文件名: {file[1]} | 状态: {file[2]}")
            print(f"   创建: {file[3]} | 更新: {file[4]}")
            print()
            
        # 3. 检查STARTED状态的任务
        print("3. STARTED状态的任务:")
        cursor.execute("""
            SELECT task_id, status, progress_percentage, progress_detail, created_at, updated_at
            FROM task_records 
            WHERE status = 'STARTED'
            ORDER BY created_at DESC
        """)
        
        started_tasks = cursor.fetchall()
        if started_tasks:
            for task in started_tasks:
                print(f"   任务ID: {task[0]}")
                print(f"   状态: {task[1]} | 进度: {task[2]}% | 详情: {task[3]}")
                print(f"   创建: {task[4]} | 更新: {task[5]}")
                print()
        else:
            print("   ✅ 没有STARTED状态的任务")
            
        conn.close()
        
    except Exception as e:
        print(f"   ❌ 数据库连接失败: {e}")

if __name__ == "__main__":
    check_all_tasks()
