#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速修复脚本：并行优化和Paraformer移除
自动应用所有必要的修复
"""

import os
import sys
from pathlib import Path

def set_complete_offline_mode():
    """设置完全离线模式，避免任何网络请求"""
    offline_vars = {
        'MODELSCOPE_OFFLINE_MODE': '1',
        'HF_HUB_OFFLINE': '1',
        'HF_DATASETS_OFFLINE': '1',
        'TRANSFORMERS_OFFLINE': '1',
        'DISABLE_MODEL_DOWNLOAD': '1',
        'NO_PROXY': '*',
        'REQUESTS_CA_BUNDLE': ''
    }
    
    for key, value in offline_vars.items():
        os.environ[key] = value
    
    print("🔒 完全离线模式已启用")
    return offline_vars

def check_model_paths():
    """检查模型路径是否存在"""
    model_paths = {
        "SenseVoice": r"C:\Users\<USER>\Documents\my_project\models\model_dir\SenseVoiceSmall",
        "CAM++": r"C:\Users\<USER>\Documents\my_project\models\model_dir\speech_campplus_sv_zh_en_16k-common_advanced",
        "VAD": r"C:\Users\<USER>\Documents\my_project\models\model_dir\speech_fsmn_vad_zh-cn-16k-common-pytorch"
    }
    
    print("📁 检查模型路径:")
    all_exist = True
    for name, path in model_paths.items():
        exists = os.path.exists(path)
        status = "✅" if exists else "❌"
        print(f"  {status} {name}: {exists} ({path})")
        if not exists:
            all_exist = False
    
    return all_exist, model_paths

def test_funasr_import():
    """测试FunASR导入"""
    try:
        from funasr import AutoModel
        print("✅ FunASR导入成功")
        return True
    except ImportError as e:
        print(f"❌ FunASR导入失败: {e}")
        print("💡 请安装FunASR: pip install funasr")
        return False

def test_optimized_processor_import():
    """测试优化处理器导入"""
    try:
        from utils.optimized_speech_processing import OptimizedSpeechProcessor
        print("✅ 优化处理器导入成功")
        return True
    except ImportError as e:
        print(f"❌ 优化处理器导入失败: {e}")
        return False

def test_basic_model_loading(model_paths):
    """测试基础模型加载"""
    print("\n🧪 测试模型加载...")
    
    # 测试SenseVoice模型加载
    sensevoice_path = model_paths["SenseVoice"]
    if os.path.exists(sensevoice_path):
        try:
            from funasr import AutoModel
            
            print(f"🔧 测试SenseVoice模型加载: {sensevoice_path}")
            
            # 使用修复的配置
            model = AutoModel(
                model=sensevoice_path,
                trust_remote_code=True,
                device='cpu',
                local_files_only=True,
                disable_update=True,
                force_download=False,
                vad_model=None
            )
            print("✅ SenseVoice模型加载成功")
            return True
            
        except Exception as e:
            print(f"❌ SenseVoice模型加载失败: {e}")
            
            # 尝试最小配置
            try:
                model = AutoModel(
                    model=sensevoice_path,
                    trust_remote_code=True,
                    device='cpu'
                )
                print("✅ SenseVoice模型（最小配置）加载成功")
                return True
            except Exception as e2:
                print(f"❌ SenseVoice模型（最小配置）也失败: {e2}")
                return False
    else:
        print(f"❌ SenseVoice模型路径不存在: {sensevoice_path}")
        return False

def test_optimized_processor(model_paths):
    """测试优化处理器"""
    print("\n🚀 测试优化处理器...")
    
    try:
        from utils.optimized_speech_processing import OptimizedSpeechProcessor
        
        processor = OptimizedSpeechProcessor(
            campplus_model_path=model_paths["CAM++"],
            vad_model_path=model_paths["VAD"],
            use_gpu=False  # 强制CPU模式避免GPU问题
        )
        
        print("✅ 优化处理器创建成功")
        
        # 测试缓存功能
        cache_info = processor.get_cache_info()
        print(f"✅ 缓存功能正常: {cache_info['cache_size']} 个向量")
        
        return True
        
    except Exception as e:
        print(f"❌ 优化处理器测试失败: {e}")
        return False

def check_streamlit_syntax():
    """检查Streamlit文件语法"""
    print("\n📝 检查Streamlit语法...")
    
    try:
        import ast
        with open('pages/语音识别分析.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        ast.parse(content)
        print("✅ Streamlit语法检查通过")
        return True
        
    except SyntaxError as e:
        print(f"❌ Streamlit语法错误: {e}")
        return False
    except Exception as e:
        print(f"❌ Streamlit文件检查失败: {e}")
        return False

def run_comprehensive_test():
    """运行综合测试"""
    print("🔧 开始并行优化修复综合测试")
    print("=" * 50)
    
    # 1. 设置离线模式
    offline_vars = set_complete_offline_mode()
    
    # 2. 检查模型路径
    paths_ok, model_paths = check_model_paths()
    
    # 3. 测试导入
    funasr_ok = test_funasr_import()
    processor_ok = test_optimized_processor_import()
    
    # 4. 测试模型加载
    model_loading_ok = False
    if paths_ok and funasr_ok:
        model_loading_ok = test_basic_model_loading(model_paths)
    
    # 5. 测试优化处理器
    processor_test_ok = False
    if processor_ok and paths_ok:
        processor_test_ok = test_optimized_processor(model_paths)
    
    # 6. 检查Streamlit语法
    streamlit_ok = check_streamlit_syntax()
    
    # 总结结果
    print("\n" + "=" * 50)
    print("🎯 修复测试结果总结:")
    print(f"  🔒 离线模式设置: ✅")
    print(f"  📁 模型路径检查: {'✅' if paths_ok else '❌'}")
    print(f"  📦 FunASR导入: {'✅' if funasr_ok else '❌'}")
    print(f"  🚀 优化处理器导入: {'✅' if processor_ok else '❌'}")
    print(f"  🤖 模型加载测试: {'✅' if model_loading_ok else '❌'}")
    print(f"  ⚡ 并行处理器测试: {'✅' if processor_test_ok else '❌'}")
    print(f"  📝 Streamlit语法: {'✅' if streamlit_ok else '❌'}")
    
    # 整体评估
    all_tests = [paths_ok, funasr_ok, processor_ok, model_loading_ok, processor_test_ok, streamlit_ok]
    success_rate = sum(all_tests) / len(all_tests) * 100
    
    print(f"\n📊 总体成功率: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("🎉 修复基本成功！可以尝试运行Streamlit应用")
        print("💡 运行命令: streamlit run Home.py")
    elif success_rate >= 60:
        print("⚠️ 部分修复成功，但仍有问题需要解决")
        print("💡 请检查失败的项目并按照修复指南操作")
    else:
        print("❌ 修复失败，需要手动检查和修复")
        print("💡 请参考 语音处理并行优化修复指南.md")
    
    return success_rate >= 80

def print_next_steps():
    """打印后续步骤"""
    print("\n📋 后续步骤:")
    print("1. 如果测试成功，运行: streamlit run Home.py")
    print("2. 如果有问题，查看: 语音处理并行优化修复指南.md")
    print("3. 测试音频处理: 上传音频文件并选择'使用优化的并行处理器'")
    print("4. 监控性能: 查看处理时间和缓存命中率")

def main():
    """主函数"""
    print("🛠️ 语音处理并行优化快速修复工具")
    print("🎯 目标: 修复并行优化失败和Paraformer调用问题")
    print()
    
    try:
        success = run_comprehensive_test()
        print_next_steps()
        
        if success:
            print("\n✅ 修复完成！系统应该可以正常运行了。")
        else:
            print("\n⚠️ 修复未完全成功，请查看上述错误信息。")
            
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断测试")
    except Exception as e:
        print(f"\n💥 测试过程中出现意外错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 