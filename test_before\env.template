# 环境变量配置文件模板
# 复制此文件为 .env 并修改为实际配置

# ============================================
# Ollama服务配置
# ============================================

# 方式1: 使用宿主机IP（推荐）
# 获取宿主机IP: ipconfig（Windows）或 ifconfig（Linux/Mac）
# 示例：OLLAMA_BASE_URL=http://*************:11434
OLLAMA_BASE_URL=http://***********:11434

# 方式2: 使用Docker内部网络（如果方式1不工作）
# OLLAMA_BASE_URL=http://host.docker.internal:11434

# 方式3: 使用localhost（仅适用于host网络模式）
# OLLAMA_BASE_URL=http://localhost:11434

# ============================================
# 容器配置
# ============================================

# Streamlit端口配置
STREAMLIT_PORT=8501

# Python配置
PYTHONUNBUFFERED=1

# ============================================
# 说明
# ============================================

# 使用方法：
# 1. 复制此文件为 .env
# 2. 修改 OLLAMA_BASE_URL 为你的实际宿主机IP
# 3. 运行: docker-compose up -d

# 获取宿主机IP方法：
# Windows: ipconfig | findstr "IPv4"
# Linux/Mac: ip addr show | grep inet | grep -v 127.0.0.1 