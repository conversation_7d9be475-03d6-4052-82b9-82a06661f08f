#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强音频处理器
优化的音频处理工具，去除Streamlit依赖，添加更好的错误处理和性能优化
"""

import os
import tempfile
import logging
import numpy as np
import soundfile as sf
import librosa
from pathlib import Path
from typing import Tuple, Optional, Dict, List, Union, Any
from dataclasses import dataclass
from enum import Enum

# 配置日志
logger = logging.getLogger(__name__)

# 音频处理相关库
try:
    import scipy.signal as signal
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    logger.warning("Scipy不可用，某些高级音频处理功能将被禁用")

try:
    from pydub import AudioSegment
    PYDUB_AVAILABLE = True
except ImportError:
    PYDUB_AVAILABLE = False
    logger.warning("Pydub不可用，某些音频格式转换功能将被禁用")

# 支持的音频格式
SUPPORTED_AUDIO_FORMATS = {
    '.wav', '.mp3', '.m4a', '.aac', '.flac', '.ogg', '.wma', '.aiff', '.au'
}

# 默认音频参数
DEFAULT_SAMPLE_RATE = 16000
DEFAULT_CHANNELS = 1
DEFAULT_BIT_DEPTH = 16

class ProcessingMethod(Enum):
    """处理方法枚举"""
    SPECTRAL_GATING = "spectral_gating"
    WIENER = "wiener"
    SIMPLE = "simple"

class NormalizationMethod(Enum):
    """标准化方法枚举"""
    PEAK = "peak"
    RMS = "rms"
    LUFS = "lufs"

@dataclass
class AudioInfo:
    """音频信息数据类"""
    file_path: str
    sample_rate: int
    channels: int
    duration: float
    frames: int
    format: str
    file_size: int
    is_valid: bool = True
    error: Optional[str] = None

@dataclass
class ProcessingConfig:
    """音频处理配置"""
    target_sample_rate: int = DEFAULT_SAMPLE_RATE
    target_channels: int = DEFAULT_CHANNELS
    normalize: bool = True
    denoise: bool = True
    target_db: float = -20.0
    normalization_method: NormalizationMethod = NormalizationMethod.PEAK
    denoise_method: ProcessingMethod = ProcessingMethod.SPECTRAL_GATING

class AudioProcessingError(Exception):
    """音频处理异常"""
    pass

class EnhancedAudioProcessor:
    """增强音频处理器"""
    
    def __init__(self, config: Optional[ProcessingConfig] = None):
        """
        初始化音频处理器
        
        Args:
            config: 处理配置
        """
        self.config = config or ProcessingConfig()
        self.temp_files = []
        
    def __del__(self):
        """清理临时文件"""
        self.cleanup_temp_files()
    
    def cleanup_temp_files(self):
        """清理临时文件"""
        for temp_file in self.temp_files:
            try:
                if os.path.exists(temp_file):
                    os.unlink(temp_file)
            except Exception as e:
                logger.warning(f"清理临时文件失败: {temp_file}, 错误: {e}")
        self.temp_files.clear()
    
    def get_audio_info(self, file_path: str) -> AudioInfo:
        """
        获取音频文件信息
        
        Args:
            file_path: 音频文件路径
            
        Returns:
            AudioInfo: 音频信息对象
        """
        try:
            if not os.path.exists(file_path):
                return AudioInfo(
                    file_path=file_path,
                    sample_rate=0, channels=0, duration=0, frames=0,
                    format="", file_size=0, is_valid=False,
                    error="文件不存在"
                )
            
            file_ext = Path(file_path).suffix.lower()
            if file_ext not in SUPPORTED_AUDIO_FORMATS:
                return AudioInfo(
                    file_path=file_path,
                    sample_rate=0, channels=0, duration=0, frames=0,
                    format=file_ext, file_size=os.path.getsize(file_path),
                    is_valid=False, error=f"不支持的音频格式: {file_ext}"
                )
            
            # 获取音频信息
            info = sf.info(file_path)
            file_size = os.path.getsize(file_path)
            
            return AudioInfo(
                file_path=file_path,
                sample_rate=info.samplerate,
                channels=info.channels,
                duration=info.duration,
                frames=info.frames,
                format=info.format,
                file_size=file_size,
                is_valid=True
            )
            
        except Exception as e:
            logger.error(f"获取音频信息失败: {file_path}, 错误: {e}")
            return AudioInfo(
                file_path=file_path,
                sample_rate=0, channels=0, duration=0, frames=0,
                format="", file_size=0, is_valid=False,
                error=str(e)
            )
    
    def load_audio(self, file_path: str, target_sr: Optional[int] = None) -> Tuple[Optional[np.ndarray], Optional[int]]:
        """
        加载音频文件
        
        Args:
            file_path: 音频文件路径
            target_sr: 目标采样率
            
        Returns:
            Tuple[audio_data, sample_rate]: 音频数据和采样率
        """
        try:
            target_sr = target_sr or self.config.target_sample_rate
            
            # 检查文件
            audio_info = self.get_audio_info(file_path)
            if not audio_info.is_valid:
                raise AudioProcessingError(f"无效的音频文件: {audio_info.error}")
            
            # 加载音频
            audio, sr = sf.read(file_path)
            logger.info(f"成功加载音频: {file_path}, 采样率: {sr}Hz, 时长: {len(audio)/sr:.2f}s")
            
            # 转换为单声道
            if len(audio.shape) > 1 and audio.shape[1] > 1:
                audio = np.mean(audio, axis=1)
                logger.info("多声道音频已转换为单声道")
            
            # 重采样
            if sr != target_sr:
                audio = self._resample_audio(audio, sr, target_sr)
                sr = target_sr
                logger.info(f"音频已重采样到 {target_sr}Hz")
            
            return audio, sr
            
        except Exception as e:
            logger.error(f"加载音频失败: {file_path}, 错误: {e}")
            return None, None
    
    def _resample_audio(self, audio: np.ndarray, orig_sr: int, target_sr: int) -> np.ndarray:
        """重采样音频"""
        try:
            if orig_sr == target_sr:
                return audio
            
            # 使用librosa进行重采样
            resampled = librosa.resample(audio, orig_sr=orig_sr, target_sr=target_sr)
            logger.debug(f"重采样完成: {orig_sr}Hz -> {target_sr}Hz")
            return resampled
            
        except Exception as e:
            logger.error(f"重采样失败: {e}")
            # 备用方案：使用scipy
            if SCIPY_AVAILABLE:
                try:
                    num_samples = int(len(audio) * target_sr / orig_sr)
                    resampled = signal.resample(audio, num_samples)
                    logger.info("使用scipy重采样成功")
                    return resampled
                except Exception as e2:
                    logger.error(f"scipy重采样也失败: {e2}")
            
            return audio
    
    def normalize_audio(self, audio: np.ndarray, target_db: float = -20.0, 
                       method: NormalizationMethod = NormalizationMethod.PEAK) -> np.ndarray:
        """
        音频标准化
        
        Args:
            audio: 音频数据
            target_db: 目标音量(dB)
            method: 标准化方法
            
        Returns:
            标准化后的音频数据
        """
        try:
            if len(audio) == 0:
                return audio
            
            if method == NormalizationMethod.PEAK:
                # 峰值标准化
                peak = np.max(np.abs(audio))
                if peak > 0:
                    target_amplitude = 10 ** (target_db / 20.0)
                    normalized = audio * (target_amplitude / peak)
                else:
                    normalized = audio
                    
            elif method == NormalizationMethod.RMS:
                # RMS标准化 - numpy 2.x兼容
                audio_squared = np.power(audio, 2)
                rms = np.sqrt(np.mean(audio_squared))
                if rms > 0:
                    target_rms = 10 ** (target_db / 20.0)
                    normalized = audio * (target_rms / rms)
                else:
                    normalized = audio
            else:
                # 默认峰值标准化
                peak = np.max(np.abs(audio))
                if peak > 0:
                    target_amplitude = 10 ** (target_db / 20.0)
                    normalized = audio * (target_amplitude / peak)
                else:
                    normalized = audio
            
            # 防止削波
            normalized = np.clip(normalized, -1.0, 1.0)
            logger.debug(f"音频标准化完成: 方法={method.value}, 目标={target_db}dB")
            return normalized
            
        except Exception as e:
            logger.error(f"音频标准化失败: {e}")
            return audio
    
    def denoise_audio(self, audio: np.ndarray, sr: int, 
                     method: ProcessingMethod = ProcessingMethod.SPECTRAL_GATING) -> np.ndarray:
        """
        音频降噪
        
        Args:
            audio: 音频数据
            sr: 采样率
            method: 降噪方法
            
        Returns:
            降噪后的音频数据
        """
        try:
            if len(audio) == 0:
                return audio
            
            if method == ProcessingMethod.SPECTRAL_GATING:
                return self._spectral_gating_denoise(audio, sr)
            elif method == ProcessingMethod.WIENER:
                return self._wiener_denoise(audio, sr)
            else:
                return self._simple_denoise(audio, sr)
                
        except Exception as e:
            logger.error(f"音频降噪失败: {e}")
            return audio
    
    def _spectral_gating_denoise(self, audio: np.ndarray, sr: int) -> np.ndarray:
        """频谱门控降噪"""
        try:
            # STFT分析
            stft = librosa.stft(audio)
            magnitude = np.abs(stft)
            phase = np.angle(stft)
            
            # 估计噪声水平
            noise_frames = min(int(0.5 * sr / 512), magnitude.shape[1] // 4)
            if noise_frames > 0:
                noise_profile = np.mean(magnitude[:, :noise_frames], axis=1, keepdims=True)
            else:
                noise_profile = np.percentile(magnitude, 10, axis=1, keepdims=True)
            
            # 计算信噪比并应用软门控
            snr = magnitude / (noise_profile + 1e-10)
            alpha = 2.0
            gate = 1.0 / (1.0 + np.exp(-alpha * (snr - 1.5)))
            
            # 重构音频
            denoised_magnitude = magnitude * gate
            denoised_stft = denoised_magnitude * np.exp(1j * phase)
            denoised = librosa.istft(denoised_stft)
            
            logger.debug("频谱门控降噪完成")
            return denoised
            
        except Exception as e:
            logger.warning(f"频谱门控降噪失败，使用简单降噪: {e}")
            return self._simple_denoise(audio, sr)
    
    def _wiener_denoise(self, audio: np.ndarray, sr: int) -> np.ndarray:
        """维纳滤波降噪"""
        try:
            if not SCIPY_AVAILABLE:
                return self._simple_denoise(audio, sr)
            
            # STFT分析
            nperseg = min(1024, len(audio) // 4)
            f, t, stft = signal.stft(audio, sr, nperseg=nperseg)
            
            # 估计噪声功率谱密度
            noise_psd = np.mean(np.abs(stft) ** 2, axis=1, keepdims=True)
            
            # 维纳滤波
            signal_psd = np.abs(stft) ** 2
            wiener_filter = signal_psd / (signal_psd + noise_psd)
            filtered_stft = stft * wiener_filter
            
            # 重构信号
            _, denoised = signal.istft(filtered_stft, sr, nperseg=nperseg)
            
            logger.debug("维纳滤波降噪完成")
            return denoised
            
        except Exception as e:
            logger.warning(f"维纳滤波降噪失败，使用简单降噪: {e}")
            return self._simple_denoise(audio, sr)
    
    def _simple_denoise(self, audio: np.ndarray, sr: int) -> np.ndarray:
        """简单降噪（高通滤波）"""
        try:
            if not SCIPY_AVAILABLE:
                return audio
            
            # 高通滤波器
            cutoff = 80  # 80Hz
            nyquist = sr / 2
            normalized_cutoff = cutoff / nyquist
            
            if normalized_cutoff >= 1.0:
                return audio
            
            b, a = signal.butter(4, normalized_cutoff, btype='high')
            filtered = signal.filtfilt(b, a, audio)
            
            logger.debug("简单降噪完成")
            return filtered
            
        except Exception as e:
            logger.warning(f"简单降噪失败: {e}")
            return audio

    def process_audio(self, input_path: str, output_path: Optional[str] = None,
                     config: Optional[ProcessingConfig] = None) -> Optional[str]:
        """
        完整的音频处理流程

        Args:
            input_path: 输入音频文件路径
            output_path: 输出音频文件路径（可选）
            config: 处理配置（可选）

        Returns:
            处理后的音频文件路径
        """
        try:
            processing_config = config or self.config

            # 加载音频
            audio, sr = self.load_audio(input_path, processing_config.target_sample_rate)
            if audio is None:
                raise AudioProcessingError("音频加载失败")

            # 降噪处理
            if processing_config.denoise:
                audio = self.denoise_audio(audio, sr, processing_config.denoise_method)

            # 音量标准化
            if processing_config.normalize:
                audio = self.normalize_audio(
                    audio,
                    processing_config.target_db,
                    processing_config.normalization_method
                )

            # 确定输出路径
            if output_path is None:
                with tempfile.NamedTemporaryFile(suffix='.wav', delete=False) as tmp:
                    output_path = tmp.name
                self.temp_files.append(output_path)

            # 保存处理后的音频
            sf.write(output_path, audio, sr)

            logger.info(f"音频处理完成: {input_path} -> {output_path}")
            return output_path

        except Exception as e:
            logger.error(f"音频处理失败: {e}")
            return None

    def batch_process(self, input_files: List[str], output_dir: Optional[str] = None,
                     config: Optional[ProcessingConfig] = None) -> Dict[str, Any]:
        """
        批量音频处理

        Args:
            input_files: 输入文件列表
            output_dir: 输出目录（可选）
            config: 处理配置（可选）

        Returns:
            处理结果字典
        """
        results = {
            'successful': [],
            'failed': [],
            'total_count': len(input_files),
            'success_count': 0,
            'error_count': 0
        }

        if output_dir:
            os.makedirs(output_dir, exist_ok=True)

        for input_file in input_files:
            try:
                # 确定输出文件路径
                if output_dir:
                    filename = Path(input_file).stem + '_processed.wav'
                    output_path = os.path.join(output_dir, filename)
                else:
                    output_path = None

                # 处理音频
                processed_path = self.process_audio(input_file, output_path, config)

                if processed_path:
                    results['successful'].append({
                        'input_file': input_file,
                        'output_file': processed_path,
                        'status': 'success'
                    })
                    results['success_count'] += 1
                else:
                    results['failed'].append({
                        'input_file': input_file,
                        'error': '处理失败',
                        'status': 'failed'
                    })
                    results['error_count'] += 1

            except Exception as e:
                logger.error(f"批量处理文件失败: {input_file}, 错误: {e}")
                results['failed'].append({
                    'input_file': input_file,
                    'error': str(e),
                    'status': 'failed'
                })
                results['error_count'] += 1

        logger.info(f"批量处理完成: 成功 {results['success_count']}, 失败 {results['error_count']}")
        return results

    def convert_format(self, input_path: str, output_path: str,
                      target_format: str = 'wav') -> bool:
        """
        转换音频格式

        Args:
            input_path: 输入文件路径
            output_path: 输出文件路径
            target_format: 目标格式

        Returns:
            转换是否成功
        """
        try:
            # 加载音频
            audio, sr = self.load_audio(input_path)
            if audio is None:
                return False

            # 保存为目标格式
            if target_format.lower() == 'wav':
                sf.write(output_path, audio, sr)
            elif PYDUB_AVAILABLE:
                # 使用pydub进行格式转换 - numpy 2.x兼容
                audio_scaled = audio * 32767
                audio_int16 = audio_scaled.astype(np.int16, copy=False)
                audio_segment = AudioSegment(
                    audio_int16.tobytes(),
                    frame_rate=sr,
                    sample_width=2,
                    channels=1
                )
                audio_segment.export(output_path, format=target_format)
            else:
                logger.error(f"不支持转换到格式: {target_format}")
                return False

            logger.info(f"格式转换成功: {input_path} -> {output_path}")
            return True

        except Exception as e:
            logger.error(f"格式转换失败: {e}")
            return False

    def analyze_audio_quality(self, audio: np.ndarray, sr: int) -> Dict[str, float]:
        """
        分析音频质量

        Args:
            audio: 音频数据
            sr: 采样率

        Returns:
            质量分析结果
        """
        try:
            if len(audio) == 0:
                return {}

            # 基本统计 - numpy 2.x兼容
            audio_squared = np.power(audio, 2)
            rms = np.sqrt(np.mean(audio_squared))
            peak = np.max(np.abs(audio))

            # 动态范围
            dynamic_range = 20 * np.log10(peak / (rms + 1e-10))

            # 过零率
            sign_changes = np.diff(np.signbit(audio))
            zero_crossings = np.sum(sign_changes)
            zero_crossing_rate = zero_crossings / len(audio)

            # 频谱质量分析
            stft = librosa.stft(audio)
            magnitude = np.abs(stft)

            # 频谱平坦度
            spectral_flatness = np.mean(librosa.feature.spectral_flatness(S=magnitude))

            # 频谱质心
            spectral_centroid = np.mean(librosa.feature.spectral_centroid(S=magnitude, sr=sr))

            # 频谱带宽
            spectral_bandwidth = np.mean(librosa.feature.spectral_bandwidth(S=magnitude, sr=sr))

            quality_metrics = {
                'rms_level_db': 20 * np.log10(rms + 1e-10),
                'peak_level_db': 20 * np.log10(peak + 1e-10),
                'dynamic_range_db': dynamic_range,
                'zero_crossing_rate': zero_crossing_rate,
                'spectral_flatness': float(spectral_flatness),
                'spectral_centroid_hz': float(spectral_centroid),
                'spectral_bandwidth_hz': float(spectral_bandwidth),
                'duration_seconds': len(audio) / sr,
                'sample_rate': sr
            }

            # 质量评分 (0-100)
            quality_score = self._calculate_quality_score(quality_metrics)
            quality_metrics['quality_score'] = quality_score

            return quality_metrics

        except Exception as e:
            logger.error(f"音频质量分析失败: {e}")
            return {}

    def _calculate_quality_score(self, metrics: Dict[str, float]) -> float:
        """计算音频质量评分"""
        try:
            score = 100.0

            # RMS电平检查 (-30dB 到 -10dB 为理想范围)
            rms_db = metrics.get('rms_level_db', -60)
            if rms_db < -40:
                score -= 20  # 音量过低
            elif rms_db > -5:
                score -= 30  # 音量过高，可能削波

            # 动态范围检查
            dynamic_range = metrics.get('dynamic_range_db', 0)
            if dynamic_range < 10:
                score -= 15  # 动态范围过小

            # 过零率检查
            zcr = metrics.get('zero_crossing_rate', 0)
            if zcr > 0.3:
                score -= 10  # 过零率过高，可能有噪声

            # 频谱平坦度检查
            flatness = metrics.get('spectral_flatness', 0)
            if flatness > 0.8:
                score -= 10  # 频谱过于平坦，可能是噪声

            return max(0.0, min(100.0, score))

        except Exception as e:
            logger.error(f"质量评分计算失败: {e}")
            return 50.0  # 默认中等质量


def check_audio_quality(file_path: str) -> Dict[str, Any]:
    """
    检查音频文件质量（便捷函数）

    Args:
        file_path: 音频文件路径

    Returns:
        质量分析结果
    """
    processor = EnhancedAudioProcessor()

    try:
        # 获取文件信息
        audio_info = processor.get_audio_info(file_path)
        if not audio_info.is_valid:
            return {
                'is_valid': False,
                'error': audio_info.error,
                'file_info': audio_info.__dict__
            }

        # 加载音频
        audio, sr = processor.load_audio(file_path)
        if audio is None:
            return {
                'is_valid': False,
                'error': '音频加载失败',
                'file_info': audio_info.__dict__
            }

        # 分析质量
        quality_metrics = processor.analyze_audio_quality(audio, sr)

        return {
            'is_valid': True,
            'file_info': audio_info.__dict__,
            'quality_metrics': quality_metrics
        }

    except Exception as e:
        logger.error(f"音频质量检查失败: {e}")
        return {
            'is_valid': False,
            'error': str(e)
        }
    finally:
        processor.cleanup_temp_files()
