#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Streamlit应用修复验证脚本
验证所有已修复的问题是否正常工作
"""

import importlib
import sys
import subprocess

def test_imports():
    """测试关键依赖导入"""
    print("🧪 测试依赖导入...")
    
    test_modules = [
        ("plotly.graph_objects", "Plotly图表库"),
        ("chromadb", "ChromaDB向量数据库"),
        ("streamlit", "Streamlit框架"),
        ("utils.monitoring_components", "监控组件")
    ]
    
    results = []
    
    for module_name, description in test_modules:
        try:
            importlib.import_module(module_name)
            print(f"✅ {description}: 导入成功")
            results.append(True)
        except ImportError as e:
            print(f"❌ {description}: 导入失败 - {e}")
            results.append(False)
    
    return all(results)

def test_streamlit_connectivity():
    """测试Streamlit连接"""
    print("\n🌐 测试Streamlit应用连接...")
    
    try:
        import requests
        response = requests.get("http://localhost:8501", timeout=5)
        if response.status_code == 200:
            print("✅ Streamlit应用响应正常")
            return True
        else:
            print(f"⚠️ Streamlit应用响应异常: HTTP {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ 无法连接到Streamlit应用: {e}")
        return False

def test_page_config_fixes():
    """检查页面配置修复"""
    print("\n📄 检查页面配置修复...")
    
    # 检查语音处理分析页面的配置修复
    try:
        with open("pages/语音处理分析.py", "r", encoding="utf-8") as f:
            content = f.read()
            
        if "st.set_page_config(" not in content:
            print("✅ 语音处理分析页面: set_page_config已移除")
            return True
        else:
            print("❌ 语音处理分析页面: 仍包含set_page_config")
            return False
    except Exception as e:
        print(f"❌ 检查页面配置时出错: {e}")
        return False

def test_button_keys():
    """检查按钮唯一key修复"""
    print("\n🔘 检查按钮唯一key修复...")
    
    try:
        with open("Home.py", "r", encoding="utf-8") as f:
            content = f.read()
        
        # 检查是否所有按钮都有唯一的key
        button_lines = [line for line in content.split('\n') if 'st.button(' in line]
        keyed_buttons = [line for line in button_lines if 'key=' in line]
        
        if len(button_lines) == len(keyed_buttons):
            print(f"✅ Home.py: 所有 {len(button_lines)} 个按钮都有唯一key")
            return True
        else:
            print(f"❌ Home.py: {len(button_lines)} 个按钮中只有 {len(keyed_buttons)} 个有key")
            return False
    except Exception as e:
        print(f"❌ 检查按钮key时出错: {e}")
        return False

def test_monitoring_integration():
    """测试监控组件集成"""
    print("\n📊 测试监控组件集成...")
    
    try:
        from utils.monitoring_components import ProcessingMonitor, IntegratedMonitorWidget
        
        # 创建监控器
        monitor = ProcessingMonitor()
        widget = IntegratedMonitorWidget()
        
        print("✅ 监控组件: 创建成功")
        
        # 测试基本功能
        monitor.start_monitoring()
        task_id = "test_integration"
        monitor.update_task(task_id, "running", 50, {"test": "integration"})
        monitor.update_task(task_id, "completed", 100, {"test": "done"})
        
        print("✅ 监控组件: 基本功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 监控组件集成测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 Streamlit应用修复验证")
    print("=" * 50)
    
    tests = [
        ("依赖导入", test_imports),
        ("Streamlit连接", test_streamlit_connectivity),
        ("页面配置修复", test_page_config_fixes),
        ("按钮Key修复", test_button_keys),
        ("监控组件集成", test_monitoring_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}: 测试执行失败 - {e}")
            results.append((test_name, False))
    
    # 输出总结
    print("\n" + "=" * 50)
    print("🏁 测试结果总结:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("\n🎉 所有修复验证通过！Streamlit应用可以正常使用")
        print("\n💡 访问地址: http://localhost:8501")
        print("📚 主要功能:")
        print("   • 语音处理分析 (VAD检测、音频预处理)")
        print("   • 监控组件集成 (实时进度、性能指标)")
        print("   • 本地RAG知识库 (文档问答)")
        print("   • 音频上传配置 (批量处理)")
    else:
        print(f"\n⚠️ 还有 {len(results) - passed} 项需要修复")
    
    return passed == len(results)

if __name__ == "__main__":
    main() 