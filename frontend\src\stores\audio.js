import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { audioFileAPI } from '@/api/audioProcessing'

export const useAudioStore = defineStore('audio', () => {
  // 文件管理
  const uploadedFiles = ref(new Map())
  const selectedFiles = ref([])
  const currentPreviewFile = ref(null)
  
  // 任务管理
  const processingTasks = ref(new Map())
  const taskQueue = ref([])
  const taskHistory = ref([])
  
  // 配置管理
  const currentConfig = ref({
    mode: 'speech-recognition',
    language: 'zh-CN',
    enableVAD: true,
    enableTimestamp: true,
    enableSpeakerDiarization: false
  })
  
  const configTemplates = ref([])
  
  // 结果管理
  const processingResults = ref(new Map())
  const resultHistory = ref([])
  
  // 工作区管理
  const workspaces = ref(new Map())
  const currentWorkspace = ref('default')
  
  // 计算属性
  const activeFiles = computed(() => Array.from(uploadedFiles.value.values()))
  
  const runningTasks = computed(() => 
    Array.from(processingTasks.value.values()).filter(task => task.status === 'running')
  )
  
  const completedTasks = computed(() => 
    Array.from(processingTasks.value.values()).filter(task => task.status === 'completed')
  )
  
  const failedTasks = computed(() => 
    Array.from(processingTasks.value.values()).filter(task => task.status === 'error')
  )
  
  // 文件管理方法
  const addFile = (file) => {
    const fileId = generateFileId()
    const fileInfo = {
      id: fileId,
      file,
      status: 'ready',
      progress: 0,
      uploadedAt: new Date(),
      metadata: {}
    }
    
    uploadedFiles.value.set(fileId, fileInfo)
    return fileInfo
  }
  
  const removeFile = (fileId) => {
    uploadedFiles.value.delete(fileId)
    
    // 从选中文件中移除
    const index = selectedFiles.value.findIndex(f => f.id === fileId)
    if (index > -1) {
      selectedFiles.value.splice(index, 1)
    }
    
    // 如果是当前预览文件，清除预览
    if (currentPreviewFile.value?.id === fileId) {
      currentPreviewFile.value = null
    }
  }
  
  const updateFileStatus = (fileId, status, data = {}) => {
    const file = uploadedFiles.value.get(fileId)
    if (file) {
      file.status = status
      Object.assign(file, data)
    }
  }
  
  const updateFileProgress = (fileId, progress) => {
    const file = uploadedFiles.value.get(fileId)
    if (file) {
      file.progress = progress
    }
  }
  
  // 任务管理方法
  const createTask = async (taskConfig) => {
    try {
      const task = await audioFileAPI.createProcessingTask(taskConfig)
      const taskInfo = {
        ...task,
        createdAt: new Date(),
        status: 'pending',
        progress: 0,
        stages: []
      }
      
      processingTasks.value.set(task.id, taskInfo)
      taskQueue.value.push(task.id)
      
      return taskInfo
    } catch (error) {
      throw new Error(`创建任务失败: ${error.message}`)
    }
  }
  
  const updateTaskStatus = (taskId, status, data = {}) => {
    const task = processingTasks.value.get(taskId)
    if (task) {
      task.status = status
      task.lastUpdate = new Date()
      Object.assign(task, data)
      
      // 如果任务完成或失败，移动到历史记录
      if (status === 'completed' || status === 'error') {
        taskHistory.value.unshift({ ...task })
        processingTasks.value.delete(taskId)
        
        // 从队列中移除
        const queueIndex = taskQueue.value.indexOf(taskId)
        if (queueIndex > -1) {
          taskQueue.value.splice(queueIndex, 1)
        }
      }
    }
  }
  
  const updateTaskProgress = (taskId, progress, stage = null) => {
    const task = processingTasks.value.get(taskId)
    if (task) {
      task.progress = progress
      task.lastUpdate = new Date()
      
      if (stage) {
        task.currentStage = stage
        
        // 添加到阶段历史
        if (!task.stages) {
          task.stages = []
        }
        
        const existingStage = task.stages.find(s => s.stage === stage.stage)
        if (existingStage) {
          Object.assign(existingStage, stage)
        } else {
          task.stages.push({ ...stage, timestamp: new Date() })
        }
      }
    }
  }
  
  const cancelTask = async (taskId) => {
    try {
      await audioFileAPI.cancelTask(taskId)
      updateTaskStatus(taskId, 'cancelled')
      return true
    } catch (error) {
      throw new Error(`取消任务失败: ${error.message}`)
    }
  }
  
  // 配置管理方法
  const updateConfig = (newConfig) => {
    currentConfig.value = { ...currentConfig.value, ...newConfig }
  }
  
  const saveConfigTemplate = (name, config = null) => {
    const template = {
      id: generateTemplateId(),
      name,
      config: config || { ...currentConfig.value },
      createdAt: new Date()
    }
    
    configTemplates.value.push(template)
    
    // 保存到本地存储
    localStorage.setItem('audio-config-templates', JSON.stringify(configTemplates.value))
    
    return template
  }
  
  const loadConfigTemplate = (templateId) => {
    const template = configTemplates.value.find(t => t.id === templateId)
    if (template) {
      currentConfig.value = { ...template.config }
      return template
    }
    return null
  }
  
  const deleteConfigTemplate = (templateId) => {
    const index = configTemplates.value.findIndex(t => t.id === templateId)
    if (index > -1) {
      configTemplates.value.splice(index, 1)
      localStorage.setItem('audio-config-templates', JSON.stringify(configTemplates.value))
      return true
    }
    return false
  }
  
  // 结果管理方法
  const addResult = (result) => {
    const resultId = generateResultId()
    const resultInfo = {
      id: resultId,
      ...result,
      createdAt: new Date()
    }
    
    processingResults.value.set(resultId, resultInfo)
    resultHistory.value.unshift(resultInfo)
    
    return resultInfo
  }
  
  const getResult = (resultId) => {
    return processingResults.value.get(resultId)
  }
  
  const removeResult = (resultId) => {
    processingResults.value.delete(resultId)
    
    const index = resultHistory.value.findIndex(r => r.id === resultId)
    if (index > -1) {
      resultHistory.value.splice(index, 1)
    }
  }
  
  // 工作区管理方法
  const createWorkspace = (name) => {
    const workspaceId = generateWorkspaceId()
    const workspace = {
      id: workspaceId,
      name,
      files: [],
      config: { ...currentConfig.value },
      createdAt: new Date()
    }
    
    workspaces.value.set(workspaceId, workspace)
    return workspace
  }
  
  const switchWorkspace = (workspaceId) => {
    if (workspaces.value.has(workspaceId)) {
      currentWorkspace.value = workspaceId
      
      // 加载工作区配置
      const workspace = workspaces.value.get(workspaceId)
      currentConfig.value = { ...workspace.config }
      
      return workspace
    }
    return null
  }
  
  const saveWorkspace = (workspaceId = null) => {
    const id = workspaceId || currentWorkspace.value
    const workspace = workspaces.value.get(id)
    
    if (workspace) {
      workspace.config = { ...currentConfig.value }
      workspace.files = [...selectedFiles.value]
      workspace.lastSaved = new Date()
      
      // 保存到本地存储
      localStorage.setItem(`workspace-${id}`, JSON.stringify(workspace))
      
      return workspace
    }
    return null
  }
  
  // 工具方法
  const generateFileId = () => {
    return `file-${Date.now()}-${Math.random().toString(36).substring(2)}`
  }
  
  const generateTaskId = () => {
    return `task-${Date.now()}-${Math.random().toString(36).substring(2)}`
  }
  
  const generateTemplateId = () => {
    return `template-${Date.now()}-${Math.random().toString(36).substring(2)}`
  }
  
  const generateResultId = () => {
    return `result-${Date.now()}-${Math.random().toString(36).substring(2)}`
  }
  
  const generateWorkspaceId = () => {
    return `workspace-${Date.now()}-${Math.random().toString(36).substring(2)}`
  }
  
  // 清理方法
  const clearAll = () => {
    uploadedFiles.value.clear()
    selectedFiles.value = []
    currentPreviewFile.value = null
    processingTasks.value.clear()
    taskQueue.value = []
    processingResults.value.clear()
  }
  
  const clearHistory = () => {
    taskHistory.value = []
    resultHistory.value = []
  }
  
  // 初始化
  const initialize = () => {
    // 加载保存的配置模板
    const savedTemplates = localStorage.getItem('audio-config-templates')
    if (savedTemplates) {
      try {
        configTemplates.value = JSON.parse(savedTemplates)
      } catch (error) {
        console.error('加载配置模板失败:', error)
      }
    }
    
    // 创建默认工作区
    if (!workspaces.value.has('default')) {
      const defaultWorkspace = createWorkspace('默认工作区')
      currentWorkspace.value = defaultWorkspace.id
    }
  }
  
  return {
    // 状态
    uploadedFiles,
    selectedFiles,
    currentPreviewFile,
    processingTasks,
    taskQueue,
    taskHistory,
    currentConfig,
    configTemplates,
    processingResults,
    resultHistory,
    workspaces,
    currentWorkspace,
    
    // 计算属性
    activeFiles,
    runningTasks,
    completedTasks,
    failedTasks,
    
    // 方法
    addFile,
    removeFile,
    updateFileStatus,
    updateFileProgress,
    createTask,
    updateTaskStatus,
    updateTaskProgress,
    cancelTask,
    updateConfig,
    saveConfigTemplate,
    loadConfigTemplate,
    deleteConfigTemplate,
    addResult,
    getResult,
    removeResult,
    createWorkspace,
    switchWorkspace,
    saveWorkspace,
    clearAll,
    clearHistory,
    initialize
  }
})
