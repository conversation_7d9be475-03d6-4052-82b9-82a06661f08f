import{l as w,h as i,J as _,m as f,ad as u,ac as v,ae as A}from"./index-2c134546.js";const S=w("auth",()=>{const s=i(_()),t=i(null),r=i(!1),h=f(()=>!!s.value),k=f(()=>{var e;return((e=t.value)==null?void 0:e.username)||""}),g=f(()=>{var e;return((e=t.value)==null?void 0:e.is_admin)||!1}),m=async e=>{var n,o;try{r.value=!0;const a=await u.login(e);if(a.data.access_token)return s.value=a.data.access_token,v(a.data.access_token),await l(),{success:!0,data:a.data};throw new Error("登录失败：未获取到访问令牌")}catch(a){return console.error("登录失败:",a),{success:!1,message:((o=(n=a.response)==null?void 0:n.data)==null?void 0:o.detail)||a.message||"登录失败"}}finally{r.value=!1}},y=async e=>{var n,o;try{return r.value=!0,{success:!0,data:(await u.register(e)).data}}catch(a){return console.error("注册失败:",a),{success:!1,message:((o=(n=a.response)==null?void 0:n.data)==null?void 0:o.detail)||a.message||"注册失败"}}finally{r.value=!1}},l=async()=>{try{if(!s.value)return null;const e=await u.getCurrentUser();return t.value=e.data,e.data}catch(e){return console.error("获取用户信息失败:",e),c(),null}},c=()=>{s.value=null,t.value=null,A()},p=async()=>{try{const e=await u.refreshToken();return e.data.access_token?(s.value=e.data.access_token,v(e.data.access_token),!0):!1}catch(e){return console.error("刷新token失败:",e),c(),!1}},d=async()=>{if(!s.value)return!1;try{return await l(),!0}catch{return c(),!1}};return{token:s,userInfo:t,isLoading:r,isAuthenticated:h,userName:k,isAdmin:g,login:m,register:y,logout:c,getUserInfo:l,refreshToken:p,checkAuth:d,init:async()=>{s.value&&await d()}}});export{S as u};
