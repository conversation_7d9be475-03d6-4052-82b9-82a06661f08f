---
description: 
globs: 
alwaysApply: true
---
# 语音处理智能平台 - Cursor Rules 规范体系

## 📋 规范概览

本项目采用Vue.js + FastAPI的前后端分离架构，为确保代码质量和开发效率，制定了以下5个核心规范：

### 🎨 1. UI统一规范
**文件**: [ui-standards.mdc](mdc:.cursor/rules/ui-standards.mdc)

- Element Plus 2.3.9 组件库标准
- Vue 3 Composition API 开发模式
- 响应式设计和主题色彩规范
- 数据可视化和交互规范

### ⚙️ 2. 任务处理规范  
**文件**: [task-processing.mdc](mdc:.cursor/rules/task-processing.mdc)

- Celery + Redis 任务队列架构
- 异步任务定义和进度监控
- 错误处理和重试机制
- WebSocket实时通信标准

### 🔗 3. API设计规范
**文件**: [api-standards.mdc](mdc:.cursor/rules/api-standards.mdc)

- FastAPI RESTful API 设计原则
- JWT认证和权限管理
- 统一响应格式和错误处理
- API文档和版本控制规范

### 📝 4. 代码编写命名规范
**文件**: [coding-conventions.mdc](mdc:.cursor/rules/coding-conventions.mdc)

- Python后端编码规范（snake_case）
- Vue.js前端编码规范（camelCase/PascalCase）
- 注释文档和类型注解标准
- Git提交信息规范

### 🏗️ 5. 项目结构规范
**文件**: [project-structure.mdc](mdc:.cursor/rules/project-structure.mdc)

- 前后端目录组织标准
- 数据存储和模型结构
- 配置文件和部署规范
- 测试和日志文件组织

## 🚀 核心技术栈

### 后端技术栈
- **框架**: FastAPI + uvicorn
- **数据库**: SQLite + ChromaDB (向量数据库)
- **任务队列**: Celery + Redis
- **AI模型**: LlamaIndex 0.10+ RAG架构
- **认证**: JWT Token认证

### 前端技术栈
- **框架**: Vue.js 3.3+ + Vite 4.4+
- **UI库**: Element Plus 2.3.9
- **状态管理**: Pinia 2.1+
- **路由**: Vue Router 4.2+
- **图表**: ECharts 5.4.3

## 📁 项目核心文件引用

### 后端核心文件
- 主入口：[backend/main.py](mdc:backend/main.py)
- 配置管理：[backend/core/config.py](mdc:backend/core/config.py)
- 数据库配置：[backend/core/database.py](mdc:backend/core/database.py)
- 任务队列：[backend/core/task_queue.py](mdc:backend/core/task_queue.py)
- RAG服务：[backend/services/rag_service.py](mdc:backend/services/rag_service.py)

### 前端核心文件
- 主入口：[frontend/src/main.js](mdc:frontend/src/main.js)
- 根组件：[frontend/src/App.vue](mdc:frontend/src/App.vue)
- 主页面：[frontend/src/views/Home.vue](mdc:frontend/src/views/Home.vue)
- 文档管理：[frontend/src/views/DocumentManager.vue](mdc:frontend/src/views/DocumentManager.vue)
- 知识库：[frontend/src/views/KnowledgeBase.vue](mdc:frontend/src/views/KnowledgeBase.vue)

### API端点文件
- 认证API：[backend/api/v1/endpoints/auth.py](mdc:backend/api/v1/endpoints/auth.py)
- 语音API：[backend/api/v1/endpoints/speech.py](mdc:backend/api/v1/endpoints/speech.py)
- 文档API：[backend/api/v1/endpoints/document_manager.py](mdc:backend/api/v1/endpoints/document_manager.py)
- 知识库API：[backend/api/v1/endpoints/knowledge.py](mdc:backend/api/v1/endpoints/knowledge.py)

## 🔥 开发原则

1. **模块化开发**: 遵循单一职责原则，保持代码模块化
2. **类型安全**: Python使用类型注解，前端使用明确的数据类型
3. **错误处理**: 统一的错误处理机制和用户友好的错误提示
4. **性能优化**: 异步处理、缓存策略、资源优化
5. **安全第一**: 数据验证、权限控制、安全认证

## 📚 快速参考

### 常用命令
```bash
# 后端开发
cd backend && python main.py

# 前端开发  
cd frontend && npm run dev

# 任务队列
cd backend && python start_worker.py

# 数据库迁移
cd backend && python -m alembic upgrade head
```

### 代码模板
- API端点模板：参考 [api-standards.mdc](mdc:.cursor/rules/api-standards.mdc)
- Vue组件模板：参考 [ui-standards.mdc](mdc:.cursor/rules/ui-standards.mdc)  
- 任务定义模板：参考 [task-processing.mdc](mdc:.cursor/rules/task-processing.mdc)

## 🤝 贡献指南

在提交代码前，请确保：
1. 遵循相应的编码规范
2. 添加必要的注释和文档
3. 进行单元测试
4. 遵循Git提交信息规范

---
*本规范体系随项目发展持续更新，请及时查阅最新版本。*

