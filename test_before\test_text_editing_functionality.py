#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文本编辑功能测试脚本
测试任务11.3的文本编辑功能实现
"""

import streamlit as st
import os
import sys
import json
import requests
import time
from datetime import datetime

def test_enhanced_text_editor():
    """测试增强的文本编辑功能"""
    print("🧪 测试文本编辑功能...")
    
    # 测试用例
    test_cases = [
        {
            "name": "页面访问测试",
            "description": "测试结果展示和编辑页面是否可访问",
            "test_func": test_page_accessibility
        },
        {
            "name": "文本编辑模式测试",
            "description": "测试片段编辑和全文编辑模式",
            "test_func": test_edit_modes
        },
        {
            "name": "撤销重做功能测试",
            "description": "测试编辑历史和撤销重做机制",
            "test_func": test_undo_redo_functionality
        },
        {
            "name": "自动保存功能测试",
            "description": "测试编辑后的自动保存机制",
            "test_func": test_auto_save
        },
        {
            "name": "编辑历史记录测试",
            "description": "测试编辑历史的记录和显示",
            "test_func": test_edit_history
        }
    ]
    
    results = []
    
    for case in test_cases:
        print(f"\n📋 {case['name']}")
        print(f"   描述: {case['description']}")
        
        try:
            result = case['test_func']()
            results.append({
                "name": case['name'],
                "status": "✅ 通过" if result else "❌ 失败",
                "success": result
            })
            print(f"   结果: {'✅ 通过' if result else '❌ 失败'}")
        except Exception as e:
            results.append({
                "name": case['name'],
                "status": f"❌ 错误: {str(e)}",
                "success": False
            })
            print(f"   结果: ❌ 错误: {str(e)}")
    
    # 输出测试结果总结
    print("\n" + "="*60)
    print("🎯 文本编辑功能测试总结")
    print("="*60)
    
    passed = sum(1 for r in results if r['success'])
    total = len(results)
    
    print(f"总测试用例: {total}")
    print(f"通过: {passed}")
    print(f"失败: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    
    print("\n📊 详细结果:")
    for result in results:
        print(f"  {result['status']} - {result['name']}")
    
    return passed == total

def test_page_accessibility():
    """测试页面访问性"""
    try:
        # 检查结果展示页面文件是否存在
        page_file = "pages/结果展示和编辑.py"
        if not os.path.exists(page_file):
            print(f"   ❌ 页面文件不存在: {page_file}")
            return False
        
        # 检查文件是否包含新的编辑功能
        with open(page_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        required_functions = [
            'display_enhanced_text_editor',
            'display_segment_editor',
            'display_full_text_editor',
            'save_segment_edit',
            'save_full_text_edit',
            'undo_edit',
            'redo_edit'
        ]
        
        missing_functions = []
        for func in required_functions:
            if func not in content:
                missing_functions.append(func)
        
        if missing_functions:
            print(f"   ❌ 缺少必要功能: {', '.join(missing_functions)}")
            return False
        
        print("   ✅ 页面文件和核心功能完整")
        return True
        
    except Exception as e:
        print(f"   ❌ 页面访问测试失败: {str(e)}")
        return False

def test_edit_modes():
    """测试编辑模式"""
    try:
        # 检查编辑模式相关代码
        page_file = "pages/结果展示和编辑.py"
        with open(page_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查编辑模式选择
        if 'st.radio' not in content or '"片段编辑"' not in content or '"全文编辑"' not in content:
            print("   ❌ 编辑模式选择功能缺失")
            return False
        
        # 检查片段编辑器
        if 'display_segment_editor' not in content:
            print("   ❌ 片段编辑器功能缺失")
            return False
        
        # 检查全文编辑器
        if 'display_full_text_editor' not in content:
            print("   ❌ 全文编辑器功能缺失")
            return False
        
        # 检查编辑控制按钮
        edit_buttons = ['💾 保存修改', '↶ 撤销', '↷ 重做', '🔄 重置']
        for button in edit_buttons:
            if button not in content:
                print(f"   ❌ 缺少编辑按钮: {button}")
                return False
        
        print("   ✅ 编辑模式功能完整")
        return True
        
    except Exception as e:
        print(f"   ❌ 编辑模式测试失败: {str(e)}")
        return False

def test_undo_redo_functionality():
    """测试撤销重做功能"""
    try:
        page_file = "pages/结果展示和编辑.py"
        with open(page_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查撤销重做相关函数
        undo_redo_functions = [
            'save_edit_to_history',
            'undo_edit',
            'redo_edit',
            'apply_edit_record'
        ]
        
        for func in undo_redo_functions:
            if f'def {func}' not in content:
                print(f"   ❌ 缺少撤销重做功能: {func}")
                return False
        
        # 检查编辑历史的session state管理
        if 'edit_history' not in content or 'edit_index' not in content:
            print("   ❌ 编辑历史状态管理缺失")
            return False
        
        print("   ✅ 撤销重做功能完整")
        return True
        
    except Exception as e:
        print(f"   ❌ 撤销重做功能测试失败: {str(e)}")
        return False

def test_auto_save():
    """测试自动保存功能"""
    try:
        page_file = "pages/结果展示和编辑.py"
        with open(page_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查自动保存函数
        if 'def auto_save_result' not in content:
            print("   ❌ 自动保存功能缺失")
            return False
        
        # 检查保存调用
        if 'auto_save_result(analysis)' not in content:
            print("   ❌ 自动保存调用缺失")
            return False
        
        # 检查保存目录创建
        if 'os.makedirs(save_dir, exist_ok=True)' not in content:
            print("   ❌ 保存目录创建逻辑缺失")
            return False
        
        # 检查JSON保存
        if 'json.dump' not in content:
            print("   ❌ JSON保存功能缺失")
            return False
        
        print("   ✅ 自动保存功能完整")
        return True
        
    except Exception as e:
        print(f"   ❌ 自动保存功能测试失败: {str(e)}")
        return False

def test_edit_history():
    """测试编辑历史记录"""
    try:
        page_file = "pages/结果展示和编辑.py"
        with open(page_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查编辑历史显示函数
        if 'def display_edit_history' not in content:
            print("   ❌ 编辑历史显示功能缺失")
            return False
        
        # 检查历史记录格式
        history_elements = [
            'edit_record',
            'timestamp',
            'old_value',
            'new_value',
            'edit_type'
        ]
        
        for element in history_elements:
            if element not in content:
                print(f"   ❌ 编辑历史元素缺失: {element}")
                return False
        
        # 检查编辑历史标签页
        if '"📜 编辑历史"' not in content:
            print("   ❌ 编辑历史标签页缺失")
            return False
        
        print("   ✅ 编辑历史功能完整")
        return True
        
    except Exception as e:
        print(f"   ❌ 编辑历史测试失败: {str(e)}")
        return False

def test_streamlit_deployment():
    """测试Streamlit部署"""
    try:
        print("\n🚀 启动Streamlit应用测试...")
        
        # 检查是否有Streamlit进程在运行
        import subprocess
        import time
        
        # 尝试在新端口启动应用
        port = 8508
        
        cmd = f"streamlit run pages/结果展示和编辑.py --server.port={port} --server.headless=true"
        print(f"   执行命令: {cmd}")
        
        # 使用subprocess启动
        process = subprocess.Popen(
            cmd.split(),
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            shell=True
        )
        
        # 等待启动
        time.sleep(3)
        
        # 检查进程状态
        if process.poll() is None:
            print(f"   ✅ Streamlit应用成功启动在端口 {port}")
            
            # 尝试访问页面
            try:
                response = requests.get(f"http://localhost:{port}", timeout=5)
                if response.status_code == 200:
                    print("   ✅ 页面响应正常")
                    process.terminate()
                    return True
                else:
                    print(f"   ⚠️ 页面响应异常: {response.status_code}")
            except requests.exceptions.RequestException as e:
                print(f"   ⚠️ 页面访问异常: {str(e)}")
            
            process.terminate()
            return True
        else:
            stdout, stderr = process.communicate()
            print(f"   ❌ Streamlit启动失败")
            if stderr:
                print(f"   错误信息: {stderr.decode()}")
            return False
            
    except Exception as e:
        print(f"   ❌ Streamlit部署测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🎯 开始文本编辑功能测试")
    print("=" * 60)
    
    # 基础功能测试
    basic_test_result = test_enhanced_text_editor()
    
    # Streamlit部署测试
    print("\n" + "="*60)
    print("🚀 Streamlit部署测试")
    print("="*60)
    
    deployment_result = test_streamlit_deployment()
    
    # 最终结果
    print("\n" + "="*60)
    print("🏆 最终测试结果")
    print("="*60)
    
    print(f"基础功能测试: {'✅ 通过' if basic_test_result else '❌ 失败'}")
    print(f"部署测试: {'✅ 通过' if deployment_result else '❌ 失败'}")
    
    overall_success = basic_test_result and deployment_result
    print(f"\n总体结果: {'🎉 全部通过' if overall_success else '⚠️ 部分失败'}")
    
    if overall_success:
        print("\n🎯 任务11.3 - 文本编辑功能实现 ✅ 完成")
        print("✨ 所有文本编辑功能测试通过！")
    else:
        print("\n⚠️ 部分测试未通过，需要进一步检查和修复") 