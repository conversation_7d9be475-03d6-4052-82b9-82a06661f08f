import os
import sys
import soundfile as sf
import numpy as np
import tempfile
import torch
from funasr import AutoModel
import streamlit as st

# 模拟streamlit环境，避免测试时出现错误
if not hasattr(st, "info"):
    class DummyStreamlit:
        def info(self, text): print(f"INFO: {text}")
        def success(self, text): print(f"SUCCESS: {text}")
        def warning(self, text): print(f"WARNING: {text}")
        def error(self, text): print(f"ERROR: {text}")
    st = DummyStreamlit()

# 从utils导入相关函数
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.speech_recognition_utils import extract_speaker_embedding_campplus

def generate_test_audio(file_path, duration=3, sr=16000):
    """生成测试用音频文件"""
    # 生成一个正弦波
    t = np.linspace(0, duration, int(duration * sr), endpoint=False)
    audio = np.sin(2*np.pi*440*t) * 0.5
    
    # 保存为WAV文件
    sf.write(file_path, audio, sr)
    print(f"生成测试音频: {file_path}")
    return file_path

def test_mock_tensor_embedding():
    """测试处理tensor格式嵌入向量的能力"""
    print("\n===== 测试处理tensor格式嵌入向量 =====")
    
    # 创建一个模拟的tensor嵌入向量
    tensor_embedding = torch.tensor([0.1, 0.2, -0.3, 0.4, -0.5])
    tensor_str = str(tensor_embedding)
    print(f"模拟tensor嵌入向量: {tensor_str}")
    
    # 使用正则表达式提取数值
    import re
    values = re.findall(r'-?\d+\.\d+e[+-]\d+|-?\d+\.\d+', tensor_str)
    if values:
        embedding_array = np.array([float(v) for v in values])
        print(f"成功从tensor字符串中提取数值: {embedding_array}")
        print(f"维度: {len(embedding_array)}")
        return True
    else:
        print(f"无法从tensor字符串中提取数值: {tensor_str}")
        return False

def test_with_real_model():
    """使用真实的CAM++模型测试嵌入向量提取"""
    print("\n===== 使用真实模型测试 =====")
    
    # 检查是否有GPU可用
    device = "cuda:0" if torch.cuda.is_available() else "cpu"
    print(f"使用设备: {device}")
    
    # 创建临时音频文件
    with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp:
        audio_file = tmp.name
    
    # 生成测试音频
    generate_test_audio(audio_file)
    
    try:
        # 加载CAM++模型
        print("正在加载CAM++模型...")
        model_id = "damo/speech_campplus_sv_zh"  # 使用在线模型
        model = AutoModel(model=model_id, device=device)
        print("CAM++模型加载成功")
        
        # 提取嵌入向量
        print(f"从音频提取嵌入向量: {audio_file}")
        embedding = extract_speaker_embedding_campplus(audio_file, model)
        
        # 验证结果
        if embedding is not None:
            print(f"嵌入向量提取成功! 维度: {len(embedding)}")
            print(f"嵌入向量示例: {embedding[:5]}...")
            return True
        else:
            print("嵌入向量提取失败")
            return False
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理临时文件
        try:
            os.unlink(audio_file)
        except:
            pass

def main():
    """主测试函数"""
    print("===== CAM++嵌入向量提取测试 =====")
    
    # 测试模拟的tensor嵌入向量处理
    mock_test_success = test_mock_tensor_embedding()
    print(f"模拟tensor测试: {'成功' if mock_test_success else '失败'}")
    
    # 如果环境允许，测试真实模型
    try:
        model_test_success = test_with_real_model()
        print(f"真实模型测试: {'成功' if model_test_success else '失败'}")
    except Exception as e:
        print(f"无法完成真实模型测试: {str(e)}")
        print("提示: 请确保已安装所有依赖，并且可以访问模型。")

if __name__ == "__main__":
    main() 