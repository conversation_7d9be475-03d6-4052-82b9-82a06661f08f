# 使用多阶段构建优化的Dockerfile
FROM python:3.11-slim AS builder

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV PIP_NO_CACHE_DIR=1
ENV PIP_DISABLE_PIP_VERSION_CHECK=1

# 配置pip使用阿里云镜像源
RUN pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip config set install.trusted-host mirrors.aliyun.com

# 安装构建依赖（只包含构建必需的包）
RUN apt-get update && apt-get install -y \
    build-essential \
    git \
    curl

# 复制requirements.txt
COPY requirements.txt .

# 安装Python依赖，使用buildx缓存
RUN pip install -r requirements.txt

# 安装额外的依赖包，使用buildx缓存
RUN pip install \
    llama-index \
    llama-index-embeddings-huggingface \
    llama-index-embeddings-ollama \
    llama-index-llms-ollama \
    llama-index-vector-stores-chroma \
    chromadb \
    sentence-transformers \
    PyPDF2 \
    langchain \
    langchain-core && \
    pip install torch==2.6.0 torchvision==0.21.0 torchaudio==2.6.0 --index-url https://download.pytorch.org/whl/cu124

# 第二阶段：运行时镜像
FROM python:3.11-slim AS runtime

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV STREAMLIT_SERVER_PORT=8501
ENV STREAMLIT_SERVER_ADDRESS=0.0.0.0
ENV STREAMLIT_SERVER_HEADLESS=true
ENV STREAMLIT_BROWSER_GATHER_USAGE_STATS=false

# NVIDIA 运行时环境变量
ENV NVIDIA_VISIBLE_DEVICES=all
ENV NVIDIA_DRIVER_CAPABILITIES=compute,utility

# 安装运行时依赖
RUN apt-get update && apt-get install -y --no-install-recommends \
    tesseract-ocr \
    tesseract-ocr-chi-sim \
    tesseract-ocr-chi-tra \
    poppler-utils \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    curl \
    && rm -rf /var/lib/apt/lists/*

# 从构建阶段复制Python依赖
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# 复制项目文件
COPY . .

#复制模型文件
COPY ./models /app/models

# 创建必要的目录
RUN mkdir -p /app/chroma_db \
    /app/data/documents \
    /app/knowledge_base \
    /app/models

# 设置文件权限
RUN chmod -R 755 /app

# 暴露Streamlit默认端口
EXPOSE 8501

# 健康检查
HEALTHCHECK CMD curl --fail http://localhost:8501/_stcore/health

# 启动命令
CMD ["streamlit", "run", "Home.py", "--server.port=8501", "--server.address=0.0.0.0"] 