<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket连接调试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
            margin: 5px 0;
        }
        .status.connected { background-color: #d4edda; color: #155724; }
        .status.disconnected { background-color: #f8d7da; color: #721c24; }
        .status.connecting { background-color: #fff3cd; color: #856404; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .info-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebSocket连接调试工具</h1>
        
        <div class="status-section">
            <h3>连接状态</h3>
            <div class="status" id="connectionStatus">未连接</div>
            
            <button onclick="testConnection()">测试连接</button>
            <button onclick="testSubscription()">测试订阅</button>
            <button onclick="sendTestMessage()">发送测试消息</button>
            <button onclick="clearLogs()">清空日志</button>
        </div>

        <div class="info-grid">
            <div class="info-card">
                <h4>连接信息</h4>
                <div id="connectionInfo">
                    <p>WebSocket URL: <span id="wsUrl">-</span></p>
                    <p>认证状态: <span id="authStatus">-</span></p>
                    <p>重连次数: <span id="reconnectCount">-</span></p>
                </div>
            </div>
            
            <div class="info-card">
                <h4>监听器状态</h4>
                <div id="listenerInfo">
                    <p>Progress监听器: <span id="progressListeners">0</span></p>
                    <p>Completed监听器: <span id="completedListeners">0</span></p>
                    <p>Failed监听器: <span id="failedListeners">0</span></p>
                </div>
            </div>
        </div>
        
        <div class="status-section">
            <h3>实时日志</h3>
            <div class="log" id="logContainer"></div>
        </div>
    </div>

    <script>
        // 模拟WebSocket管理器
        class WebSocketDebugger {
            constructor() {
                this.ws = null;
                this.isConnected = false;
                this.isConnecting = false;
                this.listeners = new Map();
                this.reconnectAttempts = 0;
                this.maxReconnectAttempts = 5;
                this.reconnectInterval = 1000;
                this.heartbeatInterval = null;
                this.heartbeatTimeout = null;
            }

            async connect(token) {
                if (this.isConnecting || this.isConnected) {
                    this.log('⚠️ 已经在连接或已连接');
                    return;
                }

                this.isConnecting = true;
                this.updateStatus('connecting', '正在连接...');
                
                return new Promise((resolve, reject) => {
                    try {
                        // 使用实际的WebSocket URL
                        const wsUrl = `ws://localhost:8002/ws?token=${token}`;
                        this.log(`🔌 尝试连接到: ${wsUrl}`);
                        document.getElementById('wsUrl').textContent = wsUrl;
                        
                        this.ws = new WebSocket(wsUrl);
                        
                        this.ws.onopen = () => {
                            this.log('✅ WebSocket连接成功');
                            this.isConnected = true;
                            this.isConnecting = false;
                            this.reconnectAttempts = 0;
                            this.updateStatus('connected', '已连接');
                            this.startHeartbeat();
                            resolve();
                        };
                        
                        this.ws.onmessage = (event) => {
                            try {
                                const data = JSON.parse(event.data);
                                this.log(`📨 收到消息: ${JSON.stringify(data, null, 2)}`);
                                this.handleMessage(data);
                            } catch (error) {
                                this.log(`❌ 消息解析失败: ${error.message}`);
                            }
                        };
                        
                        this.ws.onclose = (event) => {
                            this.log(`🔌 连接关闭: ${event.code} - ${event.reason}`);
                            this.isConnecting = false;
                            this.isConnected = false;
                            this.updateStatus('disconnected', '连接已关闭');
                            this.stopHeartbeat();
                            
                            if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
                                this.scheduleReconnect(token);
                            }
                        };
                        
                        this.ws.onerror = (error) => {
                            this.log(`❌ WebSocket错误: ${error}`);
                            this.isConnecting = false;
                            this.isConnected = false;
                            this.updateStatus('disconnected', '连接错误');
                            reject(error);
                        };
                        
                    } catch (error) {
                        this.isConnecting = false;
                        this.updateStatus('disconnected', '连接失败');
                        reject(error);
                    }
                });
            }

            handleMessage(data) {
                this.log(`📋 处理消息类型: ${data.type}`);
                
                switch (data.type) {
                    case 'progress_update':
                        this.log(`📈 进度更新: ${JSON.stringify(data.payload)}`);
                        this.notifyListeners('progress', data.payload);
                        break;
                    case 'task_completed':
                        this.log(`✅ 任务完成: ${JSON.stringify(data.payload)}`);
                        this.notifyListeners('completed', data.payload);
                        break;
                    case 'task_failed':
                        this.log(`❌ 任务失败: ${JSON.stringify(data.payload)}`);
                        this.notifyListeners('failed', data.payload);
                        break;
                    case 'heartbeat':
                        this.log('💓 收到心跳响应');
                        this.handleHeartbeat();
                        break;
                    default:
                        this.log(`⚠️ 未知消息类型: ${data.type}`);
                }
            }

            notifyListeners(event, data) {
                const listeners = this.listeners.get(event) || [];
                this.log(`📢 通知 ${listeners.length} 个 ${event} 监听器`);
                listeners.forEach((callback, index) => {
                    try {
                        this.log(`📞 调用监听器 ${index + 1}`);
                        callback(data);
                    } catch (error) {
                        this.log(`❌ 监听器执行失败: ${error.message}`);
                    }
                });
            }

            on(event, callback) {
                if (!this.listeners.has(event)) {
                    this.listeners.set(event, []);
                }
                this.listeners.get(event).push(callback);
                this.log(`➕ 添加 ${event} 监听器，当前总数: ${this.listeners.get(event).length}`);
                this.updateListenerInfo();
            }

            off(event, callback) {
                const listeners = this.listeners.get(event) || [];
                const index = listeners.indexOf(callback);
                if (index > -1) {
                    listeners.splice(index, 1);
                    this.log(`➖ 移除 ${event} 监听器，当前总数: ${listeners.length}`);
                    this.updateListenerInfo();
                }
            }

            subscribeTask(taskId) {
                if (this.isConnected) {
                    const message = {
                        type: 'subscribe',
                        task_id: taskId
                    };
                    this.send(message);
                    this.log(`📝 订阅任务: ${taskId}`);
                } else {
                    this.log(`⚠️ 无法订阅任务 ${taskId}：WebSocket未连接`);
                }
            }

            unsubscribeTask(taskId) {
                if (this.isConnected) {
                    const message = {
                        type: 'unsubscribe',
                        task_id: taskId
                    };
                    this.send(message);
                    this.log(`📝 取消订阅任务: ${taskId}`);
                } else {
                    this.log(`⚠️ 无法取消订阅任务 ${taskId}：WebSocket未连接`);
                }
            }

            send(data) {
                if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
                    const message = JSON.stringify(data);
                    this.ws.send(message);
                    this.log(`📤 发送消息: ${message}`);
                } else {
                    this.log(`⚠️ 无法发送消息：WebSocket未连接`);
                }
            }

            startHeartbeat() {
                this.heartbeatInterval = setInterval(() => {
                    if (this.isConnected) {
                        this.send({ type: 'ping' });
                        this.log('💓 发送心跳');
                        
                        this.heartbeatTimeout = setTimeout(() => {
                            this.log('⚠️ 心跳超时，重连WebSocket');
                            this.disconnect();
                        }, 5000);
                    }
                }, 30000);
            }

            stopHeartbeat() {
                if (this.heartbeatInterval) {
                    clearInterval(this.heartbeatInterval);
                    this.heartbeatInterval = null;
                }
                if (this.heartbeatTimeout) {
                    clearTimeout(this.heartbeatTimeout);
                    this.heartbeatTimeout = null;
                }
            }

            handleHeartbeat() {
                if (this.heartbeatTimeout) {
                    clearTimeout(this.heartbeatTimeout);
                    this.heartbeatTimeout = null;
                }
            }

            scheduleReconnect(token) {
                this.reconnectAttempts++;
                const delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1);
                
                this.log(`🔄 ${delay}ms后尝试第${this.reconnectAttempts}次重连...`);
                document.getElementById('reconnectCount').textContent = this.reconnectAttempts;
                
                setTimeout(() => {
                    if (!this.isConnected) {
                        this.connect(token).catch(error => {
                            this.log(`❌ 重连失败: ${error.message}`);
                        });
                    }
                }, delay);
            }

            disconnect() {
                this.isConnected = false;
                this.isConnecting = false;
                this.stopHeartbeat();
                
                if (this.ws) {
                    this.ws.close(1000, 'Client disconnect');
                    this.ws = null;
                }
                
                this.updateStatus('disconnected', '已断开连接');
            }

            getStatus() {
                return {
                    isConnected: this.isConnected,
                    isConnecting: this.isConnecting,
                    reconnectAttempts: this.reconnectAttempts
                };
            }

            updateStatus(status, message) {
                const statusEl = document.getElementById('connectionStatus');
                statusEl.className = `status ${status}`;
                statusEl.textContent = message;
            }

            updateListenerInfo() {
                document.getElementById('progressListeners').textContent = (this.listeners.get('progress') || []).length;
                document.getElementById('completedListeners').textContent = (this.listeners.get('completed') || []).length;
                document.getElementById('failedListeners').textContent = (this.listeners.get('failed') || []).length;
            }

            log(message) {
                const logContainer = document.getElementById('logContainer');
                const timestamp = new Date().toLocaleTimeString();
                logContainer.innerHTML += `<div>[${timestamp}] ${message}</div>`;
                logContainer.scrollTop = logContainer.scrollHeight;
                console.log(message);
            }
        }

        // 全局实例
        const wsDebugger = new WebSocketDebugger();

        // 测试函数
        async function testConnection() {
            // 模拟获取token
            const token = 'demo-token'; // 这里应该是实际的token
            document.getElementById('authStatus').textContent = token ? '有效' : '无效';
            
            try {
                await wsDebugger.connect(token);
                wsDebugger.log('🎉 连接测试成功');
            } catch (error) {
                wsDebugger.log(`💥 连接测试失败: ${error.message}`);
            }
        }

        function testSubscription() {
            // 添加测试监听器
            wsDebugger.on('progress', (data) => {
                wsDebugger.log(`🎯 Progress监听器收到数据: ${JSON.stringify(data)}`);
            });
            
            wsDebugger.on('completed', (data) => {
                wsDebugger.log(`🎯 Completed监听器收到数据: ${JSON.stringify(data)}`);
            });
            
            wsDebugger.on('failed', (data) => {
                wsDebugger.log(`🎯 Failed监听器收到数据: ${JSON.stringify(data)}`);
            });

            // 订阅测试任务
            const testTaskId = 'test_task_' + Date.now();
            wsDebugger.subscribeTask(testTaskId);
            wsDebugger.log(`📋 已订阅测试任务: ${testTaskId}`);
        }

        function sendTestMessage() {
            wsDebugger.send({
                type: 'test',
                message: 'Hello from debug tool',
                timestamp: new Date().toISOString()
            });
        }

        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
        }

        // 页面加载时初始化
        window.onload = function() {
            wsDebugger.log('🚀 WebSocket调试工具已启动');
            wsDebugger.updateListenerInfo();
        };
    </script>
</body>
</html>
