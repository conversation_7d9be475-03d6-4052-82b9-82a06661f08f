#!/usr/bin/env python3
"""
检查数据库表是否存在
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_tables():
    """检查数据库表"""
    try:
        from backend.core.database import get_db_session
        from sqlalchemy import text
        
        db = get_db_session()
        try:
            # 检查task_records表
            result = db.execute(text("SELECT name FROM sqlite_master WHERE type='table' AND name='task_records'"))
            task_records_exists = bool(result.fetchone())
            
            # 获取所有表
            result = db.execute(text("SELECT name FROM sqlite_master WHERE type='table'"))
            all_tables = [row[0] for row in result.fetchall()]
            
            print(f"task_records表存在: {task_records_exists}")
            print(f"数据库中的所有表 ({len(all_tables)} 个):")
            for table in sorted(all_tables):
                print(f"  - {table}")
                
            # 检查任务相关表
            task_tables = [t for t in all_tables if 'task' in t.lower()]
            print(f"\n任务相关表 ({len(task_tables)} 个):")
            for table in task_tables:
                print(f"  - {table}")
                
        finally:
            db.close()
            
    except Exception as e:
        print(f"检查失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_tables()
