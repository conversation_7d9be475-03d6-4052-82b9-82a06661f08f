#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文档上传修复
"""

import requests
import time
import json
from pathlib import Path

# 配置
API_BASE_URL = "http://localhost:8002"
TEST_FILE_PATH = "test_document.txt"

def create_test_file():
    """创建测试文件"""
    test_content = """
# 测试文档

这是一个测试文档，用于验证文档上传和处理功能。

## 第一章：介绍

这是第一章的内容。包含一些基本的介绍信息。

## 第二章：详细说明

这是第二章的内容。包含更详细的说明。

### 2.1 子章节

这是一个子章节的内容。

### 2.2 另一个子章节

这是另一个子章节的内容。

## 第三章：总结

这是总结章节的内容。

## 结论

文档处理测试完成。
"""
    
    with open(TEST_FILE_PATH, 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    print(f"✅ 创建测试文件: {TEST_FILE_PATH}")

def login():
    """登录获取token"""
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    response = requests.post(f"{API_BASE_URL}/api/v1/auth/login", json=login_data)
    
    if response.status_code == 200:
        token = response.json()["access_token"]
        print("✅ 登录成功")
        return token
    else:
        print(f"❌ 登录失败: {response.status_code} - {response.text}")
        return None

def upload_document(token):
    """上传文档"""
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    with open(TEST_FILE_PATH, 'rb') as f:
        files = {
            'file': (TEST_FILE_PATH, f, 'text/plain')
        }
        data = {
            'use_ocr': 'false'
        }
        
        response = requests.post(
            f"{API_BASE_URL}/api/v1/documents/upload-file",
            headers=headers,
            files=files,
            data=data
        )
    
    if response.status_code == 200:
        result = response.json()
        print("✅ 文档上传成功")
        print(f"  任务ID: {result['task_id']}")
        print(f"  文档ID: {result.get('document_id', 'N/A')}")
        print(f"  文件名: {result['filename']}")
        return result['task_id'], result.get('document_id')
    else:
        print(f"❌ 文档上传失败: {response.status_code} - {response.text}")
        return None, None

def check_task_status(task_id):
    """检查任务状态"""
    import redis
    
    redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
    progress_key = f"task_progress:{task_id}"
    
    data = redis_client.hgetall(progress_key)
    
    if data:
        print(f"📊 任务 {task_id} 状态:")
        print(f"  进度: {data.get('percentage', 0)}%")
        print(f"  详情: {data.get('detail', '')}")
        print(f"  阶段: {data.get('stage', '')}")
        
        if data.get('result'):
            try:
                result = eval(data['result'])  # 注意：生产环境中应该使用json.loads
                print(f"  结果: {result}")
                return result
            except:
                print(f"  结果: {data.get('result', '')}")
        
        return data
    else:
        print(f"❌ 任务 {task_id} 没有进度数据")
        return None

def check_document_status(token, document_id):
    """检查文档状态"""
    if not document_id:
        print("❌ 没有文档ID")
        return None
    
    headers = {
        "Authorization": f"Bearer {token}"
    }
    
    response = requests.get(
        f"{API_BASE_URL}/api/v1/documents/{document_id}/status",
        headers=headers
    )
    
    if response.status_code == 200:
        result = response.json()
        print(f"📄 文档 {document_id} 状态:")
        print(f"  状态: {result.get('status', 'N/A')}")
        print(f"  进度: {result.get('processing_progress', 0) * 100:.1f}%")
        print(f"  节点数: {result.get('sections_count', 0)}")
        print(f"  创建时间: {result.get('created_at', 'N/A')}")
        print(f"  处理完成时间: {result.get('processed_at', 'N/A')}")
        return result
    else:
        print(f"❌ 获取文档状态失败: {response.status_code} - {response.text}")
        return None

def main():
    """主函数"""
    print("=" * 80)
    print("文档上传修复测试")
    print("=" * 80)
    
    # 1. 创建测试文件
    create_test_file()
    
    # 2. 登录
    token = login()
    if not token:
        return
    
    # 3. 上传文档
    task_id, document_id = upload_document(token)
    if not task_id:
        return
    
    # 4. 监控任务进度
    print("\n📈 监控任务进度...")
    max_wait = 60  # 最多等待60秒
    start_time = time.time()
    
    while time.time() - start_time < max_wait:
        task_status = check_task_status(task_id)
        
        if task_status and task_status.get('stage') == 'completed':
            print("✅ 任务完成！")
            break
        elif task_status and task_status.get('stage') == 'failed':
            print("❌ 任务失败！")
            break
        
        time.sleep(2)
    else:
        print("⏰ 任务监控超时")
    
    # 5. 检查文档状态
    print("\n📄 检查文档状态...")
    doc_status = check_document_status(token, document_id)
    
    # 6. 总结
    print("\n" + "=" * 80)
    print("测试总结")
    print("=" * 80)
    
    if doc_status:
        if doc_status.get('status') == 'completed':
            print("✅ 文档状态正确更新为 'completed'")
            print("🎉 修复成功！")
        else:
            print(f"❌ 文档状态仍然是: {doc_status.get('status')}")
            print("🔧 需要进一步修复")
    else:
        print("❌ 无法获取文档状态")
    
    # 7. 清理测试文件
    try:
        Path(TEST_FILE_PATH).unlink()
        print(f"🧹 清理测试文件: {TEST_FILE_PATH}")
    except:
        pass

if __name__ == "__main__":
    main()
