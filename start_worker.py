#!/usr/bin/env python
"""
启动 Celery Worker 的 Python 脚本
解决模块导入路径问题
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ.setdefault('PYTHONPATH', str(project_root))

# 导入 celery 应用
from backend.core.task_queue import celery_app

# 确保任务模块被导入并注册到 Celery
try:
    import backend.tasks.document_tasks
    import backend.tasks.vectorization_tasks
    import backend.tasks.ocr_tasks
    print("✅ 任务模块导入成功")

    # 手动注册任务到 Celery 应用
    celery_app.autodiscover_tasks(['backend.tasks'])
    print("✅ 任务自动发现完成")

except ImportError as e:
    print(f"❌ 任务模块导入失败: {e}")
    sys.exit(1)

if __name__ == '__main__':
    print("🚀 启动 Celery Worker...")
    print(f"📁 项目根目录: {project_root}")
    print(f"🐍 Python 路径: {sys.path[:3]}...")
    
    # 启动 celery worker
    celery_app.worker_main([
        'worker',
        '--loglevel=info',
        '--concurrency=2',
        '--queues=document_processing,vectorization,ocr_processing,default'
    ])
