#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细的音频API测试
"""

import requests
import tempfile
import numpy as np
from scipy.io import wavfile
import os

BASE_URL = "http://localhost:8002"
TEST_TOKEN = "test_token_12345"

def create_test_audio():
    """创建测试音频文件"""
    sample_rate = 16000
    duration = 1.0
    frequency = 440.0
    
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    audio_data = (np.sin(frequency * 2 * np.pi * t) * 32767).astype(np.int16)
    
    temp_file = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
    wavfile.write(temp_file.name, sample_rate, audio_data)
    temp_file.close()
    
    return temp_file.name

def test_audio_upload_detailed():
    """详细测试音频上传"""
    print("=== 详细测试音频上传 ===")
    
    try:
        # 创建测试音频
        audio_file = create_test_audio()
        print(f"创建测试音频文件: {audio_file}")
        
        # 测试上传
        with open(audio_file, 'rb') as f:
            files = {'file': ('test.wav', f, 'audio/wav')}
            data = {'user_id': '1'}
            headers = {'Authorization': f'Bearer {TEST_TOKEN}'}
            
            print("发送上传请求...")
            response = requests.post(
                f"{BASE_URL}/api/v1/audio/upload", 
                files=files, 
                data=data, 
                headers=headers,
                timeout=30
            )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {response.headers}")
        print(f"响应内容: {response.text}")
        
        # 清理
        os.unlink(audio_file)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✓ 音频上传成功")
                return result.get('file_id')
            else:
                print(f"✗ 上传失败: {result}")
                return None
        else:
            print(f"✗ HTTP错误: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"✗ 上传异常: {e}")
        return None

def test_audio_list_detailed():
    """详细测试音频列表"""
    print("\n=== 详细测试音频列表 ===")
    
    try:
        headers = {'Authorization': f'Bearer {TEST_TOKEN}'}
        
        print("发送列表请求...")
        response = requests.get(f"{BASE_URL}/api/v1/audio/", headers=headers, timeout=10)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {response.headers}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            print("✓ 音频列表获取成功")
            return True
        else:
            print(f"✗ HTTP错误: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"✗ 列表异常: {e}")
        return False

def test_auth_required():
    """测试是否需要认证"""
    print("\n=== 测试认证要求 ===")
    
    try:
        # 不带认证头的请求
        response = requests.get(f"{BASE_URL}/api/v1/audio/", timeout=5)
        print(f"无认证请求状态码: {response.status_code}")
        print(f"无认证响应: {response.text}")
        
        # 带错误认证头的请求
        headers = {'Authorization': 'Bearer wrong_token'}
        response = requests.get(f"{BASE_URL}/api/v1/audio/", headers=headers, timeout=5)
        print(f"错误token状态码: {response.status_code}")
        print(f"错误token响应: {response.text}")
        
    except Exception as e:
        print(f"认证测试异常: {e}")

if __name__ == "__main__":
    test_auth_required()
    file_id = test_audio_upload_detailed()
    test_audio_list_detailed()
    
    if file_id:
        print(f"\n🎉 上传成功，文件ID: {file_id}")
    else:
        print("\n⚠️ 上传失败，需要检查认证或API配置") 