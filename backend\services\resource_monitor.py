"""
系统资源监控服务
监控CPU、内存、磁盘等系统资源使用情况
"""

import psutil
import time
import asyncio
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from datetime import datetime, timezone, timedelta
from loguru import logger
import redis
import json


@dataclass
class ResourceMetrics:
    """资源指标数据类"""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_available_mb: float
    disk_usage_percent: float
    disk_free_gb: float
    active_processes: int
    load_average: Optional[List[float]] = None  # Linux/Mac only
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "timestamp": self.timestamp.isoformat(),
            "cpu_percent": self.cpu_percent,
            "memory_percent": self.memory_percent,
            "memory_available_mb": self.memory_available_mb,
            "disk_usage_percent": self.disk_usage_percent,
            "disk_free_gb": self.disk_free_gb,
            "active_processes": self.active_processes,
            "load_average": self.load_average
        }


@dataclass
class ResourceThresholds:
    """资源阈值配置"""
    cpu_warning: float = 85.0
    cpu_critical: float = 98.0
    memory_warning: float = 85.0
    memory_critical: float = 98.0
    disk_warning: float = 90.0
    disk_critical: float = 98.0
    min_free_memory_mb: float = 256.0
    min_free_disk_gb: float = 0.5


class ResourceMonitor:
    """系统资源监控器"""
    
    def __init__(self, redis_url: str = None):
        self.redis_client = None
        if redis_url:
            try:
                self.redis_client = redis.Redis.from_url(redis_url, decode_responses=True)
                self.redis_client.ping()
            except Exception as e:
                logger.warning(f"Redis连接失败，将使用内存存储: {e}")
                self.redis_client = None
        
        self.thresholds = ResourceThresholds()
        self.metrics_history: List[ResourceMetrics] = []
        self.max_history_size = 1000
        self.monitoring = False
        self.monitor_task = None
    
    def get_current_metrics(self) -> ResourceMetrics:
        """获取当前系统资源指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # 内存信息
            memory = psutil.virtual_memory()
            memory_percent = memory.percent
            memory_available_mb = memory.available / (1024 * 1024)
            
            # 磁盘信息
            disk = psutil.disk_usage('/')
            disk_usage_percent = disk.percent
            disk_free_gb = disk.free / (1024 * 1024 * 1024)
            
            # 进程数量
            active_processes = len(psutil.pids())
            
            # 负载平均值（Linux/Mac）
            load_average = None
            try:
                load_average = list(psutil.getloadavg())
            except AttributeError:
                # Windows不支持getloadavg
                pass
            
            return ResourceMetrics(
                timestamp=datetime.now(timezone.utc),
                cpu_percent=cpu_percent,
                memory_percent=memory_percent,
                memory_available_mb=memory_available_mb,
                disk_usage_percent=disk_usage_percent,
                disk_free_gb=disk_free_gb,
                active_processes=active_processes,
                load_average=load_average
            )
            
        except Exception as e:
            logger.error(f"获取系统资源指标失败: {e}")
            raise
    
    def check_resource_health(self, metrics: ResourceMetrics) -> Dict[str, Any]:
        """检查资源健康状态"""
        health_status = {
            "overall": "healthy",
            "warnings": [],
            "critical": [],
            "recommendations": []
        }
        
        # 检查CPU
        if metrics.cpu_percent >= self.thresholds.cpu_critical:
            health_status["critical"].append(f"CPU使用率过高: {metrics.cpu_percent:.1f}%")
            health_status["overall"] = "critical"
        elif metrics.cpu_percent >= self.thresholds.cpu_warning:
            health_status["warnings"].append(f"CPU使用率较高: {metrics.cpu_percent:.1f}%")
            if health_status["overall"] == "healthy":
                health_status["overall"] = "warning"
        
        # 检查内存
        if metrics.memory_percent >= self.thresholds.memory_critical:
            health_status["critical"].append(f"内存使用率过高: {metrics.memory_percent:.1f}%")
            health_status["overall"] = "critical"
        elif metrics.memory_percent >= self.thresholds.memory_warning:
            health_status["warnings"].append(f"内存使用率较高: {metrics.memory_percent:.1f}%")
            if health_status["overall"] == "healthy":
                health_status["overall"] = "warning"
        
        if metrics.memory_available_mb < self.thresholds.min_free_memory_mb:
            health_status["critical"].append(f"可用内存不足: {metrics.memory_available_mb:.1f}MB")
            health_status["overall"] = "critical"
        
        # 检查磁盘
        if metrics.disk_usage_percent >= self.thresholds.disk_critical:
            health_status["critical"].append(f"磁盘使用率过高: {metrics.disk_usage_percent:.1f}%")
            health_status["overall"] = "critical"
        elif metrics.disk_usage_percent >= self.thresholds.disk_warning:
            health_status["warnings"].append(f"磁盘使用率较高: {metrics.disk_usage_percent:.1f}%")
            if health_status["overall"] == "healthy":
                health_status["overall"] = "warning"
        
        if metrics.disk_free_gb < self.thresholds.min_free_disk_gb:
            health_status["critical"].append(f"磁盘剩余空间不足: {metrics.disk_free_gb:.1f}GB")
            health_status["overall"] = "critical"
        
        # 生成建议
        if health_status["overall"] == "critical":
            health_status["recommendations"].append("立即停止新任务提交")
            health_status["recommendations"].append("清理临时文件和日志")
            health_status["recommendations"].append("考虑增加系统资源")
        elif health_status["overall"] == "warning":
            health_status["recommendations"].append("减少并发任务数量")
            health_status["recommendations"].append("监控资源使用趋势")
        
        return health_status
    
    def store_metrics(self, metrics: ResourceMetrics):
        """存储资源指标"""
        try:
            # 存储到Redis
            if self.redis_client:
                key = f"resource_metrics:{int(metrics.timestamp.timestamp())}"
                self.redis_client.setex(key, 3600, json.dumps(metrics.to_dict()))  # 1小时过期
                
                # 维护最近指标列表
                recent_key = "resource_metrics:recent"
                self.redis_client.lpush(recent_key, key)
                self.redis_client.ltrim(recent_key, 0, self.max_history_size - 1)
                self.redis_client.expire(recent_key, 3600)
            
            # 存储到内存
            self.metrics_history.append(metrics)
            if len(self.metrics_history) > self.max_history_size:
                self.metrics_history.pop(0)
                
        except Exception as e:
            logger.error(f"存储资源指标失败: {e}")
    
    def get_recent_metrics(self, count: int = 60) -> List[ResourceMetrics]:
        """获取最近的资源指标"""
        try:
            if self.redis_client:
                # 从Redis获取
                recent_key = "resource_metrics:recent"
                keys = self.redis_client.lrange(recent_key, 0, count - 1)
                
                metrics = []
                for key in keys:
                    data = self.redis_client.get(key)
                    if data:
                        metric_dict = json.loads(data)
                        metric_dict["timestamp"] = datetime.fromisoformat(metric_dict["timestamp"])
                        metrics.append(ResourceMetrics(**metric_dict))
                
                return metrics
            else:
                # 从内存获取
                return self.metrics_history[-count:] if self.metrics_history else []
                
        except Exception as e:
            logger.error(f"获取历史指标失败: {e}")
            return []
    
    def get_resource_summary(self, minutes: int = 60) -> Dict[str, Any]:
        """获取资源使用摘要"""
        try:
            recent_metrics = self.get_recent_metrics(minutes)
            
            if not recent_metrics:
                return {"error": "没有可用的历史数据"}
            
            # 计算统计信息
            cpu_values = [m.cpu_percent for m in recent_metrics]
            memory_values = [m.memory_percent for m in recent_metrics]
            disk_values = [m.disk_usage_percent for m in recent_metrics]
            
            summary = {
                "period_minutes": minutes,
                "sample_count": len(recent_metrics),
                "cpu": {
                    "current": cpu_values[-1] if cpu_values else 0,
                    "average": sum(cpu_values) / len(cpu_values) if cpu_values else 0,
                    "max": max(cpu_values) if cpu_values else 0,
                    "min": min(cpu_values) if cpu_values else 0
                },
                "memory": {
                    "current": memory_values[-1] if memory_values else 0,
                    "average": sum(memory_values) / len(memory_values) if memory_values else 0,
                    "max": max(memory_values) if memory_values else 0,
                    "min": min(memory_values) if memory_values else 0
                },
                "disk": {
                    "current": disk_values[-1] if disk_values else 0,
                    "average": sum(disk_values) / len(disk_values) if disk_values else 0,
                    "max": max(disk_values) if disk_values else 0,
                    "min": min(disk_values) if disk_values else 0
                }
            }
            
            # 添加当前健康状态
            if recent_metrics:
                current_health = self.check_resource_health(recent_metrics[-1])
                summary["health"] = current_health
            
            return summary
            
        except Exception as e:
            logger.error(f"获取资源摘要失败: {e}")
            return {"error": str(e)}
    
    async def start_monitoring(self, interval: int = 30):
        """开始资源监控"""
        if self.monitoring:
            logger.warning("资源监控已在运行")
            return
        
        self.monitoring = True
        logger.info(f"开始资源监控，间隔: {interval}秒")
        
        try:
            while self.monitoring:
                # 获取当前指标
                metrics = self.get_current_metrics()
                
                # 存储指标
                self.store_metrics(metrics)
                
                # 检查健康状态
                health = self.check_resource_health(metrics)
                
                # 记录警告和严重问题
                if health["warnings"]:
                    logger.warning(f"资源警告: {', '.join(health['warnings'])}")
                
                if health["critical"]:
                    logger.error(f"资源严重问题: {', '.join(health['critical'])}")
                
                # 等待下次检查
                await asyncio.sleep(interval)
                
        except Exception as e:
            logger.error(f"资源监控出错: {e}")
        finally:
            self.monitoring = False
            logger.info("资源监控已停止")
    
    def stop_monitoring(self):
        """停止资源监控"""
        self.monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
    
    def is_resource_available(self, required_memory_mb: float = 100) -> bool:
        """检查是否有足够的资源执行新任务"""
        try:
            current_metrics = self.get_current_metrics()
            
            # 检查内存
            if current_metrics.memory_available_mb < required_memory_mb:
                return False
            
            # 检查CPU（如果CPU使用率过高，不接受新任务）
            if current_metrics.cpu_percent > self.thresholds.cpu_critical:
                return False
            
            # 检查磁盘空间
            if current_metrics.disk_free_gb < self.thresholds.min_free_disk_gb:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"检查资源可用性失败: {e}")
            return False


# 全局资源监控器实例
resource_monitor = ResourceMonitor()


def get_resource_monitor() -> ResourceMonitor:
    """获取资源监控器实例"""
    return resource_monitor
