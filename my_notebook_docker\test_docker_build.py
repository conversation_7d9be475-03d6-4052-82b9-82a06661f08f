#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Docker构建测试脚本
验证Dockerfile语法和构建过程
"""

import os
import sys
import subprocess
import time

def log_info(message):
    print(f"[INFO] {message}")

def log_success(message):
    print(f"[SUCCESS] ✅ {message}")

def log_warning(message):
    print(f"[WARNING] ⚠️ {message}")

def log_error(message):
    print(f"[ERROR] ❌ {message}")

def check_required_files():
    """检查构建所需的文件"""
    log_info("检查构建所需的文件...")
    
    required_files = [
        ".venv/",
        "frontend/package.json",
        "backend/main.py",
        "config/",
        "utils/",
        "requirements.txt"
    ]
    
    missing_files = []
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
            log_error(f"缺少必需文件: {file_path}")
        else:
            log_success(f"找到必需文件: {file_path}")
    
    return len(missing_files) == 0

def test_dockerfile_syntax():
    """测试Dockerfile语法"""
    log_info("测试Dockerfile语法...")

    dockerfiles = [
        "my_notebook_docker/Dockerfile.frontend",
        "my_notebook_docker/Dockerfile.backend",
        "my_notebook_docker/Dockerfile.celery"
    ]

    for dockerfile in dockerfiles:
        try:
            # 检查文件是否存在
            if not os.path.exists(dockerfile):
                log_error(f"Dockerfile不存在: {dockerfile}")
                return False

            # 简单的语法检查 - 读取文件并检查基本语法
            with open(dockerfile, 'r', encoding='utf-8') as f:
                content = f.read()

            # 检查基本的Dockerfile指令
            required_instructions = ['FROM', 'WORKDIR']
            for instruction in required_instructions:
                if instruction not in content:
                    log_error(f"{dockerfile} 缺少必需指令: {instruction}")
                    return False

            # 检查是否有明显的语法错误
            lines = content.split('\n')
            for line in lines:
                line = line.strip()
                if line and not line.startswith('#'):
                    # 检查指令是否以大写字母开头（Docker指令）
                    if line and not (line.split()[0].isupper() or line.startswith('RUN') or line.startswith('COPY')):
                        # 可能是shell脚本内容，检查是否在RUN指令内
                        continue

            log_success(f"{dockerfile} 基本语法检查通过")

        except Exception as e:
            log_error(f"检查{dockerfile}时出错: {e}")
            return False

    return True

def test_build_context():
    """测试构建上下文"""
    log_info("测试构建上下文...")

    try:
        # 检查.dockerignore文件是否存在
        if os.path.exists('.dockerignore'):
            log_success("找到.dockerignore文件")

            # 检查必需目录是否存在
            required_dirs = ['frontend/', 'backend/', 'config/', 'utils/', '.venv/']
            for dir_name in required_dirs:
                if os.path.exists(dir_name):
                    log_success(f"构建上下文包含必需目录: {dir_name}")
                else:
                    log_error(f"构建上下文缺少必需目录: {dir_name}")
                    return False
        else:
            log_warning(".dockerignore文件不存在")

        return True

    except Exception as e:
        log_warning(f"构建上下文测试失败: {e}")
        return True

def test_quick_build():
    """快速构建测试（检查Docker环境）"""
    log_info("检查Docker环境...")

    try:
        # 检查Docker是否可用
        result = subprocess.run([
            'docker', '--version'
        ], capture_output=True, text=True, timeout=10, encoding='utf-8', errors='ignore')

        if result.returncode == 0:
            log_success(f"Docker环境正常: {result.stdout.strip()}")

            # 检查docker-compose是否可用
            result = subprocess.run([
                'docker-compose', '--version'
            ], capture_output=True, text=True, timeout=10, encoding='utf-8', errors='ignore')

            if result.returncode == 0:
                log_success(f"Docker Compose环境正常: {result.stdout.strip()}")
                return True
            else:
                log_warning("Docker Compose不可用，但Docker可用")
                return True
        else:
            log_error("Docker不可用")
            return False

    except subprocess.TimeoutExpired:
        log_warning("Docker环境检查超时")
        return False
    except FileNotFoundError:
        log_warning("Docker命令未找到")
        return False
    except Exception as e:
        log_error(f"Docker环境检查失败: {e}")
        return False

def main():
    """主测试流程"""
    print("🔍 Docker构建测试")
    print("=" * 50)
    
    # 切换到项目根目录
    os.chdir("..")
    
    tests = [
        ("检查必需文件", check_required_files),
        ("测试Dockerfile语法", test_dockerfile_syntax),
        ("测试构建上下文", test_build_context),
        ("检查Docker环境", test_quick_build)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            log_error(f"测试 {test_name} 异常: {e}")
            results[test_name] = False
    
    # 显示总结
    print(f"\n{'='*50}")
    print(f"📊 测试总结:")
    
    passed = sum(1 for v in results.values() if v)
    total = len(results)
    
    print(f"通过测试: {passed}/{total}")
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    if passed == total:
        print(f"\n🎉 Docker构建测试通过！")
        print(f"✅ 可以安全执行 docker-compose build")
        return True
    else:
        print(f"\n❌ Docker构建测试失败")
        print(f"请修复失败的测试项目后再构建")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
