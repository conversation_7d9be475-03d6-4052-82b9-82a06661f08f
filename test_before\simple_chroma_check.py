#!/usr/bin/env python3
import os
import chromadb

def main():
    print("🔍 ChromaDB位置调查")
    print("=" * 50)
    
    # 1. 检查配置路径
    config_path = "./chroma_db"
    print(f"配置路径: {config_path}")
    print(f"绝对路径: {os.path.abspath(config_path)}")
    print(f"目录存在: {os.path.exists(config_path)}")
    print()
    
    # 2. 尝试连接ChromaDB
    try:
        client = chromadb.PersistentClient(path=config_path)
        collections = client.list_collections()
        print(f"找到 {len(collections)} 个集合:")
        
        for collection in collections:
            print(f"  - {collection.name}")
            print(f"    ID: {collection.id}")
            print(f"    文档数量: {collection.count()}")
            
            # 如果有文档，显示一些样本
            if collection.count() > 0:
                try:
                    results = collection.peek(limit=1)
                    if results and 'metadatas' in results and results['metadatas']:
                        metadata = results['metadatas'][0]
                        if metadata and 'filename' in metadata:
                            print(f"    样本文件: {metadata['filename']}")
                except:
                    pass
            print()
            
    except Exception as e:
        print(f"连接ChromaDB失败: {str(e)}")
        print()
    
    # 3. 搜索可能的ChromaDB文件
    print("搜索ChromaDB相关文件...")
    found_files = []
    
    for root, dirs, files in os.walk("."):
        # 跳过太深的目录
        if root.count(os.sep) - ".".count(os.sep) > 3:
            continue
            
        for file in files:
            if any(pattern in file.lower() for pattern in ['chroma', 'sqlite']):
                found_files.append(os.path.join(root, file))
        
        for dir_name in dirs:
            if 'chroma' in dir_name.lower():
                found_files.append(os.path.join(root, dir_name))
    
    if found_files:
        print("找到的相关文件/目录:")
        for f in found_files:
            print(f"  {f}")
    else:
        print("未找到ChromaDB相关文件")
    
    print()
    print("=" * 50)
    print("结论:")
    if not os.path.exists(config_path):
        print("1. 配置的chroma_db目录不存在")
        print("2. 可能数据存储在其他位置或内存中")
    else:
        print("1. chroma_db目录存在")
        print("2. 检查上述集合信息")

if __name__ == "__main__":
    main() 