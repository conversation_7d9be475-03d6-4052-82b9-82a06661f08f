#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
异常处理框架测试脚本
测试各种异常类型、恢复机制、日志记录等功能
"""

import os
import sys
import tempfile
import time
from pathlib import Path

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from utils.exception_handler import (
        ExceptionHandler, ErrorCategory, ErrorSeverity,
        AudioProcessingError, ModelLoadingError, FileOperationError,
        CustomMemoryError, GPUError, ValidationError, ConfigurationError,
        exception_handler_decorator, create_custom_exception,
        handle_streamlit_exception, exception_handler
    )
    print("✓ 成功导入异常处理框架")
except ImportError as e:
    print(f"✗ 导入失败: {e}")
    sys.exit(1)


def test_exception_classes():
    """测试各种异常类型"""
    print("\n=== 测试异常类型 ===")
    
    # 测试音频处理异常
    try:
        raise AudioProcessingError(
            "音频文件格式不支持",
            context={"file_path": "test.mp3", "format": "mp3"},
            suggestion="请使用WAV或FLAC格式"
        )
    except AudioProcessingError as e:
        print(f"✓ 音频处理异常: {e}")
        print(f"  错误代码: {e.error_code}")
        print(f"  严重程度: {e.severity.value}")
        print(f"  建议: {e.suggestion}")
    
    # 测试模型加载异常
    try:
        raise ModelLoadingError(
            "模型文件丢失",
            context={"model_path": "/path/to/model.bin"},
            recoverable=False
        )
    except ModelLoadingError as e:
        print(f"✓ 模型加载异常: {e}")
        print(f"  可恢复: {e.recoverable}")
    
    # 测试文件操作异常
    try:
        raise FileOperationError(
            "磁盘空间不足",
            context={"required_space": "1GB", "available_space": "100MB"}
        )
    except FileOperationError as e:
        print(f"✓ 文件操作异常: {e}")
    
    # 测试内存异常
    try:
        raise MemoryError(
            "内存不足，无法处理大型音频文件",
            context={"file_size": "500MB", "available_memory": "200MB"}
        )
    except MemoryError as e:
        print(f"✓ 内存异常: {e}")
        print(f"  建议: {e.suggestion}")
    
    # 测试GPU异常
    try:
        raise GPUError(
            "CUDA内存溢出",
            context={"gpu_memory": "8GB", "required_memory": "12GB"}
        )
    except GPUError as e:
        print(f"✓ GPU异常: {e}")
        print(f"  建议: {e.suggestion}")
    
    # 测试验证异常
    try:
        raise ValidationError(
            "音频采样率不匹配",
            context={"expected": 16000, "actual": 44100}
        )
    except ValidationError as e:
        print(f"✓ 验证异常: {e}")
    
    print("所有异常类型测试完成")


def test_exception_handler():
    """测试异常处理器"""
    print("\n=== 测试异常处理器 ===")
    
    # 创建临时日志文件
    with tempfile.TemporaryDirectory() as temp_dir:
        log_file = os.path.join(temp_dir, "test_exceptions.log")
        test_handler = ExceptionHandler(log_file)
        
        # 测试处理自定义异常
        try:
            raise AudioProcessingError("测试音频异常")
        except Exception as e:
            recovery_success = test_handler.handle_exception(e)
            print(f"✓ 处理音频异常，恢复成功: {recovery_success}")
        
        # 测试处理标准异常
        try:
            raise FileNotFoundError("测试文件未找到")
        except Exception as e:
            recovery_success = test_handler.handle_exception(e)
            print(f"✓ 处理标准异常，恢复成功: {recovery_success}")
        
        # 测试安全执行
        def failing_function():
            raise ValueError("这是一个测试错误")
        
        result = test_handler.safe_execute(
            failing_function,
            fallback_result="默认结果",
            context={"test": "safe_execute"}
        )
        print(f"✓ 安全执行返回: {result}")
        
        # 检查日志文件是否创建
        if os.path.exists(log_file):
            print(f"✓ 日志文件创建成功: {log_file}")
            with open(log_file, 'r', encoding='utf-8') as f:
                log_content = f.read()
                print(f"  日志长度: {len(log_content)} 字符")
        else:
            print("✗ 日志文件未创建")


def test_decorator():
    """测试异常处理装饰器"""
    print("\n=== 测试异常处理装饰器 ===")
    
    @exception_handler_decorator(
        fallback_result="装饰器默认结果",
        category=ErrorCategory.AUDIO_PROCESSING,
        max_retries=2
    )
    def test_decorated_function(should_fail=True):
        if should_fail:
            raise RuntimeError("装饰器测试异常")
        return "正常执行结果"
    
    # 测试失败情况
    result1 = test_decorated_function(should_fail=True)
    print(f"✓ 装饰器失败处理: {result1}")
    
    # 测试成功情况
    result2 = test_decorated_function(should_fail=False)
    print(f"✓ 装饰器成功执行: {result2}")


def test_recovery_mechanism():
    """测试恢复机制"""
    print("\n=== 测试恢复机制 ===")
    
    # 测试内存恢复
    memory_exception = MemoryError(
        "测试内存不足",
        context={"operation": "batch_processing"}
    )
    
    recovery_success = exception_handler.recovery_manager.attempt_recovery(memory_exception)
    print(f"✓ 内存异常恢复测试: {recovery_success}")
    
    # 测试不可恢复异常
    model_exception = ModelLoadingError(
        "模型文件损坏",
        recoverable=False
    )
    
    recovery_success = exception_handler.recovery_manager.attempt_recovery(model_exception)
    print(f"✓ 不可恢复异常测试: {recovery_success}")


def test_retry_mechanism():
    """测试重试机制"""
    print("\n=== 测试重试机制 ===")
    
    attempt_count = 0
    
    def flaky_function():
        nonlocal attempt_count
        attempt_count += 1
        if attempt_count < 3:
            raise RuntimeError(f"尝试 {attempt_count} 失败")
        return f"成功执行，尝试次数: {attempt_count}"
    
    try:
        result = exception_handler.recovery_manager.retry_with_backoff(
            flaky_function,
            max_retries=3
        )
        print(f"✓ 重试机制测试: {result}")
    except Exception as e:
        print(f"✗ 重试机制失败: {e}")


def test_streamlit_exception_handling():
    """测试Streamlit异常处理"""
    print("\n=== 测试Streamlit异常处理 ===")
    
    # 测试标准异常
    try:
        raise FileNotFoundError("文件不存在")
    except Exception as e:
        result = handle_streamlit_exception(
            e,
            user_message="文件上传失败",
            show_details=True
        )
        print("✓ Streamlit异常处理结果:")
        for key, value in result.items():
            print(f"  {key}: {value}")


def test_custom_exception_creation():
    """测试自定义异常创建"""
    print("\n=== 测试自定义异常创建 ===")
    
    # 使用便捷函数创建异常
    exception = create_custom_exception(
        "自定义GPU错误",
        ErrorCategory.GPU_ERROR,
        ErrorSeverity.HIGH,
        context={"gpu_id": 0, "memory_usage": "90%"},
        suggestion="请减少批处理大小或使用CPU模式"
    )
    
    print(f"✓ 创建自定义异常: {exception}")
    print(f"  错误代码: {exception.error_code}")
    print(f"  类别: {exception.category.value}")
    print(f"  严重程度: {exception.severity.value}")
    print(f"  建议: {exception.suggestion}")


def test_exception_serialization():
    """测试异常序列化"""
    print("\n=== 测试异常序列化 ===")
    
    exception = AudioProcessingError(
        "音频预处理失败",
        context={
            "file_path": "test_audio.wav",
            "sample_rate": 16000,
            "duration": 120.5
        },
        suggestion="请检查音频文件完整性"
    )
    
    # 转换为字典
    exception_dict = exception.to_dict()
    print("✓ 异常序列化结果:")
    for key, value in exception_dict.items():
        print(f"  {key}: {value}")


def test_system_info():
    """测试系统信息获取"""
    print("\n=== 测试系统信息获取 ===")
    
    try:
        logger = exception_handler.logger
        system_info = logger._get_system_info()
        print("✓ 系统信息:")
        for key, value in system_info.items():
            print(f"  {key}: {value}")
    except Exception as e:
        print(f"✗ 获取系统信息失败: {e}")


def run_all_tests():
    """运行所有测试"""
    print("开始异常处理框架测试...")
    
    tests = [
        test_exception_classes,
        test_exception_handler,
        test_decorator,
        test_recovery_mechanism,
        test_retry_mechanism,
        test_streamlit_exception_handling,
        test_custom_exception_creation,
        test_exception_serialization,
        test_system_info
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            test_func()
            passed += 1
            print(f"✓ {test_func.__name__} 通过")
        except Exception as e:
            print(f"✗ {test_func.__name__} 失败: {e}")
    
    print(f"\n=== 测试结果 ===")
    print(f"总测试数: {total}")
    print(f"通过: {passed}")
    print(f"失败: {total - passed}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    return passed == total


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)