"""
并发控制服务
管理任务队列的并发执行，防止系统资源耗尽
"""

import asyncio
import time
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from datetime import datetime, timezone, timedelta
from loguru import logger
import redis
import json

from backend.services.resource_monitor import get_resource_monitor
from backend.services.task_persistence_service import get_task_persistence_service
from backend.core.database import get_db_session
from backend.models.task_models import TaskStatus


@dataclass
class QueueConfig:
    """队列配置"""
    name: str
    max_concurrent: int
    priority: int = 1
    resource_weight: float = 1.0  # 资源权重
    enabled: bool = True


@dataclass
class ConcurrencyLimits:
    """并发限制配置"""
    max_total_tasks: int = 10
    max_per_user: int = 3
    max_per_queue: Dict[str, int] = None
    resource_based_scaling: bool = True
    
    def __post_init__(self):
        if self.max_per_queue is None:
            self.max_per_queue = {
                "document_processing": 5,
                "vectorization": 5,
                "ocr_processing": 5,
                "default": 5
            }


class ConcurrencyController:
    """并发控制器"""

    def __init__(self, redis_url: str = None):
        self.redis_client = None
        if redis_url:
            self._connect_redis(redis_url)

    def _connect_redis(self, redis_url: str, max_retries: int = 3):
        """连接Redis，支持重试机制"""
        for attempt in range(max_retries):
            try:
                logger.info(f"尝试连接Redis (第{attempt + 1}次): {redis_url}")
                self.redis_client = redis.Redis.from_url(
                    redis_url,
                    decode_responses=True,
                    socket_connect_timeout=5,
                    socket_timeout=5,
                    retry_on_timeout=True
                )
                self.redis_client.ping()
                logger.info("✅ Redis连接成功")
                return
            except Exception as e:
                logger.warning(f"Redis连接失败 (第{attempt + 1}次): {e}")
                if attempt < max_retries - 1:
                    import time
                    time.sleep(2 ** attempt)  # 指数退避
                else:
                    logger.warning("Redis连接最终失败，将使用内存存储")
                    self.redis_client = None
        
        self.limits = ConcurrencyLimits()
        self.resource_monitor = get_resource_monitor()
        self.persistence_service = get_task_persistence_service()
        
        # 队列配置
        self.queue_configs = {
            "document_processing": QueueConfig("document_processing", 5, 2, 1.5),
            "vectorization": QueueConfig("vectorization", 5, 1, 2.0),
            "ocr_processing": QueueConfig("ocr_processing", 5, 2, 1.2),
            "default": QueueConfig("default", 5, 3, 1.0)
        }
        
        # 运行时状态
        self.active_tasks: Dict[str, Dict] = {}  # task_id -> task_info
        self.queue_counters: Dict[str, int] = {}  # queue_name -> count
        self.user_counters: Dict[str, int] = {}   # user_id -> count
    
    def _get_redis_key(self, key_type: str, identifier: str = "") -> str:
        """生成Redis键"""
        if identifier:
            return f"concurrency:{key_type}:{identifier}"
        return f"concurrency:{key_type}"
    
    def _update_counters_from_redis(self):
        """从Redis更新计数器并自动清理无效任务"""
        if not self.redis_client:
            return

        try:
            # 自动清理过期任务
            self._auto_cleanup_stale_tasks()

            # 更新队列计数器
            for queue_name in self.queue_configs.keys():
                key = self._get_redis_key("queue", queue_name)
                count = self.redis_client.scard(key)
                self.queue_counters[queue_name] = count

            # 更新用户计数器
            pattern = self._get_redis_key("user", "*")
            keys = self.redis_client.keys(pattern)
            self.user_counters.clear()

            for key in keys:
                user_id = key.split(":")[-1]
                count = self.redis_client.scard(key)
                self.user_counters[user_id] = count

        except Exception as e:
            logger.error(f"从Redis更新计数器失败: {e}")

    def _auto_cleanup_stale_tasks(self):
        """自动清理无效的任务记录"""
        try:
            # 获取所有任务ID
            total_key = self._get_redis_key("total")
            task_ids = self.redis_client.smembers(total_key)

            if not task_ids:
                return

            # 检查每个任务的状态
            stale_tasks = []
            for task_id in task_ids:
                # 检查Celery任务状态
                celery_task_key = f"celery-task-meta-{task_id}"
                celery_status = self.redis_client.get(celery_task_key)

                if celery_status:
                    try:
                        status_data = json.loads(celery_status)
                        task_status = status_data.get("status")

                        # 如果任务已完成、失败或被撤销，则清理
                        if task_status in ["SUCCESS", "FAILURE", "REVOKED"]:
                            stale_tasks.append(task_id)
                    except:
                        # 如果无法解析状态，也认为是无效任务
                        stale_tasks.append(task_id)
                else:
                    # 如果Celery中没有任务记录，也认为是无效任务
                    stale_tasks.append(task_id)

            # 清理无效任务
            for task_id in stale_tasks:
                self.unregister_task(task_id)

            if stale_tasks:
                logger.info(f"自动清理了 {len(stale_tasks)} 个无效任务: {stale_tasks}")

        except Exception as e:
            logger.error(f"自动清理无效任务失败: {e}")
    
    def get_current_concurrency(self) -> Dict[str, Any]:
        """获取当前并发状态"""
        self._update_counters_from_redis()
        
        total_active = sum(self.queue_counters.values())
        
        return {
            "total_active_tasks": total_active,
            "queue_counters": self.queue_counters.copy(),
            "user_counters": self.user_counters.copy(),
            "limits": {
                "max_total": self.limits.max_total_tasks,
                "max_per_user": self.limits.max_per_user,
                "max_per_queue": self.limits.max_per_queue.copy()
            },
            "queue_configs": {
                name: {
                    "max_concurrent": config.max_concurrent,
                    "priority": config.priority,
                    "enabled": config.enabled
                }
                for name, config in self.queue_configs.items()
            }
        }
    
    def can_accept_task(
        self,
        user_id: str,
        queue_name: str,
        required_memory_mb: float = 100
    ) -> Dict[str, Any]:
        """检查是否可以接受新任务"""
        result = {
            "can_accept": False,
            "reason": "",
            "current_state": {},
            "recommendations": []
        }
        
        try:
            # 更新当前状态
            self._update_counters_from_redis()
            current_state = self.get_current_concurrency()
            result["current_state"] = current_state
            
            # 检查队列是否启用
            if queue_name not in self.queue_configs:
                result["reason"] = f"未知的队列: {queue_name}"
                return result
            
            queue_config = self.queue_configs[queue_name]
            if not queue_config.enabled:
                result["reason"] = f"队列已禁用: {queue_name}"
                return result
            
            # 检查总任务数限制
            total_active = current_state["total_active_tasks"]
            if total_active >= self.limits.max_total_tasks:
                result["reason"] = f"总任务数已达上限: {total_active}/{self.limits.max_total_tasks}"
                result["recommendations"].append("等待现有任务完成")
                return result
            
            # 检查用户任务数限制
            user_active = self.user_counters.get(user_id, 0)
            if user_active >= self.limits.max_per_user:
                result["reason"] = f"用户任务数已达上限: {user_active}/{self.limits.max_per_user}"
                result["recommendations"].append("等待您的其他任务完成")
                return result
            
            # 检查队列任务数限制
            queue_active = self.queue_counters.get(queue_name, 0)
            queue_limit = self.limits.max_per_queue.get(queue_name, queue_config.max_concurrent)
            if queue_active >= queue_limit:
                result["reason"] = f"队列任务数已达上限: {queue_active}/{queue_limit}"
                result["recommendations"].append(f"等待{queue_name}队列中的任务完成")
                return result
            
            # 检查系统资源
            if self.limits.resource_based_scaling:
                if not self.resource_monitor.is_resource_available(required_memory_mb):
                    result["reason"] = "系统资源不足"
                    result["recommendations"].extend([
                        "等待系统资源释放",
                        "考虑减少并发任务数量"
                    ])
                    return result
            
            # 所有检查通过
            result["can_accept"] = True
            result["reason"] = "可以接受新任务"
            
            return result
            
        except Exception as e:
            logger.error(f"检查任务接受条件失败: {e}")
            result["reason"] = f"检查失败: {str(e)}"
            return result
    
    def register_task(
        self,
        task_id: str,
        user_id: str,
        queue_name: str,
        task_info: Dict[str, Any] = None
    ) -> bool:
        """注册新任务"""
        try:
            task_data = {
                "task_id": task_id,
                "user_id": user_id,
                "queue_name": queue_name,
                "start_time": time.time(),
                "info": task_info or {}
            }
            
            # 存储到Redis
            if self.redis_client:
                # 添加到总任务集合
                total_key = self._get_redis_key("total")
                self.redis_client.sadd(total_key, task_id)
                self.redis_client.expire(total_key, 3600)
                
                # 添加到队列集合
                queue_key = self._get_redis_key("queue", queue_name)
                self.redis_client.sadd(queue_key, task_id)
                self.redis_client.expire(queue_key, 3600)
                
                # 添加到用户集合
                user_key = self._get_redis_key("user", user_id)
                self.redis_client.sadd(user_key, task_id)
                self.redis_client.expire(user_key, 3600)
                
                # 存储任务详细信息
                task_key = self._get_redis_key("task", task_id)
                self.redis_client.setex(task_key, 3600, json.dumps(task_data))
            
            # 存储到内存
            self.active_tasks[task_id] = task_data
            
            # 更新计数器
            self.queue_counters[queue_name] = self.queue_counters.get(queue_name, 0) + 1
            self.user_counters[user_id] = self.user_counters.get(user_id, 0) + 1
            
            logger.info(f"注册任务: {task_id}, 队列: {queue_name}, 用户: {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"注册任务失败: {task_id}, {e}")
            return False
    
    def unregister_task(self, task_id: str) -> bool:
        """注销任务"""
        try:
            # 从内存获取任务信息
            task_data = self.active_tasks.get(task_id)
            
            if not task_data and self.redis_client:
                # 从Redis获取任务信息
                task_key = self._get_redis_key("task", task_id)
                data = self.redis_client.get(task_key)
                if data:
                    task_data = json.loads(data)
            
            if not task_data:
                logger.warning(f"任务不存在: {task_id}")
                return False
            
            user_id = task_data["user_id"]
            queue_name = task_data["queue_name"]
            
            # 从Redis删除
            if self.redis_client:
                total_key = self._get_redis_key("total")
                self.redis_client.srem(total_key, task_id)
                
                queue_key = self._get_redis_key("queue", queue_name)
                self.redis_client.srem(queue_key, task_id)
                
                user_key = self._get_redis_key("user", user_id)
                self.redis_client.srem(user_key, task_id)
                
                task_key = self._get_redis_key("task", task_id)
                self.redis_client.delete(task_key)
            
            # 从内存删除
            if task_id in self.active_tasks:
                del self.active_tasks[task_id]
            
            # 更新计数器
            if queue_name in self.queue_counters:
                self.queue_counters[queue_name] = max(0, self.queue_counters[queue_name] - 1)
            
            if user_id in self.user_counters:
                self.user_counters[user_id] = max(0, self.user_counters[user_id] - 1)
            
            logger.info(f"注销任务: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"注销任务失败: {task_id}, {e}")
            return False
    
    def adjust_limits_based_on_resources(self):
        """根据系统资源动态调整并发限制"""
        if not self.limits.resource_based_scaling:
            return
        
        try:
            # 获取资源摘要
            resource_summary = self.resource_monitor.get_resource_summary(minutes=5)
            
            if "error" in resource_summary:
                return
            
            # 获取当前资源使用率
            cpu_usage = resource_summary["cpu"]["current"]
            memory_usage = resource_summary["memory"]["current"]
            
            # 计算资源压力
            resource_pressure = max(cpu_usage, memory_usage) / 100.0
            
            # 动态调整并发限制
            if resource_pressure > 0.9:  # 高压力
                scale_factor = 0.5
            elif resource_pressure > 0.8:  # 中等压力
                scale_factor = 0.7
            elif resource_pressure > 0.6:  # 轻微压力
                scale_factor = 0.85
            else:  # 低压力
                scale_factor = 1.0
            
            # 应用缩放因子
            original_total = 10  # 基础最大任务数
            self.limits.max_total_tasks = max(1, int(original_total * scale_factor))
            
            for queue_name, config in self.queue_configs.items():
                original_limit = self.limits.max_per_queue.get(queue_name, config.max_concurrent)
                new_limit = max(1, int(original_limit * scale_factor))
                self.limits.max_per_queue[queue_name] = new_limit
            
            logger.debug(f"根据资源压力调整并发限制: 压力={resource_pressure:.2f}, 缩放={scale_factor:.2f}")
            
        except Exception as e:
            logger.error(f"动态调整并发限制失败: {e}")
    
    def get_queue_statistics(self) -> Dict[str, Any]:
        """获取队列统计信息"""
        try:
            self._update_counters_from_redis()
            
            stats = {
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "queues": {},
                "users": {},
                "total": {
                    "active_tasks": sum(self.queue_counters.values()),
                    "max_tasks": self.limits.max_total_tasks
                }
            }
            
            # 队列统计
            for queue_name, config in self.queue_configs.items():
                active_count = self.queue_counters.get(queue_name, 0)
                max_count = self.limits.max_per_queue.get(queue_name, config.max_concurrent)
                
                stats["queues"][queue_name] = {
                    "active": active_count,
                    "max": max_count,
                    "utilization": (active_count / max_count) if max_count > 0 else 0,
                    "enabled": config.enabled,
                    "priority": config.priority
                }
            
            # 用户统计
            for user_id, count in self.user_counters.items():
                stats["users"][user_id] = {
                    "active": count,
                    "max": self.limits.max_per_user,
                    "utilization": count / self.limits.max_per_user
                }
            
            return stats
            
        except Exception as e:
            logger.error(f"获取队列统计失败: {e}")
            return {"error": str(e)}
    
    def cleanup_stale_tasks(self, max_age_hours: int = 2):
        """清理过期的任务记录"""
        try:
            cutoff_time = time.time() - (max_age_hours * 3600)
            cleaned_count = 0
            
            # 检查内存中的任务
            stale_tasks = []
            for task_id, task_data in self.active_tasks.items():
                if task_data["start_time"] < cutoff_time:
                    stale_tasks.append(task_id)
            
            # 清理过期任务
            for task_id in stale_tasks:
                self.unregister_task(task_id)
                cleaned_count += 1
            
            if cleaned_count > 0:
                logger.info(f"清理了 {cleaned_count} 个过期任务记录")
            
            return cleaned_count
            
        except Exception as e:
            logger.error(f"清理过期任务失败: {e}")
            return 0


# 全局并发控制器实例
import os

def _get_redis_url():
    """获取Redis URL，支持多种环境变量"""
    # 优先级：REDIS_URL > CELERY_BROKER_URL > 默认值
    redis_url = os.getenv("REDIS_URL")
    if not redis_url:
        redis_url = os.getenv("CELERY_BROKER_URL")
    if not redis_url:
        redis_url = "redis://localhost:6379/0"

    logger.info(f"使用Redis URL: {redis_url}")
    return redis_url

REDIS_URL = _get_redis_url()
concurrency_controller = ConcurrencyController(redis_url=REDIS_URL)


def get_concurrency_controller() -> ConcurrencyController:
    """获取并发控制器实例"""
    return concurrency_controller
