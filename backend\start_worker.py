#!/usr/bin/env python
"""
启动Celery Worker的脚本
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
backend_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(backend_root))

# 设置环境变量
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'backend.settings')

from backend.core.task_queue import celery_app

if __name__ == '__main__':
    # 启动Celery Worker
    celery_app.worker_main([
        'worker',
        '--loglevel=info',
        '--concurrency=2',
        '--queues=document_processing,vectorization,ocr_processing,default'
    ])
