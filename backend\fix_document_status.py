#!/usr/bin/env python3
"""
修复文档状态不一致问题的脚本
"""

import sys
import os
from datetime import datetime, timezone

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from backend.core.database import get_db_session
from backend.models.document_management import ManagedDocument, DocumentSection
from backend.services.document_db_service import document_db_service
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_document_status():
    """修复文档状态不一致问题"""
    db = get_db_session()
    try:
        print("=" * 80)
        print("文档状态修复工具")
        print("=" * 80)
        
        # 查找状态不一致的文档
        # 1. 状态为 uploaded 但有节点的文档
        uploaded_docs_with_sections = db.query(ManagedDocument)\
            .filter(ManagedDocument.status == 'uploaded')\
            .filter(ManagedDocument.sections_count > 0)\
            .all()
        
        # 2. 状态为 processing 但有节点的文档
        processing_docs_with_sections = db.query(ManagedDocument)\
            .filter(ManagedDocument.status == 'processing')\
            .filter(ManagedDocument.sections_count > 0)\
            .all()
        
        # 3. 进度为0但有节点的文档
        zero_progress_docs_with_sections = db.query(ManagedDocument)\
            .filter(ManagedDocument.processing_progress == 0.0)\
            .filter(ManagedDocument.sections_count > 0)\
            .all()
        
        all_problematic_docs = set()
        all_problematic_docs.update(uploaded_docs_with_sections)
        all_problematic_docs.update(processing_docs_with_sections)
        all_problematic_docs.update(zero_progress_docs_with_sections)
        
        if not all_problematic_docs:
            print("✅ 没有发现状态不一致的文档")
            return
        
        print(f"发现 {len(all_problematic_docs)} 个状态不一致的文档")
        print()
        
        fixed_count = 0
        
        for doc in all_problematic_docs:
            # 检查实际节点数量
            actual_sections = db.query(DocumentSection)\
                               .filter(DocumentSection.document_id == doc.id)\
                               .count()
            
            print(f"文档ID: {doc.id}")
            print(f"文件名: {doc.filename[:50]}...")
            print(f"当前状态: {doc.status}")
            print(f"当前进度: {doc.processing_progress}")
            print(f"记录的节点数: {doc.sections_count}")
            print(f"实际节点数: {actual_sections}")
            
            if actual_sections > 0:
                print("🔧 修复状态为 'completed'...")
                
                try:
                    # 更新文档状态
                    update_data = {
                        "status": "completed",
                        "processing_progress": 1.0,
                        "processed_at": datetime.now(timezone.utc)
                    }
                    
                    # 如果节点数不匹配，也更新节点数
                    if doc.sections_count != actual_sections:
                        update_data["sections_count"] = actual_sections
                        print(f"  📊 同时修复节点数: {doc.sections_count} -> {actual_sections}")
                    
                    document_db_service.update_document(
                        db, doc.id, doc.user_id, update_data
                    )
                    
                    # 添加处理日志
                    document_db_service.add_processing_log(
                        db, doc.id, "INFO",
                        f"状态修复：自动修复为completed状态（{actual_sections}个节点）",
                        "status_fix"
                    )
                    
                    print("✅ 修复成功")
                    fixed_count += 1
                    
                except Exception as e:
                    print(f"❌ 修复失败: {e}")
            else:
                print("⚠️  没有节点数据，跳过修复")
            
            print("-" * 40)
        
        print(f"\n修复完成！成功修复 {fixed_count} 个文档")
        
    except Exception as e:
        logger.error(f"修复文档状态失败: {e}")
    finally:
        db.close()

def check_and_fix_task_completion():
    """检查并修复任务完成状态"""
    try:
        from backend.models.task_management import TaskRecord
        from backend.core.database import get_db_session
        
        db = get_db_session()
        try:
            print("\n" + "=" * 80)
            print("任务状态检查与修复")
            print("=" * 80)
            
            # 查找状态为PENDING或PROGRESS但实际已完成的任务
            incomplete_tasks = db.query(TaskRecord)\
                .filter(TaskRecord.task_type == 'document_processing')\
                .filter(TaskRecord.status.in_(['PENDING', 'PROGRESS']))\
                .order_by(TaskRecord.created_at.desc())\
                .limit(20)\
                .all()
            
            if not incomplete_tasks:
                print("✅ 没有发现未完成的文档处理任务")
                return
            
            print(f"发现 {len(incomplete_tasks)} 个可能未正确完成的任务")
            
            for task in incomplete_tasks:
                print(f"\n任务ID: {task.task_id}")
                print(f"任务名称: {task.task_name}")
                print(f"状态: {task.status}")
                print(f"进度: {task.progress_percentage}%")
                
                # 检查是否有对应的已完成文档
                # 从任务参数中提取用户ID（如果可能）
                if task.task_args and len(task.task_args) > 1:
                    try:
                        user_id = task.task_args[1]
                        
                        # 查找该用户在任务创建时间附近的已完成文档
                        from datetime import timedelta, timezone
                        time_window = timedelta(minutes=30)
                        
                        completed_docs = db.query(ManagedDocument)\
                            .filter(ManagedDocument.user_id == user_id)\
                            .filter(ManagedDocument.status == 'completed')\
                            .filter(ManagedDocument.created_at >= task.created_at - time_window)\
                            .filter(ManagedDocument.created_at <= task.created_at + time_window)\
                            .all()
                        
                        if completed_docs:
                            print(f"  📄 找到 {len(completed_docs)} 个可能对应的已完成文档")
                            print("  🔧 标记任务为成功...")
                            
                            # 更新任务状态
                            task.status = 'SUCCESS'
                            task.progress_percentage = 100.0
                            task.progress_detail = "任务已完成"
                            task.completed_at = datetime.now(timezone.utc)
                            
                            db.commit()
                            print("  ✅ 任务状态已修复")
                        else:
                            print("  ⚠️  未找到对应的已完成文档")
                    except Exception as e:
                        print(f"  ❌ 处理任务失败: {e}")
                
                print("-" * 40)
                
        finally:
            db.close()
            
    except ImportError:
        print("⚠️  任务管理模块不可用，跳过任务状态检查")
    except Exception as e:
        logger.error(f"检查任务状态失败: {e}")

if __name__ == "__main__":
    print("开始修复文档状态问题...")
    
    # 修复文档状态
    fix_document_status()
    
    # 检查并修复任务状态
    check_and_fix_task_completion()
    
    print("\n修复完成！")
    print("建议重新刷新前端页面以查看修复结果。")
