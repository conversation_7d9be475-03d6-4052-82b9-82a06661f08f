2025-07-05 20:07:19,845 - backend.utils.ocr_utils - INFO - 检测到Tesseract版本: 5.3.0.20221214
2025-07-05 20:07:30,935 - backend.core.gpu_manager - INFO - GPU资源管理器初始化完成，进程ID: 5724
2025-07-05 20:07:32,136 - torio._extension.utils - DEBUG - Loading FFmpeg6
2025-07-05 20:07:32,140 - torio._extension.utils - DEBUG - Failed to load FFmpeg6 extension.
Traceback (most recent call last):
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\torio\_extension\utils.py", line 116, in _find_ffmpeg_extension
    ext = _find_versionsed_ffmpeg_extension(ffmpeg_ver)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\torio\_extension\utils.py", line 108, in _find_versionsed_ffmpeg_extension
    _load_lib(lib)
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\torio\_extension\utils.py", line 94, in _load_lib
    torch.ops.load_library(path)
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\torch\_ops.py", line 1357, in load_library
    ctypes.CDLL(path)
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.11.12-windows-x86_64-none\Lib\ctypes\__init__.py", line 376, in __init__
    self._handle = _dlopen(self._name, mode)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: Could not find module 'D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\torio\lib\libtorio_ffmpeg6.pyd' (or one of its dependencies). Try using the full path with constructor syntax.
2025-07-05 20:07:32,216 - torio._extension.utils - DEBUG - Loading FFmpeg5
2025-07-05 20:07:32,218 - torio._extension.utils - DEBUG - Failed to load FFmpeg5 extension.
Traceback (most recent call last):
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\torio\_extension\utils.py", line 116, in _find_ffmpeg_extension
    ext = _find_versionsed_ffmpeg_extension(ffmpeg_ver)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\torio\_extension\utils.py", line 108, in _find_versionsed_ffmpeg_extension
    _load_lib(lib)
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\torio\_extension\utils.py", line 94, in _load_lib
    torch.ops.load_library(path)
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\torch\_ops.py", line 1357, in load_library
    ctypes.CDLL(path)
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.11.12-windows-x86_64-none\Lib\ctypes\__init__.py", line 376, in __init__
    self._handle = _dlopen(self._name, mode)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: Could not find module 'D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\torio\lib\libtorio_ffmpeg5.pyd' (or one of its dependencies). Try using the full path with constructor syntax.
2025-07-05 20:07:32,218 - torio._extension.utils - DEBUG - Loading FFmpeg4
2025-07-05 20:07:32,224 - torio._extension.utils - DEBUG - Failed to load FFmpeg4 extension.
Traceback (most recent call last):
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\torio\_extension\utils.py", line 116, in _find_ffmpeg_extension
    ext = _find_versionsed_ffmpeg_extension(ffmpeg_ver)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\torio\_extension\utils.py", line 108, in _find_versionsed_ffmpeg_extension
    _load_lib(lib)
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\torio\_extension\utils.py", line 94, in _load_lib
    torch.ops.load_library(path)
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\torch\_ops.py", line 1357, in load_library
    ctypes.CDLL(path)
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.11.12-windows-x86_64-none\Lib\ctypes\__init__.py", line 376, in __init__
    self._handle = _dlopen(self._name, mode)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: Could not find module 'D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\torio\lib\libtorio_ffmpeg4.pyd' (or one of its dependencies). Try using the full path with constructor syntax.
2025-07-05 20:07:32,225 - torio._extension.utils - DEBUG - Loading FFmpeg
2025-07-05 20:07:32,226 - torio._extension.utils - DEBUG - Failed to load FFmpeg extension.
Traceback (most recent call last):
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\torio\_extension\utils.py", line 116, in _find_ffmpeg_extension
    ext = _find_versionsed_ffmpeg_extension(ffmpeg_ver)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\torio\_extension\utils.py", line 106, in _find_versionsed_ffmpeg_extension
    raise RuntimeError(f"FFmpeg{version} extension is not available.")
RuntimeError: FFmpeg extension is not available.
2025-07-05 20:07:35,496 - root - DEBUG - New registry table added: preprocessor_classes
2025-07-05 20:07:36,460 - root - DEBUG - Key Conformer already exists in model_classes, re-register
2025-07-05 20:07:37,076 - root - DEBUG - Key ParaformerSANMDecoder already exists in decoder_classes, re-register
2025-07-05 20:07:37,104 - root - DEBUG - Key ParaformerSANMDecoderExport already exists in decoder_classes, re-register
2025-07-05 20:07:37,120 - root - DEBUG - Key ParaformerSANMDecoderOnlineExport already exists in decoder_classes, re-register
2025-07-05 20:07:37,144 - root - DEBUG - Key ParaformerSANDecoder already exists in decoder_classes, re-register
2025-07-05 20:07:37,162 - root - DEBUG - Key ParaformerDecoderSANExport already exists in decoder_classes, re-register
2025-07-05 20:07:37,444 - root - DEBUG - New registry table added: adaptor_classes
2025-07-05 20:07:37,580 - root - DEBUG - Key Linear already exists in adaptor_classes, re-register
2025-07-05 20:07:38,560 - root - DEBUG - Key TransformerDecoder already exists in decoder_classes, re-register
2025-07-05 20:07:38,577 - root - DEBUG - Key LightweightConvolutionTransformerDecoder already exists in decoder_classes, re-register
2025-07-05 20:07:38,587 - root - DEBUG - Key LightweightConvolution2DTransformerDecoder already exists in decoder_classes, re-register
2025-07-05 20:07:38,596 - root - DEBUG - Key DynamicConvolutionTransformerDecoder already exists in decoder_classes, re-register
2025-07-05 20:07:38,606 - root - DEBUG - Key DynamicConvolution2DTransformerDecoder already exists in decoder_classes, re-register
2025-07-05 20:07:39,125 - root - DEBUG - New registry table added: lid_predictor_classes
2025-07-05 20:07:39,340 - backend.utils.audio.speech_recognition_core - INFO - [OK] 优化FunASR管理器可用
2025-07-05 20:07:39,492 - matplotlib - DEBUG - matplotlib data path: D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\matplotlib\mpl-data
2025-07-05 20:07:39,502 - matplotlib - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-07-05 20:07:39,505 - matplotlib - DEBUG - interactive is False
2025-07-05 20:07:39,505 - matplotlib - DEBUG - platform is win32
2025-07-05 20:07:39,608 - matplotlib - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-07-05 20:07:39,612 - matplotlib.font_manager - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-07-05 20:07:40,187 - celery.utils.functional - DEBUG - 
def meeting_transcription_task(self, task_id, user_id, file_ids, language=0, output_format=1, include_timestamps=2, speaker_labeling=3, expected_speakers=4, similarity_threshold=5, clustering_method=6, config=7):
    return 1

2025-07-05 20:07:40,844 - passlib.utils.compat - DEBUG - loaded lazy attr 'SafeConfigParser': <class 'configparser.ConfigParser'>
2025-07-05 20:07:40,847 - passlib.utils.compat - DEBUG - loaded lazy attr 'NativeStringIO': <class '_io.StringIO'>
2025-07-05 20:07:40,847 - passlib.utils.compat - DEBUG - loaded lazy attr 'BytesIO': <class '_io.BytesIO'>
2025-07-05 20:07:40,855 - passlib.registry - DEBUG - registered 'bcrypt' handler: <class 'passlib.handlers.bcrypt.bcrypt'>
2025-07-05 20:07:40,894 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717260 - 0.0%
2025-07-05 20:07:40,894 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717260 - 0.0% - 开始会议转录任务
2025-07-05 20:07:41,044 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717260 - 5.0%
2025-07-05 20:07:41,044 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717260 - 5.0% - 找到 1 个音频files
2025-07-05 20:07:41,180 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717260 - 10.0%
2025-07-05 20:07:41,180 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717260 - 10.0% - Load处理模型
2025-07-05 20:07:41,284 - root - INFO - download models from model hub: ms
2025-07-05 20:07:41,399 - root - INFO - Loading pretrained params from D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\models\fsmn_vad_zh\model.pt
2025-07-05 20:07:41,400 - root - INFO - ckpt: D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\models\fsmn_vad_zh\model.pt
2025-07-05 20:07:41,491 - root - INFO - scope_map: ['module.', 'None']
2025-07-05 20:07:41,491 - root - INFO - excludes: None
2025-07-05 20:07:41,493 - root - INFO - Loading ckpt: D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\models\fsmn_vad_zh\model.pt, status: <All keys matched successfully>
2025-07-05 20:07:41,649 - backend.utils.audio.speech_recognition_core - INFO - SenseVoice识别器初始化完成 - 配置: {'model_path': '', 'device': 'auto', 'trust_remote_code': True, 'local_files_only': True, 'disable_update': True, 'language': 'auto', 'use_itn': True, 'merge_vad': True, 'merge_length_s': 15.0, 'ban_emo_unk': False, 'batch_size_s': 60, 'max_workers': 4, 'chunk_size': 30.0, 'enable_cache': True, 'memory_limit_gb': 8.0, 'min_audio_duration': 0.1, 'max_audio_duration': 600.0, 'confidence_threshold': 0.5}
2025-07-05 20:07:41,649 - backend.utils.audio.speech_recognition_core - INFO - 使用优化的FunASR管理器加载模型...
2025-07-05 20:07:41,649 - backend.utils.audio.optimized_funasr_manager - INFO - GPU优化设置完成，内存使用比例: 0.8
2025-07-05 20:07:41,649 - backend.utils.audio.optimized_funasr_manager - INFO - 使用GPU设备: cuda:0
2025-07-05 20:07:41,649 - backend.utils.audio.optimized_funasr_manager - INFO - 🔧 完全离线模式配置完成: D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\models\SenseVoiceSmall
2025-07-05 20:07:41,649 - backend.utils.audio.optimized_funasr_manager - INFO - 开始加载FunASR模型: D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\models\SenseVoiceSmall
2025-07-05 20:07:41,649 - backend.utils.audio.optimized_funasr_manager - INFO - 🔧 验证离线环境配置...
2025-07-05 20:07:41,655 - backend.utils.audio.optimized_funasr_manager - INFO -   HF_HUB_OFFLINE: 1
2025-07-05 20:07:41,655 - backend.utils.audio.optimized_funasr_manager - INFO -   HF_DATASETS_OFFLINE: 1
2025-07-05 20:07:41,655 - backend.utils.audio.optimized_funasr_manager - INFO -   TRANSFORMERS_OFFLINE: 1
2025-07-05 20:07:41,656 - backend.utils.audio.optimized_funasr_manager - INFO - 🔧 开始离线加载FunASR模型: D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\models\SenseVoiceSmall
2025-07-05 20:07:41,656 - root - INFO - download models from model hub: ms
2025-07-05 20:07:43,970 - root - INFO - Loading pretrained params from D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\models\SenseVoiceSmall\model.pt
2025-07-05 20:07:43,970 - root - INFO - ckpt: D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\models\SenseVoiceSmall\model.pt
2025-07-05 20:07:52,340 - root - INFO - scope_map: ['module.', 'None']
2025-07-05 20:07:52,340 - root - INFO - excludes: None
2025-07-05 20:07:52,543 - root - INFO - Loading ckpt: D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\models\SenseVoiceSmall\model.pt, status: <All keys matched successfully>
2025-07-05 20:07:52,951 - root - INFO - Building VAD model.
2025-07-05 20:07:52,951 - root - INFO - download models from model hub: ms
2025-07-05 20:07:55,361 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): www.modelscope.cn:443
2025-07-05 20:07:55,643 - urllib3.connectionpool - DEBUG - https://www.modelscope.cn:443 "GET /api/v1/repos/internalAccelerationInfo HTTP/1.1" 200 188
2025-07-05 20:07:55,695 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): 100.100.100.200:80
2025-07-05 20:07:55,912 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): www.modelscope.cn:443
2025-07-05 20:07:56,169 - urllib3.connectionpool - DEBUG - https://www.modelscope.cn:443 "GET /api/v1/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch HTTP/1.1" 200 None
2025-07-05 20:07:56,341 - urllib3.connectionpool - DEBUG - https://www.modelscope.cn:443 "GET /api/v1/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch/revisions HTTP/1.1" 200 1016
2025-07-05 20:07:56,573 - urllib3.connectionpool - DEBUG - https://www.modelscope.cn:443 "GET /api/v1/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch/repo/files?Revision=master&Recursive=True HTTP/1.1" 200 None
2025-07-05 20:07:56,834 - root - INFO - Loading pretrained params from C:\Users\<USER>\.cache\modelscope\hub\models\iic\speech_fsmn_vad_zh-cn-16k-common-pytorch\model.pt
2025-07-05 20:07:56,834 - root - INFO - ckpt: C:\Users\<USER>\.cache\modelscope\hub\models\iic\speech_fsmn_vad_zh-cn-16k-common-pytorch\model.pt
2025-07-05 20:07:56,855 - root - INFO - scope_map: ['module.', 'None']
2025-07-05 20:07:56,855 - root - INFO - excludes: None
2025-07-05 20:07:56,857 - root - INFO - Loading ckpt: C:\Users\<USER>\.cache\modelscope\hub\models\iic\speech_fsmn_vad_zh-cn-16k-common-pytorch\model.pt, status: <All keys matched successfully>
2025-07-05 20:07:56,863 - backend.utils.audio.optimized_funasr_manager - INFO - FunASR模型加载成功，耗时: 15.2秒
2025-07-05 20:07:56,912 - backend.utils.audio.optimized_funasr_manager - INFO - [模型加载后] 进程内存: 2.4GB, 系统内存: 86.2%, GPU已分配: 0.9GB, GPU已缓存: 0.9GB
2025-07-05 20:07:56,912 - backend.utils.audio.speech_recognition_core - INFO - [OK] SenseVoice模型加载成功（优化版，耗时: 15.26秒）
2025-07-05 20:07:56,912 - backend.utils.audio.speech_recognition_core - INFO - 语音识别系统初始化成功
2025-07-05 20:07:57,147 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717260 - 20.0%
2025-07-05 20:07:57,147 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717260 - 20.0% - 模型Load完成
2025-07-05 20:07:57,343 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717260 - 20.0%
2025-07-05 20:07:57,343 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717260 - 20.0% - 转录files 1/1: debug_test.mp3
2025-07-05 20:07:57,534 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717260 - 23.5%
2025-07-05 20:07:57,534 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717260 - 23.5% - 音频预处理
2025-07-05 20:07:59,315 - backend.utils.audio.enhanced_audio_processor - INFO - 成功加载音频: C:\Users\<USER>\AppData\Local\Temp\tmpml5ueuq8\debug_test.mp3, 采样率: 44100Hz, 时长: 48.21s
2025-07-05 20:07:59,375 - backend.utils.audio.enhanced_audio_processor - INFO - 多声道音频已转换为单声道
2025-07-05 20:08:10,842 - backend.utils.audio.enhanced_audio_processor - DEBUG - 重采样完成: 44100Hz -> 16000Hz
2025-07-05 20:08:10,842 - backend.utils.audio.enhanced_audio_processor - INFO - 音频已重采样到 16000Hz
2025-07-05 20:08:11,369 - backend.utils.audio.enhanced_audio_processor - DEBUG - 频谱门控降噪完成
2025-07-05 20:08:11,395 - backend.utils.audio.enhanced_audio_processor - DEBUG - 音频标准化完成: 方法=peak, 目标=-20.0dB
2025-07-05 20:08:11,424 - backend.utils.audio.enhanced_audio_processor - INFO - 音频处理完成: C:\Users\<USER>\AppData\Local\Temp\tmpml5ueuq8\debug_test.mp3 -> C:\Users\<USER>\AppData\Local\Temp\tmp9l33_yxr.wav
2025-07-05 20:08:11,425 - backend.tasks.audio_processing_tasks - INFO - 音频预处理成功: C:\Users\<USER>\AppData\Local\Temp\tmpml5ueuq8\debug_test.mp3 -> C:\Users\<USER>\AppData\Local\Temp\tmp9l33_yxr.wav
2025-07-05 20:08:11,585 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717260 - 27.0%
2025-07-05 20:08:11,585 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717260 - 27.0% - 音频预处理完成
2025-07-05 20:08:11,735 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717260 - 31.725%
2025-07-05 20:08:11,735 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717260 - 31.725% - 执行混合分段策略
2025-07-05 20:08:11,756 - backend.utils.audio.hybrid_segmentation - INFO - 混合分段策略初始化: hybrid, 窗口大小: 4.0s, 最小分段数: 3, 强制分割阈值: 8.0s
2025-07-05 20:08:11,757 - backend.utils.audio.hybrid_segmentation - INFO - 开始执行混合分段策略: hybrid
2025-07-05 20:08:11,757 - backend.utils.audio.hybrid_segmentation - DEBUG - 执行VAD分段，参数: {'merge_length_s': 15.0, 'min_speech_duration': 0.5, 'max_speech_duration': 60, 'threshold': 0.5}
2025-07-05 20:08:13,642 - backend.utils.audio.hybrid_segmentation - DEBUG - VAD分段完成: 15 -> 15个有效片段
2025-07-05 20:08:13,642 - backend.utils.audio.hybrid_segmentation - INFO - VAD分段结果: 15个片段
2025-07-05 20:08:13,642 - backend.utils.audio.hybrid_segmentation - INFO - VAD分段充足，直接使用VAD结果
2025-07-05 20:08:13,642 - backend.utils.audio.hybrid_segmentation - INFO - 分段优化完成: 15 -> 15个有效片段
2025-07-05 20:08:13,646 - backend.tasks.audio_processing_tasks - INFO - 混合分段完成：15个片段
2025-07-05 20:08:13,939 - backend.tasks.audio_processing_tasks - INFO - [OK] 混合分段后内存清理完成
2025-07-05 20:08:14,100 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717260 - 42.75%
2025-07-05 20:08:14,100 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717260 - 42.75% - 执行增强说话人识别
2025-07-05 20:08:14,143 - backend.utils.audio.enhanced_speaker_recognition - INFO - 增强说话人识别初始化: 期望2个说话人, 相似度阈值: 0.1, 聚类方法: auto
2025-07-05 20:08:14,143 - backend.utils.audio.enhanced_speaker_recognition - INFO - 开始说话人识别，处理15个片段
2025-07-05 20:08:14,143 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmpjtq4mu52
2025-07-05 20:08:14,190 - backend.utils.audio.enhanced_speaker_recognition - ERROR - 嵌入向量提取失败: 'SpeakerRecognition' object has no attribute 'generate'
2025-07-05 20:08:14,190 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 详细错误信息: Traceback (most recent call last):
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\backend\utils\audio\enhanced_speaker_recognition.py", line 164, in _extract_single_embedding
    res = speaker_model.generate(
          ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SpeakerRecognition' object has no attribute 'generate'

2025-07-05 20:08:14,190 - backend.utils.audio.enhanced_speaker_recognition - WARNING - 嵌入向量提取失败: 片段0
2025-07-05 20:08:14,190 - backend.utils.audio.enhanced_speaker_recognition - ERROR - 嵌入向量提取失败: 'SpeakerRecognition' object has no attribute 'generate'
2025-07-05 20:08:14,190 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 详细错误信息: Traceback (most recent call last):
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\backend\utils\audio\enhanced_speaker_recognition.py", line 164, in _extract_single_embedding
    res = speaker_model.generate(
          ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SpeakerRecognition' object has no attribute 'generate'

2025-07-05 20:08:14,190 - backend.utils.audio.enhanced_speaker_recognition - WARNING - 嵌入向量提取失败: 片段1
2025-07-05 20:08:14,208 - backend.utils.audio.enhanced_speaker_recognition - ERROR - 嵌入向量提取失败: 'SpeakerRecognition' object has no attribute 'generate'
2025-07-05 20:08:14,208 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 详细错误信息: Traceback (most recent call last):
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\backend\utils\audio\enhanced_speaker_recognition.py", line 164, in _extract_single_embedding
    res = speaker_model.generate(
          ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SpeakerRecognition' object has no attribute 'generate'

2025-07-05 20:08:14,213 - backend.utils.audio.enhanced_speaker_recognition - WARNING - 嵌入向量提取失败: 片段2
2025-07-05 20:08:14,222 - backend.utils.audio.enhanced_speaker_recognition - ERROR - 嵌入向量提取失败: 'SpeakerRecognition' object has no attribute 'generate'
2025-07-05 20:08:14,223 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 详细错误信息: Traceback (most recent call last):
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\backend\utils\audio\enhanced_speaker_recognition.py", line 164, in _extract_single_embedding
    res = speaker_model.generate(
          ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SpeakerRecognition' object has no attribute 'generate'

2025-07-05 20:08:14,223 - backend.utils.audio.enhanced_speaker_recognition - WARNING - 嵌入向量提取失败: 片段3
2025-07-05 20:08:14,232 - backend.utils.audio.enhanced_speaker_recognition - ERROR - 嵌入向量提取失败: 'SpeakerRecognition' object has no attribute 'generate'
2025-07-05 20:08:14,232 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 详细错误信息: Traceback (most recent call last):
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\backend\utils\audio\enhanced_speaker_recognition.py", line 164, in _extract_single_embedding
    res = speaker_model.generate(
          ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SpeakerRecognition' object has no attribute 'generate'

2025-07-05 20:08:14,233 - backend.utils.audio.enhanced_speaker_recognition - WARNING - 嵌入向量提取失败: 片段4
2025-07-05 20:08:14,242 - backend.utils.audio.enhanced_speaker_recognition - ERROR - 嵌入向量提取失败: 'SpeakerRecognition' object has no attribute 'generate'
2025-07-05 20:08:14,243 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 详细错误信息: Traceback (most recent call last):
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\backend\utils\audio\enhanced_speaker_recognition.py", line 164, in _extract_single_embedding
    res = speaker_model.generate(
          ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SpeakerRecognition' object has no attribute 'generate'

2025-07-05 20:08:14,243 - backend.utils.audio.enhanced_speaker_recognition - WARNING - 嵌入向量提取失败: 片段5
2025-07-05 20:08:14,251 - backend.utils.audio.enhanced_speaker_recognition - ERROR - 嵌入向量提取失败: 'SpeakerRecognition' object has no attribute 'generate'
2025-07-05 20:08:14,252 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 详细错误信息: Traceback (most recent call last):
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\backend\utils\audio\enhanced_speaker_recognition.py", line 164, in _extract_single_embedding
    res = speaker_model.generate(
          ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SpeakerRecognition' object has no attribute 'generate'

2025-07-05 20:08:14,252 - backend.utils.audio.enhanced_speaker_recognition - WARNING - 嵌入向量提取失败: 片段6
2025-07-05 20:08:14,261 - backend.utils.audio.enhanced_speaker_recognition - ERROR - 嵌入向量提取失败: 'SpeakerRecognition' object has no attribute 'generate'
2025-07-05 20:08:14,261 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 详细错误信息: Traceback (most recent call last):
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\backend\utils\audio\enhanced_speaker_recognition.py", line 164, in _extract_single_embedding
    res = speaker_model.generate(
          ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SpeakerRecognition' object has no attribute 'generate'

2025-07-05 20:08:14,262 - backend.utils.audio.enhanced_speaker_recognition - WARNING - 嵌入向量提取失败: 片段7
2025-07-05 20:08:14,271 - backend.utils.audio.enhanced_speaker_recognition - ERROR - 嵌入向量提取失败: 'SpeakerRecognition' object has no attribute 'generate'
2025-07-05 20:08:14,271 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 详细错误信息: Traceback (most recent call last):
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\backend\utils\audio\enhanced_speaker_recognition.py", line 164, in _extract_single_embedding
    res = speaker_model.generate(
          ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SpeakerRecognition' object has no attribute 'generate'

2025-07-05 20:08:14,272 - backend.utils.audio.enhanced_speaker_recognition - WARNING - 嵌入向量提取失败: 片段8
2025-07-05 20:08:14,280 - backend.utils.audio.enhanced_speaker_recognition - ERROR - 嵌入向量提取失败: 'SpeakerRecognition' object has no attribute 'generate'
2025-07-05 20:08:14,280 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 详细错误信息: Traceback (most recent call last):
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\backend\utils\audio\enhanced_speaker_recognition.py", line 164, in _extract_single_embedding
    res = speaker_model.generate(
          ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SpeakerRecognition' object has no attribute 'generate'

2025-07-05 20:08:14,281 - backend.utils.audio.enhanced_speaker_recognition - WARNING - 嵌入向量提取失败: 片段9
2025-07-05 20:08:14,292 - backend.utils.audio.enhanced_speaker_recognition - ERROR - 嵌入向量提取失败: 'SpeakerRecognition' object has no attribute 'generate'
2025-07-05 20:08:14,293 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 详细错误信息: Traceback (most recent call last):
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\backend\utils\audio\enhanced_speaker_recognition.py", line 164, in _extract_single_embedding
    res = speaker_model.generate(
          ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SpeakerRecognition' object has no attribute 'generate'

2025-07-05 20:08:14,293 - backend.utils.audio.enhanced_speaker_recognition - WARNING - 嵌入向量提取失败: 片段10
2025-07-05 20:08:14,302 - backend.utils.audio.enhanced_speaker_recognition - ERROR - 嵌入向量提取失败: 'SpeakerRecognition' object has no attribute 'generate'
2025-07-05 20:08:14,302 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 详细错误信息: Traceback (most recent call last):
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\backend\utils\audio\enhanced_speaker_recognition.py", line 164, in _extract_single_embedding
    res = speaker_model.generate(
          ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SpeakerRecognition' object has no attribute 'generate'

2025-07-05 20:08:14,303 - backend.utils.audio.enhanced_speaker_recognition - WARNING - 嵌入向量提取失败: 片段11
2025-07-05 20:08:14,312 - backend.utils.audio.enhanced_speaker_recognition - ERROR - 嵌入向量提取失败: 'SpeakerRecognition' object has no attribute 'generate'
2025-07-05 20:08:14,313 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 详细错误信息: Traceback (most recent call last):
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\backend\utils\audio\enhanced_speaker_recognition.py", line 164, in _extract_single_embedding
    res = speaker_model.generate(
          ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SpeakerRecognition' object has no attribute 'generate'

2025-07-05 20:08:14,313 - backend.utils.audio.enhanced_speaker_recognition - WARNING - 嵌入向量提取失败: 片段12
2025-07-05 20:08:14,324 - backend.utils.audio.enhanced_speaker_recognition - ERROR - 嵌入向量提取失败: 'SpeakerRecognition' object has no attribute 'generate'
2025-07-05 20:08:14,324 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 详细错误信息: Traceback (most recent call last):
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\backend\utils\audio\enhanced_speaker_recognition.py", line 164, in _extract_single_embedding
    res = speaker_model.generate(
          ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SpeakerRecognition' object has no attribute 'generate'

2025-07-05 20:08:14,325 - backend.utils.audio.enhanced_speaker_recognition - WARNING - 嵌入向量提取失败: 片段13
2025-07-05 20:08:14,335 - backend.utils.audio.enhanced_speaker_recognition - ERROR - 嵌入向量提取失败: 'SpeakerRecognition' object has no attribute 'generate'
2025-07-05 20:08:14,336 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 详细错误信息: Traceback (most recent call last):
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\backend\utils\audio\enhanced_speaker_recognition.py", line 164, in _extract_single_embedding
    res = speaker_model.generate(
          ^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'SpeakerRecognition' object has no attribute 'generate'

2025-07-05 20:08:14,337 - backend.utils.audio.enhanced_speaker_recognition - WARNING - 嵌入向量提取失败: 片段14
2025-07-05 20:08:14,342 - backend.utils.audio.enhanced_speaker_recognition - INFO - 嵌入向量提取完成: 0/15个有效片段
2025-07-05 20:08:14,343 - backend.utils.audio.enhanced_speaker_recognition - WARNING - 嵌入向量不足：0个，无法进行说话人聚类
2025-07-05 20:08:14,343 - backend.utils.audio.enhanced_speaker_recognition - INFO - 创建单说话人结果
2025-07-05 20:08:14,344 - backend.tasks.audio_processing_tasks - INFO - 增强说话人识别完成：识别出1个说话人
2025-07-05 20:08:14,605 - backend.tasks.audio_processing_tasks - INFO - [OK] 说话人识别后内存清理完成
2025-07-05 20:08:14,826 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717260 - 55.35%
2025-07-05 20:08:14,826 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717260 - 55.35% - 执行语音识别
2025-07-05 20:08:15,035 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717260 - 62.2%
2025-07-05 20:08:15,037 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717260 - 62.2% - 开始语音识别
2025-07-05 20:08:15,037 - backend.utils.audio.speech_recognition_core - INFO - 音频时长: 48.19秒, chunk_size: 30.0秒
2025-07-05 20:08:15,042 - backend.utils.audio.speech_recognition_core - INFO - 使用长音频识别
2025-07-05 20:08:15,043 - backend.utils.audio.speech_recognition_core - INFO - 开始长音频识别: C:\Users\<USER>\AppData\Local\Temp\tmp9l33_yxr.wav
2025-07-05 20:08:15,045 - backend.utils.audio.speech_recognition_core - INFO - 音频总时长: 48.19秒
2025-07-05 20:08:15,046 - backend.utils.audio.speech_recognition_core - INFO - 长音频分为2块处理，每块30.0秒
2025-07-05 20:08:15,047 - backend.utils.audio.speech_recognition_core - INFO - 开始处理分块1/2: 0.00s - 30.00s
2025-07-05 20:08:15,047 - backend.utils.audio.speech_recognition_core - INFO - 提取音频分块到: temp_chunk_0_1751717295.wav
2025-07-05 20:08:15,185 - backend.utils.audio.speech_recognition_core - INFO - 音频分块提取完成，时长: 30.00s
2025-07-05 20:08:15,185 - backend.utils.audio.speech_recognition_core - INFO - 开始识别分块1
2025-07-05 20:08:15,201 - backend.utils.audio.speech_recognition_core - INFO - 开始识别音频: temp_chunk_0_1751717295.wav (时长: 30.00s)
2025-07-05 20:08:15,201 - backend.utils.audio.optimized_funasr_manager - INFO - 开始语音识别: temp_chunk_0_1751717295.wav
2025-07-05 20:08:17,916 - backend.utils.audio.optimized_funasr_manager - INFO - 语音识别完成，耗时: 2.7秒
2025-07-05 20:08:17,924 - backend.utils.audio.speech_recognition_core - INFO - 识别成功: 67字符
2025-07-05 20:08:17,924 - backend.utils.audio.speech_recognition_core - INFO - 分块1/2识别完成，文本长度: 67
2025-07-05 20:08:17,924 - backend.utils.audio.speech_recognition_core - INFO - 开始处理分块2/2: 29.00s - 48.19s
2025-07-05 20:08:17,924 - backend.utils.audio.speech_recognition_core - INFO - 提取音频分块到: temp_chunk_1_1751717297.wav
2025-07-05 20:08:18,040 - backend.utils.audio.speech_recognition_core - INFO - 音频分块提取完成，时长: 19.19s
2025-07-05 20:08:18,041 - backend.utils.audio.speech_recognition_core - INFO - 开始识别分块2
2025-07-05 20:08:18,075 - backend.utils.audio.speech_recognition_core - INFO - 开始识别音频: temp_chunk_1_1751717297.wav (时长: 19.19s)
2025-07-05 20:08:18,075 - backend.utils.audio.optimized_funasr_manager - INFO - 开始语音识别: temp_chunk_1_1751717297.wav
2025-07-05 20:08:18,448 - backend.utils.audio.optimized_funasr_manager - INFO - 语音识别完成，耗时: 0.4秒
2025-07-05 20:08:18,448 - backend.utils.audio.speech_recognition_core - INFO - 识别成功: 47字符
2025-07-05 20:08:18,451 - backend.utils.audio.speech_recognition_core - INFO - 分块2/2识别完成，文本长度: 47
2025-07-05 20:08:18,452 - backend.utils.audio.speech_recognition_core - INFO - 长音频识别完成: 115字符
2025-07-05 20:08:18,454 - backend.utils.audio.speech_recognition_core - DEBUG - 清理临时文件: temp_chunk_0_1751717295.wav
2025-07-05 20:08:18,457 - backend.utils.audio.speech_recognition_core - DEBUG - 清理临时文件: temp_chunk_1_1751717297.wav
2025-07-05 20:08:18,572 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717260 - 59.8%
2025-07-05 20:08:18,573 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717260 - 59.8% - 处理识别结果
2025-07-05 20:08:18,771 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717260 - 64.8%
2025-07-05 20:08:18,771 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717260 - 64.8% - 语音识别完成
2025-07-05 20:08:19,035 - backend.tasks.audio_processing_tasks - INFO - [OK] 语音识别后内存清理完成
2025-07-05 20:08:19,158 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717260 - 74.25%
2025-07-05 20:08:19,158 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717260 - 74.25% - 整合转录结果
2025-07-05 20:08:19,158 - backend.tasks.audio_processing_tasks - INFO - 使用增强说话人识别结果进行整合
2025-07-05 20:08:19,158 - backend.tasks.audio_processing_tasks - INFO - 增强说话人识别整合完成: 15 个片段, 1 个说话人
2025-07-05 20:08:19,320 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717260 - 90.0%
2025-07-05 20:08:19,320 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717260 - 90.0% - 会议转录完成
2025-07-05 20:08:19,322 - backend.tasks.audio_processing_tasks - INFO - 已清理预处理临时文件: C:\Users\<USER>\AppData\Local\Temp\tmp9l33_yxr.wav
2025-07-05 20:08:19,461 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717260 - 95.0%
2025-07-05 20:08:19,461 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717260 - 95.0% - 整理转录结果
2025-07-05 20:08:19,584 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717260 - 100.0%
2025-07-05 20:08:19,584 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717260 - 100.0% - 会议转录任务完成
2025-07-05 20:08:19,586 - backend.tasks.audio_processing_tasks - INFO - [OK] 会议转录Task status updated to completed: debug_meeting_transcription_1751717260
2025-07-05 20:08:19,589 - backend.tasks.audio_processing_tasks - ERROR - 更新会议转录音频files状态失败: invalid literal for int() with base 10: 'debug_test'
2025-07-05 20:08:19,589 - asyncio - DEBUG - Using proactor: IocpProactor
2025-07-05 20:08:19,594 - backend.tasks.audio_processing_tasks - INFO - [LOG] 会议转录WebSocket completion notification sent: debug_meeting_transcription_1751717260
2025-07-05 20:08:19,891 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717260 - 100.0%
2025-07-05 20:08:19,892 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717260 - 100.0% - 会议转录任务完成
2025-07-05 20:08:19,892 - backend.tasks.audio_processing_tasks - INFO - [SUCCESS] 会议转录任务完全完成: debug_meeting_transcription_1751717260
2025-07-05 20:14:32,216 - backend.utils.ocr_utils - INFO - 检测到Tesseract版本: 5.3.0.20221214
2025-07-05 20:14:42,772 - backend.core.gpu_manager - INFO - GPU资源管理器初始化完成，进程ID: 5888
2025-07-05 20:14:44,487 - torio._extension.utils - DEBUG - Loading FFmpeg6
2025-07-05 20:14:44,490 - torio._extension.utils - DEBUG - Failed to load FFmpeg6 extension.
Traceback (most recent call last):
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\torio\_extension\utils.py", line 116, in _find_ffmpeg_extension
    ext = _find_versionsed_ffmpeg_extension(ffmpeg_ver)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\torio\_extension\utils.py", line 108, in _find_versionsed_ffmpeg_extension
    _load_lib(lib)
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\torio\_extension\utils.py", line 94, in _load_lib
    torch.ops.load_library(path)
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\torch\_ops.py", line 1357, in load_library
    ctypes.CDLL(path)
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.11.12-windows-x86_64-none\Lib\ctypes\__init__.py", line 376, in __init__
    self._handle = _dlopen(self._name, mode)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: Could not find module 'D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\torio\lib\libtorio_ffmpeg6.pyd' (or one of its dependencies). Try using the full path with constructor syntax.
2025-07-05 20:14:44,549 - torio._extension.utils - DEBUG - Loading FFmpeg5
2025-07-05 20:14:44,551 - torio._extension.utils - DEBUG - Failed to load FFmpeg5 extension.
Traceback (most recent call last):
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\torio\_extension\utils.py", line 116, in _find_ffmpeg_extension
    ext = _find_versionsed_ffmpeg_extension(ffmpeg_ver)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\torio\_extension\utils.py", line 108, in _find_versionsed_ffmpeg_extension
    _load_lib(lib)
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\torio\_extension\utils.py", line 94, in _load_lib
    torch.ops.load_library(path)
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\torch\_ops.py", line 1357, in load_library
    ctypes.CDLL(path)
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.11.12-windows-x86_64-none\Lib\ctypes\__init__.py", line 376, in __init__
    self._handle = _dlopen(self._name, mode)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: Could not find module 'D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\torio\lib\libtorio_ffmpeg5.pyd' (or one of its dependencies). Try using the full path with constructor syntax.
2025-07-05 20:14:44,552 - torio._extension.utils - DEBUG - Loading FFmpeg4
2025-07-05 20:14:44,553 - torio._extension.utils - DEBUG - Failed to load FFmpeg4 extension.
Traceback (most recent call last):
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\torio\_extension\utils.py", line 116, in _find_ffmpeg_extension
    ext = _find_versionsed_ffmpeg_extension(ffmpeg_ver)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\torio\_extension\utils.py", line 108, in _find_versionsed_ffmpeg_extension
    _load_lib(lib)
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\torio\_extension\utils.py", line 94, in _load_lib
    torch.ops.load_library(path)
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\torch\_ops.py", line 1357, in load_library
    ctypes.CDLL(path)
  File "C:\Users\<USER>\AppData\Roaming\uv\python\cpython-3.11.12-windows-x86_64-none\Lib\ctypes\__init__.py", line 376, in __init__
    self._handle = _dlopen(self._name, mode)
                   ^^^^^^^^^^^^^^^^^^^^^^^^^
FileNotFoundError: Could not find module 'D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\torio\lib\libtorio_ffmpeg4.pyd' (or one of its dependencies). Try using the full path with constructor syntax.
2025-07-05 20:14:44,555 - torio._extension.utils - DEBUG - Loading FFmpeg
2025-07-05 20:14:44,555 - torio._extension.utils - DEBUG - Failed to load FFmpeg extension.
Traceback (most recent call last):
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\torio\_extension\utils.py", line 116, in _find_ffmpeg_extension
    ext = _find_versionsed_ffmpeg_extension(ffmpeg_ver)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\torio\_extension\utils.py", line 106, in _find_versionsed_ffmpeg_extension
    raise RuntimeError(f"FFmpeg{version} extension is not available.")
RuntimeError: FFmpeg extension is not available.
2025-07-05 20:14:48,198 - root - DEBUG - New registry table added: preprocessor_classes
2025-07-05 20:14:49,366 - root - DEBUG - Key Conformer already exists in model_classes, re-register
2025-07-05 20:14:50,056 - root - DEBUG - Key ParaformerSANMDecoder already exists in decoder_classes, re-register
2025-07-05 20:14:50,105 - root - DEBUG - Key ParaformerSANMDecoderExport already exists in decoder_classes, re-register
2025-07-05 20:14:50,136 - root - DEBUG - Key ParaformerSANMDecoderOnlineExport already exists in decoder_classes, re-register
2025-07-05 20:14:50,168 - root - DEBUG - Key ParaformerSANDecoder already exists in decoder_classes, re-register
2025-07-05 20:14:50,192 - root - DEBUG - Key ParaformerDecoderSANExport already exists in decoder_classes, re-register
2025-07-05 20:14:50,585 - root - DEBUG - New registry table added: adaptor_classes
2025-07-05 20:14:50,725 - root - DEBUG - Key Linear already exists in adaptor_classes, re-register
2025-07-05 20:14:51,639 - root - DEBUG - Key TransformerDecoder already exists in decoder_classes, re-register
2025-07-05 20:14:51,665 - root - DEBUG - Key LightweightConvolutionTransformerDecoder already exists in decoder_classes, re-register
2025-07-05 20:14:51,684 - root - DEBUG - Key LightweightConvolution2DTransformerDecoder already exists in decoder_classes, re-register
2025-07-05 20:14:51,695 - root - DEBUG - Key DynamicConvolutionTransformerDecoder already exists in decoder_classes, re-register
2025-07-05 20:14:51,711 - root - DEBUG - Key DynamicConvolution2DTransformerDecoder already exists in decoder_classes, re-register
2025-07-05 20:14:52,221 - root - DEBUG - New registry table added: lid_predictor_classes
2025-07-05 20:14:52,491 - backend.utils.audio.speech_recognition_core - INFO - [OK] 优化FunASR管理器可用
2025-07-05 20:14:52,642 - matplotlib - DEBUG - matplotlib data path: D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\.venv\Lib\site-packages\matplotlib\mpl-data
2025-07-05 20:14:52,650 - matplotlib - DEBUG - CONFIGDIR=C:\Users\<USER>\.matplotlib
2025-07-05 20:14:52,654 - matplotlib - DEBUG - interactive is False
2025-07-05 20:14:52,654 - matplotlib - DEBUG - platform is win32
2025-07-05 20:14:52,764 - matplotlib - DEBUG - CACHEDIR=C:\Users\<USER>\.matplotlib
2025-07-05 20:14:52,769 - matplotlib.font_manager - DEBUG - Using fontManager instance from C:\Users\<USER>\.matplotlib\fontlist-v390.json
2025-07-05 20:14:53,256 - celery.utils.functional - DEBUG - 
def meeting_transcription_task(self, task_id, user_id, file_ids, language=0, output_format=1, include_timestamps=2, speaker_labeling=3, expected_speakers=4, similarity_threshold=5, clustering_method=6, config=7):
    return 1

2025-07-05 20:14:54,022 - passlib.utils.compat - DEBUG - loaded lazy attr 'SafeConfigParser': <class 'configparser.ConfigParser'>
2025-07-05 20:14:54,022 - passlib.utils.compat - DEBUG - loaded lazy attr 'NativeStringIO': <class '_io.StringIO'>
2025-07-05 20:14:54,022 - passlib.utils.compat - DEBUG - loaded lazy attr 'BytesIO': <class '_io.BytesIO'>
2025-07-05 20:14:54,039 - passlib.registry - DEBUG - registered 'bcrypt' handler: <class 'passlib.handlers.bcrypt.bcrypt'>
2025-07-05 20:14:54,072 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717693 - 0.0%
2025-07-05 20:14:54,072 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717693 - 0.0% - 开始会议转录任务
2025-07-05 20:14:54,209 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717693 - 5.0%
2025-07-05 20:14:54,209 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717693 - 5.0% - 找到 1 个音频files
2025-07-05 20:14:54,327 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717693 - 10.0%
2025-07-05 20:14:54,328 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717693 - 10.0% - Load处理模型
2025-07-05 20:14:54,495 - root - INFO - download models from model hub: ms
2025-07-05 20:14:54,590 - root - INFO - Loading pretrained params from D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\models\fsmn_vad_zh\model.pt
2025-07-05 20:14:54,591 - root - INFO - ckpt: D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\models\fsmn_vad_zh\model.pt
2025-07-05 20:14:54,634 - root - INFO - scope_map: ['module.', 'None']
2025-07-05 20:14:54,634 - root - INFO - excludes: None
2025-07-05 20:14:54,636 - root - INFO - Loading ckpt: D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\models\fsmn_vad_zh\model.pt, status: <All keys matched successfully>
2025-07-05 20:14:54,761 - backend.utils.audio.speech_recognition_core - INFO - SenseVoice识别器初始化完成 - 配置: {'model_path': '', 'device': 'auto', 'trust_remote_code': True, 'local_files_only': True, 'disable_update': True, 'language': 'auto', 'use_itn': True, 'merge_vad': True, 'merge_length_s': 15.0, 'ban_emo_unk': False, 'batch_size_s': 60, 'max_workers': 4, 'chunk_size': 30.0, 'enable_cache': True, 'memory_limit_gb': 8.0, 'min_audio_duration': 0.1, 'max_audio_duration': 600.0, 'confidence_threshold': 0.5}
2025-07-05 20:14:54,763 - backend.utils.audio.speech_recognition_core - INFO - 使用优化的FunASR管理器加载模型...
2025-07-05 20:14:54,763 - backend.utils.audio.optimized_funasr_manager - INFO - GPU优化设置完成，内存使用比例: 0.8
2025-07-05 20:14:54,764 - backend.utils.audio.optimized_funasr_manager - INFO - 使用GPU设备: cuda:0
2025-07-05 20:14:54,764 - backend.utils.audio.optimized_funasr_manager - INFO - 🔧 完全离线模式配置完成: D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\models\SenseVoiceSmall
2025-07-05 20:14:54,765 - backend.utils.audio.optimized_funasr_manager - INFO - 开始加载FunASR模型: D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\models\SenseVoiceSmall
2025-07-05 20:14:54,765 - backend.utils.audio.optimized_funasr_manager - INFO - 🔧 验证离线环境配置...
2025-07-05 20:14:54,765 - backend.utils.audio.optimized_funasr_manager - INFO -   HF_HUB_OFFLINE: 1
2025-07-05 20:14:54,766 - backend.utils.audio.optimized_funasr_manager - INFO -   HF_DATASETS_OFFLINE: 1
2025-07-05 20:14:54,766 - backend.utils.audio.optimized_funasr_manager - INFO -   TRANSFORMERS_OFFLINE: 1
2025-07-05 20:14:54,766 - backend.utils.audio.optimized_funasr_manager - INFO - 🔧 开始离线加载FunASR模型: D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\models\SenseVoiceSmall
2025-07-05 20:14:54,767 - root - INFO - download models from model hub: ms
2025-07-05 20:14:56,817 - root - INFO - Loading pretrained params from D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\models\SenseVoiceSmall\model.pt
2025-07-05 20:14:56,817 - root - INFO - ckpt: D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\models\SenseVoiceSmall\model.pt
2025-07-05 20:15:01,141 - root - INFO - scope_map: ['module.', 'None']
2025-07-05 20:15:01,141 - root - INFO - excludes: None
2025-07-05 20:15:01,338 - root - INFO - Loading ckpt: D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\models\SenseVoiceSmall\model.pt, status: <All keys matched successfully>
2025-07-05 20:15:01,698 - root - INFO - Building VAD model.
2025-07-05 20:15:01,698 - root - INFO - download models from model hub: ms
2025-07-05 20:15:02,068 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): www.modelscope.cn:443
2025-07-05 20:15:02,285 - urllib3.connectionpool - DEBUG - https://www.modelscope.cn:443 "GET /api/v1/repos/internalAccelerationInfo HTTP/1.1" 200 188
2025-07-05 20:15:02,302 - urllib3.connectionpool - DEBUG - Starting new HTTP connection (1): 100.100.100.200:80
2025-07-05 20:15:02,516 - urllib3.connectionpool - DEBUG - Starting new HTTPS connection (1): www.modelscope.cn:443
2025-07-05 20:15:02,776 - urllib3.connectionpool - DEBUG - https://www.modelscope.cn:443 "GET /api/v1/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch HTTP/1.1" 200 None
2025-07-05 20:15:02,953 - urllib3.connectionpool - DEBUG - https://www.modelscope.cn:443 "GET /api/v1/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch/revisions HTTP/1.1" 200 1016
2025-07-05 20:15:03,145 - urllib3.connectionpool - DEBUG - https://www.modelscope.cn:443 "GET /api/v1/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch/repo/files?Revision=master&Recursive=True HTTP/1.1" 200 None
2025-07-05 20:15:03,405 - root - INFO - Loading pretrained params from C:\Users\<USER>\.cache\modelscope\hub\models\iic\speech_fsmn_vad_zh-cn-16k-common-pytorch\model.pt
2025-07-05 20:15:03,405 - root - INFO - ckpt: C:\Users\<USER>\.cache\modelscope\hub\models\iic\speech_fsmn_vad_zh-cn-16k-common-pytorch\model.pt
2025-07-05 20:15:03,439 - root - INFO - scope_map: ['module.', 'None']
2025-07-05 20:15:03,439 - root - INFO - excludes: None
2025-07-05 20:15:03,443 - root - INFO - Loading ckpt: C:\Users\<USER>\.cache\modelscope\hub\models\iic\speech_fsmn_vad_zh-cn-16k-common-pytorch\model.pt, status: <All keys matched successfully>
2025-07-05 20:15:03,447 - backend.utils.audio.optimized_funasr_manager - INFO - FunASR模型加载成功，耗时: 8.7秒
2025-07-05 20:15:03,492 - backend.utils.audio.optimized_funasr_manager - INFO - [模型加载后] 进程内存: 2.4GB, 系统内存: 84.5%, GPU已分配: 0.9GB, GPU已缓存: 0.9GB
2025-07-05 20:15:03,492 - backend.utils.audio.speech_recognition_core - INFO - [OK] SenseVoice模型加载成功（优化版，耗时: 8.73秒）
2025-07-05 20:15:03,493 - backend.utils.audio.speech_recognition_core - INFO - 语音识别系统初始化成功
2025-07-05 20:15:03,629 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717693 - 20.0%
2025-07-05 20:15:03,629 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717693 - 20.0% - 模型Load完成
2025-07-05 20:15:03,746 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717693 - 20.0%
2025-07-05 20:15:03,746 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717693 - 20.0% - 转录files 1/1: debug_test.mp3
2025-07-05 20:15:03,866 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717693 - 23.5%
2025-07-05 20:15:03,866 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717693 - 23.5% - 音频预处理
2025-07-05 20:15:03,999 - backend.utils.audio.enhanced_audio_processor - INFO - 成功加载音频: C:\Users\<USER>\AppData\Local\Temp\tmpoashspq4\debug_test.mp3, 采样率: 44100Hz, 时长: 48.21s
2025-07-05 20:15:04,035 - backend.utils.audio.enhanced_audio_processor - INFO - 多声道音频已转换为单声道
2025-07-05 20:15:12,172 - backend.utils.audio.enhanced_audio_processor - DEBUG - 重采样完成: 44100Hz -> 16000Hz
2025-07-05 20:15:12,172 - backend.utils.audio.enhanced_audio_processor - INFO - 音频已重采样到 16000Hz
2025-07-05 20:15:12,749 - backend.utils.audio.enhanced_audio_processor - DEBUG - 频谱门控降噪完成
2025-07-05 20:15:12,772 - backend.utils.audio.enhanced_audio_processor - DEBUG - 音频标准化完成: 方法=peak, 目标=-20.0dB
2025-07-05 20:15:12,805 - backend.utils.audio.enhanced_audio_processor - INFO - 音频处理完成: C:\Users\<USER>\AppData\Local\Temp\tmpoashspq4\debug_test.mp3 -> C:\Users\<USER>\AppData\Local\Temp\tmphq71iv08.wav
2025-07-05 20:15:12,805 - backend.tasks.audio_processing_tasks - INFO - 音频预处理成功: C:\Users\<USER>\AppData\Local\Temp\tmpoashspq4\debug_test.mp3 -> C:\Users\<USER>\AppData\Local\Temp\tmphq71iv08.wav
2025-07-05 20:15:12,924 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717693 - 27.0%
2025-07-05 20:15:12,924 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717693 - 27.0% - 音频预处理完成
2025-07-05 20:15:13,041 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717693 - 31.725%
2025-07-05 20:15:13,041 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717693 - 31.725% - 执行混合分段策略
2025-07-05 20:15:13,055 - backend.utils.audio.hybrid_segmentation - INFO - 混合分段策略初始化: hybrid, 窗口大小: 4.0s, 最小分段数: 3, 强制分割阈值: 8.0s
2025-07-05 20:15:13,055 - backend.utils.audio.hybrid_segmentation - INFO - 开始执行混合分段策略: hybrid
2025-07-05 20:15:13,055 - backend.utils.audio.hybrid_segmentation - DEBUG - 执行VAD分段，参数: {'merge_length_s': 15.0, 'min_speech_duration': 0.5, 'max_speech_duration': 60, 'threshold': 0.5}
2025-07-05 20:15:16,272 - backend.utils.audio.hybrid_segmentation - DEBUG - VAD分段完成: 15 -> 15个有效片段
2025-07-05 20:15:16,272 - backend.utils.audio.hybrid_segmentation - INFO - VAD分段结果: 15个片段
2025-07-05 20:15:16,272 - backend.utils.audio.hybrid_segmentation - INFO - VAD分段充足，直接使用VAD结果
2025-07-05 20:15:16,276 - backend.utils.audio.hybrid_segmentation - INFO - 分段优化完成: 15 -> 15个有效片段
2025-07-05 20:15:16,276 - backend.tasks.audio_processing_tasks - INFO - 混合分段完成：15个片段
2025-07-05 20:15:16,559 - backend.tasks.audio_processing_tasks - INFO - [OK] 混合分段后内存清理完成
2025-07-05 20:15:16,690 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717693 - 42.75%
2025-07-05 20:15:16,691 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717693 - 42.75% - 执行增强说话人识别
2025-07-05 20:15:16,732 - backend.utils.audio.enhanced_speaker_recognition - INFO - 增强说话人识别初始化: 期望2个说话人, 相似度阈值: 0.1, 聚类方法: auto
2025-07-05 20:15:16,732 - backend.utils.audio.enhanced_speaker_recognition - INFO - 开始说话人识别，处理15个片段
2025-07-05 20:15:16,732 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 创建临时目录: C:\Users\<USER>\AppData\Local\Temp\tmp33mqg2nw
2025-07-05 20:15:16,799 - root - INFO - download models from model hub: ms
2025-07-05 20:15:17,009 - root - INFO - Loading pretrained params from D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\models\cam++\campplus_cn_common.bin
2025-07-05 20:15:17,025 - root - INFO - ckpt: D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\models\cam++\campplus_cn_common.bin
2025-07-05 20:15:17,597 - root - INFO - scope_map: ['module.', 'None']
2025-07-05 20:15:17,597 - root - INFO - excludes: None
2025-07-05 20:15:17,662 - root - INFO - Loading ckpt: D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\models\cam++\campplus_cn_common.bin, status: <All keys matched successfully>
2025-07-05 20:15:20,906 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 嵌入向量提取成功 - 文件: segment_0.wav
2025-07-05 20:15:20,906 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 维度: (192,)
2025-07-05 20:15:20,906 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 数据类型: float32
2025-07-05 20:15:20,906 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 数值范围: [-3.188842, 3.064672]
2025-07-05 20:15:20,922 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 均值: -0.066687
2025-07-05 20:15:20,922 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 标准差: 0.988459
2025-07-05 20:15:20,922 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - L2范数: 13.727623
2025-07-05 20:15:20,922 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 成功提取片段0的嵌入向量
2025-07-05 20:15:21,147 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 嵌入向量提取成功 - 文件: segment_1.wav
2025-07-05 20:15:21,148 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 维度: (192,)
2025-07-05 20:15:21,148 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 数据类型: float32
2025-07-05 20:15:21,148 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 数值范围: [-3.393943, 2.576052]
2025-07-05 20:15:21,149 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 均值: -0.080744
2025-07-05 20:15:21,149 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 标准差: 1.115988
2025-07-05 20:15:21,150 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - L2范数: 15.504007
2025-07-05 20:15:21,150 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 成功提取片段1的嵌入向量
2025-07-05 20:15:21,436 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 嵌入向量提取成功 - 文件: segment_2.wav
2025-07-05 20:15:21,436 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 维度: (192,)
2025-07-05 20:15:21,436 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 数据类型: float32
2025-07-05 20:15:21,436 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 数值范围: [-3.608591, 2.263725]
2025-07-05 20:15:21,436 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 均值: -0.003277
2025-07-05 20:15:21,450 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 标准差: 1.009034
2025-07-05 20:15:21,450 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - L2范数: 13.981659
2025-07-05 20:15:21,450 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 成功提取片段2的嵌入向量
2025-07-05 20:15:22,085 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 嵌入向量提取成功 - 文件: segment_3.wav
2025-07-05 20:15:22,100 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 维度: (192,)
2025-07-05 20:15:22,134 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 数据类型: float32
2025-07-05 20:15:22,159 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 数值范围: [-2.429764, 2.541211]
2025-07-05 20:15:22,187 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 均值: -0.005739
2025-07-05 20:15:22,224 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 标准差: 0.820022
2025-07-05 20:15:22,260 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - L2范数: 11.362829
2025-07-05 20:15:22,296 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 成功提取片段3的嵌入向量
2025-07-05 20:15:23,236 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 嵌入向量提取成功 - 文件: segment_4.wav
2025-07-05 20:15:23,253 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 维度: (192,)
2025-07-05 20:15:23,279 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 数据类型: float32
2025-07-05 20:15:23,299 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 数值范围: [-2.607835, 3.900863]
2025-07-05 20:15:23,319 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 均值: 0.023873
2025-07-05 20:15:23,340 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 标准差: 1.017956
2025-07-05 20:15:23,350 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - L2范数: 14.109085
2025-07-05 20:15:23,373 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 成功提取片段4的嵌入向量
2025-07-05 20:15:24,559 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 嵌入向量提取成功 - 文件: segment_5.wav
2025-07-05 20:15:24,574 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 维度: (192,)
2025-07-05 20:15:24,593 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 数据类型: float32
2025-07-05 20:15:24,606 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 数值范围: [-1.958747, 2.361906]
2025-07-05 20:15:24,618 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 均值: -0.038093
2025-07-05 20:15:24,640 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 标准差: 0.857679
2025-07-05 20:15:24,658 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - L2范数: 11.896060
2025-07-05 20:15:24,683 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 成功提取片段5的嵌入向量
2025-07-05 20:15:25,658 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 嵌入向量提取成功 - 文件: segment_6.wav
2025-07-05 20:15:25,689 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 维度: (192,)
2025-07-05 20:15:25,716 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 数据类型: float32
2025-07-05 20:15:25,734 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 数值范围: [-2.801777, 2.248478]
2025-07-05 20:15:25,737 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 均值: 0.000982
2025-07-05 20:15:25,738 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 标准差: 0.966748
2025-07-05 20:15:25,741 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - L2范数: 13.395662
2025-07-05 20:15:25,758 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 成功提取片段6的嵌入向量
2025-07-05 20:15:27,199 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 嵌入向量提取成功 - 文件: segment_7.wav
2025-07-05 20:15:27,231 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 维度: (192,)
2025-07-05 20:15:27,257 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 数据类型: float32
2025-07-05 20:15:27,289 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 数值范围: [-2.535534, 2.736651]
2025-07-05 20:15:27,320 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 均值: -0.072630
2025-07-05 20:15:27,337 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 标准差: 1.052647
2025-07-05 20:15:27,357 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - L2范数: 14.620588
2025-07-05 20:15:27,386 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 成功提取片段7的嵌入向量
2025-07-05 20:15:28,600 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 嵌入向量提取成功 - 文件: segment_8.wav
2025-07-05 20:15:28,602 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 维度: (192,)
2025-07-05 20:15:28,606 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 数据类型: float32
2025-07-05 20:15:28,608 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 数值范围: [-2.174439, 2.027314]
2025-07-05 20:15:28,613 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 均值: -0.036248
2025-07-05 20:15:28,615 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 标准差: 0.869571
2025-07-05 20:15:28,618 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - L2范数: 12.059599
2025-07-05 20:15:28,620 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 成功提取片段8的嵌入向量
2025-07-05 20:15:28,975 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 嵌入向量提取成功 - 文件: segment_9.wav
2025-07-05 20:15:28,976 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 维度: (192,)
2025-07-05 20:15:28,977 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 数据类型: float32
2025-07-05 20:15:28,978 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 数值范围: [-3.028199, 2.072181]
2025-07-05 20:15:28,978 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 均值: -0.102680
2025-07-05 20:15:28,979 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 标准差: 1.015333
2025-07-05 20:15:28,981 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - L2范数: 14.140631
2025-07-05 20:15:28,988 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 成功提取片段9的嵌入向量
2025-07-05 20:15:29,307 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 嵌入向量提取成功 - 文件: segment_10.wav
2025-07-05 20:15:29,317 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 维度: (192,)
2025-07-05 20:15:29,319 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 数据类型: float32
2025-07-05 20:15:29,324 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 数值范围: [-2.774662, 2.091562]
2025-07-05 20:15:29,325 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 均值: 0.022075
2025-07-05 20:15:29,331 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 标准差: 0.895783
2025-07-05 20:15:29,333 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - L2范数: 12.416107
2025-07-05 20:15:29,334 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 成功提取片段10的嵌入向量
2025-07-05 20:15:29,632 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 嵌入向量提取成功 - 文件: segment_11.wav
2025-07-05 20:15:29,632 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 维度: (192,)
2025-07-05 20:15:29,633 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 数据类型: float32
2025-07-05 20:15:29,633 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 数值范围: [-2.362322, 2.171606]
2025-07-05 20:15:29,634 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 均值: -0.106720
2025-07-05 20:15:29,638 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 标准差: 0.926800
2025-07-05 20:15:29,642 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - L2范数: 12.926970
2025-07-05 20:15:29,645 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 成功提取片段11的嵌入向量
2025-07-05 20:15:30,003 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 嵌入向量提取成功 - 文件: segment_12.wav
2025-07-05 20:15:30,003 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 维度: (192,)
2025-07-05 20:15:30,005 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 数据类型: float32
2025-07-05 20:15:30,005 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 数值范围: [-2.094234, 2.219449]
2025-07-05 20:15:30,007 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 均值: -0.084961
2025-07-05 20:15:30,007 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 标准差: 0.818710
2025-07-05 20:15:30,007 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - L2范数: 11.405301
2025-07-05 20:15:30,008 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 成功提取片段12的嵌入向量
2025-07-05 20:15:30,304 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 嵌入向量提取成功 - 文件: segment_13.wav
2025-07-05 20:15:30,307 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 维度: (192,)
2025-07-05 20:15:30,312 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 数据类型: float32
2025-07-05 20:15:30,319 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 数值范围: [-1.797801, 1.832275]
2025-07-05 20:15:30,323 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 均值: 0.050243
2025-07-05 20:15:30,331 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 标准差: 0.780844
2025-07-05 20:15:30,333 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - L2范数: 10.842072
2025-07-05 20:15:30,336 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 成功提取片段13的嵌入向量
2025-07-05 20:15:30,683 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 嵌入向量提取成功 - 文件: segment_14.wav
2025-07-05 20:15:30,684 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 维度: (192,)
2025-07-05 20:15:30,685 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 数据类型: float32
2025-07-05 20:15:30,686 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 数值范围: [-1.892825, 1.487223]
2025-07-05 20:15:30,686 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 均值: -0.002494
2025-07-05 20:15:30,687 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - 标准差: 0.724244
2025-07-05 20:15:30,688 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   - L2范数: 10.035473
2025-07-05 20:15:30,688 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 成功提取片段14的嵌入向量
2025-07-05 20:15:30,700 - backend.utils.audio.enhanced_speaker_recognition - INFO - 嵌入向量提取完成: 15/15个有效片段
2025-07-05 20:15:30,701 - backend.utils.audio.enhanced_speaker_recognition - INFO - 成功提取15个说话人嵌入向量
2025-07-05 20:15:30,702 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 嵌入向量矩阵形状: (15, 192)
2025-07-05 20:15:30,784 - backend.utils.audio.enhanced_speaker_recognition - INFO - 相似度统计 - 平均: 0.2794, 最小: 0.0287, 最大: 0.6589, 标准差: 0.1482
2025-07-05 20:15:30,785 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - === 详细相似度矩阵分析 ===
2025-07-05 20:15:30,785 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 相似度矩阵形状: (15, 15)
2025-07-05 20:15:30,785 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 相似度矩阵:
2025-07-05 20:15:30,786 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   行0: 1.0000 0.0541 0.0317 0.0345 0.0287 0.1661 0.1969 0.4455 0.1587 0.3377 0.3251 0.3251 0.4111 0.2206 0.1905
2025-07-05 20:15:30,786 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   行1: 0.0541 1.0000 0.5264 0.4752 0.1648 0.3462 0.3823 0.0779 0.3221 0.0703 0.1439 0.1345 0.1610 0.1524 0.3149
2025-07-05 20:15:30,787 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   行2: 0.0317 0.5264 1.0000 0.6589 0.4674 0.4178 0.5223 0.0757 0.5375 0.1405 0.0819 0.1081 0.2715 0.3711 0.4288
2025-07-05 20:15:30,787 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   行3: 0.0345 0.4752 0.6589 1.0000 0.4262 0.4383 0.5039 0.1504 0.5810 0.1976 0.2126 0.1669 0.2083 0.3432 0.4268
2025-07-05 20:15:30,788 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   行4: 0.0287 0.1648 0.4674 0.4262 1.0000 0.3661 0.3678 0.0442 0.3772 0.1431 0.0360 0.1516 0.1905 0.3369 0.2954
2025-07-05 20:15:30,788 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   行5: 0.1661 0.3462 0.4178 0.4383 0.3661 1.0000 0.4599 0.0730 0.4386 0.1400 0.2710 0.1639 0.1940 0.1864 0.2851
2025-07-05 20:15:30,789 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   行6: 0.1969 0.3823 0.5223 0.5039 0.3678 0.4599 1.0000 0.2283 0.4346 0.1785 0.2073 0.2188 0.2768 0.2924 0.5357
2025-07-05 20:15:30,790 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   行7: 0.4455 0.0779 0.0757 0.1504 0.0442 0.0730 0.2283 1.0000 0.2009 0.4551 0.4286 0.2908 0.3382 0.1387 0.1666
2025-07-05 20:15:30,791 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   行8: 0.1587 0.3221 0.5375 0.5810 0.3772 0.4386 0.4346 0.2009 1.0000 0.1789 0.2430 0.2369 0.2884 0.3803 0.4397
2025-07-05 20:15:30,792 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   行9: 0.3377 0.0703 0.1405 0.1976 0.1431 0.1400 0.1785 0.4551 0.1789 1.0000 0.5634 0.2142 0.5052 0.1883 0.2837
2025-07-05 20:15:30,792 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   行10: 0.3251 0.1439 0.0819 0.2126 0.0360 0.2710 0.2073 0.4286 0.2430 0.5634 1.0000 0.2858 0.4480 0.1511 0.1648
2025-07-05 20:15:30,793 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   行11: 0.3251 0.1345 0.1081 0.1669 0.1516 0.1639 0.2188 0.2908 0.2369 0.2142 0.2858 1.0000 0.5035 0.1773 0.1476
2025-07-05 20:15:30,793 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   行12: 0.4111 0.1610 0.2715 0.2083 0.1905 0.1940 0.2768 0.3382 0.2884 0.5052 0.4480 0.5035 1.0000 0.2786 0.2645
2025-07-05 20:15:30,794 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   行13: 0.2206 0.1524 0.3711 0.3432 0.3369 0.1864 0.2924 0.1387 0.3803 0.1883 0.1511 0.1773 0.2786 1.0000 0.5496
2025-07-05 20:15:30,794 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   行14: 0.1905 0.3149 0.4288 0.4268 0.2954 0.2851 0.5357 0.1666 0.4397 0.2837 0.1648 0.1476 0.2645 0.5496 1.0000
2025-07-05 20:15:30,795 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - === 嵌入向量特征分析 ===
2025-07-05 20:15:30,796 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量0: 均值=-0.066687, 标准差=0.988459, L2范数=13.727623
2025-07-05 20:15:30,797 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量1: 均值=-0.080744, 标准差=1.115988, L2范数=15.504007
2025-07-05 20:15:30,797 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量2: 均值=-0.003277, 标准差=1.009034, L2范数=13.981659
2025-07-05 20:15:30,797 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量3: 均值=-0.005739, 标准差=0.820022, L2范数=11.362829
2025-07-05 20:15:30,797 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量4: 均值=0.023873, 标准差=1.017956, L2范数=14.109085
2025-07-05 20:15:30,797 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量5: 均值=-0.038093, 标准差=0.857679, L2范数=11.896060
2025-07-05 20:15:30,797 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量6: 均值=0.000982, 标准差=0.966748, L2范数=13.395662
2025-07-05 20:15:30,797 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量7: 均值=-0.072630, 标准差=1.052647, L2范数=14.620588
2025-07-05 20:15:30,803 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量8: 均值=-0.036248, 标准差=0.869571, L2范数=12.059599
2025-07-05 20:15:30,803 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量9: 均值=-0.102680, 标准差=1.015333, L2范数=14.140631
2025-07-05 20:15:30,804 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量10: 均值=0.022075, 标准差=0.895783, L2范数=12.416107
2025-07-05 20:15:30,804 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量11: 均值=-0.106720, 标准差=0.926800, L2范数=12.926970
2025-07-05 20:15:30,805 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量12: 均值=-0.084961, 标准差=0.818710, L2范数=11.405301
2025-07-05 20:15:30,805 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量13: 均值=0.050243, 标准差=0.780844, L2范数=10.842072
2025-07-05 20:15:30,806 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量14: 均值=-0.002494, 标准差=0.724244, L2范数=10.035473
2025-07-05 20:15:30,806 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - === 向量差异分析 ===
2025-07-05 20:15:30,806 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量0与向量1: L2距离=20.144386, 余弦相似度=0.054093
2025-07-05 20:15:30,807 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量0与向量2: L2距离=19.281282, 余弦相似度=0.031694
2025-07-05 20:15:30,809 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量0与向量3: L2距离=17.515841, 余弦相似度=0.034480
2025-07-05 20:15:30,809 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量0与向量4: L2距离=19.401190, 余弦相似度=0.028675
2025-07-05 20:15:30,811 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量0与向量5: L2距离=16.604568, 余弦相似度=0.166107
2025-07-05 20:15:30,811 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量0与向量6: L2距离=17.188969, 余弦相似度=0.196940
2025-07-05 20:15:30,813 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量0与向量7: L2距离=14.945636, 余弦相似度=0.445521
2025-07-05 20:15:30,813 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量0与向量8: L2距离=16.773277, 余弦相似度=0.158680
2025-07-05 20:15:30,816 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量0与向量9: L2距离=16.040133, 余弦相似度=0.337732
2025-07-05 20:15:30,817 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量0与向量10: L2距离=15.224775, 余弦相似度=0.325074
2025-07-05 20:15:30,817 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量0与向量11: L2距离=15.497642, 余弦相似度=0.325086
2025-07-05 20:15:30,818 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量0与向量12: L2距离=13.776569, 余弦相似度=0.411115
2025-07-05 20:15:30,820 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量0与向量13: L2距离=15.502740, 余弦相似度=0.220589
2025-07-05 20:15:30,821 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量0与向量14: L2距离=15.384326, 余弦相似度=0.190475
2025-07-05 20:15:30,821 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量1与向量2: L2距离=14.410576, 余弦相似度=0.526352
2025-07-05 20:15:30,821 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量1与向量3: L2距离=14.214991, 余弦相似度=0.475174
2025-07-05 20:15:30,821 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量1与向量4: L2距离=19.166409, 余弦相似度=0.164778
2025-07-05 20:15:30,821 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量1与向量5: L2距离=15.943416, 余弦相似度=0.346184
2025-07-05 20:15:30,821 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量1与向量6: L2距离=16.156322, 余弦相似度=0.382286
2025-07-05 20:15:30,825 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量1与向量7: L2距离=20.465603, 余弦相似度=0.077853
2025-07-05 20:15:30,825 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量1与向量8: L2距离=16.290051, 余弦相似度=0.322087
2025-07-05 20:15:30,826 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量1与向量9: L2距离=20.235970, 余弦相似度=0.070329
2025-07-05 20:15:30,826 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量1与向量10: L2距离=18.415855, 余弦相似度=0.143871
2025-07-05 20:15:30,826 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量1与向量11: L2距离=18.803930, 余弦相似度=0.134450
2025-07-05 20:15:30,826 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量1与向量12: L2距离=17.706341, 余弦相似度=0.161006
2025-07-05 20:15:30,829 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量1与向量13: L2距离=17.512455, 余弦相似度=0.152409
2025-07-05 20:15:30,829 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量1与向量14: L2距离=15.591084, 余弦相似度=0.314940
2025-07-05 20:15:30,829 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量2与向量3: L2距离=10.735025, 余弦相似度=0.658898
2025-07-05 20:15:30,829 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量2与向量4: L2距离=14.496716, 余弦相似度=0.467379
2025-07-05 20:15:30,829 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量2与向量5: L2距离=14.071488, 余弦相似度=0.417841
2025-07-05 20:15:30,829 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量2与向量6: L2距离=13.389325, 余弦相似度=0.522326
2025-07-05 20:15:30,829 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量2与向量7: L2距离=19.450264, 余弦相似度=0.075667
2025-07-05 20:15:30,829 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量2与向量8: L2距离=12.635124, 余弦相似度=0.537545
2025-07-05 20:15:30,829 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量2与向量9: L2距离=18.436359, 余弦相似度=0.140471
2025-07-05 20:15:30,829 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量2与向量10: L2距离=17.922001, 余弦相似度=0.081938
2025-07-05 20:15:30,829 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量2与向量11: L2距离=17.987043, 余弦相似度=0.108054
2025-07-05 20:15:30,829 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量2与向量12: L2距离=15.458468, 余弦相似度=0.271544
2025-07-05 20:15:30,829 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量2与向量13: L2距离=14.161022, 余弦相似度=0.371075
2025-07-05 20:15:30,829 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量2与向量14: L2距离=13.261374, 余弦相似度=0.428805
2025-07-05 20:15:30,829 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量3与向量4: L2距离=13.839725, 余弦相似度=0.426157
2025-07-05 20:15:30,829 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量3与向量5: L2距离=12.334142, 余弦相似度=0.438324
2025-07-05 20:15:30,829 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量3与向量6: L2距离=12.456258, 余弦相似度=0.503898
2025-07-05 20:15:30,829 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量3与向量7: L2距离=17.113937, 余弦相似度=0.150449
2025-07-05 20:15:30,839 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量3与向量8: L2距离=10.738196, 余弦相似度=0.581032
2025-07-05 20:15:30,839 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量3与向量9: L2距离=16.296787, 余弦相似度=0.197557
2025-07-05 20:15:30,840 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量3与向量10: L2距离=14.942513, 余弦相似度=0.212624
2025-07-05 20:15:30,841 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量3与向量11: L2距离=15.722287, 余弦相似度=0.166898
2025-07-05 20:15:30,841 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量3与向量12: L2距离=14.324976, 余弦相似度=0.208300
2025-07-05 20:15:30,843 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量3与向量13: L2距离=12.731750, 余弦相似度=0.343220
2025-07-05 20:15:30,845 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量3与向量14: L2距离=11.510224, 余弦相似度=0.426810
2025-07-05 20:15:30,845 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量4与向量5: L2距离=14.754313, 余弦相似度=0.366095
2025-07-05 20:15:30,847 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量4与向量6: L2距离=15.475697, 余弦相似度=0.367758
2025-07-05 20:15:30,847 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量4与向量7: L2距离=19.864031, 余弦相似度=0.044231
2025-07-05 20:15:30,848 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量4与向量8: L2距离=14.701143, 余弦相似度=0.377245
2025-07-05 20:15:30,848 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量4与向量9: L2距离=18.490805, 余弦相似度=0.143134
2025-07-05 20:15:30,848 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量4与向量10: L2距离=18.455650, 余弦相似度=0.036006
2025-07-05 20:15:30,849 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量4与向量11: L2距离=17.632011, 余弦相似度=0.151559
2025-07-05 20:15:30,849 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量4与向量12: L2距离=16.366205, 余弦相似度=0.190452
2025-07-05 20:15:30,850 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量4与向量13: L2距离=14.613372, 余弦相似度=0.336880
2025-07-05 20:15:30,850 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量4与向量14: L2距离=14.700733, 余弦相似度=0.295447
2025-07-05 20:15:30,850 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量5与向量6: L2距离=13.204923, 余弦相似度=0.459946
2025-07-05 20:15:30,851 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量5与向量7: L2距离=18.162567, 余弦相似度=0.073016
2025-07-05 20:15:30,851 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量5与向量8: L2距离=12.692387, 余弦相似度=0.438631
2025-07-05 20:15:30,851 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量5与向量9: L2距离=17.157694, 余弦相似度=0.139959
2025-07-05 20:15:30,851 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量5与向量10: L2距离=14.683704, 余弦相似度=0.271034
2025-07-05 20:15:30,851 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量5与向量11: L2距离=16.068903, 余弦相似度=0.163913
2025-07-05 20:15:30,851 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量5与向量12: L2距离=14.796681, 余弦相似度=0.194045
2025-07-05 20:15:30,853 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量5与向量13: L2距离=14.525716, 余弦相似度=0.186351
2025-07-05 20:15:30,853 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量5与向量14: L2距离=13.196778, 余弦相似度=0.285100
2025-07-05 20:15:30,853 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量6与向量7: L2距离=17.429029, 余弦相似度=0.228321
2025-07-05 20:15:30,854 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量6与向量8: L2距离=13.581384, 余弦相似度=0.434624
2025-07-05 20:15:30,854 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量6与向量9: L2距离=17.656780, 余弦相似度=0.178540
2025-07-05 20:15:30,854 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量6与向量10: L2距离=16.267828, 余弦相似度=0.207312
2025-07-05 20:15:30,854 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量6与向量11: L2距离=16.454693, 余弦相似度=0.218847
2025-07-05 20:15:30,855 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量6与向量12: L2距离=14.997668, 余弦相似度=0.276848
2025-07-05 20:15:30,855 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量6与向量13: L2距离=14.561956, 余弦相似度=0.292432
2025-07-05 20:15:30,855 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量6与向量14: L2距离=11.666867, 余弦相似度=0.535731
2025-07-05 20:15:30,856 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量7与向量8: L2距离=16.980806, 余弦相似度=0.200908
2025-07-05 20:15:30,856 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量7与向量9: L2距离=15.017685, 余弦相似度=0.455123
2025-07-05 20:15:30,856 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量7与向量10: L2距离=14.570495, 余弦相似度=0.428638
2025-07-05 20:15:30,858 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量7与向量11: L2距离=16.459993, 余弦相似度=0.290838
2025-07-05 20:15:30,858 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量7与向量12: L2距离=15.200768, 余弦相似度=0.338164
2025-07-05 20:15:30,859 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量7与向量13: L2距离=16.950623, 余弦相似度=0.138750
2025-07-05 20:15:30,859 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量7与向量14: L2距离=16.296593, 余弦相似度=0.166616
2025-07-05 20:15:30,859 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量8与向量9: L2距离=16.863390, 余弦相似度=0.178905
2025-07-05 20:15:30,859 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量8与向量10: L2距离=15.060186, 余弦相似度=0.243047
2025-07-05 20:15:30,859 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量8与向量11: L2距离=15.449560, 余弦相似度=0.236865
2025-07-05 20:15:30,859 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量8与向量12: L2距离=14.006702, 余弦相似度=0.288371
2025-07-05 20:15:30,862 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量8与向量13: L2距离=12.787757, 余弦相似度=0.380332
2025-07-05 20:15:30,862 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量8与向量14: L2距离=11.820054, 余弦相似度=0.439711
2025-07-05 20:15:30,863 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量9与向量10: L2距离=12.500689, 余弦相似度=0.563445
2025-07-05 20:15:30,863 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量9与向量11: L2距离=16.993010, 余弦相似度=0.214178
2025-07-05 20:15:30,863 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量9与向量12: L2距离=12.925483, 余弦相似度=0.505246
2025-07-05 20:15:30,864 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量9与向量13: L2距离=16.117550, 余弦相似度=0.188282
2025-07-05 20:15:30,864 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量9与向量14: L2距离=14.837072, 余弦相似度=0.283738
2025-07-05 20:15:30,864 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量10与向量11: L2距离=15.150518, 余弦相似度=0.285753
2025-07-05 20:15:30,865 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量10与向量12: L2距离=12.544194, 余弦相似度=0.448006
2025-07-05 20:15:30,865 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量10与向量13: L2距离=15.199759, 余弦相似度=0.151087
2025-07-05 20:15:30,866 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量10与向量14: L2距离=14.621632, 余弦相似度=0.164839
2025-07-05 20:15:30,866 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量11与向量12: L2距离=12.195427, 余弦相似度=0.503470
2025-07-05 20:15:30,866 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量11与向量13: L2距离=15.328677, 余弦相似度=0.177263
2025-07-05 20:15:30,866 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量11与向量14: L2距离=15.150063, 余弦相似度=0.147589
2025-07-05 20:15:30,866 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量12与向量13: L2距离=13.368748, 余弦相似度=0.278625
2025-07-05 20:15:30,866 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量12与向量14: L2距离=13.047817, 余弦相似度=0.264491
2025-07-05 20:15:30,866 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 向量13与向量14: L2距离=9.932431, 余弦相似度=0.549642
2025-07-05 20:15:31,054 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 层次聚类（ward）: 2个聚类, 得分: 0.2797
2025-07-05 20:15:31,072 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 层次聚类（complete）: 2个聚类, 得分: 0.2797
2025-07-05 20:15:31,075 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 层次聚类（average）: 2个聚类, 得分: 0.2797
2025-07-05 20:15:31,075 - backend.utils.audio.enhanced_speaker_recognition - WARNING - 聚类评估失败: Number of labels is 15. Valid values are 2 to n_samples - 1 (inclusive)
2025-07-05 20:15:31,075 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 严格阈值聚类: 15个聚类, 得分: 0.0000
2025-07-05 20:15:31,079 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - DBSCAN(eps_factor=0.5)失败: Negative values in data passed to X.
2025-07-05 20:15:31,080 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - DBSCAN(eps_factor=0.3)失败: Negative values in data passed to X.
2025-07-05 20:15:31,081 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - DBSCAN(eps_factor=0.1)失败: Negative values in data passed to X.
2025-07-05 20:15:31,081 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - === 所有聚类方法结果分析 ===
2025-07-05 20:15:31,082 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - agglomerative_ward: 2个聚类, 得分: 0.2797
2025-07-05 20:15:31,082 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   标签分布: [9 6]
2025-07-05 20:15:31,083 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   标签序列: [1 0 0 0 0 0 0 1 0 1 1 1 1 0 0]
2025-07-05 20:15:31,083 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - agglomerative_complete: 2个聚类, 得分: 0.2797
2025-07-05 20:15:31,084 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   标签分布: [9 6]
2025-07-05 20:15:31,084 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   标签序列: [1 0 0 0 0 0 0 1 0 1 1 1 1 0 0]
2025-07-05 20:15:31,084 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - agglomerative_average: 2个聚类, 得分: 0.2797
2025-07-05 20:15:31,086 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   标签分布: [9 6]
2025-07-05 20:15:31,086 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   标签序列: [1 0 0 0 0 0 0 1 0 1 1 1 1 0 0]
2025-07-05 20:15:31,086 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - agglomerative_strict: 15个聚类, 得分: 0.0000
2025-07-05 20:15:31,087 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   标签分布: [1 1 1 1 1 1 1 1 1 1 1 1 1 1 1]
2025-07-05 20:15:31,087 - backend.utils.audio.enhanced_speaker_recognition - DEBUG -   标签序列: [ 8 11 14 12 13  9  7 10  5  6  3  4  1  2  0]
2025-07-05 20:15:31,087 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - agglomerative_ward: 质量=0.2797, 数量匹配=1.0000, 综合=0.5678, 聚类数=2
2025-07-05 20:15:31,088 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - agglomerative_complete: 质量=0.2797, 数量匹配=1.0000, 综合=0.5678, 聚类数=2
2025-07-05 20:15:31,088 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - agglomerative_average: 质量=0.2797, 数量匹配=1.0000, 综合=0.5678, 聚类数=2
2025-07-05 20:15:31,088 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - agglomerative_strict: 质量=0.0000, 数量匹配=0.1333, 综合=0.0533, 聚类数=15
2025-07-05 20:15:31,089 - backend.utils.audio.enhanced_speaker_recognition - INFO - 选择最佳聚类方法: agglomerative_ward, 识别出 2 个说话人, 得分: 0.2797
2025-07-05 20:15:31,089 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - === 最佳聚类结果详细分析 ===
2025-07-05 20:15:31,089 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 最佳标签序列: [1 0 0 0 0 0 0 1 0 1 1 1 1 0 0]
2025-07-05 20:15:31,090 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 说话人0: 分配到9个片段 - [ 1  2  3  4  5  6  8 13 14]
2025-07-05 20:15:31,090 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 说话人1: 分配到6个片段 - [ 0  7  9 10 11 12]
2025-07-05 20:15:31,091 - backend.utils.audio.enhanced_speaker_recognition - DEBUG - 时间平滑：片段2从说话人0调整为0
2025-07-05 20:15:31,091 - backend.utils.audio.enhanced_speaker_recognition - INFO - 说话人识别完成：识别出2个说话人
2025-07-05 20:15:31,091 - backend.tasks.audio_processing_tasks - INFO - 增强说话人识别完成：识别出2个说话人
2025-07-05 20:15:31,390 - backend.tasks.audio_processing_tasks - INFO - [OK] 说话人识别后内存清理完成
2025-07-05 20:15:31,506 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717693 - 55.35%
2025-07-05 20:15:31,506 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717693 - 55.35% - 执行语音识别
2025-07-05 20:15:31,672 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717693 - 62.2%
2025-07-05 20:15:31,676 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717693 - 62.2% - 开始语音识别
2025-07-05 20:15:31,679 - backend.utils.audio.speech_recognition_core - INFO - 音频时长: 48.19秒, chunk_size: 30.0秒
2025-07-05 20:15:31,680 - backend.utils.audio.speech_recognition_core - INFO - 使用长音频识别
2025-07-05 20:15:31,681 - backend.utils.audio.speech_recognition_core - INFO - 开始长音频识别: C:\Users\<USER>\AppData\Local\Temp\tmphq71iv08.wav
2025-07-05 20:15:31,683 - backend.utils.audio.speech_recognition_core - INFO - 音频总时长: 48.19秒
2025-07-05 20:15:31,683 - backend.utils.audio.speech_recognition_core - INFO - 长音频分为2块处理，每块30.0秒
2025-07-05 20:15:31,683 - backend.utils.audio.speech_recognition_core - INFO - 开始处理分块1/2: 0.00s - 30.00s
2025-07-05 20:15:31,685 - backend.utils.audio.speech_recognition_core - INFO - 提取音频分块到: temp_chunk_0_1751717731.wav
2025-07-05 20:15:31,792 - backend.utils.audio.speech_recognition_core - INFO - 音频分块提取完成，时长: 30.00s
2025-07-05 20:15:31,792 - backend.utils.audio.speech_recognition_core - INFO - 开始识别分块1
2025-07-05 20:15:31,813 - backend.utils.audio.speech_recognition_core - INFO - 开始识别音频: temp_chunk_0_1751717731.wav (时长: 30.00s)
2025-07-05 20:15:31,813 - backend.utils.audio.optimized_funasr_manager - INFO - 开始语音识别: temp_chunk_0_1751717731.wav
2025-07-05 20:15:34,750 - backend.utils.audio.optimized_funasr_manager - INFO - 语音识别完成，耗时: 2.9秒
2025-07-05 20:15:34,750 - backend.utils.audio.speech_recognition_core - INFO - 识别成功: 68字符
2025-07-05 20:15:34,761 - backend.utils.audio.speech_recognition_core - INFO - 分块1/2识别完成，文本长度: 68
2025-07-05 20:15:34,761 - backend.utils.audio.speech_recognition_core - INFO - 开始处理分块2/2: 29.00s - 48.19s
2025-07-05 20:15:34,761 - backend.utils.audio.speech_recognition_core - INFO - 提取音频分块到: temp_chunk_1_1751717734.wav
2025-07-05 20:15:34,843 - backend.utils.audio.speech_recognition_core - INFO - 音频分块提取完成，时长: 19.19s
2025-07-05 20:15:34,847 - backend.utils.audio.speech_recognition_core - INFO - 开始识别分块2
2025-07-05 20:15:34,892 - backend.utils.audio.speech_recognition_core - INFO - 开始识别音频: temp_chunk_1_1751717734.wav (时长: 19.19s)
2025-07-05 20:15:34,893 - backend.utils.audio.optimized_funasr_manager - INFO - 开始语音识别: temp_chunk_1_1751717734.wav
2025-07-05 20:15:35,239 - backend.utils.audio.optimized_funasr_manager - INFO - 语音识别完成，耗时: 0.3秒
2025-07-05 20:15:35,242 - backend.utils.audio.speech_recognition_core - INFO - 识别成功: 47字符
2025-07-05 20:15:35,243 - backend.utils.audio.speech_recognition_core - INFO - 分块2/2识别完成，文本长度: 47
2025-07-05 20:15:35,243 - backend.utils.audio.speech_recognition_core - INFO - 长音频识别完成: 116字符
2025-07-05 20:15:35,248 - backend.utils.audio.speech_recognition_core - DEBUG - 清理临时文件: temp_chunk_0_1751717731.wav
2025-07-05 20:15:35,249 - backend.utils.audio.speech_recognition_core - DEBUG - 清理临时文件: temp_chunk_1_1751717734.wav
2025-07-05 20:15:35,502 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717693 - 59.8%
2025-07-05 20:15:35,510 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717693 - 59.8% - 处理识别结果
2025-07-05 20:15:35,644 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717693 - 64.8%
2025-07-05 20:15:35,644 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717693 - 64.8% - 语音识别完成
2025-07-05 20:15:36,058 - backend.tasks.audio_processing_tasks - INFO - [OK] 语音识别后内存清理完成
2025-07-05 20:15:36,192 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717693 - 74.25%
2025-07-05 20:15:36,194 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717693 - 74.25% - 整合转录结果
2025-07-05 20:15:36,194 - backend.tasks.audio_processing_tasks - INFO - 使用增强说话人识别结果进行整合
2025-07-05 20:15:36,196 - backend.tasks.audio_processing_tasks - INFO - 增强说话人识别整合完成: 15 个片段, 2 个说话人
2025-07-05 20:15:36,326 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717693 - 90.0%
2025-07-05 20:15:36,326 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717693 - 90.0% - 会议转录完成
2025-07-05 20:15:36,338 - backend.tasks.audio_processing_tasks - INFO - 已清理预处理临时文件: C:\Users\<USER>\AppData\Local\Temp\tmphq71iv08.wav
2025-07-05 20:15:36,488 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717693 - 95.0%
2025-07-05 20:15:36,491 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717693 - 95.0% - 整理转录结果
2025-07-05 20:15:36,632 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717693 - 100.0%
2025-07-05 20:15:36,641 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717693 - 100.0% - 会议转录任务完成
2025-07-05 20:15:36,650 - backend.tasks.audio_processing_tasks - INFO - [OK] 会议转录Task status updated to completed: debug_meeting_transcription_1751717693
2025-07-05 20:15:36,653 - asyncio - DEBUG - Using proactor: IocpProactor
2025-07-05 20:15:36,655 - backend.tasks.audio_processing_tasks - ERROR - 更新会议转录音频files状态失败: invalid literal for int() with base 10: 'debug_test'
2025-07-05 20:15:36,682 - backend.tasks.audio_processing_tasks - INFO - [LOG] 会议转录WebSocket completion notification sent: debug_meeting_transcription_1751717693
2025-07-05 20:15:36,807 - backend.tasks.base_task - INFO - 📡 WebSocket进度通知已发布到Redis: debug_meeting_transcription_1751717693 - 100.0%
2025-07-05 20:15:36,809 - backend.tasks.base_task - INFO - 任务进度更新: debug_meeting_transcription_1751717693 - 100.0% - 会议转录任务完成
2025-07-05 20:15:36,811 - backend.tasks.audio_processing_tasks - INFO - [SUCCESS] 会议转录任务完全完成: debug_meeting_transcription_1751717693
