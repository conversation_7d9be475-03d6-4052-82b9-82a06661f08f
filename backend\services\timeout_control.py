"""
超时控制服务
管理任务超时、监控长时间运行的任务
"""

import asyncio
import time
from typing import Dict, Any, Optional, List, Callable
from dataclasses import dataclass
from datetime import datetime, timezone, timedelta
from loguru import logger

from backend.services.task_persistence_service import get_task_persistence_service
from backend.services.error_handler import get_error_handler
from backend.core.task_queue import get_task_manager
from backend.core.database import get_db_session
from backend.models.task_models import TaskStatus, TaskRecord


@dataclass
class TimeoutConfig:
    """超时配置"""
    task_type: str
    soft_timeout: int  # 软超时（秒）
    hard_timeout: int  # 硬超时（秒）
    warning_threshold: int  # 警告阈值（秒）
    enabled: bool = True


class TimeoutController:
    """超时控制器"""
    
    def __init__(self):
        self.persistence_service = get_task_persistence_service()
        self.error_handler = get_error_handler()
        self.task_manager = get_task_manager()
        
        # 🔧 优化大文件处理的超时配置
        self.timeout_configs = {
            "document_processing": TimeoutConfig(
                task_type="document_processing",
                soft_timeout=900,    # 🔧 15分钟（大文件需要更多时间）
                hard_timeout=1800,   # 🔧 30分钟
                warning_threshold=600  # 🔧 10分钟
            ),
            "vectorization": TimeoutConfig(
                task_type="vectorization",
                soft_timeout=1200,   # 🔧 20分钟
                hard_timeout=2400,   # 🔧 40分钟
                warning_threshold=900  # 🔧 15分钟
            ),
            "ocr_processing": TimeoutConfig(
                task_type="ocr_processing",
                soft_timeout=600,    # 🔧 10分钟（大PDF需要更多OCR时间）
                hard_timeout=1200,   # 🔧 20分钟
                warning_threshold=480  # 🔧 8分钟
            ),
            "default": TimeoutConfig(
                task_type="default",
                soft_timeout=900,    # 🔧 15分钟
                hard_timeout=1800,   # 🔧 30分钟
                warning_threshold=600  # 🔧 10分钟
            )
        }
        
        # 运行时状态
        self.monitoring = False
        self.monitor_task = None
        self.timeout_callbacks: Dict[str, List[Callable]] = {}
        self.warned_tasks: set = set()  # 已警告的任务
    
    def get_timeout_config(self, task_type: str) -> TimeoutConfig:
        """获取任务类型的超时配置"""
        return self.timeout_configs.get(task_type, self.timeout_configs["default"])
    
    def update_timeout_config(self, task_type: str, config: TimeoutConfig):
        """更新超时配置"""
        self.timeout_configs[task_type] = config
        logger.info(f"更新超时配置: {task_type}")
    
    def check_task_timeout(self, task_record) -> Dict[str, Any]:
        """检查单个任务的超时状态"""
        try:
            if not task_record.started_at:
                return {"status": "not_started", "action": None}
            
            # 获取超时配置
            config = self.get_timeout_config(task_record.task_type)
            
            if not config.enabled:
                return {"status": "timeout_disabled", "action": None}
            
            # 计算运行时间
            now = datetime.now(timezone.utc)
            running_time = (now - task_record.started_at).total_seconds()
            
            result = {
                "task_id": task_record.task_id,
                "task_type": task_record.task_type,
                "running_time": running_time,
                "config": {
                    "soft_timeout": config.soft_timeout,
                    "hard_timeout": config.hard_timeout,
                    "warning_threshold": config.warning_threshold
                }
            }
            
            # 检查硬超时
            if running_time >= config.hard_timeout:
                result.update({
                    "status": "hard_timeout",
                    "action": "terminate",
                    "message": f"任务硬超时 ({running_time:.1f}s >= {config.hard_timeout}s)"
                })
                return result
            
            # 检查软超时
            if running_time >= config.soft_timeout:
                result.update({
                    "status": "soft_timeout",
                    "action": "warning",
                    "message": f"任务软超时 ({running_time:.1f}s >= {config.soft_timeout}s)"
                })
                return result
            
            # 检查警告阈值
            if running_time >= config.warning_threshold:
                result.update({
                    "status": "warning",
                    "action": "warn",
                    "message": f"任务运行时间较长 ({running_time:.1f}s >= {config.warning_threshold}s)"
                })
                return result
            
            # 正常状态
            result.update({
                "status": "normal",
                "action": None,
                "message": f"任务运行正常 ({running_time:.1f}s)"
            })
            return result
            
        except Exception as e:
            logger.error(f"检查任务超时失败: {task_record.task_id}, {e}")
            return {"status": "error", "action": None, "error": str(e)}
    
    def handle_timeout(self, task_record, timeout_info: Dict[str, Any]):
        """处理超时任务"""
        try:
            task_id = task_record.task_id
            action = timeout_info.get("action")
            
            if action == "terminate":
                # 硬超时：终止任务
                self._terminate_task(task_record, timeout_info)
                
            elif action == "warning":
                # 软超时：记录警告
                self._handle_soft_timeout(task_record, timeout_info)
                
            elif action == "warn":
                # 警告阈值：发出警告
                self._handle_warning(task_record, timeout_info)
            
            # 执行超时回调
            self._execute_timeout_callbacks(task_record, timeout_info)
            
        except Exception as e:
            logger.error(f"处理超时任务失败: {task_record.task_id}, {e}")
    
    def _terminate_task(self, task_record, timeout_info: Dict[str, Any]):
        """终止超时任务"""
        try:
            task_id = task_record.task_id
            
            # 取消Celery任务
            self.task_manager.cancel_task(task_id)
            
            # 更新任务状态
            db = get_db_session()
            try:
                self.persistence_service.update_task_status(
                    db=db,
                    task_id=task_id,
                    status=TaskStatus.FAILURE,
                    error_message=f"任务硬超时: {timeout_info['message']}",
                    traceback=f"Task terminated due to hard timeout after {timeout_info['running_time']:.1f} seconds"
                )
            finally:
                db.close()
            
            logger.error(f"任务因硬超时被终止: {task_id}")
            
        except Exception as e:
            logger.error(f"终止超时任务失败: {task_record.task_id}, {e}")
    
    def _handle_soft_timeout(self, task_record, timeout_info: Dict[str, Any]):
        """处理软超时"""
        try:
            task_id = task_record.task_id
            
            # 记录软超时警告
            logger.warning(f"任务软超时: {task_id} - {timeout_info['message']}")
            
            # 可以在这里添加额外的处理逻辑
            # 例如：降低任务优先级、发送通知等
            
        except Exception as e:
            logger.error(f"处理软超时失败: {task_record.task_id}, {e}")
    
    def _handle_warning(self, task_record, timeout_info: Dict[str, Any]):
        """处理警告"""
        try:
            task_id = task_record.task_id
            
            # 避免重复警告
            if task_id in self.warned_tasks:
                return
            
            self.warned_tasks.add(task_id)
            
            # 记录警告
            logger.warning(f"任务运行时间警告: {task_id} - {timeout_info['message']}")
            
        except Exception as e:
            logger.error(f"处理警告失败: {task_record.task_id}, {e}")
    
    def _execute_timeout_callbacks(self, task_record, timeout_info: Dict[str, Any]):
        """执行超时回调"""
        try:
            action = timeout_info.get("action", "")
            callbacks = self.timeout_callbacks.get(action, [])
            callbacks.extend(self.timeout_callbacks.get("all", []))
            
            for callback in callbacks:
                try:
                    callback(task_record, timeout_info)
                except Exception as e:
                    logger.error(f"超时回调执行失败: {e}")
                    
        except Exception as e:
            logger.error(f"执行超时回调时出错: {e}")
    
    async def start_monitoring(self, interval: int = 30):
        """开始超时监控"""
        if self.monitoring:
            logger.warning("超时监控已在运行")
            return
        
        self.monitoring = True
        logger.info(f"开始超时监控，检查间隔: {interval}秒")
        
        try:
            while self.monitoring:
                await self._check_all_running_tasks()
                await asyncio.sleep(interval)
                
        except Exception as e:
            logger.error(f"超时监控出错: {e}")
        finally:
            self.monitoring = False
            logger.info("超时监控已停止")
    
    async def _check_all_running_tasks(self):
        """检查所有运行中的任务"""
        try:
            db = get_db_session()
            try:
                # 获取所有运行中的任务
                running_tasks = db.query(TaskRecord).filter(
                    TaskRecord.status == TaskStatus.STARTED
                ).all()
                
                timeout_count = 0
                warning_count = 0
                
                for task_record in running_tasks:
                    try:
                        # 检查超时
                        timeout_info = self.check_task_timeout(task_record)
                        
                        if timeout_info.get("action"):
                            if timeout_info["action"] == "terminate":
                                timeout_count += 1
                            elif timeout_info["action"] in ["warning", "warn"]:
                                warning_count += 1
                            
                            # 处理超时
                            self.handle_timeout(task_record, timeout_info)
                            
                    except Exception as e:
                        logger.error(f"检查任务超时失败: {task_record.task_id}, {e}")
                
                if timeout_count > 0 or warning_count > 0:
                    logger.info(f"超时检查完成: 终止 {timeout_count} 个任务, 警告 {warning_count} 个任务")
                    
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"检查所有运行任务失败: {e}")
    
    def stop_monitoring(self):
        """停止超时监控"""
        self.monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
    
    def register_timeout_callback(
        self,
        action: str,
        callback: Callable[[Any, Dict[str, Any]], None]
    ):
        """注册超时回调"""
        if action not in self.timeout_callbacks:
            self.timeout_callbacks[action] = []
        self.timeout_callbacks[action].append(callback)
    
    def get_timeout_statistics(self, hours: int = 24) -> Dict[str, Any]:
        """获取超时统计"""
        try:
            db = get_db_session()
            try:
                # 获取指定时间范围内的任务
                since = datetime.now(timezone.utc) - timedelta(hours=hours)
                tasks = db.query(TaskRecord).filter(
                    TaskRecord.created_at >= since
                ).all()
                
                stats = {
                    "total_tasks": len(tasks),
                    "timeout_tasks": 0,
                    "average_duration": 0,
                    "task_types": {},
                    "timeout_reasons": {}
                }
                
                total_duration = 0
                completed_tasks = 0
                
                for task in tasks:
                    # 统计任务类型
                    task_type = task.task_type
                    if task_type not in stats["task_types"]:
                        stats["task_types"][task_type] = {
                            "total": 0,
                            "completed": 0,
                            "timeout": 0,
                            "average_duration": 0
                        }
                    
                    stats["task_types"][task_type]["total"] += 1
                    
                    # 计算执行时间
                    if task.started_at and task.completed_at:
                        duration = (task.completed_at - task.started_at).total_seconds()
                        total_duration += duration
                        completed_tasks += 1
                        stats["task_types"][task_type]["completed"] += 1
                        
                        # 检查是否超时
                        config = self.get_timeout_config(task_type)
                        if duration >= config.soft_timeout:
                            stats["timeout_tasks"] += 1
                            stats["task_types"][task_type]["timeout"] += 1
                            
                            # 分类超时原因
                            if "timeout" in (task.error_message or "").lower():
                                reason = "explicit_timeout"
                            elif duration >= config.hard_timeout:
                                reason = "hard_timeout"
                            else:
                                reason = "soft_timeout"
                            
                            stats["timeout_reasons"][reason] = stats["timeout_reasons"].get(reason, 0) + 1
                
                # 计算平均执行时间
                if completed_tasks > 0:
                    stats["average_duration"] = total_duration / completed_tasks
                
                # 计算各任务类型的平均时间
                for task_type, type_stats in stats["task_types"].items():
                    if type_stats["completed"] > 0:
                        # 这里需要重新计算，简化处理
                        type_stats["average_duration"] = stats["average_duration"]
                
                return stats
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"获取超时统计失败: {e}")
            return {"error": str(e)}
    
    def get_current_timeouts(self) -> List[Dict[str, Any]]:
        """获取当前超时任务"""
        try:
            db = get_db_session()
            try:
                # 获取运行中的任务
                running_tasks = db.query(TaskRecord).filter(
                    TaskRecord.status == TaskStatus.STARTED
                ).all()
                
                timeout_tasks = []
                
                for task_record in running_tasks:
                    timeout_info = self.check_task_timeout(task_record)
                    
                    if timeout_info.get("action"):
                        timeout_tasks.append({
                            "task_id": task_record.task_id,
                            "task_name": task_record.task_name,
                            "task_type": task_record.task_type,
                            "user_id": task_record.user_id,
                            "started_at": task_record.started_at.isoformat() if task_record.started_at else None,
                            "timeout_info": timeout_info
                        })
                
                return timeout_tasks
                
            finally:
                db.close()
                
        except Exception as e:
            logger.error(f"获取当前超时任务失败: {e}")
            return []


# 全局超时控制器实例
timeout_controller = TimeoutController()


def get_timeout_controller() -> TimeoutController:
    """获取超时控制器实例"""
    return timeout_controller
