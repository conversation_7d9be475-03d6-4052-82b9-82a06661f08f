#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试统一结果数据结构
验证所有数据结构的功能是否正常工作
"""

import sys
import os
import tempfile
import json
from datetime import datetime

# 添加utils目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))

from utils.result_data_structures import (
    TimeInterval, SpeechSegment, SpeakerProfile, ProcessingMetrics, AnalysisResult,
    create_speech_segment, create_analysis_result, convert_recognition_result_to_analysis
)

def test_time_interval():
    """测试时间区间类"""
    print("🧪 测试TimeInterval类...")
    
    # 创建时间区间
    interval1 = TimeInterval(0.0, 5.0)
    interval2 = TimeInterval(3.0, 8.0)
    interval3 = TimeInterval(10.0, 15.0)
    
    # 测试基本属性
    assert interval1.duration == 5.0, f"期望持续时间5.0，实际{interval1.duration}"
    print(f"  ✅ 时间长度计算正确: {interval1.duration}秒")
    
    # 测试重叠检测
    assert interval1.overlaps_with(interval2), "interval1和interval2应该重叠"
    assert not interval1.overlaps_with(interval3), "interval1和interval3不应该重叠"
    print(f"  ✅ 重叠检测正确")
    
    # 测试区间合并
    merged = interval1.merge_with(interval2)
    assert merged.start == 0.0 and merged.end == 8.0, f"期望合并区间[0.0, 8.0]，实际[{merged.start}, {merged.end}]"
    print(f"  ✅ 区间合并正确: [{merged.start}, {merged.end}]")
    
    # 测试字典转换
    dict_repr = interval1.to_dict()
    assert 'start' in dict_repr and 'end' in dict_repr, "字典应包含start和end字段"
    print(f"  ✅ 字典转换正确: {dict_repr}")

def test_speech_segment():
    """测试语音片段类"""
    print("\n🧪 测试SpeechSegment类...")
    
    # 创建语音片段
    time_interval = TimeInterval(0.0, 5.0)
    segment = SpeechSegment(
        time_interval=time_interval,
        text="这是一段测试语音识别的文本内容。",
        confidence=0.95,
        language="zh-cn",
        speaker_id="speaker_001",
        speaker_confidence=0.88,
        emotion="neutral",
        event="speech"
    )
    
    # 测试基本属性
    assert segment.text == "这是一段测试语音识别的文本内容。", "文本内容应该匹配"
    assert segment.confidence == 0.95, f"期望置信度0.95，实际{segment.confidence}"
    print(f"  ✅ 基本属性正确")
    
    # 测试自动计算的属性
    assert segment.speech_rate > 0, f"语速应该大于0，实际{segment.speech_rate}"
    assert segment.word_count > 0, f"字数应该大于0，实际{segment.word_count}"
    print(f"  ✅ 自动计算属性: 语速{segment.speech_rate:.2f}字符/秒, 字数{segment.word_count}")
    
    # 测试字典转换
    dict_repr = segment.to_dict()
    assert 'text' in dict_repr and 'confidence' in dict_repr, "字典应包含基本字段"
    print(f"  ✅ 字典转换正确")

def test_speaker_profile():
    """测试说话人档案类"""
    print("\n🧪 测试SpeakerProfile类...")
    
    # 创建说话人档案
    speaker = SpeakerProfile(
        id="speaker_001",
        name="张三",
        gender="male",
        age_estimate="30-40"
    )
    
    # 测试基本属性
    assert speaker.name == "张三", f"期望名称'张三'，实际'{speaker.name}'"
    assert speaker.id == "speaker_001", f"期望ID'speaker_001'，实际'{speaker.id}'"
    print(f"  ✅ 基本属性正确: {speaker.name} ({speaker.id})")
    
    # 创建测试片段来更新统计信息
    segments = []
    for i in range(3):
        time_interval = TimeInterval(i * 5.0, (i + 1) * 5.0)
        segment = SpeechSegment(
            time_interval=time_interval,
            text=f"第{i+1}段测试文本内容。",
            speaker_id="speaker_001",
            speaker_confidence=0.9 + i * 0.02,
            emotion="neutral",
            event="speech"
        )
        segments.append(segment)
    
    # 更新统计信息
    speaker.update_from_segments(segments)
    
    # 验证统计信息
    assert speaker.segment_count == 3, f"期望片段数3，实际{speaker.segment_count}"
    assert speaker.total_speech_time == 15.0, f"期望总时长15.0秒，实际{speaker.total_speech_time}"
    assert speaker.avg_confidence > 0, f"平均置信度应该大于0，实际{speaker.avg_confidence}"
    print(f"  ✅ 统计信息更新正确: {speaker.segment_count}片段, {speaker.total_speech_time}秒, 置信度{speaker.avg_confidence:.3f}")

def test_analysis_result():
    """测试统一分析结果类"""
    print("\n🧪 测试AnalysisResult类...")
    
    # 创建分析结果
    analysis = AnalysisResult(
        audio_file_path="test_audio.wav",
        audio_duration=30.0
    )
    
    # 添加说话人
    speaker1 = SpeakerProfile(id="speaker_001", name="张三")
    speaker2 = SpeakerProfile(id="speaker_002", name="李四")
    analysis.add_speaker(speaker1)
    analysis.add_speaker(speaker2)
    
    # 添加语音片段
    segments_data = [
        (0.0, 5.0, "你好，今天天气很好。", "speaker_001", 0.95),
        (5.5, 10.0, "是的，确实很好。", "speaker_002", 0.92),
        (11.0, 16.0, "我们去公园走走吧。", "speaker_001", 0.88),
        (17.0, 22.0, "好主意，我同意。", "speaker_002", 0.90)
    ]
    
    for start, end, text, speaker_id, confidence in segments_data:
        segment = create_speech_segment(
            start_time=start,
            end_time=end,
            text=text,
            speaker_id=speaker_id,
            confidence=confidence
        )
        analysis.add_segment(segment)
    
    # 验证统计信息
    assert analysis.speaker_count == 2, f"期望说话人数2，实际{analysis.speaker_count}"
    assert len(analysis.segments) == 4, f"期望片段数4，实际{len(analysis.segments)}"
    assert analysis.total_speech_time > 0, f"总语音时长应该大于0，实际{analysis.total_speech_time}"
    assert analysis.word_count > 0, f"总字数应该大于0，实际{analysis.word_count}"
    print(f"  ✅ 基本统计正确: {analysis.speaker_count}说话人, {len(analysis.segments)}片段, {analysis.total_speech_time:.1f}秒")
    
    # 测试转录文本生成
    transcript = analysis.get_full_transcript()
    assert len(transcript) > 0, "转录文本不应为空"
    assert "张三:" in transcript, "转录文本应包含说话人名称"
    print(f"  ✅ 转录文本生成正确")
    
    # 测试说话人片段获取
    speaker1_segments = analysis.get_speaker_segments("speaker_001")
    assert len(speaker1_segments) == 2, f"说话人1应该有2个片段，实际{len(speaker1_segments)}"
    print(f"  ✅ 说话人片段获取正确")
    
    return analysis

def test_serialization():
    """测试序列化和反序列化"""
    print("\n🧪 测试序列化和反序列化...")
    
    # 创建测试数据
    analysis = test_analysis_result()  # 复用上面的测试数据
    
    # 测试字典导出
    data_dict = analysis.export_to_dict()
    assert 'segments' in data_dict, "导出字典应包含segments字段"
    assert 'speakers' in data_dict, "导出字典应包含speakers字段"
    assert 'created_at' in data_dict, "导出字典应包含created_at字段"
    print(f"  ✅ 字典导出正确")
    
    # 测试从字典重建
    restored_analysis = AnalysisResult.from_dict(data_dict)
    assert len(restored_analysis.segments) == len(analysis.segments), "重建后片段数应该一致"
    assert len(restored_analysis.speakers) == len(analysis.speakers), "重建后说话人数应该一致"
    assert restored_analysis.audio_file_path == analysis.audio_file_path, "重建后文件路径应该一致"
    print(f"  ✅ 字典重建正确")
    
    # 测试文件保存和加载
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
        temp_file = f.name
    
    try:
        # 保存到文件
        analysis.save_to_file(temp_file)
        assert os.path.exists(temp_file), "文件应该已创建"
        print(f"  ✅ 文件保存成功: {temp_file}")
        
        # 从文件加载
        loaded_analysis = AnalysisResult.load_from_file(temp_file)
        assert len(loaded_analysis.segments) == len(analysis.segments), "加载后片段数应该一致"
        assert len(loaded_analysis.speakers) == len(analysis.speakers), "加载后说话人数应该一致"
        print(f"  ✅ 文件加载正确")
        
        # 检查文件格式
        with open(temp_file, 'r', encoding='utf-8') as f:
            file_content = json.load(f)
        assert 'segments' in file_content, "文件应包含segments字段"
        assert 'speakers' in file_content, "文件应包含speakers字段"
        print(f"  ✅ 文件格式正确")
        
    finally:
        # 清理临时文件
        if os.path.exists(temp_file):
            os.unlink(temp_file)

def test_convenience_functions():
    """测试便捷函数"""
    print("\n🧪 测试便捷函数...")
    
    # 测试create_speech_segment
    segment = create_speech_segment(
        start_time=0.0,
        end_time=5.0,
        text="测试文本",
        speaker_id="speaker_001",
        confidence=0.95
    )
    assert segment.text == "测试文本", "文本应该匹配"
    assert segment.time_interval.duration == 5.0, "时长应该为5秒"
    print(f"  ✅ create_speech_segment函数正确")
    
    # 测试create_analysis_result
    analysis = create_analysis_result(
        audio_file_path="test.wav",
        audio_duration=10.0,
        segments=[segment]
    )
    assert analysis.audio_file_path == "test.wav", "文件路径应该匹配"
    assert len(analysis.segments) == 1, "应该有1个片段"
    print(f"  ✅ create_analysis_result函数正确")

def main():
    """主测试函数"""
    print("🚀 开始测试统一结果数据结构...")
    print("=" * 60)
    
    try:
        # 运行所有测试
        test_time_interval()
        test_speech_segment()
        test_speaker_profile()
        test_analysis_result()
        test_serialization()
        test_convenience_functions()
        
        print("\n" + "=" * 60)
        print("🎉 所有测试通过！统一结果数据结构工作正常。")
        print("📊 数据结构特性:")
        print("  - ✅ 时间区间管理")
        print("  - ✅ 语音片段存储")
        print("  - ✅ 说话人档案管理")
        print("  - ✅ 统计信息自动更新")
        print("  - ✅ 完整转录文本生成")
        print("  - ✅ JSON序列化/反序列化")
        print("  - ✅ 文件保存/加载")
        print("  - ✅ 便捷创建函数")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1) 