#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化VAD测试脚本
测试现有的VAD功能是否正常工作
"""

import os
import sys
import traceback
import numpy as np

# 添加utils目录到Python路径
sys.path.append('utils')

def test_vad_imports():
    """测试VAD相关模块的导入"""
    print("🔍 测试VAD模块导入...")
    
    try:
        from speech_recognition_utils import (
            load_vad_model, vad_segment, vad_segment_for_two_person,
            get_optimized_vad_params_for_two_person, 
            set_offline_mode
        )
        print("✅ VAD相关函数导入成功")
        return True
    except ImportError as e:
        print(f"❌ VAD模块导入失败: {str(e)}")
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_vad_model_loading():
    """测试VAD模型加载"""
    print("\n🔍 测试VAD模型加载...")
    
    try:
        from speech_recognition_utils import load_vad_model, set_offline_mode
        
        # 设置离线模式
        set_offline_mode()
        
        # 检查本地模型路径
        potential_paths = [
            "models/Qwen3-Reranker-0.6B",
            "models",
            "config/models"
        ]
        
        vad_model = None
        for path in potential_paths:
            if os.path.exists(path):
                print(f"  尝试从路径加载VAD模型: {path}")
                try:
                    vad_model = load_vad_model(path)
                    if vad_model is not None:
                        print(f"✅ VAD模型从 {path} 加载成功")
                        break
                except Exception as e:
                    print(f"  从 {path} 加载失败: {str(e)}")
        
        if vad_model is None:
            print("⚠️ 未找到可用的VAD模型，尝试默认加载")
            vad_model = load_vad_model()
        
        return vad_model is not None
        
    except Exception as e:
        print(f"❌ VAD模型加载测试失败: {str(e)}")
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_vad_parameters():
    """测试VAD参数获取"""
    print("\n🔍 测试VAD参数获取...")
    
    try:
        from speech_recognition_utils import get_optimized_vad_params_for_two_person
        
        params = get_optimized_vad_params_for_two_person()
        print(f"✅ VAD参数获取成功: {params}")
        return True
        
    except Exception as e:
        print(f"❌ VAD参数获取失败: {str(e)}")
        return False

def test_create_test_audio():
    """创建测试音频文件"""
    print("\n🔍 创建测试音频文件...")
    
    try:
        import soundfile as sf
        
        # 生成简单的测试音频
        sample_rate = 16000
        duration = 5  # 5秒
        t = np.linspace(0, duration, duration * sample_rate)
        
        # 创建复合信号：语音-静音-语音
        audio = np.zeros_like(t)
        
        # 语音段1: 0-2秒
        speech1_mask = (t >= 0) & (t < 2)
        audio[speech1_mask] = 0.3 * np.sin(2 * np.pi * 440 * t[speech1_mask])
        
        # 静音段: 2-3秒 
        silence_mask = (t >= 2) & (t < 3)
        audio[silence_mask] = 0.01 * np.random.randn(np.sum(silence_mask))
        
        # 语音段2: 3-5秒
        speech2_mask = (t >= 3) & (t <= 5)
        audio[speech2_mask] = 0.3 * np.sin(2 * np.pi * 880 * t[speech2_mask])
        
        # 保存测试音频
        test_file = "test_vad_audio.wav"
        sf.write(test_file, audio, sample_rate)
        
        print(f"✅ 测试音频已创建: {test_file}")
        return test_file
        
    except Exception as e:
        print(f"❌ 创建测试音频失败: {str(e)}")
        return None

def test_vad_functionality(test_audio_file):
    """测试VAD分割功能"""
    print("\n🔍 测试VAD分割功能...")
    
    try:
        from speech_recognition_utils import vad_segment, load_vad_model, set_offline_mode
        
        # 设置离线模式
        set_offline_mode()
        
        # 尝试加载VAD模型
        vad_model = load_vad_model()
        if vad_model is None:
            print("⚠️ 无法加载VAD模型，跳过FunASR VAD测试")
            return False
        
        # 执行VAD分割
        print("  执行VAD分割...")
        segments = vad_segment(test_audio_file, vad_model)
        
        print(f"✅ VAD分割完成，检测到 {len(segments)} 个语音段:")
        for i, (start, end) in enumerate(segments):
            print(f"  段{i+1}: {start}ms - {end}ms ({(end-start)/1000:.2f}秒)")
        
        return True
        
    except Exception as e:
        print(f"❌ VAD分割功能测试失败: {str(e)}")
        print(f"详细错误: {traceback.format_exc()}")
        return False

def test_vad_for_two_person(test_audio_file):
    """测试针对两人对话优化的VAD"""
    print("\n🔍 测试两人对话VAD...")
    
    try:
        from speech_recognition_utils import vad_segment_for_two_person, load_vad_model, set_offline_mode
        
        # 设置离线模式
        set_offline_mode()
        
        # 尝试加载VAD模型
        vad_model = load_vad_model()
        if vad_model is None:
            print("⚠️ 无法加载VAD模型，跳过两人对话VAD测试")
            return False
        
        # 执行优化的VAD分割
        print("  执行两人对话优化VAD分割...")
        segments = vad_segment_for_two_person(test_audio_file, vad_model)
        
        print(f"✅ 两人对话VAD分割完成，检测到 {len(segments)} 个语音段:")
        for i, (start, end) in enumerate(segments):
            print(f"  段{i+1}: {start}ms - {end}ms ({(end-start)/1000:.2f}秒)")
        
        return True
        
    except Exception as e:
        print(f"❌ 两人对话VAD测试失败: {str(e)}")
        print(f"详细错误: {traceback.format_exc()}")
        return False

def main():
    """主测试函数"""
    print("🎯 简化VAD功能测试")
    print("="*50)
    
    test_results = {}
    
    # 1. 测试模块导入
    test_results['imports'] = test_vad_imports()
    
    # 2. 测试VAD模型加载
    test_results['model_loading'] = test_vad_model_loading()
    
    # 3. 测试VAD参数
    test_results['parameters'] = test_vad_parameters()
    
    # 4. 创建测试音频
    test_audio = test_create_test_audio()
    test_results['test_audio'] = test_audio is not None
    
    # 5. 测试VAD功能
    if test_audio:
        test_results['vad_basic'] = test_vad_functionality(test_audio)
        test_results['vad_two_person'] = test_vad_for_two_person(test_audio)
    else:
        test_results['vad_basic'] = False
        test_results['vad_two_person'] = False
    
    # 打印测试摘要
    print("\n" + "="*50)
    print("📋 测试摘要")
    print("="*50)
    
    passed_tests = sum(1 for result in test_results.values() if result)
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:<20}: {status}")
    
    print(f"\n总结: {passed_tests}/{total_tests} 个测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有VAD测试都通过了！")
    elif passed_tests > 0:
        print("⚠️ 部分VAD功能可用，需要进一步检查")
    else:
        print("❌ VAD功能可能存在问题，需要修复")

if __name__ == "__main__":
    main() 