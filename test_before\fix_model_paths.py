#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 FunASR模型路径自动修复工具 - 2024年12月最新版
基于FunASR官方Model Zoo的标准名称修复本地模型路径
解决"not registered"错误
"""

import os
import shutil
import argparse
from pathlib import Path
import platform

# 🔧 2024年12月最新：FunASR官方Model Zoo标准名称映射
OFFICIAL_MODEL_NAMES = {
    "paraformer": {
        "standard": "damo/speech_paraformer-large-vad-punc_asr_nat-zh-cn-16k-common-vocab8404-pytorch",
        "windows_safe": "damo_speech_paraformer-large-vad-punc_asr_nat-zh-cn-16k-common-vocab8404-pytorch",
        "simple": "paraformer-zh",
        "alternatives": [
            "damo/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-pytorch",
            "iic/speech_seaco_paraformer_large_asr_nat-zh-cn-16k-common-vocab8404-pytorch",
            "damo_speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-pytorch",
            "iic_speech_seaco_paraformer_large_asr_nat-zh-cn-16k-common-vocab8404-pytorch"
        ]
    },
    "sensevoice": {
        "standard": "iic/SenseVoiceSmall",
        "windows_safe": "iic_SenseVoiceSmall",
        "simple": "SenseVoiceSmall",
        "alternatives": [
            "FunAudioLLM/SenseVoiceSmall",
            "FunAudioLLM_SenseVoiceSmall"
        ]
    },
    "campplus": {
        "standard": "damo/speech_campplus_sv_zh-cn_16k-common",
        "windows_safe": "damo_speech_campplus_sv_zh-cn_16k-common",
        "simple": "cam++",
        "alternatives": [
            "damo/speech_campplus_sv_zh",
            "damo_speech_campplus_sv_zh"
        ]
    },
    "vad": {
        "standard": "damo/speech_fsmn_vad_zh-cn-16k-common-pytorch",
        "windows_safe": "damo_speech_fsmn_vad_zh-cn-16k-common-pytorch",
        "simple": "fsmn-vad",
        "alternatives": [
            "damo/speech_fsmn_vad_zh-cn_16k-common",
            "damo_speech_fsmn_vad_zh-cn_16k-common"
        ]
    }
}

def detect_model_type(model_path):
    """
    检测模型类型基于文件夹内容和名称
    """
    path_str = str(model_path).lower()
    
    # 检查文件夹名称
    if "paraformer" in path_str:
        return "paraformer"
    elif "sensevoice" in path_str:
        return "sensevoice"
    elif "campplus" in path_str or "cam++" in path_str:
        return "campplus"
    
    # 检查文件夹内容
    files = []
    if os.path.exists(model_path):
        try:
            files = os.listdir(model_path)
            files_str = " ".join(files).lower()
            
            if "model.py" in files_str and any(word in files_str for word in ["sense", "voice"]):
                return "sensevoice"
            elif "pytorch_model.bin" in files_str and "paraformer" in files_str:
                return "paraformer"
            elif "campplus" in files_str or "sv" in files_str:
                return "campplus"
                
        except Exception:
            pass
    
    return "unknown"

def create_standard_structure(source_path, target_path, model_type):
    """
    创建标准的模型文件夹结构
    """
    try:
        # 创建目标目录结构
        target_dir = Path(target_path)
        target_dir.parent.mkdir(parents=True, exist_ok=True)
        
        if os.path.exists(target_path):
            print(f"⚠️  目标路径已存在: {target_path}")
            response = input("是否覆盖? (y/N): ").strip().lower()
            if response != 'y':
                return False
            shutil.rmtree(target_path)
        
        # 复制模型文件
        shutil.copytree(source_path, target_path)
        print(f"✅ 成功复制模型到: {target_path}")
        
        # 验证必要文件
        required_files = {
            "paraformer": ["config.yaml", "pytorch_model.bin"],
            "sensevoice": ["config.yaml", "model.py"],
            "campplus": ["config.yaml", "pytorch_model.bin"]
        }
        
        if model_type in required_files:
            missing_files = []
            for file in required_files[model_type]:
                if not os.path.exists(os.path.join(target_path, file)):
                    missing_files.append(file)
            
            if missing_files:
                print(f"⚠️  警告：缺少以下文件: {', '.join(missing_files)}")
                return True  # 仍然返回True，因为文件已复制
        
        return True
        
    except Exception as e:
        print(f"❌ 创建标准结构失败: {str(e)}")
        return False

def fix_model_paths(models_dir, dry_run=False):
    """
    自动修复模型路径
    """
    models_path = Path(models_dir)
    if not models_path.exists():
        print(f"❌ 模型目录不存在: {models_dir}")
        return False
    
    print(f"🔍 扫描模型目录: {models_dir}")
    fixed_count = 0
    
    # 遍历模型目录中的子文件夹
    for item in models_path.iterdir():
        if not item.is_dir():
            continue
        
        model_type = detect_model_type(item)
        if model_type == "unknown":
            print(f"⚠️  跳过未知模型类型: {item.name}")
            continue
        
        # 获取官方标准名称
        official_name = OFFICIAL_MODEL_NAMES[model_type]["standard"]
        print(f"\n🎯 检测到 {model_type.upper()} 模型: {item.name}")
        print(f"📋 建议标准名称: {official_name}")
        
        # 构建新的路径
        new_path = models_path / official_name
        
        if str(item) == str(new_path):
            print(f"✅ 模型路径已经是标准格式")
            continue
        
        if dry_run:
            print(f"🔧 [预览模式] 将会重命名为: {new_path}")
            fixed_count += 1
        else:
            # 询问用户确认
            print(f"原路径: {item}")
            print(f"新路径: {new_path}")
            response = input("是否执行重命名? (y/N): ").strip().lower()
            
            if response == 'y':
                if create_standard_structure(str(item), str(new_path), model_type):
                    print(f"✅ 成功修复模型路径")
                    
                    # 询问是否删除原文件夹
                    delete_response = input("是否删除原文件夹? (y/N): ").strip().lower()
                    if delete_response == 'y':
                        try:
                            shutil.rmtree(str(item))
                            print(f"🗑️  已删除原文件夹: {item}")
                        except Exception as e:
                            print(f"⚠️  删除原文件夹失败: {str(e)}")
                    
                    fixed_count += 1
                else:
                    print(f"❌ 修复失败")
            else:
                print(f"⏭️  跳过: {item.name}")
    
    print(f"\n🎉 修复完成！共处理 {fixed_count} 个模型")
    return True

def show_current_status(models_dir):
    """
    显示当前模型状态
    """
    models_path = Path(models_dir)
    if not models_path.exists():
        print(f"❌ 模型目录不存在: {models_dir}")
        return
    
    print(f"📊 当前模型状态报告")
    print(f"📁 模型目录: {models_dir}")
    print("-" * 60)
    
    standard_models = []
    non_standard_models = []
    
    for item in models_path.iterdir():
        if not item.is_dir():
            continue
        
        model_type = detect_model_type(item)
        if model_type == "unknown":
            print(f"❓ 未知类型: {item.name}")
            continue
        
        official_name = OFFICIAL_MODEL_NAMES[model_type]["standard"]
        
        if item.name == official_name or str(item).endswith(official_name):
            standard_models.append((model_type, item.name))
            print(f"✅ 标准格式 [{model_type.upper()}]: {item.name}")
        else:
            non_standard_models.append((model_type, item.name, official_name))
            print(f"⚠️  非标准格式 [{model_type.upper()}]: {item.name}")
            print(f"   建议名称: {official_name}")
    
    print("-" * 60)
    print(f"📈 统计：标准格式 {len(standard_models)} 个，非标准格式 {len(non_standard_models)} 个")
    
    if non_standard_models:
        print("\n💡 建议：运行以下命令修复非标准模型路径：")
        print("python fix_model_paths.py --fix")

def convert_to_windows_safe_path(model_name):
    """
    将包含斜杠的模型名称转换为Windows兼容的路径
    """
    return model_name.replace('/', '_').replace('\\', '_')

def get_platform_compatible_model_names(model_type):
    """
    根据操作系统返回兼容的模型名称列表
    """
    if model_type not in OFFICIAL_MODEL_NAMES:
        return []
    
    model_info = OFFICIAL_MODEL_NAMES[model_type]
    names = []
    
    # 添加标准名称
    names.append(model_info["standard"])
    
    # 如果是Windows系统，优先添加Windows安全名称
    if platform.system() == "Windows":
        if "windows_safe" in model_info:
            names.insert(0, model_info["windows_safe"])  # 优先使用
        
        # 添加所有Windows兼容的替代名称
        for alt in model_info.get("alternatives", []):
            windows_alt = convert_to_windows_safe_path(alt)
            if windows_alt not in names:
                names.append(windows_alt)
    
    # 添加简化名称
    if "simple" in model_info:
        names.append(model_info["simple"])
    
    # 添加替代名称
    names.extend(model_info.get("alternatives", []))
    
    return names

def main():
    parser = argparse.ArgumentParser(description="FunASR模型路径自动修复工具")
    parser.add_argument("--models-dir", default="./models", help="模型文件夹路径")
    parser.add_argument("--fix", action="store_true", help="执行修复操作")
    parser.add_argument("--dry-run", action="store_true", help="预览模式，不执行实际操作")
    parser.add_argument("--status", action="store_true", help="显示当前模型状态")
    
    args = parser.parse_args()
    
    print("🔧 FunASR模型路径自动修复工具 - 2024年12月最新版")
    print("=" * 60)
    
    if args.status:
        show_current_status(args.models_dir)
    elif args.fix or args.dry_run:
        fix_model_paths(args.models_dir, dry_run=args.dry_run)
    else:
        print("使用说明：")
        print("  --status    : 查看当前模型状态")
        print("  --dry-run   : 预览修复操作（不执行实际修复）")
        print("  --fix       : 执行修复操作")
        print("\n示例：")
        print("  python fix_model_paths.py --status")
        print("  python fix_model_paths.py --dry-run")
        print("  python fix_model_paths.py --fix")

if __name__ == "__main__":
    main() 