# 建议的开发命令

## 环境管理

### 虚拟环境激活 (Windows)
```powershell
# 激活虚拟环境 (必须在项目根目录)
.venv\Scripts\activate

# 检查Python版本
python --version

# 检查包管理工具
uv --version
```

### 依赖管理
```powershell
# 安装依赖 (使用阿里源)
uv pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/

# 安装语音处理专用依赖
uv pip install -r requirements_speech.txt -i https://mirrors.aliyun.com/pypi/simple/

# 查看已安装包
uv pip list

# 检查特定包版本
uv pip show torch
uv pip show transformers
uv pip show funasr
```

## 服务启动

### 推荐启动顺序
```powershell
# 1. 启动Redis (Docker)
docker run -d --name redis-server -p 6379:6379 redis:latest

# 2. 激活虚拟环境并启动后端
.venv\Scripts\activate
powershell -Command ".venv/Scripts/activate ; python -m uvicorn backend.main:app --host 0.0.0.0 --port 8002 --reload"

# 3. 启动Celery Worker (新终端，需激活虚拟环境)
.venv\Scripts\activate
python start_worker_windows.py

# 4. 启动前端 (新终端)
cd frontend
npm run dev
```

### 自动启动脚本
```powershell
# 一键启动所有服务 (如果有配置)
start_services.bat

# 检查服务状态
check_system.bat
```

## 开发和测试

### 后端开发
```powershell
# 激活虚拟环境后运行所有命令
.venv\Scripts\activate

# 音频处理测试
uv run python test_audio_api.py

# 会议转录功能测试
uv run python test/test_solution2_fix.py

# 集成测试
uv run python test_integrated_audio_system.py

# GPU内存优化测试
uv run python test_gpu_memory_optimization.py
```

### 前端开发
```powershell
cd frontend

# 开发服务器
npm run dev

# 代码检查
npm run lint

# 构建生产版本
npm run build
```

### 数据库管理
```powershell
# 激活虚拟环境后运行
.venv\Scripts\activate

# 检查数据库状态
uv run python backend/debug_document_status.py

# 修复文档状态
uv run python backend/fix_document_status.py

# 数据库迁移 (如果需要)
uv run python backend/migrate_database.py
```

## 音频处理专用命令

### 模型验证
```powershell
# 激活虚拟环境
.venv\Scripts\activate

# 验证所有模型路径
uv run python -c "from backend.tasks.audio_processing_tasks import _validate_all_model_paths; print(_validate_all_model_paths())"

# 检查模型配置
uv run python -c "from backend.tasks.audio_processing_tasks import _get_model_path; print('SenseVoice:', _get_model_path('sensevoice')); print('VAD:', _get_model_path('vad')); print('Speaker:', _get_model_path('speaker'))"
```

### 离线环境测试
```powershell
# 设置离线环境变量
$env:HF_HUB_OFFLINE="1"
$env:TRANSFORMERS_OFFLINE="1"

# 测试离线模型加载
uv run python test/test_offline_model_loading.py

# 会议转录离线测试
uv run python test/test_meeting_transcription_offline.py
```

### 音频文件处理
```powershell
# 测试音频文件 (对话.mp3)
$testFile = "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\resource\对话.mp3"

# 验证文件存在
Test-Path $testFile

# 获取文件信息
Get-Item $testFile | Select-Object Name, Length, LastWriteTime
```

## 系统监控和调试

### 性能监控
```powershell
# 激活虚拟环境
.venv\Scripts\activate

# 内存监控
uv run python debug_memory_monitor.py

# 集成测试监控
uv run python monitor_integration_test.py

# GPU使用监控 (如果有GPU)
nvidia-smi
```

### 日志查看
```powershell
# 查看后端日志 (实时)
Get-Content backend\logs\app.log -Tail 50 -Wait

# 查看Celery Worker日志
Get-Content worker_safe.log -Tail 50 -Wait

# 查看系统日志
Get-Content logs\system.log -Tail 50 -Wait

# 搜索特定错误
Select-String "ERROR" backend\logs\app.log | Select-Object -Last 10
```

### 进程管理
```powershell
# 查看Python进程
Get-Process python

# 查看端口占用
netstat -an | findstr 8002
netstat -an | findstr 3000
netstat -an | findstr 6379

# 终止特定端口进程
$process = Get-NetTCPConnection -LocalPort 8002 -ErrorAction SilentlyContinue
if ($process) { Stop-Process -Id $process.OwningProcess -Force }
```

## 故障排除

### 常见问题解决
```powershell
# 重启语言服务器 (在Serena中使用)
# restart_language_server_serena

# 清理Python缓存
Get-ChildItem -Path . -Recurse -Name "__pycache__" | Remove-Item -Recurse -Force
Get-ChildItem -Path . -Recurse -Name "*.pyc" | Remove-Item -Force

# 重新安装依赖
uv pip install --force-reinstall -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/

# 清理npm缓存 (前端)
cd frontend
npm cache clean --force
Remove-Item node_modules -Recurse -Force
npm install
```

### 模型相关问题
```powershell
# 检查模型目录
Get-ChildItem "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\models\model_dir" -Recurse

# 验证模型文件完整性
Test-Path "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\models\model_dir\SenseVoiceSmall"
Test-Path "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\models\model_dir\fsmn_vad_zh"
Test-Path "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\models\model_dir\cam++"

# 重新下载模型 (如果需要)
uv run python scripts/download_models.py
```

### 服务重启
```powershell
# 重启Redis
docker stop redis-server
docker rm redis-server
docker run -d --name redis-server -p 6379:6379 redis:latest

# 重启后端 (Ctrl+C停止后重新运行)
.venv\Scripts\activate
python -m uvicorn backend.main:app --host 0.0.0.0 --port 8002 --reload

# 重启Celery Worker
.venv\Scripts\activate
python start_worker_windows.py
```

## 测试和验证

### 功能测试
```powershell
# 激活虚拟环境
.venv\Scripts\activate

# API健康检查
Invoke-RestMethod -Uri "http://localhost:8002/health" -Method GET

# 音频上传测试
$testFile = "D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\resource\对话.mp3"
# (需要通过前端界面或API客户端测试)

# 会议转录端到端测试
uv run python test/test_meeting_transcription_e2e.py
```

### 性能基准测试
```powershell
# 处理时间测试
Measure-Command { uv run python test/test_processing_time.py }

# 并发测试
uv run python test/test_concurrent_processing.py

# 内存使用测试
uv run python test/test_memory_usage.py
```

## 开发工具集成

### VS Code集成
```powershell
# 打开项目
code .

# Python解释器路径
# .venv\Scripts\python.exe
```

### Git操作
```powershell
# 查看状态
git status

# 提交更改
git add .
git commit -m "feat: 会议转录功能优化和离线模型配置修复"

# 推送更改 (需要用户明确同意)
# git push origin main
```

## 常用Windows PowerShell命令
```powershell
# 文件操作
Get-ChildItem          # 列出文件 (ls)
Set-Location           # 切换目录 (cd)
Get-Content            # 查看文件内容 (cat)
Select-String          # 搜索文本 (grep)
Copy-Item              # 复制文件 (cp)
Move-Item              # 移动文件 (mv)
Remove-Item            # 删除文件 (rm)

# 进程和服务
Get-Process            # 查看进程 (ps)
Stop-Process           # 终止进程 (kill)
Get-Service            # 查看服务
Start-Service          # 启动服务
Stop-Service           # 停止服务

# 网络
Test-NetConnection     # 测试网络连接
Get-NetTCPConnection   # 查看TCP连接
Invoke-RestMethod      # HTTP请求 (curl)
Invoke-WebRequest      # Web请求 (wget)

# 系统信息
Get-ComputerInfo       # 系统信息
Get-WmiObject          # WMI查询
Get-EventLog           # 事件日志
```