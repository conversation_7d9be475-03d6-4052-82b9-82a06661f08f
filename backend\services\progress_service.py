#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
进度追踪服务
用于追踪文档处理和向量化的实时进度
"""

import asyncio
import logging
import time
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum

logger = logging.getLogger(__name__)


class ProgressStatus(Enum):
    """进度状态枚举"""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class ProgressInfo:
    """进度信息数据类"""
    task_id: str
    title: str
    detail: str
    percentage: float
    status: ProgressStatus
    start_time: float
    end_time: Optional[float] = None
    error_message: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['status'] = self.status.value
        return data


class ProgressService:
    """进度追踪服务"""
    
    def __init__(self):
        self._progress_store: Dict[str, ProgressInfo] = {}
        self._callbacks: Dict[str, Callable] = {}
    
    def create_task(self, task_id: str, title: str, detail: str = "") -> ProgressInfo:
        """创建新的进度任务"""
        progress = ProgressInfo(
            task_id=task_id,
            title=title,
            detail=detail,
            percentage=0.0,
            status=ProgressStatus.NOT_STARTED,
            start_time=time.time()
        )
        self._progress_store[task_id] = progress
        logger.info(f"创建进度任务: {task_id} - {title}")
        return progress
    
    def update_progress(self, task_id: str, percentage: float, detail: str = "", 
                       status: Optional[ProgressStatus] = None) -> Optional[ProgressInfo]:
        """更新进度"""
        if task_id not in self._progress_store:
            logger.warning(f"进度任务不存在: {task_id}")
            return None
        
        progress = self._progress_store[task_id]
        progress.percentage = max(0.0, min(100.0, percentage))
        
        if detail:
            progress.detail = detail
        
        if status:
            progress.status = status
            if status in [ProgressStatus.COMPLETED, ProgressStatus.FAILED, ProgressStatus.CANCELLED]:
                progress.end_time = time.time()
        elif percentage >= 100.0:
            progress.status = ProgressStatus.COMPLETED
            progress.end_time = time.time()
        elif percentage > 0.0:
            progress.status = ProgressStatus.IN_PROGRESS
        
        logger.debug(f"更新进度: {task_id} - {percentage}% - {detail}")
        
        # 执行回调
        if task_id in self._callbacks:
            try:
                self._callbacks[task_id](progress)
            except Exception as e:
                logger.error(f"进度回调执行失败: {e}")
        
        return progress
    
    def complete_task(self, task_id: str, detail: str = "任务完成") -> Optional[ProgressInfo]:
        """完成任务"""
        return self.update_progress(task_id, 100.0, detail, ProgressStatus.COMPLETED)
    
    def fail_task(self, task_id: str, error_message: str) -> Optional[ProgressInfo]:
        """任务失败"""
        if task_id not in self._progress_store:
            return None
        
        progress = self._progress_store[task_id]
        progress.status = ProgressStatus.FAILED
        progress.error_message = error_message
        progress.end_time = time.time()
        
        logger.error(f"任务失败: {task_id} - {error_message}")
        return progress
    
    def cancel_task(self, task_id: str) -> Optional[ProgressInfo]:
        """取消任务"""
        return self.update_progress(task_id, 0.0, "任务已取消", ProgressStatus.CANCELLED)
    
    def get_progress(self, task_id: str) -> Optional[ProgressInfo]:
        """获取进度信息"""
        return self._progress_store.get(task_id)
    
    def get_all_progress(self) -> Dict[str, ProgressInfo]:
        """获取所有进度信息"""
        return self._progress_store.copy()
    
    def remove_task(self, task_id: str) -> bool:
        """移除任务"""
        if task_id in self._progress_store:
            del self._progress_store[task_id]
            if task_id in self._callbacks:
                del self._callbacks[task_id]
            logger.info(f"移除进度任务: {task_id}")
            return True
        return False
    
    def set_callback(self, task_id: str, callback: Callable[[ProgressInfo], None]):
        """设置进度回调"""
        self._callbacks[task_id] = callback
    
    def cleanup_completed_tasks(self, max_age_seconds: int = 3600):
        """清理已完成的旧任务"""
        current_time = time.time()
        to_remove = []
        
        for task_id, progress in self._progress_store.items():
            if (progress.status in [ProgressStatus.COMPLETED, ProgressStatus.FAILED, ProgressStatus.CANCELLED] 
                and progress.end_time 
                and current_time - progress.end_time > max_age_seconds):
                to_remove.append(task_id)
        
        for task_id in to_remove:
            self.remove_task(task_id)
        
        if to_remove:
            logger.info(f"清理了 {len(to_remove)} 个过期任务")


# 全局进度服务实例
progress_service = ProgressService()


class ProgressTracker:
    """进度追踪器上下文管理器"""
    
    def __init__(self, task_id: str, title: str, detail: str = ""):
        self.task_id = task_id
        self.title = title
        self.detail = detail
        self.progress_info = None
    
    def __enter__(self):
        self.progress_info = progress_service.create_task(self.task_id, self.title, self.detail)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            # 发生异常，标记为失败
            progress_service.fail_task(self.task_id, str(exc_val))
        else:
            # 正常完成
            progress_service.complete_task(self.task_id)
    
    def update(self, percentage: float, detail: str = ""):
        """更新进度"""
        progress_service.update_progress(self.task_id, percentage, detail)
    
    def set_detail(self, detail: str):
        """设置详细信息"""
        progress_service.update_progress(self.task_id, self.progress_info.percentage, detail)


def track_progress(task_id: str, title: str, detail: str = ""):
    """进度追踪装饰器工厂"""
    def decorator(func):
        async def async_wrapper(*args, **kwargs):
            with ProgressTracker(task_id, title, detail) as tracker:
                return await func(tracker, *args, **kwargs)
        
        def sync_wrapper(*args, **kwargs):
            with ProgressTracker(task_id, title, detail) as tracker:
                return func(tracker, *args, **kwargs)
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else sync_wrapper
    return decorator
