# 语音处理智能平台 Docker 部署指南

## 概述

本Docker部署方案专门解决RAG检索系统中的向量维度不匹配问题，确保容器环境与本地开发环境完全一致。

## 部署架构

```
项目根目录/
├── .dockerignore              # Docker构建忽略文件 (重要!)
└── my_notebook_docker/
    ├── docker-compose.yml     # 主要服务编排
    ├── .env.template         # 环境变量模板
    ├── Dockerfile.frontend   # Frontend服务镜像 (Vue.js + Nginx)
    ├── Dockerfile.backend    # Backend服务镜像 (FastAPI)
    ├── Dockerfile.celery     # Celery Worker服务镜像
    ├── nginx.conf            # Nginx配置文件
    ├── scripts/
    │   ├── start.sh         # 启动脚本
    │   ├── stop.sh          # 停止脚本
    │   └── health-check.sh  # 健康检查脚本
    └── volumes/              # 数据持久化目录
        ├── data/            # 数据库和向量数据库
        ├── logs/            # 日志文件
        ├── models/          # AI模型文件
        └── uploads/         # 上传文件

```

## 服务组件

- **Frontend服务**: Vue.js + Nginx (端口3000 → 80)
- **Backend服务**: FastAPI应用 (端口8002)
- **Celery Worker服务**: 向量化任务处理
- **Redis服务**: 任务队列存储
- **Flower监控**: Celery任务监控 (端口5555)
- **共享存储**: ChromaDB、SQLite、模型文件

## 关键特性

1. **环境一致性**: 完全复制本地.venv环境到容器
2. **向量维度兼容**: 确保768维nomic-embed-text模型一致性
3. **数据持久化**: 所有重要数据映射到宿主机
4. **健康检查**: 自动验证服务状态
5. **一键部署**: 简化的启动和管理脚本

## Docker构建命令

### 重要提醒

**构建前请确认**：
- ✅ 项目根目录存在 `.dockerignore` 文件
- ✅ `.dockerignore` 正确排除了不必要的文件
- ✅ 必需的目录（.venv, frontend, backend, config, utils）未被排除
- ✅ 这将显著减少构建时间和镜像大小

**验证.dockerignore配置**：
```bash
# 验证.dockerignore配置是否正确
python validate_dockerignore.py
```

### 构建所有服务镜像

```bash
# 构建所有服务
docker-compose build

# 构建特定服务
docker-compose build frontend
docker-compose build backend
docker-compose build celery-worker

# 强制重新构建（不使用缓存）
docker-compose build --no-cache

# 并行构建（加速）
docker-compose build --parallel
```

### 单独构建镜像

```bash
# 构建前端镜像
docker build -f my_notebook_docker/Dockerfile.frontend -t speech-platform-frontend:latest .

# 构建后端镜像
docker build -f my_notebook_docker/Dockerfile.backend -t speech-platform-backend:latest .

# 构建Celery镜像
docker build -f my_notebook_docker/Dockerfile.celery -t speech-platform-celery:latest .
```

### 镜像管理

```bash
# 查看构建的镜像
docker images | grep speech-platform

# 清理未使用的镜像
docker image prune -f

# 清理所有相关镜像（谨慎使用）
docker rmi $(docker images "speech-platform*" -q)
```

### .dockerignore 优化效果

正确配置的 `.dockerignore` 文件带来的优化：

- **构建速度提升**: 排除不必要文件，减少上下文传输时间
- **镜像大小减少**: 避免包含测试文件、文档、缓存等
- **安全性增强**: 防止敏感文件（.env、密钥等）进入镜像
- **构建稳定性**: 避免因临时文件变化导致的不必要重建

**被排除的主要内容**：
- 开发和测试文件 (`test/`, `*_test.py`)
- 文档和说明 (`*.md`, `docs/`)
- 缓存和临时文件 (`__pycache__/`, `*.log`)
- 大型数据文件 (`models/`, `data/uploads/`)
- 敏感配置文件 (`.env`, `*.key`)
- IDE和系统文件 (`.vscode/`, `.DS_Store`)

## 快速开始

### Windows环境

1. 复制环境配置：
   ```cmd
   copy .env.template .env
   REM 编辑.env文件，配置您的环境变量
   ```

2. 启动服务：
   ```cmd
   scripts\start.bat
   ```

3. 验证部署：
   ```cmd
   scripts\health-check.bat
   REM 或者运行完整验证
   python validate_deployment.py
   ```

4. 停止服务：
   ```cmd
   scripts\stop.bat
   ```

### Linux/Mac环境

1. 复制环境配置：
   ```bash
   cp .env.template .env
   # 编辑.env文件，配置您的环境变量
   ```

2. 启动服务：
   ```bash
   ./scripts/start.sh
   ```

3. 验证部署：
   ```bash
   ./scripts/health-check.sh
   # 或者运行完整验证
   python3 validate_deployment.py
   ```

4. 停止服务：
   ```bash
   ./scripts/stop.sh
   ```

## 环境变量配置

### 关键配置项说明

在 `.env` 文件中，以下配置项对解决向量维度不匹配问题至关重要：

```env
# 前端服务配置
FRONTEND_PORT=3000                    # 前端服务端口
FRONTEND_API_PROXY_HOST=backend       # API代理目标主机
FRONTEND_API_PROXY_PORT=8002          # API代理目标端口

# Nginx配置
NGINX_CLIENT_MAX_BODY_SIZE=100M       # 最大上传文件大小
NGINX_WORKER_PROCESSES=auto           # Nginx工作进程数

# Ollama配置 - 确保容器能访问宿主机的Ollama服务
OLLAMA_BASE_URL=http://host.docker.internal:11434

# 嵌入模型配置 - 必须与现有向量数据库(768维)兼容
EMBEDDING_MODEL=nomic-embed-text:latest
EMBEDDING_DIMENSION=768

# ChromaDB配置 - 确保路径一致性
CHROMADB_PATH=/app/data/chroma_db
CHROMADB_COLLECTION_NAME=knowledge_base

# 数据库配置
DATABASE_URL=sqlite:///./data/speech_platform.db

# Redis配置 - 容器间通信
REDIS_URL=redis://redis:6379/0
CELERY_BROKER_URL=redis://redis:6379/0
```

### 配置检查清单

部署前请确认：

1. **Ollama服务运行**：
   ```bash
   ollama list  # 检查已安装的模型
   ollama show nomic-embed-text  # 确认模型详情
   ```

2. **模型维度验证**：
   确保 `nomic-embed-text` 模型输出768维向量

3. **数据目录权限**：
   确保 `volumes/` 目录可读写

4. **端口可用性**：
   确保端口 3000、8002、6379、5555 未被占用

5. **前端构建环境**：
   确保Node.js 18+环境可用（Docker构建时需要）

详细的环境变量配置请参考 `.env.template` 文件。

## 服务访问地址

部署成功后，可以通过以下地址访问各个服务：

- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:8002
- **API文档**: http://localhost:8002/docs
- **Flower监控**: http://localhost:5555 (需要启用monitoring profile)
- **健康检查**:
  - 前端: http://localhost:3000/health
  - 后端: http://localhost:8002/health

## 故障排除

### 常见问题解决

**前端相关问题：**
```bash
# 前端无法访问
docker-compose logs frontend

# 检查nginx配置
docker-compose exec frontend nginx -t

# 重启前端服务
docker-compose restart frontend
```

**API代理问题：**
```bash
# 检查后端服务状态
docker-compose ps backend

# 测试API连接
curl http://localhost:3000/api/health
curl http://localhost:8002/health
```

**向量维度不匹配问题：**
1. 检查OLLAMA_BASE_URL配置
2. 验证nomic-embed-text模型版本
3. 查看容器日志：`docker-compose logs backend`

**构建失败问题：**
```bash
# 清理构建缓存
docker-compose build --no-cache

# 检查磁盘空间
docker system df

# 清理未使用资源
docker system prune -f

# 如果遇到COPY命令错误，检查文件是否存在
ls -la .venv/ frontend/ backend/ config/ utils/
```

**常见构建错误修复：**
- `COPY pyproject.toml failed`: 文件不存在，已在Dockerfile中处理
- `failed to compute cache key`: 通常是COPY命令语法错误，已修复
- `heredoc syntax error`: shell脚本语法问题，已使用echo命令替代

## 数据备份

重要数据存储在 `volumes/` 目录下，请定期备份：
- `volumes/data/chroma_db/` - 向量数据库
- `volumes/data/speech_platform.db` - SQLite数据库
- `volumes/models/` - AI模型文件
