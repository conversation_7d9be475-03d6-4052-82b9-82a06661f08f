"""
基础导入测试
检查新创建的服务模块是否可以正常导入
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试基础导入"""
    print("🚀 开始测试基础导入...")
    
    tests = []
    
    # 测试Redis连接
    try:
        import redis
        redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        redis_client.ping()
        tests.append(("Redis连接", True, "连接成功"))
    except Exception as e:
        tests.append(("Redis连接", False, str(e)))
    
    # 测试Celery导入
    try:
        from celery import Celery
        tests.append(("Celery导入", True, "导入成功"))
    except Exception as e:
        tests.append(("Celery导入", False, str(e)))
    
    # 测试现有服务导入
    try:
        from backend.services.rag_service import rag_service
        tests.append(("RAG服务导入", True, "导入成功"))
    except Exception as e:
        tests.append(("RAG服务导入", False, str(e)))
    
    # 测试数据库模型
    try:
        from backend.models.task_models import TaskRecord, TaskStatus
        tests.append(("任务模型导入", True, "导入成功"))
    except Exception as e:
        tests.append(("任务模型导入", False, str(e)))
    
    # 测试新服务（如果存在）
    service_tests = [
        ("enhanced_progress_service", "backend.services.enhanced_progress_service"),
        ("resource_monitor", "backend.services.resource_monitor"),
        ("concurrency_control", "backend.services.concurrency_control"),
        ("error_handler", "backend.services.error_handler"),
        ("timeout_control", "backend.services.timeout_control"),
        ("task_persistence_service", "backend.services.task_persistence_service"),
        ("task_recovery_service", "backend.services.task_recovery_service"),
    ]
    
    for service_name, module_path in service_tests:
        try:
            __import__(module_path)
            tests.append((f"{service_name}导入", True, "导入成功"))
        except Exception as e:
            tests.append((f"{service_name}导入", False, str(e)))
    
    # 测试任务队列
    try:
        from backend.core.task_queue import get_task_manager
        task_manager = get_task_manager()
        tests.append(("任务队列导入", True, "导入成功"))
    except Exception as e:
        tests.append(("任务队列导入", False, str(e)))
    
    # 打印结果
    print("\n" + "="*60)
    print("导入测试结果:")
    print("="*60)
    
    passed = 0
    total = len(tests)
    
    for test_name, success, message in tests:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name}: {status}")
        if not success:
            print(f"  错误: {message}")
        if success:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    
    if passed == total:
        print("🎉 所有导入测试通过！")
        return True
    else:
        print("⚠️ 部分导入测试失败")
        return False


def test_simple_functionality():
    """测试简单功能"""
    print("\n🔧 开始测试简单功能...")
    
    try:
        # 测试Redis基本操作
        import redis
        redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        
        # 写入测试数据
        test_key = "test_optimized_system"
        test_value = "test_value_123"
        redis_client.set(test_key, test_value, ex=60)  # 60秒过期
        
        # 读取测试数据
        retrieved_value = redis_client.get(test_key)
        
        if retrieved_value == test_value:
            print("✅ Redis读写测试通过")
        else:
            print("❌ Redis读写测试失败")
            return False
        
        # 清理测试数据
        redis_client.delete(test_key)
        
        return True
        
    except Exception as e:
        print(f"❌ 简单功能测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 开始基础系统测试...\n")
    
    # 测试导入
    import_success = test_imports()
    
    # 测试简单功能
    func_success = test_simple_functionality()
    
    print("\n" + "="*60)
    print("测试总结:")
    print("="*60)
    
    if import_success and func_success:
        print("🎉 基础测试全部通过！系统准备就绪")
        return 0
    else:
        print("⚠️ 部分测试失败，需要修复问题")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
