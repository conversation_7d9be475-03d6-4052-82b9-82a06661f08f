"""
音频处理API测试
测试音频上传、处理、任务管理等功能
"""

import sys
import os
import time
import asyncio
import tempfile
import json
from pathlib import Path
from typing import Dict, Any

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import pytest
import httpx
from fastapi.testclient import TestClient

# 导入应用
from backend.main import app
from backend.core.config import settings
from backend.core.database import get_db_session
from backend.core.task_queue import get_task_manager
from backend.core.security import create_access_token

# 创建测试客户端
client = TestClient(app)

# 测试配置
TEST_USER_ID = "test_user_audio"
TEST_TOKEN = create_access_token({"sub": "1", "username": "test_user"})
TEST_AUDIO_FILE = "test_audio.wav"

class TestAudioAPI:
    """音频API测试类"""
    
    @classmethod
    def setup_class(cls):
        """测试类初始化"""
        print("=== 初始化音频API测试 ===")
        cls.uploaded_files = []
        cls.created_tasks = []
        
        # 创建测试音频文件
        cls.test_audio_path = cls.create_test_audio_file()
        print(f"✅ 创建测试音频文件: {cls.test_audio_path}")
    
    @classmethod
    def teardown_class(cls):
        """测试类清理"""
        print("=== 清理音频API测试 ===")
        
        # 清理上传的文件
        for file_id in cls.uploaded_files:
            try:
                response = client.delete(
                    f"/api/v1/audio/{file_id}",
                    headers={"Authorization": f"Bearer {TEST_TOKEN}"}
                )
                print(f"清理文件: {file_id}")
            except Exception as e:
                print(f"清理文件失败: {e}")
        
        # 清理创建的任务
        for task_id in cls.created_tasks:
            try:
                response = client.delete(
                    f"/api/v1/speech/task/{task_id}",
                    headers={"Authorization": f"Bearer {TEST_TOKEN}"}
                )
                print(f"清理任务: {task_id}")
            except Exception as e:
                print(f"清理任务失败: {e}")
        
        # 删除测试文件
        if hasattr(cls, 'test_audio_path') and os.path.exists(cls.test_audio_path):
            os.unlink(cls.test_audio_path)
            print("✅ 删除测试音频文件")
    
    @staticmethod
    def create_test_audio_file() -> str:
        """创建测试音频文件"""
        try:
            import numpy as np
            import soundfile as sf
            
            # 生成1秒的正弦波音频
            sample_rate = 16000
            duration = 1.0
            frequency = 440  # A4音符
            
            t = np.linspace(0, duration, int(sample_rate * duration))
            audio_data = 0.5 * np.sin(2 * np.pi * frequency * t)
            
            # 保存到临时文件
            temp_file = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
            sf.write(temp_file.name, audio_data, sample_rate)
            
            return temp_file.name
            
        except ImportError:
            # 如果没有音频库，创建一个假的WAV文件
            temp_file = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
            
            # 写入最小的WAV文件头
            wav_header = b'RIFF\x24\x08\x00\x00WAVEfmt \x10\x00\x00\x00\x01\x00\x01\x00\x80>\x00\x00\x00}\x00\x00\x02\x00\x10\x00data\x00\x08\x00\x00'
            temp_file.write(wav_header)
            temp_file.write(b'\x00' * 2048)  # 添加一些音频数据
            temp_file.close()
            
            return temp_file.name
    
    def test_health_check(self):
        """测试健康检查"""
        print("\n--- 测试健康检查 ---")
        
        response = client.get("/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "healthy"
        print("✅ 健康检查通过")
    
    def test_audio_file_upload(self):
        """测试音频文件上传"""
        print("\n--- 测试音频文件上传 ---")
        
        with open(self.test_audio_path, 'rb') as f:
            files = {"file": (TEST_AUDIO_FILE, f, "audio/wav")}
            response = client.post(
                "/api/v1/audio/upload",
                files=files,
                headers={"Authorization": f"Bearer {TEST_TOKEN}"}
            )
        
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        assert "file_id" in data
        assert data["file_info"]["filename"] == TEST_AUDIO_FILE
        
        # 保存文件ID用于后续测试
        self.uploaded_files.append(data["file_id"])
        self.test_file_id = data["file_id"]
        
        print(f"✅ 音频上传成功: {data['file_id']}")
    
    def test_audio_file_list(self):
        """测试获取音频文件列表"""
        print("\n--- 测试获取音频文件列表 ---")
        
        response = client.get(
            "/api/v1/audio/",
            headers={"Authorization": f"Bearer {TEST_TOKEN}"}
        )
        
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, list)
        print(f"✅ 获取文件列表成功: {len(data)} 个文件")
    
    def test_audio_file_info(self):
        """测试获取音频文件信息"""
        print("\n--- 测试获取音频文件信息 ---")
        
        if not hasattr(self, 'test_file_id'):
            pytest.skip("需要先上传文件")
        
        response = client.get(
            f"/api/v1/audio/{self.test_file_id}",
            headers={"Authorization": f"Bearer {TEST_TOKEN}"}
        )
        
        assert response.status_code == 200
        print("✅ 获取文件信息成功")
    
    def test_batch_upload(self):
        """测试批量上传"""
        print("\n--- 测试批量上传 ---")
        
        # 创建多个测试文件
        files = []
        for i in range(2):
            with open(self.test_audio_path, 'rb') as f:
                files.append(("files", (f"test_audio_{i}.wav", f.read(), "audio/wav")))
        
        response = client.post(
            "/api/v1/audio/upload/batch",
            files=files,
            headers={"Authorization": f"Bearer {TEST_TOKEN}"}
        )
        
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        assert data["success_count"] >= 0
        
        # 保存上传的文件ID
        for file_info in data["uploaded_files"]:
            self.uploaded_files.append(file_info["id"])
        
        print(f"✅ 批量上传成功: {data['success_count']} 个文件")
    
    def test_vad_detection(self):
        """测试VAD检测"""
        print("\n--- 测试VAD检测 ---")
        
        if not hasattr(self, 'test_file_id'):
            pytest.skip("需要先上传文件")
        
        request_data = {
            "file_ids": [self.test_file_id],
            "config": {
                "merge_length_s": 15,
                "min_speech_duration": 0.5,
                "threshold": 0.5
            }
        }
        
        response = client.post(
            "/api/v1/speech/vad-detection",
            json=request_data,
            headers={"Authorization": f"Bearer {TEST_TOKEN}"}
        )
        
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        assert "task_id" in data
        
        self.created_tasks.append(data["task_id"])
        self.vad_task_id = data["task_id"]
        
        print(f"✅ VAD检测任务创建成功: {data['task_id']}")
    
    def test_speech_recognition(self):
        """测试语音识别"""
        print("\n--- 测试语音识别 ---")
        
        if not hasattr(self, 'test_file_id'):
            pytest.skip("需要先上传文件")
        
        request_data = {
            "file_ids": [self.test_file_id],
            "language": "auto",
            "use_itn": True,
            "ban_emo_unk": False,
            "config": {}
        }
        
        response = client.post(
            "/api/v1/speech/speech-recognition",
            json=request_data,
            headers={"Authorization": f"Bearer {TEST_TOKEN}"}
        )
        
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        assert "task_id" in data
        
        self.created_tasks.append(data["task_id"])
        self.speech_task_id = data["task_id"]
        
        print(f"✅ 语音识别任务创建成功: {data['task_id']}")
    
    def test_speaker_recognition(self):
        """测试说话人识别"""
        print("\n--- 测试说话人识别 ---")
        
        if not hasattr(self, 'test_file_id'):
            pytest.skip("需要先上传文件")
        
        request_data = {
            "file_ids": [self.test_file_id],
            "clustering_method": "auto",
            "expected_speakers": 2,
            "similarity_threshold": 0.7,
            "config": {}
        }
        
        response = client.post(
            "/api/v1/speech/speaker-recognition",
            json=request_data,
            headers={"Authorization": f"Bearer {TEST_TOKEN}"}
        )
        
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        assert "task_id" in data
        
        self.created_tasks.append(data["task_id"])
        self.speaker_task_id = data["task_id"]
        
        print(f"✅ 说话人识别任务创建成功: {data['task_id']}")
    
    def test_meeting_transcription(self):
        """测试会议转录"""
        print("\n--- 测试会议转录 ---")
        
        if not hasattr(self, 'test_file_id'):
            pytest.skip("需要先上传文件")
        
        request_data = {
            "file_ids": [self.test_file_id],
            "language": "auto",
            "output_format": "timeline",
            "include_timestamps": True,
            "speaker_labeling": True,
            "config": {}
        }
        
        response = client.post(
            "/api/v1/speech/meeting-transcription",
            json=request_data,
            headers={"Authorization": f"Bearer {TEST_TOKEN}"}
        )
        
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        assert "task_id" in data
        
        self.created_tasks.append(data["task_id"])
        self.meeting_task_id = data["task_id"]
        
        print(f"✅ 会议转录任务创建成功: {data['task_id']}")
    
    def test_audio_preprocessing(self):
        """测试音频预处理"""
        print("\n--- 测试音频预处理 ---")
        
        if not hasattr(self, 'test_file_id'):
            pytest.skip("需要先上传文件")
        
        request_data = {
            "file_ids": [self.test_file_id],
            "target_sr": 16000,
            "target_channels": 1,
            "normalize": True,
            "denoise": True,
            "config": {
                "target_db": -20.0
            }
        }
        
        response = client.post(
            "/api/v1/speech/preprocessing",
            json=request_data,
            headers={"Authorization": f"Bearer {TEST_TOKEN}"}
        )
        
        assert response.status_code == 200
        
        data = response.json()
        assert data["success"] is True
        assert "task_id" in data
        
        self.created_tasks.append(data["task_id"])
        self.preprocess_task_id = data["task_id"]
        
        print(f"✅ 音频预处理任务创建成功: {data['task_id']}")
    
    def test_task_status(self):
        """测试任务状态查询"""
        print("\n--- 测试任务状态查询 ---")
        
        if not hasattr(self, 'vad_task_id'):
            pytest.skip("需要先创建任务")
        
        response = client.get(
            f"/api/v1/speech/task/{self.vad_task_id}",
            headers={"Authorization": f"Bearer {TEST_TOKEN}"}
        )
        
        assert response.status_code == 200
        
        data = response.json()
        assert "task_id" in data
        assert "status" in data
        
        print(f"✅ 任务状态查询成功: {data['status']}")
    
    def test_task_cancellation(self):
        """测试任务取消"""
        print("\n--- 测试任务取消 ---")
        
        if not hasattr(self, 'speech_task_id'):
            pytest.skip("需要先创建任务")
        
        response = client.delete(
            f"/api/v1/speech/task/{self.speech_task_id}",
            headers={"Authorization": f"Bearer {TEST_TOKEN}"}
        )
        
        # 任务可能已经完成或不存在，所以接受404状态码
        assert response.status_code in [200, 404]
        
        print("✅ 任务取消测试完成")


def test_audio_utils():
    """测试音频工具函数"""
    print("\n=== 测试音频工具函数 ===")
    
    try:
        from backend.utils.audio.enhanced_audio_processor import EnhancedAudioProcessor, check_audio_quality
        
        # 创建测试音频文件
        test_audio = TestAudioAPI.create_test_audio_file()
        
        # 测试音频质量检查
        quality_result = check_audio_quality(test_audio)
        assert quality_result["is_valid"] is True
        print("[OK] 音频质量检查通过")
        
        # 测试音频处理器
        processor = EnhancedAudioProcessor()
        audio_info = processor.get_audio_info(test_audio)
        assert audio_info.is_valid is True
        print("[OK] 音频信息获取成功")
        
        # 清理测试文件
        os.unlink(test_audio)
        
        return True
        
    except Exception as e:
        print(f"[FAIL] 音频工具测试失败: {e}")
        return False


def run_integration_test():
    """运行集成测试"""
    print("\n=== 运行音频处理集成测试 ===")
    
    try:
        # 检查必要的服务
        task_manager = get_task_manager()
        task_manager.redis_client.ping()
        print("✅ Redis连接正常")
        
        # 运行API测试
        test_instance = TestAudioAPI()
        test_instance.setup_class()
        
        # 执行测试序列
        tests = [
            ("健康检查", test_instance.test_health_check),
            ("音频上传", test_instance.test_audio_file_upload),
            ("文件列表", test_instance.test_audio_file_list),
            ("文件信息", test_instance.test_audio_file_info),
            ("批量上传", test_instance.test_batch_upload),
            ("VAD检测", test_instance.test_vad_detection),
            ("语音识别", test_instance.test_speech_recognition),
            ("说话人识别", test_instance.test_speaker_recognition),
            ("会议转录", test_instance.test_meeting_transcription),
            ("音频预处理", test_instance.test_audio_preprocessing),
            ("任务状态", test_instance.test_task_status),
            ("任务取消", test_instance.test_task_cancellation),
        ]
        
        results = []
        for test_name, test_func in tests:
            try:
                test_func()
                results.append((test_name, True))
                print(f"✅ {test_name} 测试通过")
            except Exception as e:
                results.append((test_name, False))
                print(f"❌ {test_name} 测试失败: {e}")
        
        # 清理
        test_instance.teardown_class()
        
        # 输出结果
        passed = sum(1 for _, result in results if result)
        total = len(results)
        
        print(f"\n集成测试结果: {passed}/{total} 通过")
        
        return passed == total
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        return False


if __name__ == "__main__":
    print("开始音频处理API测试...\n")
    
    # 运行工具测试
    utils_ok = test_audio_utils()
    
    # 运行集成测试
    integration_ok = run_integration_test()
    
    # 输出总结
    print("\n" + "="*50)
    print("测试总结:")
    print("="*50)
    print(f"音频工具测试: {'✅ 通过' if utils_ok else '❌ 失败'}")
    print(f"API集成测试: {'✅ 通过' if integration_ok else '❌ 失败'}")
    
    if utils_ok and integration_ok:
        print("\n🎉 所有测试通过！音频处理系统工作正常")
        sys.exit(0)
    else:
        print("\n⚠️  部分测试失败，请检查系统配置")
        sys.exit(1)
