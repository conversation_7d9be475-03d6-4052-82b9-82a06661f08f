#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 Windows系统FunASR模型路径修复工具
专门解决Windows系统中文件夹名称不能包含'/'字符的问题
"""

import os
import shutil
import platform
from pathlib import Path

def is_windows():
    """检查是否为Windows系统"""
    return platform.system() == "Windows"

def convert_slash_to_underscore(path_name):
    """将路径中的斜杠转换为下划线"""
    return path_name.replace('/', '_').replace('\\', '_')

def get_windows_safe_model_names():
    """返回Windows安全的模型名称映射"""
    return {
        # Paraformer模型
        "damo/speech_paraformer-large-vad-punc_asr_nat-zh-cn-16k-common-vocab8404-pytorch": 
            "damo_speech_paraformer-large-vad-punc_asr_nat-zh-cn-16k-common-vocab8404-pytorch",
        
        "damo/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-pytorch":
            "damo_speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-pytorch",
        
        "iic/speech_seaco_paraformer_large_asr_nat-zh-cn-16k-common-vocab8404-pytorch":
            "iic_speech_seaco_paraformer_large_asr_nat-zh-cn-16k-common-vocab8404-pytorch",
        
        # SenseVoice模型
        "iic/SenseVoiceSmall": "iic_SenseVoiceSmall",
        "FunAudioLLM/SenseVoiceSmall": "FunAudioLLM_SenseVoiceSmall",
        
        # CAM++模型
        "damo/speech_campplus_sv_zh-cn_16k-common": "damo_speech_campplus_sv_zh-cn_16k-common",
        "damo/speech_campplus_sv_zh": "damo_speech_campplus_sv_zh",
        
        # VAD模型
        "damo/speech_fsmn_vad_zh-cn-16k-common-pytorch": "damo_speech_fsmn_vad_zh-cn-16k-common-pytorch",
        "damo/speech_fsmn_vad_zh-cn-16k-common": "damo_speech_fsmn_vad_zh-cn-16k-common",
        
        # XVector模型
        "damo/speech_xvector_sv-zh-cn_16k-common": "damo_speech_xvector_sv-zh-cn_16k-common"
    }

def find_models_with_slashes(base_directory):
    """在指定目录中查找包含斜杠的模型文件夹"""
    if not os.path.exists(base_directory):
        return []
    
    problematic_paths = []
    
    try:
        for root, dirs, files in os.walk(base_directory):
            for dir_name in dirs:
                if '/' in dir_name:
                    full_path = os.path.join(root, dir_name)
                    problematic_paths.append(full_path)
    except Exception as e:
        print(f"搜索目录时出错: {str(e)}")
    
    return problematic_paths

def suggest_windows_compatible_names(model_directories):
    """为包含斜杠的模型目录建议Windows兼容的名称"""
    suggestions = []
    name_mapping = get_windows_safe_model_names()
    
    for model_dir in model_directories:
        dir_name = os.path.basename(model_dir)
        parent_dir = os.path.dirname(model_dir)
        
        # 检查是否有预定义的映射
        if dir_name in name_mapping:
            suggested_name = name_mapping[dir_name]
        else:
            # 使用通用转换规则
            suggested_name = convert_slash_to_underscore(dir_name)
        
        suggested_path = os.path.join(parent_dir, suggested_name)
        
        suggestions.append({
            "original_path": model_dir,
            "original_name": dir_name,
            "suggested_name": suggested_name,
            "suggested_path": suggested_path
        })
    
    return suggestions

def rename_model_directory(original_path, new_path, dry_run=True):
    """重命名模型目录"""
    try:
        if dry_run:
            print(f"[预览] 将重命名: {original_path} -> {new_path}")
            return True
        else:
            if os.path.exists(new_path):
                print(f"目标路径已存在，跳过: {new_path}")
                return False
            
            shutil.move(original_path, new_path)
            print(f"✅ 重命名成功: {os.path.basename(original_path)} -> {os.path.basename(new_path)}")
            return True
    except Exception as e:
        print(f"❌ 重命名失败: {str(e)}")
        return False

def main():
    if not is_windows():
        print("此工具专门为Windows系统设计，当前系统无需使用此工具")
        return
    
    print("🔧 Windows系统FunASR模型路径修复工具")
    print("=" * 50)
    
    # 常见的模型存储位置
    common_model_dirs = [
        os.path.expanduser("~/.cache/modelscope/hub"),
        os.path.expanduser("~/.cache/huggingface/hub"),
        "./models",
        "./cache",
        "."
    ]
    
    # 让用户选择要扫描的目录
    print("请选择要扫描的目录:")
    print("1. 自动扫描常见位置")
    print("2. 手动指定目录")
    print("3. 扫描当前目录")
    
    choice = input("请输入选择 (1-3): ").strip()
    
    scan_dirs = []
    if choice == "1":
        scan_dirs = [d for d in common_model_dirs if os.path.exists(d)]
        print(f"将扫描 {len(scan_dirs)} 个常见目录")
    elif choice == "2":
        custom_dir = input("请输入要扫描的目录路径: ").strip()
        if os.path.exists(custom_dir):
            scan_dirs = [custom_dir]
        else:
            print("指定的目录不存在")
            return
    elif choice == "3":
        scan_dirs = ["."]
    else:
        print("无效选择")
        return
    
    # 查找包含斜杠的模型目录
    all_problematic_paths = []
    for scan_dir in scan_dirs:
        print(f"正在扫描: {scan_dir}")
        problematic_paths = find_models_with_slashes(scan_dir)
        all_problematic_paths.extend(problematic_paths)
    
    if not all_problematic_paths:
        print("✅ 未发现包含斜杠的模型目录，您的系统已经兼容Windows")
        return
    
    print(f"\n🔍 发现 {len(all_problematic_paths)} 个包含斜杠的模型目录:")
    for i, path in enumerate(all_problematic_paths, 1):
        print(f"  {i}. {path}")
    
    # 生成重命名建议
    suggestions = suggest_windows_compatible_names(all_problematic_paths)
    
    print(f"\n💡 重命名建议:")
    for i, suggestion in enumerate(suggestions, 1):
        print(f"  {i}. {suggestion['original_name']}")
        print(f"     -> {suggestion['suggested_name']}")
    
    # 询问是否执行重命名
    print(f"\n选择操作:")
    print("1. 预览重命名操作（不实际执行）")
    print("2. 执行重命名")
    print("3. 退出")
    
    action = input("请输入选择 (1-3): ").strip()
    
    if action == "1":
        print(f"\n📋 重命名预览:")
        for suggestion in suggestions:
            rename_model_directory(suggestion['original_path'], suggestion['suggested_path'], dry_run=True)
    elif action == "2":
        print(f"\n🔧 执行重命名:")
        success_count = 0
        for suggestion in suggestions:
            if rename_model_directory(suggestion['original_path'], suggestion['suggested_path'], dry_run=False):
                success_count += 1
        
        print(f"\n📊 重命名完成: {success_count}/{len(suggestions)} 成功")
        
        if success_count > 0:
            print(f"\n💡 接下来的步骤:")
            print("1. 更新您代码中的模型路径配置")
            print("2. 使用新的Windows兼容路径名称")
            print("3. 重新运行您的语音识别程序")
    else:
        print("操作已取消")

if __name__ == "__main__":
    main() 