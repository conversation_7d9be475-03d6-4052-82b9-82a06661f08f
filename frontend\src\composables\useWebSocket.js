/**
 * WebSocket连接组合式函数
 * 专门用于音频处理任务的实时进度推送
 */

import { ref, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import { useAuthStore } from '@/stores/auth'

export function useWebSocket() {
  const authStore = useAuthStore()
  
  // 响应式状态
  const connected = ref(false)
  const connecting = ref(false)
  const reconnectAttempts = ref(0)
  const lastError = ref(null)
  
  // WebSocket实例
  let ws = null
  let reconnectTimer = null
  let heartbeatTimer = null
  let heartbeatTimeout = null
  
  // 配置 - 🔧 优化大文件处理的连接管理
  const config = {
    maxReconnectAttempts: 10,    // 🔧 进一步增加重连次数
    reconnectInterval: 3000,     // 🔧 缩短重连间隔（更快恢复）
    heartbeatInterval: 90000,    // 🔧 进一步延长心跳间隔到90秒
    heartbeatTimeout: 45000,     // 🔧 延长心跳超时到45秒
    connectionTimeout: 15000,    // 🔧 延长连接超时时间
    maxIdleTime: 600000,         // 🔧 延长最大空闲时间到10分钟
    maxBackoffDelay: 60000       // 🔧 最大退避延迟60秒
  }
  
  // 事件监听器
  const listeners = new Map()
  
  /**
   * 连接WebSocket
   */
  const connect = async () => {
    if (connecting.value || connected.value) {
      return Promise.resolve()
    }
    
    return new Promise((resolve, reject) => {
      try {
        connecting.value = true
        lastError.value = null
        
        // 获取认证token
        const token = authStore.token
        if (!token) {
          throw new Error('没有有效的认证token')
        }
        
        // 构建WebSocket URL
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
        const host = import.meta.env.VITE_WS_HOST || window.location.hostname
        const port = import.meta.env.VITE_WS_PORT || '8002'
        const wsUrl = `${protocol}//${host}:${port}/ws/progress?token=${token}`
        
        console.log('🔌 连接WebSocket:', wsUrl)
        
        ws = new WebSocket(wsUrl)
        
        ws.onopen = () => {
          console.log('✅ WebSocket连接成功')
          connecting.value = false
          connected.value = true
          reconnectAttempts.value = 0
          
          // 启动心跳
          startHeartbeat()
          
          // 触发连接成功事件
          emit('connected')
          
          resolve()
        }
        
        ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data)
            handleMessage(data)
          } catch (error) {
            console.error('❌ WebSocket消息解析失败:', error)
          }
        }
        
        ws.onclose = (event) => {
          console.log('🔌 WebSocket连接关闭:', event.code, event.reason)
          connecting.value = false
          connected.value = false
          
          stopHeartbeat()
          
          // 触发断开连接事件
          emit('disconnected', { code: event.code, reason: event.reason })
          
          // 如果不是主动关闭，尝试重连
          if (event.code !== 1000 && reconnectAttempts.value < config.maxReconnectAttempts) {
            scheduleReconnect()
          }
        }
        
        ws.onerror = (error) => {
          console.error('❌ WebSocket连接错误:', error)
          connecting.value = false
          connected.value = false
          lastError.value = error
          
          // 触发错误事件
          emit('error', error)
          
          reject(error)
        }
        
      } catch (error) {
        connecting.value = false
        lastError.value = error
        reject(error)
      }
    })
  }
  
  /**
   * 断开WebSocket连接
   */
  const disconnect = () => {
    connected.value = false
    connecting.value = false
    
    // 清理定时器
    if (reconnectTimer) {
      clearTimeout(reconnectTimer)
      reconnectTimer = null
    }
    
    stopHeartbeat()
    
    // 关闭WebSocket
    if (ws) {
      ws.close(1000, 'Client disconnect')
      ws = null
    }
    
    console.log('🔌 WebSocket已断开')
  }
  
  /**
   * 发送消息
   */
  const send = (message) => {
    if (ws && connected.value) {
      try {
        ws.send(JSON.stringify(message))
        return true
      } catch (error) {
        console.error('❌ 发送WebSocket消息失败:', error)
        return false
      }
    }
    return false
  }
  
  /**
   * 订阅任务进度
   */
  const subscribeTask = (taskId) => {
    return send({
      type: 'subscribe',
      task_id: taskId
    })
  }
  
  /**
   * 取消订阅任务进度
   */
  const unsubscribeTask = (taskId) => {
    return send({
      type: 'unsubscribe',
      task_id: taskId
    })
  }
  
  /**
   * 添加事件监听器
   */
  const onMessage = (callback) => {
    const id = Date.now() + Math.random()
    listeners.set(id, callback)
    
    // 返回移除监听器的函数
    return () => {
      listeners.delete(id)
    }
  }
  
  /**
   * 处理接收到的消息
   */
  const handleMessage = (data) => {
    console.log('📨 收到WebSocket消息:', data)
    
    // 调用所有监听器
    listeners.forEach(callback => {
      try {
        callback(data)
      } catch (error) {
        console.error('❌ WebSocket消息处理器错误:', error)
      }
    })
    
    // 处理特定类型的消息 - 统一消息格式处理
    switch (data.type) {
      case 'heartbeat':
        // 心跳响应
        if (heartbeatTimeout) {
          clearTimeout(heartbeatTimeout)
          heartbeatTimeout = null
        }
        break

      case 'progress':
      case 'progress_update':
        // 🔧 修复：统一处理新的消息格式 {type, task_id, data}
        const progressData = {
          task_id: data.task_id,
          ...data.data
        }
        emit('progress', progressData)
        break

      case 'task_completed':
        // 🔧 修复：统一处理新的消息格式 {type, task_id, data}
        const completedData = {
          task_id: data.task_id,
          ...data.data
        }
        emit('task_completed', completedData)
        ElMessage.success('任务处理完成')
        break

      case 'task_failed':
        // 🔧 修复：统一处理新的消息格式 {type, task_id, data}
        const failedData = {
          task_id: data.task_id,
          ...data.data
        }
        emit('task_failed', failedData)
        ElMessage.error(`任务处理失败: ${failedData.error || '未知错误'}`)
        break

      case 'error':
        // 错误消息
        emit('error', data)
        ElMessage.error(`服务器错误: ${data.message || '未知错误'}`)
        break

      default:
        console.log('🔍 未处理的消息类型:', data.type)
    }
  }
  
  /**
   * 触发事件
   */
  const emit = (eventType, data = null) => {
    // 这里可以添加更多的事件处理逻辑
    console.log(`📡 WebSocket事件: ${eventType}`, data)
  }
  
  /**
   * 安排重连
   */
  const scheduleReconnect = () => {
    if (reconnectTimer) return
    
    reconnectAttempts.value++
    const delay = config.reconnectInterval * Math.pow(2, reconnectAttempts.value - 1)
    
    console.log(`🔄 ${delay}ms后尝试第${reconnectAttempts.value}次重连...`)
    
    reconnectTimer = setTimeout(() => {
      reconnectTimer = null
      if (!connected.value) {
        connect().catch(error => {
          console.error('❌ WebSocket重连失败:', error)
        })
      }
    }, delay)
  }
  
  /**
   * 启动心跳
   */
  const startHeartbeat = () => {
    stopHeartbeat()
    
    heartbeatTimer = setInterval(() => {
      if (connected.value) {
        // 发送心跳
        const success = send({ type: 'ping' })
        
        if (success) {
          // 设置心跳超时
          heartbeatTimeout = setTimeout(() => {
            console.warn('⚠️ 心跳超时，重连WebSocket')
            // 🔧 修复：心跳超时时重连而不是直接断开
            if (connected.value) {
              console.warn('⚠️ 心跳超时，断开连接')
              ws.close(1006, 'Heartbeat timeout')
            }
          }, config.heartbeatTimeout)
        }
      }
    }, config.heartbeatInterval)
  }
  
  /**
   * 停止心跳
   */
  const stopHeartbeat = () => {
    if (heartbeatTimer) {
      clearInterval(heartbeatTimer)
      heartbeatTimer = null
    }
    
    if (heartbeatTimeout) {
      clearTimeout(heartbeatTimeout)
      heartbeatTimeout = null
    }
  }
  
  /**
   * 获取连接状态
   */
  const getStatus = () => {
    return {
      connected: connected.value,
      connecting: connecting.value,
      reconnectAttempts: reconnectAttempts.value,
      lastError: lastError.value
    }
  }
  
  // 组件卸载时清理
  onUnmounted(() => {
    disconnect()
  })
  
  return {
    // 状态
    connected,
    connecting,
    reconnectAttempts,
    lastError,
    
    // 方法
    connect,
    disconnect,
    send,
    subscribeTask,
    unsubscribeTask,
    onMessage,
    getStatus
  }
}
