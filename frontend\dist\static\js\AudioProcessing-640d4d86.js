import{_ as ye,h as S,m as q,v as $e,r as F,a,c as r,d as t,w as c,b as e,A as z,B as Qe,t as i,s as fe,f as B,R as Se,F as pe,p as ge,W as We,x as b,X as Ne,z as ne,Y as Ge,Z as Je,$ as Ke,a0 as Ee,a1 as Ue,a2 as Me,a3 as Te,E as R,O as Ce,a4 as Xe,o as Ae,n as De,D as Ve,G as ze,V as we,N as Re,Q as Le,q as xe,a5 as Pe,a6 as Fe,H as Ye,a7 as Ze}from"./index-2c134546.js";import{a as Ie}from"./audioProcessing-868a9059.js";import{useWebSocketStore as Oe}from"./websocket-aa959a18.js";import{u as es,a as ss}from"./useAudioProcessing-53cfd45c.js";import"./auth-e6295339.js";const ts={class:"audio-uploader"},os={class:"upload-content"},ls={class:"upload-icon"},ns={class:"upload-text"},as={class:"upload-hint"},is={class:"upload-formats"},rs={class:"format-list"},us={class:"upload-size"},ds={class:"size-label"},cs={key:0,class:"file-list"},ps={class:"file-list-header"},vs={class:"file-items"},ms={class:"file-info"},gs={class:"file-icon"},_s={class:"file-details"},fs={class:"file-name"},hs={class:"file-meta"},ks={class:"file-size"},ys={class:"file-type"},bs={key:0,class:"file-duration"},$s={key:1,class:"file-progress"},ws={class:"progress-text"},Cs={key:2,class:"file-error"},Ss={class:"error-text"},Vs={class:"file-actions"},zs={class:"file-status"},Us={key:1,class:"upload-progress"},Ms={class:"progress-header"},xs={class:"progress-details"},Ts={key:0},As={__name:"AudioUploader",props:{mode:{type:String,default:"single",validator:U=>["single","batch"].includes(U)},accept:{type:Array,default:()=>[".wav",".mp3",".m4a",".aac",".flac",".ogg"]},maxSize:{type:Number,default:200*1024*1024},chunkSize:{type:Number,default:5*1024*1024},enableChunkedUpload:{type:Boolean,default:!0},enableResume:{type:Boolean,default:!0},autoUpload:{type:Boolean,default:!1}},emits:["files-selected","upload-progress","file-preview","file-remove","upload-start","upload-success","upload-error","upload-pause","upload-resume","upload-cancel"],setup(U,{expose:A,emit:f}){const v=U,m=f,L=S(),V=S([]),J=S(!1),O=S(!1),K=S(0),Q=S(""),W=S(""),X=S(new Map),$=S(new Set),I=S(new Map),j=S({startTime:0,uploadedBytes:0,totalBytes:0,lastUpdateTime:0,lastUploadedBytes:0}),M=q(()=>v.mode==="single"?"拖拽音频文件到此处或点击选择":"拖拽多个音频文件到此处或点击选择"),ee=q(()=>v.mode==="single"?"支持单个音频文件上传":"支持批量音频文件上传，最多10个文件"),P=o=>{if(o===0)return"0 B";const p=1024,s=["B","KB","MB","GB"],n=Math.floor(Math.log(o)/Math.log(p));return parseFloat((o/Math.pow(p,n)).toFixed(2))+" "+s[n]},ae=o=>o.split(".").pop().toUpperCase(),ve=o=>{const p=Math.floor(o/60),s=Math.floor(o%60);return`${p}:${s.toString().padStart(2,"0")}`},ce=o=>{const p=[],s=[];return["audio/wav","audio/wave","audio/x-wav","audio/mpeg","audio/mp3","audio/mp4","audio/m4a","audio/aac","audio/flac","audio/ogg","audio/vorbis"].includes(o.type)||v.accept.some(y=>o.name.toLowerCase().endsWith(y.toLowerCase()))||p.push(`不支持的文件格式: ${ae(o.name)}`),o.size>v.maxSize&&p.push(`文件大小超过限制: ${P(v.maxSize)}`),o.name.length>255&&p.push("文件名过长，请重命名后重试"),/[<>:"/\\|?*]/.test(o.name)&&s.push("文件名包含特殊字符，可能影响处理"),o.size>100*1024*1024&&s.push("文件较大，上传可能需要较长时间"),{isValid:p.length===0,errors:p,warnings:s}},ie=async(o,p)=>{const s=w(),n=Math.ceil(o.size/v.chunkSize);let k=0;try{const y=v.enableResume?l(o):null,D=y?y.uploadedChunks:0;for(let te=D;te<n;te++){if($.value.has(s))throw C(o,te),new Error("Upload paused");const Be=te*v.chunkSize,je=Math.min(Be+v.chunkSize,o.size),He=o.slice(Be,je),be=new FormData;be.append("chunk",He),be.append("chunkIndex",te),be.append("totalChunks",n),be.append("fileId",s),be.append("fileName",o.name),be.append("fileSize",o.size),await re(be),k=te+1;const qe=Math.round(k/n*100);p&&p(qe)}const H=await d(s,o.name,n);return h(o),H}catch(y){throw y.message!=="Upload paused"&&I.value.set(s,{file:o,uploadedChunks:k,totalChunks:n,error:y.message}),y}},re=async o=>{const p=await fetch("/api/v1/audio/upload/chunk",{method:"POST",body:o,headers:{Authorization:`Bearer ${N()}`}});if(!p.ok)throw new Error(`Chunk upload failed: ${p.statusText}`);return p.json()},d=async(o,p,s)=>{const n=await fetch("/api/v1/audio/upload/merge",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${N()}`},body:JSON.stringify({fileId:o,fileName:p,totalChunks:s})});if(!n.ok)throw new Error(`File merge failed: ${n.statusText}`);return n.json()},l=o=>{const p=`upload_resume_${o.name}_${o.size}`,s=localStorage.getItem(p);return s?JSON.parse(s):null},C=(o,p)=>{const s=`upload_resume_${o.name}_${o.size}`;localStorage.setItem(s,JSON.stringify({uploadedChunks:p,timestamp:Date.now()}))},h=o=>{const p=`upload_resume_${o.name}_${o.size}`;localStorage.removeItem(p)},w=()=>Date.now().toString(36)+Math.random().toString(36).substring(2),N=()=>localStorage.getItem("auth_token")||"",oe=o=>{const p=Date.now(),s=Math.round(o/100*j.value.totalBytes);if(p-j.value.lastUpdateTime>=1e3){const n=(p-j.value.lastUpdateTime)/1e3,k=s-j.value.lastUploadedBytes;if(n>0&&k>0){const y=k/n;W.value=le(y)}j.value.lastUpdateTime=p,j.value.lastUploadedBytes=s}j.value.uploadedBytes=s},le=o=>o<1024?`${Math.round(o)} B/s`:o<1024*1024?`${Math.round(o/1024)} KB/s`:`${(o/(1024*1024)).toFixed(1)} MB/s`,Y=(o,p)=>p.some(s=>{var n;return s.name===o.name&&s.size===o.size&&((n=s.file)==null?void 0:n.lastModified)===o.lastModified}),se=o=>Y(o,V.value)?(R.warning(`文件 "${o.name}" 已存在，跳过重复上传`),!1):(v.mode==="single"?V.value=[o]:V.value.push(o),!0),ue=o=>{const p=ce(o);return p.isValid?(p.warnings.length>0&&p.warnings.forEach(s=>{R.warning(s)}),!0):(p.errors.forEach(s=>{R.error(s)}),!1)},de=async(o,p)=>{var s;if(o.status==="ready"){const n={uid:o.uid,name:o.name,size:o.size,type:o.raw.type,file:o.raw,status:"ready",progress:0,fileId:null,lastModified:o.raw.lastModified};if(E(o.raw).then(y=>{n.duration=y}),!se(n))return;try{n.status="uploading",O.value=!0,Q.value=n.name,K.value=0,j.value={startTime:Date.now(),uploadedBytes:0,totalBytes:n.size,lastUpdateTime:Date.now(),lastUploadedBytes:0},m("files-selected",V.value);const y=await Ie.uploadAudio(o.raw,D=>{n.progress=D,K.value=D,oe(D),m("upload-progress",{file:n,progress:D})});n.status="success",n.result=y,n.fileId=y.file_id||((s=y.data)==null?void 0:s.file_id),O.value=!1,K.value=100,Q.value="",W.value="",m("upload-success",{file:n,result:y})}catch(y){n.status="error",n.error=y.message,O.value=!1,K.value=0,Q.value="",W.value="",m("upload-error",{file:n,error:y})}m("files-selected",V.value)}},G=o=>{const p=V.value.findIndex(s=>s.uid===o.uid);p>-1&&(V.value.splice(p,1),m("files-selected",V.value))},x=o=>{const p=v.mode==="single"?1:10;R.warning(`最多只能选择 ${p} 个文件`)},_=o=>{const p=V.value[o];V.value.splice(o,1),m("file-remove",p,o),m("files-selected",V.value)},he=(o,p)=>{m("file-preview",o,p)},ke=()=>{var o;V.value=[],(o=L.value)==null||o.clearFiles(),m("files-selected",[])},me=o=>{$.value.add(o),m("upload-pause",o),R.info("上传已暂停")},_e=async o=>{$.value.delete(o),m("upload-resume",o);const p=V.value.findIndex(s=>s.uid===o);if(p>-1){const s=V.value[p];try{await T(s,p)}catch(n){R.error(`恢复上传失败: ${n.message}`)}}},g=o=>{$.value.delete(o),X.value.delete(o);const p=V.value.findIndex(s=>s.uid===o);p>-1&&(V.value[p].status="ready",V.value[p].progress=0),m("upload-cancel",o),R.info("上传已取消")},u=async(o,p)=>{I.value.delete(o.uid),o.status="ready",o.progress=0;try{await T(o,p)}catch(s){R.error(`重试上传失败: ${s.message}`)}},T=async(o,p)=>{var s;o.status="uploading",o.progress=0,O.value=!0,Q.value=o.name,K.value=0,j.value={startTime:Date.now(),uploadedBytes:0,totalBytes:o.size,lastUpdateTime:Date.now(),lastUploadedBytes:0},m("upload-start",o);try{let n;v.enableChunkedUpload&&o.size>v.chunkSize?n=await ie(o.file,k=>{o.progress=k,K.value=k,oe(k),m("upload-progress",{file:o,progress:k})}):n=await Ie.uploadAudio(o.file,k=>{o.progress=k,K.value=k,oe(k),m("upload-progress",{file:o,progress:k})}),o.status="success",o.result=n,o.fileId=n.file_id||((s=n.data)==null?void 0:s.file_id),O.value=!1,K.value=100,Q.value="",W.value="",m("upload-success",{file:o,result:n}),R.success(`${o.name} 上传成功`)}catch(n){o.status="error",o.error=n.message,O.value=!1,K.value=0,Q.value="",W.value="",m("upload-error",{file:o,error:n}),R.error(`${o.name} 上传失败: ${n.message}`)}};A({startBatchUpload:async()=>{const o=V.value.filter(p=>p.status==="ready");if(o.length===0){R.warning("没有可上传的文件");return}O.value=!0;try{const s=[];for(let n=0;n<o.length;n+=3)s.push(o.slice(n,n+3));for(const n of s)await Promise.all(n.map((k,y)=>T(k,y)));R.success("批量上传完成")}catch(p){R.error(`批量上传失败: ${p.message}`)}finally{O.value=!1}},clearFiles:ke,pauseUpload:me,resumeUpload:_e,cancelUpload:g,retryUpload:u});const E=o=>new Promise(p=>{const s=new Audio,n=URL.createObjectURL(o);s.addEventListener("loadedmetadata",()=>{p(s.duration),URL.revokeObjectURL(n)}),s.addEventListener("error",()=>{p(0),URL.revokeObjectURL(n)}),s.src=n});return $e(()=>V.value.length,o=>{var p;o===0&&((p=L.value)==null||p.clearFiles())}),(o,p)=>{const s=F("el-icon"),n=F("el-upload"),k=F("el-button"),y=F("el-progress");return a(),r("div",ts,[t(n,{ref_key:"uploadRef",ref:L,class:fe(["upload-area",{"is-dragover":J.value}]),drag:"",multiple:U.mode==="batch",accept:U.accept.join(","),"before-upload":ue,"on-change":de,"on-remove":G,"on-exceed":x,limit:U.mode==="single"?1:10,"auto-upload":!1,"show-file-list":!1,onDragover:p[0]||(p[0]=D=>J.value=!0),onDragleave:p[1]||(p[1]=D=>J.value=!1),onDrop:p[2]||(p[2]=D=>J.value=!1)},{default:c(()=>[e("div",os,[e("div",ls,[t(s,{size:"48",color:"var(--accent-primary)"},{default:c(()=>[t(z(Qe))]),_:1})]),e("div",ns,[e("h3",null,i(M.value),1),e("p",as,i(ee.value),1)]),e("div",is,[p[3]||(p[3]=e("span",{class:"format-label"},"支持格式：",-1)),e("span",rs,i(U.accept.join(", ")),1)]),e("div",us,[e("span",ds,"最大文件大小："+i(P(U.maxSize)),1)])])]),_:1},8,["class","multiple","accept","limit"]),V.value.length>0?(a(),r("div",cs,[e("div",ps,[e("h4",null,"已选择文件 ("+i(V.value.length)+")",1),t(k,{text:"",onClick:ke,icon:z(Se)},{default:c(()=>p[4]||(p[4]=[B("清空")])),_:1,__:[4]},8,["icon"])]),e("div",vs,[(a(!0),r(pe,null,ge(V.value,(D,H)=>(a(),r("div",{key:D.uid,class:fe(["file-item",{"has-error":D.status==="error"}])},[e("div",ms,[e("div",gs,[t(s,null,{default:c(()=>[t(z(We))]),_:1})]),e("div",_s,[e("div",fs,i(D.name),1),e("div",hs,[e("span",ks,i(P(D.size)),1),e("span",ys,i(ae(D.name)),1),D.duration?(a(),r("span",bs,i(ve(D.duration)),1)):b("",!0),D.status==="uploading"?(a(),r("div",$s,[t(y,{percentage:D.progress||0,"stroke-width":4,"show-text":!1,color:"var(--accent-primary)"},null,8,["percentage"]),e("span",ws,i(D.progress||0)+"%",1)])):b("",!0),D.status==="error"&&D.error?(a(),r("div",Cs,[t(s,{color:"var(--danger-color)"},{default:c(()=>[t(z(Ne))]),_:1}),e("span",Ss,i(D.error),1)])):b("",!0)])])]),e("div",Vs,[D.status==="uploading"?(a(),r(pe,{key:0},[$.value.has(D.uid)?(a(),ne(k,{key:1,text:"",onClick:te=>_e(D.uid),icon:z(Je),title:"恢复上传"},null,8,["onClick","icon"])):(a(),ne(k,{key:0,text:"",onClick:te=>me(D.uid),icon:z(Ge),title:"暂停上传"},null,8,["onClick","icon"])),t(k,{text:"",onClick:te=>g(D.uid),icon:z(Se),title:"取消上传"},null,8,["onClick","icon"])],64)):b("",!0),D.status==="error"?(a(),ne(k,{key:1,text:"",onClick:te=>u(D,H),icon:z(Ke),title:"重试上传"},null,8,["onClick","icon"])):b("",!0),D.status!=="uploading"?(a(),r(pe,{key:2},[t(k,{text:"",onClick:te=>he(D,H),icon:z(Ee),title:"预览"},null,8,["onClick","icon"]),t(k,{text:"",onClick:te=>_(H),icon:z(Se),title:"删除"},null,8,["onClick","icon"])],64)):b("",!0)]),e("div",zs,[D.status==="success"?(a(),ne(s,{key:0,color:"var(--success-color)"},{default:c(()=>[t(z(Ue))]),_:1})):D.status==="error"?(a(),ne(s,{key:1,color:"var(--danger-color)"},{default:c(()=>[t(z(Me))]),_:1})):(a(),ne(s,{key:2,color:"var(--warning-color)"},{default:c(()=>[t(z(Te))]),_:1}))])],2))),128))])])):b("",!0),O.value?(a(),r("div",Us,[e("div",Ms,[p[5]||(p[5]=e("span",null,"上传进度",-1)),e("span",null,i(Math.round(K.value))+"%",1)]),t(y,{percentage:Math.round(K.value),"stroke-width":8,"show-text":!1,color:"var(--accent-primary)"},null,8,["percentage"]),e("div",xs,[e("span",null,i(Q.value),1),W.value?(a(),r("span",Ts,i(W.value),1)):b("",!0)])])):b("",!0)])}}},Ds=ye(As,[["__scopeId","data-v-7147a909"]]);const Bs={class:"audio-config-panel"},Rs={key:0,class:"config-group"},Ps={class:"config-items"},Fs={class:"config-item"},Is={class:"config-item"},Ws={class:"config-item"},Ns={class:"config-item"},Es={class:"config-hint"},Ls={key:1,class:"config-group"},Os={class:"config-items"},js={class:"config-item"},Hs={class:"config-item"},qs={class:"config-item"},Qs={class:"config-item"},Gs={class:"config-item"},Js={key:2,class:"config-group"},Ks={class:"config-items"},Xs={class:"config-item"},Ys={class:"config-item"},Zs={class:"config-item"},et={class:"config-item"},st={key:3,class:"config-group"},tt={class:"config-items"},ot={class:"config-item"},lt={class:"config-item"},nt={class:"config-item"},at={key:0,class:"config-item"},it={class:"config-item"},rt={key:1,class:"config-item"},ut={key:4,class:"config-group"},dt={class:"config-items"},ct={class:"config-item"},pt={class:"config-item"},vt={class:"config-item"},mt={class:"config-item"},gt={key:0,class:"config-subgroup"},_t={class:"config-item"},ft={class:"config-item"},ht={class:"config-item"},kt={class:"threshold-value"},yt={class:"config-item"},bt={class:"config-item"},$t={key:0,class:"config-recommendation"},wt={class:"recommendation-details"},Ct={class:"config-actions"},St={__name:"AudioConfigPanel",props:{processingMode:{type:String,default:"speech_recognition"},audioConfig:{type:Object,required:!0},recordingConfig:{type:Object,required:!0},recordingStatus:{type:String,default:"idle"},recordingDuration:{type:Number,default:0},recordedAudioUrl:{type:String,default:""}},emits:["config-change","processing-mode-change","recording-start","recording-pause","recording-resume","recording-stop","recording-save","recording-discard","apply-optimal-config"],setup(U,{emit:A}){const f=U,v=A,m=(d,l)=>{v("config-change",{key:d,value:l})},L=[{value:"auto",label:"自动检测"},{value:"zh",label:"中文"},{value:"en",label:"英文"},{value:"yue",label:"粤语"},{value:"ja",label:"日语"},{value:"ko",label:"韩语"}],V=S("custom"),J=S(!1),O=S({}),K={two_person:{expected_speakers:2,similarity_threshold:.15,clustering_method:"agglomerative"},small_group:{expected_speakers:4,similarity_threshold:.2,clustering_method:"agglomerative"},large_meeting:{expected_speakers:8,similarity_threshold:.25,clustering_method:"auto"},custom:{expected_speakers:2,similarity_threshold:.15,clustering_method:"auto"}},Q=q(()=>["vad_detection","meeting_transcription","comprehensive_analysis"].includes(f.processingMode)),W=q(()=>["speech_recognition","meeting_transcription","comprehensive_analysis"].includes(f.processingMode)),X=q(()=>["speaker_recognition","meeting_transcription","comprehensive_analysis"].includes(f.processingMode)),$=q(()=>["audio_preprocessing","comprehensive_analysis"].includes(f.processingMode)),I=q(()=>f.processingMode==="meeting_transcription"),j=d=>{K[d]&&(Object.assign(meetingConfig.value,K[d]),R.success(`已应用${d==="two_person"?"两人对话":d==="small_group"?"小组讨论":d==="large_meeting"?"大型会议":"自定义"}场景配置`))},M=d=>d<=.15?`${d} (容易区分)`:d<=.25?`${d} (适中)`:d<=.4?`${d} (较难区分)`:`${d} (很难区分)`,ee=()=>{const d=[];return(meetingConfig.value.expected_speakers<1||meetingConfig.value.expected_speakers>10)&&d.push("说话人数量应在1-10之间"),(meetingConfig.value.similarity_threshold<.1||meetingConfig.value.similarity_threshold>.8)&&d.push("聚类阈值应在0.1-0.8之间"),meetingConfig.value.expected_speakers>5&&meetingConfig.value.similarity_threshold<.2&&d.push("大型会议(>5人)建议使用较高的聚类阈值(≥0.2)"),meetingConfig.value.expected_speakers<=2&&meetingConfig.value.similarity_threshold>.3&&d.push("小型对话(≤2人)建议使用较低的聚类阈值(≤0.3)"),d},P=()=>{const d=meetingConfig.value.expected_speakers;return d<=2?{similarity_threshold:.15,clustering_method:"agglomerative",description:"两人对话推荐配置：低阈值，层次聚类"}:d<=5?{similarity_threshold:.2,clustering_method:"agglomerative",description:"小组讨论推荐配置：中等阈值，层次聚类"}:{similarity_threshold:.25,clustering_method:"auto",description:"大型会议推荐配置：高阈值，自动选择算法"}},ae=()=>{const d=P();meetingConfig.value.similarity_threshold=d.similarity_threshold,meetingConfig.value.clustering_method=d.clustering_method,O.value=d,J.value=!0,setTimeout(()=>{J.value=!1},3e3),R.success("已应用智能推荐配置")},ve=d=>({auto:"自动选择",agglomerative:"层次聚类",dbscan:"DBSCAN聚类"})[d]||d,ce=()=>{vadConfig.value={preset:"meeting_optimized",merge_length_s:15,min_speech_duration:.5,max_speech_duration:60,threshold:.5,optimize_two_person:!0},speechConfig.value={language:"auto",use_itn:!0,ban_emo_unk:!1,merge_vad:!0,merge_length_s:15},speakerConfig.value={clustering_method:"auto",expected_speakers:2,similarity_threshold:.7,enable_gender_detection:!1},preprocessConfig.value={target_sr:16e3,target_channels:1,normalize:!0,target_db:-20,denoise:!0,denoise_method:"spectral_gating"},meetingConfig.value={output_format:"timeline",include_timestamps:!0,include_confidence:!1,speaker_labeling:!0,expected_speakers:2,similarity_threshold:.15,clustering_method:"auto"},V.value="custom",R.success("配置已重置为默认值")},ie=()=>{R.success("配置预设已保存")},re=()=>{if(f.processingMode==="meeting_transcription"){const l=ee();l.length>0&&console.warn("配置验证警告:",l)}const d={vad:vadConfig.value,speech:speechConfig.value,speaker:speakerConfig.value,preprocess:preprocessConfig.value,meeting:meetingConfig.value};v("update:modelValue",d),v("config-changed",d)};return $e([vadConfig,speechConfig,speakerConfig,preprocessConfig,meetingConfig],re,{deep:!0}),re(),(d,l)=>{const C=F("el-input-number"),h=F("el-slider"),w=F("el-option"),N=F("el-select"),oe=F("el-checkbox"),le=F("el-switch"),Y=F("el-radio"),se=F("el-radio-group"),ue=F("el-icon"),de=F("el-tooltip"),G=F("el-button"),x=F("el-alert");return a(),r("div",Bs,[Q.value?(a(),r("div",Rs,[l[31]||(l[31]=e("div",{class:"group-header"},[e("h4",null,"🎯 VAD语音活动检测配置")],-1)),e("div",Ps,[e("div",Fs,[l[27]||(l[27]=e("label",null,"合并长度 (秒)",-1)),t(C,{"model-value":U.audioConfig.merge_length_s,"onUpdate:modelValue":l[0]||(l[0]=_=>m("merge_length_s",_)),min:1,max:60,step:1,size:"small"},null,8,["model-value"])]),e("div",Is,[l[28]||(l[28]=e("label",null,"最小语音时长 (秒)",-1)),t(C,{"model-value":U.audioConfig.min_speech_duration,"onUpdate:modelValue":l[1]||(l[1]=_=>m("min_speech_duration",_)),min:.1,max:10,step:.1,precision:1,size:"small"},null,8,["model-value"])]),e("div",Ws,[l[29]||(l[29]=e("label",null,"最大语音时长 (秒)",-1)),t(C,{"model-value":U.audioConfig.max_speech_duration,"onUpdate:modelValue":l[2]||(l[2]=_=>m("max_speech_duration",_)),min:10,max:300,step:5,size:"small"},null,8,["model-value"])]),e("div",Ns,[l[30]||(l[30]=e("label",null,"检测阈值",-1)),t(h,{"model-value":U.audioConfig.threshold,"onUpdate:modelValue":l[3]||(l[3]=_=>m("threshold",_)),min:.1,max:.9,step:.05,"format-tooltip":M},null,8,["model-value"]),e("div",Es,"阈值越高，检测越严格 (当前: "+i(U.audioConfig.threshold)+")",1)])])])):b("",!0),W.value?(a(),r("div",Ls,[l[37]||(l[37]=e("div",{class:"group-header"},[e("h4",null,"🗣️ 语音识别配置")],-1)),e("div",Os,[e("div",js,[l[32]||(l[32]=e("label",null,"识别语言",-1)),t(N,{modelValue:d.speechConfig.language,"onUpdate:modelValue":l[4]||(l[4]=_=>d.speechConfig.language=_)},{default:c(()=>[(a(),r(pe,null,ge(L,_=>t(w,{key:_.value,label:_.label,value:_.value},null,8,["label","value"])),64))]),_:1},8,["modelValue"])]),e("div",Hs,[t(oe,{modelValue:d.speechConfig.use_itn,"onUpdate:modelValue":l[5]||(l[5]=_=>d.speechConfig.use_itn=_)},{default:c(()=>l[33]||(l[33]=[B(" 使用逆文本正则化 ")])),_:1,__:[33]},8,["modelValue"])]),e("div",qs,[t(oe,{modelValue:d.speechConfig.ban_emo_unk,"onUpdate:modelValue":l[6]||(l[6]=_=>d.speechConfig.ban_emo_unk=_)},{default:c(()=>l[34]||(l[34]=[B(" 禁用未知情感标签 ")])),_:1,__:[34]},8,["modelValue"])]),e("div",Qs,[l[35]||(l[35]=e("label",null,"合并VAD结果",-1)),t(le,{modelValue:d.speechConfig.merge_vad,"onUpdate:modelValue":l[7]||(l[7]=_=>d.speechConfig.merge_vad=_)},null,8,["modelValue"])]),e("div",Gs,[l[36]||(l[36]=e("label",null,"VAD合并长度 (秒)",-1)),t(C,{modelValue:d.speechConfig.merge_length_s,"onUpdate:modelValue":l[8]||(l[8]=_=>d.speechConfig.merge_length_s=_),min:5,max:60,step:5,size:"small"},null,8,["modelValue"])])])])):b("",!0),X.value?(a(),r("div",Js,[l[42]||(l[42]=e("div",{class:"group-header"},[e("h4",null,"👥 说话人识别配置")],-1)),e("div",Ks,[e("div",Xs,[l[38]||(l[38]=e("label",null,"聚类方法",-1)),t(N,{modelValue:d.speakerConfig.clustering_method,"onUpdate:modelValue":l[9]||(l[9]=_=>d.speakerConfig.clustering_method=_)},{default:c(()=>[t(w,{label:"K-Means",value:"kmeans"}),t(w,{label:"层次聚类",value:"hierarchical"}),t(w,{label:"DBSCAN",value:"dbscan"}),t(w,{label:"自动检测",value:"auto"})]),_:1},8,["modelValue"])]),e("div",Ys,[l[39]||(l[39]=e("label",null,"预期说话人数",-1)),t(C,{modelValue:d.speakerConfig.expected_speakers,"onUpdate:modelValue":l[10]||(l[10]=_=>d.speakerConfig.expected_speakers=_),min:1,max:10,step:1,size:"small"},null,8,["modelValue"])]),e("div",Zs,[l[40]||(l[40]=e("label",null,"相似度阈值",-1)),t(h,{modelValue:d.speakerConfig.similarity_threshold,"onUpdate:modelValue":l[11]||(l[11]=_=>d.speakerConfig.similarity_threshold=_),min:.1,max:1,step:.05,"format-tooltip":_=>_.toFixed(2),"show-input":""},null,8,["modelValue","format-tooltip"])]),e("div",et,[t(oe,{modelValue:d.speakerConfig.enable_gender_detection,"onUpdate:modelValue":l[12]||(l[12]=_=>d.speakerConfig.enable_gender_detection=_)},{default:c(()=>l[41]||(l[41]=[B(" 启用性别检测 ")])),_:1,__:[41]},8,["modelValue"])])])])):b("",!0),$.value?(a(),r("div",st,[l[51]||(l[51]=e("div",{class:"group-header"},[e("h4",null,"🔧 音频预处理配置")],-1)),e("div",tt,[e("div",ot,[l[43]||(l[43]=e("label",null,"目标采样率 (Hz)",-1)),t(N,{modelValue:d.preprocessConfig.target_sr,"onUpdate:modelValue":l[13]||(l[13]=_=>d.preprocessConfig.target_sr=_)},{default:c(()=>[t(w,{label:"16000 Hz",value:16e3}),t(w,{label:"22050 Hz",value:22050}),t(w,{label:"44100 Hz",value:44100}),t(w,{label:"48000 Hz",value:48e3})]),_:1},8,["modelValue"])]),e("div",lt,[l[46]||(l[46]=e("label",null,"目标声道数",-1)),t(se,{modelValue:d.preprocessConfig.target_channels,"onUpdate:modelValue":l[14]||(l[14]=_=>d.preprocessConfig.target_channels=_)},{default:c(()=>[t(Y,{label:1},{default:c(()=>l[44]||(l[44]=[B("单声道")])),_:1,__:[44]}),t(Y,{label:2},{default:c(()=>l[45]||(l[45]=[B("立体声")])),_:1,__:[45]})]),_:1},8,["modelValue"])]),e("div",nt,[t(oe,{modelValue:d.preprocessConfig.normalize,"onUpdate:modelValue":l[15]||(l[15]=_=>d.preprocessConfig.normalize=_)},{default:c(()=>l[47]||(l[47]=[B(" 音量标准化 ")])),_:1,__:[47]},8,["modelValue"])]),d.preprocessConfig.normalize?(a(),r("div",at,[l[48]||(l[48]=e("label",null,"目标音量 (dB)",-1)),t(h,{modelValue:d.preprocessConfig.target_db,"onUpdate:modelValue":l[16]||(l[16]=_=>d.preprocessConfig.target_db=_),min:-40,max:0,step:1,"format-tooltip":_=>`${_} dB`,"show-input":""},null,8,["modelValue","format-tooltip"])])):b("",!0),e("div",it,[t(oe,{modelValue:d.preprocessConfig.denoise,"onUpdate:modelValue":l[17]||(l[17]=_=>d.preprocessConfig.denoise=_)},{default:c(()=>l[49]||(l[49]=[B(" 音频降噪 ")])),_:1,__:[49]},8,["modelValue"])]),d.preprocessConfig.denoise?(a(),r("div",rt,[l[50]||(l[50]=e("label",null,"降噪方法",-1)),t(N,{modelValue:d.preprocessConfig.denoise_method,"onUpdate:modelValue":l[18]||(l[18]=_=>d.preprocessConfig.denoise_method=_)},{default:c(()=>[t(w,{label:"频谱门控",value:"spectral_gating"}),t(w,{label:"维纳滤波",value:"wiener"}),t(w,{label:"简单滤波",value:"simple"})]),_:1},8,["modelValue"])])):b("",!0)])])):b("",!0),I.value?(a(),r("div",ut,[l[64]||(l[64]=e("div",{class:"group-header"},[e("h4",null,"🎤 会议转录配置")],-1)),e("div",dt,[e("div",ct,[l[52]||(l[52]=e("label",null,"输出格式",-1)),t(N,{modelValue:d.meetingConfig.output_format,"onUpdate:modelValue":l[19]||(l[19]=_=>d.meetingConfig.output_format=_)},{default:c(()=>[t(w,{label:"按时间排序",value:"timeline"}),t(w,{label:"按说话人分组",value:"speaker_grouped"}),t(w,{label:"对话格式",value:"dialogue"})]),_:1},8,["modelValue"])]),e("div",pt,[t(oe,{modelValue:d.meetingConfig.include_timestamps,"onUpdate:modelValue":l[20]||(l[20]=_=>d.meetingConfig.include_timestamps=_)},{default:c(()=>l[53]||(l[53]=[B(" 包含时间戳 ")])),_:1,__:[53]},8,["modelValue"])]),e("div",vt,[t(oe,{modelValue:d.meetingConfig.include_confidence,"onUpdate:modelValue":l[21]||(l[21]=_=>d.meetingConfig.include_confidence=_)},{default:c(()=>l[54]||(l[54]=[B(" 包含置信度分数 ")])),_:1,__:[54]},8,["modelValue"])]),e("div",mt,[t(oe,{modelValue:d.meetingConfig.speaker_labeling,"onUpdate:modelValue":l[22]||(l[22]=_=>d.meetingConfig.speaker_labeling=_)},{default:c(()=>l[55]||(l[55]=[B(" 说话人标注 ")])),_:1,__:[55]},8,["modelValue"])]),d.meetingConfig.speaker_labeling?(a(),r("div",gt,[l[63]||(l[63]=e("div",{class:"subgroup-header"},[e("h5",null,"👥 说话人识别设置")],-1)),e("div",_t,[l[56]||(l[56]=e("label",null,"预期说话人数量",-1)),t(C,{modelValue:d.meetingConfig.expected_speakers,"onUpdate:modelValue":l[23]||(l[23]=_=>d.meetingConfig.expected_speakers=_),min:1,max:10,step:1,size:"small",style:{width:"120px"}},null,8,["modelValue"]),t(de,{content:"设置会议中预期的说话人数量，有助于提高识别准确性",placement:"top"},{default:c(()=>[t(ue,{class:"help-icon"},{default:c(()=>[t(z(Ce))]),_:1})]),_:1})]),e("div",ft,[l[57]||(l[57]=e("label",null,"聚类方法",-1)),t(N,{modelValue:d.meetingConfig.clustering_method,"onUpdate:modelValue":l[24]||(l[24]=_=>d.meetingConfig.clustering_method=_),size:"small"},{default:c(()=>[t(w,{label:"自动选择",value:"auto"}),t(w,{label:"层次聚类",value:"agglomerative"}),t(w,{label:"DBSCAN",value:"dbscan"})]),_:1},8,["modelValue"]),t(de,{content:"选择说话人聚类算法，自动选择适合大多数场景",placement:"top"},{default:c(()=>[t(ue,{class:"help-icon"},{default:c(()=>[t(z(Ce))]),_:1})]),_:1})]),e("div",ht,[l[58]||(l[58]=e("label",null,"聚类阈值",-1)),t(h,{modelValue:d.meetingConfig.similarity_threshold,"onUpdate:modelValue":l[25]||(l[25]=_=>d.meetingConfig.similarity_threshold=_),min:.1,max:.8,step:.05,"format-tooltip":M,style:{width:"150px","margin-right":"10px"}},null,8,["modelValue"]),e("span",kt,i(d.meetingConfig.similarity_threshold),1),t(de,{content:"调整说话人相似度阈值，值越小越容易区分不同说话人",placement:"top"},{default:c(()=>[t(ue,{class:"help-icon"},{default:c(()=>[t(z(Ce))]),_:1})]),_:1})]),e("div",yt,[l[59]||(l[59]=e("label",null,"场景预设",-1)),t(N,{modelValue:V.value,"onUpdate:modelValue":l[26]||(l[26]=_=>V.value=_),onChange:j,size:"small",placeholder:"选择场景预设"},{default:c(()=>[t(w,{label:"两人对话",value:"two_person"}),t(w,{label:"小组讨论(3-5人)",value:"small_group"}),t(w,{label:"大型会议(6-10人)",value:"large_meeting"}),t(w,{label:"自定义",value:"custom"})]),_:1},8,["modelValue"]),t(de,{content:"根据会议场景快速应用推荐配置",placement:"top"},{default:c(()=>[t(ue,{class:"help-icon"},{default:c(()=>[t(z(Ce))]),_:1})]),_:1})]),e("div",bt,[t(G,{onClick:ae,size:"small",type:"primary",icon:z(Xe)},{default:c(()=>l[60]||(l[60]=[B(" 智能推荐配置 ")])),_:1,__:[60]},8,["icon"]),t(de,{content:"基于当前说话人数量自动推荐最佳配置",placement:"top"},{default:c(()=>[t(ue,{class:"help-icon"},{default:c(()=>[t(z(Ce))]),_:1})]),_:1})]),J.value?(a(),r("div",$t,[t(x,{title:O.value.description,type:"info",closable:!1,"show-icon":""},{default:c(()=>[e("div",wt,[e("p",null,[l[61]||(l[61]=e("strong",null,"推荐阈值:",-1)),B(" "+i(O.value.similarity_threshold),1)]),e("p",null,[l[62]||(l[62]=e("strong",null,"推荐算法:",-1)),B(" "+i(ve(O.value.clustering_method)),1)])])]),_:1},8,["title"])])):b("",!0)])):b("",!0)])])):b("",!0),e("div",Ct,[t(G,{onClick:ce,size:"small"},{default:c(()=>l[65]||(l[65]=[B(" 🔄 重置默认 ")])),_:1,__:[65]}),t(G,{onClick:ie,size:"small",type:"primary"},{default:c(()=>l[66]||(l[66]=[B(" 💾 保存预设 ")])),_:1,__:[66]})])])}}},Vt=ye(St,[["__scopeId","data-v-01394c7c"]]);const zt={class:"audio-preview"},Ut={class:"preview-header"},Mt={class:"file-meta"},xt={class:"file-size"},Tt={key:0,class:"file-duration"},At={class:"audio-player"},Dt=["src"],Bt={key:0,class:"waveform-section"},Rt={class:"section-title"},Pt={class:"waveform-container"},Ft={key:0,class:"waveform-loading"},It={key:1,class:"spectrum-section"},Wt={class:"section-title"},Nt={class:"spectrum-container"},Et={key:0,class:"spectrum-loading"},Lt={key:2,class:"quality-section"},Ot={class:"section-title"},jt={class:"quality-container"},Ht={key:0,class:"quality-loading"},qt={key:1,class:"quality-metrics"},Qt={class:"metric-item"},Gt={class:"metric-bar"},Jt={class:"metric-item"},Kt={class:"metric-bar"},Xt={class:"metric-item"},Yt={class:"metric-value"},Zt={class:"metric-item"},eo={class:"metric-value"},so={class:"metric-item"},to={key:3,class:"segment-section"},oo={class:"section-title"},lo={class:"segment-container"},no={class:"segment-controls"},ao={class:"segment-info"},io={class:"audio-info"},ro={class:"info-grid"},uo={class:"info-item"},co={class:"info-item"},po={class:"info-item"},vo={class:"info-item"},mo={class:"info-item"},go={class:"info-item"},_o={__name:"AudioPreview",props:{file:{type:Object,required:!0},showWaveform:{type:Boolean,default:!0},showSpectrum:{type:Boolean,default:!0},showQualityAnalysis:{type:Boolean,default:!0},showSegmentSelector:{type:Boolean,default:!0},enableAdvancedAnalysis:{type:Boolean,default:!0}},setup(U){const A=U,f=S(),v=S(),m=S(),L=S(""),V=S({}),J=S(!0),O=S(!0),K=S(!1),Q=S(!1),W=S(!0),X=S(!1),$=S({}),I=S(!0),j=S(0),M=S(0),ee=S(null),P=S(null),ae=S(0);S(null),S(null);const ve=q(()=>800),ce=q(()=>200),ie=g=>{if(g===0)return"0 B";const u=1024,T=["B","KB","MB","GB"],Z=Math.floor(Math.log(g)/Math.log(u));return parseFloat((g/Math.pow(u,Z)).toFixed(2))+" "+T[Z]},re=g=>{const u=Math.floor(g/60),T=Math.floor(g%60);return`${u}:${T.toString().padStart(2,"0")}`},d=g=>g.split(".").pop().toUpperCase(),l=()=>{var g;f.value&&(V.value={duration:f.value.duration,sampleRate:(g=ee.value)==null?void 0:g.sampleRate,channels:2,bitRate:128},A.showWaveform&&oe(),A.showSpectrum&&se(),A.showQualityAnalysis&&G(),j.value=0,M.value=f.value.duration||0)},C=()=>{f.value&&(ae.value=f.value.currentTime)},h=g=>{console.error("音频加载错误:",g),R.error("音频文件加载失败")},w=g=>{if(!f.value||!v.value)return;const u=v.value,T=u.getBoundingClientRect(),o=(g.clientX-T.left)/u.width*f.value.duration;f.value.currentTime=o},N=()=>{J.value=!J.value,J.value&&!P.value&&oe()},oe=async()=>{if(!(!L.value||K.value))try{K.value=!0,ee.value||(ee.value=new(window.AudioContext||window.webkitAudioContext));const u=await(await fetch(L.value)).arrayBuffer();P.value=await ee.value.decodeAudioData(u),le()}catch(g){console.error("生成波形图失败:",g),R.error("生成波形图失败")}finally{K.value=!1}},le=()=>{if(!v.value||!P.value)return;const g=v.value,u=g.getContext("2d");g.width=ve.value,g.height=ce.value,u.fillStyle="var(--surface-bg)",u.fillRect(0,0,g.width,g.height);const T=P.value.getChannelData(0),Z=T.length,E=Math.floor(Z/g.width);u.strokeStyle="var(--accent-primary)",u.lineWidth=1,u.beginPath();for(let o=0;o<g.width;o++){const p=o*E,s=p+E;let n=0,k=0;for(let H=p;H<s&&H<Z;H++){const te=T[H];te<n&&(n=te),te>k&&(k=te)}const y=(1+n)*g.height/2,D=(1+k)*g.height/2;o===0?u.moveTo(o,y):(u.lineTo(o,y),u.lineTo(o,D))}if(u.stroke(),f.value&&f.value.duration>0){const p=ae.value/f.value.duration*g.width;u.strokeStyle="var(--danger-color)",u.lineWidth=2,u.beginPath(),u.moveTo(p,0),u.lineTo(p,g.height),u.stroke()}},Y=()=>{O.value=!O.value,O.value&&!P.value&&se()},se=async()=>{if(!(!P.value||Q.value))try{Q.value=!0,ue()}catch(g){console.error("生成频谱图失败:",g),R.error("生成频谱图失败")}finally{Q.value=!1}},ue=()=>{if(!m.value||!P.value)return;const g=m.value,u=g.getContext("2d");g.width=ve.value,g.height=ce.value,u.fillStyle="var(--surface-bg)",u.fillRect(0,0,g.width,g.height);const T=u.createLinearGradient(0,0,0,g.height);T.addColorStop(0,"var(--accent-primary)"),T.addColorStop(1,"var(--accent-secondary)"),u.fillStyle=T;for(let Z=0;Z<g.width;Z+=4){const E=Math.random()*g.height*.8;u.fillRect(Z,g.height-E,2,E)}};Ae(()=>{A.file.file&&(L.value=URL.createObjectURL(A.file.file))}),De(()=>{L.value&&URL.revokeObjectURL(L.value),ee.value&&ee.value.close()});const de=()=>{W.value=!W.value,W.value&&Object.keys($.value).length===0&&G()},G=async()=>{if(!(!P.value||X.value))try{X.value=!0;const g=P.value.getChannelData(0),u=g.length;let T=0,Z=0;for(let k=0;k<u;k++){const y=Math.abs(g[k]);T+=y*y,y>Z&&(Z=y)}const E=Math.sqrt(T/u),o=Z>0?20*Math.log10(Z/(E+1e-10)):0,p=x(g),s=E>0?20*Math.log10(E/(p+1e-10)):0,n=_(E,Z,o,s);$.value={rms:E,peak:Z,dynamicRange:o,snr:s,score:n}}catch(g){console.error("音频质量分析失败:",g),R.error("音频质量分析失败")}finally{X.value=!1}},x=g=>{const u=[...g].map(Math.abs).sort((E,o)=>E-o),T=Math.floor(u.length*.1);return u.slice(0,T).reduce((E,o)=>E+o,0)/T},_=(g,u,T,Z)=>{let E=0;return g>.1&&g<.8?E+=25:g>.05?E+=15:E+=5,u<.95&&u>.3?E+=25:u<.99?E+=15:E+=5,T>10?E+=25:T>5?E+=15:E+=5,Z>40?E+=25:Z>20?E+=15:E+=5,Math.min(100,E)},he=g=>g>=80?"excellent":g>=60?"good":g>=40?"fair":"poor",ke=()=>{I.value=!I.value},me=()=>{if(!f.value)return;f.value.currentTime=j.value,f.value.play();const g=(M.value-j.value)*1e3;setTimeout(()=>{f.value&&!f.value.paused&&f.value.pause()},g)},_e=()=>{R.info("片段导出功能开发中...")};return $e(ae,()=>{J.value&&P.value&&le()}),(g,u)=>{const T=F("el-button"),Z=F("el-icon"),E=F("el-input-number");return a(),r("div",zt,[e("div",Ut,[e("h4",null,i(U.file.name),1),e("div",Mt,[e("span",xt,i(ie(U.file.size)),1),U.file.duration?(a(),r("span",Tt,i(re(U.file.duration)),1)):b("",!0)])]),e("div",At,[e("audio",{ref_key:"audioRef",ref:f,src:L.value,controls:"",preload:"metadata",onLoadedmetadata:l,onTimeupdate:C,onError:h},null,40,Dt)]),U.showWaveform?(a(),r("div",Bt,[e("div",Rt,[u[2]||(u[2]=e("span",null,"🌊 波形图",-1)),t(T,{text:"",onClick:N,size:"small"},{default:c(()=>[B(i(J.value?"隐藏":"显示"),1)]),_:1})]),Ve(e("div",Pt,[e("canvas",{ref_key:"waveformCanvas",ref:v,class:"waveform-canvas",onClick:w},null,512),K.value?(a(),r("div",Ft,[t(Z,{class:"is-loading"},{default:c(()=>[t(z(we))]),_:1}),u[3]||(u[3]=e("span",null,"生成波形图中...",-1))])):b("",!0)],512),[[ze,J.value]])])):b("",!0),U.showSpectrum?(a(),r("div",It,[e("div",Wt,[u[4]||(u[4]=e("span",null,"📊 频谱图",-1)),t(T,{text:"",onClick:Y,size:"small"},{default:c(()=>[B(i(O.value?"隐藏":"显示"),1)]),_:1})]),Ve(e("div",Nt,[e("canvas",{ref_key:"spectrumCanvas",ref:m,class:"spectrum-canvas"},null,512),Q.value?(a(),r("div",Et,[t(Z,{class:"is-loading"},{default:c(()=>[t(z(we))]),_:1}),u[5]||(u[5]=e("span",null,"生成频谱图中...",-1))])):b("",!0)],512),[[ze,O.value]])])):b("",!0),U.showQualityAnalysis?(a(),r("div",Lt,[e("div",Ot,[u[6]||(u[6]=e("span",null,"📈 音频质量分析",-1)),t(T,{text:"",onClick:de,size:"small"},{default:c(()=>[B(i(W.value?"隐藏":"显示"),1)]),_:1})]),Ve(e("div",jt,[X.value?(a(),r("div",Ht,[t(Z,{class:"is-loading"},{default:c(()=>[t(z(we))]),_:1}),u[7]||(u[7]=e("span",null,"分析音频质量中...",-1))])):(a(),r("div",qt,[e("div",Qt,[u[8]||(u[8]=e("label",null,"RMS 音量",-1)),e("div",Gt,[e("div",{class:"bar-fill",style:Re({width:`${($.value.rms||0)*100}%`})},null,4)]),e("span",null,i((($.value.rms||0)*100).toFixed(1))+"%",1)]),e("div",Jt,[u[9]||(u[9]=e("label",null,"峰值音量",-1)),e("div",Kt,[e("div",{class:"bar-fill",style:Re({width:`${($.value.peak||0)*100}%`})},null,4)]),e("span",null,i((($.value.peak||0)*100).toFixed(1))+"%",1)]),e("div",Xt,[u[10]||(u[10]=e("label",null,"动态范围",-1)),e("span",Yt,i(($.value.dynamicRange||0).toFixed(1))+" dB",1)]),e("div",Zt,[u[11]||(u[11]=e("label",null,"信噪比",-1)),e("span",eo,i(($.value.snr||0).toFixed(1))+" dB",1)]),e("div",so,[u[12]||(u[12]=e("label",null,"质量评分",-1)),e("div",{class:fe(["quality-score",he($.value.score)])},i($.value.score||0)+"/100 ",3)])]))],512),[[ze,W.value]])])):b("",!0),U.showSegmentSelector?(a(),r("div",to,[e("div",oo,[u[13]||(u[13]=e("span",null,"✂️ 片段选择",-1)),t(T,{text:"",onClick:ke,size:"small"},{default:c(()=>[B(i(I.value?"隐藏":"显示"),1)]),_:1})]),Ve(e("div",lo,[e("div",no,[t(E,{modelValue:j.value,"onUpdate:modelValue":u[0]||(u[0]=o=>j.value=o),min:0,max:V.value.duration||0,step:.1,precision:1,size:"small",placeholder:"开始时间"},null,8,["modelValue","max"]),u[16]||(u[16]=e("span",null,"-",-1)),t(E,{modelValue:M.value,"onUpdate:modelValue":u[1]||(u[1]=o=>M.value=o),min:j.value,max:V.value.duration||0,step:.1,precision:1,size:"small",placeholder:"结束时间"},null,8,["modelValue","min","max"]),t(T,{onClick:me,size:"small",type:"primary"},{default:c(()=>u[14]||(u[14]=[B("播放片段")])),_:1,__:[14]}),t(T,{onClick:_e,size:"small"},{default:c(()=>u[15]||(u[15]=[B("导出片段")])),_:1,__:[15]})]),e("div",ao,[e("span",null,"片段时长: "+i(re((M.value||0)-(j.value||0))),1)])],512),[[ze,I.value]])])):b("",!0),e("div",io,[e("div",ro,[e("div",uo,[u[17]||(u[17]=e("label",null,"采样率",-1)),e("span",null,i(V.value.sampleRate||"N/A")+" Hz",1)]),e("div",co,[u[18]||(u[18]=e("label",null,"声道数",-1)),e("span",null,i(V.value.channels||"N/A"),1)]),e("div",po,[u[19]||(u[19]=e("label",null,"比特率",-1)),e("span",null,i(V.value.bitRate||"N/A")+" kbps",1)]),e("div",vo,[u[20]||(u[20]=e("label",null,"格式",-1)),e("span",null,i(d(U.file.name)),1)]),e("div",mo,[u[21]||(u[21]=e("label",null,"文件大小",-1)),e("span",null,i(ie(U.file.size)),1)]),e("div",go,[u[22]||(u[22]=e("label",null,"时长",-1)),e("span",null,i(re(V.value.duration||0)),1)])])])])}}},fo=ye(_o,[["__scopeId","data-v-cc2cd66b"]]);const ho={class:"batch-file-list"},ko={class:"list-header"},yo={class:"header-info"},bo={class:"status-summary"},$o={class:"status-item success"},wo={class:"status-item processing"},Co={class:"status-item error"},So={class:"header-actions"},Vo={class:"file-list-container"},zo={key:0,class:"empty-list"},Uo={key:1,class:"file-items"},Mo={class:"file-checkbox"},xo=["onClick"],To={class:"file-icon"},Ao={class:"file-details"},Do=["title"],Bo={class:"file-meta"},Ro={class:"file-size"},Po={class:"file-type"},Fo={key:0,class:"file-duration"},Io={class:"file-status"},Wo={key:0,class:"status-processing"},No={class:"progress-info"},Eo={key:1,class:"status-success"},Lo={key:2,class:"status-error"},Oo=["title"],jo={key:3,class:"status-pending"},Ho={class:"file-actions"},qo={key:0,class:"batch-actions"},Qo={class:"batch-info"},Go={key:0},Jo={class:"batch-buttons"},Ko={__name:"BatchFileList",props:{files:{type:Array,default:()=>[]},processingStatus:{type:Object,default:()=>({})}},emits:["remove-file","preview-file","download-result","retry-failed","export-results"],setup(U,{emit:A}){const f=U,v=A,m=S([]),L=q(()=>f.files.length>0&&m.value.length===f.files.length),V=q(()=>f.files.filter((C,h)=>I(h)==="success").length),J=q(()=>f.files.filter((C,h)=>I(h)==="processing").length),O=q(()=>f.files.filter((C,h)=>I(h)==="error").length),K=q(()=>f.files.reduce((C,h)=>C+(h.size||0),0)),Q=q(()=>f.files.reduce((C,h)=>C+(h.duration||0),0)),W=C=>{if(C===0)return"0 B";const h=1024,w=["B","KB","MB","GB"],N=Math.floor(Math.log(C)/Math.log(h));return parseFloat((C/Math.pow(h,N)).toFixed(2))+" "+w[N]},X=C=>{const h=Math.floor(C/60),w=Math.floor(C%60);return`${h}:${w.toString().padStart(2,"0")}`},$=C=>C.split(".").pop().toUpperCase(),I=C=>{var w,N;const h=((w=f.files[C])==null?void 0:w.uid)||C;return((N=f.processingStatus[h])==null?void 0:N.status)||"pending"},j=C=>{var w,N;const h=((w=f.files[C])==null?void 0:w.uid)||C;return((N=f.processingStatus[h])==null?void 0:N.progress)||0},M=C=>{var w,N;const h=((w=f.files[C])==null?void 0:w.uid)||C;return((N=f.processingStatus[h])==null?void 0:N.error)||"未知错误"},ee=C=>{const h=m.value.indexOf(C);h>-1?m.value.splice(h,1):m.value.push(C)},P=()=>{L.value?m.value=[]:m.value=f.files.map((C,h)=>h)},ae=async()=>{if(m.value.length!==0)try{await xe.confirm(`确定要删除选中的 ${m.value.length} 个文件吗？`,"确认删除",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"});const C=[...m.value].sort((h,w)=>w-h);for(const h of C)v("remove-file",h);m.value=[],R.success(`已删除 ${C.length} 个文件`)}catch{}},ve=(C,h)=>{ee(h)},ce=(C,h)=>{v("preview-file",C,h)},ie=(C,h)=>{v("download-result",C,h)},re=async C=>{try{await xe.confirm(`确定要删除文件 "${f.files[C].name}" 吗？`,"确认删除",{confirmButtonText:"删除",cancelButtonText:"取消",type:"warning"}),v("remove-file",C);const h=m.value.indexOf(C);h>-1&&m.value.splice(h,1),m.value=m.value.map(w=>w>C?w-1:w)}catch{}},d=()=>{v("retry-failed")},l=()=>{v("export-results")};return(C,h)=>{const w=F("el-icon"),N=F("el-button"),oe=F("el-checkbox");return a(),r("div",ho,[e("div",ko,[e("div",yo,[e("h4",null,"文件列表 ("+i(U.files.length)+")",1),e("div",bo,[e("span",$o,[t(w,null,{default:c(()=>[t(z(Ue))]),_:1}),B(" 成功: "+i(V.value),1)]),e("span",wo,[t(w,null,{default:c(()=>[t(z(we))]),_:1}),B(" 处理中: "+i(J.value),1)]),e("span",Co,[t(w,null,{default:c(()=>[t(z(Me))]),_:1}),B(" 失败: "+i(O.value),1)])])]),e("div",So,[t(N,{onClick:P,size:"small",disabled:U.files.length===0},{default:c(()=>[B(i(L.value?"取消全选":"全选"),1)]),_:1},8,["disabled"]),t(N,{onClick:ae,size:"small",type:"danger",disabled:m.value.length===0},{default:c(()=>[B(" 删除选中 ("+i(m.value.length)+") ",1)]),_:1},8,["disabled"])])]),e("div",Vo,[U.files.length===0?(a(),r("div",zo,h[0]||(h[0]=[e("div",{class:"empty-icon"},"📁",-1),e("p",null,"暂无文件",-1)]))):(a(),r("div",Uo,[(a(!0),r(pe,null,ge(U.files,(le,Y)=>(a(),r("div",{key:le.uid||Y,class:fe(["file-item",{"is-selected":m.value.includes(Y),"is-processing":I(Y)==="processing","is-success":I(Y)==="success","is-error":I(Y)==="error"}])},[e("div",Mo,[t(oe,{"model-value":m.value.includes(Y),onChange:se=>ee(Y)},null,8,["model-value","onChange"])]),e("div",{class:"file-info",onClick:se=>ve(le,Y)},[e("div",To,[t(w,{size:"24"},{default:c(()=>[t(z(We))]),_:1})]),e("div",Ao,[e("div",{class:"file-name",title:le.name},i(le.name),9,Do),e("div",Bo,[e("span",Ro,i(W(le.size)),1),e("span",Po,i($(le.name)),1),le.duration?(a(),r("span",Fo,i(X(le.duration)),1)):b("",!0)])])],8,xo),e("div",Io,[I(Y)==="processing"?(a(),r("div",Wo,[t(w,{class:"is-loading"},{default:c(()=>[t(z(we))]),_:1}),h[1]||(h[1]=e("span",null,"处理中",-1)),e("div",No,i(j(Y))+"% ",1)])):I(Y)==="success"?(a(),r("div",Eo,[t(w,{color:"var(--success-color)"},{default:c(()=>[t(z(Ue))]),_:1}),h[2]||(h[2]=e("span",null,"完成",-1))])):I(Y)==="error"?(a(),r("div",Lo,[t(w,{color:"var(--danger-color)"},{default:c(()=>[t(z(Me))]),_:1}),h[3]||(h[3]=e("span",null,"失败",-1)),e("div",{class:"error-info",title:M(Y)},i(M(Y)),9,Oo)])):(a(),r("div",jo,[t(w,{color:"var(--text-muted)"},{default:c(()=>[t(z(Te))]),_:1}),h[4]||(h[4]=e("span",null,"等待",-1))]))]),e("div",Ho,[t(N,{text:"",onClick:se=>ce(le,Y),icon:z(Ee),title:"预览",size:"small"},null,8,["onClick","icon"]),I(Y)==="success"?(a(),ne(N,{key:0,text:"",onClick:se=>ie(le,Y),icon:z(Le),title:"下载结果",size:"small"},null,8,["onClick","icon"])):b("",!0),t(N,{text:"",onClick:se=>re(Y),icon:z(Se),title:"删除",size:"small",type:"danger"},null,8,["onClick","icon"])])],2))),128))]))]),U.files.length>0?(a(),r("div",qo,[e("div",Qo,[e("span",null,"总计: "+i(U.files.length)+" 个文件",1),e("span",null,"总大小: "+i(W(K.value)),1),Q.value>0?(a(),r("span",Go,"总时长: "+i(X(Q.value)),1)):b("",!0)]),e("div",Jo,[t(N,{onClick:d,disabled:O.value===0,size:"small"},{default:c(()=>[B(" 重试失败项 ("+i(O.value)+") ",1)]),_:1},8,["disabled"]),t(N,{onClick:l,disabled:V.value===0,size:"small",type:"primary"},{default:c(()=>[B(" 导出结果 ("+i(V.value)+") ",1)]),_:1},8,["disabled"])])])):b("",!0)])}}},Xo=ye(Ko,[["__scopeId","data-v-cb9c1d46"]]);const Yo={class:"status-indicator"},Zo={class:"status-text"},el={key:0,class:"status-details"},sl={key:0,class:"connecting-info"},tl={key:1,class:"error-info"},ol={key:2,class:"reconnect-info"},ll={key:1,class:"status-actions"},nl={__name:"WebSocketStatus",props:{showDetails:{type:Boolean,default:!1},showActions:{type:Boolean,default:!1},autoConnect:{type:Boolean,default:!0}},emits:["connected","disconnected","error"],setup(U,{emit:A}){const f=U,v=A,m=Oe(),L=q(()=>({"status-connected":m.isConnected,"status-connecting":m.isConnecting,"status-disconnected":m.isDisconnected,"status-error":m.hasError})),V=q(()=>({"dot-connected":m.isConnected,"dot-connecting":m.isConnecting,"dot-disconnected":m.isDisconnected,"dot-error":m.hasError})),J=q(()=>m.isConnected?"WebSocket已连接":m.isConnecting?"WebSocket连接中":m.hasError?"WebSocket连接错误":"WebSocket未连接"),O=q(()=>5),{isConnected:K,isConnecting:Q,isDisconnected:W,hasError:X,reconnectAttempts:$,lastError:I}=m,j=async()=>{try{await m.connect(),R.success("WebSocket连接成功"),v("connected")}catch(ee){R.error(`WebSocket连接失败: ${ee.message}`),v("error",ee)}},M=()=>{m.disconnect(),R.info("WebSocket已断开"),v("disconnected")};return m.on("connected",()=>{v("connected")}),m.on("disconnected",ee=>{v("disconnected",ee)}),m.on("error",ee=>{v("error",ee)}),f.autoConnect&&m.isDisconnected&&j(),(ee,P)=>{var ae;return a(),r("div",{class:fe(["websocket-status",L.value])},[e("div",Yo,[e("div",{class:fe(["status-dot",V.value])},null,2),e("span",Zo,i(J.value),1)]),U.showDetails?(a(),r("div",el,[z(Q)?(a(),r("div",sl,[t(z(Pe),{class:"rotating"},{default:c(()=>[t(z(we))]),_:1}),P[0]||(P[0]=e("span",null,"正在连接...",-1))])):b("",!0),z(X)?(a(),r("div",tl,[t(z(Pe),null,{default:c(()=>[t(z(Ne))]),_:1}),e("span",null,i(((ae=z(I))==null?void 0:ae.message)||"连接错误"),1)])):b("",!0),z($)>0?(a(),r("div",ol,[e("span",null,"重连尝试: "+i(z($))+"/"+i(O.value),1)])):b("",!0)])):b("",!0),U.showActions?(a(),r("div",ll,[z(W)||z(X)?(a(),ne(z(Fe),{key:0,size:"small",type:"primary",onClick:j,loading:z(Q)},{default:c(()=>[B(i(z(Q)?"连接中...":"重连"),1)]),_:1},8,["loading"])):b("",!0),z(K)?(a(),ne(z(Fe),{key:1,size:"small",type:"danger",onClick:M},{default:c(()=>P[1]||(P[1]=[B(" 断开 ")])),_:1,__:[1]})):b("",!0)])):b("",!0)],2)}}},al=ye(nl,[["__scopeId","data-v-2dd2a372"]]);const il={class:"processing-progress"},rl={class:"progress-header"},ul={class:"header-info"},dl={class:"header-actions"},cl={class:"overall-progress"},pl={class:"progress-info"},vl={class:"progress-percentage"},ml={class:"progress-meta"},gl={key:0},_l={key:1},fl={key:0,class:"current-stage"},hl={class:"stage-header"},kl={class:"stage-icon"},yl={class:"stage-info"},bl={class:"stage-progress"},$l={key:1,class:"progress-details"},wl={class:"stage-list"},Cl={class:"stage-indicator"},Sl={class:"stage-content"},Vl={class:"stage-name"},zl={class:"stage-description"},Ul={key:0,class:"stage-mini-progress"},Ml={class:"stage-time"},xl={key:0},Tl={key:0,class:"performance-stats"},Al={class:"stats-grid"},Dl={class:"stat-item"},Bl={class:"stat-item"},Rl={class:"stat-item"},Pl={class:"stat-item"},Fl={key:1,class:"multi-task-monitor"},Il={class:"task-list"},Wl={class:"task-header"},Nl={class:"task-id"},El={class:"task-progress"},Ll={class:"progress-text"},Ol={key:0,class:"task-stage"},jl={key:2,class:"error-messages"},Hl={class:"error-list"},ql={class:"error-time"},Ql={class:"error-message"},Gl={key:0,class:"error-task"},Jl={class:"progress-actions"},Kl={__name:"ProcessingProgress",props:{currentTask:{type:String,default:""},overallProgress:{type:Number,default:0},taskDetails:{type:Object,default:()=>({})},enableMultiTask:{type:Boolean,default:!0},maxRetries:{type:Number,default:3}},emits:["pause-processing","cancel-processing","refresh-status","websocket-connected","websocket-disconnected","websocket-error","websocket-message","task-update","task-complete","task-error"],setup(U,{expose:A,emit:f}){const v=U,m=f,L=S(!1),V=S(!1),J=S(!1),O=S(Date.now()),K=S(0),Q=S([]),W=Oe(),X=S(new Map),$=S([]),I=S(50),j=q(()=>v.overallProgress<30?"var(--danger-color)":v.overallProgress<70?"var(--warning-color)":"var(--success-color)"),M=q(()=>v.taskDetails.currentStage||null),ee=q(()=>v.taskDetails.estimatedTime||0),P=q(()=>v.taskDetails.performance||null),ae=q(()=>v.currentTask&&v.overallProgress>0&&v.overallProgress<100),ve=q(()=>{const s=[{name:"initializing",title:"初始化",description:"准备处理环境和加载模型",status:"pending"},{name:"file_validation",title:"文件验证",description:"检查文件格式和完整性",status:"pending"},{name:"model_loading",title:"模型加载",description:"加载AI处理模型",status:"pending"},{name:"processing",title:"音频处理",description:"执行音频分析和处理",status:"pending"},{name:"result_generation",title:"结果生成",description:"生成处理结果和报告",status:"pending"},{name:"completed",title:"处理完成",description:"所有处理任务已完成",status:"pending"}],n=v.taskDetails.currentStage;if(n){const k=s.findIndex(y=>y.name===n.stage);s.forEach((y,D)=>{D<k?y.status="completed":D===k?(y.status="current",y.progress=n.percentage):y.status="pending"})}return s}),ce=s=>{if(!s||s<0)return"00:00";const n=Math.floor(s/3600),k=Math.floor(s%3600/60),y=Math.floor(s%60);return n>0?`${n.toString().padStart(2,"0")}:${k.toString().padStart(2,"0")}:${y.toString().padStart(2,"0")}`:`${k.toString().padStart(2,"0")}:${y.toString().padStart(2,"0")}`},ie=s=>new Date(s).toLocaleTimeString(),re=s=>({initializing:"🚀",file_validation:"📁",model_loading:"🤖",processing:"⚙️",result_generation:"📊",completed:"✅"})[s]||"⏳",d=s=>({initializing:"初始化处理",file_validation:"验证文件",model_loading:"加载模型",processing:"处理音频",result_generation:"生成结果",completed:"处理完成"})[s]||"处理中",l=()=>{L.value=!L.value},C=()=>{V.value=!V.value,m("pause-processing",V.value),R.info(V.value?"处理已暂停":"处理已继续")},h=async()=>{try{await xe.confirm("确定要取消当前处理任务吗？已处理的进度将会丢失。","确认取消",{confirmButtonText:"取消任务",cancelButtonText:"继续处理",type:"warning"}),m("cancel-processing")}catch{}},w=async()=>{J.value=!0;try{m("refresh-status"),await new Promise(s=>setTimeout(s,1e3)),R.success("状态已刷新")}catch{R.error("刷新状态失败")}finally{J.value=!1}},N=s=>{console.log("📈 ProcessingProgress收到进度消息:",s);const n=s.task_id||s.taskId;if(n===v.currentTask){const k={task_id:n,progress:s.progress||s.percentage||0,detail:s.detail||s.message||"",stage:s.stage||s.status||"processing"};console.log("🔄 更新当前任务进度:",k),m("task-update",k)}if(v.enableMultiTask){const k=s.progress||s.percentage||0,y=s.stage||s.status||"processing",D=s.detail||s.message||"";X.value.set(n,{progress:k,status:y,currentStage:D?{stage:y,detail:D}:null,lastUpdate:Date.now()}),console.log("📊 更新多任务监控:",n,{progress:k,status:y,detail:D})}},oe=s=>{console.log("✅ ProcessingProgress收到完成消息:",s);const n=s.task_id||s.taskId;n===v.currentTask&&m("task-complete",s),X.value.delete(n),$.value.unshift({taskId:n,status:"completed",completedAt:Date.now(),result:s.result||s}),$.value.length>I.value&&($.value=$.value.slice(0,I.value)),console.log("📝 任务完成记录已添加:",n)},le=s=>{console.log("❌ ProcessingProgress收到失败消息:",s);const n=s.task_id||s.taskId,k=s.error||s.error_message||"任务处理失败";n===v.currentTask&&m("task-error",s),X.value.delete(n),Q.value.unshift({taskId:n,message:k,timestamp:Date.now()}),$.value.unshift({taskId:n,status:"error",completedAt:Date.now(),error:k}),console.log("📝 任务失败记录已添加:",n,k)};let Y=null,se=null;const ue=s=>{se&&clearInterval(se);const n=typeof s=="string"?s:(s==null?void 0:s.id)||null;if(!n){console.warn("⚠️ 无效的任务ID，跳过轮询:",s);return}console.log("🔄 启动进度轮询:",n,"(原始值:",s,")"),se=setInterval(async()=>{try{const k=await fetch(`/api/v1/tasks/${n}/progress`);if(k.ok){const y=await k.json();console.log("📊 轮询获取进度:",y),N({task_id:n,progress:y.percentage||0,detail:y.detail||"",stage:y.status||"processing"}),y.status==="completed"?(oe({task_id:n,result:y.result||{}}),clearInterval(se),se=null):y.status==="failed"&&(le({task_id:n,error:y.error_message||"任务执行失败"}),clearInterval(se),se=null)}}catch(k){console.error("❌ 进度轮询失败:",k)}},2e3)},de=()=>{se&&(clearInterval(se),se=null,console.log("⏹️ 停止进度轮询"))};Ae(()=>{Y=setInterval(()=>{K.value=(Date.now()-O.value)/1e3},1e3),W.on("progress",N),W.on("task_completed",oe),W.on("task_failed",le),v.currentTask&&!W.isConnected&&(console.log("⚠️ WebSocket未连接，启动轮询备用机制"),ue(v.currentTask))}),De(()=>{Y&&clearInterval(Y),de(),W.off("progress",N),W.off("task_completed",oe),W.off("task_failed",le)}),$e(()=>v.currentTask,(s,n)=>{console.log("🔄 任务变化:",n,"->",s),n&&se&&de(),s&&(O.value=Date.now(),K.value=0,Q.value=[],W.isConnected||(console.log("⚠️ WebSocket未连接，为新任务启动轮询:",s),ue(s)))}),$e(()=>W.isConnected,s=>{console.log("🔌 WebSocket连接状态变化:",s),s?se&&(console.log("✅ WebSocket已连接，停止轮询"),de()):v.currentTask&&!se&&(console.log("⚠️ WebSocket断开，启动轮询备用机制"),ue(v.currentTask))});const G=()=>{if(!(!v.enableWebSocket||!v.websocketUrl))try{websocket.value=new WebSocket(v.websocketUrl),websocket.value.onopen=()=>{wsConnected.value=!0,wsReconnectAttempts.value=0,m("websocket-connected"),ke(),g(),R.success("WebSocket连接已建立")},websocket.value.onmessage=s=>{try{const n=JSON.parse(s.data);u(n)}catch(n){console.error("WebSocket消息解析失败:",n)}},websocket.value.onclose=s=>{wsConnected.value=!1,me(),m("websocket-disconnected",s),wsReconnectAttempts.value<v.maxRetries?_():R.error("WebSocket连接失败，已达到最大重试次数")},websocket.value.onerror=s=>{console.error("WebSocket错误:",s),m("websocket-error",s)}}catch(s){console.error("WebSocket连接失败:",s),R.error("WebSocket连接失败")}},x=()=>{websocket.value&&(websocket.value.close(),websocket.value=null),me(),he(),wsConnected.value=!1},_=()=>{he();const s=Math.min(1e3*Math.pow(2,wsReconnectAttempts.value),3e4);wsReconnectAttempts.value++,wsReconnectTimer.value=setTimeout(()=>{console.log(`WebSocket重连尝试 ${wsReconnectAttempts.value}/${v.maxRetries}`),G()},s)},he=()=>{wsReconnectTimer.value&&(clearTimeout(wsReconnectTimer.value),wsReconnectTimer.value=null)},ke=()=>{me(),wsHeartbeatTimer.value=setInterval(()=>{var s;wsConnected.value&&((s=websocket.value)==null?void 0:s.readyState)===WebSocket.OPEN&&_e({type:"ping",timestamp:Date.now()})},3e4)},me=()=>{wsHeartbeatTimer.value&&(clearInterval(wsHeartbeatTimer.value),wsHeartbeatTimer.value=null)},_e=s=>{var n;wsConnected.value&&((n=websocket.value)==null?void 0:n.readyState)===WebSocket.OPEN?websocket.value.send(JSON.stringify(s)):wsMessageQueue.value.push(s)},g=()=>{for(;wsMessageQueue.value.length>0;){const s=wsMessageQueue.value.shift();_e(s)}},u=s=>{switch(m("websocket-message",s),s.type){case"task_update":T(s.payload);break;case"task_complete":Z(s.payload);break;case"task_error":E(s.payload);break;case"progress_update":o(s.payload);break;case"pong":break;default:console.log("未知WebSocket消息类型:",s.type)}},T=s=>{X.value.set(s.taskId,{...s,lastUpdate:Date.now()}),m("task-update",s)},Z=s=>{const n=X.value.get(s.taskId);n&&(n.status="completed",n.completedAt=Date.now(),p(n),X.value.delete(s.taskId)),m("task-complete",s)},E=s=>{const n=X.value.get(s.taskId);n&&(n.status="error",n.error=s.error,n.errorAt=Date.now(),Q.value.push({timestamp:Date.now(),message:s.error,taskId:s.taskId}),p(n),X.value.delete(s.taskId)),m("task-error",s)},o=s=>{const n=X.value.get(s.taskId);n&&(n.progress=s.progress,n.currentStage=s.currentStage,n.lastUpdate=Date.now())},p=s=>{$.value.unshift(s),$.value.length>I.value&&($.value=$.value.slice(0,I.value))};return $e(()=>v.taskDetails.errors,s=>{s&&Array.isArray(s)&&(Q.value=s)},{deep:!0}),$e(()=>v.websocketConnected,s=>{wsConnected.value=s}),A({connectWebSocket:G,disconnectWebSocket:x,sendWebSocketMessage:_e,activeTasks:q(()=>X.value),taskHistory:q(()=>$.value),wsConnected:q(()=>wsConnected.value)}),(s,n)=>{const k=F("el-button"),y=F("el-progress"),D=F("el-icon");return a(),r("div",il,[e("div",rl,[e("div",ul,[n[0]||(n[0]=e("h4",null,"🔄 处理进度",-1)),t(al,{"show-details":!1,"show-actions":!1})]),e("div",dl,[t(k,{onClick:l,text:"",size:"small"},{default:c(()=>[B(i(L.value?"隐藏详情":"显示详情"),1)]),_:1})])]),e("div",cl,[e("div",pl,[n[1]||(n[1]=e("span",{class:"progress-label"},"总体进度",-1)),e("span",vl,i(Math.round(U.overallProgress))+"%",1)]),t(y,{percentage:U.overallProgress,"stroke-width":12,"show-text":!1,color:j.value,class:"progress-bar"},null,8,["percentage","color"]),e("div",ml,[U.currentTask?(a(),r("span",gl,"任务ID: "+i(U.currentTask),1)):b("",!0),ee.value?(a(),r("span",_l,"预计剩余: "+i(ce(ee.value)),1)):b("",!0)])]),M.value?(a(),r("div",fl,[e("div",hl,[e("div",kl,i(re(M.value.stage)),1),e("div",yl,[e("h5",null,i(d(M.value.stage)),1),e("p",null,i(M.value.detail||"正在处理..."),1)])]),e("div",bl,[t(y,{percentage:M.value.percentage||0,"stroke-width":6,"show-text":!1,color:"var(--accent-secondary)"},null,8,["percentage"])])])):b("",!0),L.value?(a(),r("div",$l,[n[8]||(n[8]=e("div",{class:"details-header"},[e("h5",null,"📊 详细信息")],-1)),e("div",wl,[(a(!0),r(pe,null,ge(ve.value,H=>(a(),r("div",{key:H.name,class:fe(["stage-item",{"is-completed":H.status==="completed","is-current":H.status==="current","is-pending":H.status==="pending","is-error":H.status==="error"}])},[e("div",Cl,[H.status==="completed"?(a(),ne(D,{key:0,color:"var(--success-color)"},{default:c(()=>[t(z(Ue))]),_:1})):H.status==="current"?(a(),ne(D,{key:1,color:"var(--accent-primary)",class:"is-loading"},{default:c(()=>[t(z(we))]),_:1})):H.status==="error"?(a(),ne(D,{key:2,color:"var(--danger-color)"},{default:c(()=>[t(z(Me))]),_:1})):(a(),ne(D,{key:3,color:"var(--text-muted)"},{default:c(()=>[t(z(Te))]),_:1}))]),e("div",Sl,[e("div",Vl,i(H.title),1),e("div",zl,i(H.description),1),H.status==="current"&&H.progress?(a(),r("div",Ul,[t(y,{percentage:H.progress,"stroke-width":4,"show-text":!1,color:"var(--accent-primary)"},null,8,["percentage"])])):b("",!0)]),e("div",Ml,[H.startTime?(a(),r("span",xl,i(ce(H.duration||0)),1)):b("",!0)])],2))),128))]),P.value?(a(),r("div",Tl,[n[6]||(n[6]=e("h6",null,"⚡ 性能统计",-1)),e("div",Al,[e("div",Dl,[n[2]||(n[2]=e("label",null,"处理速度",-1)),e("span",null,i(P.value.processingSpeed||"N/A"),1)]),e("div",Bl,[n[3]||(n[3]=e("label",null,"内存使用",-1)),e("span",null,i(P.value.memoryUsage||"N/A"),1)]),e("div",Rl,[n[4]||(n[4]=e("label",null,"CPU使用率",-1)),e("span",null,i(P.value.cpuUsage||"N/A"),1)]),e("div",Pl,[n[5]||(n[5]=e("label",null,"已用时间",-1)),e("span",null,i(ce(K.value)),1)])])])):b("",!0),U.enableMultiTask&&X.value.size>0?(a(),r("div",Fl,[e("h6",null,"🔄 活跃任务 ("+i(X.value.size)+")",1),e("div",Il,[(a(!0),r(pe,null,ge(X.value,([H,te])=>(a(),r("div",{key:H,class:fe(["task-item",{"is-current":H===U.currentTask}])},[e("div",Wl,[e("div",Nl,i(H.substring(0,8))+"...",1),e("div",{class:fe(["task-status",`status-${te.status}`])},i(te.status),3)]),e("div",El,[t(y,{percentage:te.progress||0,"stroke-width":4,"show-text":!1,color:"var(--accent-primary)"},null,8,["percentage"]),e("span",Ll,i(te.progress||0)+"%",1)]),te.currentStage?(a(),r("div",Ol,i(d(te.currentStage.stage)),1)):b("",!0)],2))),128))])])):b("",!0),Q.value.length>0?(a(),r("div",jl,[n[7]||(n[7]=e("h6",null,"⚠️ 错误信息",-1)),e("div",Hl,[(a(!0),r(pe,null,ge(Q.value,(H,te)=>(a(),r("div",{key:te,class:"error-item"},[e("div",ql,i(ie(H.timestamp)),1),e("div",Ql,i(H.message),1),H.taskId?(a(),r("div",Gl,"任务: "+i(H.taskId.substring(0,8))+"...",1)):b("",!0)]))),128))])])):b("",!0)])):b("",!0),e("div",Jl,[t(k,{onClick:C,disabled:!ae.value,size:"small"},{default:c(()=>[B(i(V.value?"继续":"暂停"),1)]),_:1},8,["disabled"]),t(k,{onClick:h,type:"danger",size:"small"},{default:c(()=>n[9]||(n[9]=[B(" 取消处理 ")])),_:1,__:[9]}),t(k,{onClick:w,loading:J.value,size:"small"},{default:c(()=>n[10]||(n[10]=[B(" 刷新状态 ")])),_:1,__:[10]},8,["loading"])])])}}},Xl=ye(Kl,[["__scopeId","data-v-ffe48f47"]]);const Yl={name:"ProcessingResults",components:{Document:Ye},props:{results:{type:Array,default:()=>[]},processingMode:{type:String,default:"speech_recognition"}},emits:["result-selected"],setup(U,{emit:A}){const f=S(!1),v=S(null),m=q(()=>U.results.length>0),L=q(()=>U.results.filter($=>$.status==="success").length),V=q(()=>U.results.filter($=>$.status==="error").length),J=q(()=>{const $=U.results.reduce((I,j)=>j.status==="success"&&j.duration?I+parseFloat(j.duration):I,0);return $>0?`${$.toFixed(1)}s`:"N/A"});return{detailDialogVisible:f,selectedResult:v,hasResults:m,successCount:L,failureCount:V,totalDuration:J,viewDetails:$=>{v.value=$,f.value=!0,A("result-selected",$)},downloadResult:$=>{if($.downloadUrl){const I=document.createElement("a");I.href=$.downloadUrl,I.download=`${$.fileName}_result.json`,I.click()}else R.warning("暂无下载链接")},playAudio:$=>{$.audioUrl?new Audio($.audioUrl).play().catch(()=>{R.error("音频播放失败")}):R.warning("暂无音频文件")},getConfidenceColor:$=>$>=.8?"#67c23a":$>=.6?"#e6a23c":"#f56c6c",getDetailComponent:()=>"div"}}},Zl={class:"processing-results"},en={key:0,class:"no-results"},sn={key:1,class:"results-container"},tn={class:"results-summary"},on={class:"summary-content"},ln={class:"summary-item"},nn={class:"value"},an={class:"summary-item"},rn={class:"value success"},un={class:"summary-item"},dn={class:"value error"},cn={class:"summary-item"},pn={class:"value"},vn={class:"results-list"},mn={class:"result-header"},gn={class:"file-info"},_n={class:"file-name"},fn={class:"result-actions"},hn={key:0,class:"result-content"},kn={key:0,class:"speech-result"},yn={class:"transcript"},bn={class:"metadata"},$n={key:1,class:"speaker-result"},wn={class:"speakers-info"},Cn={class:"speaker-count"},Sn={key:0,class:"speakers-list"},Vn={class:"speaker-id"},zn={class:"speaker-duration"},Un={key:2,class:"meeting-result"},Mn={class:"meeting-info"},xn={class:"meeting-stats"},Tn={key:0,class:"dialogue-preview"},An={class:"speaker"},Dn={class:"text"},Bn={key:0,class:"more-indicator"},Rn={key:3,class:"general-result"},Pn={class:"result-data"},Fn={key:1,class:"error-content"},In={key:0,class:"detail-content"};function Wn(U,A,f,v,m,L){var j;const V=F("el-empty"),J=F("el-card"),O=F("Document"),K=F("el-icon"),Q=F("el-tag"),W=F("el-button"),X=F("el-progress"),$=F("el-alert"),I=F("el-dialog");return a(),r("div",Zl,[v.hasResults?(a(),r("div",sn,[e("div",tn,[t(J,{shadow:"hover"},{default:c(()=>[e("div",on,[e("div",ln,[A[1]||(A[1]=e("span",{class:"label"},"总文件数:",-1)),e("span",nn,i(f.results.length),1)]),e("div",an,[A[2]||(A[2]=e("span",{class:"label"},"成功处理:",-1)),e("span",rn,i(v.successCount),1)]),e("div",un,[A[3]||(A[3]=e("span",{class:"label"},"处理失败:",-1)),e("span",dn,i(v.failureCount),1)]),e("div",cn,[A[4]||(A[4]=e("span",{class:"label"},"总时长:",-1)),e("span",pn,i(v.totalDuration),1)])])]),_:1})]),e("div",vn,[(a(!0),r(pe,null,ge(f.results,(M,ee)=>(a(),ne(J,{key:ee,class:"result-item",shadow:"hover"},{default:c(()=>[e("div",mn,[e("div",gn,[t(K,{class:"file-icon"},{default:c(()=>[t(O)]),_:1}),e("span",_n,i(M.fileName),1),t(Q,{type:M.status==="success"?"success":"danger",size:"small"},{default:c(()=>[B(i(M.status==="success"?"成功":"失败"),1)]),_:2},1032,["type"])]),e("div",fn,[M.status==="success"?(a(),ne(W,{key:0,type:"primary",size:"small",onClick:P=>v.viewDetails(M)},{default:c(()=>A[5]||(A[5]=[B(" 查看详情 ")])),_:2,__:[5]},1032,["onClick"])):b("",!0),M.status==="success"?(a(),ne(W,{key:1,type:"success",size:"small",onClick:P=>v.downloadResult(M)},{default:c(()=>A[6]||(A[6]=[B(" 下载结果 ")])),_:2,__:[6]},1032,["onClick"])):b("",!0),t(W,{type:"info",size:"small",onClick:P=>v.playAudio(M),disabled:!M.audioUrl},{default:c(()=>A[7]||(A[7]=[B(" 播放音频 ")])),_:2,__:[7]},1032,["onClick","disabled"])])]),M.status==="success"?(a(),r("div",hn,[f.processingMode==="speech_recognition"?(a(),r("div",kn,[A[8]||(A[8]=e("h4",null,"识别结果",-1)),e("div",yn,i(M.transcript||"无识别内容"),1),e("div",bn,[e("span",null,"时长: "+i(M.duration||"N/A"),1),e("span",null,"置信度: "+i(M.confidence?(M.confidence*100).toFixed(1)+"%":"N/A"),1)])])):f.processingMode==="speaker_recognition"?(a(),r("div",$n,[A[9]||(A[9]=e("h4",null,"说话人识别",-1)),e("div",wn,[e("div",Cn," 检测到 "+i(M.speakerCount||0)+" 个说话人 ",1),M.speakers?(a(),r("div",Sn,[(a(!0),r(pe,null,ge(M.speakers,P=>(a(),r("div",{key:P.id,class:"speaker-item"},[e("span",Vn,"说话人"+i(P.id),1),e("span",zn,i(P.duration),1),t(X,{percentage:Math.round(P.confidence*100),color:v.getConfidenceColor(P.confidence),"show-text":!1,size:"small"},null,8,["percentage","color"])]))),128))])):b("",!0)])])):f.processingMode==="meeting_transcription"?(a(),r("div",Un,[A[10]||(A[10]=e("h4",null,"会议转录",-1)),e("div",Mn,[e("div",xn,[e("span",null,"说话人数: "+i(M.speakerCount||0),1),e("span",null,"对话轮次: "+i(M.turnCount||0),1)]),M.dialogue?(a(),r("div",Tn,[(a(!0),r(pe,null,ge(M.dialogue.slice(0,3),(P,ae)=>(a(),r("div",{key:ae,class:"dialogue-turn"},[e("span",An,i(P.speaker)+":",1),e("span",Dn,i(P.text),1)]))),128)),M.dialogue.length>3?(a(),r("div",Bn," 还有 "+i(M.dialogue.length-3)+" 条对话... ",1)):b("",!0)])):b("",!0)])])):(a(),r("div",Rn,[A[11]||(A[11]=e("h4",null,"处理结果",-1)),e("div",Pn,[e("pre",null,i(JSON.stringify(M.data,null,2)),1)])]))])):(a(),r("div",Fn,[t($,{title:M.error||"处理失败",type:"error",closable:!1},null,8,["title"])]))]),_:2},1024))),128))])])):(a(),r("div",en,[t(V,{description:"暂无处理结果"})])),t(I,{modelValue:v.detailDialogVisible,"onUpdate:modelValue":A[0]||(A[0]=M=>v.detailDialogVisible=M),title:(j=v.selectedResult)==null?void 0:j.fileName,width:"80%",top:"5vh"},{default:c(()=>[v.selectedResult?(a(),r("div",In,[(a(),ne(Ze(v.getDetailComponent()),{result:v.selectedResult,"processing-mode":f.processingMode},null,8,["result","processing-mode"]))])):b("",!0)]),_:1},8,["modelValue","title"])])}const Nn=ye(Yl,[["render",Wn],["__scopeId","data-v-2668e515"]]);const En={class:"audio-processing-container"},Ln={class:"page-header"},On={class:"header-content"},jn={class:"header-actions"},Hn={class:"page-main"},qn={class:"container"},Qn={class:"main-content"},Gn={class:"config-panel"},Jn={class:"config-section"},Kn={class:"mode-icon"},Xn={class:"config-section"},Yn={class:"upload-mode-switch"},Zn={key:0,class:"config-section"},ea={class:"action-buttons"},sa={key:0},ta={key:1},oa={class:"work-area"},la={key:0,class:"file-section"},na={class:"section-header"},aa={key:1,class:"progress-section"},ia={key:2,class:"results-section"},ra={class:"section-header"},ua={class:"result-actions"},da={key:3,class:"empty-state"},ca={class:"empty-content"},pa={class:"supported-formats"},va={class:"format-tags"},ma=200*1024*1024,ga={__name:"AudioProcessing",setup(U){const A=S("vad_detection"),f=S("single"),v=S([]),m=S({}),L=S(!1),V=S(!1),J=S([]),O=S({}),K=S(null),Q=S(0),W=S({}),{connected:X,connect:$,disconnect:I,onMessage:j}=es(),{startAudioProcessing:M,cancelProcessing:ee,getProcessingStatus:P}=ss(),ae=[{value:"vad_detection",label:"VAD语音活动检测",icon:"🎯"},{value:"speech_recognition",label:"语音识别",icon:"🗣️"},{value:"speaker_recognition",label:"说话人识别",icon:"👥"},{value:"meeting_transcription",label:"会议语音转录",icon:"🎤"},{value:"audio_preprocessing",label:"音频预处理",icon:"🔧"},{value:"quality_analysis",label:"质量分析",icon:"📊"},{value:"comprehensive_analysis",label:"综合分析",icon:"🔬"}],ve=[".wav",".mp3",".m4a",".aac",".flac",".ogg"],ce=["WAV","MP3","M4A","AAC","FLAC","OGG"],ie=q(()=>v.value.length>0),re=q(()=>J.value.length>0),d=q(()=>ie.value&&!L.value),l=G=>{v.value=G,R.success(`已选择 ${G.length} 个文件`)},C=G=>{},h=G=>{m.value={...m.value,...G}},w=()=>{V.value=!V.value},N=async()=>{try{L.value=!0;const G=await M({files:v.value,mode:A.value,config:m.value});K.value=G.taskId,R.success("处理任务已启动")}catch(G){R.error(`启动处理失败: ${G.message}`),L.value=!1}},oe=()=>{v.value=[],O.value={},R.info("文件队列已清空")},le=G=>{v.value.splice(G,1)},Y=G=>{},se=G=>{},ue=()=>{},de=()=>{J.value=[],R.info("结果已清空")};return j(G=>{G.type==="progress"?(Q.value=G.progress,W.value=G.details):G.type==="task_complete"?(L.value=!1,J.value.push(G.result),R.success("处理完成")):G.type==="task_error"&&(L.value=!1,R.error(`处理失败: ${G.error}`))}),Ae(()=>{$()}),De(()=>{I()}),(G,x)=>{const _=F("router-link"),he=F("el-option"),ke=F("el-select"),me=F("el-radio-button"),_e=F("el-radio-group"),g=F("el-button"),u=F("el-tag");return a(),r("div",En,[e("div",Ln,[e("div",On,[x[4]||(x[4]=e("div",{class:"header-brand"},[e("div",{class:"brand-logo"},"🎵"),e("span",{class:"brand-text"},"音频处理中心")],-1)),e("div",jn,[t(_,{to:"/dashboard",class:"btn-outline"},{default:c(()=>x[3]||(x[3]=[B("返回控制台")])),_:1,__:[3]})])])]),e("div",Hn,[e("div",qn,[x[20]||(x[20]=e("div",{class:"intro-section"},[e("h1",{class:"page-title"},[e("span",{class:"title-main"},"智能音频处理平台"),e("span",{class:"title-sub gradient-text"},"集成VAD检测、语音识别、说话人识别、会议转录等功能")])],-1)),e("div",Qn,[e("div",Gn,[x[11]||(x[11]=e("div",{class:"panel-header"},[e("h3",null,"🔧 处理配置")],-1)),e("div",Jn,[x[5]||(x[5]=e("label",{class:"config-label"},"处理模式",-1)),t(ke,{modelValue:A.value,"onUpdate:modelValue":x[0]||(x[0]=T=>A.value=T),placeholder:"选择处理模式",class:"full-width"},{default:c(()=>[(a(),r(pe,null,ge(ae,T=>t(he,{key:T.value,label:T.label,value:T.value},{default:c(()=>[e("span",Kn,i(T.icon),1),e("span",null,i(T.label),1)]),_:2},1032,["label","value"])),64))]),_:1},8,["modelValue"])]),e("div",Xn,[x[8]||(x[8]=e("label",{class:"config-label"},"文件上传",-1)),e("div",Yn,[t(_e,{modelValue:f.value,"onUpdate:modelValue":x[1]||(x[1]=T=>f.value=T),size:"small"},{default:c(()=>[t(me,{label:"single"},{default:c(()=>x[6]||(x[6]=[B("单文件")])),_:1,__:[6]}),t(me,{label:"batch"},{default:c(()=>x[7]||(x[7]=[B("批量")])),_:1,__:[7]})]),_:1},8,["modelValue"])]),t(Ds,{mode:f.value,accept:ve,"max-size":ma,onFilesSelected:l,onUploadProgress:C},null,8,["mode"])]),V.value?(a(),r("div",Zn,[x[9]||(x[9]=e("label",{class:"config-label"},"高级配置",-1)),t(Vt,{"processing-mode":A.value,modelValue:m.value,"onUpdate:modelValue":x[2]||(x[2]=T=>m.value=T),onConfigChanged:h},null,8,["processing-mode","modelValue"])])):b("",!0),e("div",ea,[t(g,{type:"primary",disabled:!d.value,loading:L.value,onClick:N,class:"process-btn"},{default:c(()=>[L.value?(a(),r("span",ta,"⏳ 处理中...")):(a(),r("span",sa,"🚀 开始处理"))]),_:1},8,["disabled","loading"]),f.value==="batch"?(a(),ne(g,{key:0,onClick:oe,disabled:!ie.value},{default:c(()=>x[10]||(x[10]=[B(" 🗑️ 清空队列 ")])),_:1,__:[10]},8,["disabled"])):b("",!0)])]),e("div",oa,[ie.value?(a(),r("div",la,[e("div",na,[x[12]||(x[12]=e("h3",null,"📁 文件管理",-1)),t(g,{text:"",onClick:w,icon:V.value?"ArrowUp":"ArrowDown"},{default:c(()=>[B(i(V.value?"隐藏":"显示")+"高级配置 ",1)]),_:1},8,["icon"])]),f.value==="single"&&v.value.length===1?(a(),ne(fo,{key:0,file:v.value[0],"show-waveform":!0,"show-spectrum":!0},null,8,["file"])):f.value==="batch"?(a(),ne(Xo,{key:1,files:v.value,"processing-status":O.value,onRemoveFile:le,onPreviewFile:Y},null,8,["files","processing-status"])):b("",!0)])):b("",!0),L.value?(a(),r("div",aa,[t(Xl,{"current-task":K.value,"overall-progress":Q.value,"task-details":W.value,"websocket-connected":z(X)},null,8,["current-task","overall-progress","task-details","websocket-connected"])])):b("",!0),re.value?(a(),r("div",ia,[e("div",ra,[x[15]||(x[15]=e("h3",null,"📊 处理结果",-1)),e("div",ua,[t(g,{onClick:ue,icon:z(Le)},{default:c(()=>x[13]||(x[13]=[B("导出结果")])),_:1,__:[13]},8,["icon"]),t(g,{onClick:de,icon:z(Se)},{default:c(()=>x[14]||(x[14]=[B("清空结果")])),_:1,__:[14]},8,["icon"])])]),t(Nn,{results:J.value,"processing-mode":A.value,onResultSelected:se},null,8,["results","processing-mode"])])):b("",!0),!ie.value&&!L.value&&!re.value?(a(),r("div",da,[e("div",ca,[x[17]||(x[17]=e("div",{class:"empty-icon"},"🎵",-1)),x[18]||(x[18]=e("h3",null,"开始音频处理",-1)),x[19]||(x[19]=e("p",null,"请在左侧上传音频文件并选择处理模式",-1)),e("div",pa,[x[16]||(x[16]=e("h4",null,"支持的格式：",-1)),e("div",va,[(a(),r(pe,null,ge(ce,T=>t(u,{key:T,size:"small"},{default:c(()=>[B(i(T),1)]),_:2},1024)),64))])])])])):b("",!0)])])])])])}}},ba=ye(ga,[["__scopeId","data-v-8b51e866"]]);export{ba as default};
