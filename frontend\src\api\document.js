import request from '@/utils/request'

/**
 * 文档管理相关API
 */

// 获取任务进度
export function getTaskProgress(taskId) {
  return request({
    url: `/api/v1/documents/progress/${taskId}`,
    method: 'get'
  })
}

// 获取用户所有任务进度
export function getAllProgress() {
  return request({
    url: '/api/v1/documents/progress',
    method: 'get'
  })
}

// 上传文档
export function uploadDocument(formData) {
  return request({
    url: '/api/v1/documents/upload-file',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 上传文档到RAG
export function uploadDocumentToRAG(documentId) {
  return request({
    url: `/api/v1/documents/documents/${documentId}/upload-to-rag`,
    method: 'post'
  })
}

// 获取文档列表
export function getDocuments(params = {}) {
  return request({
    url: '/api/v1/documents/documents',
    method: 'get',
    params
  })
}

// 获取文档详情
export function getDocument(documentId) {
  return request({
    url: `/api/v1/documents/documents/${documentId}`,
    method: 'get'
  })
}

// 删除文档
export function deleteDocument(documentId) {
  return request({
    url: `/api/v1/documents/documents/${documentId}`,
    method: 'delete'
  })
}

// 获取文档节点
export function getDocumentSections(documentId, params = {}) {
  return request({
    url: `/api/v1/documents/documents/${documentId}/nodes`,
    method: 'get',
    params
  })
}

// 获取用户统计信息
export function getUserStatistics() {
  return request({
    url: '/api/v1/documents/user/statistics',
    method: 'get'
  })
}

// 上传文本
export function uploadText(data) {
  return request({
    url: '/api/v1/documents/upload-text',
    method: 'post',
    data
  })
}
