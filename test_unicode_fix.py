#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Unicode字符修复是否成功
"""

import sys
import os

print("=" * 50)
print("[START] 测试Unicode字符修复")
print("=" * 50)

try:
    # 测试导入关键模块
    print("[LOG] 测试导入backend.utils.audio.speech_recognition_core...")
    from backend.utils.audio.speech_recognition_core import SenseVoiceRecognizer
    print("[OK] speech_recognition_core导入成功")
    
    print("[LOG] 测试导入backend.core.database...")
    from backend.core.database import init_db
    print("[OK] database导入成功")
    
    print("[LOG] 测试导入backend.tasks.audio_processing_tasks...")
    from backend.tasks.audio_processing_tasks import meeting_transcription_task
    print("[OK] audio_processing_tasks导入成功")
    
    print("[LOG] 测试导入backend.utils.audio.speaker_recognition...")
    from backend.utils.audio.speaker_recognition import Speaker<PERSON><PERSON>ognizer
    print("[OK] speaker_recognition导入成功")
    
    print("=" * 50)
    print("[OK] 所有关键模块导入成功！Unicode修复生效")
    print("=" * 50)
    
except Exception as e:
    print(f"[ERROR] 导入失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
