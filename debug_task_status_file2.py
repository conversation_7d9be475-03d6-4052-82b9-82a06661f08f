#!/usr/bin/env python3
"""
调试任务状态 - 检查file_id=2的情况
"""

import redis
import sqlite3
from celery import Celery
import json
from datetime import datetime

def check_task_status():
    task_id = "meeting_transcription_a8c695b2c7d1"
    file_id = "2"  # 注意这里改为2
    
    print(f"=== 检查任务状态: {task_id} (file_id={file_id}) ===")
    print(f"检查时间: {datetime.now()}")
    print()
    
    # 1. 检查Redis状态
    print("1. Redis状态:")
    try:
        r = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        
        # 检查任务进度
        progress_key = f"task_progress:{task_id}"
        progress_data = r.hgetall(progress_key)
        if progress_data:
            print(f"   进度数据: {progress_data}")
        else:
            print("   ❌ 未找到进度数据")
            
        # 检查任务结果
        result_key = f"celery-task-meta-{task_id}"
        result_data = r.get(result_key)
        if result_data:
            result_json = json.loads(result_data)
            print(f"   结果状态: {result_json.get('status', 'N/A')}")
            print(f"   结果数据: {result_json.get('result', {}).get('task_type', 'N/A')}")
        else:
            print("   ❌ 未找到结果数据")
            
    except Exception as e:
        print(f"   ❌ Redis连接失败: {e}")
    
    print()
    
    # 2. 检查数据库状态
    print("2. 数据库状态:")
    try:
        conn = sqlite3.connect('data/speech_platform.db')
        cursor = conn.cursor()
        
        # 检查task_records表
        cursor.execute("""
            SELECT task_id, status, progress_percentage, progress_detail, created_at, updated_at
            FROM task_records 
            WHERE task_id = ?
        """, (task_id,))
        
        task_record = cursor.fetchone()
        if task_record:
            print(f"   任务ID: {task_record[0]}")
            print(f"   状态: {task_record[1]}")
            print(f"   进度: {task_record[2]}%")
            print(f"   详情: {task_record[3]}")
            print(f"   创建时间: {task_record[4]}")
            print(f"   更新时间: {task_record[5]}")
        else:
            print("   ❌ 未找到任务记录")
            
        # 检查audio_files表 (file_id=2)
        cursor.execute("""
            SELECT id, filename, status, created_at, updated_at
            FROM audio_files 
            WHERE id = ?
        """, (file_id,))
        
        audio_file = cursor.fetchone()
        if audio_file:
            print(f"   文件ID: {audio_file[0]}")
            print(f"   文件名: {audio_file[1]}")
            print(f"   文件状态: {audio_file[2]}")
            print(f"   创建时间: {audio_file[3]}")
            print(f"   更新时间: {audio_file[4]}")
        else:
            print(f"   ❌ 未找到文件记录 (file_id={file_id})")
            
        conn.close()
        
    except Exception as e:
        print(f"   ❌ 数据库连接失败: {e}")
    
    print()
    
    # 3. 检查Celery状态
    print("3. Celery状态:")
    try:
        app = Celery('backend')
        app.config_from_object('backend.core.celery_config')
        
        # 获取任务状态
        result = app.AsyncResult(task_id)
        print(f"   状态: {result.status}")
        print(f"   结果: {result.result if result.ready() else '未完成'}")
        
    except Exception as e:
        print(f"   ❌ Celery连接失败: {e}")

if __name__ == "__main__":
    check_task_status()
