#!/bin/bash
# 启动Celery Worker的Linux/Mac脚本

echo "启动Celery Worker进程..."

# 设置环境变量
export PYTHONPATH=$(pwd)

# 激活虚拟环境
source .venv/bin/activate

# 检查Redis是否运行
echo "检查Redis服务..."
if ! redis-cli ping > /dev/null 2>&1; then
    echo "警告: Redis服务未运行，请先启动Redis"
    echo "可以使用: redis-server"
    exit 1
fi

# 创建日志目录
mkdir -p logs/celery

# 启动文档处理Worker
echo "启动文档处理Worker..."
uv run celery -A backend.core.task_queue:celery_app worker \
    --loglevel=info \
    --hostname=document_worker@%h \
    --queues=document_processing \
    --concurrency=2 \
    --logfile=logs/celery/document_worker.log \
    --detach

# 启动向量化Worker
echo "启动向量化Worker..."
uv run celery -A backend.core.task_queue:celery_app worker \
    --loglevel=info \
    --hostname=vectorization_worker@%h \
    --queues=vectorization \
    --concurrency=1 \
    --logfile=logs/celery/vectorization_worker.log \
    --detach

# 启动OCR Worker
echo "启动OCR Worker..."
uv run celery -A backend.core.task_queue:celery_app worker \
    --loglevel=info \
    --hostname=ocr_worker@%h \
    --queues=ocr_processing \
    --concurrency=2 \
    --logfile=logs/celery/ocr_worker.log \
    --detach

# 启动默认Worker
echo "启动默认Worker..."
uv run celery -A backend.core.task_queue:celery_app worker \
    --loglevel=info \
    --hostname=default_worker@%h \
    --queues=default \
    --concurrency=1 \
    --logfile=logs/celery/default_worker.log \
    --detach

echo "所有Worker进程已启动！"
echo "查看Worker状态: uv run celery -A backend.core.task_queue:celery_app status"
echo "停止所有Worker: uv run celery -A backend.core.task_queue:celery_app control shutdown"
