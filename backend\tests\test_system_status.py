"""
系统状态检查和报告
生成优化后系统的完整状态报告
"""

import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def check_database_status():
    """检查数据库状态"""
    try:
        from backend.core.database import get_db_session
        from backend.models.task_models import TaskRecord
        
        db = get_db_session()
        try:
            # 尝试查询任务表
            count = db.query(TaskRecord).count()
            return {
                "status": "✅ 正常",
                "details": f"数据库连接正常，任务表中有 {count} 条记录"
            }
        finally:
            db.close()
            
    except Exception as e:
        return {
            "status": "❌ 异常",
            "details": f"数据库连接失败: {str(e)}"
        }


def check_redis_status():
    """检查Redis状态"""
    try:
        import redis
        redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        redis_client.ping()
        
        # 获取Redis信息
        info = redis_client.info()
        memory_used = info.get('used_memory_human', 'Unknown')
        
        return {
            "status": "✅ 正常",
            "details": f"Redis连接正常，内存使用: {memory_used}"
        }
        
    except Exception as e:
        return {
            "status": "⚠️ 未启动",
            "details": f"Redis连接失败: {str(e)}"
        }


def check_celery_status():
    """检查Celery状态"""
    try:
        from backend.core.task_queue import celery_app
        
        # 检查Worker状态
        inspect = celery_app.control.inspect()
        active_workers = inspect.active()
        
        if active_workers:
            worker_count = len(active_workers)
            total_tasks = sum(len(tasks) for tasks in active_workers.values())
            
            return {
                "status": "✅ 正常",
                "details": f"发现 {worker_count} 个活跃Worker，正在处理 {total_tasks} 个任务"
            }
        else:
            return {
                "status": "⚠️ 无Worker",
                "details": "Celery应用正常但没有活跃的Worker进程"
            }
            
    except Exception as e:
        return {
            "status": "❌ 异常",
            "details": f"Celery检查失败: {str(e)}"
        }


def check_system_resources():
    """检查系统资源"""
    try:
        from backend.services.resource_monitor import get_resource_monitor
        
        monitor = get_resource_monitor()
        metrics = monitor.get_current_metrics()
        health = monitor.check_resource_health(metrics)
        
        status_map = {
            "healthy": "✅ 健康",
            "warning": "⚠️ 警告", 
            "critical": "❌ 严重"
        }
        
        return {
            "status": status_map.get(health["overall"], "❓ 未知"),
            "details": f"CPU: {metrics.cpu_percent:.1f}%, 内存: {metrics.memory_percent:.1f}%, 磁盘: {metrics.disk_usage_percent:.1f}%"
        }
        
    except Exception as e:
        return {
            "status": "❌ 异常",
            "details": f"资源监控检查失败: {str(e)}"
        }


def check_api_endpoints():
    """检查API端点"""
    try:
        from backend.api.v1.endpoints import task_management, resource_management, error_management
        
        endpoints = [
            ("任务管理API", task_management.router),
            ("资源管理API", resource_management.router),
            ("错误管理API", error_management.router)
        ]
        
        loaded_count = len([ep for ep in endpoints if ep[1] is not None])
        
        return {
            "status": "✅ 正常",
            "details": f"成功加载 {loaded_count}/{len(endpoints)} 个API端点"
        }
        
    except Exception as e:
        return {
            "status": "❌ 异常",
            "details": f"API端点检查失败: {str(e)}"
        }


def check_services_status():
    """检查服务状态"""
    services = {}
    
    # 检查各个服务
    service_checks = [
        ("资源监控", "backend.services.resource_monitor"),
        ("并发控制", "backend.services.concurrency_control"),
        ("错误处理", "backend.services.error_handler"),
        ("超时控制", "backend.services.timeout_control"),
        ("任务持久化", "backend.services.task_persistence_service"),
        ("任务恢复", "backend.services.task_recovery_service"),
    ]
    
    for service_name, module_path in service_checks:
        try:
            __import__(module_path)
            services[service_name] = {
                "status": "✅ 正常",
                "details": "服务模块加载成功"
            }
        except Exception as e:
            services[service_name] = {
                "status": "❌ 异常",
                "details": f"模块加载失败: {str(e)}"
            }
    
    return services


def generate_system_report():
    """生成系统状态报告"""
    print("🔍 正在检查系统状态...\n")
    
    # 系统信息
    print("="*80)
    print("📊 语音处理智能平台 - 系统状态报告")
    print("="*80)
    print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Python版本: {sys.version.split()[0]}")
    print(f"项目路径: {project_root}")
    print()
    
    # 核心组件状态
    print("🔧 核心组件状态:")
    print("-" * 40)
    
    components = [
        ("数据库", check_database_status()),
        ("Redis缓存", check_redis_status()),
        ("Celery任务队列", check_celery_status()),
        ("系统资源", check_system_resources()),
        ("API端点", check_api_endpoints()),
    ]
    
    for name, status in components:
        print(f"{name:15} {status['status']}")
        print(f"{'':15} {status['details']}")
        print()
    
    # 服务模块状态
    print("🛠️ 服务模块状态:")
    print("-" * 40)
    
    services = check_services_status()
    for service_name, status in services.items():
        print(f"{service_name:15} {status['status']}")
        print(f"{'':15} {status['details']}")
        print()
    
    # 系统就绪状态评估
    print("🎯 系统就绪状态评估:")
    print("-" * 40)
    
    # 计算就绪分数
    ready_score = 0
    total_checks = len(components) + len(services)
    
    for _, status in components:
        if "✅" in status["status"]:
            ready_score += 1
        elif "⚠️" in status["status"]:
            ready_score += 0.5
    
    for _, status in services.items():
        if "✅" in status["status"]:
            ready_score += 1
        elif "⚠️" in status["status"]:
            ready_score += 0.5
    
    ready_percentage = (ready_score / total_checks) * 100
    
    if ready_percentage >= 90:
        ready_status = "🟢 完全就绪"
        ready_desc = "系统已完全就绪，可以投入生产使用"
    elif ready_percentage >= 70:
        ready_status = "🟡 基本就绪"
        ready_desc = "系统基本就绪，建议解决警告项后投入使用"
    elif ready_percentage >= 50:
        ready_status = "🟠 部分就绪"
        ready_desc = "系统部分功能可用，需要解决关键问题"
    else:
        ready_status = "🔴 未就绪"
        ready_desc = "系统存在严重问题，不建议投入使用"
    
    print(f"就绪状态: {ready_status}")
    print(f"就绪分数: {ready_percentage:.1f}% ({ready_score:.1f}/{total_checks})")
    print(f"状态描述: {ready_desc}")
    print()
    
    # 建议和下一步
    print("💡 建议和下一步:")
    print("-" * 40)
    
    suggestions = []
    
    # 检查Redis
    redis_status = check_redis_status()
    if "未启动" in redis_status["status"]:
        suggestions.append("🔧 启动Redis服务以启用完整的进度监控功能")
    
    # 检查Celery
    celery_status = check_celery_status()
    if "无Worker" in celery_status["status"]:
        suggestions.append("🔧 启动Celery Worker进程以处理异步任务")
    
    # 检查系统资源
    resource_status = check_system_resources()
    if "警告" in resource_status["status"] or "严重" in resource_status["status"]:
        suggestions.append("⚠️ 监控系统资源使用情况，考虑优化或扩容")
    
    if not suggestions:
        suggestions.append("✅ 系统状态良好，可以开始使用")
        suggestions.append("📝 建议定期运行此检查脚本监控系统状态")
        suggestions.append("🚀 可以开始测试文档处理和RAG功能")
    
    for suggestion in suggestions:
        print(f"  {suggestion}")
    
    print()
    print("="*80)
    print("📋 报告完成")
    print("="*80)
    
    return ready_percentage >= 70


def main():
    """主函数"""
    try:
        is_ready = generate_system_report()
        return 0 if is_ready else 1
    except Exception as e:
        print(f"❌ 生成系统报告时出错: {e}")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
