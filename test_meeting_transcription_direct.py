#!/usr/bin/env python3
"""
直接测试会议转录内部函数的文本清理功能
"""

import sys
import os
import time
import json
from pathlib import Path

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.tasks.audio_processing_tasks import _perform_meeting_transcription

def test_meeting_transcription_direct():
    """直接测试会议转录内部函数"""
    
    print("🧪 开始直接测试会议转录文本清理功能")
    print("=" * 80)
    
    # 测试音频文件
    test_audio_path = "resource/对话.mp3"
    if not os.path.exists(test_audio_path):
        print(f"❌ 测试音频文件不存在: {test_audio_path}")
        return False
    
    # 配置参数
    config = {
        'sensevoice_model_path': r'D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\models\model_dir\SenseVoiceSmall',
        'vad_model_path': r'D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\models\model_dir\speech_fsmn_vad_zh-cn-16k-common-pytorch',
        'speaker_model_path': r'D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\models\model_dir\speech_campplus_sv_zh-cn_16k-common'
    }
    
    # 进度回调函数
    def progress_callback(progress, message, status):
        print(f"📊 进度: {progress:.1f}% - {message} ({status})")
    
    try:
        print(f"🎵 音频文件: {test_audio_path}")
        print("开始执行会议转录...")
        
        # 直接调用内部函数
        result = _perform_meeting_transcription(
            file_path=test_audio_path,
            language="auto",
            output_format="text",
            include_timestamps=True,
            speaker_labeling=True,
            config=config,
            progress_callback=progress_callback,
            start_progress=0,
            end_progress=100
        )
        
        print("\n" + "=" * 80)
        print("📊 转录结果:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        # 检查结果中是否还有技术标记
        if result:
            # 检查主要文本
            main_text = result.get('text', '')
            print(f"\n📝 主要文本: {main_text}")
            
            # 检查是否包含技术标记
            technical_markers = ['<|zh|>', '<|SAD|>', '<|Speech|>', '<|withitn|>', '<|EMO_UNKNOWN|>', '<|NEUTRAL|>', '<|BGM|>']
            has_markers = any(marker in main_text for marker in technical_markers)
            
            if has_markers:
                print("❌ 主要文本仍包含技术标记")
                for marker in technical_markers:
                    if marker in main_text:
                        print(f"  - 发现标记: {marker}")
            else:
                print("✅ 主要文本已清理技术标记")
            
            # 检查语音片段
            speech_segments = result.get('speech_segments', [])
            print(f"\n🎤 语音片段数量: {len(speech_segments)}")
            
            segments_with_markers = 0
            for i, segment in enumerate(speech_segments):
                segment_text = segment.get('text', '')
                segment_has_markers = any(marker in segment_text for marker in technical_markers)
                
                if segment_has_markers:
                    segments_with_markers += 1
                    print(f"❌ 片段 {i+1} 包含技术标记: {segment_text}")
                else:
                    print(f"✅ 片段 {i+1} 已清理: {segment_text}")
            
            if segments_with_markers == 0:
                print("🎉 所有语音片段都已清理技术标记！")
                return True
            else:
                print(f"❌ {segments_with_markers} 个片段仍包含技术标记")
                return False
        else:
            print("❌ 转录失败，结果为空")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_meeting_transcription_direct()
    if success:
        print("\n🎉 会议转录文本清理测试通过！")
    else:
        print("\n❌ 会议转录文本清理测试失败！")
