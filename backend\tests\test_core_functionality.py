"""
核心功能测试（不依赖外部服务）
测试优化后系统的核心组件功能
"""

import sys
import os
import time
import tempfile
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def test_task_models():
    """测试任务模型"""
    print("🔧 测试任务模型...")
    
    try:
        from backend.models.task_models import TaskRecord, TaskStatus, TaskProgressLog
        
        # 测试枚举
        assert TaskStatus.PENDING == "PENDING"
        assert TaskStatus.STARTED == "STARTED"
        assert TaskStatus.SUCCESS == "SUCCESS"
        assert TaskStatus.FAILURE == "FAILURE"
        
        print("✅ 任务模型测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 任务模型测试失败: {e}")
        return False


def test_resource_monitor():
    """测试资源监控（不连接Redis）"""
    print("🔧 测试资源监控...")
    
    try:
        from backend.services.resource_monitor import ResourceMonitor, ResourceMetrics
        
        # 创建监控器实例（不连接Redis）
        monitor = ResourceMonitor(redis_url=None)
        
        # 获取当前资源指标
        metrics = monitor.get_current_metrics()
        
        # 验证指标
        assert isinstance(metrics, ResourceMetrics)
        assert 0 <= metrics.cpu_percent <= 100
        assert 0 <= metrics.memory_percent <= 100
        assert metrics.memory_available_mb > 0
        assert 0 <= metrics.disk_usage_percent <= 100
        assert metrics.disk_free_gb >= 0
        assert metrics.active_processes > 0
        
        # 测试健康检查
        health = monitor.check_resource_health(metrics)
        assert "overall" in health
        assert health["overall"] in ["healthy", "warning", "critical"]
        
        # 测试资源可用性检查
        available = monitor.is_resource_available(required_memory_mb=50)
        assert isinstance(available, bool)
        
        print(f"✅ 资源监控测试通过 - CPU: {metrics.cpu_percent:.1f}%, 内存: {metrics.memory_percent:.1f}%")
        return True
        
    except Exception as e:
        print(f"❌ 资源监控测试失败: {e}")
        return False


def test_concurrency_control():
    """测试并发控制（不连接Redis）"""
    print("🔧 测试并发控制...")
    
    try:
        from backend.services.concurrency_control import ConcurrencyController
        
        # 创建控制器实例（不连接Redis）
        controller = ConcurrencyController(redis_url=None)
        
        # 测试并发状态获取
        status = controller.get_current_concurrency()
        assert "total_active_tasks" in status
        assert "queue_counters" in status
        assert "user_counters" in status
        assert "limits" in status
        
        # 测试任务接受检查
        can_accept = controller.can_accept_task(
            user_id="test_user",
            queue_name="document_processing",
            required_memory_mb=100
        )
        assert "can_accept" in can_accept
        assert "reason" in can_accept
        assert isinstance(can_accept["can_accept"], bool)
        
        # 测试队列统计
        stats = controller.get_queue_statistics()
        assert "timestamp" in stats
        assert "queues" in stats
        assert "total" in stats
        
        print("✅ 并发控制测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 并发控制测试失败: {e}")
        return False


def test_error_handler():
    """测试错误处理"""
    print("🔧 测试错误处理...")
    
    try:
        from backend.services.error_handler import ErrorHandler, ErrorInfo, ErrorCategory, ErrorSeverity
        
        # 创建错误处理器
        handler = ErrorHandler()
        
        # 测试错误分类
        classification = handler.classify_error("MemoryError: out of memory", "MemoryError")
        assert "category" in classification
        assert "severity" in classification
        assert classification["category"] == ErrorCategory.RESOURCE
        
        # 测试超时错误分类
        timeout_classification = handler.classify_error("TimeoutError: operation timed out", "TimeoutError")
        assert timeout_classification["category"] == ErrorCategory.TIMEOUT
        
        # 测试网络错误分类
        network_classification = handler.classify_error("ConnectionError: connection refused", "ConnectionError")
        assert network_classification["category"] == ErrorCategory.NETWORK
        
        print("✅ 错误处理测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")
        return False


def test_timeout_control():
    """测试超时控制"""
    print("🔧 测试超时控制...")
    
    try:
        from backend.services.timeout_control import TimeoutController, TimeoutConfig
        
        # 创建超时控制器
        controller = TimeoutController()
        
        # 测试超时配置
        config = controller.get_timeout_config("document_processing")
        assert isinstance(config, TimeoutConfig)
        assert config.soft_timeout > 0
        assert config.hard_timeout > config.soft_timeout
        assert config.warning_threshold < config.soft_timeout
        
        # 测试配置更新
        new_config = TimeoutConfig(
            task_type="test",
            soft_timeout=120,
            hard_timeout=240,
            warning_threshold=90
        )
        controller.update_timeout_config("test", new_config)
        
        retrieved_config = controller.get_timeout_config("test")
        assert retrieved_config.soft_timeout == 120
        assert retrieved_config.hard_timeout == 240
        
        print("✅ 超时控制测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 超时控制测试失败: {e}")
        return False


def test_database_operations():
    """测试数据库操作（使用临时数据库）"""
    print("🔧 测试数据库操作...")
    
    try:
        from backend.models.task_models import Base, TaskRecord, TaskStatus
        from sqlalchemy import create_engine
        from sqlalchemy.orm import sessionmaker
        
        # 创建临时内存数据库
        engine = create_engine("sqlite:///:memory:", echo=False)
        Base.metadata.create_all(bind=engine)
        
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        try:
            # 创建测试任务记录
            task_record = TaskRecord(
                task_id="test_task_001",
                user_id="test_user",
                task_type="test",
                task_name="测试任务",
                status=TaskStatus.PENDING,
                task_metadata={"test": True}
            )
            
            db.add(task_record)
            db.commit()
            db.refresh(task_record)
            
            # 查询任务记录
            retrieved_task = db.query(TaskRecord).filter(TaskRecord.task_id == "test_task_001").first()
            assert retrieved_task is not None
            assert retrieved_task.task_name == "测试任务"
            assert retrieved_task.status == TaskStatus.PENDING
            
            # 更新任务状态
            retrieved_task.status = TaskStatus.STARTED
            db.commit()
            
            # 验证更新
            updated_task = db.query(TaskRecord).filter(TaskRecord.task_id == "test_task_001").first()
            assert updated_task.status == TaskStatus.STARTED
            
            print("✅ 数据库操作测试通过")
            return True
            
        finally:
            db.close()
        
    except Exception as e:
        print(f"❌ 数据库操作测试失败: {e}")
        return False


def test_api_imports():
    """测试API模块导入"""
    print("🔧 测试API模块导入...")
    
    try:
        # 测试API路由器导入
        from backend.api.v1.endpoints.task_management import router as task_router
        from backend.api.v1.endpoints.resource_management import router as resource_router
        from backend.api.v1.endpoints.error_management import router as error_router
        
        # 验证路由器对象
        assert task_router is not None
        assert resource_router is not None
        assert error_router is not None
        
        print("✅ API模块导入测试通过")
        return True
        
    except Exception as e:
        print(f"❌ API模块导入测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 开始核心功能测试...\n")
    
    tests = [
        ("任务模型", test_task_models),
        ("资源监控", test_resource_monitor),
        ("并发控制", test_concurrency_control),
        ("错误处理", test_error_handler),
        ("超时控制", test_timeout_control),
        ("数据库操作", test_database_operations),
        ("API模块导入", test_api_imports),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试出错: {e}")
            results.append((test_name, False))
        print()  # 空行分隔
    
    # 打印结果摘要
    print("="*60)
    print("核心功能测试结果:")
    print("="*60)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 个测试通过")
    print(f"成功率: {(passed/total*100):.1f}%")
    
    if passed == total:
        print("\n🎉 所有核心功能测试通过！")
        print("✅ 优化后的系统核心组件工作正常")
        print("✅ 可以进行下一步的集成测试")
        return 0
    else:
        print("\n⚠️ 部分核心功能测试失败")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
