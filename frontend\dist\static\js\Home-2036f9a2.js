import{_ as n,o,r as l,a as r,c as f,b as s,d as t,w as d,e as v,f as i}from"./index-2c134546.js";const c={class:"home-container"},u={class:"navbar"},p={class:"nav-content"},_={class:"nav-links"},g={class:"main-content"},m={class:"hero-section"},h={class:"hero-content"},b={class:"hero-actions"},k={__name:"Home",setup(x){return o(()=>{console.log("🏠 首页加载完成")}),(C,a)=>{const e=l("router-link");return r(),f("div",c,[s("nav",u,[s("div",p,[a[4]||(a[4]=s("div",{class:"nav-brand"},[s("div",{class:"brand-logo"},"🧠"),s("span",{class:"brand-text"},"RAG智能问答平台")],-1)),s("div",_,[t(e,{to:"/knowledge",class:"nav-link"},{default:d(()=>a[0]||(a[0]=[i("知识库")])),_:1,__:[0]}),t(e,{to:"/speech",class:"nav-link"},{default:d(()=>a[1]||(a[1]=[i("语音识别")])),_:1,__:[1]}),t(e,{to:"/dashboard",class:"nav-link"},{default:d(()=>a[2]||(a[2]=[i("控制台")])),_:1,__:[2]}),t(e,{to:"/login",class:"nav-link btn-outline"},{default:d(()=>a[3]||(a[3]=[i("登录")])),_:1,__:[3]})])])]),s("main",g,[s("section",m,[a[8]||(a[8]=v('<div class="hero-background" data-v-ef61a34a><div class="floating-shapes" data-v-ef61a34a><div class="shape shape-1" data-v-ef61a34a></div><div class="shape shape-2" data-v-ef61a34a></div><div class="shape shape-3" data-v-ef61a34a></div></div></div>',1)),s("div",h,[a[7]||(a[7]=s("h1",{class:"hero-title"},[s("span",{class:"title-main"},"本地知识库RAG系统"),s("span",{class:"title-sub gradient-text"},"让你能够通过音频形成文本，从文本中构建自己的知识库。")],-1)),s("div",b,[t(e,{to:"/knowledge",class:"btn-gradient"},{default:d(()=>a[5]||(a[5]=[i(" 🧠 开始智能问答 ")])),_:1,__:[5]}),t(e,{to:"/dashboard",class:"btn-outline"},{default:d(()=>a[6]||(a[6]=[i(" 📊 更多功能 ")])),_:1,__:[6]})])])]),a[9]||(a[9]=v('<section class="features-section" data-v-ef61a34a><div class="container" data-v-ef61a34a><div class="features-grid" data-v-ef61a34a><div class="feature-card gradient-border animate-fade-in-up" data-v-ef61a34a><div class="feature-content" data-v-ef61a34a><div class="feature-icon" data-v-ef61a34a><div class="icon-gradient" data-v-ef61a34a>🧠</div></div><h3 data-v-ef61a34a>前沿智能</h3><p data-v-ef61a34a>由定制化模型与前沿模型提供支撑，Cursor 既聪明又快速。</p></div><div class="feature-visual" data-v-ef61a34a><div class="visual-element visual-brain" data-v-ef61a34a></div></div></div><div class="feature-card gradient-border animate-fade-in-up" data-v-ef61a34a><div class="feature-content" data-v-ef61a34a><div class="feature-icon" data-v-ef61a34a><div class="icon-gradient" data-v-ef61a34a>⚡</div></div><h3 data-v-ef61a34a>熟悉的体验</h3><p data-v-ef61a34a>一键即可导入 VS Code 扩展、主题和快捷键绑定。</p></div><div class="feature-visual" data-v-ef61a34a><div class="visual-element visual-lightning" data-v-ef61a34a></div></div></div><div class="feature-card gradient-border animate-fade-in-up" data-v-ef61a34a><div class="feature-content" data-v-ef61a34a><div class="feature-icon" data-v-ef61a34a><div class="icon-gradient" data-v-ef61a34a>🔒</div></div><h3 data-v-ef61a34a>隐私选项</h3><p data-v-ef61a34a>启用隐私模式时，你的代码不会存储在我们的服务器。Cursor 已通过 SOC 2 认证。</p></div><div class="feature-visual" data-v-ef61a34a><div class="visual-element visual-security" data-v-ef61a34a></div></div></div></div></div></section><section class="tech-section" data-v-ef61a34a><div class="container" data-v-ef61a34a><h3 class="section-title" data-v-ef61a34a>技术栈</h3><div class="tech-grid" data-v-ef61a34a><div class="tech-item" data-v-ef61a34a><strong data-v-ef61a34a>前端框架</strong><span data-v-ef61a34a>Vue3 + Element Plus + Vite</span></div><div class="tech-item" data-v-ef61a34a><strong data-v-ef61a34a>后端服务</strong><span data-v-ef61a34a>FastAPI + Uvicorn</span></div><div class="tech-item" data-v-ef61a34a><strong data-v-ef61a34a>AI引擎</strong><span data-v-ef61a34a>LlamaIndex + Ollama</span></div><div class="tech-item" data-v-ef61a34a><strong data-v-ef61a34a>向量数据库</strong><span data-v-ef61a34a>ChromaDB + 嵌入模型</span></div><div class="tech-item" data-v-ef61a34a><strong data-v-ef61a34a>文档处理</strong><span data-v-ef61a34a>多格式解析 + 智能分块</span></div><div class="tech-item" data-v-ef61a34a><strong data-v-ef61a34a>流式响应</strong><span data-v-ef61a34a>EventSource + 实时更新</span></div></div></div></section>',2))]),a[10]||(a[10]=s("footer",{class:"footer"},[s("div",{class:"container"},[s("p",null,"© 2025 RAG智能问答平台. All rights reserved.")])],-1))])}}},A=n(k,[["__scopeId","data-v-ef61a34a"]]);export{A as default};
