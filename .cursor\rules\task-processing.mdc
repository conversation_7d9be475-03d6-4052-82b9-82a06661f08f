---
description: 
globs: 
alwaysApply: true
---
# 任务处理规范

## 后端任务队列架构

### Celery任务系统
- 任务队列：使用Celery + Redis
- 主配置文件：[backend/celery_app.py](mdc:backend/celery_app.py)
- 工作进程：[backend/celery_worker.py](mdc:backend/celery_worker.py)
- 任务启动：[backend/start_worker.py](mdc:backend/start_worker.py)

### 核心任务服务
参考以下服务实现：
- 任务队列控制：[backend/core/task_queue.py](mdc:backend/core/task_queue.py)
- 并发控制服务：[backend/services/concurrency_control.py](mdc:backend/services/concurrency_control.py)
- 超时控制服务：[backend/services/timeout_control.py](mdc:backend/services/timeout_control.py)
- 任务持久化：[backend/services/task_persistence_service.py](mdc:backend/services/task_persistence_service.py)
- 任务恢复服务：[backend/services/task_recovery_service.py](mdc:backend/services/task_recovery_service.py)

### 任务定义规范
```python
from celery import Celery
from backend.core.task_queue import task_queue
from backend.services.progress_service import ProgressService

@task_queue.task(bind=True, name="task_name")
def process_task(self, task_data: dict):
    """
    标准任务模板
    
    Args:
        task_data: 任务输入数据
        
    Returns:
        dict: 任务结果
    """
    try:
        # 初始化进度服务
        progress = ProgressService(task_id=self.request.id)
        progress.update(0, "任务开始")
        
        # 任务逻辑处理
        # ... 具体业务逻辑
        
        progress.update(100, "任务完成")
        return {"status": "success", "result": result}
        
    except Exception as e:
        progress.update(-1, f"任务失败: {str(e)}")
        raise self.retry(exc=e, countdown=60, max_retries=3)
```

### 进度监控系统
- 进度服务：[backend/services/progress_service.py](mdc:backend/services/progress_service.py)
- 增强进度服务：[backend/services/enhanced_progress_service.py](mdc:backend/services/enhanced_progress_service.py)
- WebSocket推送：[backend/api/websocket.py](mdc:backend/api/websocket.py)

### 任务类型定义
```python
# 语音处理任务
SPEECH_RECOGNITION = "speech.recognition"
SPEAKER_IDENTIFICATION = "speech.speaker_id"
AUDIO_PREPROCESSING = "audio.preprocessing"

# 文档处理任务
DOCUMENT_UPLOAD = "document.upload"
DOCUMENT_VECTORIZE = "document.vectorize"
DOCUMENT_PARSE = "document.parse"

# RAG任务
RAG_QUERY = "rag.query"
RAG_INDEX = "rag.index"
KNOWLEDGE_BASE_BUILD = "knowledge.build"
```

### 任务优先级管理
```python
from enum import IntEnum

class TaskPriority(IntEnum):
    LOW = 1
    NORMAL = 5
    HIGH = 8
    CRITICAL = 10

# 任务路由配置
CELERY_ROUTES = {
    'speech.*': {'queue': 'speech_queue', 'priority': TaskPriority.HIGH},
    'document.*': {'queue': 'document_queue', 'priority': TaskPriority.NORMAL},
    'rag.*': {'queue': 'rag_queue', 'priority': TaskPriority.NORMAL},
}
```

### 错误处理和重试机制
- 错误处理服务：[backend/services/error_handler.py](mdc:backend/services/error_handler.py)
- 自动重试：最大3次，指数退避策略
- 死信队列：处理失败任务
- 任务超时：设置合理的超时时间

### 资源管理
- 资源监控：[backend/services/resource_monitor.py](mdc:backend/services/resource_monitor.py)
- CPU/内存限制：防止资源耗尽
- 并发控制：限制同类任务并发数
- 队列长度监控：防止队列堆积

### 任务状态管理
```python
from enum import Enum

class TaskStatus(Enum):
    PENDING = "pending"      # 等待执行
    STARTED = "started"      # 开始执行
    PROGRESS = "progress"    # 执行中
    SUCCESS = "success"      # 执行成功
    FAILURE = "failure"      # 执行失败
    RETRY = "retry"          # 重试中
    REVOKED = "revoked"      # 已撤销
```

### WebSocket实时通信
- 任务进度实时推送
- 错误信息即时通知
- 系统状态监控
- 前端实时更新

### 任务管理API
参考任务管理端点：
- 任务管理：[backend/api/v1/endpoints/task_management.py](mdc:backend/api/v1/endpoints/task_management.py)
- 支持任务查询、取消、重试等操作
- 提供任务统计和监控接口

