print("🧪 开始基础测试...")

try:
    import sys
    import os
    print("✅ 基础模块导入成功")
    
    # 添加路径
    sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))
    print("✅ 路径添加成功")
    
    # 尝试导入监控组件
    print("尝试导入监控组件...")
    from utils.monitoring_components import ProcessingMonitor
    print("✅ ProcessingMonitor导入成功")
    
    # 创建监控器
    monitor = ProcessingMonitor("test")
    print("✅ 监控器创建成功")
    
    print("🎉 基础测试通过！监控组件工作正常")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc() 