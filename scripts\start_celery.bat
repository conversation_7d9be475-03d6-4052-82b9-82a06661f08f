@echo off
REM 启动Celery Worker的Windows批处理脚本

echo 启动Celery Worker进程...

REM 设置环境变量
set PYTHONPATH=%cd%

REM 激活虚拟环境
call .venv\Scripts\activate

REM 启动Redis服务器（如果需要）
echo 请确保Redis服务器已启动...

REM 启动文档处理Worker
echo 启动文档处理Worker...
start "Document Worker" cmd /k "uv run celery -A backend.core.task_queue:celery_app worker --loglevel=info --hostname=document_worker@%%h --queues=document_processing --concurrency=2"

REM 等待一下
timeout /t 2 /nobreak >nul

REM 启动向量化Worker
echo 启动向量化Worker...
start "Vectorization Worker" cmd /k "uv run celery -A backend.core.task_queue:celery_app worker --loglevel=info --hostname=vectorization_worker@%%h --queues=vectorization --concurrency=1"

REM 等待一下
timeout /t 2 /nobreak >nul

REM 启动OCR Worker
echo 启动OCR Worker...
start "OCR Worker" cmd /k "uv run celery -A backend.core.task_queue:celery_app worker --loglevel=info --hostname=ocr_worker@%%h --queues=ocr_processing --concurrency=2"

echo 所有Worker进程已启动！
echo 按任意键退出...
pause
