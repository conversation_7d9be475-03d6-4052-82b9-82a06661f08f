#!/usr/bin/env python3
"""
简单的文档管理API测试
"""

import requests
import json

# 测试配置
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

def test_document_api():
    """测试文档管理API"""
    print("🔍 测试文档管理API...")
    
    # 1. 测试健康检查
    print("\n1. 测试健康检查...")
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print(f"   响应: {response.json()}")
            print("   ✅ 后端服务正常")
        else:
            print("   ❌ 后端服务异常")
            return False
    except Exception as e:
        print(f"   ❌ 无法连接后端服务: {e}")
        return False
    
    # 2. 测试文档列表API（无认证）
    print("\n2. 测试文档列表API（无认证）...")
    try:
        response = requests.get(f"{API_BASE}/documents/documents", timeout=5)
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.text[:200]}...")
        
        if response.status_code == 403:
            print("   ✅ 正确返回403（需要认证）")
        elif response.status_code == 401:
            print("   ✅ 正确返回401（未授权）")
        else:
            print(f"   ⚠️ 意外的状态码: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
        return False
    
    # 3. 测试文档列表API（使用demo token）
    print("\n3. 测试文档列表API（使用demo token）...")
    try:
        headers = {"Authorization": "Bearer demo-token"}
        response = requests.get(f"{API_BASE}/documents/documents", headers=headers, timeout=5)
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.text[:200]}...")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ 成功获取文档列表")
            print(f"   文档数量: {data.get('total', 0)}")
        else:
            print(f"   ❌ 获取文档列表失败: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
        return False
    
    # 4. 测试文档节点API
    print("\n4. 测试文档节点API...")
    try:
        headers = {"Authorization": "Bearer demo-token"}
        response = requests.get(f"{API_BASE}/documents/documents/1/nodes", headers=headers, timeout=5)
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.text[:200]}...")
        
        if response.status_code == 200:
            print("   ✅ 文档节点API正常")
        elif response.status_code == 404:
            print("   ✅ 文档不存在（正常）")
        else:
            print(f"   ❌ 文档节点API异常: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
        return False
    
    # 5. 测试API文档
    print("\n5. 测试API文档...")
    try:
        response = requests.get(f"{BASE_URL}/docs", timeout=5)
        print(f"   状态码: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ API文档可访问")
        else:
            print(f"   ❌ API文档不可访问: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    return True

if __name__ == "__main__":
    print("🚀 开始测试文档管理API...")
    success = test_document_api()
    
    if success:
        print("\n🎉 测试完成！")
    else:
        print("\n❌ 测试失败！")
