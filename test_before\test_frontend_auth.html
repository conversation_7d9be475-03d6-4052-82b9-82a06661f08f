<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端认证测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 前端认证测试</h1>
        
        <div class="section info">
            <h3>📋 当前状态</h3>
            <div id="currentStatus">检查中...</div>
        </div>

        <div class="section">
            <h3>🔑 登录测试</h3>
            <div>
                <input type="text" id="username" placeholder="用户名" value="admin">
                <input type="password" id="password" placeholder="密码" value="admin123">
                <button onclick="testLogin()">登录</button>
            </div>
            <div id="loginResult"></div>
        </div>

        <div class="section">
            <h3>📄 文档API测试</h3>
            <button onclick="testDocumentAPI()">测试文档列表</button>
            <button onclick="testDocumentNodes()">测试文档节点</button>
            <div id="documentResult"></div>
        </div>

        <div class="section">
            <h3>🧹 清理操作</h3>
            <button onclick="clearAuth()">清除认证信息</button>
            <button onclick="checkStorage()">检查存储</button>
            <div id="storageResult"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000/api/v1';
        
        // 检查当前状态
        function checkCurrentStatus() {
            const token = getToken();
            const userInfo = getUserInfo();
            
            let status = '<strong>Token状态:</strong> ';
            if (token) {
                status += `✅ 已保存 (${token.substring(0, 20)}...)`;
            } else {
                status += '❌ 未找到';
            }
            
            status += '<br><strong>用户信息:</strong> ';
            if (userInfo) {
                status += `✅ ${userInfo.username || '未知用户'}`;
            } else {
                status += '❌ 未找到';
            }
            
            document.getElementById('currentStatus').innerHTML = status;
        }
        
        // Token管理函数
        function getToken() {
            return localStorage.getItem('speech_platform_token') || getCookie('speech_platform_token');
        }
        
        function setToken(token) {
            localStorage.setItem('speech_platform_token', token);
            setCookie('speech_platform_token', token, 7);
        }
        
        function removeToken() {
            localStorage.removeItem('speech_platform_token');
            deleteCookie('speech_platform_token');
        }
        
        function getUserInfo() {
            try {
                const userInfo = localStorage.getItem('speech_platform_user');
                return userInfo ? JSON.parse(userInfo) : null;
            } catch (error) {
                return null;
            }
        }
        
        function setUserInfo(userInfo) {
            localStorage.setItem('speech_platform_user', JSON.stringify(userInfo));
        }
        
        // Cookie操作函数
        function setCookie(name, value, days) {
            const expires = new Date();
            expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
            document.cookie = `${name}=${value};expires=${expires.toUTCString()};path=/`;
        }
        
        function getCookie(name) {
            const nameEQ = name + "=";
            const ca = document.cookie.split(';');
            for(let i = 0; i < ca.length; i++) {
                let c = ca[i];
                while (c.charAt(0) == ' ') c = c.substring(1, c.length);
                if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
            }
            return null;
        }
        
        function deleteCookie(name) {
            document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
        }
        
        // 登录测试
        async function testLogin() {
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('loginResult');
            
            try {
                resultDiv.innerHTML = '<div class="info">登录中...</div>';
                
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });
                
                const data = await response.json();
                
                if (response.ok && data.access_token) {
                    setToken(data.access_token);
                    setUserInfo(data.user);
                    
                    resultDiv.innerHTML = `
                        <div class="success">
                            <strong>✅ 登录成功!</strong><br>
                            用户: ${data.user.username}<br>
                            Token: ${data.access_token.substring(0, 30)}...
                        </div>
                    `;
                    checkCurrentStatus();
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <strong>❌ 登录失败:</strong><br>
                            ${data.detail || '未知错误'}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <strong>❌ 请求失败:</strong><br>
                        ${error.message}
                    </div>
                `;
            }
        }
        
        // 文档API测试
        async function testDocumentAPI() {
            const token = getToken();
            const resultDiv = document.getElementById('documentResult');
            
            if (!token) {
                resultDiv.innerHTML = '<div class="error">❌ 请先登录获取Token</div>';
                return;
            }
            
            try {
                resultDiv.innerHTML = '<div class="info">测试中...</div>';
                
                const response = await fetch(`${API_BASE}/documents/documents`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <strong>✅ 文档API测试成功!</strong><br>
                            文档数量: ${data.total || 0}<br>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <strong>❌ 文档API测试失败:</strong><br>
                            ${data.detail || '未知错误'}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <strong>❌ 请求失败:</strong><br>
                        ${error.message}
                    </div>
                `;
            }
        }
        
        // 文档节点测试
        async function testDocumentNodes() {
            const token = getToken();
            const resultDiv = document.getElementById('documentResult');
            
            if (!token) {
                resultDiv.innerHTML = '<div class="error">❌ 请先登录获取Token</div>';
                return;
            }
            
            try {
                const response = await fetch(`${API_BASE}/documents/documents/1/nodes`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <strong>✅ 文档节点API测试成功!</strong><br>
                            节点数量: ${data.sections ? data.sections.length : 0}<br>
                            <pre>${JSON.stringify(data, null, 2)}</pre>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <strong>❌ 文档节点API测试失败:</strong><br>
                            ${data.detail || '未知错误'}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div class="error">
                        <strong>❌ 请求失败:</strong><br>
                        ${error.message}
                    </div>
                `;
            }
        }
        
        // 清除认证信息
        function clearAuth() {
            removeToken();
            localStorage.removeItem('speech_platform_user');
            localStorage.removeItem('speech_platform_refresh_token');
            checkCurrentStatus();
            document.getElementById('storageResult').innerHTML = '<div class="success">✅ 认证信息已清除</div>';
        }
        
        // 检查存储
        function checkStorage() {
            const storage = {
                localStorage: {},
                cookies: {}
            };
            
            // 检查localStorage
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key.includes('speech_platform')) {
                    storage.localStorage[key] = localStorage.getItem(key);
                }
            }
            
            // 检查cookies
            const cookies = document.cookie.split(';');
            cookies.forEach(cookie => {
                const [name, value] = cookie.trim().split('=');
                if (name && name.includes('speech_platform')) {
                    storage.cookies[name] = value;
                }
            });
            
            document.getElementById('storageResult').innerHTML = `
                <div class="info">
                    <strong>📦 存储检查结果:</strong><br>
                    <pre>${JSON.stringify(storage, null, 2)}</pre>
                </div>
            `;
        }
        
        // 页面加载时检查状态
        window.onload = function() {
            checkCurrentStatus();
        };
    </script>
</body>
</html>
