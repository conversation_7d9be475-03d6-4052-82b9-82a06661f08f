#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试RAG服务状态管理功能
"""

import asyncio
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.services.rag_service import rag_service, RAGServiceStatus


async def test_rag_service_status():
    """测试RAG服务状态管理功能"""
    print("=== RAG服务状态管理测试 ===\n")
    
    try:
        # 1. 测试获取服务状态
        print("1. 测试获取服务状态...")
        status = await rag_service.get_service_status()
        print(f"   当前状态: {status.value}")
        print(f"   状态类型: {type(status)}")
        
        # 2. 测试确保服务就绪
        print("\n2. 测试确保服务就绪...")
        result = await rag_service.ensure_rag_service_ready()
        print(f"   成功: {result['success']}")
        print(f"   消息: {result['message']}")
        print(f"   状态: {result['status']}")
        print(f"   重试次数: {result['retry_count']}")
        
        # 3. 再次检查状态
        print("\n3. 再次检查服务状态...")
        status_after = await rag_service.get_service_status()
        print(f"   更新后状态: {status_after.value}")
        
        # 4. 测试文档添加保障机制
        if status_after == RAGServiceStatus.READY:
            print("\n4. 测试文档添加保障机制...")
            test_content = "这是一个测试文档，用于验证RAG服务的文档添加和检索功能。"
            test_metadata = {
                "filename": "test_document.txt",
                "file_type": "text",
                "test": True
            }
            
            add_result = await rag_service.add_document_with_guarantee(
                content=test_content,
                metadata=test_metadata,
                verify_after_add=True
            )
            
            print(f"   添加成功: {add_result['success']}")
            print(f"   消息: {add_result['message']}")
            print(f"   节点数量: {add_result['nodes_added']}")
            print(f"   验证通过: {add_result['verification_passed']}")
            
            if add_result.get('details', {}).get('verification'):
                verification = add_result['details']['verification']
                print(f"   验证详情: {verification}")
        else:
            print("\n4. 跳过文档添加测试（服务未就绪）")
        
        print("\n=== 测试完成 ===")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_service_components():
    """测试服务组件"""
    print("\n=== 服务组件测试 ===")
    
    try:
        # 检查各个组件的状态
        components = {
            "chroma_client": rag_service.chroma_client,
            "collection": rag_service.collection,
            "vector_store": rag_service.vector_store,
            "index": rag_service.index,
            "query_engine": rag_service.query_engine,
            "embedding_model": rag_service.embedding_model,
            "llm": rag_service.llm
        }
        
        for name, component in components.items():
            status = "✅ 已初始化" if component is not None else "❌ 未初始化"
            print(f"   {name}: {status}")
        
        # 如果有collection，显示文档数量
        if rag_service.collection:
            try:
                doc_count = rag_service.collection.count()
                print(f"   文档数量: {doc_count}")
            except Exception as e:
                print(f"   文档数量: 获取失败 ({e})")
        
        return True
        
    except Exception as e:
        print(f"❌ 组件测试失败: {e}")
        return False


async def main():
    """主测试函数"""
    print("开始RAG服务状态管理测试...\n")
    
    # 测试服务状态
    status_test = await test_rag_service_status()
    
    # 测试服务组件
    component_test = await test_service_components()
    
    # 总结
    print(f"\n=== 测试总结 ===")
    print(f"状态管理测试: {'✅ 通过' if status_test else '❌ 失败'}")
    print(f"组件测试: {'✅ 通过' if component_test else '❌ 失败'}")
    
    if status_test and component_test:
        print("🎉 所有测试通过！")
        return 0
    else:
        print("⚠️ 部分测试失败")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
