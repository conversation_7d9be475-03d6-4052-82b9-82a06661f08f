#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化的语音处理器
专注于CAM++向量计算和并行处理
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
current_dir = Path(__file__).parent
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

print("🧪 测试优化的语音处理器")
print("=" * 50)

# 测试基础导入
try:
    from utils.optimized_speech_processing import OptimizedSpeechProcessor
    print("✅ 优化处理器导入成功")
except ImportError as e:
    print(f"❌ 优化处理器导入失败: {e}")
    sys.exit(1)

# 设置模型路径（根据您的环境调整）
CAMPPLUS_MODEL_PATH = r"C:\Users\<USER>\Documents\my_project\models\model_dir\speech_campplus_sv_zh_en_16k-common_advanced"
VAD_MODEL_PATH = r"C:\Users\<USER>\Documents\my_project\models\model_dir\speech_fsmn_vad_zh-cn-16k-common-pytorch"

print(f"\n📁 模型路径检查:")
print(f"  - CAM++模型: {os.path.exists(CAMPPLUS_MODEL_PATH)} ({CAMPPLUS_MODEL_PATH})")
print(f"  - VAD模型: {os.path.exists(VAD_MODEL_PATH)} ({VAD_MODEL_PATH})")

if not os.path.exists(CAMPPLUS_MODEL_PATH):
    print("❌ CAM++模型路径不存在，请检查路径设置")
    sys.exit(1)

if not os.path.exists(VAD_MODEL_PATH):
    print("❌ VAD模型路径不存在，请检查路径设置")
    sys.exit(1)

# 创建优化处理器实例
try:
    print(f"\n🚀 创建优化处理器实例...")
    processor = OptimizedSpeechProcessor(
        campplus_model_path=CAMPPLUS_MODEL_PATH,
        vad_model_path=VAD_MODEL_PATH,
        use_gpu=False  # 使用CPU进行测试
    )
    print("✅ 优化处理器创建成功")
except Exception as e:
    print(f"❌ 优化处理器创建失败: {e}")
    sys.exit(1)

# 测试模型加载（懒加载）
try:
    print(f"\n🔧 测试模型懒加载...")
    
    # 测试CAM++模型加载
    print("  - 测试CAM++模型加载...")
    campplus_model = processor.campplus_model
    if campplus_model is not None:
        print("  ✅ CAM++模型加载成功")
    else:
        print("  ❌ CAM++模型加载失败")
    
    # 测试VAD模型加载
    print("  - 测试VAD模型加载...")
    vad_model = processor.vad_model
    if vad_model is not None:
        print("  ✅ VAD模型加载成功")
    else:
        print("  ❌ VAD模型加载失败")
        
except Exception as e:
    print(f"❌ 模型加载测试失败: {e}")

# 测试缓存功能
try:
    print(f"\n💾 测试缓存功能...")
    cache_info = processor.get_cache_info()
    print(f"  - 缓存大小: {cache_info['cache_size']} 个向量")
    print(f"  - 内存使用: {cache_info['memory_usage_mb']:.2f}MB")
    print("✅ 缓存功能正常")
except Exception as e:
    print(f"❌ 缓存功能测试失败: {e}")

# 如果有测试音频文件，可以进行完整测试
test_audio_path = "test_audio.wav"  # 您可以提供一个测试音频文件
if os.path.exists(test_audio_path):
    try:
        print(f"\n🎙️ 测试完整音频处理流程...")
        result = processor.process_audio_file(
            audio_file_path=test_audio_path,
            num_speakers=None,  # 自动检测
            clustering_threshold=0.15
        )
        
        if result["success"]:
            print(f"✅ 音频处理成功!")
            print(f"  - 处理时间: {result['processing_time']:.2f}s")
            print(f"  - 检测到说话人数: {result['num_speakers']}")
            print(f"  - 音频片段数: {len(result['segments'])}")
            print(f"  - 缓存命中: {result['cache_hits']}")
        else:
            print(f"❌ 音频处理失败: {result['error']}")
            
    except Exception as e:
        print(f"❌ 完整流程测试失败: {e}")
else:
    print(f"\n⚠️ 未找到测试音频文件 ({test_audio_path})，跳过完整流程测试")
    print("💡 提示: 您可以提供一个测试音频文件来进行完整的功能测试")

print(f"\n🎉 优化处理器测试完成!")
print("=" * 50)

# 显示优化特性总结
print("\n📊 优化特性总结:")
print("✅ 删除了所有Paraformer相关代码，专注于CAM++")
print("✅ 实现了并行线程处理，提高处理速度")
print("✅ 添加了嵌入向量缓存，避免重复计算")
print("✅ 优化了VAD分割和聚类流程")
print("✅ 模型只加载一次，提高效率")
print("✅ 支持自动说话人数量检测")
print("✅ 提供详细的处理统计信息")

print("\n💡 使用建议:")
print("1. 首次运行时模型会自动加载，后续使用会更快")
print("2. 相同音频片段会使用缓存，大幅提升速度")
print("3. 并行处理充分利用多核CPU，提高效率")
print("4. 可根据需要调整聚类阈值和线程数") 