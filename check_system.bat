@echo off
echo ========================================
echo 语音处理智能平台 - 系统检查脚本
echo ========================================
echo.

:: 检查虚拟环境
if not exist ".venv\Scripts\activate.bat" (
    echo ❌ 虚拟环境不存在，请先运行 setup.bat
    goto :end
)

:: 激活虚拟环境
call .venv\Scripts\activate.bat

echo 🔍 检查系统状态...
echo.

:: 检查端口占用
echo 📡 检查端口状态:
netstat -an | findstr :8000 >nul
if %errorlevel% equ 0 (
    echo   ✅ 后端服务 (8000) 正在运行
) else (
    echo   ❌ 后端服务 (8000) 未运行
)

netstat -an | findstr :3000 >nul
if %errorlevel% equ 0 (
    echo   ✅ 前端服务 (3000) 正在运行
) else (
    echo   ❌ 前端服务 (3000) 未运行
)

netstat -an | findstr :6379 >nul
if %errorlevel% equ 0 (
    echo   ✅ Redis 服务 (6379) 正在运行
) else (
    echo   ❌ Redis 服务 (6379) 未运行
)

echo.

:: 检查 Docker 容器
echo 🐳 检查 Docker 容器:
docker ps | findstr redis-server >nul
if %errorlevel% equ 0 (
    echo   ✅ Redis 容器正在运行
) else (
    echo   ❌ Redis 容器未运行
)

echo.

:: 检查进程
echo 🔄 检查关键进程:
tasklist | findstr python.exe >nul
if %errorlevel% equ 0 (
    echo   ✅ Python 进程正在运行
) else (
    echo   ❌ Python 进程未运行
)

tasklist | findstr node.exe >nul
if %errorlevel% equ 0 (
    echo   ✅ Node.js 进程正在运行
) else (
    echo   ❌ Node.js 进程未运行
)

echo.

:: 检查文件和目录
echo 📁 检查关键文件和目录:
if exist "backend\main.py" (
    echo   ✅ 后端主文件存在
) else (
    echo   ❌ 后端主文件缺失
)

if exist "frontend\package.json" (
    echo   ✅ 前端配置文件存在
) else (
    echo   ❌ 前端配置文件缺失
)

if exist "backend\.env" (
    echo   ✅ 环境配置文件存在
) else (
    echo   ❌ 环境配置文件缺失
)

if exist "backend\data\uploads" (
    echo   ✅ 上传目录存在
) else (
    echo   ❌ 上传目录缺失
)

echo.

:: 运行系统测试
echo 🧪 运行系统测试...
cd backend
python tests\test_optimized_system.py
if %errorlevel% equ 0 (
    echo   ✅ 系统测试通过
) else (
    echo   ❌ 系统测试失败
)
cd ..

echo.

:: 检查磁盘空间
echo 💾 检查磁盘空间:
for /f "tokens=3" %%a in ('dir /-c ^| findstr "bytes free"') do (
    echo   可用空间: %%a bytes
)

echo.

:: 检查内存使用
echo 🧠 检查内存使用:
wmic OS get TotalVisibleMemorySize,FreePhysicalMemory /format:list | findstr "="

echo.

:end
echo ========================================
echo 🔍 系统检查完成
echo ========================================
echo.
echo 💡 提示:
echo   - 如果发现问题，请参考 PROJECT_STARTUP_GUIDE.md
echo   - 红色 ❌ 表示需要处理的问题
echo   - 绿色 ✅ 表示正常状态
echo.
pause
