#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成测试脚本 - 测试修改后的语音识别工具函数
"""

import os
import sys

# 模拟streamlit环境
class MockStreamlit:
    def info(self, msg):
        print(f"ℹ️ {msg}")
    
    def success(self, msg):
        print(f"✅ {msg}")
    
    def warning(self, msg):
        print(f"⚠️ {msg}")
    
    def error(self, msg):
        print(f"❌ {msg}")

# 在导入utils之前设置mock
sys.modules['streamlit'] = MockStreamlit()
import streamlit as st

def set_offline_mode():
    """设置离线模式环境变量"""
    os.environ['MODELSCOPE_OFFLINE_MODE'] = '1'
    os.environ['HF_HUB_OFFLINE'] = '1'
    os.environ['HF_DATASETS_OFFLINE'] = '1'
    os.environ['TRANSFORMERS_OFFLINE'] = '1'
    os.environ['DISABLE_MODEL_DOWNLOAD'] = '1'
    os.environ['NO_PROXY'] = '*'
    os.environ['REQUESTS_CA_BUNDLE'] = ''
    print("🔒 离线模式已启用")

def test_sensevoice_integration():
    """测试SenseVoice集成加载"""
    print("\n" + "="*60)
    print("🎙️ 测试SenseVoice集成加载")
    print("="*60)
    
    # 设置离线模式
    set_offline_mode()
    
    # 导入修改后的函数
    try:
        from utils.speech_recognition_utils import load_sensevoice_model_fixed
        print("✅ 成功导入 load_sensevoice_model_fixed")
    except ImportError as e:
        print(f"❌ 导入失败: {str(e)}")
        return False
    
    # 测试模型路径
    model_path = r"C:\Users\<USER>\Documents\my_project\models\model_dir\SenseVoiceSmall"
    device = "cpu"
    
    if not os.path.exists(model_path):
        print(f"❌ 模型路径不存在: {model_path}")
        return False
    
    # 尝试加载模型
    try:
        model = load_sensevoice_model_fixed(model_path, device)
        if model is not None:
            print("🎉 SenseVoice集成测试成功！")
            return True
        else:
            print("❌ SenseVoice集成测试失败 - 模型为None")
            return False
    except Exception as e:
        print(f"❌ SenseVoice集成测试异常: {str(e)}")
        return False

def test_paraformer_integration():
    """测试Paraformer集成加载"""
    print("\n" + "="*60)
    print("🎙️ 测试Paraformer集成加载")
    print("="*60)
    
    # 设置离线模式
    set_offline_mode()
    
    # 导入修改后的函数
    try:
        from utils.speech_recognition_utils import load_paraformer_model_fixed
        print("✅ 成功导入 load_paraformer_model_fixed")
    except ImportError as e:
        print(f"❌ 导入失败: {str(e)}")
        return False
    
    # 测试模型路径 - 使用可能存在的路径
    possible_paths = [
        r"C:\Users\<USER>\Documents\my_project\models\model_dir\ParaformerLarge",
        r"C:\Users\<USER>\Documents\my_project\models\model_dir\Paraformer",
        r"C:\Users\<USER>\Documents\my_project\models\model_dir\paraformer",
    ]
    
    model_path = None
    for path in possible_paths:
        if os.path.exists(path):
            model_path = path
            break
    
    if model_path is None:
        print("❌ 未找到Paraformer模型路径，跳过测试")
        print("💡 可用的模型路径:")
        for path in possible_paths:
            print(f"   - {path}")
        return False
    
    device = "cpu"
    
    # 尝试加载模型
    try:
        model = load_paraformer_model_fixed(model_path, device)
        if model is not None:
            print("🎉 Paraformer集成测试成功！")
            return True
        else:
            print("❌ Paraformer集成测试失败 - 模型为None")
            return False
    except Exception as e:
        print(f"❌ Paraformer集成测试异常: {str(e)}")
        return False

def main():
    """主函数"""
    print("🧪 语音识别工具集成测试")
    print("="*60)
    
    # 测试结果
    results = []
    
    # 测试SenseVoice
    sensevoice_result = test_sensevoice_integration()
    results.append(("SenseVoice", sensevoice_result))
    
    # 测试Paraformer
    paraformer_result = test_paraformer_integration()
    results.append(("Paraformer", paraformer_result))
    
    # 总结结果
    print("\n" + "="*60)
    print("📊 测试结果总结")
    print("="*60)
    
    success_count = 0
    for model_name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"{model_name}: {status}")
        if result:
            success_count += 1
    
    print(f"\n总计: {success_count}/{len(results)} 个测试通过")
    
    if success_count == len(results):
        print("🎉 所有集成测试通过！")
    elif success_count > 0:
        print("⚠️ 部分集成测试通过")
    else:
        print("❌ 所有集成测试失败")
    
    return success_count > 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 