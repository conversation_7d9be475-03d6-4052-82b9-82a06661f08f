# 语音处理智能平台 - 项目启动指导

## 📋 项目概述

这是一个基于 Vue.js + Python FastAPI 的语音处理智能平台，集成了以下功能：
- 🎤 语音识别与转录
- 👥 说话人识别与分离
- 📄 文档处理与 RAG 知识库
- 🔍 智能检索与问答
- 📊 任务队列与进度监控

## 🛠️ 环境要求

### 系统要求
- Windows 10/11
- Python 3.11+
- Node.js 16+
- Docker (可选)
- Redis 服务

### 硬件要求
- 内存: 8GB+ (推荐 16GB+)
- 存储: 10GB+ 可用空间
- GPU: 支持 CUDA (可选，用于加速 AI 模型)

## 🚀 快速启动

### 1. 环境准备

#### 1.1 安装 Python 虚拟环境管理器 (uv)
```bash
# 如果还没有安装 uv
pip install uv
```

#### 1.2 创建并激活虚拟环境
```bash
# 在项目根目录
uv venv .venv
.venv\Scripts\activate  # Windows
```

#### 1.3 安装 Python 依赖
```bash
# 安装后端依赖
cd backend
uv pip install -r requirements.txt
cd ..
```

#### 1.4 安装前端依赖
```bash
# 安装前端依赖
cd frontend
npm install
cd ..
```

### 2. 服务启动

#### 2.1 启动 Redis 服务
```bash
# 使用 Docker 启动 Redis (推荐)
docker run -d --name redis-server -p 6379:6379 redis:latest

# 或者使用本地 Redis 服务
# 确保 Redis 在 localhost:6379 运行
```

#### 2.2 启动后端服务

**方式一：使用 uv 启动 (推荐)**
```bash
cd backend
uv run python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

**方式二：在虚拟环境中启动**
```bash
.venv\Scripts\activate
cd backend
python -m uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

#### 2.3 启动 Celery Worker (任务队列)
```bash
# 新开一个终端窗口
.venv\Scripts\activate
python start_worker.py
```

#### 2.4 启动前端服务
```bash
# 新开一个终端窗口
cd frontend
npm run dev
```

### 3. 访问应用

- **前端界面**: http://localhost:3000
- **后端 API**: http://localhost:8000
- **API 文档**: http://localhost:8000/docs
- **Redis**: localhost:6379

## 📁 项目结构

```
my_notebook_version_0.1.0/
├── backend/                 # 后端服务
│   ├── api/                # API 路由
│   ├── core/               # 核心模块
│   ├── models/             # 数据模型
│   ├── services/           # 业务服务
│   ├── tasks/              # 异步任务
│   ├── utils/              # 工具函数
│   ├── tests/              # 测试文件
│   └── main.py             # 应用入口
├── frontend/               # 前端应用
│   ├── src/                # 源代码
│   ├── public/             # 静态资源
│   └── package.json        # 依赖配置
├── models/                 # AI 模型文件
└── data/                   # 数据文件
```

## 🔧 配置说明

### 环境变量配置

创建 `backend/.env` 文件：
```env
# 数据库配置
DATABASE_URL=sqlite:///./speech_platform.db

# Redis 配置
REDIS_URL=redis://localhost:6379/0
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# 安全配置
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# 模型路径配置
MODEL_BASE_PATH=../models
EMBEDDING_MODEL_PATH=../models/embedding
RERANKER_MODEL_PATH=../models/Qwen3-Reranker-0.6B

# 文件上传配置
MAX_FILE_SIZE=100MB
UPLOAD_DIR=./data/uploads
```

### 模型配置

确保以下模型文件存在：
- 嵌入模型: `models/embedding/`
- 重排序模型: `models/Qwen3-Reranker-0.6B/`
- 语音识别模型: 通过 FunASR 自动下载

## 🧪 测试系统

### 运行系统测试
```bash
# 确保 Redis 和后端服务已启动
cd backend
uv run python tests/test_optimized_system.py
```

### 运行单元测试
```bash
cd backend
uv run pytest tests/ -v
```

## 📚 功能使用指南

### 1. 用户认证
- 访问前端页面，使用姓名全拼注册/登录
- 系统支持基于姓名的用户识别

### 2. 文档管理
- 上传 PDF、DOC、TXT 等文档
- 自动 OCR 识别和文档分块
- 向量化存储到知识库

### 3. RAG 问答
- 基于上传的文档进行智能问答
- 支持流式响应和引用显示
- 可配置不同的 AI 模型

### 4. 语音处理
- 语音识别和转录
- 说话人识别和分离
- 会议语音处理

## 🐛 常见问题

### Q1: Redis 连接失败
**解决方案**: 
- 确保 Redis 服务正在运行
- 检查端口 6379 是否被占用
- 验证防火墙设置

### Q2: Celery Worker 无法启动
**解决方案**:
- 确保已安装 celery: `uv pip install celery==5.4.0`
- 检查 Redis 连接
- 验证任务模块导入路径

### Q3: 前端无法连接后端
**解决方案**:
- 确认后端服务在 8000 端口运行
- 检查 CORS 配置
- 验证网络连接

### Q4: 模型加载失败
**解决方案**:
- 检查模型文件路径
- 确认模型文件完整性
- 验证内存是否充足

## 🔧 高级配置

### 性能优化配置

#### Celery 配置优化
```python
# backend/core/task_queue.py 中的配置
CELERY_CONFIG = {
    'worker_concurrency': 4,  # 根据 CPU 核心数调整
    'task_soft_time_limit': 300,  # 5分钟软超时
    'task_time_limit': 600,       # 10分钟硬超时
    'worker_prefetch_multiplier': 1,  # 预取任务数
}
```

#### Redis 配置优化
```bash
# Redis 内存配置
redis-cli config set maxmemory 2gb
redis-cli config set maxmemory-policy allkeys-lru
```

### 生产环境部署

#### 使用 Docker Compose
```yaml
# docker-compose.yml
version: '3.8'
services:
  redis:
    image: redis:latest
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  backend:
    build: ./backend
    ports:
      - "8000:8000"
    depends_on:
      - redis
    environment:
      - REDIS_URL=redis://redis:6379/0

  worker:
    build: ./backend
    command: celery -A core.task_queue worker --loglevel=info
    depends_on:
      - redis
    environment:
      - REDIS_URL=redis://redis:6379/0

volumes:
  redis_data:
```

## 📊 监控和维护

### 系统监控
```bash
# 检查系统状态
cd backend
uv run python tests/test_optimized_system.py

# 查看 Celery 任务状态
celery -A core.task_queue inspect active

# 查看 Redis 状态
redis-cli info
```

### 日志管理
- 应用日志: `backend/logs/app.log`
- 错误日志: `backend/logs/error.log`
- 任务日志: `backend/logs/celery.log`

### 数据备份
```bash
# 备份 SQLite 数据库
cp backend/speech_platform.db backup/speech_platform_$(date +%Y%m%d).db

# 备份上传文件
tar -czf backup/uploads_$(date +%Y%m%d).tar.gz backend/data/uploads/

# 备份向量数据库
tar -czf backup/chroma_$(date +%Y%m%d).tar.gz backend/data/chroma_db/
```

## 📞 技术支持

### 故障排除步骤
1. **检查服务状态**
   ```bash
   # 检查端口占用
   netstat -an | findstr :8000
   netstat -an | findstr :6379
   netstat -an | findstr :3000
   ```

2. **查看日志文件**
   ```bash
   # 查看最新日志
   tail -f backend/logs/app.log
   ```

3. **运行系统测试**
   ```bash
   cd backend
   uv run python tests/test_optimized_system.py
   ```

4. **重启服务**
   ```bash
   # 重启所有服务
   docker-compose down && docker-compose up -d
   ```

### 联系方式
- 项目文档: 查看各模块的 README.md
- 测试报告: `backend/tests/test_summary.md`
- 系统状态: 运行测试脚本获取详细状态

## 🔄 更新说明

### 版本 0.1.0 特性
- ✅ 基础架构搭建完成
- ✅ 任务队列系统优化
- ✅ 进度监控实时更新
- ✅ 并发控制机制
- ✅ 错误处理完善
- ✅ 资源监控功能
- ✅ Redis 集成完成
- ✅ Celery Worker 正常运行
- ✅ 文档处理流程优化

### 下一版本计划
- 🔄 语音识别功能集成
- 🔄 说话人识别优化
- 🔄 前端界面美化
- 🔄 性能监控增强

---

**祝您使用愉快！** 🎉

> 💡 **提示**: 首次启动可能需要下载 AI 模型，请确保网络连接稳定。
