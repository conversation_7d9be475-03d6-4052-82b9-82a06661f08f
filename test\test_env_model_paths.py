#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试.env文件中的模型路径配置
验证配置系统是否正确读取环境变量中的模型路径
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def test_env_file_exists():
    """测试.env文件是否存在"""
    print("🧪 测试.env文件是否存在...")
    
    env_file = project_root / ".env"
    assert env_file.exists(), f"❌ .env文件不存在: {env_file}"
    print(f"✅ .env文件存在: {env_file}")
    return True


def test_model_paths_in_env():
    """测试.env文件中的模型路径配置"""
    print("\n🧪 测试.env文件中的模型路径配置...")
    
    env_file = project_root / ".env"
    
    # 读取.env文件内容
    with open(env_file, 'r', encoding='utf-8') as f:
        env_content = f.read()
    
    # 检查必需的模型路径配置
    required_paths = [
        'MODELS_BASE_PATH',
        'SENSEVOICE_MODEL_PATH', 
        'VAD_MODEL_PATH',
        'SPEAKER_MODEL_PATH',
        'ASR_MODEL_PATH'
    ]
    
    found_paths = 0
    for path_var in required_paths:
        if path_var in env_content:
            print(f"✅ 找到配置: {path_var}")
            found_paths += 1
        else:
            print(f"❌ 缺少配置: {path_var}")
    
    print(f"📊 模型路径配置: {found_paths}/{len(required_paths)} 个")
    return found_paths >= len(required_paths) - 1  # 允许缺少1个


def test_config_loading():
    """测试配置系统是否正确加载模型路径"""
    print("\n🧪 测试配置系统加载模型路径...")
    
    try:
        from backend.core.config import settings, get_model_path
        
        # 测试各种模型路径
        model_types = ['sensevoice', 'vad', 'speaker', 'asr']
        
        loaded_paths = 0
        for model_type in model_types:
            try:
                model_path = get_model_path(model_type)
                print(f"✅ {model_type}: {model_path}")
                loaded_paths += 1
            except Exception as e:
                print(f"❌ {model_type}: {e}")
        
        print(f"📊 模型路径加载: {loaded_paths}/{len(model_types)} 个成功")
        return loaded_paths >= len(model_types) - 1  # 允许1个失败
        
    except Exception as e:
        print(f"❌ 配置系统加载失败: {e}")
        return False


def test_model_paths_exist():
    """测试模型路径是否实际存在"""
    print("\n🧪 测试模型路径是否实际存在...")
    
    try:
        from backend.core.config import settings
        
        # 检查模型基础路径
        models_base = Path(settings.MODELS_BASE_PATH)
        if models_base.exists():
            print(f"✅ 模型基础路径存在: {models_base}")
        else:
            print(f"❌ 模型基础路径不存在: {models_base}")
            return False
        
        # 检查各个模型路径
        model_paths = {
            'SenseVoice': settings.SENSEVOICE_MODEL_PATH,
            'VAD': settings.VAD_MODEL_PATH,
            'Speaker': settings.SPEAKER_MODEL_PATH,
            'ASR': settings.ASR_MODEL_PATH
        }
        
        existing_paths = 0
        for name, path in model_paths.items():
            model_path = Path(path)
            if model_path.exists():
                print(f"✅ {name}模型路径存在: {model_path}")
                existing_paths += 1
            else:
                print(f"⚠️ {name}模型路径不存在: {model_path}")
        
        print(f"📊 模型路径存在性: {existing_paths}/{len(model_paths)} 个存在")
        return existing_paths >= 2  # 至少2个模型路径存在
        
    except Exception as e:
        print(f"❌ 模型路径检查失败: {e}")
        return False


def test_offline_config_with_real_paths():
    """测试使用真实路径的离线配置"""
    print("\n🧪 测试使用真实路径的离线配置...")
    
    try:
        from backend.utils.audio.optimized_funasr_manager import OptimizedFunASRManager
        from backend.core.config import settings
        
        # 创建管理器实例
        manager = OptimizedFunASRManager()
        
        # 使用真实的模型路径
        model_path = str(settings.SENSEVOICE_MODEL_PATH)
        device = 'cpu'
        
        # 测试配置优化
        optimized_config = manager._create_optimized_config(model_path, device)
        
        # 验证离线参数
        offline_params = {
            'local_files_only': True,
            'offline': True,
            'use_auth_token': False,
            'force_download': False
        }
        
        correct_params = 0
        for param, expected in offline_params.items():
            actual = optimized_config.get(param)
            if actual == expected:
                print(f"✅ {param} = {actual}")
                correct_params += 1
            else:
                print(f"❌ {param} = {actual} (期望: {expected})")
        
        print(f"📊 离线参数配置: {correct_params}/{len(offline_params)} 个正确")
        return correct_params >= len(offline_params) - 1  # 允许1个参数不正确
        
    except Exception as e:
        print(f"❌ 离线配置测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🔧 开始测试.env模型路径配置")
    print("=" * 60)
    
    tests = [
        (".env文件存在性", test_env_file_exists),
        ("模型路径配置", test_model_paths_in_env),
        ("配置系统加载", test_config_loading),
        ("模型路径存在性", test_model_paths_exist),
        ("离线配置测试", test_offline_config_with_real_paths)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 执行测试: {test_name}")
            result = test_func()
            if result:
                print(f"✅ {test_name} 通过")
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print(f"\n📊 测试结果汇总:")
    print("=" * 60)
    print(f"通过: {passed}/{total} 个测试")
    
    if passed >= total - 1:  # 允许1个测试失败
        print("🎉 .env模型路径配置验证基本通过！")
        return True
    else:
        print("❌ .env模型路径配置验证失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
