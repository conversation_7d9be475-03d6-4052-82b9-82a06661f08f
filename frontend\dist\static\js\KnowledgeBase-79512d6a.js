import{_ as He,u as Ge,i as P,v as K,h as k,o as Ie,r as w,a as g,c as b,b as t,d as l,w as r,t as p,x as $,j as z,F as Q,p as O,k as Ne,E as m,y as le,n as Ke,f,z as fe,A as j,B as Qe,s as W,C as ve,D as J,G as X,H as Oe}from"./index-2c134546.js";import{k as C}from"./knowledge-a7f85df9.js";const je={class:"knowledge-base-container"},We={class:"page-header"},Je={class:"header-content"},Xe={class:"header-actions"},Ze={class:"page-main"},et={class:"main-layout"},tt={class:"left-panel"},ot={class:"config-card"},st={class:"card-header"},lt={class:"config-actions"},nt={class:"config-content"},at={class:"config-section"},it={class:"form-group"},rt={class:"form-group"},dt={class:"form-group"},ut={class:"form-group"},ct={class:"config-section"},pt={class:"form-group"},_t={class:"form-group"},mt={class:"form-group"},ft={class:"form-group switch-group"},vt={class:"form-group switch-group"},gt={key:0,class:"reranker-config"},kt={class:"form-group"},ht={class:"form-group"},yt={class:"form-group"},wt={class:"config-note"},bt={class:"status-card"},xt={class:"card-header"},Vt={class:"status-grid"},Tt={class:"status-item"},St={class:"status-value"},Ct={class:"status-item"},Et={class:"status-value"},zt={class:"status-item"},Dt={class:"status-value"},Mt={class:"upload-card"},Ut={class:"card-header"},Pt={key:0,class:"upload-content"},$t={key:1,class:"text-upload-content"},At={class:"right-panel"},Yt={class:"chat-card"},qt={class:"card-header"},Bt={class:"chat-actions"},Lt={key:0,class:"query-config"},Rt={class:"config-header"},Ft={class:"config-row"},Ht={class:"config-item"},Gt={class:"config-item"},It={class:"config-item"},Nt={key:0,class:"config-item"},Kt={class:"config-note"},Qt={class:"chat-container"},Ot={class:"message-content"},jt={class:"message-text-container"},Wt={key:0,class:"think-section"},Jt=["onClick","onTouchstartPassive","onTouchmove","onTouchendPassive"],Xt={class:"think-header-left"},Zt={class:"think-header-right"},eo={class:"think-toggle-text"},to={class:"think-swipe-indicator"},oo={class:"think-content"},so=["innerHTML"],lo={class:"main-content"},no=["innerHTML"],ao={class:"message-time"},io={key:0,class:"message-sources"},ro=["onClick","onTouchstartPassive","onTouchmove","onTouchendPassive"],uo={class:"sources-header-left"},co={class:"sources-title"},po={class:"sources-header-right"},_o={class:"sources-toggle-text"},mo={class:"swipe-indicator"},fo={class:"sources-content"},vo={class:"source-header"},go={class:"source-info"},ko={class:"source-filename"},ho={class:"source-relevance"},yo={class:"source-content"},wo={class:"source-text"},bo={class:"chat-input"},xo={__name:"KnowledgeBase",setup(Vo){const ge=Ge(),Z=P({document_count:0,initialized:!1,collection_name:""}),n=P({api_type:"ollama",api_base_url:"http://localhost:11434",api_model:"qwen3:4b",embedding_model:"nomic-embed-text:latest",similarity_top_k:5,similarity_cutoff:0,response_mode:"compact",streaming:!0,use_reranker:!1,reranker_model_path:"./models/Qwen3-Reranker-0.6B",reranker_top_k:3,rerank_instruction:"Find documents containing accurate factual information related to the query",chunk_size:512,chunk_overlap:32}),d=P({similarity_top_k:5,similarity_cutoff:0,use_reranker:!1,reranker_top_k:3}),A=()=>{d.similarity_top_k=n.similarity_top_k,d.similarity_cutoff=n.similarity_cutoff,d.use_reranker=n.use_reranker,d.reranker_top_k=n.reranker_top_k,console.log("配置已同步:",{similarity_top_k:d.similarity_top_k,similarity_cutoff:d.similarity_cutoff,use_reranker:d.use_reranker,reranker_top_k:d.reranker_top_k})};K(()=>n.similarity_top_k,o=>{d.similarity_top_k=o,console.log("检索数量已自动同步:",o)}),K(()=>n.similarity_cutoff,o=>{d.similarity_cutoff=o,console.log("相似度阈值已自动同步:",o)}),K(()=>n.use_reranker,o=>{d.use_reranker=o,console.log("重排序设置已自动同步:",o)}),K(()=>n.reranker_top_k,o=>{d.reranker_top_k=o,console.log("重排序数量已自动同步:",o)});const h=P({filename:"",content:"",file_type:"txt"}),y=k([{role:"assistant",content:"<think>用户刚进入系统，我需要提供一个友好的欢迎信息，介绍系统的主要功能和使用方法。我应该简洁明了地说明文档上传、知识检索和问答功能。</think>您好！我是您的AI助手，请上传文档到知识库，然后您可以向我提问。我可以帮助您分析文档内容，回答相关问题，并提供准确的引用来源。系统支持多种文档格式，包括PDF、Word、Excel、PowerPoint、文本文件和Markdown文件。上传文档后，系统会自动进行文档解析、分块处理和向量化存储，建立高效的知识检索索引。当您提出问题时，我会基于上传的文档内容进行智能检索和分析，为您提供准确、相关的答案，并标注具体的引用来源，确保信息的可追溯性和可信度。",timestamp:new Date,sources:[],sourcesExpanded:!1,thinkExpanded:!1}]),D=k([]),Y=k("未测试"),M=k(""),E=k(!1),q=k(!1),B=k(!1),L=k(!1),ee=k(!1),ke=k([]),R=k(null),F=k(!1),H=k(!1),he="http://localhost:8002/api/v1/knowledge/documents/upload-file",ye={Authorization:`Bearer ${localStorage.getItem("token")||"demo-token"}`},we=async()=>{try{const o=await C.getAvailableModels();D.value=o.data.models.map(e=>({id:e.id||e.name,name:e.name||e.id})),console.log("获取到可用模型:",D.value)}catch(o){console.error("获取模型列表失败:",o),await G()}},G=async()=>{B.value=!0;try{console.log("测试连接配置:",n);const o=await C.testConnection(n);o.data.success?(Y.value="连接成功",D.value=o.data.models.map(e=>({id:e,name:e})),console.log("可用模型列表:",D.value),m.success(o.data.message)):(Y.value="连接失败",m.error(o.data.message))}catch(o){Y.value="连接失败",console.error("连接测试失败:",o),m.error("连接测试失败")}finally{B.value=!1}},be=async()=>{L.value=!0;try{const o=await C.updateConfig(n);m.success("配置保存成功"),A(),await ne(),await G()}catch(o){console.error("保存配置失败:",o),m.error("保存配置失败")}finally{L.value=!1}},ne=async(o=!1)=>{try{const e=await C.getConfig();return Object.assign(n,e.data.config),console.log("配置加载成功:",e.data.config),A(),o&&m.success("配置加载成功"),!0}catch(e){return console.error("加载配置失败:",e),o&&m.error("加载配置失败"),!1}},xe=async()=>{q.value=!0;try{const o=await C.initializeRAG(n.embedding_model,n.api_model);m.success("RAG服务初始化成功"),await I()}catch(o){console.error("初始化失败:",o),m.error("RAG服务初始化失败")}finally{q.value=!1}},I=async()=>{try{const o=await C.getStats();Object.assign(Z,o.data)}catch(o){console.error("获取统计失败:",o),m.error("获取统计信息失败")}},Ve=async()=>{if(!h.filename||!h.content){m.warning("请填写文件名和内容");return}ee.value=!0;try{const o=await C.uploadTextDocument({filename:h.filename,content:h.content,file_type:h.file_type});m.success("文本上传成功"),h.filename="",h.content="",await I()}catch(o){console.error("文本上传失败:",o),m.error("文本上传失败")}finally{ee.value=!1}},Te=()=>{y.value=[{role:"assistant",content:"<think>用户清空了对话，我需要重新提供欢迎信息。</think>您好！我是您的AI助手，请上传文档到知识库，然后您可以向我提问。",timestamp:new Date,sources:[],sourcesExpanded:!1,thinkExpanded:!1}]},Se=o=>!["application/pdf","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.openxmlformats-officedocument.presentationml.presentation","text/plain","text/markdown"].includes(o.type)&&!o.name.match(/\.(pdf|docx|xlsx|pptx|txt|md)$/i)?(m.error("不支持的文件格式"),!1):o.size>50*1024*1024?(m.error("文件大小不能超过50MB"),!1):!0,Ce=(o,e)=>{if(o.success){const{document_type:a,documents_added:i,nodes_added:u,sections:_}=o;let c=`文件 ${e.name} 上传成功！
`;c+=`文档类型: ${a}
`,c+=`生成文档块: ${i}个
`,c+=`生成节点: ${u}个`,_&&_.length>0&&(c+=`
章节: ${_.slice(0,3).join(", ")}`,_.length>3&&(c+=` 等${_.length}个章节`)),m({message:c,type:"success",duration:5e3,showClose:!0})}else m.success(`文件 ${e.name} 上传成功`);I()},Ee=(o,e)=>{console.error("上传失败:",o),m.error(`文件 ${e.name} 上传失败`)},ae=async()=>{if(!M.value.trim()){m.warning("请输入问题");return}y.value.push({role:"user",content:M.value,timestamp:new Date});const o=M.value;M.value="",E.value=!0;const e=P({role:"assistant",content:"",timestamp:new Date,sources:[],sourcesExpanded:!1,thinkExpanded:!1});y.value.push(e);try{const a={question:o,api_model:n.api_model,embedding_model:n.embedding_model,similarity_top_k:d.similarity_top_k,similarity_cutoff:d.similarity_cutoff,response_mode:n.response_mode,streaming:n.streaming,use_reranker:d.use_reranker,reranker_top_k:d.reranker_top_k||n.reranker_top_k};if(console.log("=== 查询配置详情 ==="),console.log("ragConfig.similarity_top_k:",n.similarity_top_k),console.log("queryConfig.similarity_top_k:",d.similarity_top_k),console.log("ragConfig.use_reranker:",n.use_reranker),console.log("queryConfig.use_reranker:",d.use_reranker),console.log("最终查询配置:",a),console.log("是否启用流式回答:",n.streaming),console.log("==================="),n.streaming){console.log("开始流式查询...");const i=new URLSearchParams({question:a.question,api_model:a.api_model,embedding_model:a.embedding_model,similarity_top_k:a.similarity_top_k.toString(),similarity_cutoff:a.similarity_cutoff.toString(),response_mode:a.response_mode,streaming:"true",use_reranker:a.use_reranker.toString(),reranker_top_k:(a.reranker_top_k||n.reranker_top_k).toString()}),u=new EventSource(`http://localhost:8002/api/v1/knowledge/query/stream?${i}`);u.onopen=()=>{console.log("SSE连接已建立")},u.onmessage=_=>{console.log("收到SSE消息:",_.data);try{const c=JSON.parse(_.data);if(console.log("解析后的数据:",c),c.content){if(c.content.includes("### 引用来源")){const x=ue(c.content);e.sources=x,e.sourcesExpanded=x.length>0}else e.content+=c.content;le(()=>{const x=y.value.length-1;x>=0&&(y.value[x]={...e}),te()})}if(c.finished&&(console.log("响应完成"),u.close(),E.value=!1,le(()=>{const x=y.value.length-1;x>=0&&(y.value[x]={...e}),te()})),c.error)throw console.error("服务器返回错误:",c.error),new Error(c.error)}catch(c){console.warn("解析SSE数据失败:",c,"原始数据:",_.data),u.close(),E.value=!1}},u.onerror=_=>{throw console.error("SSE连接错误:",_),u.close(),E.value=!1,new Error("流式连接失败")},Ke(()=>{u.readyState!==EventSource.CLOSED&&u.close()})}else{const i=await C.query(a),u=i.data.answer||i.data.response||i.data;if(u.includes("### 引用来源")){const _=u.split("### 引用来源");if(e.content=_[0].trim(),_[1]){const c=ue("### 引用来源"+_[1]);e.sources=c,e.sourcesExpanded=c.length>0}}else e.content=u}}catch(a){console.error("查询失败:",a);let i="抱歉，查询过程中出现错误，请稍后再试。",u="查询失败，请稍后重试";a.name==="AbortError"?(i="查询超时，请尝试简化问题或稍后重试。",u="查询超时（5分钟），请稍后重试"):a.message.includes("HTTP error")?(i="服务器响应错误，请检查后端服务状态。",u="服务器错误，请联系管理员"):a.message.includes("Network")&&(i="网络连接失败，请检查网络连接。",u="网络连接失败"),e.content=i,m.error(u)}finally{E.value=!1,te()}},ie=o=>{const e=o.match(/<think>([\s\S]*?)<\/think>/i);return e?e[1].trim():null},ze=o=>o.replace(/<think>[\s\S]*?<\/think>/gi,"").trim(),re=o=>o.replace(/\n/g,"<br>"),De=o=>new Date(o).toLocaleTimeString(),te=()=>{le(()=>{R.value&&(R.value.scrollTop=R.value.scrollHeight)})},de=()=>{ge.push("/documents")},ue=o=>{const e=[],a=/\*\*来源 (\d+)\*\*: (.+?) \(相关性: ([\d.]+)\)\s*\*\*内容片段\*\*: (.+?)(?=\n\*\*来源|\n\n|$)/gs;let i;for(;(i=a.exec(o))!==null;)e.push({index:parseInt(i[1]),filename:i[2].trim(),score:parseFloat(i[3]),content:i[4].trim()});return e},ce=o=>{const e=y.value[o];e&&e.sources&&(e.sourcesExpanded=!e.sourcesExpanded)},Me=o=>typeof o!="number"?"N/A":(o*100).toFixed(1)+"%",Ue=o=>typeof o!="number"?"relevance-unknown":o>=.8?"relevance-high":o>=.6?"relevance-medium":o>=.4?"relevance-low":"relevance-very-low",pe=o=>{const e=y.value[o];e&&e.role==="assistant"&&(e.thinkExpanded=!e.thinkExpanded)},T=k({startY:0,startTime:0,isSwipeDown:!1,messageIndex:-1}),Pe=(o,e)=>{const a=o.touches[0];console.log("Think touch start:",{messageIndex:e,startY:a.clientY}),T.value={startY:a.clientY,startTime:Date.now(),isSwipeDown:!1,messageIndex:e}},$e=(o,e)=>{if(T.value.messageIndex!==e)return;const i=o.touches[0].clientY-T.value.startY;console.log("Think touch move:",{messageIndex:e,deltaY:i}),i>40&&(console.log("Think swipe down detected!"),T.value.isSwipeDown=!0)},Ae=(o,e)=>{if(T.value.messageIndex!==e)return;console.log("Think touch end:",{isSwipeDown:T.value.isSwipeDown,messageIndex:e,hasMessage:!!y.value[e]});const a=y.value[e];T.value.isSwipeDown&&a&&a.role==="assistant"&&!a.thinkExpanded&&(console.log("Executing think expansion for:",e),pe(e),navigator.vibrate&&navigator.vibrate(50)),T.value={startY:0,startTime:0,isSwipeDown:!1,messageIndex:-1}},S=k({startY:0,startTime:0,isSwipeDown:!1,messageIndex:-1}),Ye=(o,e)=>{const a=o.touches[0];console.log("Touch start:",{messageIndex:e,startY:a.clientY}),S.value={startY:a.clientY,startTime:Date.now(),isSwipeDown:!1,messageIndex:e}},qe=(o,e)=>{if(S.value.messageIndex!==e)return;const i=o.touches[0].clientY-S.value.startY;console.log("Touch move:",{messageIndex:e,deltaY:i}),i>30&&(console.log("Swipe down detected!"),S.value.isSwipeDown=!0)},Be=(o,e)=>{if(S.value.messageIndex!==e)return;console.log("Touch end:",{isSwipeDown:S.value.isSwipeDown,messageIndex:e,hasMessage:!!y.value[e]});const a=y.value[e];S.value.isSwipeDown&&a&&a.sources&&a.sources.length>0&&(console.log("Executing swipe action for message:",e),ce(e),navigator.vibrate&&navigator.vibrate(50)),S.value={startY:0,startTime:0,isSwipeDown:!1,messageIndex:-1}};return Ie(async()=>{try{await I();const o=await ne();A(),await we(),console.log(o?"使用加载的配置测试连接":"使用默认配置测试连接"),await G()}catch(o){console.error("初始化失败:",o)}}),(o,e)=>{const a=w("router-link"),i=w("el-button"),u=w("el-option"),_=w("el-select"),c=w("el-input"),x=w("el-slider"),oe=w("el-switch"),_e=w("el-alert"),U=w("el-icon"),Le=w("el-upload"),N=w("el-form-item"),Re=w("el-form"),se=w("el-input-number"),me=w("el-collapse-transition");return g(),b("div",je,[t("div",We,[t("div",Je,[e[25]||(e[25]=t("div",{class:"header-brand"},[t("div",{class:"brand-logo"},"🧠"),t("span",{class:"brand-text"},"RAG知识库")],-1)),t("div",Xe,[l(a,{to:"/dashboard",class:"btn-outline"},{default:r(()=>e[24]||(e[24]=[f("返回控制台")])),_:1,__:[24]})])])]),t("div",Ze,[t("div",et,[t("div",tt,[t("div",ot,[t("div",st,[e[27]||(e[27]=t("h3",null,"⚙️ 配置管理",-1)),t("div",lt,[l(i,{type:"info",size:"large",onClick:de},{default:r(()=>e[26]||(e[26]=[f(" 📄 文档管理 ")])),_:1,__:[26]}),l(i,{type:"success",size:"large",onClick:G,loading:B.value},{default:r(()=>[f(p(B.value?"测试中...":"测试连接"),1)]),_:1},8,["loading"]),l(i,{type:"primary",size:"large",onClick:be,loading:L.value},{default:r(()=>[f(p(L.value?"保存中...":"保存配置"),1)]),_:1},8,["loading"])])]),t("div",nt,[t("div",at,[e[32]||(e[32]=t("h4",null,"🔧 基础配置",-1)),t("div",it,[e[28]||(e[28]=t("label",null,"API类型",-1)),l(_,{modelValue:n.api_type,"onUpdate:modelValue":e[0]||(e[0]=s=>n.api_type=s),size:"large"},{default:r(()=>[l(u,{label:"Ollama",value:"ollama"})]),_:1},8,["modelValue"])]),t("div",rt,[e[29]||(e[29]=t("label",null,"API地址",-1)),l(c,{modelValue:n.api_base_url,"onUpdate:modelValue":e[1]||(e[1]=s=>n.api_base_url=s),size:"large",placeholder:"http://localhost:11434"},null,8,["modelValue"])]),t("div",dt,[e[30]||(e[30]=t("label",null,"LLM模型",-1)),l(_,{modelValue:n.api_model,"onUpdate:modelValue":e[2]||(e[2]=s=>n.api_model=s),size:"large",filterable:""},{default:r(()=>[(g(!0),b(Q,null,O(D.value,s=>(g(),fe(u,{key:s.id,label:s.name,value:s.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),t("div",ut,[e[31]||(e[31]=t("label",null,"嵌入模型",-1)),l(_,{modelValue:n.embedding_model,"onUpdate:modelValue":e[3]||(e[3]=s=>n.embedding_model=s),size:"large",filterable:""},{default:r(()=>[(g(!0),b(Q,null,O(D.value,s=>(g(),fe(u,{key:s.id,label:s.name,value:s.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])])]),t("div",ct,[e[39]||(e[39]=t("h4",null,"⚡ 高级配置",-1)),t("div",pt,[t("label",null,"检索数量: "+p(n.similarity_top_k),1),l(x,{modelValue:n.similarity_top_k,"onUpdate:modelValue":e[4]||(e[4]=s=>n.similarity_top_k=s),min:1,max:20,"show-input":""},null,8,["modelValue"])]),t("div",_t,[t("label",null,"相似度阈值: "+p(n.similarity_cutoff),1),l(x,{modelValue:n.similarity_cutoff,"onUpdate:modelValue":e[5]||(e[5]=s=>n.similarity_cutoff=s),min:0,max:1,step:.05,"show-input":""},null,8,["modelValue"])]),t("div",mt,[e[33]||(e[33]=t("label",null,"响应模式",-1)),l(_,{modelValue:n.response_mode,"onUpdate:modelValue":e[6]||(e[6]=s=>n.response_mode=s),size:"large"},{default:r(()=>[l(u,{label:"紧凑模式",value:"compact"}),l(u,{label:"精炼模式",value:"refine"}),l(u,{label:"简单模式",value:"simple"}),l(u,{label:"树形总结",value:"tree_summarize"})]),_:1},8,["modelValue"])]),t("div",ft,[e[34]||(e[34]=t("label",null,"流式回答",-1)),l(oe,{modelValue:n.streaming,"onUpdate:modelValue":e[7]||(e[7]=s=>n.streaming=s),size:"large"},null,8,["modelValue"])]),t("div",vt,[e[35]||(e[35]=t("label",null,"启用重排序",-1)),l(oe,{modelValue:n.use_reranker,"onUpdate:modelValue":e[8]||(e[8]=s=>n.use_reranker=s),size:"large"},null,8,["modelValue"])]),n.use_reranker?(g(),b("div",gt,[t("div",kt,[t("label",null,"重排序数量: "+p(n.reranker_top_k),1),l(x,{modelValue:n.reranker_top_k,"onUpdate:modelValue":e[9]||(e[9]=s=>n.reranker_top_k=s),min:1,max:10,"show-input":""},null,8,["modelValue"])]),t("div",ht,[e[36]||(e[36]=t("label",null,"重排序模型路径",-1)),l(c,{modelValue:n.reranker_model_path,"onUpdate:modelValue":e[10]||(e[10]=s=>n.reranker_model_path=s),placeholder:"../models/Qwen3-Reranker-0.6B",size:"large"},null,8,["modelValue"])]),t("div",yt,[e[37]||(e[37]=t("label",null,"重排序指令",-1)),l(c,{modelValue:n.rerank_instruction,"onUpdate:modelValue":e[11]||(e[11]=s=>n.rerank_instruction=s),type:"textarea",rows:3,placeholder:"Find documents containing accurate factual information related to the query",size:"large"},null,8,["modelValue"])]),t("div",wt,[l(_e,{title:"重排序配置说明",type:"info",closable:!1,"show-icon":""},{default:r(()=>e[38]||(e[38]=[t("p",null,[f("• "),t("strong",null,"重排序数量"),f(": 重排序后保留的文档数量")],-1),t("p",null,[f("• "),t("strong",null,"模型路径"),f(": 重排序模型的本地路径")],-1),t("p",null,[f("• "),t("strong",null,"重排序指令"),f(": 指导重排序模型判断文档相关性的指令")],-1)])),_:1})])])):$("",!0)])])]),t("div",bt,[t("div",xt,[e[40]||(e[40]=t("h3",null,"📊 系统状态",-1)),l(i,{type:"primary",size:"large",onClick:xe,loading:q.value},{default:r(()=>[f(p(q.value?"初始化中...":"初始化RAG"),1)]),_:1},8,["loading"])]),t("div",Vt,[t("div",Tt,[t("div",St,p(Z.document_count||0),1),e[41]||(e[41]=t("div",{class:"status-label"},"文档数量",-1))]),t("div",Ct,[t("div",Et,p(Z.initialized?"✅":"❌"),1),e[42]||(e[42]=t("div",{class:"status-label"},"服务状态",-1))]),t("div",zt,[t("div",Dt,p(Y.value==="连接成功"?"✅":"❌"),1),e[43]||(e[43]=t("div",{class:"status-label"},"连接状态",-1))])])]),t("div",Mt,[t("div",Ut,[e[44]||(e[44]=t("h3",null,"📄 文档上传",-1)),l(i,{type:"text",size:"large",onClick:e[12]||(e[12]=s=>F.value=!F.value)},{default:r(()=>[f(p(F.value?"文件上传":"文本上传"),1)]),_:1})]),F.value?(g(),b("div",$t,[l(Re,{model:h,"label-width":"80px"},{default:r(()=>[l(N,{label:"文件名"},{default:r(()=>[l(c,{modelValue:h.filename,"onUpdate:modelValue":e[13]||(e[13]=s=>h.filename=s),size:"large",placeholder:"请输入文件名"},null,8,["modelValue"])]),_:1}),l(N,{label:"文件类型"},{default:r(()=>[l(_,{modelValue:h.file_type,"onUpdate:modelValue":e[14]||(e[14]=s=>h.file_type=s),size:"large"},{default:r(()=>[l(u,{label:"文本文件",value:"txt"}),l(u,{label:"Markdown",value:"md"}),l(u,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1}),l(N,{label:"内容"},{default:r(()=>[l(c,{modelValue:h.content,"onUpdate:modelValue":e[15]||(e[15]=s=>h.content=s),type:"textarea",rows:6,placeholder:"请输入文档内容..."},null,8,["modelValue"])]),_:1}),l(N,null,{default:r(()=>[l(i,{type:"primary",size:"large",onClick:Ve,loading:ee.value,style:{width:"100%"}},{default:r(()=>e[47]||(e[47]=[f(" 上传文本 ")])),_:1,__:[47]},8,["loading"])]),_:1})]),_:1},8,["model"])])):(g(),b("div",Pt,[l(Le,{class:"upload-area",drag:"",action:he,headers:ye,"before-upload":Se,"on-success":Ce,"on-error":Ee,"file-list":ke.value,multiple:""},{default:r(()=>[l(U,{class:"upload-icon"},{default:r(()=>[l(j(Qe))]),_:1}),e[45]||(e[45]=t("div",{class:"upload-text"},"拖拽文件到此处或点击上传",-1)),e[46]||(e[46]=t("div",{class:"upload-tip"},"支持 PDF、Word、Excel、PowerPoint、Markdown、文本文件",-1))]),_:1,__:[45,46]},8,["file-list"])]))])]),t("div",At,[t("div",Yt,[t("div",qt,[e[50]||(e[50]=t("h3",null,"💬 智能问答",-1)),t("div",Bt,[l(i,{type:"text",size:"large",onClick:Te},{default:r(()=>e[48]||(e[48]=[f(" 清空对话 ")])),_:1,__:[48]}),l(i,{type:"text",size:"large",onClick:e[16]||(e[16]=s=>H.value=!H.value)},{default:r(()=>[f(p(H.value?"隐藏配置":"查询配置"),1)]),_:1}),l(i,{type:"primary",size:"large",onClick:de},{default:r(()=>e[49]||(e[49]=[f(" 文档管理 ")])),_:1,__:[49]})])]),H.value?(g(),b("div",Lt,[t("div",Rt,[e[52]||(e[52]=t("h4",null,"🔍 当前查询配置",-1)),l(i,{type:"text",size:"small",onClick:A},{default:r(()=>e[51]||(e[51]=[f(" 同步左侧配置 ")])),_:1,__:[51]})]),t("div",Ft,[t("div",Ht,[t("label",null,"检索数量 (当前: "+p(d.similarity_top_k)+")",1),l(se,{modelValue:d.similarity_top_k,"onUpdate:modelValue":e[17]||(e[17]=s=>d.similarity_top_k=s),min:1,max:20,size:"large"},null,8,["modelValue"])]),t("div",Gt,[t("label",null,"相似度阈值 (当前: "+p(d.similarity_cutoff)+")",1),l(se,{modelValue:d.similarity_cutoff,"onUpdate:modelValue":e[18]||(e[18]=s=>d.similarity_cutoff=s),min:0,max:1,step:.05,size:"large"},null,8,["modelValue"])]),t("div",It,[e[53]||(e[53]=t("label",null,"启用重排序",-1)),l(oe,{modelValue:d.use_reranker,"onUpdate:modelValue":e[19]||(e[19]=s=>d.use_reranker=s),size:"large"},null,8,["modelValue"])]),d.use_reranker?(g(),b("div",Nt,[t("label",null,"重排序数量: "+p(d.reranker_top_k||n.reranker_top_k),1),l(se,{modelValue:d.reranker_top_k,"onUpdate:modelValue":e[20]||(e[20]=s=>d.reranker_top_k=s),min:1,max:10,size:"large",placeholder:n.reranker_top_k},null,8,["modelValue","placeholder"])])):$("",!0)]),t("div",Kt,[l(_e,{title:"配置说明",type:"info",closable:!1,description:"此处配置仅影响当前查询。要永久保存配置，请在左侧面板修改并点击'保存配置'。"})])])):$("",!0),t("div",Qt,[t("div",{class:"chat-messages",ref_key:"chatMessages",ref:R,onClick:e[22]||(e[22]=z(()=>{},["stop"]))},[(g(!0),b(Q,null,O(y.value,(s,V)=>(g(),b("div",{key:`message-${V}-${s.timestamp}`,class:W(["message",s.role]),onClick:e[21]||(e[21]=z(()=>{},["stop"]))},[t("div",Ot,[t("div",jt,[s.role==="assistant"&&ie(s.content)?(g(),b("div",Wt,[t("div",{class:"think-header",onClick:z(v=>pe(V),["stop"]),onTouchstartPassive:v=>Pe(v,V),onTouchmove:z(v=>$e(v,V),["prevent"]),onTouchendPassive:v=>Ae(v,V)},[t("div",Xt,[l(U,{class:"think-icon"},{default:r(()=>e[54]||(e[54]=[f("💭")])),_:1,__:[54]}),e[55]||(e[55]=t("span",{class:"think-title"},"AI思考过程",-1))]),t("div",Zt,[t("span",eo,p(s.thinkExpanded?"收起":"展开"),1),l(U,{class:W(["think-arrow",{"think-arrow-expanded":s.thinkExpanded}])},{default:r(()=>[l(j(ve))]),_:2},1032,["class"])]),J(t("div",to,e[56]||(e[56]=[t("div",{class:"think-swipe-line"},null,-1),t("span",{class:"think-swipe-text"},"下滑查看思考过程",-1)]),512),[[X,!s.thinkExpanded]])],40,Jt),l(me,null,{default:r(()=>[J(t("div",oo,[t("div",{class:"think-text",innerHTML:re(ie(s.content))},null,8,so)],512),[[X,s.thinkExpanded]])]),_:2},1024)])):$("",!0),t("div",lo,[t("div",{class:"message-text",innerHTML:re(ze(s.content))},null,8,no)])]),t("div",ao,p(De(s.timestamp)),1)]),s.sources&&s.sources.length>0?(g(),b("div",io,[t("div",{class:"sources-header",onClick:z(v=>ce(V),["stop"]),onTouchstartPassive:v=>Ye(v,V),onTouchmove:z(v=>qe(v,V),["prevent"]),onTouchendPassive:v=>Be(v,V)},[t("div",uo,[l(U,{class:"sources-icon"},{default:r(()=>[l(j(Oe))]),_:1}),t("span",co,"引用来源 ("+p(s.sources.length)+")",1)]),t("div",po,[t("span",_o,p(s.sourcesExpanded?"收起":"展开"),1),l(U,{class:W(["sources-arrow",{"sources-arrow-expanded":s.sourcesExpanded}])},{default:r(()=>[l(j(ve))]),_:2},1032,["class"])]),J(t("div",mo,e[57]||(e[57]=[t("div",{class:"swipe-line"},null,-1),t("span",{class:"swipe-text"},"下滑查看引用",-1)]),512),[[X,!s.sourcesExpanded]])],40,ro),l(me,null,{default:r(()=>[J(t("div",fo,[(g(!0),b(Q,null,O(s.sources,(v,Fe)=>(g(),b("div",{key:Fe,class:"source-item"},[t("div",vo,[t("div",go,[t("span",ko,p(v.filename||"未知文档"),1),t("div",ho,[e[58]||(e[58]=t("span",{class:"relevance-label"},"相关性:",-1)),t("div",{class:W(["relevance-score",Ue(v.score)])},p(Me(v.score)),3)])])]),t("div",yo,[t("p",wo,p(v.content),1)])]))),128))],512),[[X,s.sourcesExpanded]])]),_:2},1024)])):$("",!0)],2))),128))],512),t("div",bo,[l(c,{modelValue:M.value,"onUpdate:modelValue":e[23]||(e[23]=s=>M.value=s),type:"textarea",rows:4,size:"large",placeholder:"请输入您的问题...",onKeydown:Ne(z(ae,["ctrl"]),["enter"])},null,8,["modelValue","onKeydown"]),l(i,{type:"primary",size:"large",onClick:ae,loading:E.value,class:"send-button"},{default:r(()=>[f(p(E.value?"思考中...":"发送问题 (Ctrl+Enter)"),1)]),_:1},8,["loading"])])])])])])])])}}},Co=He(xo,[["__scopeId","data-v-5f24fc5a"]]);export{Co as default};
