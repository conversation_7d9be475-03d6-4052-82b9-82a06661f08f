#!/usr/bin/env python3
"""
测试修复后的API任务状态查询
"""

import requests
import json
import sqlite3
import redis
from celery.result import AsyncResult

# 配置
API_BASE = "http://localhost:8002/api/v1"
TASK_ID = "meeting_transcription_a8c695b2c7d1"
FILE_ID = 2

def get_auth_headers():
    """获取认证头"""
    # 登录获取token
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    response = requests.post(f"{API_BASE}/auth/login", json=login_data)
    if response.status_code == 200:
        token = response.json()["access_token"]
        return {"Authorization": f"Bearer {token}"}
    else:
        raise Exception(f"登录失败: {response.status_code}")

def test_api_task_status():
    """测试API任务状态查询"""
    print("=== 测试API任务状态查询 ===")
    
    try:
        headers = get_auth_headers()
        response = requests.get(f"{API_BASE}/speech/task/{TASK_ID}", headers=headers)
        
        print(f"API响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ API查询成功:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
            return result
        else:
            print(f"❌ API查询失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ API查询异常: {e}")
        return None

def check_database_status():
    """检查数据库状态"""
    print("\n=== 检查数据库状态 ===")
    
    try:
        conn = sqlite3.connect('data/speech_platform.db')
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT task_id, status, progress_percentage, progress_detail, created_at, updated_at
            FROM task_records 
            WHERE task_id = ?
        """, (TASK_ID,))
        
        task_record = cursor.fetchone()
        if task_record:
            print("✅ 数据库记录:")
            print(f"   任务ID: {task_record[0]}")
            print(f"   状态: {task_record[1]}")
            print(f"   进度: {task_record[2]}%")
            print(f"   详情: {task_record[3]}")
            print(f"   创建时间: {task_record[4]}")
            print(f"   更新时间: {task_record[5]}")
        else:
            print("❌ 数据库中未找到任务记录")
            
        conn.close()
        
    except Exception as e:
        print(f"❌ 数据库查询失败: {e}")

def check_redis_status():
    """检查Redis状态"""
    print("\n=== 检查Redis状态 ===")
    
    try:
        redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=True)
        
        progress_key = f"task_progress:{TASK_ID}"
        redis_data = redis_client.hgetall(progress_key)
        
        if redis_data:
            print("✅ Redis进度数据:")
            for key, value in redis_data.items():
                print(f"   {key}: {value}")
        else:
            print("❌ Redis中未找到进度数据")
            
    except Exception as e:
        print(f"❌ Redis查询失败: {e}")

def check_celery_status():
    """检查Celery状态"""
    print("\n=== 检查Celery状态 ===")
    
    try:
        from backend.core.task_queue import celery_app
        result = AsyncResult(TASK_ID, app=celery_app)
        
        print(f"✅ Celery任务状态:")
        print(f"   状态: {result.state}")
        print(f"   就绪: {result.ready()}")
        print(f"   成功: {result.successful() if result.ready() else 'N/A'}")
        print(f"   失败: {result.failed() if result.ready() else 'N/A'}")
        
    except Exception as e:
        print(f"❌ Celery查询失败: {e}")

def test_task_manager():
    """测试TaskManager"""
    print("\n=== 测试TaskManager ===")
    
    try:
        from backend.core.task_queue import get_task_manager
        task_manager = get_task_manager()
        
        task_status = task_manager.get_task_status(TASK_ID)
        
        print("✅ TaskManager状态:")
        print(json.dumps(task_status, indent=2, ensure_ascii=False))
        
        return task_status
        
    except Exception as e:
        print(f"❌ TaskManager查询失败: {e}")
        return None

def compare_results(api_result, task_manager_result):
    """比较API和TaskManager结果"""
    print("\n=== 结果比较 ===")
    
    if not api_result or not task_manager_result:
        print("❌ 无法比较，缺少数据")
        return
    
    api_status = api_result.get("status", "unknown")
    tm_state = task_manager_result.get("state", "unknown")
    
    print(f"API状态: {api_status}")
    print(f"TaskManager状态: {tm_state}")
    
    if api_status == "SUCCESS" and tm_state == "SUCCESS":
        print("✅ 状态一致！修复成功")
    elif api_status != tm_state:
        print(f"❌ 状态不一致: API={api_status}, TaskManager={tm_state}")
    else:
        print(f"ℹ️  状态: {api_status}")

def main():
    print("🔧 测试修复后的API任务状态查询")
    print("=" * 60)
    
    # 1. 检查各个数据源
    check_database_status()
    check_redis_status()
    check_celery_status()
    
    # 2. 测试TaskManager
    task_manager_result = test_task_manager()
    
    # 3. 测试API
    api_result = test_api_task_status()
    
    # 4. 比较结果
    compare_results(api_result, task_manager_result)
    
    print("\n🎉 测试完成")

if __name__ == "__main__":
    main()
