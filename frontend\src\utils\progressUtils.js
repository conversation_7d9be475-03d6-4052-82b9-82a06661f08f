/**
 * 进度数据处理工具函数
 * 🔧 统一不同任务类型的进度计算逻辑
 */

/**
 * 验证进度数据的有效性
 * @param {Object} progressData - 进度数据
 * @returns {boolean} 是否有效
 */
export function validateProgressData(progressData) {
  if (!progressData || typeof progressData !== 'object') {
    return false
  }
  
  // 必须有task_id
  if (!progressData.task_id && !progressData.taskId) {
    return false
  }
  
  return true
}

/**
 * 标准化进度数据格式
 * @param {Object} rawData - 原始进度数据
 * @returns {Object} 标准化后的进度数据
 */
export function normalizeProgressData(rawData) {
  if (!validateProgressData(rawData)) {
    console.warn('⚠️ 无效的进度数据:', rawData)
    return null
  }
  
  const taskId = rawData.task_id || rawData.taskId
  const percentage = Math.max(0, Math.min(100, rawData.percentage || 0))
  const detail = rawData.detail || rawData.message || '处理中...'
  const stage = rawData.stage || rawData.status || 'processing'
  
  return {
    task_id: taskId,
    percentage,
    detail,
    stage,
    status: getStatusFromPercentage(percentage, stage),
    timestamp: Date.now()
  }
}

/**
 * 根据百分比和阶段确定状态
 * @param {number} percentage - 进度百分比
 * @param {string} stage - 当前阶段
 * @returns {string} 状态
 */
export function getStatusFromPercentage(percentage, stage) {
  if (stage === 'failed' || stage === 'error') {
    return 'failed'
  }
  
  if (stage === 'completed' || stage === 'complete' || percentage >= 100) {
    return 'completed'
  }
  
  if (percentage > 0) {
    return 'progress'
  }
  
  return 'pending'
}

/**
 * 获取向量化任务的阶段映射
 * @param {string} stage - 后端阶段
 * @param {number} percentage - 进度百分比
 * @returns {Object} 阶段信息
 */
export function getVectorizationStageInfo(stage, percentage) {
  const stageMap = {
    'initializing': {
      display: 'upload',
      name: '初始化',
      icon: 'Upload'
    },
    'vectorizing': {
      display: 'indexing',
      name: '向量化处理',
      icon: 'Collection'
    },
    'saving': {
      display: 'indexing',
      name: '保存索引',
      icon: 'Check'
    },
    'completed': {
      display: 'complete',
      name: '处理完成',
      icon: 'Check'
    },
    'failed': {
      display: 'upload',
      name: '处理失败',
      icon: 'Close'
    }
  }
  
  // 如果有明确的阶段映射，使用它
  if (stageMap[stage]) {
    return stageMap[stage]
  }
  
  // 否则根据百分比判断
  if (percentage >= 100) {
    return stageMap['completed']
  } else if (percentage >= 20) {
    return stageMap['vectorizing']
  } else {
    return stageMap['initializing']
  }
}

/**
 * 获取文档处理任务的阶段映射
 * @param {string} stage - 后端阶段
 * @param {number} percentage - 进度百分比
 * @returns {Object} 阶段信息
 */
export function getDocumentProcessingStageInfo(stage, percentage) {
  const stageMap = {
    'upload': {
      display: 'upload',
      name: '文件上传',
      icon: 'Upload'
    },
    'analysis': {
      display: 'analysis',
      name: '文档分析',
      icon: 'Search'
    },
    'ocr': {
      display: 'ocr',
      name: 'OCR处理',
      icon: 'View'
    },
    'splitting': {
      display: 'splitting',
      name: '智能切分',
      icon: 'Grid'
    },
    'indexing': {
      display: 'indexing',
      name: '建立索引',
      icon: 'Collection'
    },
    'completed': {
      display: 'complete',
      name: '处理完成',
      icon: 'Check'
    }
  }
  
  // 如果有明确的阶段映射，使用它
  if (stageMap[stage]) {
    return stageMap[stage]
  }
  
  // 否则根据百分比判断
  if (percentage >= 100) {
    return stageMap['completed']
  } else if (percentage >= 95) {
    return stageMap['indexing']
  } else if (percentage >= 80) {
    return stageMap['splitting']
  } else if (percentage >= 30) {
    return stageMap['ocr']
  } else if (percentage >= 15) {
    return stageMap['analysis']
  } else {
    return stageMap['upload']
  }
}

/**
 * 计算阶段内的进度百分比
 * @param {number} totalPercentage - 总进度百分比
 * @param {Object} stageInfo - 阶段信息
 * @returns {number} 阶段内进度百分比
 */
export function calculateStageProgress(totalPercentage, stageInfo) {
  // 对于向量化任务，直接使用总进度
  if (stageInfo.display === 'indexing' && totalPercentage >= 20) {
    return Math.min(100, ((totalPercentage - 20) / 75) * 100)
  }
  
  // 对于其他阶段，使用简化的计算
  return Math.min(100, totalPercentage)
}

/**
 * 格式化进度详情文本
 * @param {string} detail - 原始详情
 * @param {string} stage - 当前阶段
 * @param {number} percentage - 进度百分比
 * @returns {string} 格式化后的详情
 */
export function formatProgressDetail(detail, stage, percentage) {
  if (!detail || detail === '处理中...') {
    // 根据阶段提供默认的详情文本
    switch (stage) {
      case 'initializing':
        return '正在初始化处理环境...'
      case 'vectorizing':
        return `正在进行向量化处理... ${percentage.toFixed(0)}%`
      case 'saving':
        return '正在保存处理结果...'
      case 'completed':
        return '处理完成！'
      case 'failed':
        return '处理失败'
      default:
        return `处理中... ${percentage.toFixed(0)}%`
    }
  }
  
  return detail
}

/**
 * 检查进度是否需要更新UI
 * @param {Object} newProgress - 新的进度数据
 * @param {Object} currentProgress - 当前进度数据
 * @returns {boolean} 是否需要更新
 */
export function shouldUpdateProgress(newProgress, currentProgress) {
  if (!currentProgress) {
    return true
  }
  
  // 如果百分比变化超过1%，或者阶段发生变化，则更新
  const percentageDiff = Math.abs(newProgress.percentage - currentProgress.percentage)
  const stageChanged = newProgress.stage !== currentProgress.stage
  
  return percentageDiff >= 1 || stageChanged
}
