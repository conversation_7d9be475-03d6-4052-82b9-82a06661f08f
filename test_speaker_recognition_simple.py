#!/usr/bin/env python3
"""
简化的说话人识别测试脚本
"""
import requests
import json
import time
import sqlite3

# API基础URL
BASE_URL = "http://localhost:8002/api/v1"

def login():
    """登录获取token"""
    url = f"{BASE_URL}/auth/login"
    data = {"username": "admin", "password": "admin123"}
    
    try:
        response = requests.post(url, json=data, timeout=10)
        response.raise_for_status()
        result = response.json()
        print(f"✅ 登录成功")
        return result.get("access_token")
    except Exception as e:
        print(f"❌ 登录失败: {e}")
        return None

def create_speaker_recognition_task(token, file_id="1"):
    """创建说话人识别任务"""
    url = f"{BASE_URL}/audio/process"
    headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
    
    data = {
        "file_ids": [file_id],
        "processing_mode": "speaker-recognition",
        "config": {
            "clustering_method": "kmeans",
            "expected_speakers": 2,  # 期望2个说话人
            "similarity_threshold": 0.7,
            "language": "auto",
            "use_itn": True,
            "speaker_model": "cam++"  # 指定说话人模型
        }
    }
    
    try:
        response = requests.post(url, json=data, headers=headers, timeout=30)
        response.raise_for_status()
        result = response.json()
        print(f"✅ 说话人识别任务创建成功")
        print(f"任务ID: {result.get('task_id')}")
        return result.get("task_id")
    except Exception as e:
        print(f"❌ 任务创建失败: {e}")
        if hasattr(e, 'response'):
            print(f"响应内容: {e.response.text}")
        return None

def check_task_in_database(task_id):
    """直接从数据库检查任务状态"""
    try:
        conn = sqlite3.connect('data/speech_platform.db')
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, status, result, error_message, created_at, updated_at
            FROM task_records
            WHERE id = ?
        """, (task_id,))
        
        task = cursor.fetchone()
        if task:
            task_data = {
                "id": task[0],
                "status": task[1],
                "result": task[2],
                "error_message": task[3],
                "created_at": task[4],
                "updated_at": task[5]
            }
            return task_data
        return None
            
    except Exception as e:
        print(f"❌ 查询数据库失败: {e}")
        return None
    finally:
        if 'conn' in locals():
            conn.close()

def analyze_result(result_json):
    """分析说话人识别结果"""
    try:
        result_data = json.loads(result_json)
        
        print("\n📊 说话人识别结果分析:")
        print(f"任务类型: {result_data.get('task_type')}")
        print(f"总文件数: {result_data.get('total_files', 0)}")
        print(f"成功数: {result_data.get('success_count', 0)}")
        print(f"失败数: {result_data.get('error_count', 0)}")
        
        results = result_data.get('results', [])
        if results:
            for i, file_result in enumerate(results):
                print(f"\n📁 文件 {i+1}:")
                print(f"  文件路径: {file_result.get('file_path')}")
                print(f"  状态: {file_result.get('status')}")
                
                file_result_data = file_result.get('result', {})
                if file_result_data:
                    print(f"  说话人数: {file_result_data.get('total_speakers', 0)}")
                    print(f"  音频时长: {file_result_data.get('audio_duration', 0):.2f}秒")
                    print(f"  聚类方法: {file_result_data.get('clustering_method', 'unknown')}")
                    print(f"  相似度阈值: {file_result_data.get('similarity_threshold', 0)}")
                    
                    # 检查优化信息
                    if 'original_threshold' in file_result_data:
                        print(f"  原始阈值: {file_result_data.get('original_threshold')}")
                        print(f"  阈值已优化: ✅")
                    
                    # 检查处理信息
                    processing_info = file_result_data.get('processing_info', {})
                    if processing_info:
                        print(f"  VAD策略: {processing_info.get('vad_strategy', 'unknown')}")
                        print(f"  说话人模型: {processing_info.get('speaker_model', 'unknown')}")
                        print(f"  音频预处理: {'✅' if processing_info.get('audio_preprocessed') else '❌'}")
                    
                    # 检查语音片段
                    segments = file_result_data.get('segments', [])
                    if segments:
                        print(f"  语音片段数: {len(segments)}")
                        print(f"  前3个片段:")
                        for j, segment in enumerate(segments[:3]):
                            start = segment.get('start', 0)
                            end = segment.get('end', 0)
                            speaker = segment.get('speaker', 'unknown')
                            text = segment.get('text', '')[:50] + '...' if len(segment.get('text', '')) > 50 else segment.get('text', '')
                            print(f"    片段{j+1}: {start:.2f}s-{end:.2f}s, 说话人: {speaker}")
                            print(f"           文本: {text}")
                    else:
                        print(f"  ❌ 没有找到语音片段数据")
                        
                    # 检查说话人统计
                    speakers = file_result_data.get('speakers', [])
                    if speakers:
                        print(f"  说话人统计:")
                        for speaker in speakers:
                            speaker_id = speaker.get('speaker_id', 'unknown')
                            duration = speaker.get('total_duration', 0)
                            count = speaker.get('segment_count', 0)
                            print(f"    {speaker_id}: {duration:.2f}秒, {count}个片段")
                    else:
                        print(f"  ❌ 没有找到说话人统计数据")
                        
                    # 检查错误信息
                    if 'error_message' in file_result_data:
                        print(f"  ❌ 错误信息: {file_result_data['error_message']}")
                        
    except Exception as e:
        print(f"❌ 解析结果失败: {e}")
        print(f"原始结果: {result_json[:500]}...")

def main():
    print("🚀 开始测试改进后的说话人识别功能...")
    
    # 1. 登录
    print("\n1. 登录...")
    token = login()
    if not token:
        return
    
    # 2. 创建说话人识别任务
    print("\n2. 创建说话人识别任务...")
    task_id = create_speaker_recognition_task(token)
    if not task_id:
        return
    
    # 3. 监控任务执行
    print(f"\n3. 监控任务执行 (任务ID: {task_id})...")
    
    for i in range(20):  # 最多等待20次，每次3秒
        time.sleep(3)
        print(f"⏳ 检查任务状态... ({i+1}/20)")
        
        task_data = check_task_in_database(task_id)
        if task_data:
            status = task_data.get('status')

            print(f"状态: {status}")
            
            if status == 'SUCCESS':
                print("✅ 任务完成！")
                if task_data.get('result'):
                    analyze_result(task_data['result'])
                break
            elif status == 'FAILURE':
                print("❌ 任务失败！")
                if task_data.get('error_message'):
                    print(f"错误信息: {task_data['error_message']}")
                break
        else:
            print("❌ 无法获取任务状态")
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
