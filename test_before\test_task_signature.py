#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试任务函数签名
"""

import inspect
from backend.tasks.document_tasks import process_document_task

def test_task_signature():
    """测试任务函数签名"""
    print("=" * 60)
    print("任务函数签名测试")
    print("=" * 60)
    
    # 获取函数签名
    sig = inspect.signature(process_document_task)
    print(f"函数名: {process_document_task.__name__}")
    print(f"函数签名: {sig}")
    
    # 获取参数列表
    params = list(sig.parameters.keys())
    print(f"参数列表: {params}")
    print(f"参数数量: {len(params)}")
    
    # 检查每个参数
    print("\n参数详情:")
    for i, (name, param) in enumerate(sig.parameters.items()):
        default = param.default if param.default != inspect.Parameter.empty else "无默认值"
        print(f"  {i+1}. {name}: {param.annotation if param.annotation != inspect.Parameter.empty else 'Any'} = {default}")
    
    # 模拟调用参数
    print("\n模拟调用参数:")
    test_args = [
        "doc_proc_test123",  # task_id
        "user123",           # user_id  
        "test.txt",          # filename
        "file:test123",      # file_key
        123,                 # document_id
        False,               # use_ocr
        None                 # ocr_config
    ]
    
    print(f"测试参数: {test_args}")
    print(f"测试参数数量: {len(test_args)}")
    
    # 检查参数匹配
    try:
        bound = sig.bind(None, *test_args)  # None for self
        print("✅ 参数匹配成功")
        print(f"绑定参数: {bound.arguments}")
    except TypeError as e:
        print(f"❌ 参数匹配失败: {e}")

if __name__ == "__main__":
    test_task_signature()
