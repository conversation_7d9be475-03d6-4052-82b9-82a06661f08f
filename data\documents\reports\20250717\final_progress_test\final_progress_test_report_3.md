### 问题根源
通过详细的测试和日志分析，发现问题出现在前端对WebSocket数据的解析上。

WebSocket发送的数据结构是：
```json
{
  "type": "progress_update",
  "payload": {
    "task_id": "doc_proc_xxx",
    "progress": {
      "task_id": "doc_proc_xxx",
      "state": "STARTED",
      "ready": false,
      "successful": false,
      "progress": {
        "percentage": 5.0,
        "detail": "开始处理文档...",
        "stage": "initializing"
      }
    }
  }
}
```