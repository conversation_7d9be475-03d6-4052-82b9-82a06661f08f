#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
向量完整性验证脚本
检查向量数据的质量和一致性
"""

import os
import chromadb
import numpy as np
from datetime import datetime
from typing import List, Dict, Any
import statistics

def analyze_vector_integrity():
    """分析向量完整性"""
    print("=" * 60)
    print("向量完整性验证报告")
    print("=" * 60)
    print(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 连接数据库
    db_path = "./data/chroma_db"
    collection_name = "knowledge_base"
    
    try:
        client = chromadb.PersistentClient(path=db_path)
        collection = client.get_collection(name=collection_name)
        
        doc_count = collection.count()
        print(f"📊 总文档数: {doc_count:,}")
        
        if doc_count == 0:
            print("❌ 集合中没有文档")
            return False
            
        # 分批获取所有文档进行分析
        batch_size = 50
        all_embeddings = []
        all_metadata = []
        all_documents = []
        
        print(f"\n🔍 分批获取文档数据 (批次大小: {batch_size})...")
        
        for offset in range(0, doc_count, batch_size):
            limit = min(batch_size, doc_count - offset)
            print(f"   获取批次: {offset//batch_size + 1}/{(doc_count + batch_size - 1)//batch_size}")
            
            try:
                # 使用get方法获取数据
                results = collection.get(
                    limit=limit,
                    offset=offset,
                    include=['embeddings', 'metadatas', 'documents']
                )
                
                if results.get('embeddings'):
                    all_embeddings.extend(results['embeddings'])
                if results.get('metadatas'):
                    all_metadata.extend(results['metadatas'])
                if results.get('documents'):
                    all_documents.extend(results['documents'])
                    
            except Exception as e:
                print(f"   ⚠️ 批次获取失败: {e}")
                continue
        
        print(f"✅ 成功获取 {len(all_embeddings)} 个向量")
        
        # 分析向量维度
        analyze_vector_dimensions(all_embeddings)
        
        # 分析向量质量
        analyze_vector_quality(all_embeddings)
        
        # 分析文档内容
        analyze_document_content(all_documents, all_metadata)
        
        return True
        
    except Exception as e:
        print(f"❌ 向量完整性分析失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_vector_dimensions(embeddings: List[List[float]]):
    """分析向量维度"""
    print(f"\n📏 向量维度分析:")
    
    if not embeddings:
        print("   ❌ 没有向量数据")
        return
    
    dimensions = [len(emb) for emb in embeddings if emb]
    
    if not dimensions:
        print("   ❌ 所有向量都为空")
        return
    
    print(f"   向量总数: {len(embeddings):,}")
    print(f"   非空向量数: {len(dimensions):,}")
    print(f"   空向量数: {len(embeddings) - len(dimensions):,}")
    
    if dimensions:
        unique_dims = set(dimensions)
        print(f"   唯一维度数: {len(unique_dims)}")
        print(f"   维度范围: {min(dimensions)} - {max(dimensions)}")
        
        if len(unique_dims) == 1:
            print(f"   ✅ 所有向量维度一致: {dimensions[0]}")
        else:
            print(f"   ⚠️ 向量维度不一致:")
            dim_counts = {}
            for dim in dimensions:
                dim_counts[dim] = dim_counts.get(dim, 0) + 1
            
            for dim, count in sorted(dim_counts.items()):
                print(f"      维度 {dim}: {count} 个向量")

def analyze_vector_quality(embeddings: List[List[float]]):
    """分析向量质量"""
    print(f"\n🎯 向量质量分析:")
    
    if not embeddings:
        print("   ❌ 没有向量数据")
        return
    
    # 过滤空向量
    valid_embeddings = [emb for emb in embeddings if emb and len(emb) > 0]
    
    if not valid_embeddings:
        print("   ❌ 没有有效向量")
        return
    
    print(f"   有效向量数: {len(valid_embeddings):,}")
    
    # 分析向量数值范围
    all_values = []
    zero_vectors = 0
    
    for emb in valid_embeddings:
        all_values.extend(emb)
        if all(abs(v) < 1e-10 for v in emb):
            zero_vectors += 1
    
    if all_values:
        print(f"   数值范围: [{min(all_values):.6f}, {max(all_values):.6f}]")
        print(f"   数值均值: {statistics.mean(all_values):.6f}")
        print(f"   数值标准差: {statistics.stdev(all_values):.6f}")
        print(f"   零向量数: {zero_vectors}")
        
        # 检查异常向量
        inf_count = sum(1 for v in all_values if not np.isfinite(v))
        if inf_count > 0:
            print(f"   ⚠️ 异常数值(inf/nan): {inf_count}")
        else:
            print(f"   ✅ 所有数值都是有限的")
    
    # 计算向量相似度分布（抽样）
    if len(valid_embeddings) > 1:
        sample_size = min(100, len(valid_embeddings))
        sample_embeddings = valid_embeddings[:sample_size]
        
        similarities = []
        for i in range(min(10, len(sample_embeddings))):
            for j in range(i+1, min(10, len(sample_embeddings))):
                emb1 = np.array(sample_embeddings[i])
                emb2 = np.array(sample_embeddings[j])
                
                # 计算余弦相似度
                norm1 = np.linalg.norm(emb1)
                norm2 = np.linalg.norm(emb2)
                
                if norm1 > 0 and norm2 > 0:
                    similarity = np.dot(emb1, emb2) / (norm1 * norm2)
                    similarities.append(similarity)
        
        if similarities:
            print(f"   相似度样本数: {len(similarities)}")
            print(f"   相似度范围: [{min(similarities):.4f}, {max(similarities):.4f}]")
            print(f"   相似度均值: {statistics.mean(similarities):.4f}")

def analyze_document_content(documents: List[str], metadata: List[Dict]):
    """分析文档内容"""
    print(f"\n📄 文档内容分析:")
    
    if not documents:
        print("   ❌ 没有文档内容")
        return
    
    print(f"   文档总数: {len(documents):,}")
    
    # 内容长度分析
    lengths = [len(doc) if doc else 0 for doc in documents]
    empty_docs = sum(1 for length in lengths if length == 0)
    
    print(f"   空文档数: {empty_docs}")
    print(f"   非空文档数: {len(documents) - empty_docs}")
    
    if lengths:
        valid_lengths = [l for l in lengths if l > 0]
        if valid_lengths:
            print(f"   内容长度范围: {min(valid_lengths)} - {max(valid_lengths)} 字符")
            print(f"   平均内容长度: {statistics.mean(valid_lengths):.1f} 字符")
    
    # 元数据分析
    if metadata:
        print(f"\n📋 元数据分析:")
        print(f"   元数据记录数: {len(metadata):,}")
        
        # 统计元数据字段
        all_keys = set()
        for meta in metadata:
            if meta:
                all_keys.update(meta.keys())
        
        print(f"   元数据字段数: {len(all_keys)}")
        print("   字段列表:")
        for key in sorted(all_keys):
            count = sum(1 for meta in metadata if meta and key in meta)
            print(f"      {key}: {count}/{len(metadata)} 记录")
        
        # 检查关键字段
        key_fields = ['filename', 'document_id', 'user_id', 'section_id']
        missing_key_fields = []
        
        for field in key_fields:
            if field not in all_keys:
                missing_key_fields.append(field)
        
        if missing_key_fields:
            print(f"   ⚠️ 缺失关键字段: {missing_key_fields}")
        else:
            print(f"   ✅ 所有关键字段都存在")

def main():
    """主函数"""
    try:
        success = analyze_vector_integrity()
        
        print("\n" + "=" * 60)
        if success:
            print("向量完整性验证完成")
        else:
            print("向量完整性验证失败")
        print("=" * 60)
        
        return success
        
    except Exception as e:
        print(f"\n❌ 验证过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
