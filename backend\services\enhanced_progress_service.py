"""
增强版进度追踪服务
支持跨进程的状态共享和实时更新，基于Redis存储
"""

import time
import json
import uuid
from typing import Dict, Optional, Callable, List, Any
from enum import Enum
from dataclasses import dataclass, asdict
import redis
from loguru import logger
import os


class ProgressStatus(Enum):
    """进度状态枚举"""
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class ProgressInfo:
    """进度信息数据类"""
    task_id: str
    title: str
    detail: str
    percentage: float
    status: ProgressStatus
    start_time: float
    end_time: Optional[float] = None
    error_message: Optional[str] = None
    user_id: Optional[str] = None
    task_type: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['status'] = self.status.value
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ProgressInfo':
        """从字典创建实例"""
        if isinstance(data.get('status'), str):
            data['status'] = ProgressStatus(data['status'])
        return cls(**data)


class EnhancedProgressService:
    """增强版进度追踪服务"""
    
    def __init__(self, redis_url: str = None):
        self.redis_url = redis_url or os.getenv("REDIS_URL", "redis://localhost:6379/0")
        self.redis_client = redis.Redis.from_url(self.redis_url, decode_responses=True)
        self.progress_prefix = "task_progress:"
        self.user_tasks_prefix = "user_tasks:"
        self.callbacks: Dict[str, Callable] = {}
        
        # 测试Redis连接
        try:
            self.redis_client.ping()
            logger.info("Redis连接成功")
        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            raise
    
    def _get_progress_key(self, task_id: str) -> str:
        """获取进度存储键"""
        return f"{self.progress_prefix}{task_id}"
    
    def _get_user_tasks_key(self, user_id: str) -> str:
        """获取用户任务列表键"""
        return f"{self.user_tasks_prefix}{user_id}"
    
    def create_task(
        self, 
        task_id: str, 
        title: str, 
        detail: str = "",
        user_id: str = None,
        task_type: str = None,
        metadata: Dict[str, Any] = None
    ) -> ProgressInfo:
        """创建新的进度任务"""
        progress = ProgressInfo(
            task_id=task_id,
            title=title,
            detail=detail,
            percentage=0.0,
            status=ProgressStatus.NOT_STARTED,
            start_time=time.time(),
            user_id=user_id,
            task_type=task_type,
            metadata=metadata or {}
        )
        
        # 存储到Redis
        progress_key = self._get_progress_key(task_id)
        self.redis_client.hset(progress_key, mapping=self._serialize_progress(progress))
        self.redis_client.expire(progress_key, 3600)  # 1小时过期
        
        # 添加到用户任务列表
        if user_id:
            user_tasks_key = self._get_user_tasks_key(user_id)
            self.redis_client.sadd(user_tasks_key, task_id)
            self.redis_client.expire(user_tasks_key, 3600)
        
        logger.info(f"创建进度任务: {task_id} - {title}")
        return progress
    
    def _serialize_progress(self, progress: ProgressInfo) -> Dict[str, str]:
        """序列化进度信息"""
        data = progress.to_dict()
        # 将所有值转换为字符串
        return {k: json.dumps(v) if isinstance(v, (dict, list)) else str(v) for k, v in data.items()}
    
    def _deserialize_progress(self, data: Dict[str, str]) -> ProgressInfo:
        """反序列化进度信息"""
        # 转换数据类型
        converted_data = {}
        for k, v in data.items():
            if k == 'percentage':
                converted_data[k] = float(v)
            elif k in ['start_time', 'end_time']:
                converted_data[k] = float(v) if v and v != 'None' else None
            elif k in ['metadata']:
                try:
                    converted_data[k] = json.loads(v) if v and v != 'None' else None
                except:
                    converted_data[k] = None
            elif k == 'status':
                converted_data[k] = ProgressStatus(v)
            else:
                converted_data[k] = v if v != 'None' else None
        
        return ProgressInfo(**converted_data)
    
    def update_progress(
        self, 
        task_id: str, 
        percentage: float, 
        detail: str = "", 
        status: Optional[ProgressStatus] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Optional[ProgressInfo]:
        """更新进度"""
        progress_key = self._get_progress_key(task_id)
        
        # 检查任务是否存在
        if not self.redis_client.exists(progress_key):
            logger.warning(f"进度任务不存在: {task_id}")
            return None
        
        # 获取当前进度
        current_data = self.redis_client.hgetall(progress_key)
        if not current_data:
            logger.warning(f"无法获取进度数据: {task_id}")
            return None
        
        progress = self._deserialize_progress(current_data)
        
        # 更新进度
        progress.percentage = max(0.0, min(100.0, percentage))
        
        if detail:
            progress.detail = detail
        
        if metadata:
            progress.metadata = {**(progress.metadata or {}), **metadata}
        
        if status:
            progress.status = status
            if status in [ProgressStatus.COMPLETED, ProgressStatus.FAILED, ProgressStatus.CANCELLED]:
                progress.end_time = time.time()
        elif percentage >= 100.0:
            progress.status = ProgressStatus.COMPLETED
            progress.end_time = time.time()
        elif percentage > 0.0:
            progress.status = ProgressStatus.IN_PROGRESS
        
        # 保存更新
        self.redis_client.hset(progress_key, mapping=self._serialize_progress(progress))
        
        logger.debug(f"更新进度: {task_id} - {percentage}% - {detail}")
        
        # 执行回调
        if task_id in self.callbacks:
            try:
                self.callbacks[task_id](progress)
            except Exception as e:
                logger.error(f"进度回调执行失败: {e}")
        
        return progress
    
    def complete_task(self, task_id: str, detail: str = "任务完成") -> Optional[ProgressInfo]:
        """完成任务"""
        return self.update_progress(task_id, 100.0, detail, ProgressStatus.COMPLETED)
    
    def fail_task(self, task_id: str, error_message: str) -> Optional[ProgressInfo]:
        """任务失败"""
        progress_key = self._get_progress_key(task_id)
        
        if not self.redis_client.exists(progress_key):
            logger.warning(f"进度任务不存在: {task_id}")
            return None
        
        # 获取当前进度
        current_data = self.redis_client.hgetall(progress_key)
        progress = self._deserialize_progress(current_data)
        
        # 更新为失败状态
        progress.status = ProgressStatus.FAILED
        progress.error_message = error_message
        progress.end_time = time.time()
        
        # 保存更新
        self.redis_client.hset(progress_key, mapping=self._serialize_progress(progress))
        
        logger.error(f"任务失败: {task_id} - {error_message}")
        return progress
    
    def cancel_task(self, task_id: str) -> Optional[ProgressInfo]:
        """取消任务"""
        return self.update_progress(task_id, 0.0, "任务已取消", ProgressStatus.CANCELLED)
    
    def get_progress(self, task_id: str) -> Optional[ProgressInfo]:
        """获取进度信息"""
        progress_key = self._get_progress_key(task_id)
        data = self.redis_client.hgetall(progress_key)
        
        if not data:
            return None
        
        try:
            return self._deserialize_progress(data)
        except Exception as e:
            logger.error(f"反序列化进度数据失败: {task_id}, {e}")
            return None
    
    def get_user_tasks(self, user_id: str) -> List[ProgressInfo]:
        """获取用户的所有任务"""
        user_tasks_key = self._get_user_tasks_key(user_id)
        task_ids = self.redis_client.smembers(user_tasks_key)
        
        tasks = []
        for task_id in task_ids:
            progress = self.get_progress(task_id)
            if progress:
                tasks.append(progress)
        
        return tasks
    
    def get_all_progress(self) -> Dict[str, ProgressInfo]:
        """获取所有进度信息"""
        pattern = f"{self.progress_prefix}*"
        keys = self.redis_client.keys(pattern)
        
        progress_dict = {}
        for key in keys:
            task_id = key.replace(self.progress_prefix, "")
            progress = self.get_progress(task_id)
            if progress:
                progress_dict[task_id] = progress
        
        return progress_dict
    
    def remove_task(self, task_id: str, user_id: str = None) -> bool:
        """移除任务"""
        progress_key = self._get_progress_key(task_id)
        
        # 删除进度数据
        deleted = self.redis_client.delete(progress_key)
        
        # 从用户任务列表中移除
        if user_id:
            user_tasks_key = self._get_user_tasks_key(user_id)
            self.redis_client.srem(user_tasks_key, task_id)
        
        # 移除回调
        if task_id in self.callbacks:
            del self.callbacks[task_id]
        
        if deleted:
            logger.info(f"移除进度任务: {task_id}")
            return True
        return False
    
    def set_callback(self, task_id: str, callback: Callable[[ProgressInfo], None]):
        """设置进度回调"""
        self.callbacks[task_id] = callback
    
    def cleanup_completed_tasks(self, max_age_seconds: int = 3600) -> int:
        """清理已完成的旧任务"""
        current_time = time.time()
        pattern = f"{self.progress_prefix}*"
        keys = self.redis_client.keys(pattern)
        
        cleaned_count = 0
        for key in keys:
            task_id = key.replace(self.progress_prefix, "")
            progress = self.get_progress(task_id)
            
            if (progress and 
                progress.status in [ProgressStatus.COMPLETED, ProgressStatus.FAILED, ProgressStatus.CANCELLED] and
                progress.end_time and
                current_time - progress.end_time > max_age_seconds):
                
                if self.remove_task(task_id, progress.user_id):
                    cleaned_count += 1
        
        if cleaned_count > 0:
            logger.info(f"清理了 {cleaned_count} 个过期任务")
        
        return cleaned_count


# 全局增强进度服务实例
enhanced_progress_service = EnhancedProgressService()


def get_enhanced_progress_service() -> EnhancedProgressService:
    """获取增强进度服务实例"""
    return enhanced_progress_service
