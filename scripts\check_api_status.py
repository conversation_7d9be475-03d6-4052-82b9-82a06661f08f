import requests
import json
import sys

def check_api_status():
    """检查API状态和任务结果"""
    try:
        # 检查最新的会议转录任务
        url = "http://localhost:8002/api/v1/audio/results"
        
        # 先尝试不带认证
        response = requests.get(url)
        
        if response.status_code == 401:
            print("需要认证，尝试登录...")
            # 登录获取token
            login_url = "http://localhost:8002/api/v1/auth/login"
            login_data = {
                "username": "admin",
                "password": "admin123"
            }
            
            login_response = requests.post(login_url, json=login_data)
            if login_response.status_code == 200:
                token = login_response.json().get("access_token")
                headers = {"Authorization": f"Bearer {token}"}
                
                # 重新请求
                response = requests.get(url, headers=headers)
            else:
                print(f"登录失败: {login_response.status_code}")
                return
        
        if response.status_code == 200:
            data = response.json()
            print(f"API响应成功，共找到 {len(data)} 个任务")
            
            # 查找最新的会议转录任务
            meeting_tasks = [task for task in data if task.get('task_type') == 'meeting_transcription']
            if meeting_tasks:
                latest_task = max(meeting_tasks, key=lambda x: x.get('created_at', ''))
                print(f"\n最新会议转录任务:")
                print(f"任务ID: {latest_task.get('task_id')}")
                print(f"状态: {latest_task.get('status')}")
                print(f"进度: {latest_task.get('progress', 0)}%")
                print(f"创建时间: {latest_task.get('created_at')}")
                
                # 检查是否有结果
                if latest_task.get('result'):
                    result = latest_task['result']
                    print(f"\n任务结果:")
                    print(f"处理状态: {result.get('status')}")
                    
                    if 'speech_segments' in result:
                        segments = result['speech_segments']
                        print(f"语音片段数量: {len(segments)}")
                        
                        # 显示前几个片段
                        for i, segment in enumerate(segments[:3]):
                            print(f"片段 {i+1}: {segment.get('speaker_name', 'Unknown')} - {segment.get('text', '')}")
                    else:
                        print("未找到speech_segments数据")
                        print(f"结果键: {list(result.keys())}")
                else:
                    print("任务无结果数据")
            else:
                print("未找到会议转录任务")
        else:
            print(f"API请求失败: {response.status_code}")
            print(f"响应: {response.text}")
            
    except Exception as e:
        print(f"检查API状态时出错: {e}")

if __name__ == "__main__":
    check_api_status()
