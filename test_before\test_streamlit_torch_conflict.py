#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Streamlit和Torch冲突的脚本
"""

import sys
import os
import time

def test_torch_only():
    """测试只导入torch"""
    print("=" * 50)
    print("测试1: 只导入torch")
    print("=" * 50)
    
    try:
        import torch
        print(f"✅ torch导入成功")
        print(f"   版本: {torch.__version__}")
        print(f"   路径: {torch.__file__}")
        print(f"   CUDA可用: {torch.cuda.is_available()}")
        
        # 测试简单操作
        x = torch.tensor([1.0, 2.0, 3.0])
        result = x.sum().item()
        print(f"   简单操作测试: {result}")
        
        return True
    except Exception as e:
        print(f"❌ torch导入失败: {e}")
        return False

def test_streamlit_only():
    """测试只导入streamlit"""
    print("=" * 50)
    print("测试2: 只导入streamlit")
    print("=" * 50)
    
    try:
        import streamlit as st
        print(f"✅ streamlit导入成功")
        print(f"   版本: {st.__version__}")
        print(f"   路径: {st.__file__}")
        return True
    except Exception as e:
        print(f"❌ streamlit导入失败: {e}")
        return False

def test_both_imports():
    """测试同时导入两者"""
    print("=" * 50)
    print("测试3: 同时导入streamlit和torch")
    print("=" * 50)
    
    try:
        # 先导入streamlit
        print("步骤1: 导入streamlit...")
        import streamlit as st
        print(f"✅ streamlit导入成功: {st.__version__}")
        
        # 再导入torch
        print("步骤2: 导入torch...")
        import torch
        print(f"✅ torch导入成功: {torch.__version__}")
        print(f"   CUDA可用: {torch.cuda.is_available()}")
        
        # 测试torch操作
        print("步骤3: 测试torch操作...")
        x = torch.tensor([1.0, 2.0, 3.0])
        result = x.sum().item()
        print(f"✅ torch操作成功: {result}")
        
        return True
    except Exception as e:
        print(f"❌ 同时导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_reverse_order():
    """测试反向导入顺序"""
    print("=" * 50)
    print("测试4: 先导入torch再导入streamlit")
    print("=" * 50)
    
    try:
        # 先导入torch
        print("步骤1: 导入torch...")
        import torch
        print(f"✅ torch导入成功: {torch.__version__}")
        
        # 再导入streamlit
        print("步骤2: 导入streamlit...")
        import streamlit as st
        print(f"✅ streamlit导入成功: {st.__version__}")
        
        # 再次测试torch
        print("步骤3: 再次测试torch...")
        x = torch.tensor([1.0, 2.0, 3.0])
        result = x.sum().item()
        print(f"✅ torch操作仍然正常: {result}")
        
        return True
    except Exception as e:
        print(f"❌ 反向导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_environment():
    """检查环境信息"""
    print("=" * 50)
    print("环境信息检查")
    print("=" * 50)
    
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"当前工作目录: {os.getcwd()}")
    print(f"虚拟环境: {'是' if '.venv' in sys.prefix else '否'}")
    
    print("\nPython路径列表:")
    for i, path in enumerate(sys.path):
        print(f"  {i}: {path}")
    
    print("\n相关环境变量:")
    env_vars = ['PYTHONPATH', 'TORCH_HOME', 'CUDA_HOME', 'CUDA_PATH']
    for var in env_vars:
        value = os.environ.get(var, 'Not set')
        print(f"  {var}: {value}")

def main():
    """主测试函数"""
    print("🧪 Streamlit和Torch冲突诊断测试")
    print(f"⏰ 时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查环境
    check_environment()
    
    # 运行测试
    results = []
    
    # 测试1: 只导入torch
    results.append(("torch_only", test_torch_only()))
    
    # 测试2: 只导入streamlit  
    results.append(("streamlit_only", test_streamlit_only()))
    
    # 重新启动Python进程来避免模块缓存影响
    print("\n" + "=" * 50)
    print("注意: 以下测试需要在新的Python进程中运行以避免模块缓存")
    print("=" * 50)
    
    # 显示结果
    print("\n" + "=" * 50)
    print("测试结果总结")
    print("=" * 50)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    if all(result for _, result in results):
        print("\n🎉 基础测试全部通过！")
        print("建议在新的Python进程中测试组合导入")
    else:
        print("\n⚠️ 发现问题，需要进一步诊断")

if __name__ == "__main__":
    main()
