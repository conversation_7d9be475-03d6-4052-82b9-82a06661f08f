#!/usr/bin/env python3
"""
检查task_records表结构
"""

import sqlite3

def check_task_records_structure():
    """检查task_records表结构"""
    db_path = "data/speech_platform.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 获取task_records表结构
        cursor.execute("PRAGMA table_info(task_records)")
        columns = cursor.fetchall()
        
        print("task_records表结构:")
        print("=" * 50)
        for col in columns:
            cid, name, type_, notnull, default_value, pk = col
            print(f"  {name}: {type_} (NOT NULL: {bool(notnull)}, PK: {bool(pk)})")
        
        # 查看最新的几条记录
        print("\n最新的5条task_records记录:")
        print("=" * 50)
        cursor.execute("""
            SELECT * FROM task_records 
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        
        records = cursor.fetchall()
        column_names = [description[0] for description in cursor.description]
        
        for i, record in enumerate(records, 1):
            print(f"\n记录 {i}:")
            for j, value in enumerate(record):
                print(f"  {column_names[j]}: {value}")
        
        # 检查processing_results表
        print("\n\nprocessing_results表结构:")
        print("=" * 50)
        cursor.execute("PRAGMA table_info(processing_results)")
        columns = cursor.fetchall()
        
        for col in columns:
            cid, name, type_, notnull, default_value, pk = col
            print(f"  {name}: {type_} (NOT NULL: {bool(notnull)}, PK: {bool(pk)})")
        
        # 查看最新的处理结果
        print("\n最新的5条processing_results记录:")
        print("=" * 50)
        cursor.execute("""
            SELECT * FROM processing_results 
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        
        results = cursor.fetchall()
        column_names = [description[0] for description in cursor.description]
        
        for i, result in enumerate(results, 1):
            print(f"\n结果 {i}:")
            for j, value in enumerate(result):
                if column_names[j] == 'result_data' and value:
                    print(f"  {column_names[j]}: {str(value)[:200]}...")
                else:
                    print(f"  {column_names[j]}: {value}")
                    
    except Exception as e:
        print(f"检查表结构时出错: {e}")
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    check_task_records_structure()
