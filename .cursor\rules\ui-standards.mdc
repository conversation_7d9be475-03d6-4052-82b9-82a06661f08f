---
description: 
globs: 
alwaysApply: true
---
# UI统一规范

## 前端UI框架标准

### 组件库规范
- 统一使用 **Element Plus 2.3.9** 作为基础组件库
- 主题色彩：黑色系 作为主色调
- 图标库：统一使用 @element-plus/icons-vue
- 响应式设计：支持桌面端和移动端适配

### 页面布局规范
参考主要页面布局：
- 主页布局：[frontend/src/views/Home.vue](mdc:frontend/src/views/Home.vue)
- 文档管理：[frontend/src/views/DocumentManager.vue](mdc:frontend/src/views/DocumentManager.vue)
- 知识库界面：[frontend/src/views/KnowledgeBase.vue](mdc:frontend/src/views/KnowledgeBase.vue)
- 仪表盘：[frontend/src/views/Dashboard.vue](mdc:frontend/src/views/Dashboard.vue)

### 组件规范
```vue
<!-- 标准Vue组件模板 -->
<template>
  <div class="component-name">
    <!-- 使用Element Plus组件 -->
    <el-card class="component-card">
      <!-- 内容区域 -->
    </el-card>
  </div>
</template>

<script setup>
// Composition API模式
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 组件逻辑
</script>

<style scoped lang="scss">
.component-name {
  // 组件样式
}
</style>
```

### 样式规范
- 全局样式：[frontend/src/assets/styles/main.scss](mdc:frontend/src/assets/styles/main.scss)
- 公共CSS：[frontend/src/assets/styles/global.css](mdc:frontend/src/assets/styles/global.css)
- 使用SCSS预处理器
- 组件样式必须使用 `scoped` 限定作用域

### 交互规范
- 加载状态：使用 `el-loading` 指令
- 消息提示：统一使用 `ElMessage` 和 `ElNotification`
- 确认对话框：使用 `ElMessageBox.confirm()`
- 表单验证：使用Element Plus表单验证规则

### 错误处理UI
- 统一的错误页面布局
- 友好的错误提示信息
- 支持错误重试机制
- 错误日志记录和上报

### 数据可视化
- 图表组件：使用 ECharts 5.4.3
- 图表封装：[frontend/src/components/common/](mdc:frontend/src/components/common)
- 实时数据更新：WebSocket + Vue响应式数据

### 国际化支持
- 中文为主要语言
- 预留国际化扩展接口
- 统一的文案管理

