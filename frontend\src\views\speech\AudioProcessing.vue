<template>
  <div class="audio-processing-container">
    <!-- 顶部导航 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-brand">
          <div class="brand-logo">🎵</div>
          <span class="brand-text">音频处理中心</span>
        </div>
        <div class="header-actions">
          <router-link to="/dashboard" class="btn-outline">返回控制台</router-link>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="page-main">
      <div class="container">
        <!-- 功能介绍 -->
        <div class="intro-section">
          <h1 class="page-title">
            <span class="title-main">智能音频处理平台</span>
            <span class="title-sub gradient-text">集成VAD检测、语音识别、说话人识别、会议转录等功能</span>
          </h1>
        </div>

        <!-- 主要功能区域 -->
        <div class="main-content">
          <!-- 左侧配置面板 -->
          <div class="config-panel">
            <div class="panel-header">
              <h3>🔧 处理配置</h3>
            </div>
            
            <!-- 处理模式选择 -->
            <div class="config-section">
              <label class="config-label">处理模式</label>
              <el-select v-model="processingMode" placeholder="选择处理模式" class="full-width">
                <el-option
                  v-for="mode in processingModes"
                  :key="mode.value"
                  :label="mode.label"
                  :value="mode.value"
                >
                  <span class="mode-icon">{{ mode.icon }}</span>
                  <span>{{ mode.label }}</span>
                </el-option>
              </el-select>
            </div>

            <!-- 文件上传区域 -->
            <div class="config-section">
              <label class="config-label">文件上传</label>
              <div class="upload-mode-switch">
                <el-radio-group v-model="uploadMode" size="small">
                  <el-radio-button label="single">单文件</el-radio-button>
                  <el-radio-button label="batch">批量</el-radio-button>
                </el-radio-group>
              </div>
              
              <!-- 文件上传组件 -->
              <AudioUploader
                :mode="uploadMode"
                :accept="audioFormats"
                :max-size="maxFileSize"
                @files-selected="handleFilesSelected"
                @upload-progress="handleUploadProgress"
              />
            </div>

            <!-- 高级配置 -->
            <div class="config-section" v-if="showAdvancedConfig">
              <label class="config-label">高级配置</label>
              <AudioConfigPanel
                :processing-mode="processingMode"
                v-model="audioConfig"
                @config-changed="handleConfigChanged"
              />
            </div>

            <!-- 操作按钮 -->
            <div class="action-buttons">
              <el-button
                type="primary"
                :disabled="!canProcess"
                :loading="isProcessing"
                @click="startProcessing"
                class="process-btn"
              >
                <span v-if="!isProcessing">🚀 开始处理</span>
                <span v-else>⏳ 处理中...</span>
              </el-button>
              
              <el-button
                v-if="uploadMode === 'batch'"
                @click="clearQueue"
                :disabled="!hasFiles"
              >
                🗑️ 清空队列
              </el-button>
            </div>
          </div>

          <!-- 右侧主要工作区 -->
          <div class="work-area">
            <!-- 文件列表/预览 -->
            <div class="file-section" v-if="hasFiles">
              <div class="section-header">
                <h3>📁 文件管理</h3>
                <el-button
                  text
                  @click="toggleAdvancedConfig"
                  :icon="showAdvancedConfig ? 'ArrowUp' : 'ArrowDown'"
                >
                  {{ showAdvancedConfig ? '隐藏' : '显示' }}高级配置
                </el-button>
              </div>
              
              <!-- 单文件预览 -->
              <AudioPreview
                v-if="uploadMode === 'single' && selectedFiles.length === 1"
                :file="selectedFiles[0]"
                :show-waveform="true"
                :show-spectrum="true"
              />
              
              <!-- 批量文件列表 -->
              <BatchFileList
                v-else-if="uploadMode === 'batch'"
                :files="selectedFiles"
                :processing-status="processingStatus"
                @remove-file="removeFile"
                @preview-file="previewFile"
              />
            </div>

            <!-- 处理进度 -->
            <div class="progress-section" v-if="isProcessing">
              <ProcessingProgress
                :current-task="currentTask"
                :overall-progress="overallProgress"
                :task-details="taskDetails"
                :websocket-connected="websocketConnected"
              />
            </div>

            <!-- 处理结果 -->
            <div class="results-section" v-if="hasResults">
              <div class="section-header">
                <h3>📊 处理结果</h3>
                <div class="result-actions">
                  <el-button @click="exportResults" :icon="Download">导出结果</el-button>
                  <el-button @click="clearResults" :icon="Delete">清空结果</el-button>
                </div>
              </div>
              
              <!-- 结果展示组件 -->
              <ProcessingResults
                :results="processingResults"
                :processing-mode="processingMode"
                @result-selected="handleResultSelected"
              />
            </div>

            <!-- 空状态 -->
            <div class="empty-state" v-if="!hasFiles && !isProcessing && !hasResults">
              <div class="empty-content">
                <div class="empty-icon">🎵</div>
                <h3>开始音频处理</h3>
                <p>请在左侧上传音频文件并选择处理模式</p>
                <div class="supported-formats">
                  <h4>支持的格式：</h4>
                  <div class="format-tags">
                    <el-tag v-for="format in supportedFormats" :key="format" size="small">
                      {{ format }}
                    </el-tag>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Download, Delete, ArrowUp, ArrowDown } from '@element-plus/icons-vue'

// 导入组件
import AudioUploader from '@/components/audio/AudioUploader.vue'
import AudioConfigPanel from '@/components/audio/AudioConfigPanel.vue'
import AudioPreview from '@/components/audio/AudioPreview.vue'
import BatchFileList from '@/components/audio/BatchFileList.vue'
import ProcessingProgress from '@/components/audio/ProcessingProgress.vue'
import ProcessingResults from '@/components/audio/ProcessingResults.vue'

// 导入API和工具
import { audioProcessingAPI } from '@/api/audioProcessing'
import { useWebSocket } from '@/composables/useWebSocket'
import { useAudioProcessing } from '@/composables/useAudioProcessing'

// 响应式数据
const processingMode = ref('vad_detection')
const uploadMode = ref('single')
const selectedFiles = ref([])
const audioConfig = ref({})
const isProcessing = ref(false)
const showAdvancedConfig = ref(false)
const processingResults = ref([])
const processingStatus = ref({})
const currentTask = ref(null)
const overallProgress = ref(0)
const taskDetails = ref({})

// WebSocket连接
const { connected: websocketConnected, connect, disconnect, onMessage } = useWebSocket()

// 音频处理逻辑
const {
  startAudioProcessing,
  cancelProcessing,
  getProcessingStatus
} = useAudioProcessing()

// 处理模式配置
const processingModes = [
  { value: 'vad_detection', label: 'VAD语音活动检测', icon: '🎯' },
  { value: 'speech_recognition', label: '语音识别', icon: '🗣️' },
  { value: 'speaker_recognition', label: '说话人识别', icon: '👥' },
  { value: 'meeting_transcription', label: '会议语音转录', icon: '🎤' },
  { value: 'audio_preprocessing', label: '音频预处理', icon: '🔧' },
  { value: 'quality_analysis', label: '质量分析', icon: '📊' },
  { value: 'comprehensive_analysis', label: '综合分析', icon: '🔬' }
]

// 支持的音频格式
const audioFormats = ['.wav', '.mp3', '.m4a', '.aac', '.flac', '.ogg']
const supportedFormats = ['WAV', 'MP3', 'M4A', 'AAC', 'FLAC', 'OGG']
const maxFileSize = 200 * 1024 * 1024 // 200MB

// 计算属性
const hasFiles = computed(() => selectedFiles.value.length > 0)
const hasResults = computed(() => processingResults.value.length > 0)
const canProcess = computed(() => hasFiles.value && !isProcessing.value)

// 事件处理
const handleFilesSelected = (files) => {
  selectedFiles.value = files
  ElMessage.success(`已选择 ${files.length} 个文件`)
}

const handleUploadProgress = (progress) => {
  // 处理上传进度
}

const handleConfigChanged = (config) => {
  audioConfig.value = { ...audioConfig.value, ...config }
}

const toggleAdvancedConfig = () => {
  showAdvancedConfig.value = !showAdvancedConfig.value
}

const startProcessing = async () => {
  try {
    isProcessing.value = true
    
    const result = await startAudioProcessing({
      files: selectedFiles.value,
      mode: processingMode.value,
      config: audioConfig.value
    })
    
    currentTask.value = result.taskId
    ElMessage.success('处理任务已启动')
    
  } catch (error) {
    ElMessage.error(`启动处理失败: ${error.message}`)
    isProcessing.value = false
  }
}

const clearQueue = () => {
  selectedFiles.value = []
  processingStatus.value = {}
  ElMessage.info('文件队列已清空')
}

const removeFile = (index) => {
  selectedFiles.value.splice(index, 1)
}

const previewFile = (file) => {
  // 预览文件逻辑
}

const handleResultSelected = (result) => {
  // 处理结果选择
}

const exportResults = () => {
  // 导出结果逻辑
}

const clearResults = () => {
  processingResults.value = []
  ElMessage.info('结果已清空')
}

// WebSocket消息处理
onMessage((data) => {
  if (data.type === 'progress') {
    overallProgress.value = data.progress
    taskDetails.value = data.details
  } else if (data.type === 'task_complete') {
    isProcessing.value = false
    processingResults.value.push(data.result)
    ElMessage.success('处理完成')
  } else if (data.type === 'task_error') {
    isProcessing.value = false
    ElMessage.error(`处理失败: ${data.error}`)
  }
})

// 生命周期
onMounted(() => {
  connect()
})

onUnmounted(() => {
  disconnect()
})
</script>

<style scoped>
.audio-processing-container {
  min-height: 100vh;
  background: var(--primary-bg);
  color: var(--text-primary);
}

.page-header {
  background: rgba(26, 26, 26, 0.8);
  backdrop-filter: blur(20px);
  border-bottom: 1px solid var(--border-color);
  padding: 0 var(--spacing-lg);
}

.header-content {
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 80px;
}

.header-brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.brand-logo {
  font-size: 2rem;
}

.brand-text {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
}

.main-content {
  display: grid;
  grid-template-columns: 350px 1fr;
  gap: var(--spacing-xl);
  margin-top: var(--spacing-xl);
}

.config-panel {
  background: var(--card-bg);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  height: fit-content;
  border: 1px solid var(--border-color);
}

.panel-header h3 {
  margin: 0 0 var(--spacing-lg) 0;
  color: var(--text-primary);
}

.config-section {
  margin-bottom: var(--spacing-lg);
}

.config-label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-weight: 500;
  color: var(--text-secondary);
}

.full-width {
  width: 100%;
}

.upload-mode-switch {
  margin-bottom: var(--spacing-md);
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.process-btn {
  width: 100%;
  height: 48px;
  font-size: 1.1rem;
}

.work-area {
  background: var(--card-bg);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
  min-height: 600px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.section-header h3 {
  margin: 0;
  color: var(--text-primary);
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 400px;
}

.empty-content {
  text-align: center;
  max-width: 400px;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: var(--spacing-lg);
}

.empty-content h3 {
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
}

.empty-content p {
  color: var(--text-secondary);
  margin-bottom: var(--spacing-lg);
}

.supported-formats h4 {
  margin-bottom: var(--spacing-sm);
  color: var(--text-secondary);
}

.format-tags {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  justify-content: center;
}

.mode-icon {
  margin-right: var(--spacing-sm);
}
</style>
