@echo off
REM ===========================================
REM 语音处理智能平台 Docker 启动脚本 (Windows)
REM ===========================================

setlocal enabledelayedexpansion

echo 🚀 语音处理智能平台 Docker 部署启动
echo ==========================================

REM 切换到脚本目录的上级目录
cd /d "%~dp0\.."

REM 检查Docker和Docker Compose
echo [INFO] 检查系统环境...
docker --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker未安装，请先安装Docker Desktop
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Docker Compose未安装，请先安装Docker Compose
    pause
    exit /b 1
)

echo [SUCCESS] Docker环境检查通过

REM 检查环境变量文件
echo [INFO] 检查环境配置文件...
if not exist ".env" (
    if exist ".env.template" (
        echo [WARNING] .env文件不存在，从模板创建...
        copy ".env.template" ".env"
        echo [WARNING] 请编辑.env文件，配置您的环境变量
        echo [WARNING] 特别注意OLLAMA_BASE_URL和EMBEDDING_MODEL配置
        set /p choice="是否现在编辑.env文件? (y/n): "
        if /i "!choice!"=="y" (
            notepad .env
        )
    ) else (
        echo [ERROR] .env.template文件不存在
        pause
        exit /b 1
    )
)

echo [SUCCESS] 环境配置文件检查完成

REM 创建数据目录
echo [INFO] 创建数据持久化目录...
if not exist "volumes" mkdir volumes
if not exist "volumes\data" mkdir volumes\data
if not exist "volumes\logs" mkdir volumes\logs
if not exist "volumes\models" mkdir volumes\models
if not exist "volumes\uploads" mkdir volumes\uploads
if not exist "volumes\redis_data" mkdir volumes\redis_data
if not exist "volumes\data\chroma_db" mkdir volumes\data\chroma_db

echo [SUCCESS] 数据目录创建完成

REM 检查虚拟环境
echo [INFO] 检查虚拟环境...
if not exist "..\.venv" (
    echo [ERROR] 项目根目录的.venv虚拟环境不存在
    echo [ERROR] 请先在项目根目录创建并配置虚拟环境
    echo [ERROR] 运行: python -m venv .venv
    echo [ERROR] 然后: .venv\Scripts\activate
    echo [ERROR] 最后: pip install -r requirements.txt
    pause
    exit /b 1
)

echo [SUCCESS] 虚拟环境检查通过

REM 检查Ollama服务
echo [INFO] 检查Ollama服务...
REM 这里可以添加Ollama连接检查，但在Windows批处理中比较复杂
echo [WARNING] 请确保Ollama服务正在运行并且nomic-embed-text模型已安装

REM 构建Docker镜像
echo [INFO] 构建Docker镜像...
echo [INFO] 构建Backend服务镜像...
docker-compose build backend
if errorlevel 1 (
    echo [ERROR] Backend镜像构建失败
    pause
    exit /b 1
)

echo [INFO] 构建Celery Worker服务镜像...
docker-compose build celery-worker
if errorlevel 1 (
    echo [ERROR] Celery Worker镜像构建失败
    pause
    exit /b 1
)

echo [SUCCESS] Docker镜像构建完成

REM 启动服务
echo [INFO] 启动服务...
docker-compose up -d redis backend celery-worker
if errorlevel 1 (
    echo [ERROR] 服务启动失败
    docker-compose logs
    pause
    exit /b 1
)

echo [INFO] 等待服务启动...
timeout /t 10 /nobreak >nul

REM 检查服务状态
docker-compose ps | findstr "Up" >nul
if errorlevel 1 (
    echo [ERROR] 服务启动失败
    docker-compose logs
    pause
    exit /b 1
)

echo [SUCCESS] 服务启动成功
echo.
echo [INFO] 服务状态:
docker-compose ps

echo.
echo [INFO] 服务访问地址:
echo   Backend API: http://localhost:8002
echo   健康检查: http://localhost:8002/health
echo   API文档: http://localhost:8002/docs

set /p choice="是否启动Flower监控服务? (y/n): "
if /i "!choice!"=="y" (
    docker-compose --profile monitoring up -d flower
    echo   Flower监控: http://localhost:5555 (admin:admin123)
)

echo.
echo [SUCCESS] 🎉 部署完成！
echo.
echo [INFO] 常用命令:
echo   查看状态: docker-compose ps
echo   查看日志: docker-compose logs -f
echo   停止服务: scripts\stop.bat
echo   重启服务: docker-compose restart
echo   健康检查: scripts\health-check.bat

set /p choice="是否查看实时日志? (y/n): "
if /i "!choice!"=="y" (
    echo [INFO] 显示服务日志 (Ctrl+C退出):
    docker-compose logs -f
)

pause
