#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查Redis中的任务状态和WebSocket监控问题
"""

import redis
import json
from datetime import datetime
from celery.result import AsyncResult
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Redis连接配置
redis_client = redis.Redis(
    host='localhost',
    port=6379,
    db=0,
    decode_responses=True
)

def check_specific_task(task_id: str):
    """检查特定任务的状态"""
    print(f"🔍 检查任务: {task_id}")
    print("=" * 80)
    
    try:
        # 1. 检查Redis中的进度数据
        progress_key = f"task_progress:{task_id}"
        progress_data = redis_client.hgetall(progress_key)
        
        print("📊 Redis进度数据:")
        if progress_data:
            for field, value in progress_data.items():
                if field in ['start_time', 'update_time'] and value:
                    try:
                        timestamp = float(value)
                        readable_time = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                        print(f"  {field}: {value} ({readable_time})")
                    except:
                        print(f"  {field}: {value}")
                else:
                    print(f"  {field}: {value}")
        else:
            print("  ❌ 没有找到进度数据")
        
        # 2. 检查Celery任务状态
        print("\n🔄 Celery任务状态:")
        try:
            from backend.core.task_queue import celery_app
            result = AsyncResult(task_id, app=celery_app)
            
            print(f"  状态: {result.state}")
            print(f"  就绪: {result.ready()}")
            print(f"  成功: {result.successful() if result.ready() else 'N/A'}")
            print(f"  失败: {result.failed() if result.ready() else 'N/A'}")
            
            if result.ready():
                if result.successful():
                    print(f"  结果: {str(result.result)[:200]}...")
                elif result.failed():
                    print(f"  错误: {result.traceback}")
        except Exception as e:
            print(f"  ❌ 无法获取Celery状态: {e}")
        
        # 3. 检查数据库任务记录
        print("\n💾 数据库任务记录:")
        try:
            from backend.core.database import get_db_session
            from backend.services.task_persistence_service import get_task_persistence_service
            
            db = get_db_session()
            try:
                persistence_service = get_task_persistence_service()
                task_record = persistence_service.get_task_record(db, task_id)
                
                if task_record:
                    print(f"  任务ID: {task_record.task_id}")
                    print(f"  用户ID: {task_record.user_id}")
                    print(f"  状态: {task_record.status}")
                    print(f"  进度: {task_record.progress_percentage}%")
                    print(f"  详情: {task_record.progress_detail}")
                    print(f"  阶段: {task_record.progress_stage}")
                    print(f"  创建时间: {task_record.created_at}")
                    print(f"  更新时间: {task_record.updated_at}")
                else:
                    print("  ❌ 没有找到数据库记录")
            finally:
                db.close()
        except Exception as e:
            print(f"  ❌ 无法获取数据库记录: {e}")
        
        # 4. 使用TaskManager获取状态
        print("\n🎯 TaskManager状态:")
        try:
            from backend.core.task_queue import get_task_manager
            task_manager = get_task_manager()
            task_status = task_manager.get_task_status(task_id)
            
            if task_status:
                print(f"  任务ID: {task_status.get('task_id')}")
                print(f"  状态: {task_status.get('state')}")
                print(f"  就绪: {task_status.get('ready')}")
                print(f"  成功: {task_status.get('successful')}")
                print(f"  失败: {task_status.get('failed')}")
                print(f"  用户ID: {task_status.get('user_id')}")
                
                progress = task_status.get('progress', {})
                print(f"  进度百分比: {progress.get('percentage')}%")
                print(f"  进度详情: {progress.get('detail')}")
                print(f"  进度阶段: {progress.get('stage')}")
                print(f"  开始时间: {progress.get('start_time')}")
                print(f"  更新时间: {progress.get('update_time')}")
            else:
                print("  ❌ TaskManager无法获取状态")
        except Exception as e:
            print(f"  ❌ TaskManager获取状态失败: {e}")
            
    except Exception as e:
        print(f"❌ 检查任务失败: {e}")

def check_all_tasks():
    """检查所有任务"""
    print("🔍 检查所有Redis任务进度数据")
    print("=" * 80)
    
    try:
        # 查找所有任务进度键
        progress_keys = redis_client.keys("task_progress:*")
        
        if not progress_keys:
            print("❌ 没有找到任何任务进度数据")
            return
        
        print(f"✅ 找到 {len(progress_keys)} 个任务进度记录\n")
        
        # 按时间排序（最新的在前）
        task_data = []
        for key in progress_keys:
            task_id = key.split(":")[-1]
            progress_data = redis_client.hgetall(key)
            
            if progress_data and 'update_time' in progress_data:
                try:
                    update_time = float(progress_data['update_time'])
                    task_data.append((task_id, update_time, progress_data))
                except:
                    task_data.append((task_id, 0, progress_data))
        
        # 按更新时间排序
        task_data.sort(key=lambda x: x[1], reverse=True)
        
        # 显示最近的10个任务
        for i, (task_id, update_time, progress_data) in enumerate(task_data[:10]):
            print(f"📋 任务 {i+1}: {task_id}")
            
            for field, value in progress_data.items():
                if field in ['start_time', 'update_time'] and value:
                    try:
                        timestamp = float(value)
                        readable_time = datetime.fromtimestamp(timestamp).strftime('%Y-%m-%d %H:%M:%S')
                        print(f"  {field}: {readable_time}")
                    except:
                        print(f"  {field}: {value}")
                else:
                    print(f"  {field}: {value}")
            
            print("-" * 60)
            
    except Exception as e:
        print(f"❌ 检查所有任务失败: {e}")

if __name__ == "__main__":
    # 测试Redis连接
    try:
        redis_client.ping()
        print("✅ Redis连接正常\n")
    except Exception as e:
        print(f"❌ Redis连接失败: {e}")
        exit(1)
    
    # 检查特定任务（最新的任务ID）
    specific_task_id = "meeting_transcription_a8c695b2c7d1"
    check_specific_task(specific_task_id)
    
    print("\n" + "=" * 80)

    # 检查所有任务
    check_all_tasks()

    print("\n" + "="*80)
    print("🔍 检查数据库ProcessingResult表")
    print("="*80)

    # 检查ProcessingResult表
    try:
        from backend.models.audio import ProcessingResult, AudioFile
        from backend.database import get_db

        db = next(get_db())

        # 查询所有ProcessingResult记录
        processing_results = db.query(ProcessingResult).all()
        print(f"✅ 找到 {len(processing_results)} 个ProcessingResult记录")

        for i, result in enumerate(processing_results[:10], 1):  # 只显示前10个
            print(f"\n📋 ProcessingResult {i}: {result.id}")
            print(f"  audio_file_id: {result.audio_file_id}")
            print(f"  processing_type: {result.processing_type}")
            print(f"  status: {result.status}")
            print(f"  created_at: {result.created_at}")
            print(f"  result_data: {str(result.result_data)[:100]}...")
            print("------------------------------------------------------------")

        db.close()

    except Exception as e:
        print(f"❌ 检查ProcessingResult表失败: {e}")

    print("\n" + "="*80)
    print("🔍 检查TaskManager表中的音频任务")
    print("="*80)

    # 检查TaskManager表中的音频任务
    try:
        from backend.models.task_manager import TaskManager
        from backend.database import get_db

        db = next(get_db())

        # 查询所有音频相关的TaskManager记录
        audio_tasks = db.query(TaskManager).filter(
            TaskManager.task_type.like('%speech%')
        ).order_by(TaskManager.created_at.desc()).limit(10).all()

        print(f"✅ 找到 {len(audio_tasks)} 个音频TaskManager记录")

        for i, task in enumerate(audio_tasks, 1):
            print(f"\n📋 TaskManager {i}: {task.task_id}")
            print(f"  task_type: {task.task_type}")
            print(f"  status: {task.status}")
            print(f"  progress: {task.progress}%")
            print(f"  created_at: {task.created_at}")
            print(f"  updated_at: {task.updated_at}")
            print("------------------------------------------------------------")

        db.close()

    except Exception as e:
        print(f"❌ 检查TaskManager表失败: {e}")
