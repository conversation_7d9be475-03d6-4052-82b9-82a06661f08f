2025-07-20 23:01:00,709 - backend.utils.audio.speech_recognition_core - INFO - [OK] 优化FunASR管理器可用
2025-07-20 23:01:02,069 - matplotlib.font_manager - INFO - generated new fontManager
2025-07-20 23:01:04,232 - backend.utils.ocr_utils - ERROR - Tesseract错误: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-07-20 23:01:10,114 - backend.core.gpu_manager - INFO - GPU资源管理器初始化完成，进程ID: 1
2025-07-21 05:27:03,731 - backend.utils.audio.speech_recognition_core - INFO - [OK] 优化FunASR管理器可用
2025-07-21 05:27:05,310 - matplotlib.font_manager - INFO - generated new fontManager
2025-07-21 05:27:07,507 - backend.utils.ocr_utils - ERROR - Tesseract错误: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-07-21 05:27:11,397 - backend.core.gpu_manager - INFO - GPU资源管理器初始化完成，进程ID: 1
2025-07-21 05:29:24,809 - backend.utils.audio.speech_recognition_core - INFO - [OK] 优化FunASR管理器可用
2025-07-21 05:29:25,871 - matplotlib.font_manager - INFO - generated new fontManager
2025-07-21 05:29:27,504 - backend.utils.ocr_utils - ERROR - Tesseract错误: tesseract is not installed or it's not in your PATH. See README file for more information.
2025-07-21 05:29:31,240 - backend.core.gpu_manager - INFO - GPU资源管理器初始化完成，进程ID: 1
2025-07-21 12:21:58,597 - backend.utils.audio.speech_recognition_core - INFO - [OK] 优化FunASR管理器可用
2025-07-21 12:22:00,466 - matplotlib.font_manager - INFO - generated new fontManager
2025-07-21 12:22:03,616 - backend.utils.ocr_utils - INFO - 检测到Tesseract版本: 5.3.0
2025-07-21 12:22:07,710 - backend.core.gpu_manager - INFO - GPU资源管理器初始化完成，进程ID: 1
2025-07-21 15:50:34,188 - backend.utils.audio.speech_recognition_core - INFO - [OK] 优化FunASR管理器可用
2025-07-21 15:50:36,165 - matplotlib.font_manager - INFO - generated new fontManager
2025-07-21 15:50:38,908 - backend.utils.ocr_utils - INFO - 检测到Tesseract版本: 5.3.0
2025-07-21 15:50:45,024 - backend.core.gpu_manager - INFO - GPU资源管理器初始化完成，进程ID: 1
2025-07-21 15:52:03,946 - backend.utils.audio.speech_recognition_core - WARNING - FunASR未安装，将使用模拟语音识别模型
2025-07-21 15:52:08,103 - matplotlib.font_manager - INFO - generated new fontManager
2025-07-21 15:52:11,632 - py.warnings - WARNING - /app/.venv/lib/python3.11/site-packages/celery/worker/consumer/consumer.py:507: CPendingDeprecationWarning: The broker_connection_retry configuration setting will no longer determine
whether broker connection retries are made during startup in Celery 6.0 and above.
If you wish to retain the existing behavior for retrying connections on startup,
you should set broker_connection_retry_on_startup to True.
  warnings.warn(

2025-07-21 15:52:11,644 - celery.worker.consumer.connection - INFO - Connected to redis://redis:6379/0
2025-07-21 15:52:11,645 - py.warnings - WARNING - /app/.venv/lib/python3.11/site-packages/celery/worker/consumer/consumer.py:507: CPendingDeprecationWarning: The broker_connection_retry configuration setting will no longer determine
whether broker connection retries are made during startup in Celery 6.0 and above.
If you wish to retain the existing behavior for retrying connections on startup,
you should set broker_connection_retry_on_startup to True.
  warnings.warn(

2025-07-21 15:52:11,650 - celery.worker.consumer.mingle - INFO - mingle: searching for neighbors
2025-07-21 15:52:12,666 - celery.worker.consumer.mingle - INFO - mingle: all alone
2025-07-21 15:52:12,702 - celery.apps.worker - INFO - celery@cf886285d641 ready.
