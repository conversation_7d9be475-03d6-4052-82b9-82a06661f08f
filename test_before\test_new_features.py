#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新增的语音处理功能
"""

import streamlit as st
import os
import sys
import tempfile
import traceback
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_imports():
    """测试所有必要的导入"""
    st.header("🧪 功能导入测试")
    
    # 测试基础模块
    try:
        import soundfile as sf
        import numpy as np
        import pandas as pd
        st.success("✅ 基础音频处理模块导入成功")
    except ImportError as e:
        st.error(f"❌ 基础模块导入失败: {str(e)}")
        return False
    
    # 测试语音处理工具
    try:
        from utils.speech_recognition_utils import (
            load_vad_model, vad_segment, vad_segment_for_two_person,
            check_audio_quality_for_speaker_recognition,
            set_offline_mode
        )
        st.success("✅ 语音处理工具导入成功")
        speech_utils_available = True
    except ImportError as e:
        st.warning(f"⚠️ 语音处理工具导入失败: {str(e)}")
        speech_utils_available = False
    
    # 测试语音识别核心
    try:
        from utils.speech_recognition_core import (
            SenseVoiceRecognizer, 
            SpeechRecognitionConfig,
            RecognitionResult
        )
        st.success("✅ 语音识别核心模块导入成功")
        speech_core_available = True
    except ImportError as e:
        st.warning(f"⚠️ 语音识别核心模块导入失败: {str(e)}")
        speech_core_available = False
    
    # 测试说话人识别
    try:
        from utils.speaker_recognition import SpeakerRecognition
        st.success("✅ 说话人识别模块导入成功")
        speaker_recognition_available = True
    except ImportError as e:
        st.warning(f"⚠️ 说话人识别模块导入失败: {str(e)}")
        speaker_recognition_available = False
    
    # 测试音频预处理
    try:
        from utils.audio_preprocessing import AudioPreprocessor, check_audio_quality
        st.success("✅ 音频预处理模块导入成功")
        audio_preprocessing_available = True
    except ImportError as e:
        st.warning(f"⚠️ 音频预处理模块导入失败: {str(e)}")
        audio_preprocessing_available = False
    
    return {
        'speech_utils': speech_utils_available,
        'speech_core': speech_core_available,
        'speaker_recognition': speaker_recognition_available,
        'audio_preprocessing': audio_preprocessing_available
    }

def test_model_paths():
    """测试模型路径"""
    st.header("📁 模型路径测试")
    
    # 默认路径映射
    default_paths = {
        'vad': r"C:\Users\<USER>\Documents\my_project\models\model_dir\fsmn_vad_zh",
        'sensevoice': r"C:\Users\<USER>\Documents\my_project\models\model_dir\SenseVoiceSmall",
        'campplus': r"C:\Users\<USER>\Documents\my_project\models\model_dir\cam++"
    }
    
    for model_name, model_path in default_paths.items():
        if os.path.exists(model_path):
            st.success(f"✅ {model_name.upper()} 模型路径存在: {model_path}")
        else:
            st.error(f"❌ {model_name.upper()} 模型路径不存在: {model_path}")

def test_speech_recognition():
    """测试语音识别功能"""
    st.header("🗣️ 语音识别功能测试")
    
    # 文件上传
    uploaded_file = st.file_uploader(
        "上传测试音频文件",
        type=['wav', 'mp3', 'm4a', 'aac', 'flac', 'ogg'],
        help="上传一个音频文件来测试语音识别功能"
    )
    
    if uploaded_file is not None:
        st.info(f"已上传文件: {uploaded_file.name}")
        
        if st.button("🚀 测试语音识别"):
            with st.spinner("正在测试语音识别..."):
                try:
                    # 保存临时文件
                    with tempfile.NamedTemporaryFile(delete=False, suffix=Path(uploaded_file.name).suffix) as tmp_file:
                        tmp_file.write(uploaded_file.getvalue())
                        temp_audio_path = tmp_file.name
                    
                    try:
                        # 导入语音识别模块
                        from utils.speech_recognition_core import (
                            SenseVoiceRecognizer, 
                            SpeechRecognitionConfig
                        )
                        
                        # 获取模型路径
                        sensevoice_model_path = r"C:\Users\<USER>\Documents\my_project\models\model_dir\SenseVoiceSmall"
                        
                        if not os.path.exists(sensevoice_model_path):
                            st.error(f"❌ SenseVoice模型路径不存在: {sensevoice_model_path}")
                            return
                        
                        # 创建配置
                        config = SpeechRecognitionConfig(
                            model_path=sensevoice_model_path,
                            language="auto",
                            use_itn=True,
                            device="auto"
                        )
                        
                        # 创建识别器
                        recognizer = SenseVoiceRecognizer(config)
                        
                        # 执行识别
                        result = recognizer.recognize_audio(temp_audio_path)
                        
                        if result.success:
                            st.success("✅ 语音识别测试成功！")
                            st.text_area("识别结果", result.text, height=100)
                            
                            col1, col2 = st.columns(2)
                            with col1:
                                st.metric("置信度", f"{result.confidence:.2f}")
                            with col2:
                                st.metric("识别语言", result.language or "未知")
                        else:
                            st.error(f"❌ 语音识别失败: {result.error}")
                            
                    finally:
                        # 清理临时文件
                        try:
                            os.unlink(temp_audio_path)
                        except:
                            pass
                            
                except Exception as e:
                    st.error(f"❌ 测试过程中发生错误: {str(e)}")
                    st.error(f"详细错误: {traceback.format_exc()}")

def test_speaker_recognition():
    """测试说话人识别功能"""
    st.header("👥 说话人识别功能测试")
    
    # 文件上传
    uploaded_file = st.file_uploader(
        "上传测试音频文件",
        type=['wav', 'mp3', 'm4a', 'aac', 'flac', 'ogg'],
        help="上传一个音频文件来测试说话人识别功能",
        key="speaker_test_upload"
    )
    
    if uploaded_file is not None:
        st.info(f"已上传文件: {uploaded_file.name}")
        
        if st.button("🚀 测试说话人识别"):
            with st.spinner("正在测试说话人识别..."):
                try:
                    # 保存临时文件
                    with tempfile.NamedTemporaryFile(delete=False, suffix=Path(uploaded_file.name).suffix) as tmp_file:
                        tmp_file.write(uploaded_file.getvalue())
                        temp_audio_path = tmp_file.name
                    
                    try:
                        # 测试VAD分割
                        from utils.speech_recognition_utils import load_vad_model, vad_segment
                        
                        vad_model_path = r"C:\Users\<USER>\Documents\my_project\models\model_dir\fsmn_vad_zh"
                        
                        if not os.path.exists(vad_model_path):
                            st.error(f"❌ VAD模型路径不存在: {vad_model_path}")
                            return
                        
                        vad_model = load_vad_model(vad_model_path)
                        if vad_model is None:
                            st.error("❌ 无法加载VAD模型")
                            return
                        
                        segments = vad_segment(temp_audio_path, vad_model)
                        
                        if segments:
                            st.success(f"✅ VAD分割成功！检测到 {len(segments)} 个语音段")
                            
                            # 显示前几个语音段
                            for i, segment in enumerate(segments[:5]):
                                start_time = segment[0] / 1000.0
                                end_time = segment[1] / 1000.0
                                st.text(f"片段 {i+1}: {start_time:.2f}s - {end_time:.2f}s")
                        else:
                            st.warning("⚠️ 未检测到语音段")
                            
                    finally:
                        # 清理临时文件
                        try:
                            os.unlink(temp_audio_path)
                        except:
                            pass
                            
                except Exception as e:
                    st.error(f"❌ 测试过程中发生错误: {str(e)}")
                    st.error(f"详细错误: {traceback.format_exc()}")

def main():
    """主函数"""
    st.title("🧪 新功能测试页面")
    st.markdown("测试新增的语音识别、说话人识别和会议转录功能")
    
    # 创建标签页
    tab1, tab2, tab3, tab4 = st.tabs(["📦 导入测试", "📁 模型测试", "🗣️ 语音识别测试", "👥 说话人识别测试"])
    
    with tab1:
        availability = test_imports()
        
        # 显示可用性总结
        st.subheader("📊 功能可用性总结")
        for feature, available in availability.items():
            if available:
                st.success(f"✅ {feature}: 可用")
            else:
                st.error(f"❌ {feature}: 不可用")
    
    with tab2:
        test_model_paths()
    
    with tab3:
        test_speech_recognition()
    
    with tab4:
        test_speaker_recognition()

if __name__ == "__main__":
    main()
