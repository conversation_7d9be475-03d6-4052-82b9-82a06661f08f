#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试模型加载修复
"""

import os
import sys
import configparser

def get_model_path(model_type: str) -> str:
    """获取模型路径"""
    try:
        config = configparser.ConfigParser()
        config_file = "speech_config.ini"
        
        # 默认路径映射
        default_paths = {
            'vad': r"C:\Users\<USER>\Documents\my_project\models\model_dir\fsmn_vad_zh",
            'sensevoice': r"C:\Users\<USER>\Documents\my_project\models\model_dir\SenseVoiceSmall",
            'campplus': r"C:\Users\<USER>\Documents\my_project\models\model_dir\cam++"
        }
        
        if os.path.exists(config_file):
            config.read(config_file, encoding='utf-8')
            if model_type == 'vad':
                return config.get('models', 'vad_path', fallback=default_paths['vad'])
            elif model_type == 'sensevoice':
                return config.get('models', 'sensevoice_path', fallback=default_paths['sensevoice'])
            elif model_type == 'campplus':
                return config.get('models', 'campplus_path', fallback=default_paths['campplus'])
        
        return default_paths.get(model_type, "")
        
    except Exception as e:
        print(f"读取模型配置失败: {str(e)}")
        # 返回默认路径
        default_paths = {
            'vad': r"C:\Users\<USER>\Documents\my_project\models\model_dir\fsmn_vad_zh",
            'sensevoice': r"C:\Users\<USER>\Documents\my_project\models\model_dir\SenseVoiceSmall",
            'campplus': r"C:\Users\<USER>\Documents\my_project\models\model_dir\cam++"
        }
        return default_paths.get(model_type, "")

def test_model_paths():
    """测试模型路径"""
    print("🔍 测试模型路径配置...")
    
    models = ['vad', 'sensevoice', 'campplus']
    
    for model_type in models:
        path = get_model_path(model_type)
        exists = os.path.exists(path)
        status = "✅" if exists else "❌"
        print(f"{status} {model_type.upper()}: {exists} ({path})")
        
        if exists:
            # 检查目录内容
            try:
                files = os.listdir(path)
                print(f"   📁 目录内容: {len(files)} 个文件/文件夹")
                # 显示前几个文件
                for i, file in enumerate(files[:3]):
                    print(f"      - {file}")
                if len(files) > 3:
                    print(f"      ... 还有 {len(files) - 3} 个文件")
            except Exception as e:
                print(f"   ❌ 无法读取目录内容: {e}")
        print()

def test_config_file():
    """测试配置文件"""
    print("📄 测试配置文件...")
    
    config_file = "speech_config.ini"
    if os.path.exists(config_file):
        print(f"✅ 配置文件存在: {config_file}")
        
        try:
            config = configparser.ConfigParser()
            config.read(config_file, encoding='utf-8')
            
            print("📋 配置文件内容:")
            for section in config.sections():
                print(f"  [{section}]")
                for key, value in config.items(section):
                    print(f"    {key} = {value}")
                print()
                
        except Exception as e:
            print(f"❌ 读取配置文件失败: {e}")
    else:
        print(f"❌ 配置文件不存在: {config_file}")

def test_import():
    """测试导入"""
    print("📦 测试关键模块导入...")
    
    try:
        # 添加utils路径
        sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))
        
        from utils.speech_recognition_utils import load_vad_model
        print("✅ load_vad_model 导入成功")
        
        # 测试模型加载
        vad_path = get_model_path('vad')
        if os.path.exists(vad_path):
            print(f"🔄 尝试加载VAD模型: {vad_path}")
            # 注意：这里不实际加载模型，只测试路径
            print("✅ VAD模型路径验证通过")
        else:
            print("❌ VAD模型路径不存在")
            
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    print("🚀 模型加载修复测试")
    print("=" * 50)
    
    test_config_file()
    print()
    
    test_model_paths()
    print()
    
    test_import()
    print()
    
    print("✅ 测试完成！")
