#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试模型路径修复
"""

import os
import sys

# 添加项目路径
sys.path.append(os.path.dirname(__file__))

def test_get_model_path():
    """测试get_model_path函数"""
    print("🔍 测试get_model_path函数...")
    
    try:
        # 导入函数
        from pages.语音处理分析 import get_model_path
        
        # 测试各种模型路径
        models = ['vad', 'sensevoice', 'campplus']
        
        for model_type in models:
            path = get_model_path(model_type)
            exists = os.path.exists(path)
            status = "✅" if exists else "❌"
            print(f"{status} {model_type.upper()}: {path}")
            print(f"   存在: {exists}")
            
            if exists:
                try:
                    files = os.listdir(path)
                    print(f"   文件数量: {len(files)}")
                except Exception as e:
                    print(f"   读取目录失败: {e}")
            print()
            
        print("✅ get_model_path函数测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_model_availability():
    """测试模型可用性检查"""
    print("🔍 测试模型可用性检查...")
    
    try:
        from utils.speech_recognition_utils import check_model_availability
        
        result = check_model_availability()
        status = "✅" if result else "❌"
        print(f"{status} 模型可用性: {result}")
        
        return result
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_load_vad_model_with_path():
    """测试带路径的VAD模型加载"""
    print("🔍 测试VAD模型加载...")
    
    try:
        from utils.speech_recognition_utils import load_vad_model
        from pages.语音处理分析 import get_model_path
        
        # 获取VAD模型路径
        vad_path = get_model_path('vad')
        print(f"VAD模型路径: {vad_path}")
        
        if not os.path.exists(vad_path):
            print("❌ VAD模型路径不存在")
            return False
        
        print("✅ VAD模型路径存在")
        print("⚠️  注意: 实际模型加载需要在Streamlit环境中进行")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 模型路径修复验证测试")
    print("=" * 50)
    
    # 测试1: get_model_path函数
    test1_result = test_get_model_path()
    print()
    
    # 测试2: 模型可用性检查
    test2_result = test_model_availability()
    print()
    
    # 测试3: VAD模型加载准备
    test3_result = test_load_vad_model_with_path()
    print()
    
    # 总结
    print("📊 测试结果总结:")
    print(f"✅ get_model_path函数: {'通过' if test1_result else '失败'}")
    print(f"✅ 模型可用性检查: {'通过' if test2_result else '失败'}")
    print(f"✅ VAD模型路径验证: {'通过' if test3_result else '失败'}")
    
    if all([test1_result, test2_result, test3_result]):
        print("\n🎉 所有测试通过！模型路径修复成功！")
    else:
        print("\n⚠️  部分测试失败，请检查配置")
