<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RAG上传进度测试 - 最终版</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
            margin: 5px 0;
        }
        .status.connected { background-color: #d4edda; color: #155724; }
        .status.disconnected { background-color: #f8d7da; color: #721c24; }
        .status.testing { background-color: #fff3cd; color: #856404; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background-color: #007bff;
            transition: width 0.3s ease;
            border-radius: 10px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .info-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #e9ecef;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>RAG上传进度测试 - 最终版</h1>
        
        <div class="test-section">
            <h3>测试步骤</h3>
            <ol>
                <li><strong>检查Token</strong>：验证认证token是否正确获取</li>
                <li><strong>测试WebSocket</strong>：验证WebSocket连接是否正常</li>
                <li><strong>模拟RAG上传</strong>：测试完整的进度监控流程</li>
            </ol>
            
            <button onclick="runFullTest()">运行完整测试</button>
            <button onclick="testTokenOnly()">仅测试Token</button>
            <button onclick="testWebSocketOnly()">仅测试WebSocket</button>
            <button onclick="clearLogs()">清空日志</button>
        </div>

        <div class="info-grid">
            <div class="info-card">
                <h4>Token状态</h4>
                <div class="status" id="tokenStatus">未检查</div>
                <div id="tokenInfo">-</div>
            </div>
            
            <div class="info-card">
                <h4>WebSocket状态</h4>
                <div class="status" id="wsStatus">未连接</div>
                <div id="wsInfo">-</div>
            </div>
        </div>

        <div class="test-section">
            <h3>全局进度条模拟</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: 0%"></div>
            </div>
            <div id="progressText">0% - 等待开始</div>
        </div>
        
        <div class="test-section">
            <h3>测试日志</h3>
            <div class="log" id="logContainer"></div>
        </div>
    </div>

    <script>
        function log(message) {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            logContainer.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(message);
        }

        function updateStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            element.className = `status ${status}`;
            element.textContent = message;
        }

        function updateProgress(percentage, detail) {
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
            
            progressFill.style.width = `${percentage}%`;
            progressText.textContent = `${Math.round(percentage)}% - ${detail}`;
        }

        async function testTokenOnly() {
            log('🔍 开始Token测试...');
            
            // 检查所有可能的token存储位置
            const sources = [
                { name: 'localStorage.token', value: localStorage.getItem('token') },
                { name: 'localStorage.access_token', value: localStorage.getItem('access_token') },
                { name: 'sessionStorage.token', value: sessionStorage.getItem('token') },
                { name: 'sessionStorage.access_token', value: sessionStorage.getItem('access_token') }
            ];

            let foundToken = null;
            for (const source of sources) {
                if (source.value) {
                    log(`✅ 找到Token: ${source.name} = ${source.value.substring(0, 20)}...`);
                    foundToken = source.value;
                    break;
                } else {
                    log(`❌ ${source.name}: 不存在`);
                }
            }

            if (foundToken) {
                updateStatus('tokenStatus', 'connected', 'Token有效');
                document.getElementById('tokenInfo').textContent = `${foundToken.substring(0, 30)}...`;
                
                // 测试token有效性
                try {
                    const response = await fetch('http://localhost:8002/api/v1/auth/me', {
                        headers: { 'Authorization': `Bearer ${foundToken}` }
                    });
                    
                    if (response.ok) {
                        const userData = await response.json();
                        log(`✅ Token验证成功: ${userData.username}`);
                        return foundToken;
                    } else {
                        log(`❌ Token验证失败: ${response.status}`);
                    }
                } catch (error) {
                    log(`❌ Token验证错误: ${error.message}`);
                }
            } else {
                updateStatus('tokenStatus', 'disconnected', 'Token不存在');
                document.getElementById('tokenInfo').textContent = '需要先登录';
                
                // 尝试登录
                log('🔐 尝试自动登录...');
                try {
                    const loginResponse = await fetch('http://localhost:8002/api/v1/auth/login', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ username: 'admin', password: 'admin123' })
                    });
                    
                    if (loginResponse.ok) {
                        const result = await loginResponse.json();
                        const newToken = result.access_token;
                        localStorage.setItem('token', newToken);
                        log(`✅ 自动登录成功，Token已保存: ${newToken.substring(0, 20)}...`);
                        updateStatus('tokenStatus', 'connected', 'Token有效');
                        document.getElementById('tokenInfo').textContent = `${newToken.substring(0, 30)}...`;
                        return newToken;
                    } else {
                        log(`❌ 自动登录失败: ${loginResponse.status}`);
                    }
                } catch (error) {
                    log(`❌ 自动登录错误: ${error.message}`);
                }
            }
            
            return null;
        }

        async function testWebSocketOnly() {
            log('🔌 开始WebSocket测试...');
            
            const token = await testTokenOnly();
            if (!token) {
                log('❌ 无法测试WebSocket：没有有效的Token');
                return false;
            }

            return new Promise((resolve) => {
                const wsUrl = `ws://localhost:8002/ws/progress?token=${token}`;
                log(`🔗 连接WebSocket: ${wsUrl}`);
                
                const ws = new WebSocket(wsUrl);
                let connected = false;
                
                const timeout = setTimeout(() => {
                    if (!connected) {
                        log('⏰ WebSocket连接超时');
                        updateStatus('wsStatus', 'disconnected', '连接超时');
                        ws.close();
                        resolve(false);
                    }
                }, 10000);
                
                ws.onopen = () => {
                    connected = true;
                    clearTimeout(timeout);
                    log('✅ WebSocket连接成功');
                    updateStatus('wsStatus', 'connected', '已连接');
                    document.getElementById('wsInfo').textContent = 'ws://localhost:8002/ws/progress';
                    
                    // 发送测试消息
                    ws.send(JSON.stringify({ type: 'ping' }));
                    log('📤 发送心跳测试');
                    
                    setTimeout(() => {
                        ws.close();
                        resolve(true);
                    }, 2000);
                };
                
                ws.onmessage = (event) => {
                    const data = JSON.parse(event.data);
                    log(`📨 收到WebSocket消息: ${JSON.stringify(data)}`);
                };
                
                ws.onclose = (event) => {
                    log(`🔌 WebSocket连接关闭: ${event.code} - ${event.reason}`);
                    if (!connected) {
                        updateStatus('wsStatus', 'disconnected', '连接失败');
                        resolve(false);
                    }
                };
                
                ws.onerror = (error) => {
                    clearTimeout(timeout);
                    log(`❌ WebSocket错误: ${error}`);
                    updateStatus('wsStatus', 'disconnected', '连接错误');
                    resolve(false);
                };
            });
        }

        async function runFullTest() {
            log('🚀 开始完整测试流程...');
            updateProgress(0, '开始测试');
            
            // 步骤1：测试Token
            updateProgress(10, '检查Token...');
            const token = await testTokenOnly();
            if (!token) {
                log('❌ 完整测试失败：Token获取失败');
                return;
            }
            
            // 步骤2：测试WebSocket
            updateProgress(30, '测试WebSocket连接...');
            const wsConnected = await testWebSocketOnly();
            if (!wsConnected) {
                log('❌ 完整测试失败：WebSocket连接失败');
                return;
            }
            
            // 步骤3：模拟RAG上传进度
            updateProgress(50, '模拟RAG上传...');
            await simulateRAGUpload();
            
            updateProgress(100, '测试完成');
            log('🎉 完整测试流程成功完成！');
        }

        async function simulateRAGUpload() {
            log('📊 模拟RAG上传进度更新...');
            
            const progressSteps = [
                { percentage: 55, detail: '正在分析文档结构...' },
                { percentage: 65, detail: '正在提取文本内容...' },
                { percentage: 75, detail: '正在向量化文档块...' },
                { percentage: 85, detail: '正在保存向量数据...' },
                { percentage: 95, detail: '正在更新索引...' },
                { percentage: 100, detail: '上传完成！' }
            ];
            
            for (let i = 0; i < progressSteps.length; i++) {
                await new Promise(resolve => setTimeout(resolve, 1000));
                const step = progressSteps[i];
                updateProgress(step.percentage, step.detail);
                log(`📈 进度更新: ${step.percentage}% - ${step.detail}`);
            }
        }

        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
            updateProgress(0, '等待开始');
            updateStatus('tokenStatus', 'testing', '未检查');
            updateStatus('wsStatus', 'testing', '未连接');
            document.getElementById('tokenInfo').textContent = '-';
            document.getElementById('wsInfo').textContent = '-';
        }

        // 页面加载时自动检查
        window.onload = function() {
            log('🚀 RAG上传进度测试工具已启动');
            log('💡 建议先运行"仅测试Token"确保认证正常');
        };
    </script>
</body>
</html>
