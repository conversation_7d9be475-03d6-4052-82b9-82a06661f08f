import{_ as c,h as _,g as m,o as A,J as p,ac as k,E as u,r as y,a as f,c as v,b as s,d as n,w as l,t as C,x as I,K as P,f as a}from"./index-2c134546.js";const x={class:"audio-center-test"},T={class:"test-section"},S={class:"test-section"},w={key:0},E={__name:"AudioCenterTest",setup(N){const d=_(!1),t=_(""),r=m();A(()=>{console.log("🧪 测试页面初始化"),console.log("当前token:",p()),console.log("用户store状态:",{isAuthenticated:r.isAuthenticated,hasToken:!!r.token,hasUser:!!r.user,user:r.user}),p()?u.info("发现现有token，可以测试API连接"):(k("demo-token"),u.info("已设置测试token，可以测试API连接"))});const g=async()=>{var i;d.value=!0;try{const e=await P.get("/audio/");t.value="✅ 后端API连接成功",u.success("后端连接正常"),console.log("API响应:",e.data)}catch(e){console.error("API测试失败:",e),e.response?t.value=`❌ 后端API连接失败 (${e.response.status}): ${((i=e.response.data)==null?void 0:i.detail)||e.response.statusText}`:t.value=`❌ 连接错误: ${e.message}`,u.error("后端连接失败")}finally{d.value=!1}};return(i,e)=>{const o=y("el-button");return f(),v("div",x,[e[7]||(e[7]=s("h1",null,"音频处理中心测试页面",-1)),e[8]||(e[8]=s("p",null,"如果您能看到这个页面，说明基本路由和组件渲染正常。",-1)),e[9]||(e[9]=s("div",{class:"test-section"},[s("h2",null,"CSS变量测试"),s("div",{class:"test-card"},[s("p",null,"这是一个测试卡片，用于验证CSS变量是否正常工作。")])],-1)),s("div",T,[e[4]||(e[4]=s("h2",null,"Element Plus组件测试",-1)),n(o,{type:"primary"},{default:l(()=>e[0]||(e[0]=[a("主要按钮")])),_:1,__:[0]}),n(o,{type:"success"},{default:l(()=>e[1]||(e[1]=[a("成功按钮")])),_:1,__:[1]}),n(o,{type:"warning"},{default:l(()=>e[2]||(e[2]=[a("警告按钮")])),_:1,__:[2]}),n(o,{type:"danger"},{default:l(()=>e[3]||(e[3]=[a("危险按钮")])),_:1,__:[3]})]),s("div",S,[e[6]||(e[6]=s("h2",null,"API连接测试",-1)),n(o,{onClick:g,loading:d.value},{default:l(()=>e[5]||(e[5]=[a("测试后端连接")])),_:1,__:[5]},8,["loading"]),t.value?(f(),v("p",w,C(t.value),1)):I("",!0)])])}}},b=c(E,[["__scopeId","data-v-88671803"]]);export{b as default};
