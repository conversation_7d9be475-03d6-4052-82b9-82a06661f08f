<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>进度监控超时修复测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background-color: #007bff;
            transition: width 0.3s ease;
            border-radius: 10px;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
            margin: 5px 0;
        }
        .status.pending { background-color: #fff3cd; color: #856404; }
        .status.progress { background-color: #d1ecf1; color: #0c5460; }
        .status.completed { background-color: #d4edda; color: #155724; }
        .status.failed { background-color: #f8d7da; color: #721c24; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>进度监控超时修复测试</h1>
        
        <div class="test-section">
            <h3>测试场景：模拟任务启动延迟</h3>
            <p>模拟真实场景：任务提交后需要一定时间才能在Redis中看到进度数据</p>
            
            <button onclick="startDelayedTask()">开始延迟任务测试</button>
            <button onclick="startNormalTask()">开始正常任务测试</button>
            <button onclick="clearLogs()">清空日志</button>
            
            <div class="status" id="taskStatus">等待开始...</div>
            
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: 0%"></div>
            </div>
            <div id="progressText">0% - 等待开始</div>
            
            <div class="log" id="logContainer"></div>
        </div>
    </div>

    <script>
        // 模拟修复后的进度监控逻辑
        class ProgressMonitor {
            constructor() {
                this.activeMonitors = new Map();
            }

            startMonitoring(taskId, callbacks = {}) {
                console.log(`🔄 开始监控任务: ${taskId}`);
                this.log(`🔄 开始监控任务: ${taskId}`);

                // 创建监控器配置（修复后的版本）
                const monitor = {
                    taskId,
                    callbacks,
                    isActive: true,
                    lastUpdate: Date.now(),
                    retryCount: 0,
                    maxRetries: 15, // 增加重试次数
                    startTime: Date.now() // 记录开始时间
                };

                this.activeMonitors.set(taskId, monitor);
                this.startPollingMonitoring(taskId, monitor);
            }

            startPollingMonitoring(taskId, monitor) {
                const pollProgress = async () => {
                    if (!monitor.isActive) return;

                    try {
                        const progressData = await this.mockGetTaskProgress(taskId);
                        
                        if (progressData) {
                            monitor.lastUpdate = Date.now();
                            monitor.retryCount = 0;

                            this.log(`📈 获取到进度数据: ${progressData.percentage}% - ${progressData.detail}`);
                            this.handleProgressUpdate(taskId, progressData, monitor.callbacks);

                            // 检查任务是否完成
                            if (progressData.status === 'completed' || progressData.percentage >= 100) {
                                this.handleTaskCompleted(taskId, progressData, monitor.callbacks);
                                return;
                            } else if (progressData.status === 'failed') {
                                this.handleTaskFailed(taskId, progressData.error_message || '任务执行失败', monitor.callbacks);
                                return;
                            }
                        } else {
                            // 没有获取到进度数据，但不立即判定为失败
                            const elapsedTime = Date.now() - monitor.startTime;
                            this.log(`⚠️ 暂未获取到进度数据，已等待 ${Math.round(elapsedTime / 1000)}秒`);
                        }

                    } catch (error) {
                        monitor.retryCount++;
                        const elapsedTime = Date.now() - monitor.startTime;
                        this.log(`⚠️ 轮询进度失败 (${monitor.retryCount}/${monitor.maxRetries}): ${error.message}`);
                        this.log(`⏰ 任务已运行 ${Math.round(elapsedTime / 1000)}秒`);

                        // 如果任务刚开始（30秒内），给更多容错时间
                        const isEarlyStage = elapsedTime < 30000; // 30秒内
                        const effectiveMaxRetries = isEarlyStage ? monitor.maxRetries * 2 : monitor.maxRetries;

                        if (monitor.retryCount >= effectiveMaxRetries) {
                            this.log(`❌ 轮询重试次数超限，停止监控: ${taskId}`);
                            this.handleTaskFailed(taskId, '进度查询失败', monitor.callbacks);
                            return;
                        }
                    }
                };

                // 立即执行一次
                pollProgress();

                // 设置动态轮询间隔
                let currentInterval = 1000; // 开始时每1秒轮询一次
                
                const intervalId = setInterval(() => {
                    const elapsedTime = Date.now() - monitor.startTime;
                    
                    // 根据运行时间调整轮询频率
                    if (elapsedTime > 60000) { // 1分钟后
                        currentInterval = 3000; // 每3秒轮询一次
                    } else if (elapsedTime > 30000) { // 30秒后
                        currentInterval = 2000; // 每2秒轮询一次
                    }
                    
                    pollProgress();
                }, currentInterval);

                monitor.cleanup = () => {
                    clearInterval(intervalId);
                };
            }

            // 模拟获取任务进度（模拟延迟和失败）
            async mockGetTaskProgress(taskId) {
                const monitor = this.activeMonitors.get(taskId);
                const elapsedTime = Date.now() - monitor.startTime;

                // 模拟不同的场景
                if (taskId.includes('delayed')) {
                    // 延迟任务：前10秒没有数据
                    if (elapsedTime < 10000) {
                        throw new Error('任务尚未开始');
                    }
                    // 10秒后开始有进度数据
                    const progress = Math.min(100, (elapsedTime - 10000) / 200); // 20秒完成
                    return {
                        percentage: progress,
                        detail: `延迟任务处理中... (${Math.round(progress)}%)`,
                        status: progress >= 100 ? 'completed' : 'progress'
                    };
                } else {
                    // 正常任务：立即有进度数据
                    const progress = Math.min(100, elapsedTime / 150); // 15秒完成
                    return {
                        percentage: progress,
                        detail: `正常任务处理中... (${Math.round(progress)}%)`,
                        status: progress >= 100 ? 'completed' : 'progress'
                    };
                }
            }

            handleProgressUpdate(taskId, progressData, callbacks) {
                this.updateUI(progressData);
                if (callbacks.onProgress) {
                    callbacks.onProgress(progressData);
                }
            }

            handleTaskCompleted(taskId, result, callbacks) {
                this.log(`✅ 任务完成: ${taskId}`);
                this.stopMonitoring(taskId);
                this.updateStatus('completed', '任务完成');
                if (callbacks.onCompleted) {
                    callbacks.onCompleted(result);
                }
            }

            handleTaskFailed(taskId, error, callbacks) {
                this.log(`❌ 任务失败: ${taskId} - ${error}`);
                this.stopMonitoring(taskId);
                this.updateStatus('failed', `任务失败: ${error}`);
                if (callbacks.onFailed) {
                    callbacks.onFailed(error);
                }
            }

            stopMonitoring(taskId) {
                const monitor = this.activeMonitors.get(taskId);
                if (monitor) {
                    monitor.isActive = false;
                    if (monitor.cleanup) {
                        monitor.cleanup();
                    }
                    this.activeMonitors.delete(taskId);
                    this.log(`🛑 停止监控任务: ${taskId}`);
                }
            }

            updateUI(progressData) {
                const progressFill = document.getElementById('progressFill');
                const progressText = document.getElementById('progressText');
                
                progressFill.style.width = `${progressData.percentage}%`;
                progressText.textContent = `${Math.round(progressData.percentage)}% - ${progressData.detail}`;
                
                this.updateStatus('progress', progressData.detail);
            }

            updateStatus(status, message) {
                const statusEl = document.getElementById('taskStatus');
                statusEl.className = `status ${status}`;
                statusEl.textContent = message;
            }

            log(message) {
                const logContainer = document.getElementById('logContainer');
                const timestamp = new Date().toLocaleTimeString();
                logContainer.innerHTML += `<div>[${timestamp}] ${message}</div>`;
                logContainer.scrollTop = logContainer.scrollHeight;
                console.log(message);
            }
        }

        // 全局实例
        const progressMonitor = new ProgressMonitor();

        // 测试函数
        function startDelayedTask() {
            progressMonitor.log('🚀 开始延迟任务测试（模拟任务启动需要10秒）');
            progressMonitor.updateStatus('pending', '启动延迟任务...');
            
            const taskId = 'delayed_task_' + Date.now();
            progressMonitor.startMonitoring(taskId, {
                onProgress: (data) => {
                    progressMonitor.log(`📈 进度回调: ${data.percentage}%`);
                },
                onCompleted: (result) => {
                    progressMonitor.log('🎉 任务完成回调');
                },
                onFailed: (error) => {
                    progressMonitor.log(`💥 任务失败回调: ${error}`);
                }
            });
        }

        function startNormalTask() {
            progressMonitor.log('🚀 开始正常任务测试');
            progressMonitor.updateStatus('pending', '启动正常任务...');
            
            const taskId = 'normal_task_' + Date.now();
            progressMonitor.startMonitoring(taskId, {
                onProgress: (data) => {
                    progressMonitor.log(`📈 进度回调: ${data.percentage}%`);
                },
                onCompleted: (result) => {
                    progressMonitor.log('🎉 任务完成回调');
                },
                onFailed: (error) => {
                    progressMonitor.log(`💥 任务失败回调: ${error}`);
                }
            });
        }

        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
            document.getElementById('progressFill').style.width = '0%';
            document.getElementById('progressText').textContent = '0% - 等待开始';
            progressMonitor.updateStatus('pending', '等待开始...');
        }
    </script>
</body>
</html>
