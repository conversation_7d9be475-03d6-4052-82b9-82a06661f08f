#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GPU加速功能测试脚本
任务#7.2 - GPU加速支持实现

测试功能：
1. GPU环境检测
2. GPU配置和设备管理
3. GPU加速器功能
4. GPU增强并行处理器
5. CPU-GPU混合调度
6. 性能对比测试

作者: AI Assistant
创建时间: 2025-01-27
任务关联: 任务#7.2 GPU加速支持测试
"""

import sys
import time
import os
import threading
from pathlib import Path

# 添加utils目录到路径
utils_path = Path(__file__).parent / "utils"
sys.path.insert(0, str(utils_path))

def test_gpu_environment():
    """测试GPU环境"""
    print("🔍 检测GPU环境...")
    
    # 检查PyTorch和CUDA
    try:
        import torch
        print(f"✅ PyTorch版本: {torch.__version__}")
        print(f"✅ CUDA可用: {torch.cuda.is_available()}")
        
        if torch.cuda.is_available():
            device_count = torch.cuda.device_count()
            print(f"✅ GPU设备数量: {device_count}")
            
            for i in range(device_count):
                props = torch.cuda.get_device_properties(i)
                print(f"   GPU {i}: {props.name}")
                print(f"   计算能力: {props.major}.{props.minor}")
                print(f"   总内存: {props.total_memory / 1024**3:.1f}GB")
        else:
            print("⚠️ CUDA不可用，将使用CPU模式")
        
        return True
    except ImportError as e:
        print(f"❌ PyTorch未安装: {e}")
        return False

def test_gpu_accelerator():
    """测试GPU加速器"""
    print("\n🚀 测试GPU加速器...")
    
    try:
        from gpu_accelerator import (
            GPUConfig, GPUMonitor, GPUDeviceManager, 
            get_gpu_info, is_gpu_available, GPUMemoryManager
        )
        
        print("✅ GPU加速器模块导入成功")
        
        # 测试GPU信息获取
        gpu_info = get_gpu_info()
        print(f"✅ GPU信息: {gpu_info}")
        
        # 测试GPU可用性
        available = is_gpu_available()
        print(f"✅ GPU可用性: {available}")
        
        # 创建GPU配置
        config = GPUConfig(
            enable_gpu=True,
            max_gpu_workers=2,
            gpu_memory_fraction=0.5
        )
        print(f"✅ GPU配置创建成功")
        
        # 测试GPU设备管理器
        if available:
            device_manager = GPUDeviceManager(config)
            best_device = device_manager.select_best_device()
            print(f"✅ 最佳GPU设备: {best_device}")
            
            # 测试内存管理
            memory_info = GPUMemoryManager.get_memory_usage(0)
            print(f"✅ GPU内存信息: {memory_info}")
        
        return True
        
    except ImportError as e:
        print(f"❌ GPU加速器导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ GPU加速器测试失败: {e}")
        return False

def test_gpu_enhanced_processor():
    """测试GPU增强并行处理器"""
    print("\n⚡ 测试GPU增强并行处理器...")
    
    try:
        from gpu_enhanced_processor import GPUEnhancedConfig, GPUEnhancedProcessor
        
        print("✅ GPU增强处理器模块导入成功")
        
        # 创建配置
        config = GPUEnhancedConfig(
            max_workers=2,
            enable_gpu=True,
            max_gpu_workers=1,
            cpu_gpu_balance=0.7
        )
        print("✅ GPU增强配置创建成功")
        
        # 创建处理器
        processor = GPUEnhancedProcessor(config)
        print("✅ GPU增强处理器创建成功")
        
        # 启动处理器
        processor.start()
        print("✅ GPU增强处理器启动成功")
        
        # 提交测试任务
        def test_task(x):
            time.sleep(0.1)
            return x * x
        
        print("📊 提交测试任务...")
        task_ids = []
        for i in range(5):
            task_id = processor.submit_task(test_task, i, device_preference="auto")
            task_ids.append(task_id)
        
        print(f"✅ 提交了 {len(task_ids)} 个任务")
        
        # 等待任务完成
        time.sleep(2.0)
        
        # 获取性能统计
        stats = processor.get_performance_stats()
        print("📈 性能统计:")
        for key, value in stats.items():
            if isinstance(value, (int, float)):
                print(f"   {key}: {value}")
        
        # 停止处理器
        processor.stop()
        print("✅ GPU增强处理器停止成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ GPU增强处理器导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ GPU增强处理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_batch_processing():
    """测试批处理功能"""
    print("\n📦 测试批处理功能...")
    
    try:
        from gpu_enhanced_processor import create_gpu_enhanced_processor, gpu_parallel_map
        
        # 简单批处理测试
        def square_task(x):
            return x * x
        
        data = list(range(10))
        
        print("📊 测试GPU并行映射...")
        start_time = time.time()
        results = gpu_parallel_map(square_task, data, max_workers=2, enable_gpu=True)
        end_time = time.time()
        
        print(f"✅ 批处理完成")
        print(f"   输入: {data}")
        print(f"   输出: {results}")
        print(f"   用时: {end_time - start_time:.2f}秒")
        
        # 验证结果
        expected = [x * x for x in data]
        if results == expected:
            print("✅ 结果验证通过")
        else:
            print("❌ 结果验证失败")
            print(f"   期望: {expected}")
            print(f"   实际: {results}")
        
        return True
        
    except Exception as e:
        print(f"❌ 批处理测试失败: {e}")
        return False

def test_performance_comparison():
    """测试性能对比"""
    print("\n⚡ 性能对比测试...")
    
    try:
        from parallel_processor import parallel_map as cpu_parallel_map
        from gpu_enhanced_processor import gpu_parallel_map
        
        def compute_intensive_task(x):
            # 模拟计算密集型任务
            result = 0
            for i in range(1000):
                result += x * i
            return result
        
        data = list(range(20))
        
        # CPU并行测试
        print("🖥️ CPU并行处理...")
        start_time = time.time()
        cpu_results = cpu_parallel_map(compute_intensive_task, data, max_workers=2)
        cpu_time = time.time() - start_time
        print(f"   CPU时间: {cpu_time:.2f}秒")
        
        # GPU增强并行测试
        print("🚀 GPU增强并行处理...")
        start_time = time.time()
        gpu_results = gpu_parallel_map(compute_intensive_task, data, max_workers=2, enable_gpu=True)
        gpu_time = time.time() - start_time
        print(f"   GPU增强时间: {gpu_time:.2f}秒")
        
        # 对比结果
        if cpu_time > 0:
            speedup = cpu_time / gpu_time
            print(f"⚡ 加速比: {speedup:.2f}x")
        
        # 验证结果一致性
        if cpu_results == gpu_results:
            print("✅ 结果一致性验证通过")
        else:
            print("❌ 结果不一致")
        
        return True
        
    except Exception as e:
        print(f"❌ 性能对比测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 70)
    print("GPU加速功能测试 - 任务#7.2")
    print("=" * 70)
    
    tests = [
        ("GPU环境检测", test_gpu_environment),
        ("GPU加速器", test_gpu_accelerator),
        ("GPU增强处理器", test_gpu_enhanced_processor),
        ("批处理功能", test_batch_processing),
        ("性能对比", test_performance_comparison),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}测试通过")
            else:
                print(f"❌ {test_name}测试失败")
        except Exception as e:
            print(f"❌ {test_name}测试出错: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n{'='*70}")
    print(f"📊 测试结果: {passed}/{total} 通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("🎉 所有GPU加速功能测试通过！")
        print("🚀 GPU加速支持实现成功！")
    elif passed > 0:
        print("⚠️ 部分功能正常，需要进一步优化")
    else:
        print("❌ GPU加速功能需要修复")
    
    print("=" * 70)

if __name__ == "__main__":
    main() 