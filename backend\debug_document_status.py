#!/usr/bin/env python3
"""
调试文档状态问题的脚本
"""

import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from backend.core.database import get_db_session
from backend.models.document_management import ManagedDocument
from backend.services.document_db_service import document_db_service
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_document_status():
    """检查最近上传的文档状态"""
    db = get_db_session()
    try:
        # 获取最近的10个文档
        documents = db.query(ManagedDocument)\
                     .order_by(ManagedDocument.created_at.desc())\
                     .limit(10)\
                     .all()
        
        print("=" * 80)
        print("最近上传的文档状态检查")
        print("=" * 80)
        
        for doc in documents:
            print(f"\n文档ID: {doc.id}")
            print(f"文件名: {doc.filename}")
            print(f"状态: {doc.status}")
            print(f"处理进度: {doc.processing_progress}")
            print(f"创建时间: {doc.created_at}")
            print(f"更新时间: {doc.updated_at}")
            print(f"处理完成时间: {doc.processed_at}")
            print(f"错误信息: {doc.error_message}")
            print(f"节点数量: {doc.sections_count}")
            print("-" * 40)
            
        # 检查状态为 uploaded 的文档
        uploaded_docs = db.query(ManagedDocument)\
                         .filter(ManagedDocument.status == 'uploaded')\
                         .order_by(ManagedDocument.created_at.desc())\
                         .limit(5)\
                         .all()
        
        if uploaded_docs:
            print("\n" + "=" * 80)
            print("状态为 'uploaded' 的文档")
            print("=" * 80)
            
            for doc in uploaded_docs:
                print(f"\n文档ID: {doc.id}")
                print(f"文件名: {doc.filename}")
                print(f"状态: {doc.status}")
                print(f"处理进度: {doc.processing_progress}")
                print(f"创建时间: {doc.created_at}")
                print(f"更新时间: {doc.updated_at}")
                print(f"节点数量: {doc.sections_count}")
                
                # 检查是否有节点数据
                from backend.models.document_management import DocumentSection
                sections = db.query(DocumentSection)\
                            .filter(DocumentSection.document_id == doc.id)\
                            .count()
                print(f"实际节点数量: {sections}")
                
                # 如果有节点但状态不是completed，这就是问题所在
                if sections > 0 and doc.status != 'completed':
                    print(f"⚠️  问题发现：文档有 {sections} 个节点但状态仍为 '{doc.status}'")
                    
                    # 尝试修复状态
                    print("🔧 尝试修复文档状态...")
                    try:
                        document_db_service.update_document(
                            db, doc.id, doc.user_id,
                            {
                                "status": "completed",
                                "processing_progress": 1.0,
                                "processed_at": doc.updated_at
                            }
                        )
                        print("✅ 文档状态已修复为 'completed'")
                    except Exception as e:
                        print(f"❌ 修复失败: {e}")
                
                print("-" * 40)
        
    except Exception as e:
        logger.error(f"检查文档状态失败: {e}")
    finally:
        db.close()

def check_task_status():
    """检查任务状态"""
    from backend.models.task_management import TaskRecord
    from backend.core.database import get_db_session
    
    db = get_db_session()
    try:
        # 获取最近的任务记录
        tasks = db.query(TaskRecord)\
                 .filter(TaskRecord.task_type == 'document_processing')\
                 .order_by(TaskRecord.created_at.desc())\
                 .limit(10)\
                 .all()
        
        print("\n" + "=" * 80)
        print("最近的文档处理任务状态")
        print("=" * 80)
        
        for task in tasks:
            print(f"\n任务ID: {task.task_id}")
            print(f"任务名称: {task.task_name}")
            print(f"状态: {task.status}")
            print(f"进度: {task.progress_percentage}%")
            print(f"阶段: {task.progress_stage}")
            print(f"详情: {task.progress_detail}")
            print(f"创建时间: {task.created_at}")
            print(f"更新时间: {task.updated_at}")
            print(f"完成时间: {task.completed_at}")
            print(f"错误信息: {task.error_message}")
            print("-" * 40)
            
    except Exception as e:
        logger.error(f"检查任务状态失败: {e}")
    finally:
        db.close()

def check_redis_tasks():
    """检查Redis中的任务状态"""
    try:
        from backend.core.redis_client import get_redis_client
        redis_client = get_redis_client()
        
        print("\n" + "=" * 80)
        print("Redis中的任务进度信息")
        print("=" * 80)
        
        # 获取所有任务进度键
        task_keys = redis_client.keys("task_progress:*")
        
        for key in task_keys[-10:]:  # 只显示最近的10个
            task_id = key.decode('utf-8').replace('task_progress:', '')
            task_data = redis_client.hgetall(key)
            
            print(f"\n任务ID: {task_id}")
            for field, value in task_data.items():
                print(f"  {field.decode('utf-8')}: {value.decode('utf-8')}")
            print("-" * 40)
            
    except Exception as e:
        logger.error(f"检查Redis任务失败: {e}")

if __name__ == "__main__":
    print("开始调试文档状态问题...")
    
    # 检查文档状态
    check_document_status()
    
    # 检查任务状态
    check_task_status()
    
    # 检查Redis任务
    check_redis_tasks()
    
    print("\n调试完成！")
