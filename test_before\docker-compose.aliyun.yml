version: '3.8'

services:
  notebook-app-aliyun:
    build:
      context: .
      dockerfile: Dockerfile.aliyun
    container_name: my-notebook-app-aliyun
    ports:
      - "8502:8501"
    volumes:
      # 持久化数据存储
      - ./chroma_db:/app/chroma_db
      - ./data:/app/data
      - ./knowledge_base:/app/knowledge_base
    environment:
      - PYTHONPATH=/app
      - STREAMLIT_SERVER_PORT=8501
      - STREAMLIT_SERVER_ADDRESS=0.0.0.0
      - STREAMLIT_SERVER_HEADLESS=true
      - STREAMLIT_BROWSER_GATHER_USAGE_STATS=false
      # 阿里云优化配置
      - PIP_INDEX_URL=https://mirrors.aliyun.com/pypi/simple/
      - PIP_TRUSTED_HOST=mirrors.aliyun.com
      # Ollama连接配置
      - OLLAMA_BASE_URL=http://host.docker.internal:11434
    restart: unless-stopped
    networks:
      - notebook-network-aliyun
    healthcheck:
      test: ["CMD", "curl", "--fail", "http://localhost:8501/_stcore/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

networks:
  notebook-network-aliyun:
    driver: bridge 