"""
数据类型转换工具模块

用于处理各种数据类型转换，特别是numpy类型到Python原生类型的转换，
以解决JSON序列化问题。
"""

import numpy as np
from typing import Any, Dict, List, Union
import logging

logger = logging.getLogger(__name__)


def convert_numpy_types(obj: Any) -> Any:
    """
    递归转换numpy类型为Python原生类型，以支持JSON序列化
    
    Args:
        obj: 需要转换的对象
        
    Returns:
        转换后的对象
    """
    if isinstance(obj, np.integer):
        # 处理所有numpy整数类型，包括int64
        return int(obj)
    elif isinstance(obj, np.floating):
        # 处理所有numpy浮点类型
        return float(obj)
    elif isinstance(obj, np.ndarray):
        # 处理numpy数组
        return obj.tolist()
    elif isinstance(obj, np.bool_):
        # 处理numpy布尔类型
        return bool(obj)
    elif isinstance(obj, dict):
        # 递归处理字典
        return {key: convert_numpy_types(value) for key, value in obj.items()}
    elif isinstance(obj, (list, tuple)):
        # 递归处理列表和元组
        converted_list = [convert_numpy_types(item) for item in obj]
        return converted_list if isinstance(obj, list) else tuple(converted_list)
    elif hasattr(obj, '__dict__'):
        # 处理自定义对象
        try:
            return {key: convert_numpy_types(value) for key, value in obj.__dict__.items()}
        except Exception:
            # 如果无法转换，返回字符串表示
            return str(obj)
    else:
        # 其他类型直接返回
        return obj


def ensure_json_serializable(data: Any) -> Any:
    """
    确保数据可以被JSON序列化
    
    Args:
        data: 需要检查的数据
        
    Returns:
        可序列化的数据
    """
    try:
        import json
        # 尝试序列化
        json.dumps(data)
        return data
    except (TypeError, ValueError) as e:
        logger.warning(f"数据不能直接序列化: {e}")
        # 使用convert_numpy_types转换
        converted_data = convert_numpy_types(data)
        try:
            json.dumps(converted_data)
            logger.info("数据转换后可以序列化")
            return converted_data
        except (TypeError, ValueError) as e2:
            logger.error(f"转换后仍无法序列化: {e2}")
            # 最后的备用方案：转换为字符串
            return str(data)


def convert_timestamps(obj: Any) -> Any:
    """
    转换时间戳相关的数据类型
    
    Args:
        obj: 包含时间戳的对象
        
    Returns:
        转换后的对象
    """
    import datetime
    
    if isinstance(obj, datetime.datetime):
        return obj.isoformat()
    elif isinstance(obj, datetime.date):
        return obj.isoformat()
    elif isinstance(obj, datetime.time):
        return obj.isoformat()
    elif isinstance(obj, dict):
        return {key: convert_timestamps(value) for key, value in obj.items()}
    elif isinstance(obj, (list, tuple)):
        converted_list = [convert_timestamps(item) for item in obj]
        return converted_list if isinstance(obj, list) else tuple(converted_list)
    else:
        return obj


def safe_convert_for_json(data: Any) -> Any:
    """
    安全地转换数据以支持JSON序列化
    
    这是一个综合函数，会依次应用numpy类型转换和时间戳转换
    
    Args:
        data: 需要转换的数据
        
    Returns:
        可安全序列化的数据
    """
    try:
        # 第一步：转换numpy类型
        data = convert_numpy_types(data)
        
        # 第二步：转换时间戳
        data = convert_timestamps(data)
        
        # 第三步：确保可序列化
        data = ensure_json_serializable(data)
        
        return data
        
    except Exception as e:
        logger.error(f"数据转换失败: {e}")
        return str(data)
