#!/usr/bin/env python3
"""
音频处理系统测试运行器
统一运行所有音频相关的测试
"""

import sys
import os
import time
import subprocess
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_prerequisites():
    """检查测试前置条件"""
    print("=== 检查测试前置条件 ===")
    
    checks = []
    
    # 1. 检查Python依赖
    try:
        import redis
        import celery
        import fastapi
        import httpx
        checks.append(("Python依赖", True, ""))
    except ImportError as e:
        checks.append(("Python依赖", False, f"缺少依赖: {e}"))
    
    # 2. 检查Redis连接
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0)
        r.ping()
        checks.append(("Redis服务", True, ""))
    except Exception as e:
        checks.append(("Redis服务", False, f"Redis连接失败: {e}"))
    
    # 3. 检查项目结构
    required_dirs = [
        "backend",
        "backend/api",
        "backend/tasks",
        "backend/utils/audio",
        "frontend/src",
        "test"
    ]
    
    missing_dirs = []
    for dir_path in required_dirs:
        if not (project_root / dir_path).exists():
            missing_dirs.append(dir_path)
    
    if missing_dirs:
        checks.append(("项目结构", False, f"缺少目录: {missing_dirs}"))
    else:
        checks.append(("项目结构", True, ""))
    
    # 4. 检查配置文件
    config_files = [
        "backend/core/config.py",
        ".env"
    ]
    
    missing_configs = []
    for config_file in config_files:
        if not (project_root / config_file).exists():
            missing_configs.append(config_file)
    
    if missing_configs:
        checks.append(("配置文件", False, f"缺少文件: {missing_configs}"))
    else:
        checks.append(("配置文件", True, ""))
    
    # 输出检查结果
    all_passed = True
    for check_name, passed, error in checks:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{check_name}: {status}")
        if not passed:
            print(f"  错误: {error}")
            all_passed = False
    
    return all_passed

def check_services():
    """检查必要的服务状态"""
    print("\n=== 检查服务状态 ===")
    
    services = []
    
    # 检查后端服务
    try:
        import httpx
        response = httpx.get("http://localhost:8002/health", timeout=5)
        if response.status_code == 200:
            services.append(("后端服务", True, ""))
        else:
            services.append(("后端服务", False, f"状态码: {response.status_code}"))
    except Exception as e:
        services.append(("后端服务", False, f"连接失败: {e}"))
    
    # 检查Celery Worker
    try:
        from backend.core.task_queue import celery_app
        inspect = celery_app.control.inspect()
        stats = inspect.stats()
        
        if stats:
            worker_count = len(stats)
            services.append(("Celery Worker", True, f"{worker_count} 个Worker"))
        else:
            services.append(("Celery Worker", False, "没有活跃的Worker"))
    except Exception as e:
        services.append(("Celery Worker", False, f"检查失败: {e}"))
    
    # 输出服务状态
    for service_name, running, info in services:
        status = "✅ 运行中" if running else "❌ 未运行"
        print(f"{service_name}: {status}")
        if info:
            print(f"  信息: {info}")
    
    return all(running for _, running, _ in services)

def run_test_suite(test_name, test_file, required_services=True):
    """运行测试套件"""
    print(f"\n{'='*20} {test_name} {'='*20}")
    
    if required_services and not check_services():
        print(f"⚠️  跳过 {test_name}：必要服务未运行")
        return False
    
    try:
        # 运行测试文件
        result = subprocess.run([
            sys.executable, str(project_root / "test" / test_file)
        ], capture_output=True, text=True, timeout=300)
        
        print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        success = result.returncode == 0
        print(f"\n{test_name} 结果: {'✅ 通过' if success else '❌ 失败'}")
        
        return success
        
    except subprocess.TimeoutExpired:
        print(f"❌ {test_name} 超时")
        return False
    except Exception as e:
        print(f"❌ {test_name} 执行失败: {e}")
        return False

def run_unit_tests():
    """运行单元测试"""
    print(f"\n{'='*20} 单元测试 {'='*20}")
    
    try:
        # 使用pytest运行单元测试
        result = subprocess.run([
            sys.executable, "-m", "pytest", 
            str(project_root / "test"),
            "-v", "--tb=short"
        ], capture_output=True, text=True, timeout=300)
        
        print(result.stdout)
        if result.stderr:
            print("错误输出:")
            print(result.stderr)
        
        success = result.returncode == 0
        print(f"\n单元测试结果: {'✅ 通过' if success else '❌ 失败'}")
        
        return success
        
    except subprocess.TimeoutExpired:
        print("❌ 单元测试超时")
        return False
    except FileNotFoundError:
        print("⚠️  pytest未安装，跳过单元测试")
        return True
    except Exception as e:
        print(f"❌ 单元测试执行失败: {e}")
        return False

def generate_test_report(results):
    """生成测试报告"""
    print("\n" + "="*60)
    print("测试报告")
    print("="*60)
    
    total_tests = len(results)
    passed_tests = sum(1 for _, result in results if result)
    
    print(f"总测试数: {total_tests}")
    print(f"通过测试: {passed_tests}")
    print(f"失败测试: {total_tests - passed_tests}")
    print(f"通过率: {passed_tests/total_tests*100:.1f}%")
    
    print("\n详细结果:")
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    # 生成建议
    print("\n建议:")
    if passed_tests == total_tests:
        print("🎉 所有测试通过！音频处理系统工作正常")
        print("✅ 系统已准备好用于生产环境")
    else:
        print("⚠️  部分测试失败，请检查以下项目：")
        
        failed_tests = [name for name, result in results if not result]
        for test_name in failed_tests:
            if "API" in test_name:
                print("  - 检查后端服务是否正常运行")
                print("  - 检查API路由和权限配置")
            elif "任务" in test_name:
                print("  - 检查Celery Worker是否启动")
                print("  - 检查Redis连接和配置")
            elif "工具" in test_name:
                print("  - 检查音频处理依赖库")
                print("  - 检查模型文件是否存在")
    
    return passed_tests == total_tests

def main():
    """主函数"""
    print("🎵 音频处理系统测试套件")
    print("="*60)
    
    start_time = time.time()
    
    # 检查前置条件
    if not check_prerequisites():
        print("\n❌ 前置条件检查失败，请先解决上述问题")
        return 1
    
    # 定义测试套件
    test_suites = [
        ("音频工具测试", "test_audio_processing_api.py", False),  # 不需要服务
        ("音频任务测试", "test_audio_tasks.py", True),           # 需要服务
        ("API集成测试", "test_audio_processing_api.py", True),   # 需要服务
    ]
    
    results = []
    
    # 运行测试套件
    for test_name, test_file, requires_services in test_suites:
        result = run_test_suite(test_name, test_file, requires_services)
        results.append((test_name, result))
    
    # 运行单元测试
    unit_test_result = run_unit_tests()
    results.append(("单元测试", unit_test_result))
    
    # 生成测试报告
    all_passed = generate_test_report(results)
    
    end_time = time.time()
    print(f"\n测试总耗时: {end_time - start_time:.1f} 秒")
    
    return 0 if all_passed else 1

if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n⚠️  测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试运行器出错: {e}")
        sys.exit(1)
