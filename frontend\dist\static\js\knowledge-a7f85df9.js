import{I as t,J as a}from"./index-2c134546.js";const g={getKnowledgeBases(e={}){return t.get("/api/v1/knowledge/",e)},createKnowledgeBase(e){return t.post("/api/v1/knowledge/",e)},getKnowledgeBase(e){return t.get(`/api/v1/knowledge/${e}`)},updateKnowledgeBase(e,n){return t.put(`/api/v1/knowledge/${e}`,n)},deleteKnowledgeBase(e){return t.delete(`/api/v1/knowledge/${e}`)},getDocuments(e,n={}){return t.get(`/api/v1/knowledge/${e}/documents`,n)},uploadDocument(e,n){return t.upload(`/api/v1/knowledge/${e}/documents`,n)},deleteDocument(e,n){return t.delete(`/api/v1/knowledge/${e}/documents/${n}`)},query(e){return t.post("/api/v1/knowledge/query",e)},queryStream(e){const n=new AbortController,r=setTimeout(()=>n.abort(),3e5),l=a(),d={"Content-Type":"application/json"};return l&&(d.Authorization=`Bearer ${l}`),fetch("http://localhost:8002/api/v1/knowledge/query/stream",{method:"POST",headers:d,body:JSON.stringify(e),signal:n.signal}).then(o=>(clearTimeout(r),o)).catch(o=>{throw clearTimeout(r),o})},initializeRAG(e="nomic-embed-text",n="qwen2.5:7b"){return t.post("/api/v1/knowledge/initialize",{embedding_model:e,llm_model:n})},getRAGStatus(){return t.get("/api/v1/knowledge/status")},getStats(){return t.get("/api/v1/knowledge/stats")},updateConfig(e){return t.post("/api/v1/knowledge/config",e)},getConfig(){return t.get("/api/v1/knowledge/config")},uploadTextDocument(e){return t.post("/api/v1/knowledge/documents/upload-text",e)},getAvailableModels(){return t.get("/api/v1/knowledge/models/available")},testConnection(e){return t.post("/api/v1/knowledge/test-connection",e)},clearKnowledgeBase(){return t.delete("/api/v1/knowledge/clear")},rebuildIndex(e){return t.post(`/api/v1/knowledge/${e}/rebuild`)},getAllDocuments(e={}){return t.get("/api/v1/knowledge/documents",e)},getDocumentNodes(e){return t.get(`/api/v1/knowledge/documents/${e}/nodes`)},deleteDocumentById(e){return t.delete(`/api/v1/knowledge/documents/${e}`)}};export{g as k};
