#!/bin/bash
# ===========================================
# 语音处理智能平台 Docker 启动脚本
# ===========================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker和Docker Compose
check_prerequisites() {
    log_info "检查系统环境..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    log_success "Docker环境检查通过"
}

# 检查环境变量文件
check_env_file() {
    log_info "检查环境配置文件..."
    
    if [ ! -f ".env" ]; then
        if [ -f ".env.template" ]; then
            log_warning ".env文件不存在，从模板创建..."
            cp .env.template .env
            log_warning "请编辑.env文件，配置您的环境变量"
            log_warning "特别注意OLLAMA_BASE_URL和EMBEDDING_MODEL配置"
            read -p "是否现在编辑.env文件? (y/n): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                ${EDITOR:-nano} .env
            fi
        else
            log_error ".env.template文件不存在"
            exit 1
        fi
    fi
    
    log_success "环境配置文件检查完成"
}

# 创建数据目录
create_directories() {
    log_info "创建数据持久化目录..."
    
    mkdir -p volumes/{data,logs,models,uploads,redis_data}
    mkdir -p volumes/data/chroma_db
    
    # 设置权限
    chmod -R 755 volumes/
    
    log_success "数据目录创建完成"
}

# 检查虚拟环境
check_venv() {
    log_info "检查虚拟环境..."
    
    if [ ! -d "../.venv" ]; then
        log_error "项目根目录的.venv虚拟环境不存在"
        log_error "请先在项目根目录创建并配置虚拟环境"
        log_error "运行: python -m venv .venv && source .venv/bin/activate && pip install -r requirements.txt"
        exit 1
    fi
    
    # 检查关键依赖
    if ! ../.venv/bin/python -c "import chromadb, torch, transformers" 2>/dev/null; then
        log_error "虚拟环境中缺少关键依赖"
        log_error "请确保已安装: chromadb, torch, transformers"
        exit 1
    fi
    
    log_success "虚拟环境检查通过"
}

# 检查Ollama服务
check_ollama() {
    log_info "检查Ollama服务..."
    
    # 从.env文件读取OLLAMA_BASE_URL
    if [ -f ".env" ]; then
        source .env
    fi
    
    OLLAMA_URL=${OLLAMA_BASE_URL:-"http://localhost:11434"}
    
    if curl -s "$OLLAMA_URL/api/tags" > /dev/null 2>&1; then
        log_success "Ollama服务连接正常"
        
        # 检查嵌入模型
        EMBEDDING_MODEL=${EMBEDDING_MODEL:-"nomic-embed-text:latest"}
        if curl -s "$OLLAMA_URL/api/tags" | grep -q "nomic-embed-text"; then
            log_success "嵌入模型 $EMBEDDING_MODEL 可用"
        else
            log_warning "嵌入模型 $EMBEDDING_MODEL 不存在"
            log_warning "请运行: ollama pull $EMBEDDING_MODEL"
        fi
    else
        log_warning "无法连接到Ollama服务: $OLLAMA_URL"
        log_warning "请确保Ollama服务正在运行"
    fi
}

# 构建Docker镜像
build_images() {
    log_info "构建Docker镜像..."
    
    # 构建Backend镜像
    log_info "构建Backend服务镜像..."
    docker-compose build backend
    
    # 构建Celery镜像
    log_info "构建Celery Worker服务镜像..."
    docker-compose build celery-worker
    
    log_success "Docker镜像构建完成"
}

# 启动服务
start_services() {
    log_info "启动服务..."
    
    # 启动核心服务
    docker-compose up -d redis backend celery-worker
    
    log_info "等待服务启动..."
    sleep 10
    
    # 检查服务状态
    if docker-compose ps | grep -q "Up"; then
        log_success "服务启动成功"
        
        # 显示服务状态
        echo
        log_info "服务状态:"
        docker-compose ps
        
        echo
        log_info "服务访问地址:"
        echo "  Backend API: http://localhost:${BACKEND_PORT:-8002}"
        echo "  健康检查: http://localhost:${BACKEND_PORT:-8002}/health"
        echo "  API文档: http://localhost:${BACKEND_PORT:-8002}/docs"
        
        # 可选启动监控服务
        read -p "是否启动Flower监控服务? (y/n): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            docker-compose --profile monitoring up -d flower
            echo "  Flower监控: http://localhost:5555 (admin:admin123)"
        fi
        
    else
        log_error "服务启动失败"
        docker-compose logs
        exit 1
    fi
}

# 显示日志
show_logs() {
    echo
    log_info "显示服务日志 (Ctrl+C退出):"
    docker-compose logs -f
}

# 主函数
main() {
    echo "🚀 语音处理智能平台 Docker 部署启动"
    echo "=========================================="
    
    # 切换到脚本目录
    cd "$(dirname "$0")/.."
    
    # 执行检查和启动流程
    check_prerequisites
    check_env_file
    create_directories
    check_venv
    check_ollama
    build_images
    start_services
    
    echo
    log_success "🎉 部署完成！"
    echo
    log_info "常用命令:"
    echo "  查看状态: docker-compose ps"
    echo "  查看日志: docker-compose logs -f"
    echo "  停止服务: ./scripts/stop.sh"
    echo "  重启服务: docker-compose restart"
    echo "  健康检查: ./scripts/health-check.sh"
    
    # 询问是否查看日志
    read -p "是否查看实时日志? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        show_logs
    fi
}

# 执行主函数
main "$@"
