#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用Playwright进行生产环境测试
重点监控Celery和后端日志，验证方案二的离线加载效果
"""

import asyncio
import time
import logging
import sys
from pathlib import Path
from playwright.async_api import async_playwright

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_production_playwright.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class PlaywrightProductionTester:
    def __init__(self):
        self.frontend_url = "http://localhost:3000"
        self.audio_center_url = f"{self.frontend_url}/audio-center"
        self.test_audio_path = Path("resource/对话.mp3").resolve()
        
    async def setup_browser(self):
        """设置浏览器"""
        self.playwright = await async_playwright().start()
        self.browser = await self.playwright.chromium.launch(headless=False)
        self.context = await self.browser.new_context()
        self.page = await self.context.new_page()
        
        # 监听控制台消息
        self.page.on("console", lambda msg: logger.info(f"🖥️ 前端控制台: {msg.text}"))
        
    async def cleanup_browser(self):
        """清理浏览器资源"""
        if hasattr(self, 'browser'):
            await self.browser.close()
        if hasattr(self, 'playwright'):
            await self.playwright.stop()
    
    async def navigate_to_audio_center(self):
        """导航到音频中心页面"""
        logger.info(f"🌐 导航到音频中心: {self.audio_center_url}")
        await self.page.goto(self.audio_center_url)
        await self.page.wait_for_load_state('networkidle')
        
        # 等待页面加载完成
        await asyncio.sleep(2)
        
        # 检查页面标题
        title = await self.page.title()
        logger.info(f"📄 页面标题: {title}")
        
    async def upload_audio_file(self):
        """上传音频文件"""
        if not self.test_audio_path.exists():
            logger.error(f"❌ 测试音频文件不存在: {self.test_audio_path}")
            return False
            
        logger.info(f"📁 准备上传音频文件: {self.test_audio_path}")
        
        try:
            # 查找文件上传输入框
            file_input = await self.page.wait_for_selector('input[type="file"]', timeout=10000)
            
            # 上传文件
            await file_input.set_input_files(str(self.test_audio_path))
            logger.info("✅ 音频文件上传成功")
            
            # 等待上传完成
            await asyncio.sleep(3)
            return True
            
        except Exception as e:
            logger.error(f"❌ 音频文件上传失败: {e}")
            return False
    
    async def start_meeting_transcription(self):
        """启动会议转录"""
        logger.info("🎯 启动会议转录任务")
        
        try:
            # 查找会议转录按钮或相关控件
            # 这里需要根据实际的前端界面调整选择器
            transcription_button = await self.page.wait_for_selector(
                'button:has-text("会议转录"), button:has-text("Meeting"), button:has-text("转录")', 
                timeout=10000
            )
            
            await transcription_button.click()
            logger.info("✅ 会议转录任务已启动")
            
            # 等待任务开始处理
            await asyncio.sleep(2)
            return True
            
        except Exception as e:
            logger.error(f"❌ 启动会议转录失败: {e}")
            # 尝试截图以便调试
            await self.page.screenshot(path="meeting_transcription_error.png")
            return False
    
    async def monitor_task_progress(self, timeout=300):
        """监控任务进度"""
        logger.info("📊 开始监控任务进度")
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                # 检查进度条或状态指示器
                progress_elements = await self.page.query_selector_all(
                    '.progress, .el-progress, [class*="progress"], [class*="status"]'
                )
                
                if progress_elements:
                    for element in progress_elements:
                        text = await element.inner_text()
                        if text.strip():
                            logger.info(f"📈 进度更新: {text}")
                
                # 检查是否有完成指示
                completion_indicators = await self.page.query_selector_all(
                    'text="完成", text="Complete", text="成功", text="Success", .success, .completed'
                )
                
                if completion_indicators:
                    logger.info("✅ 检测到任务完成指示")
                    return True
                
                # 检查是否有错误指示
                error_indicators = await self.page.query_selector_all(
                    'text="错误", text="Error", text="失败", text="Failed", .error, .failed'
                )
                
                if error_indicators:
                    logger.error("❌ 检测到任务错误指示")
                    return False
                
                await asyncio.sleep(5)  # 每5秒检查一次
                
            except Exception as e:
                logger.warning(f"⚠️ 监控进度时出现异常: {e}")
                await asyncio.sleep(5)
        
        logger.error(f"❌ 任务监控超时 ({timeout}秒)")
        return False
    
    async def check_transcription_results(self):
        """检查转录结果"""
        logger.info("🔍 检查转录结果")
        
        try:
            # 查找结果显示区域
            result_areas = await self.page.query_selector_all(
                '.result, .transcription, [class*="result"], [class*="transcription"], textarea, .content'
            )
            
            found_results = False
            for area in result_areas:
                text = await area.inner_text()
                if text and len(text.strip()) > 10:  # 有实际内容
                    logger.info(f"✅ 发现转录结果: {text[:100]}...")
                    found_results = True
                    
                    # 检查是否包含说话人信息
                    if "说话人" in text or "Speaker" in text:
                        logger.info("✅ 转录结果包含说话人识别信息")
            
            return found_results
            
        except Exception as e:
            logger.error(f"❌ 检查转录结果失败: {e}")
            return False
    
    async def run_full_test(self):
        """运行完整的Playwright测试"""
        logger.info("🚀 开始Playwright生产环境测试")
        logger.info("=" * 60)
        
        try:
            # 设置浏览器
            await self.setup_browser()
            
            # 步骤1: 导航到音频中心
            logger.info("📋 步骤1: 导航到音频中心页面")
            await self.navigate_to_audio_center()
            
            # 步骤2: 上传音频文件
            logger.info("📋 步骤2: 上传测试音频文件")
            if not await self.upload_audio_file():
                return False
            
            # 步骤3: 启动会议转录
            logger.info("📋 步骤3: 启动会议转录任务")
            if not await self.start_meeting_transcription():
                return False
            
            # 步骤4: 监控任务进度
            logger.info("📋 步骤4: 监控任务执行进度")
            if not await self.monitor_task_progress():
                return False
            
            # 步骤5: 检查转录结果
            logger.info("📋 步骤5: 检查转录结果")
            if not await self.check_transcription_results():
                return False
            
            logger.info("🎉 Playwright生产环境测试完全成功！")
            return True
            
        except Exception as e:
            logger.error(f"❌ 测试执行异常: {e}")
            return False
        finally:
            await self.cleanup_browser()

async def monitor_logs():
    """监控日志的辅助函数"""
    logger.info("👀 请同时监控以下日志:")
    logger.info("   1. Celery Worker日志 (终端1)")
    logger.info("   2. 后端服务器日志 (终端2)")
    logger.info("   3. 重点关注是否有网络请求到ModelScope")
    logger.info("   4. 检查VAD模型是否从本地路径加载")

async def main():
    """主函数"""
    # 显示监控提示
    await monitor_logs()
    
    tester = PlaywrightProductionTester()
    
    try:
        success = await tester.run_full_test()
        
        if success:
            print("\n" + "=" * 60)
            print("🎉 Playwright生产环境测试成功！")
            print("✅ 请检查Celery和后端日志确认:")
            print("   - 无ModelScope网络请求")
            print("   - VAD模型从本地路径加载")
            print("   - 会议转录功能正常工作")
            print("=" * 60)
            return 0
        else:
            print("\n" + "=" * 60)
            print("❌ Playwright生产环境测试失败")
            print("请检查日志文件和截图获取详细信息")
            print("=" * 60)
            return 1
            
    except KeyboardInterrupt:
        logger.info("⚠️ 测试被用户中断")
        return 1
    except Exception as e:
        logger.error(f"❌ 测试执行异常: {e}")
        return 1

if __name__ == "__main__":
    exit(asyncio.run(main()))
