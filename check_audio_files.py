import sqlite3
import os

print("开始检查音频文件...")

# 连接到数据库
db_path = "data/speech_platform.db"
print(f"数据库路径: {db_path}")

if not os.path.exists(db_path):
    print(f"数据库文件不存在: {db_path}")
    exit(1)

print("数据库文件存在，正在连接...")

conn = sqlite3.connect(db_path)
cursor = conn.cursor()

try:
    print("正在查询音频文件表...")

    # 首先检查表是否存在
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='audio_files'")
    table_exists = cursor.fetchone()

    if not table_exists:
        print("audio_files表不存在")
        exit(1)

    print("audio_files表存在，正在查询数据...")

    # 首先查看表结构
    cursor.execute("PRAGMA table_info(audio_files)")
    columns = cursor.fetchall()
    print("audio_files表结构:")
    for col in columns:
        print(f"  {col}")

    # 查询音频文件表
    cursor.execute("SELECT * FROM audio_files ORDER BY id")
    files = cursor.fetchall()

    print(f"数据库中的音频文件数量: {len(files)}")

    if len(files) > 0:
        print("\n音频文件列表:")
        print("所有字段:")
        print("-" * 80)

        for file_record in files:
            print(f"记录: {file_record}")
    else:
        print("没有找到音频文件记录")

    # 查询任务记录表
    print("\n正在查询任务记录表...")
    cursor.execute("SELECT COUNT(*) FROM task_records")
    task_count = cursor.fetchone()[0]
    print(f"任务记录数量: {task_count}")

except Exception as e:
    print(f"查询失败: {e}")
    import traceback
    traceback.print_exc()
finally:
    conn.close()
    print("数据库连接已关闭")
