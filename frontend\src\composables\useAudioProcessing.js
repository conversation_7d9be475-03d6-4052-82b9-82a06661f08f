/**
 * 音频处理组合式函数
 * 提供音频处理相关的API调用和状态管理
 */

import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { audioProcessingAPI } from '@/api/audioProcessing'
import { useWebSocket } from './useWebSocket'

export function useAudioProcessing() {
  // WebSocket连接
  const { connected: wsConnected, subscribeTask, unsubscribeTask, onMessage } = useWebSocket()
  
  // 响应式状态
  const isProcessing = ref(false)
  const currentTaskId = ref('')
  const processingResults = ref([])
  const processingErrors = ref([])
  
  // 任务状态管理
  const taskStatus = reactive({
    overall_progress: 0,
    current_stage: null,
    estimated_time: 0,
    performance_stats: null,
    errors: []
  })
  
  // 处理进度监听器
  const progressListeners = new Map()
  
  /**
   * 启动音频处理任务
   */
  const startAudioProcessing = async (options) => {
    try {
      const { files, mode, config = {} } = options
      
      if (!files || files.length === 0) {
        throw new Error('请选择要处理的音频文件')
      }
      
      isProcessing.value = true
      processingErrors.value = []
      
      // 重置任务状态
      Object.assign(taskStatus, {
        overall_progress: 0,
        current_stage: null,
        estimated_time: 0,
        performance_stats: null,
        errors: []
      })
      
      // 根据处理模式调用相应的API
      let response
      const fileIds = files.map(file => file.uid || file.id)
      
      switch (mode) {
        case 'vad_detection':
          response = await audioProcessingAPI.vadDetection({
            file_ids: fileIds,
            config
          })
          break
          
        case 'speech_recognition':
          response = await audioProcessingAPI.speechRecognition({
            file_ids: fileIds,
            language: config.language || 'auto',
            use_itn: config.use_itn !== false,
            ban_emo_unk: config.ban_emo_unk === true,
            config
          })
          break
          
        case 'speaker_recognition':
          response = await audioProcessingAPI.speakerRecognition({
            file_ids: fileIds,
            clustering_method: config.clustering_method || 'auto',
            expected_speakers: config.expected_speakers || 2,
            similarity_threshold: config.similarity_threshold || 0.7,
            config
          })
          break
          
        case 'meeting_transcription':
          response = await audioProcessingAPI.meetingTranscription({
            file_ids: fileIds,
            language: config.language || 'auto',
            output_format: config.output_format || 'timeline',
            include_timestamps: config.include_timestamps !== false,
            speaker_labeling: config.speaker_labeling !== false,
            // 说话人识别配置
            expected_speakers: config.expected_speakers || 2,
            similarity_threshold: config.similarity_threshold || 0.15,
            clustering_method: config.clustering_method || 'auto',
            config
          })
          break
          
        case 'audio_preprocessing':
          response = await audioProcessingAPI.audioPreprocessing({
            file_ids: fileIds,
            target_sr: config.target_sr || 16000,
            target_channels: config.target_channels || 1,
            normalize: config.normalize !== false,
            denoise: config.denoise !== false,
            config
          })
          break

        case 'audio_enhancement':
          // 音频增强模式：先预处理，再语音识别
          response = await audioProcessingAPI.audioEnhancement({
            file_ids: fileIds,
            language: config.language || 'auto',
            use_itn: config.use_itn !== false,
            ban_emo_unk: config.ban_emo_unk === true,
            normalize: config.normalize !== false,
            denoise: config.denoise !== false,
            config
          })
          break

        case 'comprehensive_analysis':
          // 综合分析需要依次执行多个步骤
          response = await startComprehensiveAnalysis(fileIds, config)
          break

        default:
          throw new Error(`不支持的处理模式: ${mode}`)
      }
      
      // 修复响应数据结构：axios将后端数据包装在response.data中
      const responseData = response.data || response

      if (responseData.success) {
        currentTaskId.value = responseData.task_id

        // 订阅WebSocket进度更新
        if (wsConnected.value) {
          subscribeTask(responseData.task_id)
        }

        // 设置进度监听
        setupProgressMonitoring(responseData.task_id)

        ElMessage.success('音频处理任务已启动')
        return responseData
      } else {
        throw new Error(responseData.message || '启动处理任务失败')
      }
      
    } catch (error) {
      isProcessing.value = false
      processingErrors.value.push({
        timestamp: Date.now(),
        message: error.message || '未知错误'
      })
      
      ElMessage.error(`启动处理失败: ${error.message}`)
      throw error
    }
  }
  
  /**
   * 综合分析处理
   */
  const startComprehensiveAnalysis = async (fileIds, config) => {
    // 综合分析包含多个步骤，需要按顺序执行
    const steps = [
      { name: 'preprocessing', weight: 0.2 },
      { name: 'vad_detection', weight: 0.2 },
      { name: 'speech_recognition', weight: 0.3 },
      { name: 'speaker_recognition', weight: 0.2 },
      { name: 'quality_analysis', weight: 0.1 }
    ]
    
    const results = []
    let totalProgress = 0
    
    for (const step of steps) {
      try {
        taskStatus.current_stage = {
          stage: step.name,
          percentage: 0,
          detail: `执行${step.name}...`
        }
        
        let stepResult
        switch (step.name) {
          case 'preprocessing':
            stepResult = await audioProcessingAPI.audioPreprocessing({
              file_ids: fileIds,
              config: config.preprocessing || {}
            })
            break
          case 'vad_detection':
            stepResult = await audioProcessingAPI.vadDetection({
              file_ids: fileIds,
              config: config.vad || {}
            })
            break
          case 'speech_recognition':
            stepResult = await audioProcessingAPI.speechRecognition({
              file_ids: fileIds,
              config: config.speech || {}
            })
            break
          case 'speaker_recognition':
            stepResult = await audioProcessingAPI.speakerRecognition({
              file_ids: fileIds,
              config: config.speaker || {}
            })
            break
          case 'quality_analysis':
            stepResult = await audioProcessingAPI.qualityAnalysis({
              file_ids: fileIds,
              config: config.quality || {}
            })
            break
        }
        
        results.push({
          step: step.name,
          result: stepResult,
          status: 'success'
        })
        
        totalProgress += step.weight * 100
        taskStatus.overall_progress = totalProgress
        
      } catch (error) {
        results.push({
          step: step.name,
          error: error.message,
          status: 'error'
        })
        
        processingErrors.value.push({
          timestamp: Date.now(),
          message: `${step.name}步骤失败: ${error.message}`
        })
      }
    }
    
    return {
      success: true,
      task_id: `comprehensive_${Date.now()}`,
      message: '综合分析完成',
      results
    }
  }
  
  /**
   * 设置进度监听
   */
  const setupProgressMonitoring = (taskId) => {
    // WebSocket进度监听
    const removeListener = onMessage((data) => {
      if (data.task_id === taskId) {
        handleProgressUpdate(data)
      }
    })
    
    progressListeners.set(taskId, removeListener)
    
    // 轮询备用方案
    if (!wsConnected.value) {
      startProgressPolling(taskId)
    }
  }
  
  /**
   * 处理进度更新
   */
  const handleProgressUpdate = (data) => {
    switch (data.type) {
      case 'progress':
        taskStatus.overall_progress = data.progress || 0
        taskStatus.current_stage = data.stage_info || null
        taskStatus.estimated_time = data.estimated_time || 0
        taskStatus.performance_stats = data.performance || null
        break
        
      case 'task_completed':
        isProcessing.value = false
        taskStatus.overall_progress = 100
        processingResults.value.push({
          task_id: data.task_id,
          result: data.result,
          timestamp: Date.now(),
          status: 'completed'
        })
        
        // 清理监听器
        cleanupTaskMonitoring(data.task_id)
        break
        
      case 'task_failed':
        isProcessing.value = false
        processingErrors.value.push({
          timestamp: Date.now(),
          message: data.error || '任务处理失败'
        })
        
        // 清理监听器
        cleanupTaskMonitoring(data.task_id)
        break
    }
  }
  
  /**
   * 轮询进度（WebSocket不可用时的备用方案）
   */
  const startProgressPolling = (taskId) => {
    const pollInterval = setInterval(async () => {
      try {
        const status = await audioProcessingAPI.getTaskStatus(taskId)
        
        if (status.status === 'completed') {
          clearInterval(pollInterval)
          handleProgressUpdate({
            type: 'task_completed',
            task_id: taskId,
            result: status.result
          })
        } else if (status.status === 'failed') {
          clearInterval(pollInterval)
          handleProgressUpdate({
            type: 'task_failed',
            task_id: taskId,
            error: status.error
          })
        } else {
          // 更新进度
          handleProgressUpdate({
            type: 'progress',
            task_id: taskId,
            progress: status.progress || 0
          })
        }
        
      } catch (error) {
        console.error('轮询任务状态失败:', error)
      }
    }, 2000) // 每2秒轮询一次
    
    // 存储定时器ID以便清理
    progressListeners.set(`poll_${taskId}`, () => clearInterval(pollInterval))
  }
  
  /**
   * 清理任务监听
   */
  const cleanupTaskMonitoring = (taskId) => {
    // 移除WebSocket监听器
    const removeListener = progressListeners.get(taskId)
    if (removeListener) {
      removeListener()
      progressListeners.delete(taskId)
    }
    
    // 移除轮询定时器
    const removePoller = progressListeners.get(`poll_${taskId}`)
    if (removePoller) {
      removePoller()
      progressListeners.delete(`poll_${taskId}`)
    }
    
    // 取消WebSocket订阅
    if (wsConnected.value) {
      unsubscribeTask(taskId)
    }
  }
  
  /**
   * 取消处理任务
   */
  const cancelProcessing = async () => {
    if (!currentTaskId.value) return
    
    try {
      await audioProcessingAPI.cancelTask(currentTaskId.value)
      
      isProcessing.value = false
      cleanupTaskMonitoring(currentTaskId.value)
      currentTaskId.value = ''
      
      ElMessage.info('任务已取消')
      
    } catch (error) {
      ElMessage.error(`取消任务失败: ${error.message}`)
    }
  }
  
  /**
   * 获取处理状态
   */
  const getProcessingStatus = async (taskId) => {
    try {
      return await audioProcessingAPI.getTaskStatus(taskId)
    } catch (error) {
      console.error('获取任务状态失败:', error)
      return null
    }
  }
  
  /**
   * 清理所有监听器
   */
  const cleanup = () => {
    progressListeners.forEach(removeListener => removeListener())
    progressListeners.clear()
    
    isProcessing.value = false
    currentTaskId.value = ''
  }
  
  return {
    // 状态
    isProcessing,
    currentTaskId,
    processingResults,
    processingErrors,
    taskStatus,
    wsConnected,
    
    // 方法
    startAudioProcessing,
    cancelProcessing,
    getProcessingStatus,
    cleanup
  }
}
