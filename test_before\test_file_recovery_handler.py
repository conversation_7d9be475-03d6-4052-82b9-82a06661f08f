import os
import tempfile
import shutil
import time
from pathlib import Path

from utils.file_recovery_handler import (
    FileRecoveryHandler, 
    file_recovery_handler,
    safe_file_upload,
    safe_file_process,
    safe_file_export,
    safe_file_delete
)

class TestFileRecoveryHandler:
    """文件恢复处理器测试"""
    
    def setup_method(self):
        """测试前设置"""
        self.test_dir = Path(tempfile.mkdtemp())
        self.handler = FileRecoveryHandler(
            backup_dir=str(self.test_dir / "backup"),
            recovery_log="test_recovery.log"
        )
        
        # 创建测试文件
        self.test_file = self.test_dir / "test_file.txt"
        self.test_file.write_text("测试内容", encoding='utf-8')
        
    def teardown_method(self):
        """测试后清理"""
        try:
            # 关闭日志处理器避免文件锁定
            for handler in self.handler.logger.handlers[:]:
                handler.close()
                self.handler.logger.removeHandler(handler)
            
            if self.test_dir.exists():
                # 多次尝试删除，处理Windows文件锁定
                for attempt in range(3):
                    try:
                        shutil.rmtree(self.test_dir)
                        break
                    except PermissionError:
                        time.sleep(0.5)
                        if attempt == 2:
                            print(f"警告：无法清理测试目录 {self.test_dir}")
        except Exception as e:
            print(f"清理测试环境时出错: {e}")
    
    def test_create_backup(self):
        """测试创建备份"""
        backup_path = self.handler._create_backup(str(self.test_file), "test")
        
        assert backup_path is not None
        assert os.path.exists(backup_path)
        assert "test" in backup_path
        assert len(self.handler.recovery_records) == 1
        
        # 验证备份内容
        with open(backup_path, 'r', encoding='utf-8') as f:
            content = f.read()
        assert content == "测试内容"
    
    def test_restore_from_backup(self):
        """测试从备份恢复"""
        # 创建备份
        backup_path = self.handler._create_backup(str(self.test_file), "test")
        
        # 删除原文件
        self.test_file.unlink()
        assert not self.test_file.exists()
        
        # 从备份恢复
        result = self.handler._restore_from_backup(backup_path)
        assert result is True
        assert self.test_file.exists()
        
        # 验证恢复内容
        content = self.test_file.read_text(encoding='utf-8')
        assert content == "测试内容"
    
    def test_verify_file_integrity(self):
        """测试文件完整性验证"""
        # 测试正常文件
        is_valid, msg = self.handler.verify_file_integrity(str(self.test_file))
        assert is_valid is True
        assert "完整" in msg
        
        # 测试不存在的文件
        is_valid, msg = self.handler.verify_file_integrity("nonexistent.txt")
        assert is_valid is False
        assert "不存在" in msg
        
        # 测试空文件
        empty_file = self.test_dir / "empty.txt"
        empty_file.touch()
        is_valid, msg = self.handler.verify_file_integrity(str(empty_file))
        assert is_valid is False
        assert "为空" in msg
    
    def test_safe_file_operation_success(self):
        """测试安全文件操作成功情况"""
        def mock_operation(content):
            with open(str(self.test_file), 'w', encoding='utf-8') as f:
                f.write(content)
            return "success"
        
        success, result, error = self.handler.safe_file_operation(
            mock_operation, str(self.test_file), "process", "新内容"
        )
        
        assert success is True
        assert result == "success"
        assert error == ""
        assert self.test_file.read_text(encoding='utf-8') == "新内容"
    
    def test_safe_file_operation_with_recovery(self):
        """测试文件操作失败后的恢复"""
        # 创建备份
        original_content = self.test_file.read_text(encoding='utf-8')
        
        def failing_operation():
            # 先损坏文件
            with open(str(self.test_file), 'w', encoding='utf-8') as f:
                f.write("损坏的内容")
            # 然后抛出异常
            raise ValueError("模拟操作失败")
        
        success, result, error = self.handler.safe_file_operation(
            failing_operation, str(self.test_file), "process"
        )
        
        assert success is False
        assert "操作失败" in error
        
        # 验证文件是否已恢复
        if "(已从备份恢复)" in error:
            current_content = self.test_file.read_text(encoding='utf-8')
            assert current_content == original_content
    
    def test_retry_with_backoff(self):
        """测试带退避的重试机制"""
        attempt_count = [0]
        
        def unreliable_operation():
            attempt_count[0] += 1
            if attempt_count[0] < 3:
                raise ConnectionError("网络错误")
            return "最终成功"
        
        start_time = time.time()
        success, result, error = self.handler.retry_with_backoff(
            unreliable_operation, max_retries=3
        )
        end_time = time.time()
        
        assert success is True
        assert result == "最终成功"
        assert attempt_count[0] == 3
        # 验证有退避延迟（至少1+3=4秒）
        assert end_time - start_time >= 4
    
    def test_retry_max_attempts_exceeded(self):
        """测试超过最大重试次数"""
        def always_failing_operation():
            raise RuntimeError("持续失败")
        
        success, result, error = self.handler.retry_with_backoff(
            always_failing_operation, max_retries=2
        )
        
        assert success is False
        assert result is None
        assert "重试3次后仍然失败" in error
    
    def test_cleanup_old_backups(self):
        """测试清理旧备份"""
        # 创建几个备份
        backup1 = self.handler._create_backup(str(self.test_file), "old")
        time.sleep(0.1)
        backup2 = self.handler._create_backup(str(self.test_file), "new")
        
        # 修改其中一个记录的时间戳为过期
        if backup1 in self.handler.recovery_records:
            record = self.handler.recovery_records[backup1]
            record.timestamp = "2020-01-01T00:00:00"
            self.handler._save_recovery_records()
        
        # 设置短的保留期限
        self.handler.max_backup_age_days = 0.001
        
        # 执行清理
        self.handler._cleanup_old_backups()
        
        # 验证旧备份被清理
        assert not os.path.exists(backup1)
        assert os.path.exists(backup2)
        assert backup1 not in self.handler.recovery_records
        assert backup2 in self.handler.recovery_records
    
    def test_get_recovery_status(self):
        """测试获取恢复状态"""
        # 创建一些备份
        self.handler._create_backup(str(self.test_file), "test1")
        self.handler._create_backup(str(self.test_file), "test2")
        
        status = self.handler.get_recovery_status()
        
        assert "total_recovery_records" in status
        assert status["total_recovery_records"] == 2
        assert "status_distribution" in status
        assert "disk_usage" in status
        assert "backup_directory" in status
    
    def test_emergency_recovery(self):
        """测试紧急恢复"""
        # 创建多个备份
        backup1 = self.handler._create_backup(str(self.test_file), "backup1")
        time.sleep(0.1)
        backup2 = self.handler._create_backup(str(self.test_file), "backup2")
        
        backups = self.handler.emergency_recovery(str(self.test_file))
        
        assert len(backups) == 2
        # 验证按时间排序（最新的在前）
        assert backups[0]["operation_type"] == "backup2"
        assert backups[1]["operation_type"] == "backup1"
    
    def test_convenience_functions(self):
        """测试便捷函数"""
        def mock_upload(file_path, destination):
            return f"上传 {file_path} 到 {destination}"
        
        success, result, error = safe_file_upload(
            mock_upload, str(self.test_file), str(self.test_file), "目标位置"
        )
        
        assert success is True
        assert "上传" in result
        assert error == ""
    
    def test_permission_error_handling(self):
        """测试权限错误处理"""
        def permission_error_operation():
            raise PermissionError("权限被拒绝")
        
        success, result, error = self.handler.safe_file_operation(
            permission_error_operation, str(self.test_file), "process"
        )
        
        assert success is False
        assert "权限错误" in error
    
    def test_disk_space_error_handling(self):
        """测试磁盘空间错误处理"""
        # 设置极高的最小空间要求
        self.handler.min_free_space_mb = 999999999
        
        def space_consuming_operation():
            # 这个操作在磁盘空间检查时就会失败
            return "不应该执行到这里"
        
        success, result, error = self.handler.safe_file_operation(
            space_consuming_operation, str(self.test_file), "process"
        )
        
        assert success is False
        assert "磁盘空间不足" in error

def test_global_instance():
    """测试全局实例"""
    assert file_recovery_handler is not None
    assert isinstance(file_recovery_handler, FileRecoveryHandler)

if __name__ == "__main__":
    # 简单测试运行
    test_handler = TestFileRecoveryHandler()
    test_handler.setup_method()
    
    try:
        print("开始测试文件恢复处理器...")
        
        # 测试备份和恢复
        test_handler.test_create_backup()
        print("✓ 备份创建测试通过")
        
        test_handler.test_restore_from_backup()
        print("✓ 备份恢复测试通过")
        
        test_handler.test_verify_file_integrity()
        print("✓ 文件完整性验证测试通过")
        
        test_handler.test_safe_file_operation_success()
        print("✓ 安全文件操作测试通过")
        
        test_handler.test_retry_with_backoff()
        print("✓ 重试机制测试通过")
        
        test_handler.test_get_recovery_status()
        print("✓ 恢复状态获取测试通过")
        
        test_handler.test_convenience_functions()
        print("✓ 便捷函数测试通过")
        
        print("所有基础测试通过！文件恢复处理器工作正常。")
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        test_handler.teardown_method() 