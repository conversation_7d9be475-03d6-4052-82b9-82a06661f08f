#!/usr/bin/env python3
"""检查会议转录任务状态"""

import sqlite3
import sys
import json
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def check_meeting_tasks():
    """检查会议转录任务"""
    db_path = project_root / "data" / "speech_platform.db"
    
    if not db_path.exists():
        print(f"数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # 查找最新的会议转录任务
        cursor.execute("""
            SELECT task_id, task_name, status, progress_percentage, 
                   created_at, completed_at, result, error_message
            FROM task_records 
            WHERE task_name LIKE '%对话.mp3%' 
            OR task_id LIKE '%meeting_transcription%'
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        
        results = cursor.fetchall()
        
        if not results:
            print("没有找到会议转录任务")
            return
        
        print("会议转录任务:")
        print("-" * 100)
        
        for row in results:
            task_id, task_name, status, progress, created_at, completed_at, result, error = row
            print(f"任务ID: {task_id}")
            print(f"任务名称: {task_name}")
            print(f"状态: {status}")
            print(f"进度: {progress}%")
            print(f"创建时间: {created_at}")
            print(f"完成时间: {completed_at}")
            print(f"错误信息: {error or '无'}")
            
            if result:
                try:
                    result_data = json.loads(result)
                    print(f"结果数据: {json.dumps(result_data, ensure_ascii=False, indent=2)[:500]}...")
                except:
                    print(f"结果数据: {result[:200]}...")
            else:
                print("结果数据: 无")
            
            print("-" * 100)
        
        # 检查processing_results表中的结果
        cursor.execute("""
            SELECT pr.id, pr.processing_type, pr.result_data, pr.status, pr.created_at,
                   af.filename, af.original_filename
            FROM processing_results pr
            JOIN audio_files af ON pr.audio_file_id = af.id
            WHERE af.original_filename = '对话.mp3'
            ORDER BY pr.created_at DESC
            LIMIT 3
        """)
        
        processing_results = cursor.fetchall()
        
        if processing_results:
            print("\n处理结果:")
            print("-" * 100)
            
            for row in processing_results:
                pr_id, proc_type, result_data, status, created_at, filename, orig_filename = row
                print(f"处理ID: {pr_id}")
                print(f"处理类型: {proc_type}")
                print(f"状态: {status}")
                print(f"文件名: {orig_filename}")
                print(f"创建时间: {created_at}")
                
                if result_data:
                    try:
                        data = json.loads(result_data)
                        print(f"结果数据: {json.dumps(data, ensure_ascii=False, indent=2)[:500]}...")
                    except:
                        print(f"结果数据: {result_data[:200]}...")
                else:
                    print("结果数据: 无")
                
                print("-" * 100)
        
        conn.close()
        
    except Exception as e:
        print(f"检查任务时出错: {e}")

if __name__ == "__main__":
    check_meeting_tasks()
