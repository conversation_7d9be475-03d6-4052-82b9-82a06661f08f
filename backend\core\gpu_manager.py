"""
GPU资源管理器
防止多进程GPU访问冲突，确保系统稳定性
"""

import os
import time
import threading
import logging
from typing import Optional, Dict, Any
from pathlib import Path
import psutil

logger = logging.getLogger(__name__)

class GPUResourceManager:
    """GPU资源管理器 - 单例模式"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
            
        self._initialized = True
        self.gpu_lock = threading.Lock()
        self.current_process_id = os.getpid()
        self.gpu_in_use = False
        self.current_task_id = None
        
        # GPU状态文件路径
        self.gpu_state_file = Path.cwd() / ".gpu_state"
        
        logger.info(f"GPU资源管理器初始化完成，进程ID: {self.current_process_id}")
    
    def acquire_gpu(self, task_id: str, timeout: int = 30) -> bool:
        """
        获取GPU资源
        
        Args:
            task_id: 任务ID
            timeout: 超时时间（秒）
            
        Returns:
            bool: 是否成功获取GPU资源
        """
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                with self.gpu_lock:
                    # 检查是否有其他进程在使用GPU
                    if self._is_gpu_available():
                        self.gpu_in_use = True
                        self.current_task_id = task_id
                        
                        # 写入状态文件
                        self._write_gpu_state(task_id)
                        
                        logger.info(f"✅ 任务 {task_id} 成功获取GPU资源")
                        return True
                    else:
                        logger.debug(f"GPU资源被占用，任务 {task_id} 等待中...")
                        
            except Exception as e:
                logger.error(f"获取GPU资源时发生错误: {e}")
                
            time.sleep(1)  # 等待1秒后重试
        
        logger.warning(f"❌ 任务 {task_id} 获取GPU资源超时")
        return False
    
    def release_gpu(self, task_id: str):
        """
        释放GPU资源
        
        Args:
            task_id: 任务ID
        """
        try:
            with self.gpu_lock:
                if self.current_task_id == task_id:
                    self.gpu_in_use = False
                    self.current_task_id = None
                    
                    # 清理状态文件
                    self._clear_gpu_state()
                    
                    # 清理CUDA缓存
                    self._safe_cuda_cleanup()
                    
                    logger.info(f"✅ 任务 {task_id} 释放GPU资源")
                else:
                    logger.warning(f"任务 {task_id} 尝试释放不属于它的GPU资源")
                    
        except Exception as e:
            logger.error(f"释放GPU资源时发生错误: {e}")
    
    def _is_gpu_available(self) -> bool:
        """检查GPU是否可用"""
        try:
            # 检查状态文件
            if self.gpu_state_file.exists():
                state_info = self._read_gpu_state()
                if state_info:
                    # 检查占用进程是否还存在
                    if psutil.pid_exists(state_info.get('pid', 0)):
                        return False
                    else:
                        # 进程已不存在，清理状态文件
                        self._clear_gpu_state()
            
            # 检查当前进程状态
            return not self.gpu_in_use
            
        except Exception as e:
            logger.error(f"检查GPU可用性时发生错误: {e}")
            return False
    
    def _write_gpu_state(self, task_id: str):
        """写入GPU状态文件"""
        try:
            state_info = {
                'task_id': task_id,
                'pid': self.current_process_id,
                'timestamp': time.time()
            }
            
            import json
            with open(self.gpu_state_file, 'w') as f:
                json.dump(state_info, f)
                
        except Exception as e:
            logger.error(f"写入GPU状态文件失败: {e}")
    
    def _read_gpu_state(self) -> Optional[Dict[str, Any]]:
        """读取GPU状态文件"""
        try:
            if self.gpu_state_file.exists():
                import json
                with open(self.gpu_state_file, 'r') as f:
                    return json.load(f)
        except Exception as e:
            logger.error(f"读取GPU状态文件失败: {e}")
        return None
    
    def _clear_gpu_state(self):
        """清理GPU状态文件"""
        try:
            if self.gpu_state_file.exists():
                self.gpu_state_file.unlink()
        except Exception as e:
            logger.error(f"清理GPU状态文件失败: {e}")
    
    def _safe_cuda_cleanup(self):
        """安全的CUDA清理"""
        try:
            import torch
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.synchronize()
                logger.debug("CUDA缓存清理完成")
        except Exception as e:
            logger.warning(f"CUDA清理失败: {e}")
    
    def get_gpu_status(self) -> Dict[str, Any]:
        """获取GPU状态信息"""
        try:
            import torch
            
            status = {
                'gpu_available': torch.cuda.is_available(),
                'gpu_in_use': self.gpu_in_use,
                'current_task': self.current_task_id,
                'process_id': self.current_process_id
            }
            
            if torch.cuda.is_available():
                status.update({
                    'device_count': torch.cuda.device_count(),
                    'current_device': torch.cuda.current_device(),
                    'memory_allocated': torch.cuda.memory_allocated(),
                    'memory_reserved': torch.cuda.memory_reserved()
                })
            
            return status
            
        except Exception as e:
            logger.error(f"获取GPU状态失败: {e}")
            return {'error': str(e)}


# 全局GPU管理器实例
gpu_manager = GPUResourceManager()


def get_gpu_manager() -> GPUResourceManager:
    """获取GPU管理器实例"""
    return gpu_manager


class GPUContext:
    """GPU上下文管理器"""
    
    def __init__(self, task_id: str, timeout: int = 30):
        self.task_id = task_id
        self.timeout = timeout
        self.acquired = False
    
    def __enter__(self):
        self.acquired = gpu_manager.acquire_gpu(self.task_id, self.timeout)
        if not self.acquired:
            raise RuntimeError(f"无法获取GPU资源，任务: {self.task_id}")
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.acquired:
            gpu_manager.release_gpu(self.task_id)
