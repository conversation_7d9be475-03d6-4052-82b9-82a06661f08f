#!/usr/bin/env python3
"""
测试优化后的内存使用情况
验证FunASR优化管理器的内存管理效果
"""

import os
import sys
import gc
import time
import psutil
import torch
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def monitor_memory(label: str):
    """监控内存使用"""
    memory = psutil.virtual_memory()
    process = psutil.Process()
    process_memory = process.memory_info().rss / 1024**3  # GB
    
    gpu_info = ""
    if torch.cuda.is_available():
        allocated = torch.cuda.memory_allocated() / 1024**3
        reserved = torch.cuda.memory_reserved() / 1024**3
        gpu_info = f", GPU: {allocated:.1f}GB/{reserved:.1f}GB"
    
    print(f"[{label}] 系统: {memory.percent:.1f}%, 进程: {process_memory:.1f}GB{gpu_info}")
    return process_memory

def test_original_funasr():
    """测试原始FunASR加载方式"""
    print("\n🔴 测试原始FunASR加载方式...")
    
    try:
        from funasr import AutoModel
        
        model_path = "./models/SenseVoiceSmall"
        if not os.path.exists(model_path):
            print(f"⚠️ 模型路径不存在: {model_path}")
            return
        
        initial_memory = monitor_memory("原始方式-开始")
        
        # 原始加载方式（CPU模式）
        model = AutoModel(
            model=model_path,
            trust_remote_code=True,
            device='cpu',  # 强制CPU模式
            local_files_only=True,
            disable_update=True
        )
        
        after_load_memory = monitor_memory("原始方式-加载后")
        
        # 测试推理
        test_audio = "resource/对话.mp3"
        if os.path.exists(test_audio):
            result = model.generate(input=test_audio)
            after_inference_memory = monitor_memory("原始方式-推理后")
        
        # 清理
        del model
        gc.collect()
        
        final_memory = monitor_memory("原始方式-清理后")
        
        print(f"📊 原始方式内存增长: {final_memory - initial_memory:.1f}GB")
        
    except Exception as e:
        print(f"❌ 原始方式测试失败: {e}")

def test_optimized_funasr():
    """测试优化的FunASR管理器"""
    print("\n🟢 测试优化的FunASR管理器...")
    
    try:
        from backend.utils.audio.optimized_funasr_manager import get_funasr_manager, cleanup_funasr_manager
        
        model_path = "./models/SenseVoiceSmall"
        if not os.path.exists(model_path):
            print(f"⚠️ 模型路径不存在: {model_path}")
            return
        
        initial_memory = monitor_memory("优化方式-开始")
        
        # 获取优化管理器
        funasr_manager = get_funasr_manager()
        
        # 加载模型（GPU模式）
        success = funasr_manager.load_model(model_path)
        if not success:
            print("❌ 优化管理器模型加载失败")
            return
        
        after_load_memory = monitor_memory("优化方式-加载后")
        
        # 测试推理
        test_audio = "resource/对话.mp3"
        if os.path.exists(test_audio):
            result = funasr_manager.generate(test_audio)
            after_inference_memory = monitor_memory("优化方式-推理后")
        
        # 清理
        cleanup_funasr_manager()
        gc.collect()
        
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        final_memory = monitor_memory("优化方式-清理后")
        
        print(f"📊 优化方式内存增长: {final_memory - initial_memory:.1f}GB")
        
    except Exception as e:
        print(f"❌ 优化方式测试失败: {e}")

def test_multiple_loads_optimized():
    """测试优化管理器的多次加载"""
    print("\n🔄 测试优化管理器多次加载...")
    
    try:
        from backend.utils.audio.optimized_funasr_manager import get_funasr_manager, cleanup_funasr_manager
        
        model_path = "./models/SenseVoiceSmall"
        initial_memory = monitor_memory("多次加载-开始")
        
        for i in range(3):
            print(f"\n--- 第{i+1}次加载 ---")
            
            # 获取管理器
            funasr_manager = get_funasr_manager()
            
            # 加载模型
            funasr_manager.load_model(model_path)
            load_memory = monitor_memory(f"第{i+1}次加载后")
            
            # 测试推理
            test_audio = "resource/对话.mp3"
            if os.path.exists(test_audio):
                result = funasr_manager.generate(test_audio)
            
            inference_memory = monitor_memory(f"第{i+1}次推理后")
            
            # 清理
            cleanup_funasr_manager()
            gc.collect()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            cleanup_memory = monitor_memory(f"第{i+1}次清理后")
            
            time.sleep(2)  # 等待内存稳定
        
        final_memory = monitor_memory("多次加载-最终")
        print(f"📊 多次加载总内存增长: {final_memory - initial_memory:.1f}GB")
        
    except Exception as e:
        print(f"❌ 多次加载测试失败: {e}")

def test_memory_leak_detection():
    """内存泄漏检测"""
    print("\n🔍 内存泄漏检测...")
    
    try:
        from backend.utils.audio.optimized_funasr_manager import get_funasr_manager, cleanup_funasr_manager
        
        model_path = "./models/SenseVoiceSmall"
        test_audio = "resource/对话.mp3"
        
        # 记录基线内存
        baseline_memory = monitor_memory("基线")
        
        # 执行多次推理循环
        for i in range(5):
            print(f"推理循环 {i+1}/5")
            
            # 加载模型
            funasr_manager = get_funasr_manager()
            funasr_manager.load_model(model_path)
            
            # 执行推理
            if os.path.exists(test_audio):
                result = funasr_manager.generate(test_audio)
            
            # 清理
            cleanup_funasr_manager()
            gc.collect()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            # 监控内存
            current_memory = monitor_memory(f"循环{i+1}后")
            
            # 检查内存增长
            memory_growth = current_memory - baseline_memory
            if memory_growth > 1.0:  # 超过1GB认为有泄漏
                print(f"⚠️ 检测到内存增长: {memory_growth:.1f}GB")
            
            time.sleep(1)
        
        # 最终检查
        final_memory = monitor_memory("最终检查")
        total_growth = final_memory - baseline_memory
        
        if total_growth < 0.5:  # 小于500MB认为正常
            print(f"✅ 内存管理良好，总增长: {total_growth:.1f}GB")
        elif total_growth < 1.0:  # 小于1GB认为可接受
            print(f"⚠️ 轻微内存增长: {total_growth:.1f}GB")
        else:
            print(f"🚨 检测到内存泄漏: {total_growth:.1f}GB")
        
    except Exception as e:
        print(f"❌ 内存泄漏检测失败: {e}")

def main():
    """主函数"""
    print("🚀 优化内存使用测试")
    print("=" * 60)
    
    # 检查GPU状态
    if torch.cuda.is_available():
        print(f"🎯 GPU可用: {torch.cuda.get_device_name(0)}")
        print(f"📊 GPU显存: {torch.cuda.get_device_properties(0).total_memory/1024**3:.1f}GB")
    else:
        print("⚠️ GPU不可用")
    
    # 初始状态
    initial_memory = monitor_memory("系统初始状态")
    
    # 测试1: 原始FunASR方式（CPU模式）
    test_original_funasr()
    
    # 等待内存稳定
    print("\n⏳ 等待内存稳定...")
    time.sleep(5)
    gc.collect()
    
    # 测试2: 优化FunASR管理器（GPU模式）
    test_optimized_funasr()
    
    # 等待内存稳定
    print("\n⏳ 等待内存稳定...")
    time.sleep(5)
    gc.collect()
    
    # 测试3: 多次加载测试
    test_multiple_loads_optimized()
    
    # 等待内存稳定
    print("\n⏳ 等待内存稳定...")
    time.sleep(5)
    gc.collect()
    
    # 测试4: 内存泄漏检测
    test_memory_leak_detection()
    
    # 最终状态
    print("\n" + "=" * 60)
    final_memory = monitor_memory("测试完成")
    
    total_growth = final_memory - initial_memory
    print(f"\n📋 测试总结:")
    print(f"  总内存增长: {total_growth:.1f}GB")
    
    if total_growth < 1.0:
        print("🎉 内存优化效果良好！")
    elif total_growth < 2.0:
        print("✅ 内存使用可接受")
    else:
        print("⚠️ 仍需进一步优化")

if __name__ == "__main__":
    main()
