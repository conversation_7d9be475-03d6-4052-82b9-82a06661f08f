#!/usr/bin/env python3
"""快速测试UI异常处理器"""

import sys
import os
sys.path.append('utils')

def test_ui_exception_handler():
    """测试UI异常处理器基本功能"""
    try:
        from utils.ui_exception_handler import UIExceptionHandler
        
        print("✅ UI异常处理器导入成功")
        
        # 创建处理器实例
        handler = UIExceptionHandler()
        print(f"✅ 处理器实例创建成功")
        
        # 检查错误消息配置
        print(f"✅ 错误消息配置已加载，共{len(handler.error_messages)}条消息")
        
        # 测试错误消息获取
        test_exception = Exception("测试异常")
        friendly_message = handler._get_friendly_message(test_exception)
        print(f"✅ 友好消息生成成功: {friendly_message[:50]}...")
        
        # 测试严重程度和图标映射
        print(f"✅ 严重程度图标映射: {len(handler.severity_icons)}个级别")
        print(f"✅ 类别图标映射: {len(handler.category_icons)}个类别")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_exception_types():
    """测试不同异常类型"""
    try:
        from utils.exception_handler import (
            AudioProcessingError, 
            ModelLoadingError, 
            ErrorSeverity
        )
        
        print("✅ 异常类型导入成功")
        
        # 测试创建自定义异常
        audio_error = AudioProcessingError(
            "测试音频处理错误",
            severity=ErrorSeverity.HIGH,
            suggestion="这是一个测试建议"
        )
        print(f"✅ 音频处理异常创建成功: {audio_error}")
        
        model_error = ModelLoadingError(
            "测试模型加载错误",
            severity=ErrorSeverity.CRITICAL
        )
        print(f"✅ 模型加载异常创建成功: {model_error}")
        
        return True
        
    except Exception as e:
        print(f"❌ 异常类型测试失败: {e}")
        return False

def test_error_config():
    """测试错误配置文件"""
    try:
        import json
        from pathlib import Path
        
        config_file = Path("config/ui_error_messages.json")
        if config_file.exists():
            print(f"✅ 错误消息配置文件存在: {config_file}")
            
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            print(f"✅ 配置文件加载成功，包含{len(config)}条错误消息")
            
            # 检查几个关键消息
            key_messages = [
                "audio_processing_general",
                "model_loading_failed", 
                "file_not_found",
                "unknown_error"
            ]
            
            for key in key_messages:
                if key in config:
                    print(f"✅ 关键消息存在: {key}")
                else:
                    print(f"⚠️ 关键消息缺失: {key}")
            
            return True
        else:
            print(f"⚠️ 错误消息配置文件不存在: {config_file}")
            return False
            
    except Exception as e:
        print(f"❌ 配置文件测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🧪 开始UI异常处理器快速测试")
    print("=" * 50)
    
    # 运行测试
    test1 = test_ui_exception_handler()
    print()
    
    test2 = test_exception_types()
    print()
    
    test3 = test_error_config()
    print()
    
    # 总结
    print("=" * 50)
    if test1 and test2 and test3:
        print("✅ 所有测试通过！UI异常处理器功能正常")
    else:
        print("❌ 部分测试失败，请检查相关模块")
    
    print("🎯 任务13.4 - 用户界面异常提示 实施完成") 