#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的端到端测试 - 验证数据结构修复
直接测试_integrate_meeting_results函数而不依赖完整的服务
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from backend.tasks.audio_processing_tasks import _integrate_meeting_results


def test_meeting_transcription_data_structure():
    """测试会议转录数据结构修复"""
    print("🧪 测试会议转录数据结构修复...")
    
    # 模拟真实的会议转录数据
    vad_segments = [
        {'start_time': 0.0, 'end_time': 2.5, 'duration': 2.5},
        {'start_time': 3.0, 'end_time': 5.5, 'duration': 2.5},
        {'start_time': 6.0, 'end_time': 8.0, 'duration': 2.0}
    ]
    
    recognition_segments = [
        {
            'start_time': 0.0, 'end_time': 2.5, 'duration': 2.5,
            'text': '你好，今天的会议开始了', 'confidence': 0.95,
            'language': 'zh', 'emotions': [], 'events': []
        },
        {
            'start_time': 3.0, 'end_time': 5.5, 'duration': 2.5,
            'text': '好的，我们来讨论项目进展', 'confidence': 0.92,
            'language': 'zh', 'emotions': [], 'events': []
        },
        {
            'start_time': 6.0, 'end_time': 8.0, 'duration': 2.0,
            'text': '这个功能需要优化', 'confidence': 0.88,
            'language': 'zh', 'emotions': [], 'events': []
        }
    ]
    
    speaker_segments = [
        {'id': 0, 'name': '说话人1'},
        {'id': 1, 'name': '说话人2'}
    ]
    
    full_text = "你好，今天的会议开始了。好的，我们来讨论项目进展。这个功能需要优化。"
    
    # 调用修复后的函数
    result = _integrate_meeting_results(
        vad_segments,
        recognition_segments,
        speaker_segments,
        full_text
    )
    
    # 验证数据结构
    print("📊 验证返回的数据结构...")
    
    # 1. 验证基本结构
    assert isinstance(result, dict), "❌ 返回结果应该是字典"
    print("✅ 返回结果是字典")
    
    # 2. 验证必需字段
    required_fields = ['speech_segments', 'speaker_segments', 'text', 'total_segments', 'total_speakers']
    for field in required_fields:
        assert field in result, f"❌ 缺少必需字段: {field}"
        print(f"✅ 包含字段: {field}")
    
    # 3. 验证speech_segments结构
    speech_segments = result['speech_segments']
    assert isinstance(speech_segments, list), "❌ speech_segments应该是数组"
    assert len(speech_segments) > 0, "❌ 应该有语音片段"
    print(f"✅ speech_segments包含 {len(speech_segments)} 个片段")
    
    # 4. 验证每个语音片段的结构
    for i, segment in enumerate(speech_segments):
        segment_fields = ['segment_id', 'start_time', 'end_time', 'duration', 'text', 'speaker_id', 'speaker_name', 'confidence']
        for field in segment_fields:
            assert field in segment, f"❌ 片段 {i} 缺少字段: {field}"
        print(f"✅ 片段 {i}: {segment['speaker_name']} - {segment['text'][:20]}...")
    
    # 5. 验证speaker_segments结构
    speaker_segments = result['speaker_segments']
    assert isinstance(speaker_segments, list), "❌ speaker_segments应该是数组"
    print(f"✅ speaker_segments包含 {len(speaker_segments)} 个说话人")
    
    for i, speaker in enumerate(speaker_segments):
        speaker_fields = ['name', 'segment_count', 'total_time']
        for field in speaker_fields:
            assert field in speaker, f"❌ 说话人 {i} 缺少字段: {field}"
        print(f"✅ 说话人 {i}: {speaker['name']} - {speaker['segment_count']}个片段, {speaker['total_time']:.1f}秒")
    
    # 6. 验证统计信息
    assert result['total_segments'] == len(speech_segments), "❌ total_segments统计错误"
    assert result['total_speakers'] == len(speaker_segments), "❌ total_speakers统计错误"
    print(f"✅ 统计信息正确: {result['total_segments']}个片段, {result['total_speakers']}个说话人")
    
    print("🎉 会议转录数据结构修复验证通过！")
    return True


def test_empty_input_handling():
    """测试空输入处理"""
    print("\n🧪 测试空输入处理...")
    
    result = _integrate_meeting_results([], [], [], "")
    
    # 验证空输入返回正确的结构
    assert isinstance(result, dict), "❌ 空输入应该返回字典"
    assert result['speech_segments'] == [], "❌ 空输入的speech_segments应该为空数组"
    assert result['speaker_segments'] == [], "❌ 空输入的speaker_segments应该为空数组"
    assert result['total_segments'] == 0, "❌ 空输入的total_segments应该为0"
    assert result['total_speakers'] == 0, "❌ 空输入的total_speakers应该为0"
    
    print("✅ 空输入处理正确")
    return True


def test_frontend_compatibility():
    """测试前端兼容性"""
    print("\n🧪 测试前端兼容性...")
    
    # 模拟前端期望的数据处理
    vad_segments = [{'start_time': 0.0, 'end_time': 2.0, 'duration': 2.0}]
    recognition_segments = [{'start_time': 0.0, 'end_time': 2.0, 'duration': 2.0, 'text': '测试文本', 'confidence': 0.9, 'language': 'zh', 'emotions': [], 'events': []}]
    speaker_segments = [{'id': 0, 'name': '说话人1'}]
    full_text = "测试文本"
    
    result = _integrate_meeting_results(vad_segments, recognition_segments, speaker_segments, full_text)
    
    # 模拟前端的formatMeetingTranscriptionText函数处理
    speech_segments = result.get('speech_segments', [])
    speaker_segments = result.get('speaker_segments', [])
    
    # 验证前端能够正确处理数据
    assert len(speech_segments) > 0, "❌ 前端应该能获取到语音片段"
    assert len(speaker_segments) > 0, "❌ 前端应该能获取到说话人信息"
    
    # 验证前端期望的字段都存在
    if speech_segments:
        segment = speech_segments[0]
        assert 'speaker_name' in segment, "❌ 前端期望的speaker_name字段缺失"
        assert 'text' in segment, "❌ 前端期望的text字段缺失"
        assert 'start_time' in segment, "❌ 前端期望的start_time字段缺失"
        assert 'end_time' in segment, "❌ 前端期望的end_time字段缺失"
    
    print("✅ 前端兼容性验证通过")
    return True


def main():
    """主测试函数"""
    print("🚀 开始简化端到端测试")
    print("=" * 60)
    
    tests = [
        ("会议转录数据结构修复", test_meeting_transcription_data_structure),
        ("空输入处理", test_empty_input_handling),
        ("前端兼容性", test_frontend_compatibility)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 执行测试: {test_name}")
            result = test_func()
            if result:
                print(f"✅ {test_name} 通过")
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print(f"\n📊 测试结果汇总:")
    print("=" * 60)
    print(f"通过: {passed}/{total} 个测试")
    
    if passed == total:
        print("🎉 所有测试通过！数据结构修复成功！")
        return True
    else:
        print("❌ 部分测试失败")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
