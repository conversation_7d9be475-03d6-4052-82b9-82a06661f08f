#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试结果展示和编辑页面功能
验证页面组件是否正常工作
"""

import sys
import os
import tempfile
import json
from datetime import datetime

# 添加utils目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'utils'))

try:
    from utils.result_data_structures import (
        TimeInterval, SpeechSegment, SpeakerProfile, ProcessingMetrics, AnalysisResult,
        create_speech_segment, create_analysis_result
    )
    print("✅ 成功导入结果数据结构模块")
except ImportError as e:
    print(f"❌ 导入结果数据结构失败: {e}")
    sys.exit(1)

def test_demo_data_creation():
    """测试演示数据创建"""
    print("\n🧪 测试演示数据创建...")
    
    # 创建分析结果
    analysis = AnalysisResult(
        audio_file_path="test_conversation.wav",
        audio_duration=30.0,
        processing_status="completed"
    )
    
    # 添加说话人
    speaker1 = SpeakerProfile(
        id="speaker_001",
        name="测试说话人1",
        gender="male",
        age_estimate="30-40"
    )
    speaker2 = SpeakerProfile(
        id="speaker_002", 
        name="测试说话人2",
        gender="female",
        age_estimate="25-35"
    )
    analysis.add_speaker(speaker1)
    analysis.add_speaker(speaker2)
    
    # 添加语音片段
    test_segments = [
        (0.0, 5.0, "这是第一段测试语音。", "speaker_001", 0.95, "neutral", "speech"),
        (5.5, 10.0, "这是第二段测试语音。", "speaker_002", 0.92, "positive", "speech"),
        (11.0, 16.0, "这是第三段测试语音。", "speaker_001", 0.89, "neutral", "speech"),
        (17.0, 22.0, "这是第四段测试语音。", "speaker_002", 0.91, "agreement", "speech"),
        (23.0, 28.0, "这是最后一段测试语音。", "speaker_001", 0.88, "conclusion", "speech")
    ]
    
    for start, end, text, speaker_id, confidence, emotion, event in test_segments:
        segment = create_speech_segment(
            start_time=start,
            end_time=end,
            text=text,
            speaker_id=speaker_id,
            confidence=confidence,
            emotion=emotion,
            event=event
        )
        analysis.add_segment(segment)
    
    # 更新处理指标
    analysis.metrics.total_processing_time = 5.2
    analysis.metrics.recognition_time = 3.8
    analysis.metrics.speaker_analysis_time = 1.1
    analysis.metrics.avg_recognition_confidence = 0.91
    analysis.metrics.avg_speaker_confidence = 0.88
    
    # 验证创建结果
    assert analysis.speaker_count == 2, f"期望说话人数2，实际{analysis.speaker_count}"
    assert len(analysis.segments) == 5, f"期望片段数5，实际{len(analysis.segments)}"
    assert analysis.total_speech_time > 0, f"总语音时长应该大于0，实际{analysis.total_speech_time}"
    
    print(f"  ✅ 演示数据创建成功:")
    print(f"    - 说话人数: {analysis.speaker_count}")
    print(f"    - 片段数: {len(analysis.segments)}")
    print(f"    - 总时长: {analysis.audio_duration:.1f}秒")
    print(f"    - 有效语音时长: {analysis.total_speech_time:.1f}秒")
    print(f"    - 平均置信度: {analysis.metrics.avg_recognition_confidence:.3f}")
    
    return analysis

def test_transcript_generation(analysis: AnalysisResult):
    """测试转录文本生成"""
    print("\n🧪 测试转录文本生成...")
    
    # 测试完整转录（包含时间戳和说话人）
    full_transcript = analysis.get_full_transcript(
        include_timestamps=True,
        include_speakers=True
    )
    
    assert len(full_transcript) > 0, "转录文本不应为空"
    assert "测试说话人1:" in full_transcript, "转录文本应包含说话人1"
    assert "测试说话人2:" in full_transcript, "转录文本应包含说话人2"
    print(f"  ✅ 完整转录生成成功 ({len(full_transcript)}字符)")
    
    # 测试仅文本转录
    text_only = analysis.get_full_transcript(
        include_timestamps=False,
        include_speakers=False
    )
    
    assert len(text_only) > 0, "纯文本转录不应为空"
    assert "测试说话人1:" not in text_only, "纯文本转录不应包含说话人标识"
    print(f"  ✅ 纯文本转录生成成功 ({len(text_only)}字符)")
    
    # 测试仅时间戳
    timestamps_only = analysis.get_full_transcript(
        include_timestamps=True,
        include_speakers=False
    )
    
    assert "[" in timestamps_only and "]" in timestamps_only, "应包含时间戳格式"
    print(f"  ✅ 时间戳转录生成成功")
    
    return full_transcript

def test_data_export_import(analysis: AnalysisResult):
    """测试数据导出和导入"""
    print("\n🧪 测试数据导出和导入...")
    
    # 测试字典导出
    export_dict = analysis.export_to_dict()
    
    assert 'segments' in export_dict, "导出字典应包含segments"
    assert 'speakers' in export_dict, "导出字典应包含speakers"
    assert 'metrics' in export_dict, "导出字典应包含metrics"
    assert 'created_at' in export_dict, "导出字典应包含created_at"
    print(f"  ✅ 字典导出成功 ({len(export_dict)}个字段)")
    
    # 测试JSON序列化
    json_str = json.dumps(export_dict, ensure_ascii=False, indent=2)
    assert len(json_str) > 0, "JSON字符串不应为空"
    print(f"  ✅ JSON序列化成功 ({len(json_str)}字符)")
    
    # 测试文件保存和加载
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False, encoding='utf-8') as f:
        temp_file = f.name
    
    try:
        # 保存到文件
        analysis.save_to_file(temp_file)
        assert os.path.exists(temp_file), "文件应该已创建"
        print(f"  ✅ 文件保存成功: {temp_file}")
        
        # 从文件加载
        loaded_analysis = AnalysisResult.load_from_file(temp_file)
        
        # 验证加载结果
        assert len(loaded_analysis.segments) == len(analysis.segments), "加载后片段数应该一致"
        assert len(loaded_analysis.speakers) == len(analysis.speakers), "加载后说话人数应该一致"
        assert loaded_analysis.audio_duration == analysis.audio_duration, "加载后音频时长应该一致"
        print(f"  ✅ 文件加载验证成功")
        
    finally:
        # 清理临时文件
        if os.path.exists(temp_file):
            os.unlink(temp_file)

def test_timeline_data_preparation(analysis: AnalysisResult):
    """测试时间轴数据准备"""
    print("\n🧪 测试时间轴数据准备...")
    
    # 准备时间轴数据
    timeline_data = []
    
    for segment in analysis.segments:
        speaker_name = analysis.get_speaker_name(segment.speaker_id)
        timeline_data.append({
            'Speaker': speaker_name,
            'Start': segment.time_interval.start,
            'End': segment.time_interval.end,
            'Duration': segment.time_interval.duration,
            'Text': segment.text[:50] + "..." if len(segment.text) > 50 else segment.text,
            'Confidence': segment.confidence,
            'Emotion': segment.emotion or "unknown"
        })
    
    assert len(timeline_data) == len(analysis.segments), "时间轴数据条数应该与片段数一致"
    
    # 验证数据结构
    for item in timeline_data:
        assert 'Speaker' in item, "应包含Speaker字段"
        assert 'Start' in item, "应包含Start字段"
        assert 'End' in item, "应包含End字段"
        assert 'Duration' in item, "应包含Duration字段"
        assert 'Text' in item, "应包含Text字段"
        assert 'Confidence' in item, "应包含Confidence字段"
        assert 'Emotion' in item, "应包含Emotion字段"
        
        # 验证时间逻辑
        assert item['End'] >= item['Start'], "结束时间应该不早于开始时间"
        assert item['Duration'] == item['End'] - item['Start'], "持续时间计算应该正确"
    
    print(f"  ✅ 时间轴数据准备成功 ({len(timeline_data)}条记录)")
    
    return timeline_data

def test_speaker_statistics(analysis: AnalysisResult):
    """测试说话人统计数据"""
    print("\n🧪 测试说话人统计数据...")
    
    speaker_stats = []
    
    for speaker in analysis.speakers:
        speaker_stats.append({
            'ID': speaker.id,
            '姓名': speaker.name,
            '性别': speaker.gender or "未知",
            '年龄估计': speaker.age_estimate or "未知",
            '片段数': speaker.segment_count,
            '总时长(秒)': f"{speaker.total_speech_time:.1f}",
            '平均置信度': f"{speaker.avg_confidence:.3f}",
            '平均语速': f"{speaker.avg_speech_rate:.1f}",
            '主要情感': ', '.join(speaker.dominant_emotions[:3]) if speaker.dominant_emotions else "无"
        })
    
    assert len(speaker_stats) == analysis.speaker_count, "统计数据条数应该与说话人数一致"
    
    # 验证统计数据
    for stat in speaker_stats:
        assert stat['ID'], "ID不应为空"
        assert stat['姓名'], "姓名不应为空"
        assert int(stat['片段数']) >= 0, "片段数应该非负"
        assert float(stat['总时长(秒)']) >= 0, "总时长应该非负"
    
    print(f"  ✅ 说话人统计数据生成成功 ({len(speaker_stats)}条记录)")
    
    # 验证数据总和
    total_segments = sum(int(stat['片段数']) for stat in speaker_stats)
    total_time = sum(float(stat['总时长(秒)']) for stat in speaker_stats)
    
    assert total_segments == len(analysis.segments), f"片段总数应该一致: {total_segments} vs {len(analysis.segments)}"
    print(f"  ✅ 数据一致性验证通过: 总片段{total_segments}, 总时长{total_time:.1f}秒")
    
    return speaker_stats

def test_performance_metrics_display(analysis: AnalysisResult):
    """测试性能指标显示"""
    print("\n🧪 测试性能指标显示...")
    
    metrics = analysis.metrics
    
    # 验证基本指标
    assert metrics.total_processing_time > 0, "总处理时间应该大于0"
    assert metrics.recognition_time > 0, "识别时间应该大于0"
    assert metrics.avg_recognition_confidence > 0, "平均置信度应该大于0"
    
    # 计算处理时间分解
    time_breakdown = {
        '语音识别': metrics.recognition_time,
        '说话人分析': metrics.speaker_analysis_time,
        '后处理': metrics.post_processing_time,
        '其他': max(0, metrics.total_processing_time - metrics.recognition_time - 
                    metrics.speaker_analysis_time - metrics.post_processing_time)
    }
    
    total_breakdown = sum(time_breakdown.values())
    assert abs(total_breakdown - metrics.total_processing_time) < 0.1, "时间分解总和应该接近总处理时间"
    
    # 计算实时倍率
    real_time_factor = metrics.total_processing_time / analysis.audio_duration if analysis.audio_duration > 0 else 0
    assert real_time_factor > 0, "实时倍率应该大于0"
    
    print(f"  ✅ 性能指标验证成功:")
    print(f"    - 总处理时间: {metrics.total_processing_time:.2f}秒")
    print(f"    - 识别时间: {metrics.recognition_time:.2f}秒")
    print(f"    - 说话人分析时间: {metrics.speaker_analysis_time:.2f}秒")
    print(f"    - 平均识别置信度: {metrics.avg_recognition_confidence:.3f}")
    print(f"    - 平均说话人置信度: {metrics.avg_speaker_confidence:.3f}")
    print(f"    - 实时倍率: {real_time_factor:.2f}x")
    
    return time_breakdown

def test_editing_functions(analysis: AnalysisResult):
    """测试编辑功能"""
    print("\n🧪 测试编辑功能...")
    
    # 测试片段编辑
    if analysis.segments:
        original_segment = analysis.segments[0]
        original_text = original_segment.text
        original_speaker = original_segment.speaker_id
        
        # 修改文本
        new_text = "这是修改后的测试文本。"
        original_segment.text = new_text
        
        assert original_segment.text == new_text, "文本修改应该生效"
        print(f"  ✅ 文本编辑功能正常")
        
        # 修改时间
        original_start = original_segment.time_interval.start
        new_start = original_start + 1.0
        original_segment.time_interval.start = new_start
        
        assert original_segment.time_interval.start == new_start, "时间修改应该生效"
        print(f"  ✅ 时间编辑功能正常")
        
        # 恢复原始值
        original_segment.text = original_text
        original_segment.time_interval.start = original_start
    
    # 测试说话人编辑
    if analysis.speakers:
        original_speaker = analysis.speakers[0]
        original_name = original_speaker.name
        
        # 修改说话人名称
        new_name = "修改后的说话人名称"
        original_speaker.name = new_name
        
        assert original_speaker.name == new_name, "说话人名称修改应该生效"
        print(f"  ✅ 说话人编辑功能正常")
        
        # 恢复原始值
        original_speaker.name = original_name
    
    # 测试统计信息更新
    original_word_count = analysis.word_count
    analysis.update_statistics()
    
    # 由于我们恢复了原始值，字数应该保持不变
    assert analysis.word_count == original_word_count, "统计信息更新应该正确"
    print(f"  ✅ 统计信息更新功能正常")

def main():
    """主测试函数"""
    print("🚀 开始测试结果展示和编辑页面功能...")
    print("=" * 70)
    
    try:
        # 运行所有测试
        analysis = test_demo_data_creation()
        transcript = test_transcript_generation(analysis)
        test_data_export_import(analysis)
        timeline_data = test_timeline_data_preparation(analysis)
        speaker_stats = test_speaker_statistics(analysis)
        performance_data = test_performance_metrics_display(analysis)
        test_editing_functions(analysis)
        
        print("\n" + "=" * 70)
        print("🎉 所有功能测试通过！结果展示和编辑页面工作正常。")
        print("\n📋 功能验证清单:")
        print("  ✅ 演示数据创建")
        print("  ✅ 转录文本生成（完整/纯文本/时间戳）")
        print("  ✅ 数据导出和导入（字典/JSON/文件）")
        print("  ✅ 时间轴数据准备")
        print("  ✅ 说话人统计数据生成")
        print("  ✅ 性能指标计算和显示")
        print("  ✅ 编辑功能（文本/时间/说话人）")
        print("  ✅ 统计信息自动更新")
        
        print("\n🌟 页面特色功能:")
        print("  - 📊 交互式时间轴可视化")
        print("  - 👥 说话人统计分析")
        print("  - ✏️ 在线文本编辑")
        print("  - 📈 性能指标展示")
        print("  - 💾 多格式数据导出")
        print("  - 🎯 实时数据验证")
        
        print(f"\n📍 页面访问地址: http://localhost:8502")
        print("💡 现在可以在浏览器中测试完整的用户界面功能了！")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1) 