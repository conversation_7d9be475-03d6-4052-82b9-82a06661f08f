@echo off
REM ===========================================
REM 语音处理智能平台 Docker 停止脚本 (Windows)
REM ===========================================

setlocal enabledelayedexpansion

echo 🛑 语音处理智能平台 Docker 服务停止
echo ========================================

REM 切换到脚本目录的上级目录
cd /d "%~dp0\.."

REM 检查是否有运行的服务
docker-compose ps -q >nul 2>&1
if errorlevel 1 (
    echo [WARNING] 没有运行的服务
    goto :show_status
)

echo [INFO] 正在停止服务...

REM 优雅停止服务
docker-compose stop
if errorlevel 1 (
    echo [ERROR] 停止服务失败
    pause
    exit /b 1
)

echo [SUCCESS] 服务已停止

REM 询问是否删除容器
set /p choice="是否删除容器? (保留数据卷) (y/n): "
if /i "!choice!"=="y" (
    docker-compose down
    if errorlevel 1 (
        echo [ERROR] 删除容器失败
    ) else (
        echo [SUCCESS] 容器已删除
    )
)

REM 询问是否清理镜像
set /p choice="是否清理未使用的Docker镜像? (y/n): "
if /i "!choice!"=="y" (
    docker image prune -f
    if errorlevel 1 (
        echo [ERROR] 清理镜像失败
    ) else (
        echo [SUCCESS] 未使用的镜像已清理
    )
)

:show_status
echo.
echo [INFO] 当前服务状态:
docker-compose ps

echo.
echo [INFO] 数据卷状态:
if exist "volumes" (
    dir volumes /b
) else (
    echo [WARNING] volumes目录不存在
)

echo.
echo [SUCCESS] ✅ 停止操作完成
echo.
echo [INFO] 数据已保留在 volumes\ 目录中
echo [INFO] 重新启动请运行: scripts\start.bat

pause
