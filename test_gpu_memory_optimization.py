#!/usr/bin/env python3
"""
GPU内存优化测试脚本
验证FunASR模型在GPU模式下的内存使用情况
"""

import os
import sys
import gc
import time
import psutil
import torch
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_gpu_status():
    """检查GPU状态"""
    print("🔍 GPU状态检查:")
    print(f"  CUDA可用: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"  GPU数量: {torch.cuda.device_count()}")
        print(f"  当前设备: {torch.cuda.current_device()}")
        print(f"  设备名称: {torch.cuda.get_device_name(0)}")
        
        # 显存信息
        total_memory = torch.cuda.get_device_properties(0).total_memory
        allocated_memory = torch.cuda.memory_allocated()
        reserved_memory = torch.cuda.memory_reserved()
        
        print(f"  总显存: {total_memory/1024**3:.1f}GB")
        print(f"  已分配: {allocated_memory/1024**3:.1f}GB")
        print(f"  已缓存: {reserved_memory/1024**3:.1f}GB")
        print(f"  可用显存: {(total_memory-reserved_memory)/1024**3:.1f}GB")
        
        return True
    return False

def monitor_memory_usage(label: str):
    """监控内存使用情况"""
    # 系统内存
    memory = psutil.virtual_memory()
    process = psutil.Process()
    process_memory = process.memory_info().rss / 1024**3  # GB
    
    print(f"📊 [{label}] 内存状态:")
    print(f"  系统内存: {memory.percent:.1f}% ({memory.used/1024**3:.1f}GB/{memory.total/1024**3:.1f}GB)")
    print(f"  进程内存: {process_memory:.1f}GB")
    
    # GPU内存
    if torch.cuda.is_available():
        allocated = torch.cuda.memory_allocated() / 1024**3
        reserved = torch.cuda.memory_reserved() / 1024**3
        print(f"  GPU已分配: {allocated:.1f}GB")
        print(f"  GPU已缓存: {reserved:.1f}GB")

def test_funasr_gpu_loading():
    """测试FunASR模型GPU加载"""
    print("\n🤖 测试FunASR模型GPU加载...")
    
    try:
        from funasr import AutoModel
        
        model_path = "./models/SenseVoiceSmall"
        if not os.path.exists(model_path):
            print(f"⚠️ 模型路径不存在: {model_path}")
            return False
        
        monitor_memory_usage("模型加载前")
        
        # 强制使用GPU设备
        device = "cuda:0" if torch.cuda.is_available() else "cpu"
        print(f"🎯 使用设备: {device}")
        
        # 配置GPU优化的模型加载参数
        model_config = {
            'model': model_path,
            'trust_remote_code': True,
            'device': device,
            'local_files_only': True,
            'disable_update': True,
            'vad_model': None,  # 禁用VAD减少内存使用
        }
        
        print("🚀 开始加载模型...")
        start_time = time.time()
        
        model = AutoModel(**model_config)
        
        load_time = time.time() - start_time
        print(f"✅ 模型加载完成，耗时: {load_time:.1f}秒")
        
        monitor_memory_usage("模型加载后")
        
        # 测试推理
        test_audio = "resource/对话.mp3"
        if os.path.exists(test_audio):
            print("🎵 测试推理...")
            
            inference_start = time.time()
            result = model.generate(
                input=test_audio,
                language="auto",
                use_itn=True,
                batch_size_s=60
            )
            inference_time = time.time() - inference_start
            
            print(f"✅ 推理完成，耗时: {inference_time:.1f}秒")
            print(f"📝 结果长度: {len(str(result))}")
            
            monitor_memory_usage("推理后")
        
        # 清理模型
        print("🧹 清理模型...")
        del model
        gc.collect()
        
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.synchronize()
        
        monitor_memory_usage("清理后")
        
        return True
        
    except Exception as e:
        print(f"❌ FunASR GPU测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_model_loads():
    """测试多次模型加载和卸载"""
    print("\n🔄 测试多次模型加载和卸载...")
    
    try:
        from funasr import AutoModel
        
        model_path = "./models/SenseVoiceSmall"
        device = "cuda:0" if torch.cuda.is_available() else "cpu"
        
        for i in range(3):
            print(f"\n--- 第{i+1}次加载 ---")
            monitor_memory_usage(f"第{i+1}次加载前")
            
            # 加载模型
            model = AutoModel(
                model=model_path,
                trust_remote_code=True,
                device=device,
                local_files_only=True,
                disable_update=True,
                vad_model=None
            )
            
            monitor_memory_usage(f"第{i+1}次加载后")
            
            # 立即清理
            del model
            gc.collect()
            
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.synchronize()
            
            monitor_memory_usage(f"第{i+1}次清理后")
            
            # 等待一段时间
            time.sleep(2)
        
        return True
        
    except Exception as e:
        print(f"❌ 多次加载测试失败: {e}")
        return False

def optimize_gpu_memory_settings():
    """优化GPU内存设置"""
    print("\n⚙️ 优化GPU内存设置...")
    
    if not torch.cuda.is_available():
        print("⚠️ GPU不可用，跳过GPU内存优化")
        return
    
    try:
        # 设置GPU内存分配策略
        os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:512,expandable_segments:True'
        
        # 启用内存池
        torch.cuda.empty_cache()
        
        # 设置内存增长策略
        if hasattr(torch.cuda, 'set_per_process_memory_fraction'):
            torch.cuda.set_per_process_memory_fraction(0.8)  # 使用80%的GPU内存
            print("✅ 设置GPU内存使用比例为80%")
        
        print("✅ GPU内存优化设置完成")
        
    except Exception as e:
        print(f"⚠️ GPU内存优化设置失败: {e}")

def main():
    """主函数"""
    print("🚀 GPU内存优化测试")
    print("=" * 50)
    
    # 检查GPU状态
    gpu_available = check_gpu_status()
    
    if not gpu_available:
        print("❌ GPU不可用，无法进行GPU内存优化测试")
        return
    
    # 优化GPU内存设置
    optimize_gpu_memory_settings()
    
    # 初始内存状态
    monitor_memory_usage("初始状态")
    
    # 测试1: FunASR GPU加载
    success1 = test_funasr_gpu_loading()
    
    # 等待内存稳定
    print("\n⏳ 等待内存稳定...")
    time.sleep(5)
    
    # 测试2: 多次模型加载
    success2 = test_multiple_model_loads()
    
    # 最终状态
    print("\n" + "=" * 50)
    monitor_memory_usage("最终状态")
    
    # 总结
    print("\n📋 测试总结:")
    print(f"  FunASR GPU加载测试: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"  多次加载测试: {'✅ 成功' if success2 else '❌ 失败'}")
    
    if success1 and success2:
        print("🎉 GPU内存优化测试全部通过！")
    else:
        print("⚠️ 部分测试失败，需要进一步优化")

if __name__ == "__main__":
    main()
