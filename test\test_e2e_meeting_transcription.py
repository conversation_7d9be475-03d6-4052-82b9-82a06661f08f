#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
端到端会议转录测试
使用对话.mp3文件测试完整的会议转录流程
"""

import os
import sys
import time
import json
import asyncio
import requests
from pathlib import Path
from typing import Dict, Any

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class E2EMeetingTranscriptionTest:
    """端到端会议转录测试"""
    
    def __init__(self):
        self.base_url = "http://localhost:8002"
        self.test_audio_file = project_root / "对话.mp3"
        self.session = requests.Session()
        self.task_id = None
        
    def setup(self):
        """测试前设置"""
        print("🔧 设置端到端测试环境...")
        
        # 检查测试音频文件
        if not self.test_audio_file.exists():
            print(f"❌ 测试音频文件不存在: {self.test_audio_file}")
            return False
            
        # 检查后端服务
        try:
            response = self.session.get(f"{self.base_url}/health")
            if response.status_code != 200:
                print(f"❌ 后端服务不可用: {response.status_code}")
                return False
        except requests.exceptions.ConnectionError:
            print("❌ 无法连接到后端服务，请确保服务正在运行")
            return False
            
        print("✅ 测试环境设置完成")
        return True
    
    def login(self) -> str:
        """登录获取token"""
        print("🔐 登录系统...")
        
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        try:
            response = self.session.post(f"{self.base_url}/auth/login", json=login_data)
            if response.status_code == 200:
                token = response.json().get("access_token")
                self.session.headers.update({"Authorization": f"Bearer {token}"})
                print("✅ 登录成功")
                return token
            else:
                print(f"❌ 登录失败: {response.status_code}")
                return None
        except Exception as e:
            print(f"❌ 登录异常: {e}")
            return None
    
    def upload_audio_file(self) -> str:
        """上传音频文件"""
        print("📤 上传测试音频文件...")
        
        try:
            with open(self.test_audio_file, 'rb') as f:
                files = {'file': (self.test_audio_file.name, f, 'audio/mpeg')}
                response = self.session.post(f"{self.base_url}/api/files/upload", files=files)
                
            if response.status_code == 200:
                file_id = response.json().get("file_id")
                print(f"✅ 文件上传成功，文件ID: {file_id}")
                return file_id
            else:
                print(f"❌ 文件上传失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                return None
        except Exception as e:
            print(f"❌ 文件上传异常: {e}")
            return None
    
    def start_meeting_transcription(self, file_id: str) -> str:
        """启动会议转录任务"""
        print("🎯 启动会议转录任务...")
        
        task_data = {
            "file_ids": [file_id],
            "language": "auto",
            "output_format": "detailed",
            "include_timestamps": True,
            "speaker_labeling": True,
            "skip_preprocessing": False
        }
        
        try:
            response = self.session.post(f"{self.base_url}/api/audio/meeting-transcription", json=task_data)
            
            if response.status_code == 200:
                task_id = response.json().get("task_id")
                print(f"✅ 会议转录任务启动成功，任务ID: {task_id}")
                return task_id
            else:
                print(f"❌ 会议转录任务启动失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                return None
        except Exception as e:
            print(f"❌ 会议转录任务启动异常: {e}")
            return None
    
    def monitor_task_progress(self, task_id: str, timeout: int = 300) -> Dict[str, Any]:
        """监控任务进度"""
        print(f"📊 监控任务进度: {task_id}")
        
        start_time = time.time()
        last_percentage = -1
        
        while time.time() - start_time < timeout:
            try:
                response = self.session.get(f"{self.base_url}/api/tasks/{task_id}/progress")
                
                if response.status_code == 200:
                    progress_data = response.json()
                    percentage = float(progress_data.get("percentage", 0))
                    detail = progress_data.get("detail", "")
                    stage = progress_data.get("stage", "")
                    
                    # 只在进度变化时打印
                    if percentage != last_percentage:
                        print(f"📈 进度: {percentage:.1f}% - {stage} - {detail}")
                        last_percentage = percentage
                    
                    # 检查任务是否完成
                    if percentage >= 100:
                        print("✅ 任务完成")
                        return self.get_task_result(task_id)
                        
                elif response.status_code == 404:
                    print("❌ 任务不存在")
                    return None
                else:
                    print(f"⚠️ 获取进度失败: {response.status_code}")
                
            except Exception as e:
                print(f"⚠️ 监控进度异常: {e}")
            
            time.sleep(2)  # 每2秒检查一次
        
        print("❌ 任务超时")
        return None
    
    def get_task_result(self, task_id: str) -> Dict[str, Any]:
        """获取任务结果"""
        print(f"📋 获取任务结果: {task_id}")
        
        try:
            response = self.session.get(f"{self.base_url}/api/tasks/{task_id}/result")
            
            if response.status_code == 200:
                result = response.json()
                print("✅ 任务结果获取成功")
                return result
            else:
                print(f"❌ 获取任务结果失败: {response.status_code}")
                return None
        except Exception as e:
            print(f"❌ 获取任务结果异常: {e}")
            return None
    
    def validate_result_structure(self, result: Dict[str, Any]) -> bool:
        """验证结果数据结构"""
        print("🔍 验证结果数据结构...")
        
        if not result:
            print("❌ 结果为空")
            return False
        
        # 检查基本结构
        if "results" not in result:
            print("❌ 缺少results字段")
            return False
        
        results = result["results"]
        if not isinstance(results, list) or len(results) == 0:
            print("❌ results应该是非空数组")
            return False
        
        # 检查第一个结果
        first_result = results[0]
        if "result" not in first_result:
            print("❌ 缺少result字段")
            return False
        
        meeting_result = first_result["result"]
        
        # 验证前端期望的数据结构
        required_fields = ["speech_segments", "speaker_segments", "text"]
        for field in required_fields:
            if field not in meeting_result:
                print(f"❌ 缺少必需字段: {field}")
                return False
        
        # 验证speech_segments结构
        speech_segments = meeting_result["speech_segments"]
        if not isinstance(speech_segments, list):
            print("❌ speech_segments应该是数组")
            return False
        
        if len(speech_segments) > 0:
            segment = speech_segments[0]
            segment_fields = ["start_time", "end_time", "text", "speaker_id", "speaker_name"]
            for field in segment_fields:
                if field not in segment:
                    print(f"❌ speech_segments缺少字段: {field}")
                    return False
        
        # 验证speaker_segments结构
        speaker_segments = meeting_result["speaker_segments"]
        if not isinstance(speaker_segments, list):
            print("❌ speaker_segments应该是数组")
            return False
        
        if len(speaker_segments) > 0:
            speaker = speaker_segments[0]
            speaker_fields = ["name", "segment_count", "total_time"]
            for field in speaker_fields:
                if field not in speaker:
                    print(f"❌ speaker_segments缺少字段: {field}")
                    return False
        
        print("✅ 结果数据结构验证通过")
        return True
    
    def print_result_summary(self, result: Dict[str, Any]):
        """打印结果摘要"""
        print("\n📊 会议转录结果摘要:")
        print("=" * 50)
        
        meeting_result = result["results"][0]["result"]
        
        # 基本信息
        print(f"📝 完整文本: {meeting_result.get('text', '')[:100]}...")
        print(f"🎤 语音片段数: {len(meeting_result.get('speech_segments', []))}")
        print(f"👥 说话人数: {len(meeting_result.get('speaker_segments', []))}")
        
        # 说话人统计
        print("\n👥 说话人统计:")
        for speaker in meeting_result.get('speaker_segments', []):
            print(f"  - {speaker['name']}: {speaker['segment_count']}个片段, {speaker['total_time']:.1f}秒")
        
        # 对话内容（前5个片段）
        print("\n💬 对话内容（前5个片段）:")
        for i, segment in enumerate(meeting_result.get('speech_segments', [])[:5]):
            print(f"  {i+1}. [{segment['start_time']:.1f}s-{segment['end_time']:.1f}s] {segment['speaker_name']}: {segment['text']}")
        
        print("=" * 50)
    
    def run_test(self) -> bool:
        """运行完整测试"""
        print("🚀 开始端到端会议转录测试")
        print("=" * 60)
        
        # 1. 设置环境
        if not self.setup():
            return False
        
        # 2. 登录
        token = self.login()
        if not token:
            return False
        
        # 3. 上传文件
        file_id = self.upload_audio_file()
        if not file_id:
            return False
        
        # 4. 启动转录任务
        task_id = self.start_meeting_transcription(file_id)
        if not task_id:
            return False
        
        self.task_id = task_id
        
        # 5. 监控进度并获取结果
        result = self.monitor_task_progress(task_id)
        if not result:
            return False
        
        # 6. 验证结果结构
        if not self.validate_result_structure(result):
            return False
        
        # 7. 打印结果摘要
        self.print_result_summary(result)
        
        print("\n✅ 端到端测试完成！")
        return True


def main():
    """主函数"""
    test = E2EMeetingTranscriptionTest()
    success = test.run_test()
    
    if success:
        print("\n🎉 所有测试通过！")
        sys.exit(0)
    else:
        print("\n❌ 测试失败！")
        sys.exit(1)


if __name__ == "__main__":
    main()
