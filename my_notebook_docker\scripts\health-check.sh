#!/bin/bash
# ===========================================
# 语音处理智能平台 Docker 健康检查脚本
# ===========================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker服务状态
check_docker_services() {
    log_info "检查Docker服务状态..."
    
    cd "$(dirname "$0")/.."
    
    # 检查容器状态
    echo
    log_info "容器状态:"
    docker-compose ps
    
    # 检查各个服务的健康状态
    services=("redis" "backend" "celery-worker")
    
    for service in "${services[@]}"; do
        echo
        log_info "检查 $service 服务..."
        
        if docker-compose ps $service | grep -q "Up"; then
            log_success "$service 服务运行正常"
            
            # 检查健康状态
            health_status=$(docker-compose ps $service --format "table {{.Health}}" | tail -n +2)
            if [ "$health_status" = "healthy" ]; then
                log_success "$service 健康检查通过"
            elif [ "$health_status" = "starting" ]; then
                log_warning "$service 正在启动中..."
            elif [ "$health_status" = "unhealthy" ]; then
                log_error "$service 健康检查失败"
            fi
        else
            log_error "$service 服务未运行"
        fi
    done
}

# 检查API端点
check_api_endpoints() {
    log_info "检查API端点..."
    
    # 从.env文件读取端口配置
    if [ -f ".env" ]; then
        source .env
    fi
    
    BACKEND_PORT=${BACKEND_PORT:-8002}
    
    # 检查健康检查端点
    echo
    log_info "检查Backend健康检查端点..."
    if curl -s -f "http://localhost:$BACKEND_PORT/health" > /dev/null; then
        log_success "Backend健康检查端点响应正常"
        
        # 显示健康检查详情
        health_response=$(curl -s "http://localhost:$BACKEND_PORT/health")
        echo "健康检查响应: $health_response"
    else
        log_error "Backend健康检查端点无响应"
    fi
    
    # 检查API文档端点
    echo
    log_info "检查API文档端点..."
    if curl -s -f "http://localhost:$BACKEND_PORT/docs" > /dev/null; then
        log_success "API文档端点可访问"
        echo "API文档地址: http://localhost:$BACKEND_PORT/docs"
    else
        log_warning "API文档端点无法访问"
    fi
}

# 检查数据库连接
check_database_connections() {
    log_info "检查数据库连接..."
    
    # 检查Redis连接
    echo
    log_info "检查Redis连接..."
    if docker-compose exec -T redis redis-cli ping | grep -q "PONG"; then
        log_success "Redis连接正常"
    else
        log_error "Redis连接失败"
    fi
    
    # 检查ChromaDB
    echo
    log_info "检查ChromaDB状态..."
    docker-compose exec -T backend /app/.venv/bin/python -c "
import chromadb
import os
try:
    client = chromadb.PersistentClient(path='/app/data/chroma_db')
    collections = client.list_collections()
    print(f'✅ ChromaDB连接正常，集合数: {len(collections)}')
    
    # 检查knowledge_base集合
    try:
        kb_collection = client.get_collection('knowledge_base')
        doc_count = kb_collection.count()
        print(f'✅ knowledge_base集合存在，文档数: {doc_count}')
    except Exception as e:
        print(f'⚠️ knowledge_base集合问题: {e}')
        
except Exception as e:
    print(f'❌ ChromaDB连接失败: {e}')
    exit(1)
" && log_success "ChromaDB检查完成" || log_error "ChromaDB检查失败"
}

# 检查向量维度一致性
check_vector_dimensions() {
    log_info "检查向量维度一致性..."
    
    echo
    log_info "验证嵌入模型配置..."
    docker-compose exec -T backend /app/.venv/bin/python -c "
import os
from backend.services.rag_service import rag_service
import asyncio

async def check_embedding_dimension():
    try:
        # 初始化RAG服务
        await rag_service.initialize()
        
        # 测试嵌入生成
        test_text = '测试向量维度'
        embedding = await rag_service.embedding_model.aget_text_embedding(test_text)
        
        print(f'✅ 嵌入模型工作正常')
        print(f'当前向量维度: {len(embedding)}')
        print(f'配置的模型: {rag_service.config.get(\"embedding_model\", \"未知\")}')
        
        return len(embedding)
    except Exception as e:
        print(f'❌ 嵌入模型测试失败: {e}')
        return None

# 运行检查
loop = asyncio.new_event_loop()
asyncio.set_event_loop(loop)
dimension = loop.run_until_complete(check_embedding_dimension())
loop.close()

if dimension:
    if dimension == 768:
        print('✅ 向量维度匹配现有数据库(768维)')
    else:
        print(f'⚠️ 向量维度({dimension})可能与现有数据库不匹配')
" && log_success "向量维度检查完成" || log_warning "向量维度检查失败"
}

# 检查Celery任务队列
check_celery_status() {
    log_info "检查Celery任务队列状态..."
    
    echo
    log_info "检查Celery Worker状态..."
    docker-compose exec -T celery-worker /app/.venv/bin/python -c "
from celery import Celery
import os

try:
    # 创建Celery应用实例
    app = Celery('backend')
    app.config_from_object('backend.core.celery_config')
    
    # 检查worker状态
    inspect = app.control.inspect()
    stats = inspect.stats()
    active = inspect.active()
    
    if stats:
        print('✅ Celery Worker运行正常')
        for worker, stat in stats.items():
            print(f'Worker: {worker}')
            print(f'  任务总数: {stat.get(\"total\", 0)}')
            print(f'  活跃任务: {len(active.get(worker, []))}')
    else:
        print('❌ 没有活跃的Celery Worker')
        
except Exception as e:
    print(f'❌ Celery状态检查失败: {e}')
" && log_success "Celery检查完成" || log_error "Celery检查失败"
}

# 显示系统资源使用情况
show_resource_usage() {
    log_info "系统资源使用情况..."
    
    echo
    log_info "容器资源使用:"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}\t{{.BlockIO}}"
    
    echo
    log_info "数据卷使用情况:"
    du -sh volumes/* 2>/dev/null || log_warning "无法获取数据卷大小"
}

# 主函数
main() {
    echo "🔍 语音处理智能平台 Docker 健康检查"
    echo "========================================"
    
    # 切换到脚本目录
    cd "$(dirname "$0")/.."
    
    # 执行各项检查
    check_docker_services
    check_api_endpoints
    check_database_connections
    check_vector_dimensions
    check_celery_status
    show_resource_usage
    
    echo
    log_success "🎉 健康检查完成！"
    echo
    log_info "如果发现问题，请查看详细日志:"
    echo "  docker-compose logs backend"
    echo "  docker-compose logs celery-worker"
    echo "  docker-compose logs redis"
}

# 执行主函数
main "$@"
