import{_ as r,o as d,r as l,a as _,c as i,b as s,d as t,w as a,f as n}from"./index-2c134546.js";const c={class:"not-found-container"},u={class:"not-found-content"},p={class:"error-actions"},v={__name:"NotFound",setup(f){return d(()=>{console.log("🔍 404页面加载完成")}),(m,o)=>{const e=l("router-link");return _(),i("div",c,[s("div",u,[o[2]||(o[2]=s("div",{class:"error-visual"},[s("div",{class:"error-code"},"404"),s("div",{class:"error-icon"},"🔍")],-1)),o[3]||(o[3]=s("div",{class:"error-text"},[s("h1",null,"页面未找到"),s("p",null,"抱歉，您访问的页面不存在或已被移动。")],-1)),s("div",p,[t(e,{to:"/",class:"btn-gradient"},{default:a(()=>o[0]||(o[0]=[n(" 🏠 返回首页 ")])),_:1,__:[0]}),t(e,{to:"/dashboard",class:"btn-outline"},{default:a(()=>o[1]||(o[1]=[n(" 📊 进入控制台 ")])),_:1,__:[1]})])]),o[4]||(o[4]=s("div",{class:"background-shapes"},[s("div",{class:"shape shape-1"}),s("div",{class:"shape shape-2"}),s("div",{class:"shape shape-3"})],-1))])}}},N=r(v,[["__scopeId","data-v-21c88ae1"]]);export{N as default};
