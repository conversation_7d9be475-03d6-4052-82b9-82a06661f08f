/**
 * WebSocket实时通信工具
 * 用于实时接收任务进度更新
 */

class WebSocketManager {
  constructor() {
    this.ws = null
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectInterval = 3000
    this.listeners = new Map()
    this.isConnecting = false
    this.isConnected = false
    this.heartbeatInterval = null
    this.heartbeatTimeout = null
  }

  /**
   * 连接WebSocket
   */
  connect(token) {
    if (this.isConnecting || this.isConnected) {
      return Promise.resolve()
    }

    return new Promise((resolve, reject) => {
      try {
        this.isConnecting = true
        
        // 构建WebSocket URL
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
        // 从环境变量获取后端端口，默认8002
        const backendPort = import.meta.env.VITE_API_BASE_URL?.match(/:(\d+)/)?.[1] || '8002'
        const backendHost = window.location.hostname + ':' + backendPort
        const wsUrl = `${protocol}//${backendHost}/ws/progress?token=${token}`
        
        console.log('🔌 连接WebSocket:', wsUrl)
        
        this.ws = new WebSocket(wsUrl)
        
        this.ws.onopen = () => {
          console.log('✅ WebSocket连接成功')
          this.isConnecting = false
          this.isConnected = true
          this.reconnectAttempts = 0
          this.startHeartbeat()
          resolve()
        }
        
        this.ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data)
            this.handleMessage(data)
          } catch (error) {
            console.error('❌ WebSocket消息解析失败:', error)
          }
        }
        
        this.ws.onclose = (event) => {
          console.log('🔌 WebSocket连接关闭:', event.code, event.reason)
          this.isConnecting = false
          this.isConnected = false
          this.stopHeartbeat()
          
          // 如果不是主动关闭，尝试重连
          if (event.code !== 1000 && this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect(token)
          }
        }
        
        this.ws.onerror = (error) => {
          console.error('❌ WebSocket连接错误:', error)
          this.isConnecting = false
          this.isConnected = false
          reject(error)
        }
        
      } catch (error) {
        this.isConnecting = false
        reject(error)
      }
    })
  }

  /**
   * 处理接收到的消息 - 统一消息格式处理
   */
  handleMessage(data) {
    console.log('📨 收到WebSocket消息:', data)

    switch (data.type) {
      case 'progress_update':
        // 🔧 修复：统一处理新的消息格式 {type, task_id, data}
        const progressPayload = {
          task_id: data.task_id,
          ...data.data
        }
        this.notifyListeners('progress', progressPayload)
        break
      case 'task_completed':
        // 🔧 修复：统一处理新的消息格式 {type, task_id, data}
        const completedPayload = {
          task_id: data.task_id,
          ...data.data
        }
        this.notifyListeners('completed', completedPayload)
        break
      case 'task_failed':
        // 🔧 修复：统一处理新的消息格式 {type, task_id, data}
        const failedPayload = {
          task_id: data.task_id,
          ...data.data
        }
        this.notifyListeners('failed', failedPayload)
        break
      case 'heartbeat':
        this.handleHeartbeat()
        break
      default:
        console.warn('⚠️ 未知的WebSocket消息类型:', data.type)
    }
  }

  /**
   * 通知监听器
   */
  notifyListeners(event, data) {
    const listeners = this.listeners.get(event) || []
    listeners.forEach(callback => {
      try {
        callback(data)
      } catch (error) {
        console.error('❌ WebSocket监听器执行失败:', error)
      }
    })
  }

  /**
   * 添加事件监听器
   */
  on(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, [])
    }
    this.listeners.get(event).push(callback)
  }

  /**
   * 移除事件监听器
   */
  off(event, callback) {
    const listeners = this.listeners.get(event) || []
    const index = listeners.indexOf(callback)
    if (index > -1) {
      listeners.splice(index, 1)
    }
  }

  /**
   * 订阅任务进度
   */
  subscribeTask(taskId) {
    if (this.isConnected) {
      this.send({
        type: 'subscribe',
        task_id: taskId
      })
    }
  }

  /**
   * 取消订阅任务进度
   */
  unsubscribeTask(taskId) {
    if (this.isConnected) {
      this.send({
        type: 'unsubscribe',
        task_id: taskId
      })
    }
  }

  /**
   * 发送消息
   */
  send(data) {
    if (this.isConnected && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify(data))
    } else {
      console.warn('⚠️ WebSocket未连接，无法发送消息')
    }
  }

  /**
   * 开始心跳检测
   */
  startHeartbeat() {
    this.heartbeatInterval = setInterval(() => {
      if (this.isConnected) {
        this.send({ type: 'ping' })
        
        // 设置心跳超时
        this.heartbeatTimeout = setTimeout(() => {
          console.warn('⚠️ 心跳超时，重连WebSocket')
          this.disconnect()
        }, 5000)
      }
    }, 30000) // 每30秒发送一次心跳
  }

  /**
   * 停止心跳检测
   */
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
    if (this.heartbeatTimeout) {
      clearTimeout(this.heartbeatTimeout)
      this.heartbeatTimeout = null
    }
  }

  /**
   * 处理心跳响应
   */
  handleHeartbeat() {
    if (this.heartbeatTimeout) {
      clearTimeout(this.heartbeatTimeout)
      this.heartbeatTimeout = null
    }
  }

  /**
   * 安排重连
   */
  scheduleReconnect(token) {
    this.reconnectAttempts++
    const delay = this.reconnectInterval * Math.pow(2, this.reconnectAttempts - 1)
    
    console.log(`🔄 ${delay}ms后尝试第${this.reconnectAttempts}次重连...`)
    
    setTimeout(() => {
      if (!this.isConnected) {
        this.connect(token).catch(error => {
          console.error('❌ WebSocket重连失败:', error)
        })
      }
    }, delay)
  }

  /**
   * 断开连接
   */
  disconnect() {
    this.isConnected = false
    this.isConnecting = false
    this.stopHeartbeat()
    
    if (this.ws) {
      this.ws.close(1000, 'Client disconnect')
      this.ws = null
    }
  }

  /**
   * 获取连接状态
   */
  getStatus() {
    return {
      isConnected: this.isConnected,
      isConnecting: this.isConnecting,
      reconnectAttempts: this.reconnectAttempts
    }
  }
}

// 创建全局WebSocket管理器实例
const wsManager = new WebSocketManager()

export default wsManager

/**
 * 进度监听器组合式函数
 */
export function useProgressWebSocket() {
  const { getToken } = useAuth()
  
  const connect = async () => {
    const token = getToken()
    console.log('🔑 获取到的token:', token ? '有效' : '无效')
    if (token) {
      try {
        await wsManager.connect(token)
        return true
      } catch (error) {
        console.error('WebSocket连接失败:', error)
        return false
      }
    } else {
      console.warn('⚠️ 没有有效的token，无法连接WebSocket')
    }
    return false
  }

  const subscribeProgress = (taskId, onProgress, onCompleted, onFailed) => {
    // 添加监听器
    if (onProgress) wsManager.on('progress', onProgress)
    if (onCompleted) wsManager.on('completed', onCompleted)
    if (onFailed) wsManager.on('failed', onFailed)
    
    // 订阅任务
    wsManager.subscribeTask(taskId)
    
    // 返回清理函数
    return () => {
      wsManager.unsubscribeTask(taskId)
      if (onProgress) wsManager.off('progress', onProgress)
      if (onCompleted) wsManager.off('completed', onCompleted)
      if (onFailed) wsManager.off('failed', onFailed)
    }
  }

  const disconnect = () => {
    wsManager.disconnect()
  }

  const getStatus = () => {
    return wsManager.getStatus()
  }

  return {
    connect,
    subscribeProgress,
    disconnect,
    getStatus
  }
}
