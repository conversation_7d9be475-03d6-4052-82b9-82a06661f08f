/**
 * WebSocket连接测试脚本
 * 用于测试与后端的WebSocket连接和数据接收
 */

const WebSocket = require('ws');

// 配置
const config = {
  host: 'localhost',
  port: 8002, // 后端运行在8002端口
  token: 'demo-token' // 使用你的实际token
};

// 构建WebSocket URL
const wsUrl = `ws://${config.host}:${config.port}/ws/progress?token=${config.token}`;

console.log('=== WebSocket连接测试 ===');
console.log(`连接URL: ${wsUrl}`);
console.log('开始连接...\n');

// 创建WebSocket连接
const ws = new WebSocket(wsUrl);

// 连接成功
ws.on('open', function open() {
  console.log('✅ WebSocket连接成功!');
  
  // 发送心跳测试
  console.log('📤 发送心跳测试...');
  ws.send(JSON.stringify({
    type: 'ping'
  }));
  
  // 模拟订阅一个真实的任务
  setTimeout(() => {
    console.log('📤 订阅真实任务...');
    ws.send(JSON.stringify({
      type: 'subscribe',
      task_id: 'doc_proc_f558b3859656'  // 使用真实的正在进行的任务
    }));
  }, 1000);
});

// 接收消息
ws.on('message', function message(data) {
  try {
    const parsed = JSON.parse(data.toString());
    console.log('📨 收到WebSocket消息:');
    console.log('  类型:', parsed.type);
    console.log('  完整数据:', JSON.stringify(parsed, null, 2));
    
    // 模拟前端的数据处理
    if (parsed.type === 'progress_update') {
      console.log('🔄 处理进度更新...');
      const progressData = convertWebSocketDataToProgress(parsed.payload);
      console.log('✅ 转换后的进度数据:', progressData);
    } else if (parsed.type === 'task_completed') {
      console.log('✅ 任务完成通知');
    } else if (parsed.type === 'task_failed') {
      console.log('❌ 任务失败通知');
    } else if (parsed.type === 'heartbeat') {
      console.log('💓 心跳响应');
    }
    
    console.log('---');
    
  } catch (error) {
    console.error('❌ 消息解析失败:', error);
    console.log('原始数据:', data.toString());
  }
});

// 连接错误
ws.on('error', function error(err) {
  console.error('❌ WebSocket连接错误:', err.message);
  
  if (err.code === 'ECONNREFUSED') {
    console.log('💡 提示: 请确保后端服务正在运行');
  } else if (err.code === 'ENOTFOUND') {
    console.log('💡 提示: 请检查主机地址是否正确');
  }
});

// 连接关闭
ws.on('close', function close(code, reason) {
  console.log(`🔌 WebSocket连接关闭: ${code} - ${reason.toString()}`);
  
  if (code === 1000) {
    console.log('✅ 正常关闭');
  } else if (code === 1006) {
    console.log('⚠️ 异常关闭 - 可能是网络问题或服务器关闭');
  } else {
    console.log('⚠️ 其他关闭原因');
  }
});

// 模拟前端的数据转换函数
function convertWebSocketDataToProgress(payload) {
  if (!payload) return null;

  console.log('🔍 分析payload结构:', JSON.stringify(payload, null, 2));

  let percentage = 0;
  let detail = '处理中...';
  let status = 'pending';

  // 从payload中提取实际的任务数据
  const taskData = payload.progress || payload.status || payload;

  // 安全地获取状态信息
  const taskStatus = taskData.state || taskData.status || 'PENDING';

  // 获取嵌套的进度信息
  const progressInfo = taskData.progress || {};

  console.log('📊 任务状态:', taskStatus);
  console.log('📊 进度信息:', progressInfo);

  // 根据任务状态确定进度
  if (taskStatus === 'PENDING') {
    // 对于PENDING状态，检查是否有实际的进度数据
    if (progressInfo.percentage !== undefined && progressInfo.percentage > 0) {
      percentage = progressInfo.percentage;
      detail = progressInfo.detail || progressInfo.stage || '处理中...';
      status = 'progress';
    } else {
      percentage = 5;
      detail = '等待处理...';
      status = 'pending';
    }
  } else if (taskStatus === 'PROGRESS') {
    // 如果有具体的进度信息
    if (progressInfo.percentage !== undefined) {
      percentage = progressInfo.percentage;
      detail = progressInfo.detail || progressInfo.stage || '处理中...';
    } else if (taskData.percentage !== undefined) {
      percentage = taskData.percentage;
      detail = taskData.detail || '处理中...';
    } else {
      percentage = 20;
      detail = '正在处理...';
    }
    status = 'progress';
  } else if (taskStatus === 'SUCCESS') {
    percentage = 100;
    detail = '处理完成';
    status = 'completed';
  } else if (taskStatus === 'FAILURE') {
    percentage = 0;
    detail = '处理失败';
    status = 'failed';
  }

  return {
    percentage: percentage,
    detail: detail,
    status: status,
    ready: taskData.ready,
    successful: taskData.successful,
    failed: taskData.failed,
    error_message: taskData.error || taskData.traceback || taskData.error_message,
    task_id: payload.task_id
  };
}

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n📤 发送关闭信号...');
  ws.close(1000, 'Client shutdown');
  setTimeout(() => {
    process.exit(0);
  }, 1000);
});

// 设置超时测试
setTimeout(() => {
  if (ws.readyState === WebSocket.OPEN) {
    console.log('⏰ 测试超时，主动关闭连接');
    ws.close(1000, 'Test timeout');
  }
}, 30000); // 30秒后自动关闭

console.log('💡 提示: 按 Ctrl+C 可以手动关闭连接');
console.log('💡 提示: 30秒后自动关闭连接');
console.log('💡 提示: 现在可以在前端上传文档，观察WebSocket消息\n');
