#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
逻辑测试脚本 - 验证代码逻辑而不依赖funasr库
"""

import os
import sys

def set_offline_mode():
    """设置离线模式环境变量"""
    os.environ['MODELSCOPE_OFFLINE_MODE'] = '1'
    os.environ['HF_HUB_OFFLINE'] = '1'
    os.environ['HF_DATASETS_OFFLINE'] = '1'
    os.environ['TRANSFORMERS_OFFLINE'] = '1'
    os.environ['DISABLE_MODEL_DOWNLOAD'] = '1'
    os.environ['NO_PROXY'] = '*'
    os.environ['REQUESTS_CA_BUNDLE'] = ''
    print("🔒 离线模式已启用")

def test_model_paths():
    """测试模型路径和文件检查逻辑"""
    print("🧪 测试模型路径和文件检查逻辑")
    print("=" * 50)
    
    # 测试SenseVoice路径
    sensevoice_path = r"C:\Users\<USER>\Documents\my_project\models\model_dir\SenseVoiceSmall"
    print(f"\n📁 SenseVoice模型路径: {sensevoice_path}")
    
    if os.path.exists(sensevoice_path):
        print("✅ SenseVoice路径存在")
        
        # 检查关键文件
        model_py_path = os.path.join(sensevoice_path, "model.py")
        config_path = os.path.join(sensevoice_path, "config.yaml")
        model_pt_path = os.path.join(sensevoice_path, "model.pt")
        
        print(f"  - model.py 存在: {os.path.exists(model_py_path)}")
        print(f"  - config.yaml 存在: {os.path.exists(config_path)}")
        print(f"  - model.pt 存在: {os.path.exists(model_pt_path)}")
        
        # 显示目录内容
        try:
            files = os.listdir(sensevoice_path)
            print(f"  - 总文件数: {len(files)}")
            print("  - 文件列表:")
            for file in files[:10]:  # 只显示前10个文件
                print(f"    * {file}")
            if len(files) > 10:
                print(f"    * ... 还有 {len(files) - 10} 个文件")
        except Exception as e:
            print(f"  ❌ 读取目录失败: {str(e)}")
    else:
        print("❌ SenseVoice路径不存在")
    
    # 测试Paraformer路径
    paraformer_path = r"C:\Users\<USER>\Documents\my_project\models\model_dir\speech_paraformer-large-vad-punc_asr_nat-zh-cn"
    print(f"\n📁 Paraformer模型路径: {paraformer_path}")
    
    if os.path.exists(paraformer_path):
        print("✅ Paraformer路径存在")
        
        # 检查关键文件
        config_path = os.path.join(paraformer_path, "config.yaml")
        model_bin_path = os.path.join(paraformer_path, "pytorch_model.bin")
        model_pt_path = os.path.join(paraformer_path, "model.pt")
        
        print(f"  - config.yaml 存在: {os.path.exists(config_path)}")
        print(f"  - pytorch_model.bin 存在: {os.path.exists(model_bin_path)}")
        print(f"  - model.pt 存在: {os.path.exists(model_pt_path)}")
        
        # 显示目录内容
        try:
            files = os.listdir(paraformer_path)
            print(f"  - 总文件数: {len(files)}")
            print("  - 文件列表:")
            for file in files[:10]:  # 只显示前10个文件
                print(f"    * {file}")
            if len(files) > 10:
                print(f"    * ... 还有 {len(files) - 10} 个文件")
        except Exception as e:
            print(f"  ❌ 读取目录失败: {str(e)}")
    else:
        print("❌ Paraformer路径不存在")

def test_config_generation():
    """测试配置生成逻辑"""
    print("\n🔧 测试配置生成逻辑")
    print("=" * 50)
    
    # SenseVoice配置测试
    model_path = r"C:\Users\<USER>\Documents\my_project\models\model_dir\SenseVoiceSmall"
    device = "cpu"
    
    print("📋 SenseVoice配置方案:")
    
    # 方案1配置
    config1 = {
        'model': model_path,
        'trust_remote_code': True,
        'device': device,
        'disable_update': True,
        'local_files_only': True,
        'force_download': False,
        'vad_model': None,
    }
    
    model_py_path = os.path.join(model_path, "model.py")
    if os.path.exists(model_py_path):
        config1['remote_code'] = model_py_path
    
    print("  方案1 (完整配置):")
    for key, value in config1.items():
        print(f"    {key}: {value}")
    
    # 方案2配置
    config2 = {
        'model': model_path,
        'trust_remote_code': True,
        'device': device,
        'local_files_only': True,
        'disable_update': True
    }
    
    print("\n  方案2 (简化配置):")
    for key, value in config2.items():
        print(f"    {key}: {value}")
    
    # 方案3配置
    config3 = {
        'model': model_path,
        'trust_remote_code': True,
        'device': device
    }
    
    print("\n  方案3 (最小配置):")
    for key, value in config3.items():
        print(f"    {key}: {value}")

def test_paraformer_names():
    """测试Paraformer官方名称列表"""
    print("\n📝 测试Paraformer官方名称列表")
    print("=" * 50)
    
    official_paraformer_names = [
        "damo/speech_paraformer-large-vad-punc_asr_nat-zh-cn-16k-common-vocab8404-pytorch",
        "damo_speech_paraformer-large-vad-punc_asr_nat-zh-cn-16k-common-vocab8404-pytorch",
        "paraformer-zh",
        "damo/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-pytorch",
        "damo_speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-pytorch",
        "iic/speech_seaco_paraformer_large_asr_nat-zh-cn-16k-common-vocab8404-pytorch",
        "iic_speech_seaco_paraformer_large_asr_nat-zh-cn-16k-common-vocab8404-pytorch"
    ]
    
    print("📋 官方标准名称列表:")
    for i, name in enumerate(official_paraformer_names, 1):
        print(f"  {i}. {name}")
    
    print(f"\n总计: {len(official_paraformer_names)} 个官方名称")

def main():
    """主函数"""
    print("🧪 语音模型加载逻辑测试")
    print("=" * 60)
    
    # 设置离线模式
    set_offline_mode()
    
    # 测试模型路径
    test_model_paths()
    
    # 测试配置生成
    test_config_generation()
    
    # 测试Paraformer名称
    test_paraformer_names()
    
    print("\n" + "=" * 60)
    print("🎉 逻辑测试完成！")
    print("💡 代码逻辑验证通过，等待funasr环境配置完成后即可进行实际模型加载测试")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 