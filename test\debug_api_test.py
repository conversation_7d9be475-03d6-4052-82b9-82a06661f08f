#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细的API调试测试
显示具体的错误信息
"""

import requests
import json

BASE_URL = "http://localhost:8002"
TEST_TOKEN = "test_token_12345"

def test_api_endpoints():
    """测试各个API端点并显示详细信息"""
    
    # 1. 健康检查
    print("1. 测试健康检查")
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.text}")
        print("   ✓ 健康检查通过\n")
    except Exception as e:
        print(f"   ✗ 健康检查失败: {e}\n")
    
    # 2. 获取API文档
    print("2. 测试API文档")
    try:
        response = requests.get(f"{BASE_URL}/docs", timeout=5)
        print(f"   状态码: {response.status_code}")
        print("   ✓ API文档可访问\n")
    except Exception as e:
        print(f"   ✗ API文档访问失败: {e}\n")
    
    # 3. 测试文件列表API
    print("3. 测试文件列表API")
    headers = {'Authorization': f'Bearer {TEST_TOKEN}'}
    try:
        response = requests.get(f"{BASE_URL}/api/v1/speech/files/1", headers=headers, timeout=5)
        print(f"   状态码: {response.status_code}")
        print(f"   响应: {response.text}")
        if response.status_code == 200:
            print("   ✓ 文件列表API正常")
        else:
            print("   ⚠ 文件列表API有问题")
    except Exception as e:
        print(f"   ✗ 文件列表API失败: {e}")
    
    print("\n" + "="*50)
    
    # 4. 检查API路由
    print("4. 检查可用的API路由")
    try:
        response = requests.get(f"{BASE_URL}/openapi.json", timeout=5)
        if response.status_code == 200:
            openapi_spec = response.json()
            paths = openapi_spec.get('paths', {})
            print(f"   发现 {len(paths)} 个API端点:")
            for path in sorted(paths.keys()):
                methods = list(paths[path].keys())
                print(f"   - {path} [{', '.join(methods).upper()}]")
        else:
            print(f"   无法获取API规格: {response.status_code}")
    except Exception as e:
        print(f"   ✗ 获取API规格失败: {e}")

if __name__ == "__main__":
    test_api_endpoints() 