#!/usr/bin/env python3
"""
检查数据库结构
"""
import sqlite3
import os

def check_database_structure():
    """检查数据库结构"""
    db_path = 'data/speech_platform.db'
    
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        print(f"✅ 数据库文件存在: {db_path}")
        
        # 查询所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print(f"\n📋 数据库中的表 ({len(tables)}个):")
        for table in tables:
            table_name = table[0]
            print(f"  - {table_name}")
            
            # 查询表结构
            cursor.execute(f"PRAGMA table_info({table_name});")
            columns = cursor.fetchall()
            
            print(f"    列信息:")
            for col in columns:
                col_id, col_name, col_type, not_null, default_val, pk = col
                print(f"      {col_name} ({col_type})")
            
            # 查询表中的记录数
            cursor.execute(f"SELECT COUNT(*) FROM {table_name};")
            count = cursor.fetchone()[0]
            print(f"    记录数: {count}")
            
            print()
            
    except Exception as e:
        print(f"❌ 检查数据库结构失败: {e}")
    finally:
        if 'conn' in locals():
            conn.close()

def check_data_directory():
    """检查data目录"""
    data_dir = 'data'
    
    print(f"🔍 检查data目录: {data_dir}")
    
    if not os.path.exists(data_dir):
        print(f"❌ data目录不存在")
        return
    
    files = os.listdir(data_dir)
    print(f"✅ data目录存在，包含文件:")
    for file in files:
        file_path = os.path.join(data_dir, file)
        if os.path.isfile(file_path):
            size = os.path.getsize(file_path)
            print(f"  - {file} ({size} bytes)")
        else:
            print(f"  - {file}/ (目录)")

if __name__ == "__main__":
    print("🚀 开始检查数据库结构...")
    check_data_directory()
    print()
    check_database_structure()
    print("\n🎉 检查完成！")
