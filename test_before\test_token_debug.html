<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Token调试工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            font-weight: bold;
            margin: 5px 0;
        }
        .status.valid { background-color: #d4edda; color: #155724; }
        .status.invalid { background-color: #f8d7da; color: #721c24; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .info-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            border: 1px solid #e9ecef;
        }
        .token-display {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            word-break: break-all;
            max-height: 100px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Token调试工具</h1>
        
        <div class="section">
            <h3>Token状态检查</h3>
            <button onclick="checkAllTokenSources()">检查所有Token来源</button>
            <button onclick="testLogin()">测试登录</button>
            <button onclick="clearLogs()">清空日志</button>
            
            <div class="info-grid">
                <div class="info-card">
                    <h4>LocalStorage Token</h4>
                    <div class="status" id="localStorageStatus">未检查</div>
                    <div class="token-display" id="localStorageToken">-</div>
                </div>
                
                <div class="info-card">
                    <h4>SessionStorage Token</h4>
                    <div class="status" id="sessionStorageStatus">未检查</div>
                    <div class="token-display" id="sessionStorageToken">-</div>
                </div>
            </div>
            
            <div class="info-grid">
                <div class="info-card">
                    <h4>Cookie Token</h4>
                    <div class="status" id="cookieStatus">未检查</div>
                    <div class="token-display" id="cookieToken">-</div>
                </div>
                
                <div class="info-card">
                    <h4>API测试</h4>
                    <div class="status" id="apiStatus">未测试</div>
                    <div id="apiResult">-</div>
                </div>
            </div>
        </div>
        
        <div class="section">
            <h3>调试日志</h3>
            <div class="log" id="logContainer"></div>
        </div>
    </div>

    <script>
        function log(message) {
            const logContainer = document.getElementById('logContainer');
            const timestamp = new Date().toLocaleTimeString();
            logContainer.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logContainer.scrollTop = logContainer.scrollHeight;
            console.log(message);
        }

        function updateStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            element.className = `status ${status}`;
            element.textContent = message;
        }

        function checkAllTokenSources() {
            log('🔍 开始检查所有Token来源...');
            
            // 检查 localStorage
            const localToken = localStorage.getItem('token') || localStorage.getItem('access_token') || localStorage.getItem('authToken');
            if (localToken) {
                updateStatus('localStorageStatus', 'valid', '找到Token');
                document.getElementById('localStorageToken').textContent = localToken.substring(0, 50) + '...';
                log(`✅ LocalStorage Token: ${localToken.substring(0, 20)}...`);
            } else {
                updateStatus('localStorageStatus', 'invalid', '未找到Token');
                document.getElementById('localStorageToken').textContent = '无';
                log('❌ LocalStorage 中未找到Token');
            }

            // 检查 sessionStorage
            const sessionToken = sessionStorage.getItem('token') || sessionStorage.getItem('access_token') || sessionStorage.getItem('authToken');
            if (sessionToken) {
                updateStatus('sessionStorageStatus', 'valid', '找到Token');
                document.getElementById('sessionStorageToken').textContent = sessionToken.substring(0, 50) + '...';
                log(`✅ SessionStorage Token: ${sessionToken.substring(0, 20)}...`);
            } else {
                updateStatus('sessionStorageStatus', 'invalid', '未找到Token');
                document.getElementById('sessionStorageToken').textContent = '无';
                log('❌ SessionStorage 中未找到Token');
            }

            // 检查 Cookie
            const cookies = document.cookie.split(';');
            let cookieToken = null;
            for (let cookie of cookies) {
                const [name, value] = cookie.trim().split('=');
                if (name === 'token' || name === 'access_token' || name === 'authToken') {
                    cookieToken = value;
                    break;
                }
            }
            
            if (cookieToken) {
                updateStatus('cookieStatus', 'valid', '找到Token');
                document.getElementById('cookieToken').textContent = cookieToken.substring(0, 50) + '...';
                log(`✅ Cookie Token: ${cookieToken.substring(0, 20)}...`);
            } else {
                updateStatus('cookieStatus', 'invalid', '未找到Token');
                document.getElementById('cookieToken').textContent = '无';
                log('❌ Cookie 中未找到Token');
            }

            // 检查所有可能的localStorage键
            log('🔍 检查所有localStorage键:');
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                const value = localStorage.getItem(key);
                if (key.toLowerCase().includes('token') || key.toLowerCase().includes('auth')) {
                    log(`📝 ${key}: ${value ? value.substring(0, 30) + '...' : 'null'}`);
                }
            }

            // 尝试获取最可能的token
            const possibleToken = localToken || sessionToken || cookieToken;
            if (possibleToken) {
                log(`🎯 最可能的Token: ${possibleToken.substring(0, 30)}...`);
                testTokenWithAPI(possibleToken);
            } else {
                log('❌ 未找到任何Token');
                updateStatus('apiStatus', 'invalid', '无Token可测试');
            }
        }

        async function testTokenWithAPI(token) {
            log('🧪 测试Token有效性...');
            
            try {
                // 测试用户信息API
                const response = await fetch('http://localhost:8002/api/v1/auth/me', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const userData = await response.json();
                    updateStatus('apiStatus', 'valid', 'Token有效');
                    document.getElementById('apiResult').innerHTML = `
                        <strong>用户:</strong> ${userData.username || 'N/A'}<br>
                        <strong>角色:</strong> ${userData.role || 'N/A'}
                    `;
                    log(`✅ Token验证成功: ${userData.username}`);
                } else {
                    updateStatus('apiStatus', 'invalid', `API错误: ${response.status}`);
                    document.getElementById('apiResult').textContent = `HTTP ${response.status}: ${response.statusText}`;
                    log(`❌ Token验证失败: ${response.status} ${response.statusText}`);
                }
            } catch (error) {
                updateStatus('apiStatus', 'invalid', '网络错误');
                document.getElementById('apiResult').textContent = error.message;
                log(`❌ API测试失败: ${error.message}`);
            }
        }

        async function testLogin() {
            log('🔐 测试登录流程...');
            
            try {
                const loginData = {
                    username: 'admin',
                    password: 'admin123'
                };

                const response = await fetch('http://localhost:8002/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(loginData)
                });

                if (response.ok) {
                    const result = await response.json();
                    const token = result.access_token;
                    
                    log(`✅ 登录成功，获得Token: ${token.substring(0, 30)}...`);
                    
                    // 保存到localStorage
                    localStorage.setItem('token', token);
                    log('💾 Token已保存到localStorage');
                    
                    // 重新检查Token状态
                    setTimeout(() => {
                        checkAllTokenSources();
                    }, 500);
                    
                } else {
                    const error = await response.json();
                    log(`❌ 登录失败: ${error.detail || response.statusText}`);
                }
            } catch (error) {
                log(`❌ 登录请求失败: ${error.message}`);
            }
        }

        function clearLogs() {
            document.getElementById('logContainer').innerHTML = '';
        }

        // 页面加载时自动检查
        window.onload = function() {
            log('🚀 Token调试工具已启动');
            checkAllTokenSources();
        };
    </script>
</body>
</html>
