#!/usr/bin/env python3
"""
测试文本清理功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.utils.audio.speech_recognition_core import clean_sensevoice_text

def test_text_cleanup():
    """测试文本清理功能"""
    
    # 测试用例：包含技术标记的文本
    test_cases = [
        {
            "input": "<|zh|><|SAD|><|Speech|><|withitn|>下辈子下辈子感觉像是一种约定，但永远不，只要两个人其实永远不在，都不是永远。 <|zh|><|EMO_UNKNOWN|><|Speech|><|withitn|>如果我下辈子的话，你想当什么人当人啊，当猪啊，认真认真。",
            "expected": "下辈子下辈子感觉像是一种约定，但永远不，只要两个人其实永远不在，都不是永远。 如果我下辈子的话，你想当什么人当人啊，当猪啊，认真认真。"
        },
        {
            "input": "<|zh|><|NEUTRAL|><|Speech|><|withitn|>如果我有下辈子的话，我想当戒指戒指或者是有没有改呢？吗？要是棉被跟戒指表达成陪伴，光你喜欢。<|BGM|>",
            "expected": "如果我有下辈子的话，我想当戒指戒指或者是有没有改呢？吗？要是棉被跟戒指表达成陪伴，光你喜欢。"
        },
        {
            "input": "正常文本没有标记",
            "expected": "正常文本没有标记"
        },
        {
            "input": "",
            "expected": ""
        }
    ]
    
    print("🧪 开始测试文本清理功能")
    print("=" * 80)
    
    all_passed = True
    
    for i, case in enumerate(test_cases, 1):
        print(f"\n测试用例 {i}:")
        print(f"输入: {case['input']}")
        
        result = clean_sensevoice_text(case['input'])
        print(f"输出: {result}")
        print(f"期望: {case['expected']}")
        
        if result == case['expected']:
            print("✅ 通过")
        else:
            print("❌ 失败")
            all_passed = False
    
    print("\n" + "=" * 80)
    if all_passed:
        print("🎉 所有测试用例通过！")
    else:
        print("❌ 部分测试用例失败")
    
    return all_passed

if __name__ == "__main__":
    test_text_cleanup()
