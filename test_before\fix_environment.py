#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
环境修复脚本
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(cmd, description=""):
    """运行命令并显示结果"""
    print(f"\n{'='*50}")
    print(f"执行: {description}")
    print(f"命令: {cmd}")
    print(f"{'='*50}")
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=300)
        
        if result.stdout:
            print("输出:")
            print(result.stdout)
        
        if result.stderr:
            print("错误:")
            print(result.stderr)
        
        if result.returncode == 0:
            print("✅ 命令执行成功")
            return True
        else:
            print(f"❌ 命令执行失败，返回码: {result.returncode}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 命令执行超时")
        return False
    except Exception as e:
        print(f"❌ 命令执行异常: {e}")
        return False

def backup_important_files():
    """备份重要文件"""
    print("\n🔄 备份重要文件...")
    
    backup_dir = Path("backup_before_fix")
    backup_dir.mkdir(exist_ok=True)
    
    important_files = [
        "requirements.txt",
        "pyproject.toml", 
        "uv.lock",
        "pages/语音处理分析.py",
        "utils/speech_recognition_utils.py"
    ]
    
    for file_path in important_files:
        if Path(file_path).exists():
            try:
                shutil.copy2(file_path, backup_dir / Path(file_path).name)
                print(f"✅ 备份: {file_path}")
            except Exception as e:
                print(f"⚠️ 备份失败 {file_path}: {e}")

def create_requirements():
    """创建requirements.txt"""
    requirements = """
# 基础依赖
numpy==1.24.3
torch==2.1.0
torchaudio==2.1.0
streamlit==1.28.0

# 语音处理
soundfile
librosa
scipy

# 机器学习
scikit-learn
matplotlib
pandas

# 其他工具
Pillow
requests
""".strip()
    
    with open("requirements_fixed.txt", "w", encoding="utf-8") as f:
        f.write(requirements)
    
    print("✅ 创建了 requirements_fixed.txt")

def main():
    """主修复流程"""
    print("🛠️ 环境修复脚本")
    print("=" * 50)
    
    # 1. 备份重要文件
    backup_important_files()
    
    # 2. 创建新的requirements
    create_requirements()
    
    # 3. 显示当前状态
    print(f"\n📍 当前工作目录: {os.getcwd()}")
    print(f"📍 Python路径: {sys.executable}")
    
    # 4. 检查虚拟环境
    venv_path = Path(".venv")
    if venv_path.exists():
        print(f"📍 虚拟环境存在: {venv_path.absolute()}")
    else:
        print("⚠️ 虚拟环境不存在")
    
    # 5. 提供修复建议
    print("\n" + "=" * 50)
    print("🔧 修复建议")
    print("=" * 50)
    
    print("""
建议的修复步骤:

1. 删除当前虚拟环境:
   rmdir /s .venv

2. 重新创建虚拟环境:
   uv venv

3. 激活虚拟环境:
   .venv\\Scripts\\activate

4. 安装基础依赖:
   uv pip install -r requirements_fixed.txt

5. 测试安装:
   python -c "import numpy; import torch; print('✅ 环境修复成功')"

6. 如果仍有问题，尝试:
   uv pip install --force-reinstall numpy==1.24.3 torch==2.1.0 torchaudio==2.1.0
    """)
    
    # 6. 询问是否自动执行
    print("\n" + "=" * 50)
    print("⚠️ 注意: 这将删除当前虚拟环境并重新创建")
    print("请确保已备份重要数据")
    
    choice = input("\n是否自动执行修复? (y/N): ").strip().lower()
    
    if choice == 'y':
        print("\n🚀 开始自动修复...")
        
        # 删除虚拟环境
        if venv_path.exists():
            print("删除旧虚拟环境...")
            try:
                shutil.rmtree(venv_path)
                print("✅ 旧虚拟环境已删除")
            except Exception as e:
                print(f"❌ 删除失败: {e}")
                return
        
        # 重新创建虚拟环境
        if run_command("uv venv", "创建新虚拟环境"):
            print("✅ 新虚拟环境创建成功")
        else:
            print("❌ 虚拟环境创建失败")
            return
        
        # 安装依赖
        if run_command("uv pip install -r requirements_fixed.txt", "安装依赖包"):
            print("✅ 依赖安装成功")
        else:
            print("❌ 依赖安装失败")
            return
        
        print("\n🎉 自动修复完成！")
        print("请运行以下命令测试:")
        print("python -c \"import numpy; import torch; print('✅ 环境修复成功')\"")
    
    else:
        print("\n📋 请手动执行上述步骤")

if __name__ == "__main__":
    main()
