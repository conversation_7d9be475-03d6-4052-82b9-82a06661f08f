"""
仅测试模块导入，不依赖外部服务
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

def test_basic_imports():
    """测试基础模块导入"""
    print("🚀 开始测试基础模块导入...")
    
    tests = []
    
    # 测试标准库
    try:
        import json
        import time
        import asyncio
        tests.append(("标准库", True, "导入成功"))
    except Exception as e:
        tests.append(("标准库", False, str(e)))
    
    # 测试Redis模块（不连接）
    try:
        import redis
        tests.append(("Redis模块", True, "导入成功"))
    except Exception as e:
        tests.append(("Redis模块", False, str(e)))
    
    # 测试Celery模块
    try:
        from celery import Celery
        tests.append(("Celery模块", True, "导入成功"))
    except Exception as e:
        tests.append(("Celery模块", False, str(e)))
    
    # 测试SQLAlchemy
    try:
        from sqlalchemy import create_engine, Column, Integer, String
        tests.append(("SQLAlchemy", True, "导入成功"))
    except Exception as e:
        tests.append(("SQLAlchemy", False, str(e)))
    
    # 测试FastAPI
    try:
        from fastapi import FastAPI
        tests.append(("FastAPI", True, "导入成功"))
    except Exception as e:
        tests.append(("FastAPI", False, str(e)))
    
    # 测试现有后端模块
    try:
        from backend.core.config_simple import settings
        tests.append(("配置模块", True, "导入成功"))
    except Exception as e:
        tests.append(("配置模块", False, str(e)))
    
    # 测试数据库模块
    try:
        from backend.core.database import get_db_session
        tests.append(("数据库模块", True, "导入成功"))
    except Exception as e:
        tests.append(("数据库模块", False, str(e)))
    
    return tests


def test_new_service_imports():
    """测试新服务模块导入"""
    print("🔧 开始测试新服务模块导入...")
    
    tests = []
    
    # 测试任务模型（修复后）
    try:
        from backend.models.task_models import TaskRecord, TaskStatus, TaskProgressLog
        tests.append(("任务模型", True, "导入成功"))
    except Exception as e:
        tests.append(("任务模型", False, str(e)))
    
    # 测试新服务模块
    service_modules = [
        ("资源监控", "backend.services.resource_monitor"),
        ("增强进度服务", "backend.services.enhanced_progress_service"),
        ("并发控制", "backend.services.concurrency_control"),
        ("错误处理", "backend.services.error_handler"),
        ("超时控制", "backend.services.timeout_control"),
        ("任务持久化", "backend.services.task_persistence_service"),
        ("任务恢复", "backend.services.task_recovery_service"),
    ]
    
    for service_name, module_path in service_modules:
        try:
            __import__(module_path)
            tests.append((service_name, True, "导入成功"))
        except Exception as e:
            tests.append((service_name, False, str(e)))
    
    # 测试任务队列（可能会因为Redis连接失败）
    try:
        from backend.core.task_queue import TaskManager
        tests.append(("任务队列类", True, "导入成功"))
    except Exception as e:
        tests.append(("任务队列类", False, str(e)))
    
    # 测试任务模块
    try:
        from backend.tasks.base_task import BaseTask
        tests.append(("基础任务类", True, "导入成功"))
    except Exception as e:
        tests.append(("基础任务类", False, str(e)))
    
    return tests


def test_api_imports():
    """测试API模块导入"""
    print("🌐 开始测试API模块导入...")
    
    tests = []
    
    # 测试新API端点
    api_modules = [
        ("任务管理API", "backend.api.v1.endpoints.task_management"),
        ("资源管理API", "backend.api.v1.endpoints.resource_management"),
        ("错误管理API", "backend.api.v1.endpoints.error_management"),
    ]
    
    for api_name, module_path in api_modules:
        try:
            __import__(module_path)
            tests.append((api_name, True, "导入成功"))
        except Exception as e:
            tests.append((api_name, False, str(e)))
    
    return tests


def print_test_results(test_name, tests):
    """打印测试结果"""
    print(f"\n{'='*60}")
    print(f"{test_name}结果:")
    print('='*60)
    
    passed = 0
    total = len(tests)
    
    for name, success, message in tests:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{name}: {status}")
        if not success:
            print(f"  错误: {message}")
        if success:
            passed += 1
    
    print(f"\n{test_name}: {passed}/{total} 个测试通过")
    return passed, total


def main():
    """主函数"""
    print("🚀 开始模块导入测试...\n")
    
    # 执行各项测试
    basic_tests = test_basic_imports()
    service_tests = test_new_service_imports()
    api_tests = test_api_imports()
    
    # 打印结果
    basic_passed, basic_total = print_test_results("基础模块导入", basic_tests)
    service_passed, service_total = print_test_results("新服务模块导入", service_tests)
    api_passed, api_total = print_test_results("API模块导入", api_tests)
    
    # 总结
    total_passed = basic_passed + service_passed + api_passed
    total_tests = basic_total + service_total + api_total
    
    print(f"\n{'='*60}")
    print("总体测试结果:")
    print('='*60)
    print(f"总计: {total_passed}/{total_tests} 个测试通过")
    print(f"成功率: {(total_passed/total_tests*100):.1f}%")
    
    if total_passed == total_tests:
        print("🎉 所有模块导入测试通过！")
        print("✅ 系统模块结构正确，可以进行下一步测试")
        return 0
    else:
        print("⚠️ 部分模块导入失败，需要修复")
        print("📝 建议：修复失败的模块后再进行功能测试")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
