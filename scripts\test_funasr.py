#!/usr/bin/env python3
"""
测试FunASR是否能正常工作
"""

import os
import sys
import logging

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_funasr_import():
    """测试FunASR导入"""
    try:
        import numpy as np
        logger.info(f"NumPy版本: {np.__version__}")
        
        import numba
        logger.info(f"Numba版本: {numba.__version__}")
        
        from funasr import AutoModel
        logger.info("FunASR导入成功")
        return True
    except ImportError as e:
        logger.error(f"FunASR导入失败: {e}")
        return False
    except Exception as e:
        logger.error(f"其他错误: {e}")
        return False

def test_optimized_manager():
    """测试优化的FunASR管理器"""
    try:
        from backend.utils.audio.optimized_funasr_manager import get_funasr_manager
        manager = get_funasr_manager()
        logger.info("优化FunASR管理器创建成功")
        
        # 检查状态
        status = manager.get_status()
        logger.info(f"管理器状态: {status}")
        
        return True
    except Exception as e:
        logger.error(f"优化FunASR管理器测试失败: {e}")
        return False

def test_model_loading():
    """测试模型加载"""
    try:
        from backend.utils.audio.optimized_funasr_manager import get_funasr_manager
        
        # 模型路径
        model_path = os.path.join(project_root, "models", "SenseVoiceSmall")
        
        if not os.path.exists(model_path):
            logger.warning(f"模型路径不存在: {model_path}")
            return False
        
        logger.info(f"测试模型加载: {model_path}")
        
        manager = get_funasr_manager()
        success = manager.load_model(model_path)
        
        if success:
            logger.info("模型加载成功")
            
            # 获取状态
            status = manager.get_status()
            logger.info(f"加载后状态: {status}")
            
            return True
        else:
            logger.error("模型加载失败")
            return False
            
    except Exception as e:
        logger.error(f"模型加载测试失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("开始FunASR测试")
    
    # 测试导入
    if not test_funasr_import():
        logger.error("FunASR导入测试失败")
        return False
    
    # 测试管理器
    if not test_optimized_manager():
        logger.error("优化管理器测试失败")
        return False
    
    # 测试模型加载
    if not test_model_loading():
        logger.error("模型加载测试失败")
        return False
    
    logger.info("所有测试通过")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
