import{K as t}from"./index-2c134546.js";const r="/api/v1/audio",s="/api/v1/speech",d={getAudioFiles:()=>t({url:`${r}/`,method:"get"}),getUserFiles:()=>t({url:`${r}/user/files`,method:"get"}),uploadAudio:(e,o)=>{const a=new FormData;return a.append("file",e),t({url:`${r}/upload`,method:"post",data:a,headers:{"Content-Type":"multipart/form-data"},onUploadProgress:u=>{if(o){const l=Math.round(u.loaded*100/u.total);o(l)}}})},uploadAudioBatch:(e,o)=>{const a=new FormData;return e.forEach(u=>{a.append("files",u)}),t({url:`${r}/upload/batch`,method:"post",data:a,headers:{"Content-Type":"multipart/form-data"},onUploadProgress:u=>{if(o){const l=Math.round(u.loaded*100/u.total);o(l)}}})},getAudioFile:e=>t({url:`${r}/${e}`,method:"get"}),deleteAudio:e=>t({url:`${r}/${e}`,method:"delete"}),deleteAudioFile:e=>t({url:`${r}/${e}`,method:"delete"}),downloadAudioFile:e=>t({url:`${r}/${e}/download`,method:"get",responseType:"blob"}),deleteFiles:e=>t({url:`${r}/batch/delete`,method:"post",data:{file_ids:e}}),submitBatchProcessingTask:e=>t({url:`${r}/batch/process`,method:"post",data:e}),getSystemStatus:()=>t({url:`${r}/system/status`,method:"get"}),getProcessedFilesCount:()=>t({url:`${r}/stats/files`,method:"get"}),getActiveTasks:()=>t({url:`${r}/tasks/active`,method:"get"}),getOnlineUsers:()=>t({url:`${r}/users/online`,method:"get"}),createProcessingTask:e=>t({url:`${r}/tasks`,method:"post",data:e}),submitProcessingTask:e=>t({url:`${r}/process`,method:"post",data:e}),pauseTask:e=>t({url:`${r}/tasks/${e}/pause`,method:"post"}),resumeTask:e=>t({url:`${r}/tasks/${e}/resume`,method:"post"}),getProcessingResults:(e={})=>t({url:`${r}/results`,method:"get",params:{limit:e.limit||50,offset:e.offset||0}}),getProcessingResultDetail:e=>t({url:`${r}/results/${e}`,method:"get"}),deleteProcessingResult:e=>t({url:`${r}/results/${e}`,method:"delete"}),getSystemResources:()=>t({url:"/api/v1/resource/system/resources",method:"get"}),getSystemHealth:()=>t({url:"/api/v1/resource/system/health",method:"get"}),getQueueStats:()=>t({url:"/api/v1/task/queue/statistics",method:"get"}),getActiveTasksList:()=>t({url:"/api/v1/task/active",method:"get"}),getSystemLogs:(e={})=>t({url:"/api/v1/error/logs",method:"get",params:{limit:e.limit||20,level:e.level||"all"}}),renameFile:(e,o)=>t({url:`${r}/${e}/rename`,method:"put",data:{new_name:o}})},i={vadDetection:e=>t({url:`${s}/vad-detection`,method:"post",data:e}),speechRecognition:e=>t({url:`${s}/speech-recognition`,method:"post",data:e}),speakerRecognition:e=>t({url:`${s}/speaker-recognition`,method:"post",data:e}),meetingTranscription:e=>t({url:`${s}/meeting-transcription`,method:"post",data:e}),audioPreprocessing:e=>t({url:`${s}/preprocessing`,method:"post",data:e}),audioEnhancement:e=>t({url:`${s}/audio-enhancement`,method:"post",data:e}),qualityAnalysis:e=>t({url:`${s}/quality-analysis`,method:"post",data:e}),getTaskStatus:e=>t({url:`${s}/task/${e}`,method:"get"}),cancelTask:e=>t({url:`${s}/task/${e}`,method:"delete"}),getTaskResult:e=>t({url:`${s}/task/${e}/result`,method:"get"}),downloadResult:(e,o="json")=>t({url:`${r}/results/${e}/download`,method:"get",params:{format:o},responseType:"blob"}),createTask:e=>t({url:`${r}/tasks`,method:"post",data:e})};export{d as a,i as b};
