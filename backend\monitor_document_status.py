#!/usr/bin/env python3
"""
文档状态监控和自动修复脚本
"""

import sys
import os
import time
from datetime import datetime, timezone, timedelta

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

from backend.core.database import get_db_session
from backend.models.document_management import ManagedDocument, DocumentSection
from backend.services.document_db_service import document_db_service
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def check_and_fix_inconsistent_documents():
    """检查并修复状态不一致的文档"""
    db = get_db_session()
    try:
        # 查找最近1小时内创建的状态不一致文档
        one_hour_ago = datetime.now(timezone.utc) - timedelta(hours=1)
        
        # 查找状态为 uploaded 或 processing 但有节点的文档
        problematic_docs = db.query(ManagedDocument)\
            .filter(ManagedDocument.created_at >= one_hour_ago)\
            .filter(ManagedDocument.status.in_(['uploaded', 'processing']))\
            .filter(ManagedDocument.sections_count > 0)\
            .all()
        
        if not problematic_docs:
            return 0
        
        fixed_count = 0
        
        for doc in problematic_docs:
            # 检查实际节点数量
            actual_sections = db.query(DocumentSection)\
                               .filter(DocumentSection.document_id == doc.id)\
                               .count()
            
            if actual_sections > 0:
                logger.info(f"修复文档状态: ID={doc.id}, 文件名={doc.filename[:30]}...")
                
                try:
                    # 更新文档状态
                    update_data = {
                        "status": "completed",
                        "processing_progress": 1.0,
                        "processed_at": datetime.now(timezone.utc)
                    }
                    
                    # 如果节点数不匹配，也更新节点数
                    if doc.sections_count != actual_sections:
                        update_data["sections_count"] = actual_sections
                    
                    document_db_service.update_document(
                        db, doc.id, doc.user_id, update_data
                    )
                    
                    # 添加处理日志
                    document_db_service.add_processing_log(
                        db, doc.id, "INFO",
                        f"自动状态修复：{actual_sections}个节点",
                        "auto_fix"
                    )
                    
                    fixed_count += 1
                    logger.info(f"文档状态修复成功: ID={doc.id}")
                    
                except Exception as e:
                    logger.error(f"修复文档状态失败: ID={doc.id}, 错误={e}")
        
        return fixed_count
        
    except Exception as e:
        logger.error(f"检查文档状态失败: {e}")
        return 0
    finally:
        db.close()

def monitor_loop():
    """监控循环"""
    logger.info("开始文档状态监控...")
    
    check_interval = 30  # 30秒检查一次
    
    while True:
        try:
            fixed_count = check_and_fix_inconsistent_documents()
            
            if fixed_count > 0:
                logger.info(f"本次检查修复了 {fixed_count} 个文档状态")
            
            # 等待下次检查
            time.sleep(check_interval)
            
        except KeyboardInterrupt:
            logger.info("监控已停止")
            break
        except Exception as e:
            logger.error(f"监控循环出错: {e}")
            time.sleep(check_interval)

def run_once():
    """运行一次检查"""
    logger.info("执行一次文档状态检查...")
    fixed_count = check_and_fix_inconsistent_documents()
    
    if fixed_count > 0:
        logger.info(f"修复了 {fixed_count} 个文档状态")
    else:
        logger.info("没有发现需要修复的文档")

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description='文档状态监控工具')
    parser.add_argument('--once', action='store_true', help='只运行一次检查')
    parser.add_argument('--monitor', action='store_true', help='持续监控模式')
    
    args = parser.parse_args()
    
    if args.once:
        run_once()
    elif args.monitor:
        monitor_loop()
    else:
        # 默认运行一次
        run_once()
