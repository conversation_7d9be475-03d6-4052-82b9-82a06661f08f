<template>
  <div class="websocket-status" :class="statusClass">
    <div class="status-indicator">
      <div class="status-dot" :class="dotClass"></div>
      <span class="status-text">{{ statusText }}</span>
    </div>
    
    <div v-if="showDetails" class="status-details">
      <div v-if="isConnecting" class="connecting-info">
        <el-icon class="rotating"><Loading /></el-icon>
        <span>正在连接...</span>
      </div>
      
      <div v-if="hasError" class="error-info">
        <el-icon><Warning /></el-icon>
        <span>{{ lastError?.message || '连接错误' }}</span>
      </div>
      
      <div v-if="reconnectAttempts > 0" class="reconnect-info">
        <span>重连尝试: {{ reconnectAttempts }}/{{ maxReconnectAttempts }}</span>
      </div>
    </div>
    
    <div v-if="showActions" class="status-actions">
      <el-button 
        v-if="isDisconnected || hasError" 
        size="small" 
        type="primary" 
        @click="handleConnect"
        :loading="isConnecting"
      >
        {{ isConnecting ? '连接中...' : '重连' }}
      </el-button>
      
      <el-button 
        v-if="isConnected" 
        size="small" 
        type="danger" 
        @click="handleDisconnect"
      >
        断开
      </el-button>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { ElButton, ElIcon, ElMessage } from 'element-plus'
import { Loading, Warning } from '@element-plus/icons-vue'
import { useWebSocketStore } from '@/stores/websocket'

const props = defineProps({
  showDetails: {
    type: Boolean,
    default: false
  },
  showActions: {
    type: Boolean,
    default: false
  },
  autoConnect: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['connected', 'disconnected', 'error'])

// 使用统一的WebSocket状态管理
const wsStore = useWebSocketStore()

// 计算属性
const statusClass = computed(() => {
  return {
    'status-connected': wsStore.isConnected,
    'status-connecting': wsStore.isConnecting,
    'status-disconnected': wsStore.isDisconnected,
    'status-error': wsStore.hasError
  }
})

const dotClass = computed(() => {
  return {
    'dot-connected': wsStore.isConnected,
    'dot-connecting': wsStore.isConnecting,
    'dot-disconnected': wsStore.isDisconnected,
    'dot-error': wsStore.hasError
  }
})

const statusText = computed(() => {
  if (wsStore.isConnected) return 'WebSocket已连接'
  if (wsStore.isConnecting) return 'WebSocket连接中'
  if (wsStore.hasError) return 'WebSocket连接错误'
  return 'WebSocket未连接'
})

const maxReconnectAttempts = computed(() => 5) // 从配置获取

// 解构响应式状态
const { 
  isConnected, 
  isConnecting, 
  isDisconnected, 
  hasError, 
  reconnectAttempts, 
  lastError 
} = wsStore

// 方法
const handleConnect = async () => {
  try {
    await wsStore.connect()
    ElMessage.success('WebSocket连接成功')
    emit('connected')
  } catch (error) {
    ElMessage.error(`WebSocket连接失败: ${error.message}`)
    emit('error', error)
  }
}

const handleDisconnect = () => {
  wsStore.disconnect()
  ElMessage.info('WebSocket已断开')
  emit('disconnected')
}

// 监听WebSocket事件
wsStore.on('connected', () => {
  emit('connected')
})

wsStore.on('disconnected', (data) => {
  emit('disconnected', data)
})

wsStore.on('error', (error) => {
  emit('error', error)
})

// 自动连接
if (props.autoConnect && wsStore.isDisconnected) {
  handleConnect()
}
</script>

<style scoped>
.websocket-status {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  border-radius: 6px;
  background-color: var(--el-bg-color-page);
  border: 1px solid var(--el-border-color-light);
  transition: all 0.3s ease;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.dot-connected {
  background-color: #67c23a;
  box-shadow: 0 0 6px rgba(103, 194, 58, 0.5);
}

.dot-connecting {
  background-color: #e6a23c;
  animation: pulse 1.5s infinite;
}

.dot-disconnected {
  background-color: #909399;
}

.dot-error {
  background-color: #f56c6c;
  animation: pulse 1.5s infinite;
}

.status-text {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

.status-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.connecting-info,
.error-info,
.reconnect-info {
  display: flex;
  align-items: center;
  gap: 4px;
}

.status-actions {
  display: flex;
  gap: 8px;
}

.status-connected {
  border-color: #67c23a;
  background-color: rgba(103, 194, 58, 0.1);
}

.status-connecting {
  border-color: #e6a23c;
  background-color: rgba(230, 162, 60, 0.1);
}

.status-disconnected {
  border-color: #909399;
  background-color: rgba(144, 147, 153, 0.1);
}

.status-error {
  border-color: #f56c6c;
  background-color: rgba(245, 108, 108, 0.1);
}

@keyframes pulse {
  0% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.1);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.rotating {
  animation: rotate 1s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
