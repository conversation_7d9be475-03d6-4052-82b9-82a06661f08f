#!/usr/bin/env python3
"""
DocumentManager功能测试脚本
测试文档处理流程的各个功能点
"""

import requests
import json
import time
import os
from pathlib import Path

# 测试配置
BASE_URL = "http://localhost:8000"
TEST_TOKEN = "demo-token"

class DocumentManagerTester:
    def __init__(self):
        self.base_url = BASE_URL
        self.headers = {
            "Authorization": f"Bearer {TEST_TOKEN}",
            "Content-Type": "application/json"
        }
        self.test_results = []

    def log_test(self, test_name, success, message=""):
        """记录测试结果"""
        status = "✅ PASS" if success else "❌ FAIL"
        result = f"{status} {test_name}"
        if message:
            result += f" - {message}"
        print(result)
        self.test_results.append({
            "test": test_name,
            "success": success,
            "message": message
        })

    def test_health_check(self):
        """测试服务器健康检查"""
        try:
            response = requests.get(f"{self.base_url}/health", timeout=5)
            success = response.status_code == 200
            self.log_test("服务器健康检查", success, f"状态码: {response.status_code}")
            return success
        except Exception as e:
            self.log_test("服务器健康检查", False, str(e))
            return False

    def test_document_list(self):
        """测试文档列表获取"""
        try:
            response = requests.get(
                f"{self.base_url}/documents/documents",
                headers=self.headers,
                timeout=10
            )
            success = response.status_code == 200
            if success:
                data = response.json()
                count = len(data.get('documents', []))
                self.log_test("文档列表获取", success, f"获取到 {count} 个文档")
            else:
                self.log_test("文档列表获取", success, f"状态码: {response.status_code}")
            return success
        except Exception as e:
            self.log_test("文档列表获取", False, str(e))
            return False

    def test_text_upload(self):
        """测试文本上传功能"""
        try:
            test_data = {
                "filename": "test_document.txt",
                "content": "这是一个测试文档的内容。\n\n包含多个段落和换行符。\n\n用于测试文档处理流程的各个功能。",
                "file_type": "txt"
            }
            
            response = requests.post(
                f"{self.base_url}/documents/upload-text",
                headers=self.headers,
                json=test_data,
                timeout=30
            )
            
            success = response.status_code == 200
            if success:
                data = response.json()
                nodes_added = data.get('nodes_added', 0)
                self.log_test("文本上传功能", success, f"生成 {nodes_added} 个节点")
            else:
                self.log_test("文本上传功能", success, f"状态码: {response.status_code}")
            return success
        except Exception as e:
            self.log_test("文本上传功能", False, str(e))
            return False

    def test_upload_settings_validation(self):
        """测试上传设置验证"""
        test_settings = {
            "document_type": "auto",
            "chunk_size": 1000,
            "chunk_overlap": 100,
            "split_strategy": "smart",
            "preprocessing": {
                "remove_headers": True,
                "remove_watermark": False,
                "clean_text": True,
                "preserve_format": False
            }
        }
        
        # 验证设置格式
        required_fields = ["document_type", "chunk_size", "chunk_overlap", "split_strategy"]
        success = all(field in test_settings for field in required_fields)
        
        # 验证数值范围
        if success:
            success = (200 <= test_settings["chunk_size"] <= 2000 and
                      0 <= test_settings["chunk_overlap"] <= 200)
        
        self.log_test("上传设置验证", success, "设置格式和数值范围检查")
        return success

    def test_ocr_settings_validation(self):
        """测试OCR设置验证"""
        test_ocr_settings = {
            "language": "chi_sim+eng",
            "image_preprocess": True,
            "dpi": 300,
            "contrast": 2.0,
            "brightness": 1.2,
            "psm": 3,
            "adaptive_threshold": True
        }
        
        # 验证OCR设置格式
        required_fields = ["language", "dpi", "psm"]
        success = all(field in test_ocr_settings for field in required_fields)
        
        # 验证数值范围
        if success:
            success = (200 <= test_ocr_settings["dpi"] <= 600 and
                      1.0 <= test_ocr_settings["contrast"] <= 3.0 and
                      0.8 <= test_ocr_settings["brightness"] <= 1.5)
        
        self.log_test("OCR设置验证", success, "OCR参数格式和范围检查")
        return success

    def test_error_handling(self):
        """测试错误处理机制"""
        try:
            # 测试无效的API端点
            response = requests.get(
                f"{self.base_url}/invalid-endpoint",
                headers=self.headers,
                timeout=5
            )
            
            # 应该返回404错误
            success = response.status_code == 404
            self.log_test("错误处理机制", success, f"无效端点返回: {response.status_code}")
            return success
        except Exception as e:
            self.log_test("错误处理机制", False, str(e))
            return False

    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始DocumentManager功能测试...\n")
        
        tests = [
            self.test_health_check,
            self.test_document_list,
            self.test_upload_settings_validation,
            self.test_ocr_settings_validation,
            self.test_text_upload,
            self.test_error_handling
        ]
        
        passed = 0
        total = len(tests)
        
        for test in tests:
            if test():
                passed += 1
            time.sleep(1)  # 避免请求过快
        
        print(f"\n📊 测试结果: {passed}/{total} 通过")
        
        if passed == total:
            print("🎉 所有测试通过！DocumentManager功能正常")
            return True
        else:
            print("⚠️  部分测试失败，请检查相关功能")
            return False

    def generate_report(self):
        """生成测试报告"""
        report = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "total_tests": len(self.test_results),
            "passed_tests": sum(1 for r in self.test_results if r["success"]),
            "failed_tests": sum(1 for r in self.test_results if not r["success"]),
            "results": self.test_results
        }
        
        with open("document_manager_test_report.json", "w", encoding="utf-8") as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"📄 测试报告已保存到: document_manager_test_report.json")

if __name__ == "__main__":
    tester = DocumentManagerTester()
    success = tester.run_all_tests()
    tester.generate_report()
    
    if not success:
        exit(1)
