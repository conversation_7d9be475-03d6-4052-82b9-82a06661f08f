"""
测试Celery回调机制是否正常工作
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.core.task_queue import celery_app
from backend.tasks.base_task import BaseTask
from backend.models.task_models import TaskRecord, TaskStatus
from backend.core.database import get_db
from backend.services.task_persistence_service import TaskPersistenceService
import time
import uuid

@celery_app.task(bind=True, base=BaseTask)
def test_callback_task(self, task_id: str):
    """测试回调机制的简单任务"""
    print(f"🧪 开始测试任务: {task_id}")
    
    # 模拟一些工作
    time.sleep(2)
    
    # 返回结果
    result = {
        "task_type": "test_callback",
        "message": "测试任务完成",
        "timestamp": time.time()
    }
    
    print(f"🧪 测试任务完成: {task_id}")
    return result

def test_celery_callbacks():
    """测试Celery回调机制"""
    print("🧪 测试Celery回调机制...")
    
    # 1. 创建数据库任务记录
    db = next(get_db())
    persistence_service = TaskPersistenceService()
    
    task_id = f"test_callback_{uuid.uuid4().hex[:8]}"
    
    try:
        # 创建任务记录
        task_record = persistence_service.create_task_record(
            db=db,
            task_id=task_id,
            user_id="1",
            task_type="test_callback",
            task_name="测试回调机制",
            task_args={},
            task_kwargs={},
            queue_name="default",
            metadata={}
        )
        
        print(f"✅ 创建任务记录: {task_id}")
        
        # 2. 提交Celery任务
        celery_task = test_callback_task.apply_async(
            args=[task_id],
            task_id=task_id
        )
        
        print(f"✅ 提交Celery任务: {task_id}")
        
        # 3. 等待任务完成
        print("⏳ 等待任务完成...")
        result = celery_task.get(timeout=30)
        
        print(f"✅ 任务执行完成: {result}")
        
        # 4. 检查数据库状态
        time.sleep(1)  # 等待回调执行
        
        task_record = db.query(TaskRecord).filter(TaskRecord.task_id == task_id).first()
        if task_record:
            print(f"\n📊 最终数据库状态:")
            print(f"  - 状态: {task_record.status}")
            print(f"  - 进度: {task_record.progress_percentage}%")
            print(f"  - 详情: {task_record.progress_detail}")
            print(f"  - 阶段: {task_record.progress_stage}")
            print(f"  - 完成时间: {task_record.completed_at}")
            
            if task_record.status == TaskStatus.SUCCESS:
                print("✅ 回调机制工作正常！")
            else:
                print("❌ 回调机制可能有问题")
        else:
            print("❌ 任务记录不存在")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    test_celery_callbacks()
