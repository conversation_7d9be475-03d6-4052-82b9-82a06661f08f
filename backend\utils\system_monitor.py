"""
系统资源监控和优化工具
"""

import os
import gc
import time
import psutil
import threading
from typing import Dict, Optional, Callable
from dataclasses import dataclass
from celery.utils.log import get_task_logger

logger = get_task_logger(__name__)

try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    logger.warning("PyTorch不可用，GPU监控功能将被禁用")


@dataclass
class SystemResources:
    """系统资源状态"""
    cpu_percent: float
    memory_used_mb: float
    memory_percent: float
    gpu_memory_used_mb: float = 0.0
    gpu_memory_percent: float = 0.0
    gpu_utilization: float = 0.0
    timestamp: float = 0.0


class ResourceMonitor:
    """资源监控器"""
    
    def __init__(self, check_interval: float = 5.0):
        self.check_interval = check_interval
        self.monitoring = False
        self.monitor_thread = None
        self.callbacks = []
        self.last_resources = None
        
        # 阈值配置
        self.memory_warning_threshold = 80.0  # 内存使用超过80%警告
        self.memory_critical_threshold = 90.0  # 内存使用超过90%严重警告
        self.gpu_memory_warning_threshold = 85.0  # GPU内存使用超过85%警告
        
    def add_callback(self, callback: Callable[[SystemResources], None]):
        """添加资源状态回调"""
        self.callbacks.append(callback)
    
    def get_current_resources(self) -> SystemResources:
        """获取当前系统资源状态"""
        # CPU和内存信息
        cpu_percent = psutil.cpu_percent(interval=0.1)
        memory = psutil.virtual_memory()
        memory_used_mb = memory.used / 1024 / 1024
        memory_percent = memory.percent
        
        # GPU信息
        gpu_memory_used_mb = 0.0
        gpu_memory_percent = 0.0
        gpu_utilization = 0.0
        
        if TORCH_AVAILABLE and torch.cuda.is_available():
            try:
                gpu_memory_used = torch.cuda.memory_allocated() / 1024 / 1024
                gpu_memory_total = torch.cuda.get_device_properties(0).total_memory / 1024 / 1024
                gpu_memory_used_mb = gpu_memory_used
                gpu_memory_percent = (gpu_memory_used / gpu_memory_total) * 100
                
                # GPU利用率（简化版本）
                gpu_utilization = min(gpu_memory_percent, 100.0)
                
            except Exception as e:
                logger.warning(f"获取GPU信息失败: {e}")
        
        return SystemResources(
            cpu_percent=cpu_percent,
            memory_used_mb=memory_used_mb,
            memory_percent=memory_percent,
            gpu_memory_used_mb=gpu_memory_used_mb,
            gpu_memory_percent=gpu_memory_percent,
            gpu_utilization=gpu_utilization,
            timestamp=time.time()
        )
    
    def check_resource_warnings(self, resources: SystemResources):
        """检查资源警告"""
        warnings = []
        
        # 内存警告
        if resources.memory_percent > self.memory_critical_threshold:
            warnings.append(f"严重警告：系统内存使用率 {resources.memory_percent:.1f}% 超过临界值")
        elif resources.memory_percent > self.memory_warning_threshold:
            warnings.append(f"警告：系统内存使用率 {resources.memory_percent:.1f}% 较高")
        
        # GPU内存警告
        if resources.gpu_memory_percent > self.gpu_memory_warning_threshold:
            warnings.append(f"警告：GPU内存使用率 {resources.gpu_memory_percent:.1f}% 较高")
        
        # 记录警告
        for warning in warnings:
            logger.warning(warning)
        
        return warnings
    
    def start_monitoring(self):
        """开始监控"""
        if self.monitoring:
            return
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        logger.info("系统资源监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=1.0)
        logger.info("系统资源监控已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                resources = self.get_current_resources()
                self.last_resources = resources
                
                # 检查警告
                self.check_resource_warnings(resources)
                
                # 调用回调函数
                for callback in self.callbacks:
                    try:
                        callback(resources)
                    except Exception as e:
                        logger.error(f"资源监控回调失败: {e}")
                
                time.sleep(self.check_interval)
                
            except Exception as e:
                logger.error(f"资源监控循环错误: {e}")
                time.sleep(self.check_interval)


class ResourceOptimizer:
    """资源优化器"""
    
    @staticmethod
    def cleanup_memory():
        """清理内存"""
        try:
            # Python垃圾回收
            collected = gc.collect()
            logger.info(f"垃圾回收清理了 {collected} 个对象")
            
            # CUDA缓存清理
            if TORCH_AVAILABLE and torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.synchronize()
                logger.info("CUDA缓存已清理")
            
            return True
            
        except Exception as e:
            logger.error(f"内存清理失败: {e}")
            return False
    
    @staticmethod
    def optimize_cuda_settings():
        """优化CUDA设置"""
        if not TORCH_AVAILABLE or not torch.cuda.is_available():
            return False
        
        try:
            # 设置CUDA内存分配策略
            os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:512'
            os.environ['CUDA_LAUNCH_BLOCKING'] = '0'
            
            # 设置内存增长策略
            torch.cuda.empty_cache()
            
            logger.info("CUDA设置已优化")
            return True
            
        except Exception as e:
            logger.error(f"CUDA优化失败: {e}")
            return False
    
    @staticmethod
    def get_optimal_worker_count() -> int:
        """获取最优worker数量"""
        try:
            cpu_count = psutil.cpu_count()
            memory_gb = psutil.virtual_memory().total / (1024**3)
            
            # 基于CPU和内存的保守策略
            cpu_based = max(2, cpu_count // 2)  # CPU核心数的一半
            memory_based = max(2, int(memory_gb // 4))  # 每4GB内存一个worker
            
            # 取较小值，最多不超过4个
            optimal_count = min(cpu_based, memory_based, 4)
            
            logger.info(f"推荐worker数量: {optimal_count} (CPU: {cpu_count}, 内存: {memory_gb:.1f}GB)")
            return optimal_count
            
        except Exception as e:
            logger.error(f"计算最优worker数量失败: {e}")
            return 2  # 默认值


# 全局资源监控器实例
resource_monitor = ResourceMonitor()


def get_resource_monitor() -> ResourceMonitor:
    """获取资源监控器实例"""
    return resource_monitor


def monitor_task_resources(task_id: str, process_pid: Optional[int] = None):
    """监控任务资源使用"""
    try:
        if process_pid:
            process = psutil.Process(process_pid)
        else:
            process = psutil.Process()
        
        memory_mb = process.memory_info().rss / 1024 / 1024
        cpu_percent = process.cpu_percent()
        
        logger.info(f"任务 {task_id} 资源使用 - 内存: {memory_mb:.1f}MB, CPU: {cpu_percent:.1f}%")
        
        return {
            "memory_mb": memory_mb,
            "cpu_percent": cpu_percent,
            "timestamp": time.time()
        }
        
    except Exception as e:
        logger.error(f"监控任务资源失败: {e}")
        return None


def auto_cleanup_on_high_memory(threshold_percent: float = 85.0):
    """内存使用率过高时自动清理"""
    try:
        memory = psutil.virtual_memory()
        if memory.percent > threshold_percent:
            logger.warning(f"内存使用率 {memory.percent:.1f}% 超过阈值，执行自动清理")
            ResourceOptimizer.cleanup_memory()
            return True
        return False
        
    except Exception as e:
        logger.error(f"自动内存清理失败: {e}")
        return False
