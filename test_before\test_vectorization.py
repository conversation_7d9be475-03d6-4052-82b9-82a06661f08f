#!/usr/bin/env python3
"""
测试向量化任务
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.services.document_db_service import document_db_service
from backend.core.database import get_db_session

def test_document_access():
    """测试文档访问"""
    print("测试文档访问...")
    
    db = get_db_session()
    try:
        # 获取文档
        document = document_db_service.get_document(db, 1, 1)
        if not document:
            print("❌ 文档不存在")
            return False
        
        print(f"✅ 文档对象类型: {type(document)}")
        print(f"✅ 文档对象属性: {dir(document)}")
        
        # 测试 to_dict 方法
        try:
            document_dict = document.to_dict()
            print(f"✅ to_dict() 成功: {type(document_dict)}")
            filename = document_dict.get("filename", "unknown")
            print(f"✅ 文件名: {filename}")
            return True
        except Exception as e:
            print(f"❌ to_dict() 失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ 获取文档失败: {e}")
        return False
    finally:
        db.close()

def test_vectorization_task_import():
    """测试向量化任务导入"""
    print("\n测试向量化任务导入...")
    
    try:
        from backend.tasks.vectorization_tasks import vectorize_document_task
        print("✅ 向量化任务导入成功")
        
        # 检查任务函数
        print(f"✅ 任务函数: {vectorize_document_task}")
        print(f"✅ 任务名称: {vectorize_document_task.name}")
        
        return True
    except Exception as e:
        print(f"❌ 向量化任务导入失败: {e}")
        return False

def test_sections_data():
    """测试节点数据获取"""
    print("\n测试节点数据获取...")
    
    db = get_db_session()
    try:
        # 获取文档节点
        sections = document_db_service.get_document_sections(db, 1)
        print(f"✅ 获取到 {len(sections['sections'])} 个节点")
        
        if sections['sections']:
            first_section = sections['sections'][0]
            print(f"✅ 第一个节点: {first_section}")
            
            # 构造向量化数据
            sections_data = [
                {
                    "id": section["id"],
                    "content": section["content"],
                    "title": section["title"],
                    "section_type": section.get("section_type", "content")
                }
                for section in sections['sections']
            ]
            print(f"✅ 构造向量化数据成功: {len(sections_data)} 个节点")
            return sections_data
        else:
            print("❌ 没有找到节点数据")
            return []
            
    except Exception as e:
        print(f"❌ 获取节点数据失败: {e}")
        return []
    finally:
        db.close()

if __name__ == "__main__":
    print("🔧 向量化任务测试")
    print("=" * 50)
    
    # 测试文档访问
    doc_test = test_document_access()
    
    # 测试任务导入
    task_test = test_vectorization_task_import()
    
    # 测试节点数据
    sections_data = test_sections_data()
    
    print("\n" + "=" * 50)
    print("📊 测试结果:")
    print(f"文档访问: {'✅' if doc_test else '❌'}")
    print(f"任务导入: {'✅' if task_test else '❌'}")
    print(f"节点数据: {'✅' if sections_data else '❌'}")
    
    if doc_test and task_test and sections_data:
        print("\n🎉 所有测试通过！可以尝试手动执行向量化任务")
        
        # 尝试手动执行向量化任务
        print("\n🚀 尝试手动执行向量化任务...")
        try:
            from backend.tasks.vectorization_tasks import vectorize_document_task
            
            # 直接调用任务函数（不通过Celery）
            result = vectorize_document_task(
                task_id="test_manual",
                user_id="1",
                document_id=1,
                sections_data=sections_data[:5],  # 只测试前5个节点
                embedding_config=None
            )
            print(f"✅ 手动执行成功: {result}")
            
        except Exception as e:
            print(f"❌ 手动执行失败: {e}")
            import traceback
            traceback.print_exc()
    else:
        print("\n❌ 测试失败，请检查问题")
