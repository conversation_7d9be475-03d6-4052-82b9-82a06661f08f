"""
向量化异步任务
"""

import asyncio
from typing import Dict, Any, Optional, List
from celery import current_app
from backend.core.task_queue import celery_app
from backend.tasks.base_task import BaseTask, ProgressCallback
from backend.services.rag_service import rag_service
from backend.services.document_db_service import document_db_service
from backend.core.database import get_db_session
from datetime import datetime, timezone
from loguru import logger


@celery_app.task(bind=True, base=BaseTask)
def vectorize_document_task(
    self,
    task_id: str,
    user_id: str,
    document_id: int,
    sections_data: List[Dict[str, Any]],
    embedding_config: Optional[Dict] = None
):
    """异步向量化文档任务"""
    
    progress_callback = ProgressCallback(task_id, self)
    
    try:
        # 1. 初始化
        progress_callback(5, "开始向量化处理...", "initializing")
        
        # 2. 初始化RAG服务
        progress_callback(10, "初始化RAG服务...", "initializing")
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # 确保RAG服务已初始化
            if not rag_service.query_engine:
                loop.run_until_complete(rag_service.initialize())
            
            # 3. 获取文档信息
            progress_callback(15, "获取文档信息...", "loading")

            db = get_db_session()
            try:
                document = document_db_service.get_document(db, document_id, user_id)
                if not document:
                    raise Exception(f"文档不存在: {document_id}")

                # document 是 ManagedDocument 对象，转换为字典
                document_dict = document.to_dict()
                filename = document_dict.get("filename", "unknown")

            finally:
                db.close()
            
            # 4. 批量向量化处理
            progress_callback(20, "开始向量化处理...", "vectorizing")
            
            total_sections = len(sections_data)
            processed_nodes = 0
            
            # 🔧 优化：分批处理以避免内存问题，减少进度更新频率
            batch_size = 10
            last_progress_update = 0  # 记录上次进度更新的百分比

            for batch_start in range(0, total_sections, batch_size):
                batch_end = min(batch_start + batch_size, total_sections)
                batch_sections = sections_data[batch_start:batch_end]

                # 计算进度
                batch_progress = 20 + (batch_start / total_sections) * 70

                # 🔧 只在进度变化超过5%时更新，减少WebSocket消息频率
                if batch_progress - last_progress_update >= 5 or batch_start == 0:
                    progress_callback(
                        batch_progress,
                        f"向量化进度 {batch_start + len(batch_sections)}/{total_sections} 节点...",
                        "vectorizing"
                    )
                    last_progress_update = batch_progress

                # 处理当前批次（不再为每个节点单独更新进度）
                for i, section in enumerate(batch_sections):
                    
                    # 准备文档内容和元数据
                    content = section.get("content", "")
                    metadata = {
                        "filename": filename,
                        "document_id": document_id,
                        "section_id": section.get("id"),
                        "section_title": section.get("title", ""),
                        "section_type": section.get("section_type", "content"),
                        "user_id": user_id,
                        "created_at": datetime.now(timezone.utc).isoformat()
                    }
                    
                    # 添加到向量库
                    success = loop.run_until_complete(
                        rag_service.add_document(content, metadata)
                    )
                    
                    if success:
                        processed_nodes += 1
                    else:
                        logger.warning(f"向量化失败: 文档 {document_id}, 节点 {section.get('id')}")
            
            # 5. 更新数据库状态
            progress_callback(95, "更新数据库状态...", "saving")
            
            db = get_db_session()
            try:
                # 更新文档状态
                document_db_service.update_document(
                    db, document_id, user_id,
                    {
                        "vectorized": True,
                        "vectorized_at": datetime.now(timezone.utc),
                        "vector_nodes_count": processed_nodes
                    }
                )
                
                # 添加处理日志
                document_db_service.add_processing_log(
                    db, document_id, "INFO",
                    f"向量化完成，生成 {processed_nodes} 个向量节点",
                    "vectorization"
                )
                
                db.commit()
                
            except Exception as e:
                db.rollback()
                raise e
            finally:
                db.close()
            
            # 6. 完成
            progress_callback(100, "向量化完成！", "completed")
            
            return {
                "success": True,
                "document_id": document_id,
                "filename": filename,
                "total_sections": total_sections,
                "processed_nodes": processed_nodes,
                "message": f"向量化完成，生成 {processed_nodes} 个向量节点"
            }
            
        finally:
            loop.close()
        
    except Exception as e:
        error_message = str(e)
        logger.error(f"向量化任务失败: {task_id}, {error_message}")
        progress_callback(0, f"向量化失败: {error_message}", "failed")
        raise e


@celery_app.task(bind=True, base=BaseTask)
def batch_vectorize_documents_task(
    self,
    task_id: str,
    user_id: str,
    document_ids: List[int],
    embedding_config: Optional[Dict] = None
):
    """批量向量化文档任务"""
    
    progress_callback = ProgressCallback(task_id, self)
    
    try:
        progress_callback(5, "开始批量向量化...", "initializing")
        
        total_documents = len(document_ids)
        processed_documents = []
        failed_documents = []
        
        db = get_db_session()
        try:
            for i, document_id in enumerate(document_ids):
                try:
                    # 计算当前文档的进度范围
                    doc_start = 10 + (i * 80 // total_documents)
                    doc_end = 10 + ((i + 1) * 80 // total_documents)
                    
                    # 获取文档节点数据
                    sections = document_db_service.get_document_sections(db, document_id)
                    sections_data = [
                        {
                            "id": section.id,
                            "content": section.content,
                            "title": section.title,
                            "section_type": section.section_type
                        }
                        for section in sections
                    ]
                    
                    if not sections_data:
                        failed_documents.append({
                            "document_id": document_id,
                            "error": "文档没有可向量化的节点",
                            "success": False
                        })
                        continue
                    
                    progress_callback(
                        doc_start,
                        f"向量化文档 {i+1}/{total_documents} (ID: {document_id})...",
                        "vectorizing"
                    )
                    
                    # 向量化单个文档
                    result = vectorize_document_task.apply(
                        args=[
                            f"{task_id}_doc_{document_id}",
                            user_id,
                            document_id,
                            sections_data,
                            embedding_config
                        ]
                    ).get()
                    
                    processed_documents.append({
                        "document_id": document_id,
                        "filename": result['filename'],
                        "processed_nodes": result['processed_nodes'],
                        "success": True
                    })
                    
                    progress_callback(doc_end, f"完成文档 {document_id}", "vectorizing")
                    
                except Exception as e:
                    failed_documents.append({
                        "document_id": document_id,
                        "error": str(e),
                        "success": False
                    })
                    
                    logger.error(f"批量向量化中文档失败: {document_id}, {e}")
        
        finally:
            db.close()
        
        # 完成
        progress_callback(100, "批量向量化完成！", "completed")
        
        return {
            "success": True,
            "total_documents": total_documents,
            "processed_count": len(processed_documents),
            "failed_count": len(failed_documents),
            "processed_documents": processed_documents,
            "failed_documents": failed_documents,
            "message": f"批量向量化完成，成功 {len(processed_documents)} 个，失败 {len(failed_documents)} 个"
        }
        
    except Exception as e:
        error_message = str(e)
        logger.error(f"批量向量化任务失败: {task_id}, {error_message}")
        progress_callback(0, f"批量向量化失败: {error_message}", "failed")
        raise e


@celery_app.task(bind=True, base=BaseTask)
def rebuild_vector_index_task(
    self,
    task_id: str,
    user_id: str,
    force_rebuild: bool = False
):
    """重建向量索引任务"""
    
    progress_callback = ProgressCallback(task_id, self)
    
    try:
        progress_callback(5, "开始重建向量索引...", "initializing")
        
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            # 重建索引
            progress_callback(20, "重建向量索引...", "rebuilding")
            
            # 这里可以添加具体的索引重建逻辑
            # 例如：清空现有索引，重新加载所有文档等
            
            progress_callback(100, "向量索引重建完成！", "completed")
            
            return {
                "success": True,
                "message": "向量索引重建完成"
            }
            
        finally:
            loop.close()
        
    except Exception as e:
        error_message = str(e)
        logger.error(f"重建向量索引任务失败: {task_id}, {error_message}")
        progress_callback(0, f"重建索引失败: {error_message}", "failed")
        raise e
