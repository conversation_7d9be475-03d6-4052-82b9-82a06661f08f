import{_ as n,g as i,h as r,o as d,r as c,a as _,c as u,b as e,d as p,w as v,t as o,f as m}from"./index-2c134546.js";const f={class:"profile-container"},h={class:"page-header"},x={class:"header-content"},b={class:"header-actions"},g={class:"page-main"},k={class:"container"},N={class:"profile-card"},y={class:"profile-header"},B={__name:"Profile",setup(P){const l=i(),a=r({name:"",email:""});return d(()=>{a.value={name:l.userName||"用户",email:l.userEmail||"<EMAIL>"},console.log("👤 个人资料页面加载完成")}),(S,s)=>{const t=c("router-link");return _(),u("div",f,[e("div",h,[e("div",x,[s[1]||(s[1]=e("div",{class:"header-brand"},[e("div",{class:"brand-logo"},"👤"),e("span",{class:"brand-text"},"个人资料")],-1)),e("div",b,[p(t,{to:"/dashboard",class:"btn-outline"},{default:v(()=>s[0]||(s[0]=[m("返回控制台")])),_:1,__:[0]})])])]),e("div",g,[e("div",k,[e("div",N,[e("div",y,[s[2]||(s[2]=e("div",{class:"avatar"},[e("div",{class:"avatar-icon"},"👤")],-1)),e("h2",null,o(a.value.name||"用户"),1),e("p",null,o(a.value.email||"<EMAIL>"),1)]),s[3]||(s[3]=e("div",{class:"profile-content"},[e("h3",null,"🚧 功能开发中"),e("p",null,"个人资料管理功能正在开发中，敬请期待！"),e("p",null,"预计功能包括："),e("ul",null,[e("li",null,"个人信息编辑"),e("li",null,"头像上传"),e("li",null,"密码修改"),e("li",null,"偏好设置"),e("li",null,"使用统计")])],-1))])])])])}}},w=n(B,[["__scopeId","data-v-ca47bc05"]]);export{w as default};
