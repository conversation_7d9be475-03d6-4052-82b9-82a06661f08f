#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Unicode字符批量修复脚本
用于修复backend/utils/audio/speech_recognition_utils.py中的Unicode字符
"""

import re
import os
from pathlib import Path

def fix_runtime_unicode_characters():
    """批量修复运行时输出的Unicode字符（logger、progress_callback等）"""

    # 目标文件路径
    file_path = Path("backend/tasks/audio_processing_tasks.py")

    if not file_path.exists():
        print(f"错误：文件不存在 {file_path}")
        return False

    # 运行时输出的Unicode字符替换映射（只修复logger和progress_callback中的中文）
    runtime_unicode_replacements = {
        # Logger输出信息
        "文件不存在": "File not found",
        "VAD模型加载失败": "VAD model loading failed",
        "VAD检测失败": "VAD detection failed",
        "任务失败": "Task failed",
        "初始内存使用": "Initial memory usage",
        "CUDA设备信息": "CUDA device info",
        "CUDA初始化警告": "CUDA initialization warning",
        "将使用CPU模式": "will use CPU mode",
        "优化管理器初始化后内存使用": "Memory usage after optimizer initialization",
        "优化FunASR管理器初始化失败": "Optimized FunASR manager initialization failed",
        "内存使用较高": "High memory usage",
        "执行温和的内存清理": "Performing gentle memory cleanup",
        "语音识别失败": "Speech recognition failed",
        "错误": "error",
        "为文件": "For file",
        "创建ProcessingResult记录": "create ProcessingResult record",
        "未找到文件ID": "File ID not found",
        "对应的AudioFile记录": "corresponding AudioFile record",
        "成功保存": "Successfully saved",
        "个ProcessingResult记录": "ProcessingResult records",
        "保存ProcessingResult失败": "Failed to save ProcessingResult",
        "数据库操作失败": "Database operation failed",
        "完成内存使用": "Final memory usage",
        "语音识别任务失败": "Speech recognition task failed",
        "优化FunASR管理器资源清理完成": "Optimized FunASR manager resource cleanup completed",
        "优化FunASR管理器清理失败": "Optimized FunASR manager cleanup failed",
        "识别器资源清理完成": "Recognizer resource cleanup completed",
        "识别器清理失败": "Recognizer cleanup failed",
        "CUDA清理失败": "CUDA cleanup failed",
        "资源清理完成": "Resource cleanup completed",
        "资源清理过程中发生错误": "Error occurred during resource cleanup",

        # 说话人识别相关
        "说话人识别模型加载失败": "Speaker recognition model loading failed",
        "说话人识别失败": "Speaker recognition failed",
        "数据已转换为JSON兼容格式": "Data converted to JSON compatible format",
        "数据转换失败，使用原始数据": "Data conversion failed, using original data",
        "说话人识别结果已保存到数据库": "Speaker recognition results saved to database",
        "任务ID": "Task ID",
        "保存识别结果到数据库失败": "Failed to save recognition results to database",
        "已转换numpy类型，避免JSON序列化错误": "Converted numpy types to avoid JSON serialization errors",
        "数据类型转换失败": "Data type conversion failed",
        "任务状态已更新为完成": "Task status updated to completed",
        "WebSocket完成通知已发送": "WebSocket completion notification sent",
        "WebSocket完成通知发送失败": "WebSocket completion notification failed",
        "WebSocket通知线程启动失败": "WebSocket notification thread startup failed",
        "文件状态已更新为完成": "File status updated to completed",
        "所有音频文件状态已更新为完成": "All audio file statuses updated to completed",
        "更新音频文件状态失败": "Failed to update audio file status",
        "音频文件状态更新失败": "Audio file status update failed",
        "任务完成处理失败": "Task completion processing failed",
        "说话人识别任务完全完成": "Speaker recognition task fully completed",
        "说话人识别任务失败": "Speaker recognition task failed",
        "用户ID": "User ID",
        "文件IDs": "File IDs",
        "已更新文件": "Updated file",
        "状态为错误": "status to error",
        "已更新数据库中的文件状态": "Updated file status in database",
        "更新数据库状态失败": "Failed to update database status",
        "更新任务状态失败": "Failed to update task status",
        "进度回调失败": "Progress callback failed",
        "加载CAM++说话人验证模型，使用正确的调用方式": "Load CAM++ speaker verification model using correct calling method",
        "正在加载CAM++模型(设备": "Loading CAM++ model (device",
        "检查模型路径是否存在": "Check if model path exists",
        "使用本地模型路径": "Use local model path",
        "使用本地CAM++模型": "Use local CAM++ model",
        "使用轻量化的在线模型ID，避免下载大模型": "Use lightweight online model ID to avoid downloading large models",
        "最简模型ID": "Simplest model ID",
        "轻量版本": "Lightweight version",
        "标准版本": "Standard version",
        "本地模型路径无效，将尝试轻量化在线模型": "Local model path invalid, will try lightweight online model",
        "返回第一个轻量化模型，后续会依次尝试": "Return first lightweight model, will try others sequentially",
        "加载CAM++模型配置失败": "Failed to load CAM++ model configuration",

        # 状态检查
        "检查CAM++模型的状态和可用性": "Check CAM++ model status and availability",
        "模型为None": "Model is None",
        "检查基本属性": "Check basic attributes",
        "检查内部模型": "Check internal model",
        "尝试检查模型是否在正确的设备上": "Try to check if model is on correct device",
        "检查模型状态失败": "Failed to check model status",

        # 语音模型加载
        "优化语音模型加载函数": "Optimize speech model loading function",
        "修复": "Fix",
        "错误": "error",
        "加载语音识别模型，修复SenseVoice": "Load speech recognition model, fix SenseVoice",
        "基于GitHub issues": "Based on GitHub issues",
        "的解决方案": "solution",
        "正在加载": "Loading",
        "模型(设备": "model (device",
        "设置离线模式": "Set offline mode",
        "不支持的模型类型": "Unsupported model type",
        "目前只支持SenseVoice": "Currently only supports SenseVoice",
        "加载": "Load",
        "模型失败": "model failed",
        "详细错误信息": "Detailed error information",

        # 删除和修复标记
        "删除了Paraformer相关代码，专注于SenseVoice和CAM++优化": "Removed Paraformer related code, focus on SenseVoice and CAM++ optimization",
        "修复SenseVoice模型加载": "Fix SenseVoice model loading",
        "使用优化的FunASR管理器": "Use optimized FunASR manager",
        "离线模式：必须提供有效的本地SenseVoice模型路径": "Offline mode: must provide valid local SenseVoice model path",
        "使用本地SenseVoice模型（优化版）": "Use local SenseVoice model (optimized version)",
        "优先使用优化的FunASR管理器": "Prioritize optimized FunASR manager",
        "使用优化的FunASR管理器加载模型": "Use optimized FunASR manager to load model",
        "优化FunASR管理器加载成功": "Optimized FunASR manager loaded successfully",
        "优化FunASR管理器加载失败，回退到原始方式": "Optimized FunASR manager loading failed, fallback to original method",
        "优化FunASR管理器异常": "Optimized FunASR manager exception",
        "回退到原始方式": "Fallback to original method",
        "回退到原始加载方式": "Fallback to original loading method",
        "使用原始方式加载SenseVoice模型": "Use original method to load SenseVoice model",

        # 文件检查
        "检查关键文件": "Check key files",
        "模型文件检查": "Model file check",
        "存在": "exists",
        "方案1：使用trust_remote_code": "Solution 1: use trust_remote_code",
        "尝试方案1": "Try solution 1",
        "禁用VAD避免额外下载": "Disable VAD to avoid extra downloads",
        "如果有model.py，使用它": "If model.py exists, use it",
        "使用本地model.py": "Use local model.py",
        "方案1成功": "Solution 1 successful",
        "SenseVoice模型加载完成（原始方式）": "SenseVoice model loading completed (original method)",
        "测试模型是否正常工作": "Test if model works normally",
        "模型具有generate方法": "Model has generate method",
        "模型缺少generate方法": "Model lacks generate method",
        "方案1失败": "Solution 1 failed",
        "方案2：简化配置": "Solution 2: simplified configuration",
        "尝试方案2": "Try solution 2",
        "方案2成功": "Solution 2 successful",
        "方案2失败": "Solution 2 failed",
        "方案3：最小配置": "Solution 3: minimal configuration",
        "尝试方案3": "Try solution 3",
        "方案3成功": "Solution 3 successful",
        "方案3失败": "Solution 3 failed",
        "所有加载方案均失败": "All loading solutions failed",
        "最终建议": "Final suggestions",
        "SenseVoice解决方案": "SenseVoice solution",
        "确保模型目录包含": "Ensure model directory contains",
        "和": "and",
        "文件": "files",
        "检查模型文件是否完整下载": "Check if model files are completely downloaded",
        "或运行": "or run",
        "脚本进行诊断": "script for diagnosis",
        "SenseVoice模型加载过程失败": "SenseVoice model loading process failed",

        # 配置相关
        "简化的离线AutoModel配置": "Simplified offline AutoModel configuration",
        "创建完全离线的AutoModel配置": "Create completely offline AutoModel configuration",
        "中提到的最佳实践": "mentioned best practices",
        "关键：只使用本地文件": "Key: only use local files",
        "禁用更新检查": "Disable update check",
        "禁用强制下载": "Disable forced download",
        "不指定版本避免网络检查": "Don't specify version to avoid network check",
        "删除冗余的模型加载函数，保留核心功能": "Remove redundant model loading functions, keep core functionality",
        "移除": "Remove",
        "冗余": "redundant",
        "保留": "Keep",
        "主要的": "main",
        "函数": "function",
        "优化VAD和说话人模型加载，统一离线配置": "Optimize VAD and speaker model loading, unified offline configuration",
        "加载VAD模型": "Load VAD model",
        "优化版本": "Optimized version",
        "离线模式：VAD模型需要有效的本地路径": "Offline mode: VAD model needs valid local path",
        "正在加载VAD模型(设备": "Loading VAD model (device",
        "VAD模型加载成功": "VAD model loaded successfully",
        "加载VAD模型失败": "Failed to load VAD model",
        "加载XVector说话人识别模型": "Load XVector speaker recognition model",
        "离线模式：XVector模型需要有效的本地路径": "Offline mode: XVector model needs valid local path",
        "正在加载XVector模型(设备": "Loading XVector model (device",
        "XVector模型加载成功": "XVector model loaded successfully",
        "加载XVector模型失败": "Failed to load XVector model",

        # 基础功能
        "基础语音识别函数": "Basic speech recognition function",
        "基础语音识别已禁用（避免网络请求）": "Basic speech recognition disabled (avoid network requests)",
        "基础语音识别已禁用，避免网络请求。请使用本地SenseVoice模型。": "Basic speech recognition disabled to avoid network requests. Please use local SenseVoice model.",
        "基础语音识别已禁用": "Basic speech recognition disabled",
        "避免网络请求": "Avoid network requests",
        "SenseVoice语音识别函数": "SenseVoice speech recognition function",
        "使用SenseVoice模型进行语音识别": "Use SenseVoice model for speech recognition",
        "使用funasr进行推理": "Use funasr for inference",
        "处理结果": "Process results",
        "使用富文本后处理": "Use rich text post-processing",
        "提取情感和事件信息（如果有）": "Extract emotion and event information (if any)",
        "尝试从结果中解析情感和事件": "Try to parse emotions and events from results",
        "保存原始结果以备后续处理": "Save original results for subsequent processing",
        "未能识别出文本": "Failed to recognize text",
        "模型未返回结果": "Model returned no results",
        "语音识别失败": "Speech recognition failed",
        "删除了Paraformer识别函数，专注于SenseVoice": "Removed Paraformer recognition function, focus on SenseVoice",

        # VAD相关
        "VAD分割函数": "VAD segmentation function",
        "使用VAD模型分割音频": "Use VAD model to segment audio",
        "使用funasr进行VAD分割": "Use funasr for VAD segmentation",
        "返回VAD分割结果": "Return VAD segmentation results",
        "VAD分割失败": "VAD segmentation failed",

        # 说话人识别
        "提取说话人嵌入向量": "Extract speaker embedding vectors",
        "使用XVector模型提取说话人嵌入向量": "Use XVector model to extract speaker embedding vectors",
        "使用funasr提取说话人嵌入向量": "Use funasr to extract speaker embedding vectors",
        "返回嵌入向量": "Return embedding vectors",
        "提取说话人嵌入向量失败": "Failed to extract speaker embedding vectors",
        "说话人聚类函数": "Speaker clustering function",
        "对说话人嵌入向量进行聚类": "Cluster speaker embedding vectors",
        "如果未指定说话人数量，尝试自动确定": "If speaker count not specified, try to determine automatically",
        "使用层次聚类，距离阈值自动确定聚类数量": "Use hierarchical clustering, distance threshold automatically determines cluster count",
        "使用指定的说话人数量进行聚类": "Use specified speaker count for clustering",

        # 模型重新初始化
        "重新初始化CAM++模型（解决线程安全问题）": "Reinitialize CAM++ model (solve thread safety issues)",
        "重新初始化CAM++模型，解决可能的线程安全问题": "Reinitialize CAM++ model to solve possible thread safety issues",
        "尝试重新初始化CAM++模型": "Try to reinitialize CAM++ model",
        "获取原始模型的配置": "Get original model configuration",
        "尝试从session_state获取": "Try to get from session_state",
        "确定设备": "Determine device",
        "强制使用CPU设备": "Force use CPU device",
        "使用设备": "Use device",
        "清理GPU缓存": "Clear GPU cache",
        "已清理GPU缓存": "GPU cache cleared",
        "重新加载模型": "Reload model",
        "验证新模型": "Validate new model",
        "CAM++模型重新初始化成功": "CAM++ model reinitialization successful",
        "重新初始化的模型缺少generate方法": "Reinitialized model lacks generate method",
        "CAM++模型重新初始化失败": "CAM++ model reinitialization failed",
    }

    print(f"开始修复文件: {file_path}")

    try:
        # 读取文件内容
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 记录替换次数
        total_replacements = 0

        # 执行替换
        for chinese, english in runtime_unicode_replacements.items():
            if chinese in content:
                content = content.replace(chinese, english)
                count = content.count(english) - content.count(chinese)
                if count > 0:
                    total_replacements += count
                    print(f"替换: '{chinese}' -> '{english}' ({count}次)")

        # 写回文件
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)

        print(f"修复完成！总共替换了 {total_replacements} 处Unicode字符")
        return True

    except Exception as e:
        print(f"修复失败: {e}")
        return False

if __name__ == "__main__":
    fix_runtime_unicode_characters()