[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[ ] NAME:阶段1：数据模型扩展和API基础 DESCRIPTION:建立知识库管理的数据基础和核心API，包括数据模型扩展、ChromaDB多知识库支持、基础API实现。预估工期：3-4天
--[ ] NAME:扩展数据模型定义 DESCRIPTION:在ManagedDocument模型中添加knowledge_base_id外键字段，扩展KnowledgeBase模型添加统计字段。修改文件：backend/models/document_management.py, backend/models/conversation.py
--[ ] NAME:创建数据库迁移脚本 DESCRIPTION:创建数据库迁移脚本，为现有数据添加默认知识库，确保数据完整性。新增文件：backend/migrations/add_knowledge_base_relation.py
--[ ] NAME:实现知识库数据模型Schema DESCRIPTION:创建Pydantic数据模型，定义知识库的请求和响应格式。新增文件：backend/schemas/knowledge_base.py
--[ ] NAME:实现知识库服务层 DESCRIPTION:创建知识库业务逻辑服务，包括CRUD操作、统计信息计算等。新增文件：backend/services/knowledge_base_service.py
--[ ] NAME:改造ChromaDB多知识库支持 DESCRIPTION:修改RAG服务支持多知识库，实现collection_name动态命名策略。修改文件：backend/services/rag_service.py
--[ ] NAME:实现知识库管理API端点 DESCRIPTION:创建知识库管理的RESTful API，包括增删改查操作。新增文件：backend/api/v1/endpoints/knowledge_base_manager.py
--[ ] NAME:注册API路由 DESCRIPTION:将知识库管理API注册到主路由中。修改文件：backend/api/v1/api.py
--[ ] NAME:创建前端知识库API接口 DESCRIPTION:创建前端调用知识库管理API的接口函数。新增文件：frontend/src/api/knowledgeBase.js
--[ ] NAME:编写单元测试 DESCRIPTION:为知识库服务层和API端点编写单元测试。新增文件：backend/tests/test_knowledge_base_service.py, backend/tests/test_knowledge_base_api.py
--[ ] NAME:执行数据库迁移和验证 DESCRIPTION:运行数据库迁移脚本，验证数据模型扩展正确性，测试API基础功能。
-[ ] NAME:阶段2：知识库管理页面 DESCRIPTION:实现独立的知识库管理界面，包括知识库列表、创建、删除、统计信息展示等功能。预估工期：2-3天
--[ ] NAME:创建知识库管理主页面 DESCRIPTION:创建知识库管理的主页面组件，包括页面布局和基础结构。新增文件：frontend/src/views/KnowledgeBaseManager.vue
--[ ] NAME:实现知识库卡片组件 DESCRIPTION:创建知识库展示卡片组件，包括知识库信息、统计数据、操作按钮。新增文件：frontend/src/components/knowledge/KnowledgeBaseCard.vue
--[ ] NAME:实现创建知识库对话框 DESCRIPTION:创建知识库创建对话框组件，包括表单验证和提交逻辑。新增文件：frontend/src/components/knowledge/CreateKnowledgeBaseDialog.vue
--[ ] NAME:实现删除确认对话框 DESCRIPTION:创建知识库删除确认对话框，防止误删操作。新增文件：frontend/src/components/knowledge/DeleteConfirmDialog.vue
--[ ] NAME:添加路由配置 DESCRIPTION:在路由配置中添加知识库管理页面路由。修改文件：frontend/src/router/index.js
--[ ] NAME:更新仪表板导航 DESCRIPTION:在Dashboard页面添加知识库管理入口卡片。修改文件：frontend/src/views/Dashboard.vue
--[ ] NAME:集成WebSocket实时更新 DESCRIPTION:在知识库管理页面集成WebSocket，实现知识库状态实时同步。修改文件：frontend/src/views/KnowledgeBaseManager.vue
--[ ] NAME:实现知识库统计信息展示 DESCRIPTION:实现知识库文档数量、创建时间等统计信息的实时展示。修改文件：frontend/src/components/knowledge/KnowledgeBaseCard.vue
--[ ] NAME:添加页面样式和交互优化 DESCRIPTION:为知识库管理页面添加统一的Element Plus样式，优化用户交互体验。
--[ ] NAME:页面功能测试和验证 DESCRIPTION:测试知识库管理页面的所有功能，包括创建、删除、统计信息展示等。
-[ ] NAME:阶段3：问答页面知识库选择功能 DESCRIPTION:在RAG问答页面添加多知识库选择器，支持多知识库检索和问答。预估工期：2天
--[ ] NAME:扩展RAG问答页面布局 DESCRIPTION:在KnowledgeBase.vue左侧配置栏添加知识库选择器区域。修改文件：frontend/src/views/KnowledgeBase.vue
--[ ] NAME:实现知识库列表组件 DESCRIPTION:创建知识库列表组件，支持多选和Switch开关控制。新增文件：frontend/src/components/knowledge/KnowledgeBaseSelector.vue
--[ ] NAME:扩展知识库查询API DESCRIPTION:修改知识库查询API支持多知识库参数。修改文件：backend/api/v1/endpoints/knowledge.py
--[ ] NAME:实现多知识库检索逻辑 DESCRIPTION:在RAG服务中实现多知识库检索和结果合并逻辑。修改文件：backend/services/rag_service.py
--[ ] NAME:更新前端知识库API调用 DESCRIPTION:修改前端知识库API调用，支持多知识库查询参数。修改文件：frontend/src/api/knowledge.js
--[ ] NAME:实现知识库选择状态管理 DESCRIPTION:在问答页面实现知识库选择状态的管理和持久化。修改文件：frontend/src/views/KnowledgeBase.vue
--[ ] NAME:实现实时知识库列表更新 DESCRIPTION:集成WebSocket实现知识库列表的实时更新。修改文件：frontend/src/components/knowledge/KnowledgeBaseSelector.vue
--[ ] NAME:优化查询结果展示 DESCRIPTION:优化问答结果展示，显示来源知识库信息。修改文件：frontend/src/views/KnowledgeBase.vue
--[ ] NAME:测试多知识库问答功能 DESCRIPTION:测试多知识库选择和问答功能，验证检索结果的准确性。
-[ ] NAME:阶段4：文档管理页面知识库归属 DESCRIPTION:在文档管理页面添加知识库选择和归属功能，支持文档与知识库关联。预估工期：2天
--[ ] NAME:扩展文档管理页面布局 DESCRIPTION:在DocumentManager.vue左侧区域添加知识库选择器。修改文件：frontend/src/views/DocumentManager.vue
--[ ] NAME:实现知识库下拉选择组件 DESCRIPTION:创建知识库下拉选择组件，支持单选模式。新增文件：frontend/src/components/knowledge/KnowledgeBaseDropdown.vue
--[ ] NAME:扩展文档上传API DESCRIPTION:修改文档上传API支持知识库ID参数。修改文件：backend/api/v1/endpoints/document_manager.py
--[ ] NAME:更新文档服务层 DESCRIPTION:修改文档服务层，支持文档与知识库的关联逻辑。修改文件：backend/services/document_service.py
--[ ] NAME:实现文档知识库归属显示 DESCRIPTION:在文档列表中显示文档所属的知识库信息。修改文件：frontend/src/views/DocumentManager.vue
--[ ] NAME:实现文档知识库筛选 DESCRIPTION:实现按知识库筛选文档列表的功能。修改文件：frontend/src/views/DocumentManager.vue
--[ ] NAME:实现文档移动功能 DESCRIPTION:实现文档在不同知识库间移动的功能。新增文件：frontend/src/components/document/MoveDocumentDialog.vue
--[ ] NAME:更新前端文档API调用 DESCRIPTION:修改前端文档API调用，支持知识库关联参数。修改文件：frontend/src/api/document.js
--[ ] NAME:测试文档知识库关联功能 DESCRIPTION:测试文档上传、归属、移动等与知识库相关的所有功能。