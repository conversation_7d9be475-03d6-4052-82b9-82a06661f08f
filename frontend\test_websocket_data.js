/**
 * 测试WebSocket数据转换函数
 */

// 模拟convertWebSocketDataToProgress函数
const convertWebSocketDataToProgress = (payload) => {
  console.log('🔄 转换WebSocket payload:', payload)

  let percentage = 0
  let detail = '处理中...'
  let status = 'pending'

  // 从payload中提取实际的任务数据
  // payload格式: { task_id: 'xxx', progress: {...} } 或 { task_id: 'xxx', status: {...} }
  const taskData = payload.progress || payload.status || payload

  // 安全地获取状态信息
  const taskStatus = taskData.state || taskData.status || 'PENDING'
  
  // 根据任务状态确定进度
  if (taskStatus === 'PENDING') {
    percentage = 5
    detail = '等待处理...'
    status = 'pending'
  } else if (taskStatus === 'PROGRESS') {
    // 如果有具体的进度信息
    if (taskData.progress && typeof taskData.progress === 'object') {
      percentage = taskData.progress.percentage || taskData.progress.current || 20
      detail = taskData.progress.detail || taskData.progress.stage || '处理中...'
    } else if (taskData.percentage !== undefined) {
      percentage = taskData.percentage
      detail = taskData.detail || '处理中...'
    } else {
      percentage = 20
      detail = '正在处理...'
    }
    status = 'progress'
  } else if (taskStatus === 'SUCCESS') {
    percentage = 100
    detail = '处理完成'
    status = 'completed'
  } else if (taskStatus === 'FAILURE') {
    percentage = 0
    detail = '处理失败'
    status = 'failed'
  } else if (taskStatus === 'RETRY') {
    percentage = 10
    detail = '重试中...'
    status = 'retry'
  }

  const result = {
    percentage: percentage,
    detail: detail,
    status: status,
    ready: taskData.ready,
    successful: taskData.successful,
    failed: taskData.failed,
    error_message: taskData.error || taskData.traceback || taskData.error_message,
    task_id: payload.task_id
  }
  
  console.log('✅ 转换后的进度数据:', result)
  return result
}

// 测试用例
console.log('=== WebSocket数据转换测试 ===\n')

// 测试用例1：后端实际格式 - 有status对象
console.log('测试用例1：后端实际格式 - 有status对象')
const testData1 = {
  task_id: 'test-123',
  status: {
    state: 'PROGRESS',
    progress: {
      percentage: 45,
      detail: '正在处理文档...'
    }
  }
}
convertWebSocketDataToProgress(testData1)

console.log('\n测试用例2：后端实际格式 - 有progress对象')
const testData2 = {
  task_id: 'test-456',
  progress: {
    state: 'PROGRESS',
    percentage: 60,
    detail: '文档分析中...'
  }
}
convertWebSocketDataToProgress(testData2)

console.log('\n测试用例3：简化格式')
const testData3 = {
  task_id: 'test-789',
  state: 'PROGRESS',
  percentage: 75,
  detail: 'OCR处理中...'
}
convertWebSocketDataToProgress(testData3)

console.log('\n测试用例4：完成状态')
const testData4 = {
  task_id: 'test-999',
  status: {
    state: 'SUCCESS',
    successful: true,
    ready: true
  }
}
convertWebSocketDataToProgress(testData4)

console.log('\n测试用例5：失败状态')
const testData5 = {
  task_id: 'test-error',
  status: {
    state: 'FAILURE',
    failed: true,
    error: '处理失败：文件格式不支持'
  }
}
convertWebSocketDataToProgress(testData5)

console.log('\n=== 测试完成 ===')
