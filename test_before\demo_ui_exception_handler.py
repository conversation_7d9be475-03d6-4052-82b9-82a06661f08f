#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI异常处理器演示程序
展示友好的异常提示和错误消息功能
"""

import streamlit as st
import sys
import os
from pathlib import Path

# 添加utils路径
sys.path.append("utils")

# 导入UI异常处理器
try:
    from utils.ui_exception_handler import (
        UIExceptionHandler,
        ui_exception_handler,
        streamlit_exception_handler,
        show_error,
        show_success,
        show_warning,
        show_info
    )
    from utils.exception_handler import (
        AudioProcessingError,
        ModelLoadingError,
        FileOperationError,
        MemoryError,
        GPUError,
        ValidationError,
        ConfigurationError,
        ErrorSeverity,
        ErrorCategory
    )
    IMPORTS_SUCCESSFUL = True
except ImportError as e:
    st.error(f"❌ 导入失败: {e}")
    IMPORTS_SUCCESSFUL = False

# 页面配置
st.set_page_config(
    page_title="UI异常处理器演示",
    page_icon="🎛️",
    layout="wide"
)

def demo_basic_messages():
    """演示基本消息类型"""
    st.header("📢 基本消息类型演示")
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        if st.button("✅ 成功消息", use_container_width=True):
            show_success(
                "操作成功完成！",
                "所有音频文件已成功处理，结果已保存到指定目录。"
            )
    
    with col2:
        if st.button("⚠️ 警告消息", use_container_width=True):
            show_warning(
                "检测到潜在问题",
                "音频文件质量较低，可能影响识别精度。建议使用高质量音频文件。"
            )
    
    with col3:
        if st.button("ℹ️ 信息消息", use_container_width=True):
            show_info(
                "处理进度更新",
                "当前正在处理第3个文件，共5个文件。预计剩余时间：2分钟。"
            )
    
    with col4:
        if st.button("❌ 错误消息", use_container_width=True):
            test_error = AudioProcessingError(
                "音频文件格式不支持",
                severity=ErrorSeverity.HIGH,
                suggestion="请使用WAV、MP3或FLAC格式的音频文件"
            )
            show_error(test_error, context={"file": "demo.mp4"}, show_details=True)

def demo_exception_types():
    """演示不同类型的异常"""
    st.header("🚨 异常类型演示")
    
    exception_demos = {
        "音频处理错误": {
            "exception": AudioProcessingError(
                "音频文件损坏或格式不正确",
                severity=ErrorSeverity.HIGH,
                suggestion="请检查音频文件完整性，或尝试使用其他音频文件"
            ),
            "context": {"file": "corrupted_audio.wav", "size": "2.5MB"}
        },
        "模型加载错误": {
            "exception": ModelLoadingError(
                "VAD模型文件未找到",
                severity=ErrorSeverity.CRITICAL,
                suggestion="请检查模型文件路径，或重新下载模型文件"
            ),
            "context": {"model": "silero_vad", "path": "models/"}
        },
        "文件操作错误": {
            "exception": FileOperationError(
                "磁盘空间不足",
                severity=ErrorSeverity.HIGH,
                suggestion="请清理磁盘空间，或选择其他保存位置"
            ),
            "context": {"operation": "save_result", "required_space": "500MB"}
        },
        "内存错误": {
            "exception": MemoryError(
                "系统内存不足",
                severity=ErrorSeverity.MEDIUM,
                suggestion="请关闭其他应用程序，或减少批处理文件数量"
            ),
            "context": {"required_memory": "2GB", "available_memory": "512MB"}
        },
        "GPU错误": {
            "exception": GPUError(
                "GPU驱动程序版本过低",
                severity=ErrorSeverity.MEDIUM,
                suggestion="请更新显卡驱动程序，或切换到CPU模式"
            ),
            "context": {"gpu": "NVIDIA GTX 1060", "driver_version": "461.40"}
        },
        "验证错误": {
            "exception": ValidationError(
                "音频采样率不符合要求",
                severity=ErrorSeverity.LOW,
                suggestion="模型要求音频采样率为16kHz，请转换音频格式"
            ),
            "context": {"expected_sr": 16000, "actual_sr": 44100}
        }
    }
    
    selected_demo = st.selectbox(
        "选择要演示的异常类型",
        options=list(exception_demos.keys())
    )
    
    if st.button(f"演示 {selected_demo}", use_container_width=True):
        demo_data = exception_demos[selected_demo]
        show_error(
            demo_data["exception"],
            context=demo_data["context"],
            show_details=True
        )
        
        # 显示恢复选项
        ui_exception_handler.create_error_recovery_widget(demo_data["exception"])

def demo_decorator_functionality():
    """演示装饰器功能"""
    st.header("🎭 装饰器功能演示")
    
    st.markdown("""
    装饰器可以自动捕获函数中的异常并显示友好的错误消息。
    下面的示例展示了不同类型的装饰器配置：
    """)
    
    @streamlit_exception_handler(show_details=True, show_traceback=True, show_recovery=True)
    def demo_function_with_custom_error():
        """演示自定义异常的函数"""
        raise AudioProcessingError(
            "演示：音频文件处理失败",
            severity=ErrorSeverity.MEDIUM,
            suggestion="这是一个装饰器捕获的自定义异常示例"
        )
    
    @streamlit_exception_handler(show_details=False, show_recovery=False)
    def demo_function_with_standard_error():
        """演示标准异常的函数"""
        raise ValueError("这是一个标准的Python ValueError异常")
    
    @streamlit_exception_handler(show_details=True, show_traceback=False)
    def demo_function_with_file_error():
        """演示文件操作异常的函数"""
        raise FileNotFoundError("找不到指定的音频文件：demo_audio.wav")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("自定义异常 + 完整信息"):
            demo_function_with_custom_error()
    
    with col2:
        if st.button("标准异常 + 简洁信息"):
            demo_function_with_standard_error()
    
    with col3:
        if st.button("文件异常 + 中等信息"):
            demo_function_with_file_error()

def demo_severity_levels():
    """演示错误严重级别"""
    st.header("📊 错误严重级别演示")
    
    severity_demos = {
        "🚨 严重错误 (CRITICAL)": AudioProcessingError(
            "系统核心组件初始化失败",
            severity=ErrorSeverity.CRITICAL,
            suggestion="请重启应用程序，如果问题持续存在请联系技术支持"
        ),
        "❌ 高级错误 (HIGH)": ModelLoadingError(
            "语音识别模型加载失败",
            severity=ErrorSeverity.HIGH,
            suggestion="请检查模型文件完整性，或重新下载模型"
        ),
        "⚠️ 中级错误 (MEDIUM)": GPUError(
            "GPU内存不足，已切换到CPU模式",
            severity=ErrorSeverity.MEDIUM,
            suggestion="处理速度可能较慢，建议减少批处理文件数量"
        ),
        "ℹ️ 低级错误 (LOW)": ValidationError(
            "音频文件时长较短",
            severity=ErrorSeverity.LOW,
            suggestion="建议使用时长超过1秒的音频文件以获得更好效果"
        )
    }
    
    for severity_name, exception in severity_demos.items():
        if st.button(severity_name, use_container_width=True):
            show_error(
                exception,
                context={"demo": "severity_level", "severity": exception.severity.value},
                show_details=True
            )

def demo_feedback_system():
    """演示反馈系统"""
    st.header("📝 用户反馈系统演示")
    
    st.markdown("""
    当出现错误时，用户可以提供反馈来帮助改进错误消息。
    点击下面的按钮来模拟一个错误，然后尝试使用反馈系统：
    """)
    
    if st.button("触发错误并显示反馈表单", use_container_width=True):
        # 显示一个错误
        test_error = AudioProcessingError(
            "这是一个演示错误，用于测试反馈系统",
            severity=ErrorSeverity.MEDIUM,
            suggestion="请在下方的反馈表单中告诉我们您的意见"
        )
        show_error(test_error, show_details=True)
        
        # 显示反馈表单
        ui_exception_handler.create_error_feedback_form()

def demo_error_recovery():
    """演示错误恢复功能"""
    st.header("🔧 错误恢复功能演示")
    
    st.markdown("""
    某些错误是可以自动恢复的。下面演示可恢复的错误类型：
    """)
    
    recoverable_error = FileOperationError(
        "临时文件创建失败",
        severity=ErrorSeverity.MEDIUM,
        suggestion="系统可以尝试在其他位置创建临时文件",
        recoverable=True  # 标记为可恢复
    )
    
    if st.button("演示可恢复错误", use_container_width=True):
        show_error(recoverable_error, show_details=True)
        ui_exception_handler.create_error_recovery_widget(recoverable_error)

def demo_custom_messages():
    """演示自定义错误消息"""
    st.header("⚙️ 自定义错误消息演示")
    
    st.markdown("""
    系统允许自定义错误消息以提供更好的用户体验。
    下面显示当前的错误消息配置：
    """)
    
    # 显示当前错误消息
    with st.expander("查看当前错误消息配置", expanded=False):
        st.json(ui_exception_handler.error_messages)
    
    # 自定义错误消息测试
    st.subheader("测试自定义消息")
    
    col1, col2 = st.columns(2)
    
    with col1:
        error_key = st.selectbox(
            "选择错误类型",
            options=["audio_format_unsupported", "model_loading_failed", "file_not_found", "memory_insufficient"]
        )
    
    with col2:
        if st.button("使用此消息类型创建错误"):
            # 根据选择创建相应的错误
            if error_key == "audio_format_unsupported":
                error = AudioProcessingError("不支持的音频格式")
            elif error_key == "model_loading_failed":
                error = ModelLoadingError("模型加载失败")
            elif error_key == "file_not_found":
                error = FileNotFoundError("文件未找到")
            else:
                error = MemoryError("内存不足")
            
            show_error(error, context={"demo": "custom_message", "key": error_key})

def demo_performance_stats():
    """演示性能统计"""
    st.header("📈 系统状态与统计")
    
    # 显示错误反馈统计
    feedback_file = Path("logs/error_feedback.jsonl")
    if feedback_file.exists():
        st.subheader("用户反馈统计")
        try:
            import json
            feedbacks = []
            with open(feedback_file, 'r', encoding='utf-8') as f:
                for line in f:
                    if line.strip():
                        feedbacks.append(json.loads(line))
            
            if feedbacks:
                st.metric("总反馈数量", len(feedbacks))
                
                # 显示最近的反馈
                recent_feedbacks = feedbacks[-3:]  # 最近3条
                for i, feedback in enumerate(recent_feedbacks):
                    with st.expander(f"反馈 {len(feedbacks)-len(recent_feedbacks)+i+1}", expanded=False):
                        st.write(f"**时间**: {feedback['timestamp']}")
                        st.write(f"**描述**: {feedback['description']}")
                        st.write(f"**邮箱**: {feedback.get('email', '匿名')}")
            else:
                st.info("暂无用户反馈")
        except Exception as e:
            st.error(f"读取反馈文件失败: {e}")
    else:
        st.info("暂无反馈文件")
    
    # 系统资源监控
    st.subheader("系统资源状态")
    try:
        import psutil
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("CPU使用率", f"{cpu_percent:.1f}%")
        
        with col2:
            st.metric("内存使用率", f"{memory.percent:.1f}%")
        
        with col3:
            st.metric("可用内存", f"{memory.available // (1024**3):.1f} GB")
        
        # 根据系统状态给出提示
        if memory.percent > 80:
            show_warning("内存使用率较高", "建议关闭其他程序释放内存")
        elif cpu_percent > 80:
            show_warning("CPU使用率较高", "系统负载较重，处理速度可能受影响")
        else:
            show_success("系统状态良好", "当前系统资源充足")
            
    except ImportError:
        st.info("psutil库不可用，无法显示系统资源信息")
    except Exception as e:
        show_error(e, context={"function": "系统监控"})

def main():
    """主函数"""
    if not IMPORTS_SUCCESSFUL:
        st.error("❌ 无法导入必要的模块，请检查安装配置")
        return
    
    st.title("🎛️ UI异常处理器功能演示")
    st.markdown("演示友好的异常提示和错误消息系统的各种功能")
    
    # 侧边栏导航
    with st.sidebar:
        st.header("🧭 演示导航")
        
        demo_sections = {
            "📢 基本消息类型": demo_basic_messages,
            "🚨 异常类型演示": demo_exception_types,
            "🎭 装饰器功能": demo_decorator_functionality,
            "📊 错误严重级别": demo_severity_levels,
            "📝 用户反馈系统": demo_feedback_system,
            "🔧 错误恢复功能": demo_error_recovery,
            "⚙️ 自定义错误消息": demo_custom_messages,
            "📈 系统状态统计": demo_performance_stats
        }
        
        selected_section = st.radio(
            "选择演示功能",
            options=list(demo_sections.keys()),
            index=0
        )
        
        st.markdown("---")
        
        # 一键测试所有功能
        if st.button("🚀 运行完整演示", use_container_width=True):
            st.session_state.run_full_demo = True
    
    # 显示选中的演示功能
    if st.session_state.get("run_full_demo", False):
        st.header("🚀 完整功能演示")
        for section_name, section_func in demo_sections.items():
            st.markdown(f"### {section_name}")
            try:
                section_func()
            except Exception as e:
                show_error(e, context={"section": section_name})
            st.markdown("---")
        st.session_state.run_full_demo = False
    else:
        # 运行选中的演示
        demo_sections[selected_section]()
    
    # 使用说明
    with st.expander("📖 使用说明", expanded=False):
        st.markdown("""
        ### UI异常处理器功能说明
        
        **主要特性：**
        1. **友好错误提示**: 将技术性错误转换为用户友好的消息
        2. **分级错误显示**: 根据错误严重程度使用不同的显示样式
        3. **错误恢复**: 为可恢复的错误提供自动恢复选项
        4. **用户反馈**: 收集用户对错误消息的反馈和建议
        5. **装饰器支持**: 通过装饰器简化异常处理
        6. **自定义消息**: 支持自定义错误消息配置
        
        **集成方法：**
        ```python
        from utils.ui_exception_handler import show_error, streamlit_exception_handler
        
        # 直接显示错误
        show_error(exception, context={"file": "test.wav"})
        
        # 使用装饰器
        @streamlit_exception_handler(show_details=True)
        def my_function():
            # 函数代码
            pass
        ```
        """)

if __name__ == "__main__":
    main() 