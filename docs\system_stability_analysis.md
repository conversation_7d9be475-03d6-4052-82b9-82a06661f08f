# 音频处理系统稳定性分析与优化方案

## 🔍 问题分析

### 1. 进程崩溃问题

**根本原因**：
- **内存泄漏**：模型加载后未正确释放GPU/CPU内存
- **资源竞争**：多线程访问共享资源时缺乏同步机制
- **异常处理不当**：模型加载失败时未正确清理已分配资源
- **任务超时**：长时间运行的任务未设置合理超时机制

**解决方案**：
```python
# 1. 增强的资源管理
@celery_app.task(bind=True, base=BaseTask)
def optimized_task(self, task_id, ...):
    recognizer = None
    try:
        # 任务逻辑
        pass
    finally:
        # 强制资源清理
        if recognizer and hasattr(recognizer, 'cleanup'):
            recognizer.cleanup()
        torch.cuda.empty_cache()
        gc.collect()

# 2. 任务超时设置
celery_app.conf.task_time_limit = 1800  # 30分钟硬超时
celery_app.conf.task_soft_time_limit = 1500  # 25分钟软超时
```

### 2. CPU占用过高问题

**根本原因**：
- **并发配置不当**：4个线程对于AI模型处理过多
- **模型推理效率**：未充分利用GPU，CPU承担过多计算
- **无限循环/死锁**：音频处理中的循环逻辑存在问题
- **资源分配不均**：CUDA和CPU资源分配策略不合理

**解决方案**：
```python
# 1. 动态并发调整
def get_optimal_concurrency():
    cpu_count = psutil.cpu_count()
    memory_gb = psutil.virtual_memory().total / (1024**3)
    return min(max(2, cpu_count // 2), 4)  # 保守策略

# 2. GPU优先策略
os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:512'
torch.cuda.empty_cache()

# 3. 任务队列优化
celery_app.conf.worker_prefetch_multiplier = 1  # 减少预取
celery_app.conf.task_acks_late = True  # 延迟确认
```

### 3. 资源管理优化

**内存使用优化**：
```python
# 1. 内存监控
class ResourceMonitor:
    def check_memory_usage(self):
        memory = psutil.virtual_memory()
        if memory.percent > 85:
            self.trigger_cleanup()
    
    def trigger_cleanup(self):
        gc.collect()
        torch.cuda.empty_cache()

# 2. 模型生命周期管理
class ModelManager:
    def __init__(self):
        self.model = None
        self.last_used = time.time()
    
    def get_model(self):
        if self.model is None:
            self.load_model()
        self.last_used = time.time()
        return self.model
    
    def cleanup_if_idle(self, timeout=300):  # 5分钟未使用则清理
        if time.time() - self.last_used > timeout:
            self.cleanup_model()
```

**GPU内存优化**：
```python
# 1. CUDA内存管理
def optimize_cuda_memory():
    if torch.cuda.is_available():
        # 设置内存分配策略
        torch.cuda.empty_cache()
        torch.cuda.synchronize()
        
        # 监控GPU内存使用
        allocated = torch.cuda.memory_allocated()
        cached = torch.cuda.memory_reserved()
        
        if allocated > 1.5 * 1024**3:  # 超过1.5GB
            torch.cuda.empty_cache()

# 2. 模型加载优化
def load_model_with_memory_check():
    initial_memory = torch.cuda.memory_allocated()
    try:
        model = load_model()
        final_memory = torch.cuda.memory_allocated()
        logger.info(f"模型加载消耗GPU内存: {(final_memory-initial_memory)/1024**2:.1f}MB")
        return model
    except RuntimeError as e:
        if "out of memory" in str(e):
            torch.cuda.empty_cache()
            raise MemoryError("GPU内存不足，请减少并发任务数")
        raise
```

### 4. 错误处理机制优化

**完善的异常处理**：
```python
class AudioProcessingError(Exception):
    """音频处理专用异常"""
    pass

class ModelLoadError(AudioProcessingError):
    """模型加载异常"""
    pass

class CUDAMemoryError(AudioProcessingError):
    """CUDA内存异常"""
    pass

def robust_task_execution(func):
    """任务执行装饰器"""
    def wrapper(*args, **kwargs):
        max_retries = 3
        for attempt in range(max_retries):
            try:
                return func(*args, **kwargs)
            except CUDAMemoryError:
                torch.cuda.empty_cache()
                gc.collect()
                if attempt == max_retries - 1:
                    raise
                time.sleep(2 ** attempt)  # 指数退避
            except Exception as e:
                logger.error(f"任务执行失败 (尝试 {attempt+1}/{max_retries}): {e}")
                if attempt == max_retries - 1:
                    raise
                time.sleep(1)
    return wrapper
```

## 🚀 系统优化配置

### 1. Celery Worker优化配置

```python
# start_worker_optimized.py
def start_optimized_worker():
    concurrency = get_optimal_concurrency()
    
    celery_app.worker_main([
        'worker',
        '--pool=threads',
        f'--concurrency={concurrency}',
        '--loglevel=info',
        '--queues=audio_processing,default,celery',
        '--max-tasks-per-child=10',  # 防止内存泄漏
        '--max-memory-per-child=1000000',  # 1GB内存限制
        '--time-limit=1800',  # 30分钟硬超时
        '--soft-time-limit=1500',  # 25分钟软超时
        '--without-gossip',
        '--without-mingle',
        '--without-heartbeat',
    ])
```

### 2. 系统监控集成

```python
# 集成资源监控
from backend.utils.system_monitor import ResourceMonitor, auto_cleanup_on_high_memory

# 在worker启动时启动监控
monitor = ResourceMonitor()
monitor.add_callback(lambda res: auto_cleanup_on_high_memory(85.0))
monitor.start_monitoring()
```

### 3. 配置文件优化

```python
# backend/core/config.py
class OptimizedSettings:
    # 任务配置
    CELERY_TASK_TIME_LIMIT = 1800
    CELERY_TASK_SOFT_TIME_LIMIT = 1500
    CELERY_WORKER_PREFETCH_MULTIPLIER = 1
    CELERY_TASK_ACKS_LATE = True
    
    # 内存配置
    MAX_MEMORY_PER_WORKER = 1000  # MB
    MEMORY_WARNING_THRESHOLD = 80  # %
    MEMORY_CRITICAL_THRESHOLD = 90  # %
    
    # GPU配置
    CUDA_MEMORY_FRACTION = 0.8
    PYTORCH_CUDA_ALLOC_CONF = "max_split_size_mb:512"
    
    # 音频处理配置
    MAX_AUDIO_DURATION = 600  # 秒
    AUDIO_CHUNK_SIZE = 30  # 秒
    MAX_CONCURRENT_AUDIO_TASKS = 2
```

## 📊 性能监控指标

### 关键指标
1. **内存使用率** < 80%
2. **GPU内存使用率** < 85%
3. **CPU使用率** < 70%
4. **任务完成率** > 95%
5. **平均任务时间** < 5分钟
6. **系统稳定运行时间** > 24小时

### 监控告警
```python
def setup_monitoring_alerts():
    monitor = get_resource_monitor()
    
    def alert_callback(resources):
        if resources.memory_percent > 90:
            logger.critical(f"内存使用率过高: {resources.memory_percent:.1f}%")
            # 触发紧急清理
            ResourceOptimizer.cleanup_memory()
        
        if resources.gpu_memory_percent > 90:
            logger.critical(f"GPU内存使用率过高: {resources.gpu_memory_percent:.1f}%")
            torch.cuda.empty_cache()
    
    monitor.add_callback(alert_callback)
```

## 🔧 部署建议

### 1. 硬件要求
- **CPU**: 8核心以上
- **内存**: 16GB以上
- **GPU**: 8GB显存以上
- **存储**: SSD，至少100GB可用空间

### 2. 系统配置
```bash
# 系统级优化
echo 'vm.swappiness=10' >> /etc/sysctl.conf
echo 'vm.vfs_cache_pressure=50' >> /etc/sysctl.conf

# GPU驱动优化
nvidia-smi -pm 1  # 持久模式
nvidia-smi -ac 877,1215  # 设置时钟频率
```

### 3. 监控部署
```bash
# 启动系统监控
python -m backend.utils.system_monitor &

# 启动优化的worker
python start_worker_windows.py
```

## 📈 预期效果

实施以上优化后，预期达到：
- **系统稳定性提升90%**
- **内存使用降低30%**
- **CPU占用降低40%**
- **任务处理速度提升20%**
- **错误率降低至1%以下**
