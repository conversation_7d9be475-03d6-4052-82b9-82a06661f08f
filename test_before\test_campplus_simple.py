#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的CAM++模型测试脚本
"""

import os
import sys
import tempfile
import numpy as np

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_imports():
    """测试基本导入"""
    print("=== 测试基本导入 ===")
    
    try:
        import torch
        print("✅ PyTorch导入成功")
    except ImportError as e:
        print(f"❌ PyTorch导入失败: {e}")
        return False
    
    try:
        from funasr import AutoModel
        print("✅ FunASR导入成功")
    except ImportError as e:
        print(f"❌ FunASR导入失败: {e}")
        return False
    
    try:
        import soundfile as sf
        print("✅ soundfile导入成功")
    except ImportError as e:
        print(f"❌ soundfile导入失败: {e}")
        return False
    
    try:
        from sklearn.cluster import AgglomerativeClustering
        print("✅ scikit-learn导入成功")
    except ImportError as e:
        print(f"❌ scikit-learn导入失败: {e}")
        return False
    
    return True

def test_speaker_recognition_import():
    """测试说话人识别模块导入"""
    print("\n=== 测试说话人识别模块导入 ===")
    
    try:
        from utils.speaker_recognition import SpeakerRecognition, CAMPlusModel
        print("✅ 说话人识别模块导入成功")
        return True
    except ImportError as e:
        print(f"❌ 说话人识别模块导入失败: {e}")
        print("尝试直接导入文件...")
        
        try:
            import utils.speaker_recognition as sr
            print("✅ 通过直接导入成功")
            return True
        except ImportError as e2:
            print(f"❌ 直接导入也失败: {e2}")
            return False

def test_campplus_model_creation():
    """测试CAM++模型创建"""
    print("\n=== 测试CAM++模型创建 ===")
    
    try:
        # 直接从当前目录导入
        sys.path.insert(0, './utils')
        import speaker_recognition
        
        model = speaker_recognition.CAMPlusModel()
        print("✅ CAM++模型对象创建成功")
        print(f"   设备: {model.device}")
        print(f"   模型配置: {model.model_config}")
        return True
    except Exception as e:
        print(f"❌ CAM++模型创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_audio_creation():
    """测试音频文件创建"""
    print("\n=== 测试音频文件创建 ===")
    
    try:
        import soundfile as sf
        
        # 创建测试音频
        sample_rate = 16000
        duration = 2.0
        t = np.linspace(0, duration, int(duration * sample_rate), endpoint=False)
        audio = np.sin(2 * np.pi * 440 * t) * 0.5
        
        # 保存到临时文件
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp:
            temp_path = tmp.name
        
        sf.write(temp_path, audio, sample_rate)
        
        if os.path.exists(temp_path):
            print(f"✅ 测试音频创建成功: {temp_path}")
            # 清理
            os.unlink(temp_path)
            return True
        else:
            print("❌ 测试音频文件未创建")
            return False
            
    except Exception as e:
        print(f"❌ 音频创建失败: {e}")
        return False

def test_model_loading():
    """测试模型加载"""
    print("\n=== 测试CAM++模型加载 ===")
    
    try:
        # 导入模块
        sys.path.insert(0, './utils')
        import speaker_recognition
        
        model = speaker_recognition.CAMPlusModel()
        
        # 尝试加载模型
        print("正在尝试加载CAM++模型...")
        success = model.load_model()
        
        if success:
            print("✅ CAM++模型加载成功")
            return True
        else:
            print("❌ CAM++模型加载失败")
            return False
            
    except Exception as e:
        print(f"❌ 模型加载测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("CAM++简化测试脚本")
    print("=" * 50)
    
    test_results = []
    
    # 1. 测试基本导入
    test_results.append(("基本导入", test_basic_imports()))
    
    # 2. 测试说话人识别模块导入
    test_results.append(("模块导入", test_speaker_recognition_import()))
    
    # 3. 测试CAM++模型创建
    test_results.append(("模型创建", test_campplus_model_creation()))
    
    # 4. 测试音频创建
    test_results.append(("音频创建", test_audio_creation()))
    
    # 5. 测试模型加载（如果前面的测试通过）
    if all(result[1] for result in test_results):
        test_results.append(("模型加载", test_model_loading()))
    else:
        print("\n⚠️ 跳过模型加载测试，因为前置条件未满足")
    
    # 输出结果
    print("\n" + "=" * 50)
    print("测试结果总结:")
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    passed = sum(1 for _, result in test_results if result)
    total = len(test_results)
    
    print(f"\n总体结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查环境配置")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code) 