/**
 * 端到端音频处理工作流测试
 * 测试从文件上传到任务处理的完整流程
 */

const BASE_URL = 'http://localhost:8002'

class E2EAudioWorkflowTester {
  constructor() {
    this.testResults = []
    this.uploadedFileIds = []
  }

  logResult(testName, passed, message = '') {
    const result = { testName, passed, message, timestamp: new Date().toISOString() }
    this.testResults.push(result)
    const status = passed ? '✅' : '❌'
    console.log(`${status} ${testName}: ${message}`)
  }

  // 创建测试音频文件
  createTestAudioFile(filename = 'test-audio.wav') {
    // 创建一个简单的WAV文件头（44字节）+ 1秒的静音数据
    const sampleRate = 16000
    const duration = 1 // 1秒
    const numSamples = sampleRate * duration
    
    // WAV文件头
    const header = new ArrayBuffer(44)
    const view = new DataView(header)
    
    // "RIFF" chunk descriptor
    view.setUint32(0, 0x52494646, false)  // "RIFF"
    view.setUint32(4, 36 + numSamples * 2, true)  // 文件大小
    view.setUint32(8, 0x57415645, false)  // "WAVE"
    
    // "fmt " sub-chunk
    view.setUint32(12, 0x666d7420, false)  // "fmt "
    view.setUint32(16, 16, true)  // Sub-chunk size
    view.setUint16(20, 1, true)   // Audio format (PCM)
    view.setUint16(22, 1, true)   // Number of channels
    view.setUint32(24, sampleRate, true)  // Sample rate
    view.setUint32(28, sampleRate * 2, true)  // Byte rate
    view.setUint16(32, 2, true)   // Block align
    view.setUint16(34, 16, true)  // Bits per sample
    
    // "data" sub-chunk
    view.setUint32(36, 0x64617461, false)  // "data"
    view.setUint32(40, numSamples * 2, true)  // Data size
    
    // 创建音频数据（静音）
    const audioData = new Int16Array(numSamples).fill(0)
    
    // 合并头部和数据
    const fullData = new Uint8Array(44 + numSamples * 2)
    fullData.set(new Uint8Array(header), 0)
    fullData.set(new Uint8Array(audioData.buffer), 44)
    
    return new Blob([fullData], { type: 'audio/wav' })
  }

  // 1. 测试文件上传
  async testFileUpload() {
    console.log('\n=== 1. 测试音频文件上传 ===')
    
    try {
      const audioBlob = this.createTestAudioFile()
      const formData = new FormData()
      formData.append('file', audioBlob, 'test-workflow.wav')

      const response = await fetch(`${BASE_URL}/api/v1/audio/upload`, {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer demo-token'
        },
        body: formData
      })

      if (response.ok) {
        const data = await response.json()
        this.uploadedFileIds.push(data.file_id)
        this.logResult('文件上传', true, `上传成功，文件ID: ${data.file_id}`)
        return data.file_id
      } else {
        const errorData = await response.json()
        this.logResult('文件上传', false, `上传失败: ${errorData.detail}`)
        return null
      }
    } catch (error) {
      this.logResult('文件上传', false, `上传异常: ${error.message}`)
      return null
    }
  }

  // 2. 测试VAD检测任务创建
  async testVADDetection(fileId) {
    console.log('\n=== 2. 测试VAD语音活动检测 ===')
    
    if (!fileId) {
      this.logResult('VAD检测', false, '没有可用的文件ID')
      return null
    }

    try {
      const requestData = {
        file_ids: [fileId],
        merge_length_s: 5.0,
        config: {
          threshold: 0.5,
          min_speech_duration: 0.3
        }
      }

      const response = await fetch(`${BASE_URL}/api/v1/speech/vad-detection`, {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer demo-token',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      })

      if (response.ok) {
        const data = await response.json()
        this.logResult('VAD检测', true, `任务创建成功，任务ID: ${data.task_id}`)
        return data.task_id
      } else {
        const errorData = await response.json()
        this.logResult('VAD检测', false, `任务创建失败: ${errorData.detail}`)
        return null
      }
    } catch (error) {
      this.logResult('VAD检测', false, `请求异常: ${error.message}`)
      return null
    }
  }

  // 3. 测试语音识别任务创建
  async testSpeechRecognition(fileId) {
    console.log('\n=== 3. 测试语音识别 ===')
    
    if (!fileId) {
      this.logResult('语音识别', false, '没有可用的文件ID')
      return null
    }

    try {
      const requestData = {
        file_ids: [fileId],
        language: "auto",
        use_itn: true,
        ban_emo_unk: false,
        config: {}
      }

      const response = await fetch(`${BASE_URL}/api/v1/speech/speech-recognition`, {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer demo-token',
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData)
      })

      if (response.ok) {
        const data = await response.json()
        this.logResult('语音识别', true, `任务创建成功，任务ID: ${data.task_id}`)
        return data.task_id
      } else {
        const errorData = await response.json()
        this.logResult('语音识别', false, `任务创建失败: ${errorData.detail}`)
        return null
      }
    } catch (error) {
      this.logResult('语音识别', false, `请求异常: ${error.message}`)
      return null
    }
  }

  // 4. 测试任务状态监控
  async testTaskStatusMonitoring(taskId, taskName = '任务') {
    console.log(`\n=== 4. 测试${taskName}状态监控 ===`)
    
    if (!taskId) {
      this.logResult(`${taskName}状态监控`, false, '没有可用的任务ID')
      return false
    }

    try {
      const response = await fetch(`${BASE_URL}/api/v1/speech/task/${taskId}`, {
        headers: {
          'Authorization': 'Bearer demo-token'
        }
      })

      if (response.ok) {
        const data = await response.json()
        this.logResult(`${taskName}状态监控`, true, `状态查询成功: ${data.status}`)
        return true
      } else if (response.status === 403) {
        this.logResult(`${taskName}状态监控`, true, '权限验证正常')
        return true
      } else {
        const errorData = await response.json()
        this.logResult(`${taskName}状态监控`, false, `状态查询失败: ${errorData.detail}`)
        return false
      }
    } catch (error) {
      this.logResult(`${taskName}状态监控`, false, `请求异常: ${error.message}`)
      return false
    }
  }

  // 5. 测试WebSocket实时进度
  async testWebSocketProgress() {
    console.log('\n=== 5. 测试WebSocket实时进度 ===')
    
    return new Promise((resolve) => {
      try {
        const ws = new WebSocket('ws://localhost:8002/ws/progress?token=demo-token')
        
        const timeout = setTimeout(() => {
          ws.close()
          this.logResult('WebSocket进度监控', false, '连接超时')
          resolve(false)
        }, 5000)

        ws.onopen = () => {
          clearTimeout(timeout)
          this.logResult('WebSocket进度监控', true, '连接成功')
          
          // 发送测试消息
          const testMessage = JSON.stringify({
            type: 'subscribe',
            task_id: 'test_task'
          })
          ws.send(testMessage)
          
          // 等待一会儿后关闭
          setTimeout(() => {
            ws.close()
            resolve(true)
          }, 1000)
        }

        ws.onerror = (error) => {
          clearTimeout(timeout)
          this.logResult('WebSocket进度监控', false, `连接失败: ${error.message || '未知错误'}`)
          resolve(false)
        }

        ws.onmessage = (event) => {
          console.log('收到WebSocket消息:', event.data)
        }
        
      } catch (error) {
        this.logResult('WebSocket进度监控', false, `创建连接失败: ${error.message}`)
        resolve(false)
      }
    })
  }

  // 6. 测试文件清理
  async testFileCleanup() {
    console.log('\n=== 6. 测试文件清理 ===')
    
    let cleanupCount = 0
    
    for (const fileId of this.uploadedFileIds) {
      try {
        const response = await fetch(`${BASE_URL}/api/v1/audio/${fileId}`, {
          method: 'DELETE',
          headers: {
            'Authorization': 'Bearer demo-token'
          }
        })

        if (response.ok) {
          cleanupCount++
        }
      } catch (error) {
        console.log(`清理文件 ${fileId} 失败:`, error.message)
      }
    }

    this.logResult('文件清理', true, `清理了 ${cleanupCount}/${this.uploadedFileIds.length} 个文件`)
    return cleanupCount
  }

  // 运行完整的端到端测试
  async runCompleteWorkflow() {
    console.log('🚀 开始端到端音频处理工作流测试...\n')
    
    const startTime = Date.now()
    
    try {
      // 1. 文件上传
      const fileId = await this.testFileUpload()
      
      // 2. VAD检测任务
      const vadTaskId = await this.testVADDetection(fileId)
      
      // 3. 语音识别任务
      const speechTaskId = await this.testSpeechRecognition(fileId)
      
      // 4. 任务状态监控
      if (vadTaskId) {
        await this.testTaskStatusMonitoring(vadTaskId, 'VAD检测')
      }
      
      if (speechTaskId) {
        await this.testTaskStatusMonitoring(speechTaskId, '语音识别')
      }
      
      // 5. WebSocket测试
      await this.testWebSocketProgress()
      
      // 6. 清理文件
      await this.testFileCleanup()
      
    } catch (error) {
      console.log('❌ 工作流测试异常:', error.message)
    }
    
    // 生成测试报告
    const endTime = Date.now()
    const duration = endTime - startTime
    
    console.log('\n=== 端到端测试报告 ===')
    const passedTests = this.testResults.filter(r => r.passed).length
    const totalTests = this.testResults.length
    
    console.log(`✅ 通过: ${passedTests}/${totalTests} 个测试`)
    console.log(`⏱️  耗时: ${duration}ms`)
    
    if (passedTests === totalTests) {
      console.log('🎉 完整工作流测试通过！')
    } else {
      console.log('❌ 部分测试失败：')
      
      const failedTests = this.testResults.filter(r => !r.passed)
      failedTests.forEach(test => {
        console.log(`   ❌ ${test.testName}: ${test.message}`)
      })
    }
    
    return {
      passed: passedTests,
      total: totalTests,
      duration,
      results: this.testResults
    }
  }
}

// 运行测试
if (typeof require !== 'undefined' && require.main === module) {
  const tester = new E2EAudioWorkflowTester()
  tester.runCompleteWorkflow().then(results => {
    process.exit(results.passed === results.total ? 0 : 1)
  })
}

module.exports = E2EAudioWorkflowTester 