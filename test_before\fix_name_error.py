#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复 NameError: name 'c' is not defined 错误
这个错误通常是由torch内部的日志配置问题引起的
"""

import os
import sys
import warnings
import logging

def fix_torch_environment():
    """修复torch环境配置"""
    print("🔧 修复torch环境配置...")
    
    # 设置环境变量
    env_vars = {
        'TORCH_LOGS': '',
        'TORCH_LOG_LEVEL': 'ERROR',
        'TORCH_SHOW_CPP_STACKTRACES': '0',
        'PYTHONWARNINGS': 'ignore',
        'TF_CPP_MIN_LOG_LEVEL': '2',
        'CUDA_LAUNCH_BLOCKING': '0',
        'TORCH_USE_CUDA_DSA': '0'
    }
    
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"  设置 {key} = {value}")
    
    # 禁用警告
    warnings.filterwarnings('ignore')
    
    # 设置日志级别
    logging.getLogger().setLevel(logging.ERROR)
    
    print("✅ 环境变量设置完成")

def test_torch_import():
    """测试torch导入"""
    print("\n🧪 测试torch导入...")
    
    try:
        # 先尝试设置torch日志
        try:
            import torch._logging
            torch._logging.set_logs(all=logging.ERROR)
            print("  ✅ torch日志配置成功")
        except:
            print("  ⚠️ torch日志配置跳过")
        
        # 导入torch
        import torch
        print(f"  ✅ torch导入成功: {torch.__version__}")
        
        # 测试基本功能
        x = torch.tensor([1.0, 2.0, 3.0])
        result = x.sum().item()
        print(f"  ✅ torch基本功能测试: {result}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ torch导入失败: {e}")
        return False

def test_streamlit_import():
    """测试streamlit导入"""
    print("\n🧪 测试streamlit导入...")
    
    try:
        import streamlit as st
        print(f"  ✅ streamlit导入成功: {st.__version__}")
        return True
    except Exception as e:
        print(f"  ❌ streamlit导入失败: {e}")
        return False

def test_other_imports():
    """测试其他关键导入"""
    print("\n🧪 测试其他关键导入...")
    
    imports_to_test = [
        ('numpy', 'np'),
        ('soundfile', 'sf'),
        ('pandas', 'pd'),
        ('matplotlib.pyplot', 'plt'),
    ]
    
    success_count = 0
    for module_name, alias in imports_to_test:
        try:
            __import__(module_name)
            print(f"  ✅ {module_name} 导入成功")
            success_count += 1
        except Exception as e:
            print(f"  ❌ {module_name} 导入失败: {e}")
    
    print(f"  📊 导入成功率: {success_count}/{len(imports_to_test)}")
    return success_count == len(imports_to_test)

def create_safe_startup_script():
    """创建安全的启动脚本"""
    print("\n📝 创建安全启动脚本...")
    
    script_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全启动脚本 - 避免torch日志错误
"""

import os
import warnings
import logging

# 设置环境变量
os.environ['TORCH_LOGS'] = ''
os.environ['TORCH_LOG_LEVEL'] = 'ERROR'
os.environ['TORCH_SHOW_CPP_STACKTRACES'] = '0'
os.environ['PYTHONWARNINGS'] = 'ignore'

# 禁用警告
warnings.filterwarnings('ignore')
logging.getLogger().setLevel(logging.ERROR)

# 尝试设置torch日志
try:
    import torch._logging
    torch._logging.set_logs(all=logging.ERROR)
except:
    pass

# 导入streamlit并运行
if __name__ == "__main__":
    import subprocess
    import sys
    
    cmd = [
        sys.executable, "-m", "streamlit", "run", 
        "pages/语音处理分析.py",
        "--server.headless", "true",
        "--server.port", "8501"
    ]
    
    subprocess.run(cmd, env=os.environ.copy())
'''
    
    try:
        with open('safe_start.py', 'w', encoding='utf-8') as f:
            f.write(script_content)
        print("  ✅ 安全启动脚本创建成功: safe_start.py")
        return True
    except Exception as e:
        print(f"  ❌ 创建启动脚本失败: {e}")
        return False

def main():
    """主修复函数"""
    print("🚀 修复 NameError: name 'c' is not defined 错误")
    print("=" * 60)
    
    # 修复环境
    fix_torch_environment()
    
    # 测试导入
    torch_ok = test_torch_import()
    streamlit_ok = test_streamlit_import()
    others_ok = test_other_imports()
    
    # 创建启动脚本
    script_ok = create_safe_startup_script()
    
    print("\n" + "=" * 60)
    print("📋 修复结果:")
    print(f"  🔧 环境配置: ✅")
    print(f"  🧪 torch导入: {'✅' if torch_ok else '❌'}")
    print(f"  🧪 streamlit导入: {'✅' if streamlit_ok else '❌'}")
    print(f"  🧪 其他导入: {'✅' if others_ok else '❌'}")
    print(f"  📝 启动脚本: {'✅' if script_ok else '❌'}")
    
    if torch_ok and streamlit_ok:
        print("\n🎉 修复成功！")
        print("💡 使用建议:")
        print("  1. 重启Python环境")
        print("  2. 运行: python safe_start.py")
        print("  3. 或直接运行: streamlit run pages/语音处理分析.py")
    else:
        print("\n⚠️ 部分修复成功，可能需要重新安装依赖")
        print("💡 建议:")
        print("  1. 重新安装torch: uv pip install torch")
        print("  2. 重新安装streamlit: uv pip install streamlit")

if __name__ == "__main__":
    main()
