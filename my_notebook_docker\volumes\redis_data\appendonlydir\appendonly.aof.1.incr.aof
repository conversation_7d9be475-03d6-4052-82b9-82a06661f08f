*2
$6
SELECT
$1
0
*3
$4
SADD
$34
_kombu.binding.reply.celery.pidbox
$96
001976a1-3910-3f79-82ca-65eb8978c957001976a1-3910-3f79-82ca-65eb8978c957.reply.celery.pidbox
*5
$3
SET
$13
unacked_mutex
$32
********************************
$4
PXAT
$13
1753113412473
*2
$3
del
$13
unacked_mutex
*3
$4
SREM
$34
_kombu.binding.reply.celery.pidbox
$96
001976a1-3910-3f79-82ca-65eb8978c957001976a1-3910-3f79-82ca-65eb8978c957.reply.celery.pidbox
*5
$3
SET
$13
unacked_mutex
$32
********************************
$4
PXAT
$13
1753113431642
*2
$3
del
$13
unacked_mutex
*3
$4
SADD
$34
_kombu.binding.reply.celery.pidbox
$96
18c8cbad-7d18-3e80-8b1e-65f14f8eb8fd18c8cbad-7d18-3e80-8b1e-65f14f8eb8fd.reply.celery.pidbox
*3
$4
SREM
$34
_kombu.binding.reply.celery.pidbox
$96
18c8cbad-7d18-3e80-8b1e-65f14f8eb8fd18c8cbad-7d18-3e80-8b1e-65f14f8eb8fd.reply.celery.pidbox
*3
$4
SADD
$21
_kombu.binding.celery
$42
document_processingdocument_processing
*3
$4
SADD
$21
_kombu.binding.celery
$30
vectorizationvectorization
*3
$4
SADD
$21
_kombu.binding.celery
$32
ocr_processingocr_processing
*3
$4
SADD
$21
_kombu.binding.celery
$36
audio_processingaudio_processing
*3
$4
SADD
$21
_kombu.binding.celery
$18
defaultdefault
*3
$4
SADD
$28
_kombu.binding.celery.pidbox
$37
<EMAIL>
*3
$4
SADD
$23
_kombu.binding.celeryev
$57
worker.#celeryev.9ee39c4e-adc2-43cd-a818-c6da290f05f8
*3
$4
SADD
$34
_kombu.binding.reply.celery.pidbox
$96
a26cb31e-eb62-341c-b7d8-fd4a077bdd50a26cb31e-eb62-341c-b7d8-fd4a077bdd50.reply.celery.pidbox
*3
$5
LPUSH
$56
a26cb31e-eb62-341c-b7d8-fd4a077bdd50.reply.celery.pidbox
$447
{"body": "eyJjZWxlcnlAY2Y4ODYyODVkNjQxIjogeyJvayI6ICJwb25nIn19", "content-encoding": "utf-8", "content-type": "application/json", "headers": {"ticket": "d21b0bc9-df99-4f6c-a50e-e2fc276ffdf6", "clock": 3}, "properties": {"delivery_mode": 1, "delivery_info": {"exchange": "reply.celery.pidbox", "routing_key": "a26cb31e-eb62-341c-b7d8-fd4a077bdd50"}, "priority": 0, "body_encoding": "base64", "delivery_tag": "f8d48d00-8248-4c79-a48a-7df906589aec"}}
*2
$4
RPOP
$56
a26cb31e-eb62-341c-b7d8-fd4a077bdd50.reply.celery.pidbox
*5
$3
SET
$13
unacked_mutex
$32
********************************
$4
PXAT
$13
*************
*2
$3
del
$13
unacked_mutex
*3
$4
SREM
$34
_kombu.binding.reply.celery.pidbox
$96
a26cb31e-eb62-341c-b7d8-fd4a077bdd50a26cb31e-eb62-341c-b7d8-fd4a077bdd50.reply.celery.pidbox
*3
$4
SADD
$34
_kombu.binding.reply.celery.pidbox
$96
967d85fe-67e4-36d7-a40e-9240f36bf15b967d85fe-67e4-36d7-a40e-9240f36bf15b.reply.celery.pidbox
*3
$5
LPUSH
$56
967d85fe-67e4-36d7-a40e-9240f36bf15b.reply.celery.pidbox
$447
{"body": "eyJjZWxlcnlAY2Y4ODYyODVkNjQxIjogeyJvayI6ICJwb25nIn19", "content-encoding": "utf-8", "content-type": "application/json", "headers": {"ticket": "bca8c1e5-c821-4786-b597-dd320230d7ff", "clock": 5}, "properties": {"delivery_mode": 1, "delivery_info": {"exchange": "reply.celery.pidbox", "routing_key": "967d85fe-67e4-36d7-a40e-9240f36bf15b"}, "priority": 0, "body_encoding": "base64", "delivery_tag": "0e263b53-439d-435c-a16b-ba2a21374522"}}
*2
$4
RPOP
$56
967d85fe-67e4-36d7-a40e-9240f36bf15b.reply.celery.pidbox
*5
$3
SET
$13
unacked_mutex
$32
********************************
$4
PXAT
$13
*************
*2
$3
del
$13
unacked_mutex
*3
$4
SREM
$34
_kombu.binding.reply.celery.pidbox
$96
967d85fe-67e4-36d7-a40e-9240f36bf15b967d85fe-67e4-36d7-a40e-9240f36bf15b.reply.celery.pidbox
*5
$3
SET
$13
unacked_mutex
$32
********************************
$4
PXAT
$13
*************
*2
$3
del
$13
unacked_mutex
