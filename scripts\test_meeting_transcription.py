#!/usr/bin/env python3
"""
测试会议转录功能
"""

import os
import sys
import logging
import time

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, project_root)

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_meeting_transcription():
    """测试会议转录任务"""
    try:
        from backend.tasks.audio_processing_tasks import meeting_transcription_task
        
        # 测试参数
        task_id = "test_meeting_transcription_" + str(int(time.time()))
        user_id = "1"
        file_ids = ["1"]  # 使用数据库中存在的文件ID
        language = "auto"
        output_format = "timeline"
        include_timestamps = True
        speaker_labeling = True
        config = {
            "language": "auto",
            "use_itn": True,
            "ban_emo_unk": False,
            "merge_vad": True,
            "merge_length_s": 15,
            "min_speech_duration": 0.5,
            "max_speech_duration": 60,
            "threshold": 0.5
        }

        logger.info(f"开始测试会议转录任务: {task_id}")
        logger.info(f"参数: file_ids={file_ids}, language={language}")

        # 执行任务
        result = meeting_transcription_task(
            task_id,
            user_id,
            file_ids,
            language=language,
            output_format=output_format,
            include_timestamps=include_timestamps,
            speaker_labeling=speaker_labeling,
            config=config
        )
        
        logger.info("会议转录任务完成")
        logger.info(f"结果: {result}")
        
        # 检查结果
        if result and result.get('status') == 'success':
            logger.info("✅ 会议转录测试成功")
            
            # 检查文本内容
            results = result.get('results', [])
            if results:
                first_result = results[0]
                text_content = first_result.get('result', {}).get('text', '')
                if text_content:
                    logger.info(f"✅ 成功识别文本: {text_content[:100]}...")
                else:
                    logger.warning("⚠️ 文本内容为空")
            
            return True
        else:
            logger.error(f"❌ 会议转录测试失败: {result}")
            return False
            
    except Exception as e:
        logger.error(f"❌ 会议转录测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    logger.info("开始会议转录功能测试")
    
    success = test_meeting_transcription()
    
    if success:
        logger.info("✅ 所有测试通过")
    else:
        logger.error("❌ 测试失败")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
