/**
 * 测试全局进度条修复
 * 模拟RAG上传的WebSocket进度更新
 */

// 模拟全局进度条状态
let globalProgress = {
  show: false,
  title: '',
  detail: '',
  percentage: 0,
  taskId: null
};

// 模拟updateGlobalProgress函数
function updateGlobalProgress(percentage, detail = '') {
  console.log('🔄 updateGlobalProgress 被调用:', { percentage, detail, show: globalProgress.show });
  if (globalProgress.show) {
    const oldPercentage = globalProgress.percentage;
    globalProgress.percentage = Math.min(100, Math.max(0, percentage));
    if (detail) {
      globalProgress.detail = detail;
    }
    console.log('✅ 全局进度已更新:', {
      from: oldPercentage,
      to: globalProgress.percentage,
      detail: globalProgress.detail
    });
  } else {
    console.warn('⚠️ 全局进度条未显示，无法更新');
  }
}

// 模拟hideGlobalProgress函数
function hideGlobalProgress() {
  globalProgress.show = false;
  globalProgress.percentage = 0;
  globalProgress.taskId = null;
  console.log('🔌 全局进度条已隐藏');
}

// 模拟showGlobalProgress函数
function showGlobalProgress(title, detail = '', canCancel = false, cancelCallback = null, taskId = null) {
  globalProgress = {
    show: true,
    title,
    detail,
    percentage: 0,
    canCancel,
    cancelCallback,
    taskId
  };
  console.log('📊 显示全局进度条:', { title, detail, taskId });
}

// 修复后的updateDocumentProgress函数
function updateDocumentProgress(taskId, progressData) {
  console.log('📈 文档进度更新:', { taskId, progressData });

  const percentage = progressData.percentage || 0;
  const detail = progressData.detail || progressData.message || '处理中...';

  // 更新全局进度条（如果当前任务匹配）
  if (globalProgress.show && globalProgress.taskId === taskId) {
    console.log('🔄 更新全局进度条:', { percentage, detail });
    updateGlobalProgress(percentage, detail);
  } else {
    console.log('⚠️ 全局进度条任务ID不匹配或未显示:', {
      globalShow: globalProgress.show,
      globalTaskId: globalProgress.taskId,
      currentTaskId: taskId
    });
  }

  // 这里原本还会更新文档处理对话框，但我们只关注全局进度条
}

// 修复后的handleTaskCompletion函数
function handleTaskCompletion(taskId, result, status) {
  console.log('🏁 任务完成处理:', { taskId, result, status });

  // 更新全局进度条（如果当前任务匹配）
  if (globalProgress.show && globalProgress.taskId === taskId) {
    if (status === 'completed') {
      console.log('✅ 更新全局进度条为完成状态');
      updateGlobalProgress(100, '处理完成！');
      setTimeout(() => {
        hideGlobalProgress();
      }, 2000);
    } else if (status === 'failed') {
      console.log('❌ 更新全局进度条为失败状态');
      updateGlobalProgress(0, result.error_message || '处理失败');
      setTimeout(() => {
        hideGlobalProgress();
      }, 3000);
    }
  }
}

// 模拟WebSocket数据转换函数
function convertWebSocketDataToProgress(payload) {
  if (!payload) return null;
  
  let percentage = 0;
  let detail = '处理中...';
  let status = 'pending';

  // 从payload中提取实际的任务数据
  const taskData = payload.progress || payload.status || payload;
  
  // 安全地获取状态信息
  const taskStatus = taskData.state || taskData.status || 'PENDING';
  
  // 获取嵌套的进度信息
  const progressInfo = taskData.progress || {};
  
  // 根据任务状态确定进度
  if (taskStatus === 'PENDING') {
    if (progressInfo.percentage !== undefined && progressInfo.percentage > 0) {
      percentage = progressInfo.percentage;
      detail = progressInfo.detail || progressInfo.stage || '处理中...';
      status = 'progress';
    } else {
      percentage = 5;
      detail = '等待处理...';
      status = 'pending';
    }
  } else if (taskStatus === 'PROGRESS') {
    if (progressInfo.percentage !== undefined) {
      percentage = progressInfo.percentage;
      detail = progressInfo.detail || progressInfo.stage || '处理中...';
    } else if (taskData.percentage !== undefined) {
      percentage = taskData.percentage;
      detail = taskData.detail || '处理中...';
    } else {
      percentage = 20;
      detail = '正在处理...';
    }
    status = 'progress';
  } else if (taskStatus === 'SUCCESS') {
    percentage = 100;
    detail = '处理完成';
    status = 'completed';
  } else if (taskStatus === 'FAILURE') {
    percentage = 0;
    detail = '处理失败';
    status = 'failed';
  }

  return {
    percentage: percentage,
    detail: detail,
    status: status,
    task_id: payload.task_id
  };
}

// 测试场景
function testRAGUploadProgress() {
  console.log('=' * 80);
  console.log('RAG上传全局进度条修复测试');
  console.log('=' * 80);

  const testTaskId = 'vectorize_doc123';

  // 1. 模拟开始RAG上传
  console.log('\n🚀 步骤1: 开始RAG上传');
  showGlobalProgress('上传到知识库', '正在上传文档到知识库...', false, null, testTaskId);

  // 2. 模拟WebSocket进度更新
  console.log('\n📈 步骤2: 模拟WebSocket进度更新');
  
  const progressUpdates = [
    {
      task_id: testTaskId,
      progress: {
        state: 'PROGRESS',
        progress: {
          percentage: 15,
          detail: '正在分析文档结构...',
          stage: 'analyzing'
        }
      }
    },
    {
      task_id: testTaskId,
      progress: {
        state: 'PROGRESS',
        progress: {
          percentage: 45,
          detail: '正在向量化第 20/50 个文档块...',
          stage: 'vectorizing'
        }
      }
    },
    {
      task_id: testTaskId,
      progress: {
        state: 'PROGRESS',
        progress: {
          percentage: 80,
          detail: '正在保存向量数据...',
          stage: 'saving'
        }
      }
    },
    {
      task_id: testTaskId,
      progress: {
        state: 'SUCCESS',
        progress: {
          percentage: 100,
          detail: '向量化完成',
          stage: 'completed'
        }
      }
    }
  ];

  progressUpdates.forEach((update, index) => {
    setTimeout(() => {
      console.log(`\n📊 进度更新 ${index + 1}:`);
      const progressData = convertWebSocketDataToProgress(update);
      console.log('转换后的进度数据:', progressData);
      updateDocumentProgress(testTaskId, progressData);
      
      // 如果是最后一个更新（完成状态），触发任务完成处理
      if (progressData.status === 'completed') {
        setTimeout(() => {
          handleTaskCompletion(testTaskId, { nodes_added: 50 }, 'completed');
        }, 500);
      }
    }, index * 1000);
  });

  // 3. 测试错误的任务ID（不应该更新全局进度条）
  setTimeout(() => {
    console.log('\n🔍 步骤3: 测试错误的任务ID');
    const wrongTaskUpdate = {
      task_id: 'wrong_task_id',
      progress: {
        state: 'PROGRESS',
        progress: {
          percentage: 50,
          detail: '这个更新不应该影响全局进度条',
          stage: 'processing'
        }
      }
    };
    const progressData = convertWebSocketDataToProgress(wrongTaskUpdate);
    updateDocumentProgress('wrong_task_id', progressData);
  }, 5000);
}

// 运行测试
testRAGUploadProgress();
