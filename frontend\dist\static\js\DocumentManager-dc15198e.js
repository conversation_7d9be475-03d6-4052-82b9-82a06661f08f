import{K as at,J as A,_ as fs,u as ys,h as v,i as Re,m as Ze,o as hs,E as f,n as ks,L as K,r as h,M as bs,a as g,c as b,b as s,d as o,w as n,t as p,N as ws,z as D,x,f as y,D as et,A as w,B as tt,O as L,F as se,p as oe,P as st,s as ot,H as Ae,Q as Ss,R as Vs,C as xs,S as zs,T as Ts,U as $s,V as Ps,q as lt}from"./index-2c134546.js";function Cs(M){return at({url:`/api/v1/documents/progress/${M}`,method:"get"})}function Ds(M){return at({url:`/api/v1/documents/documents/${M}/upload-to-rag`,method:"post"})}function Us(){console.log("🔍 认证调试信息:");const M=A();console.log("Token:",M?`${M.substring(0,20)}...`:"null");const le=localStorage.getItem("token");console.log("LocalStorage Token:",le?`${le.substring(0,20)}...`:"null");const Q=sessionStorage.getItem("token");console.log("SessionStorage Token:",Q?`${Q.substring(0,20)}...`:"null");const W=localStorage.getItem("userInfo");return console.log("User Info:",W?JSON.parse(W):"null"),{hasToken:!!M,token:M,userInfo:W?JSON.parse(W):null}}const Ms={class:"document-manager-container"},Os={class:"page-header"},Es={class:"header-content"},Rs={class:"header-actions"},As={key:0,class:"global-progress-container"},Ws={class:"progress-info"},Fs={class:"progress-text"},Bs={class:"progress-title"},Ls={class:"progress-detail"},Is={class:"progress-percentage"},js={class:"progress-bar-container"},Ns={class:"progress-actions"},Gs={class:"page-main"},Hs={class:"main-layout"},qs={class:"left-panel"},Js={class:"config-card"},Ks={class:"card-header"},Qs={class:"config-actions"},Xs={class:"config-content"},Ys={key:0,class:"upload-content"},Zs={class:"form-group"},eo={class:"advanced-content"},to={class:"preprocessing-section"},so={class:"preprocessing-options"},oo={class:"option-item"},lo={class:"option-item"},ao={class:"option-item"},no={class:"option-item"},ro={class:"splitting-section"},io={class:"form-group"},co={class:"form-group"},uo={class:"parameter-settings"},po={class:"setting-item"},mo={class:"setting-label"},go={class:"setting-control"},_o={class:"setting-input"},vo={class:"setting-item"},fo={class:"setting-label"},yo={class:"setting-control"},ho={class:"setting-input"},ko={key:0,class:"semantic-settings"},bo={class:"setting-item"},wo={class:"setting-label"},So={class:"setting-control"},Vo={class:"setting-input"},xo={key:1,class:"text-upload-content"},zo={class:"config-card"},To={class:"card-header"},$o={class:"config-actions"},Po={key:0,class:"config-content"},Co={class:"form-group"},Do={class:"preset-option"},Uo={class:"preset-name"},Mo={class:"preset-desc"},Oo={class:"setting-section"},Eo={class:"form-group"},Ro={class:"setting-item"},Ao={class:"setting-label"},Wo={class:"setting-control"},Fo={class:"setting-input"},Bo={class:"setting-section"},Lo={class:"switch-grid"},Io={class:"switch-group"},jo={class:"switch-group"},No={key:0,class:"advanced-image-settings"},Go={class:"setting-item"},Ho={class:"setting-label"},qo={class:"setting-control"},Jo={class:"setting-input"},Ko={class:"setting-item"},Qo={class:"setting-label"},Xo={class:"setting-control"},Yo={class:"setting-input"},Zo={class:"form-group"},el={class:"ocr-quality-indicator"},tl={class:"quality-header"},sl={class:"quality-tips"},ol={class:"right-panel"},ll={class:"documents-card"},al={class:"card-header"},nl={class:"document-actions"},rl={class:"documents-container"},il={class:"filename-cell"},cl={class:"filename-text"},ul={class:"nodes-count"},dl={class:"upload-time"},pl={class:"table-actions"},ml={class:"pagination-container"},gl={key:0,class:"error-content"},_l={class:"error-header"},vl={class:"error-title"},fl={class:"error-details"},yl={class:"error-message"},hl={key:0,class:"error-technical"},kl={class:"error-stack"},bl={key:1,class:"error-suggestions"},wl={class:"suggestion-list"},Sl={key:2,class:"error-actions"},Vl={class:"action-buttons"},xl={class:"dialog-footer"},zl={key:0,class:"progress-content"},Tl={class:"file-info"},$l={class:"file-name"},Pl={class:"file-size"},Cl={class:"progress-stages"},Dl={class:"stage-icon"},Ul={class:"stage-content"},Ml={class:"stage-title"},Ol={class:"stage-description"},El={key:0,class:"stage-progress"},Rl={key:0,class:"error-info"},Al={key:1,class:"success-info"},Wl={class:"result-summary"},Fl={class:"dialog-footer"},Bl={class:"preview-content"},Ll={class:"preview-header"},Il={class:"stat-number"},jl={class:"upload-time"},Nl={class:"file-size"},Gl={class:"preview-tabs"},Hl={class:"preview-nodes"},ql={class:"node-header"},Jl={class:"node-info"},Kl={class:"node-index"},Ql={class:"node-actions"},Xl={class:"node-content"},Yl={class:"content-text"},Zl={class:"document-stats"},ea={class:"stats-grid"},ta={class:"stat-card"},sa={class:"stat-value"},oa={class:"stat-card"},la={class:"stat-value"},aa={class:"stat-card"},na={class:"stat-value"},ra={class:"stat-card"},ia={class:"stat-value"},ca={class:"dialog-footer"},ua={__name:"DocumentManager",setup(M){ys();const le=async()=>{try{console.log("🔌 初始化全局WebSocket系统...");const{useWebSocketStore:t}=await K(()=>import("./websocket-aa959a18.js"),["static/js/websocket-aa959a18.js","static/js/index-2c134546.js","static/css/index-5cad6ac7.css","static/js/auth-e6295339.js"]),e=t();return e.isConnected||(console.log("🔌 WebSocket未连接，开始连接..."),await e.connect()),e.isConnected?(console.log("✅ 全局WebSocket系统初始化成功"),!0):(console.warn("⚠️ 全局WebSocket连接失败"),!1)}catch(t){return console.error("❌ 全局WebSocket初始化失败:",t),!1}},Q=async t=>{try{const{useWebSocketStore:e}=await K(()=>import("./websocket-aa959a18.js"),["static/js/websocket-aa959a18.js","static/js/index-2c134546.js","static/css/index-5cad6ac7.css","static/js/auth-e6295339.js"]),a=e();a.isConnected||(console.log("🔌 WebSocket未连接，尝试连接..."),await a.connect()),a.send({type:"subscribe",task_id:t})?console.log(`📡 订阅文档任务进度: ${t}`):console.warn(`⚠️ 发送订阅消息失败: ${t}`)}catch(e){console.error("❌ 订阅任务失败:",e)}},W=async t=>{try{const{useWebSocketStore:e}=await K(()=>import("./websocket-aa959a18.js"),["static/js/websocket-aa959a18.js","static/js/index-2c134546.js","static/css/index-5cad6ac7.css","static/js/auth-e6295339.js"]);e().send({type:"unsubscribe",task_id:t})&&console.log(`📡 取消订阅文档任务: ${t}`)}catch(e){console.error("❌ 取消订阅任务失败:",e)}},ve=t=>{console.log("📨 处理DocumentManager WebSocket消息:",{type:t.type,task_id:t.task_id,hasData:!!t.data});const{taskId:e,messageData:a}=nt(t);if(!e){console.warn("⚠️ 消息缺少task_id:",t);return}switch(t.type){case"progress_update":rt(e,a);break;case"task_completed":it(e,a);break;case"task_failed":ct(e,a);break;case"connection_established":console.log("📡 DocumentManager WebSocket连接已确认");break;default:console.log("📡 未知的WebSocket消息类型:",t.type)}},nt=t=>{let e=null,a=null;return t.task_id&&t.data?(e=t.task_id,a=t.data):t.payload&&t.payload.task_id?(e=t.payload.task_id,a=t.payload.progress||t.payload.result||t.payload):t.task_id&&(e=t.task_id,a=t),{taskId:e,messageData:a}},rt=(t,e)=>{if(console.log("📈 处理进度更新:",{taskId:t,progressData:e}),console.log("🔍 详细进度数据:",JSON.stringify(e,null,2)),!e){console.warn("⚠️ 进度数据为空");return}We(t,e)},it=(t,e)=>{console.log("✅ 处理任务完成:",{taskId:t,resultData:e}),W(t),E.value.delete(t),_t(t,e),U(),f.success("文档处理完成！")},ct=(t,e)=>{console.log("❌ 处理任务失败:",{taskId:t,errorData:e}),W(t),E.value.delete(t);const a=(e==null?void 0:e.error)||(e==null?void 0:e.error_message)||"处理失败";vt(t,a),f.error(`文档处理失败: ${a}`)},ut=async()=>{try{console.log("🔧 设置全局WebSocket监听器...");const{useWebSocketStore:t}=await K(()=>import("./websocket-aa959a18.js"),["static/js/websocket-aa959a18.js","static/js/index-2c134546.js","static/css/index-5cad6ac7.css","static/js/auth-e6295339.js"]),e=t();e.off("message",ve),e.on("message",a=>{console.log("🔍 全局WebSocket消息 -> DocumentManager:",{type:a.type,task_id:a.task_id,timestamp:new Date().toISOString()}),dt(a)&&ve(a)}),e.on("connected",()=>{console.log("✅ DocumentManager: WebSocket连接已建立")}),e.on("disconnected",a=>{console.log("⚠️ DocumentManager: WebSocket连接已断开:",a)}),e.on("error",a=>{console.error("❌ DocumentManager: WebSocket错误:",a)}),console.log("✅ 全局WebSocket监听器设置完成")}catch(t){console.error("❌ 设置全局WebSocket监听器失败:",t)}},dt=t=>["progress_update","task_completed","task_failed","connection_established"].includes(t.type)?(console.log("🔍 文档相关消息类型，允许通过:",t.type),!0):t.task_id&&E.value.has(t.task_id)?(console.log("🔍 活跃任务消息，允许通过:",t.task_id),!0):(console.log("🔍 消息被过滤:",{type:t.type,task_id:t.task_id,activeTaskIds:Array.from(E.value)}),!1),pt=(t,e)=>{if(e>=100)return"complete";switch(t){case"upload":case"uploading":return"upload";case"analysis":case"analyzing":case"document_analysis":return"analysis";case"ocr":case"ocr_processing":return"ocr";case"splitting":case"text_splitting":case"chunking":return"splitting";case"indexing":case"vectorization":case"embedding":return"indexing";case"completed":case"complete":case"finished":return"complete";case"processing":default:return e<25?"upload":e<50?"analysis":e<70?"ocr":e<85?"splitting":e<100?"indexing":"complete"}},We=(t,e)=>{if(console.log("📈 统一进度更新:",{taskId:t,progressData:e}),!e||typeof e!="object"){console.warn("⚠️ 无效的进度数据:",e);return}const a=e.progress||e,u=parseFloat(a.percentage||e.percentage)||0,r=a.detail||e.detail||e.status||"处理中...",m=a.stage||e.stage||"processing",c=pt(m,u);console.log("🔄 进度信息:",{taskId:t,percentage:u,detail:r,backendStage:m,frontendStage:c}),mt(t,u,r),gt(c,u,r)},mt=(t,e,a)=>{k.value.show&&k.value.taskId===t&&(console.log("🔄 更新全局进度条:",{percentage:e,detail:a}),j(e,a))},gt=(t,e,a)=>{O.value?(console.log("🎯 更新进度对话框:",{stage:t,percentage:e,detail:a}),i.value||(console.log("🔧 创建默认的currentProcessingFile"),i.value={name:"处理中...",size:0,currentStage:t,progress:e,completed:!1,error:null,result:null}),G(t,e,a)):console.log("⚠️ 进度对话框未显示，跳过更新")},_t=(t,e)=>{console.log("🏁 文档处理完成:",{taskId:t,resultData:e}),k.value.show&&k.value.taskId===t&&(j(100,"处理完成！"),setTimeout(()=>{B()},2e3)),O.value&&i.value&&(xe(e),setTimeout(()=>{Y()},3e3))},vt=(t,e)=>{console.log("❌ 文档处理失败:",{taskId:t,errorMessage:e}),k.value.show&&k.value.taskId===t&&(j(0,e),setTimeout(()=>{B()},3e3)),O.value&&(i.value||(i.value={name:"处理失败",size:0,currentStage:"upload",progress:0,completed:!1,error:e,result:null}),G("upload",0,e))},I=v([]),P=v([]),S=v(null),fe=v(!1),ye=v(!1),he=v(!1),ae=v(!1),ne=v(!1),re=v(""),Fe=v(1),ke=v(20),Be=v([]),be=v("nodes"),ie=v(!1),Le=v([]),Ie=v([]),je=v("balanced"),ft=v(0),yt=v(1),ht=v({}),kt=v(null),bt=v({}),wt=v({});v(new Map);const O=v(!1),i=v(null),k=v({show:!1,title:"",detail:"",percentage:0,canCancel:!1,cancelCallback:null,taskId:null}),X=v(null),E=v(new Set),ce=v(!1),z=v(null),we=v([]),T=Re({filename:"",content:"",file_type:"txt"}),d=Re({documentType:"auto",chunkSize:512,chunkOverlap:32,splitStrategy:"smart",removeHeaders:!0,removeWatermark:!1,cleanText:!0,preserveFormat:!1,splittingStrategy:"auto",forceDocumentType:"auto",maxSectionLength:8e3,semanticThreshold:.6,semanticBufferSize:1}),_=Re({language:"chi_sim+eng",image_preprocess:!0,dpi:300,contrast:2,brightness:1.2,psm:3,adaptive_threshold:!0}),Ne=[{key:"fast",name:"快速模式",description:"适合清晰文档，处理速度快",settings:{dpi:200,image_preprocess:!1,contrast:1.5,brightness:1,psm:6,adaptive_threshold:!1}},{key:"balanced",name:"平衡模式",description:"速度与质量的平衡，推荐使用",settings:{dpi:300,image_preprocess:!0,contrast:2,brightness:1.2,psm:3,adaptive_threshold:!0}},{key:"quality",name:"高质量模式",description:"适合模糊或复杂文档，识别精度高",settings:{dpi:600,image_preprocess:!0,contrast:2.5,brightness:1.3,psm:3,adaptive_threshold:!0}},{key:"handwritten",name:"手写文档",description:"专门优化手写文字识别",settings:{dpi:400,image_preprocess:!0,contrast:2.2,brightness:1.1,psm:8,adaptive_threshold:!0}}],St={200:"快速",300:"标准",400:"高质量",600:"最佳"},Vt={256:"256",512:"512",1024:"1K",2048:"2K"},xt={0:"0",64:"64",128:"128",256:"256",512:"512"},Se=[{key:"upload",title:"文件上传",description:"正在上传文件到服务器...",icon:"UploadFilled"},{key:"analysis",title:"文档分析",description:"正在分析文档类型和结构...",icon:"Search"},{key:"ocr",title:"OCR处理",description:"正在进行光学字符识别...",icon:"View"},{key:"splitting",title:"智能切分",description:"正在进行文档智能切分...",icon:"Grid"},{key:"indexing",title:"建立索引",description:"正在建立文档索引...",icon:"Collection"},{key:"complete",title:"处理完成",description:"文档处理已完成",icon:"Check"}],zt="http://localhost:8002/api/v1/documents/upload-file",Tt=Ze(()=>({Authorization:`Bearer ${A()||"demo-token"}`})),$t=Ze(()=>re.value?I.value.filter(t=>t.filename.toLowerCase().includes(re.value.toLowerCase())):I.value),ue=(t,e="",a=!1,u=null,r=null)=>{k.value={show:!0,title:t,detail:e,percentage:0,canCancel:a,cancelCallback:u,taskId:r},r&&Ge(r)},j=(t,e="")=>{if(console.log("🔄 updateGlobalProgress 被调用:",{percentage:t,detail:e,show:k.value.show}),k.value.show){const a=k.value.percentage;k.value.percentage=Math.min(100,Math.max(0,t)),e&&(k.value.detail=e),console.log("✅ 全局进度已更新:",{from:a,to:k.value.percentage,detail:k.value.detail})}else console.warn("⚠️ 全局进度条未显示，无法更新")},B=()=>{k.value.show=!1,k.value.percentage=0,k.value.taskId=null,N()},Pt=()=>{k.value.canCancel&&k.value.cancelCallback&&k.value.cancelCallback(),B()},Ge=async t=>{console.log("🔄 启动进度监控，taskId:",t),N(),E.value.add(t);try{const{useWebSocketStore:e}=await K(()=>import("./websocket-aa959a18.js"),["static/js/websocket-aa959a18.js","static/js/index-2c134546.js","static/css/index-5cad6ac7.css","static/js/auth-e6295339.js"]),a=e();a.isConnected||(console.log("🔌 WebSocket未连接，尝试连接..."),await a.connect()),a.isConnected?(console.log("✅ 使用WebSocket订阅任务进度"),await Q(t)):(console.warn("⚠️ WebSocket连接失败，使用轮询模式"),He(t))}catch(e){console.error("❌ 进度监控启动失败，使用轮询模式:",e),He(t)}},He=t=>{console.log("🔄 开始进度轮询（回退模式），taskId:",t),N(),E.value.add(t);const e=async()=>{var a,u;try{console.log("📊 查询进度，taskId:",t);const r=await Cs(t);console.log("✅ 进度响应完整数据:",r),console.log("✅ 进度响应类型:",typeof r),console.log("✅ 进度响应.data:",r==null?void 0:r.data);let m=null;if(console.log("📊 原始API响应:",r),r&&r.data&&r.data.success&&r.data.status){const c=r.data.status;console.log("📊 任务状态数据:",c);let C=0,V="处理中...";c.progress?(C=c.progress.percentage||0,V=c.progress.detail||c.progress.stage||"处理中..."):c.state==="SUCCESS"?(C=100,V="处理完成"):c.state==="FAILURE"?(C=0,V="处理失败"):c.state==="PENDING"?(C=5,V="等待处理..."):c.state==="PROGRESS"&&c.result&&typeof c.result=="object"&&(C=c.result.percentage||c.result.current||0,V=c.result.detail||c.result.status||"处理中..."),m={task_id:t,percentage:C,detail:V,status:(a=c.state)==null?void 0:a.toLowerCase(),ready:c.ready,successful:c.successful,failed:c.failed,error_message:((u=c.result)==null?void 0:u.error)||c.traceback},console.log("📈 解析后的进度数据:",m)}if(m){console.log("📈 最终使用的进度数据:",m),We(t,m);const c=m.status==="completed"||m.status==="success"||m.successful===!0||m.percentage>=100,C=m.status==="failed"||m.status==="failure"||m.failed===!0,V=m.status==="cancelled"||m.status==="revoked";if(m.ready===!0||c||C||V){console.log("✅ 任务完成，状态:",m.status,"进度:",m.percentage),N(),i.value&&O.value&&(c?xe({nodes_added:m.nodes_added||0}):C&&G("upload",0,m.error_message||"处理失败")),c&&setTimeout(()=>{U()},1e3),C?f.error(m.error_message||"任务执行失败"):V?f.warning("任务已取消"):c&&f.success("文档处理完成！");return}}else console.warn("⚠️ 无法获取进度数据，响应格式:",r),(!r||!r.data||!r.data.success)&&console.log("📝 响应格式异常，可能任务已完成")}catch(r){if(console.error("❌ 获取进度失败:",r),r.response&&r.response.status===404){console.log("📝 任务不存在，可能已完成，停止轮询"),N(),setTimeout(()=>{U()},1e3);return}else console.warn("⚠️ 进度查询失败，继续尝试...")}};e(),X.value=setInterval(()=>{console.log("⏰ 定时轮询触发，taskId:",t),e()},1e3),console.log("✅ 进度轮询已启动，间隔ID:",X.value)},N=()=>{X.value&&(clearInterval(X.value),X.value=null)},Ct=t=>{const{action:e,row:a}=t;switch(e){case"download":Je(a);break;case"export":exportDetailedPreview();break;case"upload_to_rag":rs(a);break;case"delete":ns(a);break;default:console.warn("未知的操作命令:",e)}},Dt=t=>{if(t===0)return"0 B";const e=1024,a=["B","KB","MB","GB"],u=Math.floor(Math.log(t)/Math.log(e));return parseFloat((t/Math.pow(e,u)).toFixed(2))+" "+a[u]},Ve=t=>{if(!i.value)return!1;const e=Se.findIndex(u=>u.key===i.value.currentStage);return Se.findIndex(u=>u.key===t)<e||i.value.completed},Ut=t=>{if(!i.value)return t.description;if(Ve(t.key))return"已完成";if(t.key===i.value.currentStage){const e=i.value.progress||0;switch(t.key){case"upload":return e>0?`上传中... ${Math.round(e)}%`:"正在上传文件到服务器...";case"analysis":return e>0?`分析中... ${Math.round(e)}%`:"正在分析文档类型和结构...";case"ocr":return e>0?`OCR处理中... ${Math.round(e)}%`:"正在进行光学字符识别...";case"splitting":return e>0?`智能切分中... ${Math.round(e)}%`:"正在进行文档智能切分...";case"indexing":return e>0?`建立索引中... ${Math.round(e)}%`:"正在建立文档索引...";case"complete":return"文档处理已完成";default:return t.description}}return"等待处理..."},Y=()=>{O.value=!1,i.value=null,U()},Mt=()=>{i.value&&(i.value.error=null,i.value.currentStage="upload",i.value.progress=0)},Ot=t=>{i.value={name:t.name,size:t.size,currentStage:"upload",progress:0,completed:!1,error:null,result:null},O.value=!0},G=(t,e,a=null)=>{var u;console.log("🔄 updateProcessingProgress被调用:",{stage:t,progress:e,error:a,hasCurrentProcessingFile:!!i.value,currentStage:(u=i.value)==null?void 0:u.currentStage}),i.value?(i.value.currentStage=t,i.value.progress=e,a&&(i.value.error=a),console.log("✅ 进度状态已更新:",{currentStage:i.value.currentStage,progress:i.value.progress,error:i.value.error})):console.warn("⚠️ currentProcessingFile为空，无法更新进度")},xe=t=>{console.log("🏁 completeProcessing被调用:",{result:t,hasCurrentProcessingFile:!!i.value,currentProcessingFile:i.value}),i.value?(i.value.completed=!0,i.value.currentStage="complete",i.value.progress=100,i.value.result=t,console.log("✅ 处理文件状态已更新:",i.value)):console.warn("⚠️ currentProcessingFile为空，无法更新状态")},Et=t=>{const e=Ne.find(a=>a.key===t);e&&Object.assign(_,e.settings)},Rt=()=>{const t=ze();return t>=80?"success":t>=60?"warning":"danger"},At=()=>{const t=ze();return t>=80?"优秀":t>=60?"良好":"一般"},Wt=()=>{const t=ze();return t>=80?"当前配置可以获得很好的识别效果":t>=60?"建议提高DPI或启用图像预处理以获得更好效果":"建议使用高质量模式或调整图像处理参数"},ze=()=>{let t=50;return _.dpi>=400?t+=30:_.dpi>=300?t+=20:_.dpi>=200&&(t+=10),_.image_preprocess&&(t+=20),_.adaptive_threshold&&(t+=10),_.contrast>1.5&&_.brightness>1&&(t+=10),Math.min(100,t)},Z=t=>{z.value=t,ce.value=!0},Te=()=>{ce.value=!1,z.value=null,we.value=[]},Ft=t=>{switch(t.handler&&t.handler(),t.key){case"retry":Lt();break;case"refresh":U();break;case"clear_cache":It();break;case"check_connection":jt();break;default:console.warn("未知的错误操作:",t.key)}Te()},Bt=()=>{if(z.value){const t={title:z.value.title,message:z.value.message,details:z.value.details,timestamp:new Date().toISOString(),userAgent:navigator.userAgent,url:window.location.href};console.log("错误报告:",t),f.success("错误报告已发送，感谢您的反馈！")}Te()},Lt=()=>{f.info("正在重试操作...")},It=()=>{localStorage.removeItem("documentCache"),f.success("缓存已清除")},jt=async()=>{try{(await fetch("/api/health")).ok?f.success("网络连接正常"):f.error("服务器连接异常")}catch{f.error("网络连接失败")}},ee=(t,e,a=null)=>({...{upload_failed:{title:"文件上传失败",suggestions:["检查文件格式是否支持","确认文件大小不超过50MB","检查网络连接是否正常","尝试刷新页面后重新上传"],actions:[{key:"retry",label:"重试上传",type:"primary"},{key:"check_connection",label:"检查连接",type:"default"}]},network_error:{title:"网络连接错误",suggestions:["检查网络连接是否正常","确认服务器是否可访问","尝试刷新页面","检查防火墙设置"],actions:[{key:"retry",label:"重试",type:"primary"},{key:"check_connection",label:"检查连接",type:"default"}]},server_error:{title:"服务器处理错误",suggestions:["服务器可能正在维护","尝试稍后再试","如果问题持续存在，请联系管理员"],actions:[{key:"retry",label:"重试",type:"primary"},{key:"refresh",label:"刷新页面",type:"default"}],reportable:!0},validation_error:{title:"数据验证错误",suggestions:["检查输入的数据格式","确认所有必填字段已填写","检查文件类型和大小限制"],actions:[{key:"retry",label:"重新尝试",type:"primary"}]},ocr_error:{title:"OCR处理失败",suggestions:["检查图像质量是否清晰","尝试调整OCR参数设置","确认选择了正确的语言","考虑提高图像分辨率"],actions:[{key:"retry",label:"重试OCR",type:"primary"},{key:"adjust_settings",label:"调整设置",type:"default"}]}}[t]||{title:"未知错误",suggestions:["请尝试刷新页面","如果问题持续存在，请联系技术支持"],actions:[{key:"refresh",label:"刷新页面",type:"primary"}]},message:e,details:a,timestamp:new Date().toISOString()}),U=async()=>{fe.value=!0;try{const t=await fetch("http://localhost:8002/api/v1/documents/documents",{headers:{Authorization:`Bearer ${A()||"demo-token"}`}});if(t.ok){const e=await t.json();if(e.success)I.value=e.documents||[],ft.value=e.total||0,yt.value=e.total_pages||1;else throw new Error(e.message||"API请求失败")}else{const e=await t.json().catch(()=>({}));throw new Error(e.detail||`HTTP ${t.status}`)}}catch(t){console.error("获取文档列表失败:",t);const e=ee("network_error","获取文档列表失败",t.message);Z(e),I.value=[{id:1,filename:"我的简历.pdf",document_type:"resume",sections_count:12,created_at:new Date().toISOString(),file_size:1024e3,status:"completed"},{id:2,filename:"技术报告.docx",document_type:"report",sections_count:25,created_at:new Date().toISOString(),file_size:2048e3,status:"completed"}]}finally{fe.value=!1}},de=t=>({resume:"简历",paper:"论文",report:"报告",manual:"手册",novel:"小说",official:"公文",general:"通用"})[t]||"未知",qe=t=>({resume:"success",paper:"primary",report:"warning",manual:"info",novel:"danger",official:"",general:""})[t]||"",Nt=t=>({uploaded:"已上传",processing:"处理中",completed:"已完成",failed:"失败"})[t]||"未知",Gt=t=>({uploaded:"info",processing:"warning",completed:"success",failed:"danger"})[t]||"",$e=t=>new Date(t).toLocaleString(),Ht=t=>({resume:"file-resume",paper:"file-paper",report:"file-report",manual:"file-manual",novel:"file-novel",official:"file-official"})[t]||"file-default",qt=t=>{if(!(t!=null&&t.file_size))return"未知";const e=t.file_size;return e<1024?`${e} B`:e<1024*1024?`${(e/1024).toFixed(1)} KB`:`${(e/(1024*1024)).toFixed(1)} MB`},pe=()=>P.value.reduce((t,e)=>{var a;return t+(e.char_count||((a=e.content)==null?void 0:a.length)||0)},0),Pe=()=>P.value.length===0?0:Math.round(pe()/P.value.length),Ce=()=>{const t=new Set;return P.value.forEach(e=>{var a;(a=e.metadata)!=null&&a.section&&t.add(e.metadata.section)}),Array.from(t)},Jt=async t=>{try{const e=t.content||t.text||"";await navigator.clipboard.writeText(e),f.success("内容已复制到剪贴板")}catch{f.error("复制失败")}},Kt=t=>{if(console.log("🚀 开始上传文件:",t.name,"大小:",t.size,"类型:",t.type),!["application/pdf","application/vnd.openxmlformats-officedocument.wordprocessingml.document","application/vnd.openxmlformats-officedocument.spreadsheetml.sheet","application/vnd.openxmlformats-officedocument.presentationml.presentation","text/plain","text/markdown"].includes(t.type)&&!t.name.match(/\.(pdf|docx|xlsx|pptx|txt|md)$/i))return f.error("不支持的文件格式"),!1;if(t.size>50*1024*1024)return f.error("文件大小不能超过50MB"),!1;const a={document_type:d.documentType,chunk_size:d.chunkSize,chunk_overlap:d.chunkOverlap,split_strategy:d.splitStrategy,splitting_strategy:d.splittingStrategy,force_document_type:d.forceDocumentType,max_section_length:d.maxSectionLength,semantic_threshold:d.semanticThreshold,semantic_buffer_size:d.semanticBufferSize,preprocessing:{remove_headers:d.removeHeaders,remove_watermark:d.removeWatermark,clean_text:d.cleanText,preserve_format:d.preserveFormat}};return ie.value&&(a.use_ocr=!0,a.ocr_settings=JSON.stringify(_)),t.uploadParams=a,Ot(t),!0},Qt=(t,e)=>{console.log("上传成功:",t),console.log("检查task_id:",t.task_id),t.success?t.task_id?(console.log("🚀 异步处理模式：立即启动WebSocket订阅，task_id:",t.task_id),E.value.add(t.task_id),Q(t.task_id),f.success({message:`文件 ${e.name} 上传成功！正在后台处理...`,duration:2e3})):(console.log("没有task_id，直接显示完成"),f.success({message:`文件 ${e.name} 上传成功！生成了 ${t.nodes_added||0} 个节点`,duration:3e3}),xe(t),setTimeout(()=>{Y()},2e3),U()):(f.error(`文件 ${e.name} 上传失败`),Y()),Be.value=[]},Xt=(t,e)=>{var r,m;console.error("上传失败:",t),G(((r=i.value)==null?void 0:r.currentStage)||"upload",0,`文件上传失败: ${t.message||"未知错误"}`);const a=((m=t.response)==null?void 0:m.data)||t.message||"未知错误",u=ee("upload_failed",`文件 ${e.name} 上传失败`,a);Z(u)},Yt=async()=>{if(!T.filename||!T.content){const t=ee("validation_error","请填写完整的文件名和内容","文件名和内容都是必填字段");Z(t);return}he.value=!0,ss();try{const t=await fetch("http://localhost:8002/api/v1/documents/upload-text",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${A()||"demo-token"}`},body:JSON.stringify({filename:T.filename,content:T.content,file_type:T.file_type})});if(t.ok){const e=await t.json();if(e.success)f.success({message:`文本上传成功！生成了 ${e.nodes_added||0} 个节点`,duration:3e3}),os(e),es(),U(),setTimeout(()=>{Y()},2e3);else throw new Error(e.message||"上传失败")}else{const e=await t.json().catch(()=>({}));throw new Error(e.detail||`HTTP ${t.status}`)}}catch(t){console.error("文本上传失败:",t),G("upload",0,`文本上传失败: ${t.message}`);const e=ee("upload_failed","文本上传失败",t.message);Z(e)}finally{he.value=!1}},Zt=()=>{if(console.log("handlePreviewDownload 被调用"),console.log("当前 currentDocument.value:",S.value),!S.value){console.error("currentDocument.value 为空"),f.error("文档信息丢失，请重新打开预览");return}Je(S.value)},Je=async t=>{try{if(console.log("downloadDocument 被调用，参数:",t),console.log("参数类型:",typeof t),console.log("参数是否为null:",t===null),console.log("参数是否为undefined:",t===void 0),!t||!t.id){console.error("文档对象无效:",t),f.error("文档信息无效，无法下载");return}console.log("开始下载文档:",t.id,t.filename);const e=await fetch(`http://localhost:8002/api/v1/documents/documents/${t.id}/download`,{headers:{Authorization:`Bearer ${A()||"demo-token"}`}});if(console.log("下载响应状态:",e.status),e.ok){const a=await e.blob(),u=URL.createObjectURL(a),r=document.createElement("a");r.href=u,r.download=`document_${t.id}_sections.zip`,r.click(),URL.revokeObjectURL(u),f.success("文档下载成功")}else{const a=await e.text();throw console.error("下载失败响应:",a),new Error(`下载失败: ${e.status}`)}}catch(e){console.error("下载文档失败:",e),f.error(`下载文档失败: ${e.message}`)}},es=()=>{T.filename="",T.content="",T.file_type="txt"},ts=async t=>{try{const e=await fetch(`http://localhost:8002/api/v1/documents/documents/${t}/status`,{headers:{Authorization:`Bearer ${A()||"demo-token"}`}});if(e.ok){const a=await e.json();if(a.success)return kt.value=a,a}}catch(e){console.error("获取文档状态失败:",e)}return null},ss=()=>{i.value={name:T.filename,type:"text",currentStage:"upload",progress:0,message:"正在上传文本内容..."},O.value=!0},os=t=>{G("completed",100,`处理完成！生成了 ${t.nodes_added||0} 个节点`)},ls=async()=>{try{const t=await fetch("http://localhost:8002/api/v1/documents/user/statistics",{headers:{Authorization:`Bearer ${A()||"demo-token"}`}});if(t.ok){const e=await t.json();if(e.success)return wt.value=e.statistics,e.statistics}}catch(t){console.error("获取用户统计失败:",t)}return null},as=async t=>{if(console.log("previewDocument 被调用，参数 doc:",t),!t||!t.id){console.error("previewDocument: 无效的文档对象",t),f.error("无效的文档信息");return}S.value=t,console.log("设置 currentDocument.value 后:",S.value),ae.value=!0,ye.value=!0,be.value="nodes";try{const e=await fetch(`http://localhost:8002/api/v1/documents/documents/${t.id}/nodes`,{headers:{Authorization:`Bearer ${A()||"demo-token"}`}});if(e.ok){const a=await e.json();if(a.success)P.value=a.sections||[],ht.value=a.statistics||{},a.pagination&&(bt.value=a.pagination);else throw new Error(a.message||"获取节点失败")}else{const a=await e.json().catch(()=>({}));throw new Error(a.detail||`HTTP ${e.status}`)}await ts(t.id)}catch(e){console.error("获取文档节点失败:",e);const a=ee("network_error","获取文档预览失败",e.message);Z(a),P.value=[{title:"概述",content:"这是第一个文档节点的内容，包含了文档的基本信息和概述部分。这里展示了文档处理后的智能分割效果，每个节点都包含完整的语义信息。",section_index:0,char_count:65,word_count:22,metadata:{section:"概述"}},{title:"技术细节",content:"这是第二个文档节点的内容，详细描述了相关的技术细节和实现方案。通过OCR和智能分割技术，我们能够准确识别文档结构并保持内容的完整性。",section_index:1,char_count:68,word_count:23,metadata:{section:"技术细节"}},{title:"总结",content:"这是第三个文档节点的内容，总结了整个文档的要点和结论。文档处理系统支持多种格式，包括PDF、Word、Excel等常见办公文档格式。",section_index:2,char_count:64,word_count:21,metadata:{section:"总结"}}]}finally{ye.value=!1,console.log("previewDocument 完成，最终 currentDocument.value:",S.value)}},ns=async t=>{try{if(await lt.confirm(`确定要删除文档 "${t.filename}" 吗？
此操作将同时删除所有相关的分割文件。`,"确认删除",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!1}),(await fetch(`http://localhost:8002/api/v1/documents/documents/${t.id}`,{method:"DELETE",headers:{Authorization:`Bearer ${A()||"demo-token"}`}})).ok)f.success("文档删除成功"),U();else throw new Error("删除请求失败")}catch(e){e!=="cancel"&&(console.error("删除文档失败:",e),f.error("删除文档失败"))}},rs=async t=>{try{await lt.confirm(`确定要将文档 "${t.filename}" 上传到RAG知识库吗？
上传后可以在知识库中进行智能问答。`,"确认上传",{confirmButtonText:"确定上传",cancelButtonText:"取消",type:"info"});const e=await Ds(t.id),a=e.data;console.log("🔍 RAG上传API响应:",e),console.log("🔍 实际数据:",a),console.log("🔍 data.success:",a.success),console.log("🔍 data.task_id:",a.task_id),a.success?a.task_id?(ue("上传到知识库",`正在上传文档 ${t.filename} 到知识库...`,!1,null,a.task_id),Ge(a.task_id)):(ue("上传到知识库",`正在上传文档 ${t.filename} 到知识库...`,!1),j(100,"上传完成！"),f.success({message:`文档 "${t.filename}" 已成功上传到知识库！生成了 ${a.nodes_added||0} 个向量节点`,duration:3e3}),setTimeout(()=>{B()},2e3)):(ue("上传到知识库",`上传文档 ${t.filename} 失败`,!1),j(0,"上传失败"),setTimeout(()=>{B()},3e3),f.error(a.message||"上传失败"))}catch(e){e!=="cancel"?(ue("上传到知识库",`上传文档 ${t.filename} 出现异常`,!1),j(0,"上传异常"),setTimeout(()=>{B()},3e3),console.error("上传到RAG失败:",e),f.error("上传失败，请重试")):B()}},is=()=>{if(!S.value||!P.value.length){f.warning("没有可导出的内容");return}let t=`# ${S.value.filename} - 文档预览

`;t+=`**文档类型**: ${de(S.value.document_type)}
`,t+=`**节点总数**: ${P.value.length}
`,t+=`**上传时间**: ${$e(S.value.upload_time)}
`,t+=`**总字符数**: ${pe()}
`,t+=`**平均节点长度**: ${Pe()}

`,t+=`---

`,P.value.forEach((r,m)=>{var c;t+=`## 节点 ${m+1} - ${r.title||((c=r.metadata)==null?void 0:c.section)||"未分类"}

`,t+=`${r.content||r.text||""}

`,t+=`---

`}),t+=`## 文档统计

`,t+=`- 总节点数: ${P.value.length}
`,t+=`- 总字符数: ${pe()}
`,t+=`- 平均节点长度: ${Pe()}
`,t+=`- 章节数量: ${Ce().length}
`,t+=`- 章节列表: ${Ce().join(", ")}
`;const e=new Blob([t],{type:"text/markdown; charset=utf-8"}),a=URL.createObjectURL(e),u=document.createElement("a");u.href=a,u.download=`${S.value.filename}_详细预览.md`,u.click(),URL.revokeObjectURL(a),f.success("详细预览已导出")};return hs(async()=>{console.log("📄 DocumentManager 组件已挂载");const t=Us();console.log("认证状态:",t),t.hasToken||(console.warn("⚠️ 没有找到有效的认证token"),f.warning("请先登录")),await le(),ut(),U(),ls()}),ks(async()=>{console.log("📄 DocumentManager 组件即将卸载");try{const t=Array.from(E.value).map(u=>W(u));await Promise.all(t),E.value.clear(),N();const{useWebSocketStore:e}=await K(()=>import("./websocket-aa959a18.js"),["static/js/websocket-aa959a18.js","static/js/index-2c134546.js","static/css/index-5cad6ac7.css","static/js/auth-e6295339.js"]);e().off("message",ve),console.log("✅ DocumentManager 清理完成")}catch(t){console.error("❌ DocumentManager 清理失败:",t)}}),(t,e)=>{var Xe;const a=h("router-link"),u=h("el-button"),r=h("el-option"),m=h("el-select"),c=h("el-icon"),C=h("el-upload"),V=h("el-switch"),F=h("el-tooltip"),H=h("el-slider"),q=h("el-input-number"),De=h("el-collapse-item"),Ue=h("el-collapse"),Me=h("el-input"),me=h("el-form-item"),cs=h("el-form"),te=h("el-tag"),Oe=h("el-alert"),J=h("el-table-column"),ge=h("el-dropdown-item"),us=h("el-dropdown-menu"),ds=h("el-dropdown"),ps=h("el-table"),ms=h("el-pagination"),Ee=h("el-dialog"),gs=h("el-progress"),_e=h("el-descriptions-item"),_s=h("el-descriptions"),Ke=h("el-tab-pane"),vs=h("el-tabs"),Qe=bs("loading");return g(),b("div",Ms,[s("div",Os,[s("div",Es,[e[41]||(e[41]=s("div",{class:"header-brand"},[s("div",{class:"brand-logo"},"📄"),s("span",{class:"brand-text"},"文档处理中心")],-1)),s("div",Rs,[o(a,{to:"/knowledge",class:"btn-outline"},{default:n(()=>e[40]||(e[40]=[y("返回知识库")])),_:1,__:[40]})])])]),k.value.show?(g(),b("div",As,[s("div",Ws,[s("div",Fs,[s("span",Bs,p(k.value.title),1),s("span",Ls,p(k.value.detail),1)]),s("div",Is,p(Math.round(k.value.percentage))+"%",1)]),s("div",js,[s("div",{class:"progress-bar",style:ws({width:k.value.percentage+"%"})},null,4)]),s("div",Ns,[k.value.canCancel?(g(),D(u,{key:0,size:"small",type:"danger",onClick:Pt},{default:n(()=>e[42]||(e[42]=[y(" 取消 ")])),_:1,__:[42]})):x("",!0)])])):x("",!0),s("div",Gs,[s("div",Hs,[s("div",qs,[s("div",Js,[s("div",Ks,[e[44]||(e[44]=s("h3",null,"📤 文档上传",-1)),s("div",Qs,[o(u,{type:"text",size:"large",onClick:e[0]||(e[0]=l=>ne.value=!ne.value)},{default:n(()=>[y(p(ne.value?"文件上传":"文本上传"),1)]),_:1}),o(u,{type:"primary",size:"large",onClick:U},{default:n(()=>e[43]||(e[43]=[y(" 刷新列表 ")])),_:1,__:[43]})])]),s("div",Xs,[ne.value?(g(),b("div",xo,[o(cs,{model:T,"label-width":"80px"},{default:n(()=>[o(me,{label:"文件名"},{default:n(()=>[o(Me,{modelValue:T.filename,"onUpdate:modelValue":e[15]||(e[15]=l=>T.filename=l),size:"large",placeholder:"请输入文件名"},null,8,["modelValue"])]),_:1}),o(me,{label:"文件类型"},{default:n(()=>[o(m,{modelValue:T.file_type,"onUpdate:modelValue":e[16]||(e[16]=l=>T.file_type=l),size:"large"},{default:n(()=>[o(r,{label:"文本文件",value:"txt"}),o(r,{label:"Markdown",value:"md"}),o(r,{label:"其他",value:"other"})]),_:1},8,["modelValue"])]),_:1}),o(me,{label:"内容"},{default:n(()=>[o(Me,{modelValue:T.content,"onUpdate:modelValue":e[17]||(e[17]=l=>T.content=l),type:"textarea",rows:8,placeholder:"请输入文档内容..."},null,8,["modelValue"])]),_:1}),o(me,null,{default:n(()=>[o(u,{type:"primary",size:"large",onClick:Yt,loading:he.value,style:{width:"100%"}},{default:n(()=>e[62]||(e[62]=[y(" 上传文本 ")])),_:1,__:[62]},8,["loading"])]),_:1})]),_:1},8,["model"])])):(g(),b("div",Ys,[s("div",Zs,[e[45]||(e[45]=s("label",null,"文档类型",-1)),o(m,{modelValue:d.documentType,"onUpdate:modelValue":e[1]||(e[1]=l=>d.documentType=l),size:"large",placeholder:"选择文档类型"},{default:n(()=>[o(r,{label:"📄 自动识别",value:"auto"}),o(r,{label:"📝 简历",value:"resume"}),o(r,{label:"📊 报告",value:"report"}),o(r,{label:"📚 论文",value:"paper"}),o(r,{label:"📖 手册",value:"manual"}),o(r,{label:"📋 公文",value:"official"}),o(r,{label:"📄 通用文档",value:"general"})]),_:1},8,["modelValue"])]),o(C,{class:"upload-area",drag:"",action:zt,headers:Tt.value,"before-upload":Kt,"on-success":Qt,"on-error":Xt,"file-list":Be.value,multiple:""},{default:n(()=>[o(c,{class:"upload-icon"},{default:n(()=>[o(w(tt))]),_:1}),e[46]||(e[46]=s("div",{class:"upload-text"},"拖拽文件到此处或点击上传",-1)),e[47]||(e[47]=s("div",{class:"upload-tip"},"支持 PDF、Word、Excel、PowerPoint、Markdown、文本文件",-1)),e[48]||(e[48]=s("div",{class:"upload-note"},"自动识别文档类型并进行结构化处理",-1))]),_:1,__:[46,47,48]},8,["headers","file-list"]),o(Ue,{modelValue:Le.value,"onUpdate:modelValue":e[14]||(e[14]=l=>Le.value=l),class:"advanced-settings"},{default:n(()=>[o(De,{title:"🔧 高级设置",name:"advanced"},{default:n(()=>[s("div",eo,[s("div",to,[e[53]||(e[53]=s("div",{class:"section-header"},[s("h4",null,"🔄 预处理选项"),s("p",{class:"section-desc"},"选择文档预处理方式，提升内容质量")],-1)),s("div",so,[s("div",oo,[e[49]||(e[49]=s("div",{class:"option-content"},[s("span",{class:"option-label"},"去除页眉页脚"),s("span",{class:"option-desc"},"移除文档的页眉页脚信息")],-1)),o(V,{modelValue:d.removeHeaders,"onUpdate:modelValue":e[2]||(e[2]=l=>d.removeHeaders=l),size:"large"},null,8,["modelValue"])]),s("div",lo,[e[50]||(e[50]=s("div",{class:"option-content"},[s("span",{class:"option-label"},"去除水印"),s("span",{class:"option-desc"},"清除文档中的水印内容")],-1)),o(V,{modelValue:d.removeWatermark,"onUpdate:modelValue":e[3]||(e[3]=l=>d.removeWatermark=l),size:"large"},null,8,["modelValue"])]),s("div",ao,[e[51]||(e[51]=s("div",{class:"option-content"},[s("span",{class:"option-label"},"文本清理"),s("span",{class:"option-desc"},"清理多余空格和特殊字符")],-1)),o(V,{modelValue:d.cleanText,"onUpdate:modelValue":e[4]||(e[4]=l=>d.cleanText=l),size:"large"},null,8,["modelValue"])]),s("div",no,[e[52]||(e[52]=s("div",{class:"option-content"},[s("span",{class:"option-label"},"保留格式"),s("span",{class:"option-desc"},"保持原文档的格式结构")],-1)),o(V,{modelValue:d.preserveFormat,"onUpdate:modelValue":e[5]||(e[5]=l=>d.preserveFormat=l),size:"large"},null,8,["modelValue"])])])]),s("div",ro,[e[61]||(e[61]=s("div",{class:"section-header"},[s("h4",null,"✂️ 智能切割策略"),s("p",{class:"section-desc"},"选择文档切割方式，优化内容分析效果")],-1)),s("div",io,[e[59]||(e[59]=s("label",null,"切割策略",-1)),o(m,{modelValue:d.splittingStrategy,"onUpdate:modelValue":e[6]||(e[6]=l=>d.splittingStrategy=l),size:"large"},{default:n(()=>[o(r,{label:"🤖 智能自动（推荐）",value:"auto"},{default:n(()=>e[54]||(e[54]=[s("div",{class:"strategy-option"},[s("span",{class:"strategy-name"},"智能自动"),s("span",{class:"strategy-desc"},"根据文档类型自动选择最佳策略")],-1)])),_:1,__:[54]}),o(r,{label:"🧠 语义切割",value:"semantic"},{default:n(()=>e[55]||(e[55]=[s("div",{class:"strategy-option"},[s("span",{class:"strategy-name"},"语义切割"),s("span",{class:"strategy-desc"},"基于语义相似度智能分割")],-1)])),_:1,__:[55]}),o(r,{label:"📋 标题结构",value:"heading"},{default:n(()=>e[56]||(e[56]=[s("div",{class:"strategy-option"},[s("span",{class:"strategy-name"},"标题结构"),s("span",{class:"strategy-desc"},"按照文档标题层级分割")],-1)])),_:1,__:[56]}),o(r,{label:"📏 固定长度",value:"length"},{default:n(()=>e[57]||(e[57]=[s("div",{class:"strategy-option"},[s("span",{class:"strategy-name"},"固定长度"),s("span",{class:"strategy-desc"},"按照固定字符长度分割")],-1)])),_:1,__:[57]}),o(r,{label:"📄 文档类型",value:"document_type"},{default:n(()=>e[58]||(e[58]=[s("div",{class:"strategy-option"},[s("span",{class:"strategy-name"},"文档类型"),s("span",{class:"strategy-desc"},"根据文档类型特征分割")],-1)])),_:1,__:[58]})]),_:1},8,["modelValue"])]),s("div",co,[e[60]||(e[60]=s("label",null,"文档类型识别",-1)),o(m,{modelValue:d.forceDocumentType,"onUpdate:modelValue":e[7]||(e[7]=l=>d.forceDocumentType=l),size:"large"},{default:n(()=>[o(r,{label:"🔍 自动识别（推荐）",value:"auto"}),o(r,{label:"📖 手册指南",value:"manual"}),o(r,{label:"📚 小说故事",value:"novel"}),o(r,{label:"👤 个人简历",value:"resume"}),o(r,{label:"🎓 学术论文",value:"paper"}),o(r,{label:"📊 工作报告",value:"report"}),o(r,{label:"📋 公文文件",value:"official"})]),_:1},8,["modelValue"])]),s("div",uo,[s("div",po,[s("div",mo,[s("label",null,"分块大小: "+p(d.chunkSize),1),o(F,{content:"每个文本块的字符数量，影响检索精度",placement:"top"},{default:n(()=>[o(c,{class:"help-icon"},{default:n(()=>[o(w(L))]),_:1})]),_:1})]),s("div",go,[o(H,{modelValue:d.chunkSize,"onUpdate:modelValue":e[8]||(e[8]=l=>d.chunkSize=l),min:256,max:2048,step:64,marks:Vt,"show-input":!1},null,8,["modelValue"])]),s("div",_o,[o(q,{modelValue:d.chunkSize,"onUpdate:modelValue":e[9]||(e[9]=l=>d.chunkSize=l),min:256,max:2048,step:64,size:"small","controls-position":"right"},null,8,["modelValue"])])]),s("div",vo,[s("div",fo,[s("label",null,"重叠长度: "+p(d.chunkOverlap),1),o(F,{content:"相邻文本块的重叠字符数，保持上下文连贯性",placement:"top"},{default:n(()=>[o(c,{class:"help-icon"},{default:n(()=>[o(w(L))]),_:1})]),_:1})]),s("div",yo,[o(H,{modelValue:d.chunkOverlap,"onUpdate:modelValue":e[10]||(e[10]=l=>d.chunkOverlap=l),min:0,max:512,step:16,marks:xt,"show-input":!1},null,8,["modelValue"])]),s("div",ho,[o(q,{modelValue:d.chunkOverlap,"onUpdate:modelValue":e[11]||(e[11]=l=>d.chunkOverlap=l),min:0,max:512,step:16,size:"small","controls-position":"right"},null,8,["modelValue"])])])]),d.splittingStrategy==="semantic"?(g(),b("div",ko,[s("div",bo,[s("div",wo,[s("label",null,"语义阈值: "+p(d.semanticThreshold),1),o(F,{content:"语义相似度阈值，值越高分割越细",placement:"top"},{default:n(()=>[o(c,{class:"help-icon"},{default:n(()=>[o(w(L))]),_:1})]),_:1})]),s("div",So,[o(H,{modelValue:d.semanticThreshold,"onUpdate:modelValue":e[12]||(e[12]=l=>d.semanticThreshold=l),min:.3,max:.9,step:.1,"show-input":!1},null,8,["modelValue"])]),s("div",Vo,[o(q,{modelValue:d.semanticThreshold,"onUpdate:modelValue":e[13]||(e[13]=l=>d.semanticThreshold=l),min:.3,max:.9,step:.1,precision:1,size:"small","controls-position":"right"},null,8,["modelValue"])])])])):x("",!0)])])]),_:1})]),_:1},8,["modelValue"])]))])]),s("div",zo,[s("div",To,[e[63]||(e[63]=s("h3",null,"🔍 OCR设置",-1)),s("div",$o,[o(V,{modelValue:ie.value,"onUpdate:modelValue":e[18]||(e[18]=l=>ie.value=l),size:"large"},null,8,["modelValue"])])]),ie.value?(g(),b("div",Po,[s("div",Co,[e[64]||(e[64]=s("label",null,"预设配置",-1)),o(m,{modelValue:je.value,"onUpdate:modelValue":e[19]||(e[19]=l=>je.value=l),size:"large",onChange:Et},{default:n(()=>[(g(),b(se,null,oe(Ne,l=>o(r,{key:l.key,label:l.name,value:l.key},{default:n(()=>[s("div",Do,[s("span",Uo,p(l.name),1),s("span",Mo,p(l.description),1)])]),_:2},1032,["label","value"])),64))]),_:1},8,["modelValue"])]),s("div",Oo,[e[66]||(e[66]=s("h4",null,"📝 基础设置",-1)),s("div",Eo,[e[65]||(e[65]=s("label",null,"OCR语言",-1)),o(m,{modelValue:_.language,"onUpdate:modelValue":e[20]||(e[20]=l=>_.language=l),size:"large"},{default:n(()=>[o(r,{label:"🌏 中英文混合（推荐）",value:"chi_sim+eng"}),o(r,{label:"🇨🇳 简体中文",value:"chi_sim"}),o(r,{label:"🇹🇼 繁体中文",value:"chi_tra"}),o(r,{label:"🇺🇸 英文",value:"eng"}),o(r,{label:"🇯🇵 日文",value:"jpn"}),o(r,{label:"🇰🇷 韩文",value:"kor"})]),_:1},8,["modelValue"])]),s("div",Ro,[s("div",Ao,[s("label",null,"分辨率(DPI): "+p(_.dpi),1),o(F,{content:"更高的DPI可以提高识别精度，但会增加处理时间",placement:"top"},{default:n(()=>[o(c,{class:"help-icon"},{default:n(()=>[o(w(L))]),_:1})]),_:1})]),s("div",Wo,[o(H,{modelValue:_.dpi,"onUpdate:modelValue":e[21]||(e[21]=l=>_.dpi=l),min:200,max:600,step:50,marks:St,"show-input":!1},null,8,["modelValue"])]),s("div",Fo,[o(q,{modelValue:_.dpi,"onUpdate:modelValue":e[22]||(e[22]=l=>_.dpi=l),min:200,max:600,step:50,size:"small","controls-position":"right"},null,8,["modelValue"])])])]),s("div",Bo,[e[69]||(e[69]=s("h4",null,"🖼️ 图像处理",-1)),s("div",Lo,[s("div",Io,[s("label",null,[e[67]||(e[67]=y(" 图像预处理 ")),o(F,{content:"自动调整图像对比度、亮度等参数",placement:"top"},{default:n(()=>[o(c,{class:"help-icon"},{default:n(()=>[o(w(L))]),_:1})]),_:1})]),o(V,{modelValue:_.image_preprocess,"onUpdate:modelValue":e[23]||(e[23]=l=>_.image_preprocess=l),size:"large"},null,8,["modelValue"])]),s("div",jo,[s("label",null,[e[68]||(e[68]=y(" 自适应阈值 ")),o(F,{content:"根据图像内容自动调整二值化阈值",placement:"top"},{default:n(()=>[o(c,{class:"help-icon"},{default:n(()=>[o(w(L))]),_:1})]),_:1})]),o(V,{modelValue:_.adaptive_threshold,"onUpdate:modelValue":e[24]||(e[24]=l=>_.adaptive_threshold=l),size:"large"},null,8,["modelValue"])])]),_.image_preprocess?(g(),b("div",No,[s("div",Go,[s("div",Ho,[s("label",null,"对比度增强: "+p(_.contrast),1)]),s("div",qo,[o(H,{modelValue:_.contrast,"onUpdate:modelValue":e[25]||(e[25]=l=>_.contrast=l),min:1,max:3,step:.1,"show-input":!1},null,8,["modelValue"])]),s("div",Jo,[o(q,{modelValue:_.contrast,"onUpdate:modelValue":e[26]||(e[26]=l=>_.contrast=l),min:1,max:3,step:.1,precision:1,size:"small","controls-position":"right"},null,8,["modelValue"])])]),s("div",Ko,[s("div",Qo,[s("label",null,"亮度调整: "+p(_.brightness),1)]),s("div",Xo,[o(H,{modelValue:_.brightness,"onUpdate:modelValue":e[27]||(e[27]=l=>_.brightness=l),min:.8,max:1.5,step:.1,"show-input":!1},null,8,["modelValue"])]),s("div",Yo,[o(q,{modelValue:_.brightness,"onUpdate:modelValue":e[28]||(e[28]=l=>_.brightness=l),min:.8,max:1.5,step:.1,precision:1,size:"small","controls-position":"right"},null,8,["modelValue"])])])])):x("",!0)]),o(Ue,{modelValue:Ie.value,"onUpdate:modelValue":e[30]||(e[30]=l=>Ie.value=l),class:"advanced-settings"},{default:n(()=>[o(De,{title:"⚙️ 高级设置",name:"advanced"},{default:n(()=>[s("div",Zo,[s("label",null,[e[70]||(e[70]=y(" 页面分割模式 ")),o(F,{content:"控制OCR如何分析页面布局",placement:"top"},{default:n(()=>[o(c,{class:"help-icon"},{default:n(()=>[o(w(L))]),_:1})]),_:1})]),o(m,{modelValue:_.psm,"onUpdate:modelValue":e[29]||(e[29]=l=>_.psm=l),size:"large"},{default:n(()=>[o(r,{label:"自动检测页面分段（推荐）",value:3}),o(r,{label:"假设单列文本",value:6}),o(r,{label:"单个文本块",value:4}),o(r,{label:"假设为单词",value:8}),o(r,{label:"稀疏文本",value:11}),o(r,{label:"原始行",value:13})]),_:1},8,["modelValue"])])]),_:1})]),_:1},8,["modelValue"]),s("div",el,[s("div",tl,[e[71]||(e[71]=s("span",{class:"quality-label"},"预估识别质量:",-1)),o(te,{type:Rt(),size:"large"},{default:n(()=>[y(p(At()),1)]),_:1},8,["type"])]),s("div",sl,[o(Oe,{title:Wt(),type:"info",closable:!1,"show-icon":""},null,8,["title"])])])])):x("",!0)])]),s("div",ol,[s("div",ll,[s("div",al,[e[72]||(e[72]=s("h3",null,"📚 文档列表",-1)),s("div",nl,[o(Me,{modelValue:re.value,"onUpdate:modelValue":e[31]||(e[31]=l=>re.value=l),placeholder:"搜索文档...",size:"large",style:{width:"250px"},clearable:""},{prefix:n(()=>[o(c,null,{default:n(()=>[o(w(st))]),_:1})]),_:1},8,["modelValue"])])]),s("div",rl,[et((g(),D(ps,{data:$t.value,size:"large",class:"documents-table"},{default:n(()=>[o(J,{prop:"filename",label:"文件名","min-width":"250"},{default:n(({row:l})=>[s("div",il,[o(c,{class:ot(["file-icon",Ht(l.document_type)])},{default:n(()=>[o(w(Ae))]),_:2},1032,["class"]),s("span",cl,p(l.filename),1)])]),_:1}),o(J,{prop:"document_type",label:"文档类型",width:"140"},{default:n(({row:l})=>[o(te,{type:qe(l.document_type),size:"large"},{default:n(()=>[y(p(de(l.document_type)),1)]),_:2},1032,["type"])]),_:1}),o(J,{prop:"status",label:"状态",width:"100",align:"center"},{default:n(({row:l})=>[o(te,{type:Gt(l.status),size:"large"},{default:n(()=>[y(p(Nt(l.status)),1)]),_:2},1032,["type"])]),_:1}),o(J,{prop:"sections_count",label:"节点数",width:"120",align:"center"},{default:n(({row:l})=>[s("span",ul,p(l.sections_count||0),1)]),_:1}),o(J,{prop:"created_at",label:"上传时间",width:"200"},{default:n(({row:l})=>[s("span",dl,p($e(l.created_at)),1)]),_:1}),o(J,{label:"操作",width:"200",fixed:"right"},{default:n(({row:l})=>[s("div",pl,[o(u,{type:"primary",size:"default",onClick:$=>as(l),icon:w(st),class:"action-primary"},{default:n(()=>e[73]||(e[73]=[y(" 预览 ")])),_:2,__:[73]},1032,["onClick","icon"]),o(ds,{trigger:"click",onCommand:Ct},{dropdown:n(()=>[o(us,null,{default:n(()=>[o(ge,{command:{action:"download",row:l}},{default:n(()=>[o(c,null,{default:n(()=>[o(w(Ss))]),_:1}),e[75]||(e[75]=y(" 下载文档 "))]),_:2,__:[75]},1032,["command"]),o(ge,{command:{action:"export",row:l}},{default:n(()=>[o(c,null,{default:n(()=>[o(w(Ae))]),_:1}),e[76]||(e[76]=y(" 导出预览 "))]),_:2,__:[76]},1032,["command"]),o(ge,{command:{action:"upload_to_rag",row:l}},{default:n(()=>[o(c,null,{default:n(()=>[o(w(tt))]),_:1}),e[77]||(e[77]=y(" 上传至知识库 "))]),_:2,__:[77]},1032,["command"]),o(ge,{divided:"",command:{action:"delete",row:l},class:"danger-item"},{default:n(()=>[o(c,null,{default:n(()=>[o(w(Vs))]),_:1}),e[78]||(e[78]=y(" 删除文档 "))]),_:2,__:[78]},1032,["command"])]),_:2},1024)]),default:n(()=>[o(u,{size:"default",class:"action-more"},{default:n(()=>[e[74]||(e[74]=y(" 更多 ")),o(c,{class:"el-icon--right"},{default:n(()=>[o(w(xs))]),_:1})]),_:1,__:[74]})]),_:2},1024)])]),_:1})]),_:1},8,["data"])),[[Qe,fe.value]])]),s("div",ml,[I.value.length>ke.value?(g(),D(ms,{key:0,"current-page":Fe.value,"onUpdate:currentPage":e[32]||(e[32]=l=>Fe.value=l),"page-size":ke.value,"onUpdate:pageSize":e[33]||(e[33]=l=>ke.value=l),"page-sizes":[10,20,50,100],total:I.value.length,layout:"total, sizes, prev, pager, next, jumper",size:"large",background:""},null,8,["current-page","page-size","total"])):x("",!0)])])])])]),o(Ee,{modelValue:ce.value,"onUpdate:modelValue":e[35]||(e[35]=l=>ce.value=l),title:"处理错误",width:"600px","close-on-click-modal":!1},{footer:n(()=>{var l;return[s("div",xl,[o(u,{onClick:Te},{default:n(()=>e[81]||(e[81]=[y("关闭")])),_:1,__:[81]}),(l=z.value)!=null&&l.reportable?(g(),D(u,{key:0,type:"primary",onClick:Bt},{default:n(()=>e[82]||(e[82]=[y(" 报告问题 ")])),_:1,__:[82]})):x("",!0)])]}),default:n(()=>[z.value?(g(),b("div",gl,[s("div",_l,[o(c,{class:"error-icon"},{default:n(()=>[o(w(zs))]),_:1}),s("div",vl,p(z.value.title),1)]),s("div",fl,[s("div",yl,p(z.value.message),1),z.value.details?(g(),b("div",hl,[o(Ue,{modelValue:we.value,"onUpdate:modelValue":e[34]||(e[34]=l=>we.value=l)},{default:n(()=>[o(De,{title:"技术详情",name:"details"},{default:n(()=>[s("pre",kl,p(z.value.details),1)]),_:1})]),_:1},8,["modelValue"])])):x("",!0),z.value.suggestions?(g(),b("div",bl,[e[79]||(e[79]=s("h4",null,"💡 解决建议",-1)),s("ul",wl,[(g(!0),b(se,null,oe(z.value.suggestions,(l,$)=>(g(),b("li",{key:$},p(l),1))),128))])])):x("",!0),z.value.actions?(g(),b("div",Sl,[e[80]||(e[80]=s("h4",null,"🔧 可执行操作",-1)),s("div",Vl,[(g(!0),b(se,null,oe(z.value.actions,l=>(g(),D(u,{key:l.key,type:l.type||"default",onClick:$=>Ft(l)},{default:n(()=>[y(p(l.label),1)]),_:2},1032,["type","onClick"]))),128))])])):x("",!0)])])):x("",!0)]),_:1},8,["modelValue"]),o(Ee,{modelValue:O.value,"onUpdate:modelValue":e[36]||(e[36]=l=>O.value=l),title:"文档处理进度",width:"600px","close-on-click-modal":!1,"close-on-press-escape":!1,"show-close":!1},{footer:n(()=>{var l,$,R;return[s("div",Fl,[(l=i.value)!=null&&l.completed||($=i.value)!=null&&$.error?(g(),D(u,{key:0,type:"primary",onClick:Y},{default:n(()=>e[83]||(e[83]=[y(" 完成 ")])),_:1,__:[83]})):x("",!0),(R=i.value)!=null&&R.error?(g(),D(u,{key:1,onClick:Mt},{default:n(()=>e[84]||(e[84]=[y(" 重试 ")])),_:1,__:[84]})):x("",!0)])]}),default:n(()=>[i.value?(g(),b("div",zl,[s("div",Tl,[s("div",$l,[o(c,{class:"file-icon"},{default:n(()=>[o(w(Ae))]),_:1}),y(" "+p(i.value.name),1)]),s("div",Pl,p(Dt(i.value.size)),1)]),s("div",Cl,[(g(),b(se,null,oe(Se,l=>s("div",{key:l.key,class:ot(["stage-item",{active:l.key===i.value.currentStage,completed:Ve(l.key),error:i.value.error&&l.key===i.value.currentStage}])},[s("div",Dl,[Ve(l.key)?(g(),D(c,{key:0},{default:n(()=>[o(w(Ts))]),_:1})):i.value.error&&l.key===i.value.currentStage?(g(),D(c,{key:1},{default:n(()=>[o(w($s))]),_:1})):l.key===i.value.currentStage?(g(),D(c,{key:2,class:"spinning"},{default:n(()=>[o(w(Ps))]),_:1})):(g(),D(c,{key:3},{default:n(()=>[y(p(l.icon),1)]),_:2},1024))]),s("div",Ul,[s("div",Ml,p(l.title),1),s("div",Ol,p(Ut(l)),1),l.key==="ocr"&&l.key===i.value.currentStage&&i.value.progress>0?(g(),b("div",El,[o(gs,{percentage:Math.round(i.value.progress),status:i.value.error?"exception":"success","stroke-width":6},null,8,["percentage","status"])])):x("",!0)])],2)),64))]),i.value.error?(g(),b("div",Rl,[o(Oe,{title:i.value.error,type:"error",closable:!1,"show-icon":""},null,8,["title"])])):x("",!0),i.value.completed?(g(),b("div",Al,[o(Oe,{title:"文档处理完成！",type:"success",closable:!1,"show-icon":""},{default:n(()=>{var l,$,R,Ye;return[s("div",Wl,[s("p",null,"✅ 文档类型: "+p(de((l=i.value.result)==null?void 0:l.document_type)),1),s("p",null,"📄 生成节点: "+p(($=i.value.result)==null?void 0:$.nodes_added)+"个",1),s("p",null,"📚 章节数量: "+p(((Ye=(R=i.value.result)==null?void 0:R.sections)==null?void 0:Ye.length)||0)+"个",1)])]}),_:1})])):x("",!0)])):x("",!0)]),_:1},8,["modelValue"]),o(Ee,{modelValue:ae.value,"onUpdate:modelValue":e[39]||(e[39]=l=>ae.value=l),title:`文档切割预览 - ${(Xe=S.value)==null?void 0:Xe.filename}`,width:"90%",top:"3vh",class:"preview-dialog"},{footer:n(()=>[s("div",ca,[o(u,{size:"large",onClick:e[38]||(e[38]=l=>ae.value=!1)},{default:n(()=>e[90]||(e[90]=[y("关闭")])),_:1,__:[90]}),o(u,{type:"success",size:"large",onClick:Zt},{default:n(()=>e[91]||(e[91]=[y(" 下载分割文件 ")])),_:1,__:[91]}),o(u,{type:"primary",size:"large",onClick:is},{default:n(()=>e[92]||(e[92]=[y(" 导出预览 ")])),_:1,__:[92]})])]),default:n(()=>[s("div",Bl,[s("div",Ll,[o(_s,{column:4,border:"",size:"large"},{default:n(()=>[o(_e,{label:"文档类型"},{default:n(()=>{var l;return[o(te,{type:qe((l=S.value)==null?void 0:l.document_type),size:"large"},{default:n(()=>{var $;return[y(p(de(($=S.value)==null?void 0:$.document_type)),1)]}),_:1},8,["type"])]}),_:1}),o(_e,{label:"节点总数"},{default:n(()=>{var l;return[s("span",Il,p((l=S.value)==null?void 0:l.nodes_count),1)]}),_:1}),o(_e,{label:"上传时间"},{default:n(()=>{var l;return[s("span",jl,p($e((l=S.value)==null?void 0:l.upload_time)),1)]}),_:1}),o(_e,{label:"文件大小"},{default:n(()=>[s("span",Nl,p(qt(S.value)),1)]),_:1})]),_:1})]),s("div",Gl,[o(vs,{modelValue:be.value,"onUpdate:modelValue":e[37]||(e[37]=l=>be.value=l),size:"large",class:"preview-tab-container"},{default:n(()=>[o(Ke,{label:"📄 节点预览",name:"nodes"},{default:n(()=>[et((g(),b("div",Hl,[(g(!0),b(se,null,oe(P.value,(l,$)=>(g(),b("div",{key:$,class:"node-item"},[s("div",ql,[s("div",Jl,[s("span",Kl,"节点 "+p($+1),1),o(te,{size:"large",type:"info"},{default:n(()=>{var R;return[y(p(l.title||((R=l.metadata)==null?void 0:R.section)||"未分类"),1)]}),_:2},1024)]),s("div",Ql,[o(u,{type:"text",size:"small",onClick:R=>Jt(l)},{default:n(()=>e[85]||(e[85]=[y(" 复制内容 ")])),_:2,__:[85]},1032,["onClick"])])]),s("div",Xl,[s("div",Yl,p(l.content||l.text||"暂无内容"),1)])]))),128))])),[[Qe,ye.value]])]),_:1}),o(Ke,{label:"📊 统计信息",name:"stats"},{default:n(()=>[s("div",Zl,[s("div",ea,[s("div",ta,[s("div",sa,p(P.value.length),1),e[86]||(e[86]=s("div",{class:"stat-label"},"总节点数",-1))]),s("div",oa,[s("div",la,p(pe()),1),e[87]||(e[87]=s("div",{class:"stat-label"},"总字符数",-1))]),s("div",aa,[s("div",na,p(Pe()),1),e[88]||(e[88]=s("div",{class:"stat-label"},"平均节点长度",-1))]),s("div",ra,[s("div",ia,p(Ce().length),1),e[89]||(e[89]=s("div",{class:"stat-label"},"章节数量",-1))])])])]),_:1})]),_:1},8,["modelValue"])])])]),_:1},8,["modelValue","title"])])}}},pa=fs(ua,[["__scopeId","data-v-dac54741"]]);export{pa as default};
