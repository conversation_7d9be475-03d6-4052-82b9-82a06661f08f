#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
离线环境验证测试
验证所有AI模型在完全离线环境下的加载和运行
"""

import os
import sys
import socket
import subprocess
import time
from pathlib import Path
from contextlib import contextmanager
from typing import List, Dict, Any

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class OfflineEnvironmentValidator:
    """离线环境验证器"""
    
    def __init__(self):
        self.blocked_hosts = [
            'huggingface.co',
            'hf.co',
            'github.com',
            'raw.githubusercontent.com',
            'cdn.huggingface.co',
            'datasets-server.huggingface.co'
        ]
        self.original_getaddrinfo = socket.getaddrinfo
        self.network_blocked = False
        
    @contextmanager
    def block_network_access(self):
        """阻止网络访问的上下文管理器"""
        print("🚫 阻止网络访问...")
        
        def blocked_getaddrinfo(host, port, family=0, type=0, proto=0, flags=0):
            # 检查是否是被阻止的主机
            for blocked_host in self.blocked_hosts:
                if blocked_host in host:
                    raise socket.gaierror(f"Network access blocked for {host}")
            
            # 允许本地连接
            if host in ['localhost', '127.0.0.1', '0.0.0.0']:
                return self.original_getaddrinfo(host, port, family, type, proto, flags)
            
            # 阻止其他外部连接
            raise socket.gaierror(f"Network access blocked for {host}")
        
        # 替换socket.getaddrinfo
        socket.getaddrinfo = blocked_getaddrinfo
        self.network_blocked = True
        
        try:
            yield
        finally:
            # 恢复原始函数
            socket.getaddrinfo = self.original_getaddrinfo
            self.network_blocked = False
            print("✅ 网络访问已恢复")
    
    def check_offline_environment_variables(self) -> bool:
        """检查离线环境变量"""
        print("🔍 检查离线环境变量...")
        
        required_vars = {
            'HF_HUB_OFFLINE': '1',
            'HF_DATASETS_OFFLINE': '1',
            'TRANSFORMERS_OFFLINE': '1',
            'HF_HUB_DISABLE_TELEMETRY': '1',
            'TOKENIZERS_PARALLELISM': 'false'
        }
        
        all_set = True
        for var, expected_value in required_vars.items():
            actual_value = os.environ.get(var)
            if actual_value != expected_value:
                print(f"❌ 环境变量 {var} 应该是 '{expected_value}'，实际是 '{actual_value}'")
                all_set = False
            else:
                print(f"✅ {var} = {actual_value}")
        
        return all_set
    
    def test_funasr_offline_loading(self) -> bool:
        """测试FunASR离线加载"""
        print("\n🎯 测试FunASR离线加载...")
        
        try:
            # 设置离线环境变量
            os.environ.update({
                'HF_HUB_OFFLINE': '1',
                'HF_DATASETS_OFFLINE': '1',
                'TRANSFORMERS_OFFLINE': '1'
            })
            
            from backend.utils.audio.optimized_funasr_manager import OptimizedFunASRManager
            
            # 创建管理器
            manager = OptimizedFunASRManager()
            
            # 检查模型路径
            model_path = Path("models/model_dir")
            if not model_path.exists():
                print(f"⚠️ 模型路径不存在: {model_path}")
                print("   请确保已下载FunASR模型到本地")
                return False
            
            print("✅ FunASR离线配置验证通过")
            return True
            
        except ImportError as e:
            print(f"❌ FunASR模块导入失败: {e}")
            return False
        except Exception as e:
            print(f"❌ FunASR离线测试失败: {e}")
            return False
    
    def test_transformers_offline_loading(self) -> bool:
        """测试Transformers离线加载"""
        print("\n🤖 测试Transformers离线加载...")

        try:
            from backend.utils.audio.speech_recognition_core import SpeechRecognitionManager

            # 创建语音识别管理器
            manager = SpeechRecognitionManager()

            print("✅ Transformers离线配置验证通过")
            return True

        except ImportError as e:
            print(f"❌ Transformers模块导入失败: {e}")
            return False
        except Exception as e:
            print(f"❌ Transformers离线测试失败: {e}")
            return False
    
    def test_speaker_recognition_offline(self) -> bool:
        """测试说话人识别离线模式"""
        print("\n👥 测试说话人识别离线模式...")
        
        try:
            from backend.utils.audio.speaker_recognition import SpeakerRecognition
            
            # 创建说话人识别实例
            recognizer = SpeakerRecognition()
            
            print("✅ 说话人识别离线配置验证通过")
            return True
            
        except ImportError as e:
            print(f"❌ 说话人识别模块导入失败: {e}")
            return False
        except Exception as e:
            print(f"❌ 说话人识别离线测试失败: {e}")
            return False
    
    def test_network_isolation(self) -> bool:
        """测试网络隔离下的模型加载"""
        print("\n🌐 测试网络隔离下的模型加载...")
        
        try:
            with self.block_network_access():
                # 在网络阻止的情况下测试模型加载
                success = True
                
                # 测试FunASR
                try:
                    from backend.utils.audio.optimized_funasr_manager import OptimizedFunASRManager
                    manager = OptimizedFunASRManager()
                    print("✅ FunASR在网络隔离下加载成功")
                except Exception as e:
                    print(f"❌ FunASR在网络隔离下加载失败: {e}")
                    success = False
                
                # 测试Transformers
                try:
                    from backend.utils.audio.speech_recognition_core import SpeechRecognitionManager
                    manager = SpeechRecognitionManager()
                    print("✅ Transformers在网络隔离下加载成功")
                except Exception as e:
                    print(f"❌ Transformers在网络隔离下加载失败: {e}")
                    success = False
                
                return success
                
        except Exception as e:
            print(f"❌ 网络隔离测试失败: {e}")
            return False
    
    def check_model_files_exist(self) -> bool:
        """检查本地模型文件是否存在"""
        print("\n📁 检查本地模型文件...")
        
        model_paths = [
            "models/model_dir",
            "models/model_dir/model.py",
            "models/model_dir/config.yaml"
        ]
        
        all_exist = True
        for path in model_paths:
            full_path = Path(path)
            if full_path.exists():
                print(f"✅ {path} 存在")
            else:
                print(f"❌ {path} 不存在")
                all_exist = False
        
        return all_exist
    
    def test_offline_task_execution(self) -> bool:
        """测试离线任务执行"""
        print("\n⚙️ 测试离线任务执行...")

        try:
            # 导入任务模块
            from backend.tasks.audio_processing_tasks import meeting_transcription_task

            # 创建模拟任务参数
            task_params = {
                'file_paths': [],  # 空文件列表，只测试初始化
                'language': 'auto',
                'output_format': 'detailed',
                'include_timestamps': True,
                'speaker_labeling': True
            }

            print("✅ 离线任务模块导入成功")
            return True

        except ImportError as e:
            print(f"❌ 任务模块导入失败: {e}")
            return False
        except Exception as e:
            print(f"❌ 离线任务测试失败: {e}")
            return False
    
    def run_validation(self) -> bool:
        """运行完整的离线验证"""
        print("🔒 开始离线环境验证")
        print("=" * 60)
        
        tests = [
            ("环境变量检查", self.check_offline_environment_variables),
            ("本地模型文件检查", self.check_model_files_exist),
            ("FunASR离线加载", self.test_funasr_offline_loading),
            ("Transformers离线加载", self.test_transformers_offline_loading),
            ("说话人识别离线模式", self.test_speaker_recognition_offline),
            ("离线任务执行", self.test_offline_task_execution),
            ("网络隔离测试", self.test_network_isolation)
        ]
        
        results = []
        for test_name, test_func in tests:
            print(f"\n🧪 执行测试: {test_name}")
            try:
                result = test_func()
                results.append((test_name, result))
                if result:
                    print(f"✅ {test_name} 通过")
                else:
                    print(f"❌ {test_name} 失败")
            except Exception as e:
                print(f"❌ {test_name} 异常: {e}")
                results.append((test_name, False))
        
        # 汇总结果
        print("\n📊 验证结果汇总:")
        print("=" * 60)
        
        passed = 0
        total = len(results)
        
        for test_name, result in results:
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\n总计: {passed}/{total} 个测试通过")
        
        if passed == total:
            print("🎉 所有离线验证测试通过！")
            return True
        else:
            print("⚠️ 部分测试失败，请检查离线配置")
            return False


def main():
    """主函数"""
    validator = OfflineEnvironmentValidator()
    success = validator.run_validation()
    
    if success:
        print("\n🔒 离线环境验证完成，系统可以在完全离线环境下运行！")
        sys.exit(0)
    else:
        print("\n❌ 离线环境验证失败，请检查配置！")
        sys.exit(1)


if __name__ == "__main__":
    main()
