#!/usr/bin/env python3
"""
VAD语音活动检测API测试脚本
"""

import requests
import json
import time
import sys
import os

# 配置
BASE_URL = "http://localhost:8002"
LOGIN_URL = f"{BASE_URL}/api/v1/auth/login"
VAD_URL = f"{BASE_URL}/api/v1/speech/vad-detection"
TASK_STATUS_URL = f"{BASE_URL}/api/v1/tasks"

# 测试用户凭据
USERNAME = "admin"
PASSWORD = "admin123"

def login():
    """登录获取token"""
    try:
        response = requests.post(LOGIN_URL, json={
            "username": USERNAME,
            "password": PASSWORD
        })
        
        if response.status_code == 200:
            data = response.json()
            token = data.get("access_token")
            print(f"✅ 登录成功，获取token: {token[:20]}...")
            return token
        else:
            print(f"❌ 登录失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ 登录异常: {e}")
        return None

def get_audio_files(token):
    """获取音频文件列表"""
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(f"{BASE_URL}/api/v1/audio/", headers=headers)

        if response.status_code == 200:
            files = response.json()
            print(f"✅ 获取到 {len(files)} 个音频文件")
            for i, file in enumerate(files):
                if i >= 3:  # 只显示前3个文件
                    break
                print(f"  - ID: {file['id']}, 文件名: {file['name']}")
            return files
        else:
            print(f"❌ 获取文件列表失败: {response.status_code} - {response.text}")
            return []
    except Exception as e:
        print(f"❌ 获取文件列表异常: {e}")
        return []

def submit_vad_task(token, file_ids, config=None):
    """提交VAD检测任务"""
    try:
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "file_ids": file_ids,
            "config": config or {
                "merge_length_s": 15,
                "min_speech_duration": 0.5,
                "max_speech_duration": 60,
                "threshold": 0.5
            }
        }
        
        print(f"🚀 提交VAD检测任务...")
        print(f"   文件ID: {file_ids}")
        print(f"   配置: {json.dumps(payload['config'], indent=2)}")
        
        response = requests.post(VAD_URL, headers=headers, json=payload)
        
        if response.status_code == 200:
            data = response.json()
            task_id = data.get("task_id")
            print(f"✅ VAD任务提交成功，任务ID: {task_id}")
            return task_id
        else:
            print(f"❌ VAD任务提交失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ VAD任务提交异常: {e}")
        return None

def check_task_status(token, task_id):
    """检查任务状态"""
    try:
        headers = {"Authorization": f"Bearer {token}"}
        response = requests.get(f"{TASK_STATUS_URL}/{task_id}", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            status = data.get("status", "unknown")
            progress = data.get("progress", 0)
            message = data.get("message", "")
            
            print(f"📊 任务状态: {status}, 进度: {progress}%, 消息: {message}")
            return data
        else:
            print(f"❌ 获取任务状态失败: {response.status_code} - {response.text}")
            return None
    except Exception as e:
        print(f"❌ 获取任务状态异常: {e}")
        return None

def wait_for_completion(token, task_id, timeout=300):
    """等待任务完成"""
    print(f"⏳ 等待任务完成...")
    start_time = time.time()
    
    while time.time() - start_time < timeout:
        task_data = check_task_status(token, task_id)
        if not task_data:
            break
            
        status = task_data.get("status", "unknown")
        
        if status in ["completed", "success"]:
            print(f"🎉 任务完成！")
            return task_data
        elif status in ["failed", "error"]:
            print(f"❌ 任务失败！")
            return task_data
        
        time.sleep(2)  # 每2秒检查一次
    
    print(f"⏰ 任务等待超时")
    return None

def upload_test_file(token):
    """上传测试音频文件"""
    try:
        # 使用现有的测试音频文件
        test_file_path = r"D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\resource\对话.mp3"

        if not os.path.exists(test_file_path):
            print(f"❌ 测试文件不存在: {test_file_path}")
            return None

        headers = {"Authorization": f"Bearer {token}"}

        with open(test_file_path, 'rb') as f:
            files = {"file": ("对话.mp3", f, "audio/mp3")}

            print(f"📤 上传测试文件: {test_file_path}")
            response = requests.post(f"{BASE_URL}/api/v1/audio/upload", files=files, headers=headers)

            if response.status_code == 200:
                data = response.json()
                file_id = data.get("file_id")
                print(f"✅ 文件上传成功，文件ID: {file_id}")
                return file_id
            else:
                print(f"❌ 文件上传失败: {response.status_code} - {response.text}")
                return None
    except Exception as e:
        print(f"❌ 文件上传异常: {e}")
        return None

def main():
    """主函数"""
    print("🎵 VAD语音活动检测API测试")
    print("=" * 50)

    # 1. 登录
    token = login()
    if not token:
        sys.exit(1)

    # 2. 获取音频文件
    files = get_audio_files(token)

    # 3. 如果没有文件，先上传一个
    if not files:
        print("📁 没有音频文件，先上传测试文件...")
        file_id = upload_test_file(token)
        if not file_id:
            sys.exit(1)
        print(f"\n🎯 使用上传的测试文件 (ID: {file_id})")
    else:
        # 使用现有的第一个文件
        test_file = files[0]
        file_id = str(test_file['id'])
        print(f"\n🎯 选择测试文件: {test_file['name']} (ID: {file_id})")

    # 4. 提交VAD任务
    task_id = submit_vad_task(token, [file_id])
    if not task_id:
        sys.exit(1)

    # 5. 等待任务完成
    result = wait_for_completion(token, task_id)
    if result:
        print(f"\n📋 最终结果:")
        print(json.dumps(result, indent=2, ensure_ascii=False))

    print("\n✅ VAD测试完成")

if __name__ == "__main__":
    main()
