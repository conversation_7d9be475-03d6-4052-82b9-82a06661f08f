# 后端架构分析

## 核心框架
- **Web框架**: FastAPI
- **数据库**: SQLite (speech_platform.db)
- **ORM**: SQLAlchemy
- **任务队列**: Celery + Redis
- **认证**: JWT Token

## 目录结构
```
backend/
├── api/v1/endpoints/     # API路由端点
├── core/                 # 核心配置和工具
├── models/               # 数据库模型
├── services/             # 业务逻辑服务
├── tasks/                # Celery异步任务
├── schemas/              # Pydantic数据模型
├── utils/                # 工具模块
│   └── audio/           # 音频处理工具
└── main.py              # 应用入口
```

## 主要API端点
- **认证模块** (`/auth`): 登录、注册、token刷新
- **音频处理** (`/audio`): 文件上传、批量处理、任务管理
- **语音处理** (`/speech`): VAD检测、语音识别、说话人识别、会议转录
- **知识库** (`/knowledge`): RAG查询、文档管理
- **文档管理** (`/documents`): 文档上传、OCR处理
- **系统管理** (`/system`): 系统状态、资源监控
- **任务管理** (`/tasks`): 任务查询、重试、统计

## 核心服务组件

### 音频处理服务
- **语音识别管理器**: SpeechRecognitionManager (speech_recognition_core.py)
- **说话人识别**: SpeakerRecognition (speaker_recognition.py)
- **FunASR优化管理器**: OptimizedFunASRManager (optimized_funasr_manager.py)
- **音频工具**: 音频预处理、VAD检测、格式转换

### 任务管理服务
- **GPU管理器**: 统一GPU资源分配和释放
- **进度服务**: WebSocket实时进度推送 (EnhancedProgressService)
- **并发控制**: 任务队列限制和资源管理
- **错误处理**: 统一异常处理和恢复机制
- **资源监控**: 系统资源实时监控

### 离线模型管理
- **模型路径管理**: 统一的模型路径配置和验证
- **离线环境设置**: 完全离线运行的环境变量配置
- **模型加载优化**: 本地化模型加载，避免网络依赖

## 数据库模型
- **用户模型**: User (用户管理)
- **音频模型**: AudioFile, ProcessingResult, SpeakerProfile
- **文档模型**: ManagedDocument, DocumentSection
- **任务模型**: TaskRecord, TaskProgressLog
- **系统模型**: SystemConfig, SystemLog, SystemMetrics

## 关键技术组件

### 音频处理核心
- **VAD检测**: fsmn_vad_zh模型，完全离线配置
- **语音识别**: SenseVoiceSmall模型，支持多语言
- **说话人识别**: CAM++模型，无监督聚类
- **会议转录**: 集成VAD、ASR、说话人识别的完整流程

### 任务队列架构
- **Celery配置**: 多线程模式，4个工作线程
- **队列分类**: document_processing, vectorization, ocr_processing
- **任务监控**: 实时进度跟踪和状态更新
- **错误恢复**: 自动重试和故障转移机制

### WebSocket实时通信
- **进度推送**: 任务执行过程中的实时进度更新
- **状态通知**: 任务状态变化的即时通知
- **完成通知**: 任务完成后的自动通知机制
- **错误推送**: 异常情况的实时错误消息

## 最新架构优化

### 离线模型配置系统
- **环境变量管理**: 统一设置HF_HUB_OFFLINE等离线变量
- **模型路径验证**: 任务执行前验证所有模型路径有效性
- **本地化加载**: 完全消除网络依赖，提升加载速度

### 会议转录算法优化
- **文本分配算法**: 修复文本-时间-说话人三元组关联问题
- **时间对齐**: 基于VAD片段的精确时间戳对齐
- **质量验证**: 文本质量验证和异常处理机制

### 性能优化成果
- **处理速度**: 会议转录处理时间从15.5秒降至3.8秒
- **模型加载**: 离线配置后模型加载时间显著减少
- **内存优化**: GPU内存使用效率提升
- **并发能力**: 多任务并发处理能力增强

## 部署和运维

### 服务启动顺序
1. Redis服务 (Docker容器)
2. 后端FastAPI服务 (端口8002)
3. Celery Worker (多线程模式)
4. 前端Vue服务 (端口3000)

### 监控和日志
- **应用日志**: backend/logs/app.log
- **Worker日志**: worker_safe.log
- **系统日志**: logs/system.log
- **性能监控**: 实时资源使用监控

### 错误处理机制
- **任务级错误**: 单个任务失败不影响其他任务
- **系统级错误**: 自动恢复和重启机制
- **用户友好**: 错误信息友好化和操作指引