#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""系统环境验证脚本"""
import os
import sys
from datetime import datetime

print("🚀 系统集成环境验证")
print(f"⏰ 时间: {datetime.now()}")
print("="*50)

# 1. 基础环境
print("\n📋 基础环境检查")
print(f"✅ Python版本: {sys.version}")
print(f"✅ 工作目录: {os.getcwd()}")

# 2. 核心依赖
print("\n📋 核心依赖检查")
packages = ["streamlit", "numpy", "torch", "funasr", "chromadb"]
success = 0
for pkg in packages:
    try:
        __import__(pkg)
        print(f"✅ {pkg}: 可用")
        success += 1
    except:
        print(f"❌ {pkg}: 不可用")

# 3. 自定义模块
print("\n📋 自定义模块检查")
modules = [
    "utils.monitoring_components",
    "utils.ui_exception_handler", 
    "utils.system_health_monitor"
]
module_success = 0
for mod in modules:
    try:
        __import__(mod)
        print(f"✅ {mod}: 可用")
        module_success += 1
    except Exception as e:
        print(f"❌ {mod}: 不可用 - {str(e)[:50]}")

# 4. 文件结构
print("\n📋 文件结构检查")
files = ["Home.py", "requirements.txt", "utils/", "pages/"]
file_success = 0
for f in files:
    if os.path.exists(f):
        print(f"✅ {f}: 存在")
        file_success += 1
    else:
        print(f"❌ {f}: 不存在")

# 总结
print("\n📊 验证结果总结")
print(f"依赖包: {success}/{len(packages)}")
print(f"自定义模块: {module_success}/{len(modules)}")  
print(f"文件结构: {file_success}/{len(files)}")

total_score = (success + module_success + file_success) / (len(packages) + len(modules) + len(files))
if total_score >= 0.8:
    print("🎉 环境验证通过!")
else:
    print("⚠️ 环境验证失败，需要修复")
    
print(f"\n总体得分: {total_score:.1%}") 