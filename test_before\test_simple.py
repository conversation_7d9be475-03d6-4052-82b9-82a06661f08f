import os
import sys

print("Python路径:", sys.executable)
print("当前目录:", os.getcwd())
print("chroma_db存在:", os.path.exists("./chroma_db"))

# 列出当前目录的所有文件夹
dirs = []
for item in os.listdir('.'):
    if os.path.isdir(item):
        dirs.append(item)
        
print("所有文件夹:", dirs)

# 搜索任何包含chroma的文件或文件夹
print("\n搜索chroma相关:")
for root, dirs_in_root, files in os.walk('.'):
    for d in dirs_in_root:
        if 'chroma' in d.lower():
            print(f"找到目录: {os.path.join(root, d)}")
    for f in files:
        if 'chroma' in f.lower():
            print(f"找到文件: {os.path.join(root, f)}")

print("\n测试chromadb导入:")
try:
    import chromadb
    print("chromadb导入成功")
    
    # 尝试创建客户端
    client = chromadb.PersistentClient(path="./data/chroma_db")
    collections = client.list_collections()
    print(f"collections数量: {len(collections)}")
    
    if collections:
        for col in collections:
            print(f"集合: {col.name}, 文档数: {col.count()}")
            
except Exception as e:
    print(f"chromadb测试失败: {e}") 