# ===========================================
# 语音处理智能平台 - 环境配置文件
# ===========================================

# ===========================================
# 服务器配置
# ===========================================
# 后端API服务器配置
BACKEND_HOST=0.0.0.0
BACKEND_PORT=8000
BACKEND_DEBUG=false
BACKEND_RELOAD=false

# 前端服务器配置
FRONTEND_HOST=0.0.0.0
FRONTEND_PORT=3000

# 跨域配置
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# ===========================================
# 数据库配置
# ===========================================
# SQLite数据库配置
DATABASE_URL=sqlite:///./data/speech_platform.db
DATABASE_ECHO=false

# ===========================================
# AI模型API配置
# ===========================================
# OpenAI配置
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_MODEL=gpt-3.5-turbo

# Ollama配置
OLLAMA_BASE_URL=http://localhost:11434
OLLAMA_MODEL=llama2

# Anthropic配置
ANTHROPIC_API_KEY=your_anthropic_api_key_here
ANTHROPIC_MODEL=claude-3-sonnet-20240229

# Google配置
GOOGLE_API_KEY=your_google_api_key_here
GOOGLE_MODEL=gemini-pro

# ===========================================
# 语音处理模型配置
# ===========================================
# 模型基础路径
MODELS_BASE_PATH=./models

# VAD模型配置
VAD_MODEL_PATH=./models/speech_fsmn_vad_zh-cn-16k-common-pytorch
VAD_MODEL_TYPE=fsmn_vad
VAD_USE_GPU=false

# 语音识别模型配置
ASR_MODEL_PATH=./models/speech_seaco_paraformer_large_asr_nat-zh-cn-16k-common-vocab8404-pytorch
ASR_MODEL_TYPE=paraformer
ASR_USE_GPU=false
ASR_LANGUAGE=auto
ASR_USE_ITN=true

# 说话人识别模型配置
SPEAKER_MODEL_PATH=./models/speech_campplus_sv_zh-cn_16k-common
SPEAKER_MODEL_TYPE=campplus
SPEAKER_USE_GPU=false

# SenseVoice模型配置
SENSEVOICE_MODEL_PATH=./models/SenseVoiceSmall
SENSEVOICE_USE_GPU=false

# ===========================================
# 音频处理配置
# ===========================================
# 音频文件配置
AUDIO_UPLOAD_PATH=./data/uploads
AUDIO_MAX_SIZE=100MB
AUDIO_ALLOWED_FORMATS=wav,mp3,m4a,flac,aac

# 音频预处理配置
AUDIO_TARGET_SAMPLE_RATE=16000
AUDIO_TARGET_DB=-20
AUDIO_DENOISE_METHOD=spectral_gating

# VAD处理配置
VAD_THRESHOLD=0.5
VAD_MIN_SILENCE_DURATION=0.3
VAD_MIN_SPEECH_DURATION=0.1

# ===========================================
# 批量处理配置
# ===========================================
# 并行处理配置
MAX_WORKERS=4
BATCH_SIZE=10
ENABLE_GPU_ACCELERATION=false

# 缓存配置
ENABLE_CACHE=true
CACHE_SIZE=1000
CACHE_TTL=3600

# ===========================================
# 安全配置
# ===========================================
# JWT配置
JWT_SECRET_KEY=your_jwt_secret_key_here
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30

# 文件上传安全
MAX_UPLOAD_SIZE=100MB
ALLOWED_EXTENSIONS=wav,mp3,m4a,flac,aac,pdf,txt,docx

# ===========================================
# 日志配置
# ===========================================
# 日志级别
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# 日志文件配置
LOG_FILE_PATH=./logs/app.log
LOG_MAX_SIZE=10MB
LOG_BACKUP_COUNT=5

# ===========================================
# Redis配置 (可选)
# ===========================================
# Redis缓存配置
REDIS_URL=redis://localhost:6379/0
REDIS_ENABLED=false

# ===========================================
# 监控配置
# ===========================================
# 性能监控
ENABLE_MONITORING=true
METRICS_ENDPOINT=/metrics

# 健康检查
HEALTH_CHECK_ENDPOINT=/health

# ===========================================
# Docker配置
# ===========================================
# Docker环境标识
DOCKER_ENV=false

# 容器内路径
CONTAINER_MODELS_PATH=/app/models
CONTAINER_DATA_PATH=/app/data
CONTAINER_LOGS_PATH=/app/logs
