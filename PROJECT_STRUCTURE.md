# 项目结构说明

## 📁 目录结构

```
my_notebook_version_0.1.0/
├── 📁 frontend/                    # Vue 3 前端应用
│   ├── 📁 src/
│   │   ├── 📁 components/          # Vue组件
│   │   ├── 📁 views/              # 页面视图
│   │   ├── 📁 router/             # 路由配置
│   │   ├── 📁 stores/             # Pinia状态管理
│   │   ├── 📁 utils/              # 工具函数
│   │   └── 📁 assets/             # 静态资源
│   ├── 📄 package.json            # 前端依赖配置
│   ├── 📄 vite.config.js          # Vite构建配置
│   └── 📄 index.html              # 入口HTML
│
├── 📁 backend/                     # FastAPI 后端服务
│   ├── 📁 api/                    # API路由
│   │   └── 📁 v1/
│   │       ├── 📁 endpoints/      # API端点
│   │       └── 📄 api.py          # API路由汇总
│   ├── 📁 core/                   # 核心配置
│   │   ├── 📄 database.py         # 数据库配置
│   │   ├── 📄 security.py         # 安全认证
│   │   └── 📄 config.py           # 应用配置
│   ├── 📁 models/                 # 数据模型
│   │   ├── 📄 audio.py            # 音频相关模型
│   │   ├── 📄 document.py         # 文档相关模型
│   │   └── 📄 user.py             # 用户模型
│   ├── 📁 schemas/                # Pydantic模式
│   ├── 📁 services/               # 业务逻辑服务
│   ├── 📁 tasks/                  # Celery异步任务
│   │   ├── 📄 audio_processing_tasks.py  # 音频处理任务
│   │   ├── 📄 document_tasks.py          # 文档处理任务
│   │   └── 📄 vectorization_tasks.py     # 向量化任务
│   ├── 📁 utils/                  # 工具模块
│   │   └── 📁 audio/              # 音频处理工具
│   │       ├── 📄 optimized_funasr_manager.py    # FunASR管理器
│   │       ├── 📄 speech_recognition_core.py     # 语音识别核心
│   │       └── 📄 enhanced_audio_processor.py    # 音频预处理
│   ├── 📄 main.py                 # FastAPI应用入口
│   ├── 📄 celery_app.py           # Celery应用配置
│   └── 📄 requirements.txt        # Python依赖
│
├── 📁 models/                      # AI模型文件
│   ├── 📁 SenseVoiceSmall/        # 语音识别模型
│   ├── 📁 fsmn_vad_zh/           # VAD检测模型
│   ├── 📁 cam++/                 # 说话人识别模型
│   └── 📁 Qwen3-Reranker-0.6B/   # 重排序模型
│
├── 📁 data/                       # 数据存储
│   ├── 📄 speech_platform.db     # SQLite数据库
│   └── 📁 uploads/               # 上传文件存储
│
├── 📁 chroma_db/                  # ChromaDB向量数据库
├── 📁 logs/                       # 日志文件
├── 📁 test/                       # 测试文件
├── 📁 scripts/                    # 脚本工具
├── 📁 docs/                       # 文档
│
├── 📄 README.md                   # 项目说明
├── 📄 requirements.txt            # 根目录依赖
├── 📄 start_worker_windows.py     # Windows Celery启动器
├── 📄 docker-compose.yml          # Docker编排配置
└── 📄 .env                        # 环境变量配置
```

## 🔧 核心模块详解

### Frontend 前端架构

#### 🎨 组件结构
```
src/components/
├── 📁 common/                     # 通用组件
│   ├── 📄 Header.vue             # 页面头部
│   ├── 📄 Sidebar.vue            # 侧边栏
│   └── 📄 Footer.vue             # 页面底部
├── 📁 audio/                     # 音频处理组件
│   ├── 📄 AudioUpload.vue        # 音频上传
│   ├── 📄 AudioPlayer.vue        # 音频播放器
│   ├── 📄 RecordingPanel.vue     # 录音面板
│   └── 📄 ProcessingResults.vue  # 处理结果展示
├── 📁 document/                  # 文档处理组件
└── 📁 rag/                       # RAG知识库组件
```

#### 🛣️ 路由配置
```javascript
// src/router/index.js
const routes = [
  { path: '/', component: Dashboard },
  { path: '/audio-center', component: AudioCenter },
  { path: '/document-center', component: DocumentCenter },
  { path: '/rag-center', component: RAGCenter },
  { path: '/results', component: Results },
  { path: '/settings', component: Settings }
]
```

#### 🗄️ 状态管理
```javascript
// src/stores/
├── 📄 auth.js                    # 认证状态
├── 📄 audio.js                   # 音频处理状态
├── 📄 document.js                # 文档处理状态
├── 📄 rag.js                     # RAG状态
└── 📄 system.js                  # 系统状态
```

### Backend 后端架构

#### 🌐 API结构
```
backend/api/v1/endpoints/
├── 📄 auth.py                    # 认证相关API
├── 📄 audio.py                   # 音频处理API
├── 📄 document.py                # 文档处理API
├── 📄 rag.py                     # RAG知识库API
├── 📄 websocket.py               # WebSocket连接
└── 📄 system.py                  # 系统监控API
```

#### 🗃️ 数据模型
```python
# backend/models/
├── audio.py
│   ├── AudioFile                 # 音频文件模型
│   ├── ProcessingResult          # 处理结果模型
│   └── TaskRecord               # 任务记录模型
├── document.py
│   ├── Document                 # 文档模型
│   └── DocumentChunk            # 文档片段模型
└── user.py
    └── User                     # 用户模型
```

#### ⚙️ 服务层
```python
# backend/services/
├── audio_service.py             # 音频处理服务
├── document_service.py          # 文档处理服务
├── rag_service.py              # RAG服务
├── task_persistence_service.py # 任务持久化服务
└── enhanced_progress_service.py # 进度管理服务
```

#### 🔄 异步任务
```python
# backend/tasks/
├── audio_processing_tasks.py    # 音频处理任务
│   ├── speech_recognition_task  # 语音识别
│   ├── speaker_recognition_task # 说话人识别
│   ├── meeting_transcription_task # 会议转录
│   └── audio_enhancement_task   # 音频增强
├── document_tasks.py           # 文档处理任务
└── vectorization_tasks.py      # 向量化任务
```

## 🔌 核心工具模块

### 音频处理工具
```python
# backend/utils/audio/
├── optimized_funasr_manager.py  # FunASR模型管理
├── speech_recognition_core.py   # 语音识别核心
├── enhanced_audio_processor.py  # 音频预处理
└── speaker_clustering.py       # 说话人聚类
```

### 文档处理工具
```python
# backend/utils/document/
├── ocr_processor.py            # OCR处理器
├── document_splitter.py        # 文档分割器
└── format_converter.py         # 格式转换器
```

### RAG工具
```python
# backend/utils/rag/
├── vector_store.py             # 向量存储
├── retriever.py                # 检索器
└── reranker.py                 # 重排序器
```

## 🚀 启动流程

### 1. 系统初始化
```mermaid
graph TD
    A[系统启动] --> B[加载环境变量]
    B --> C[初始化数据库]
    C --> D[加载AI模型]
    D --> E[启动Redis]
    E --> F[启动Celery Worker]
    F --> G[启动FastAPI服务]
    G --> H[启动前端服务]
```

### 2. 请求处理流程
```mermaid
graph LR
    A[前端请求] --> B[FastAPI路由]
    B --> C[认证中间件]
    C --> D[业务逻辑]
    D --> E[Celery任务]
    E --> F[AI模型处理]
    F --> G[结果存储]
    G --> H[WebSocket推送]
    H --> I[前端更新]
```

## 📊 数据流架构

### 音频处理数据流
```
音频文件上传 → 文件验证 → 预处理 → AI模型推理 → 结果清理 → 数据库存储 → 前端展示
```

### 文档处理数据流
```
文档上传 → 格式检测 → OCR识别 → 智能分割 → 向量化 → ChromaDB存储 → 检索服务
```

### RAG问答数据流
```
用户问题 → 向量化 → 相似度检索 → 重排序 → 上下文构建 → 生成回答 → 流式返回
```

## 🔒 安全架构

### 认证授权
- JWT Token认证
- 密码哈希存储 (bcrypt)
- 会话管理
- CORS跨域配置

### 数据安全
- 文件上传验证
- SQL注入防护
- XSS攻击防护
- 敏感信息脱敏

### 系统安全
- 资源访问控制
- 任务权限验证
- 日志审计
- 错误信息过滤

## 📈 性能优化

### 前端优化
- 组件懒加载
- 静态资源压缩
- CDN加速
- 缓存策略

### 后端优化
- 数据库连接池
- 异步任务队列
- 内存缓存
- GPU加速

### 系统优化
- 负载均衡
- 容器化部署
- 监控告警
- 自动扩缩容
