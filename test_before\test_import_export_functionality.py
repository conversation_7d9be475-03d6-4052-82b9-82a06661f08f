#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
结果导入导出功能测试脚本
测试任务 11.5 - 结果导入导出功能
"""

import sys
import os
import json
import tempfile
import subprocess
import time
import requests
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def create_test_analysis_result():
    """创建简单的分析结果用于测试"""
    try:
        from utils.result_data_structures import AnalysisResult, SpeakerProfile, create_speech_segment
        
        # 创建分析结果对象
        analysis = AnalysisResult(
            audio_file_path="test_audio.wav",
            audio_duration=30.0,
            processing_status="completed"
        )
        
        # 添加说话人
        speaker1 = SpeakerProfile(id="speaker1", name="张三")
        speaker1.total_speaking_time = 15.0
        analysis.add_speaker(speaker1)
        
        speaker2 = SpeakerProfile(id="speaker2", name="李四") 
        speaker2.total_speaking_time = 15.0
        analysis.add_speaker(speaker2)
        
        # 添加语音片段
        segment1 = create_speech_segment(
            start_time=0.0,
            end_time=5.0,
            text="你好，欢迎来到测试程序",
            speaker_id="speaker1",
            confidence=0.95
        )
        analysis.add_segment(segment1)
        
        segment2 = create_speech_segment(
            start_time=5.0,
            end_time=10.0,
            text="很高兴见到你，我们开始今天的演示",
            speaker_id="speaker2",
            confidence=0.92
        )
        analysis.add_segment(segment2)
        
        return analysis
        
    except Exception as e:
        print(f"创建测试数据失败: {str(e)}")
        return None

def test_export_content_generation():
    """测试导出内容生成功能"""
    print("📋 导出内容生成测试")
    print("   描述: 测试各种格式的导出内容生成")
    
    try:
        from pages.结果展示和编辑 import (
            generate_json_export, generate_txt_export, 
            generate_srt_export, generate_vtt_export, 
            generate_csv_export
        )
        
        # 创建测试数据
        analysis = create_test_analysis_result()
        if not analysis:
            print("   ❌ 无法创建测试数据")
            print("   结果: ❌ 失败")
            return False
            
        options = {
            'include_timestamps': True,
            'include_speakers': True,
            'include_confidence': False,
            'max_chars_per_line': 50,
            'max_duration_per_subtitle': 5
        }
        
        # 测试JSON导出
        json_content = generate_json_export(analysis, options)
        assert json_content and len(json_content) > 0
        
        # 验证JSON格式
        json_data = json.loads(json_content)
        assert 'metadata' in json_data
        assert 'segments' in json_data
        
        # 测试TXT导出
        txt_content = generate_txt_export(analysis, options)
        assert txt_content and len(txt_content) > 0
        assert "语音转录结果" in txt_content
        
        # 测试SRT导出
        srt_content = generate_srt_export(analysis, options)
        assert srt_content and len(srt_content) > 0
        assert "-->" in srt_content
        
        # 测试VTT导出
        vtt_content = generate_vtt_export(analysis, options)
        assert vtt_content and len(vtt_content) > 0
        assert "WEBVTT" in vtt_content
        
        # 测试CSV导出
        csv_content = generate_csv_export(analysis, options)
        assert csv_content and len(csv_content) > 0
        assert "序号" in csv_content
        
        print("   ✅ 导出内容生成功能完整")
        print("   结果: ✅ 通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 导出内容生成测试失败: {str(e)}")
        print("   结果: ❌ 失败")
        return False

def test_import_parsing_functions():
    """测试导入解析功能"""
    print("📋 导入解析功能测试")
    print("   描述: 测试JSON、TXT、CSV格式的导入解析")
    
    try:
        from pages.结果展示和编辑 import (
            parse_json_to_analysis, parse_txt_to_analysis, 
            parse_txt_line, parse_time_string
        )
        
        # 测试JSON解析
        test_json_data = {
            "metadata": {
                "audio_file": "test.wav",
                "duration": 30.0,
                "speaker_count": 2
            },
            "speakers": {
                "speaker1": {"name": "张三", "total_speaking_time": 15.0},
                "speaker2": {"name": "李四", "total_speaking_time": 15.0}
            },
            "segments": [
                {
                    "start_time": 0.0,
                    "end_time": 5.0,
                    "text": "你好，欢迎来到测试",
                    "speaker_id": "speaker1",
                    "confidence": 0.95
                }
            ]
        }
        
        analysis = parse_json_to_analysis(test_json_data)
        assert analysis is not None
        assert analysis.speaker_count > 0
        assert len(analysis.segments) > 0
        
        # 测试TXT解析
        test_txt_content = """语音转录结果
音频文件: test.wav
总时长: 30.0秒
说话人数: 2
==========================================

[00:00:00 - 00:00:05] 张三: 你好，欢迎来到测试
[00:00:05 - 00:00:10] 李四: 很高兴见到你
"""
        
        analysis_txt = parse_txt_to_analysis(test_txt_content, "test.txt")
        assert analysis_txt is not None
        assert len(analysis_txt.segments) > 0
        
        # 测试时间解析
        time_seconds = parse_time_string("00:01:30")
        assert time_seconds == 90
        
        # 测试TXT行解析
        line_data = parse_txt_line("[00:00:05 - 00:00:10] 张三: 测试内容")
        assert line_data['start_time'] == 5
        assert line_data['end_time'] == 10
        assert line_data['speaker_id'] == '张三'
        assert line_data['text'] == '测试内容'
        
        print("   ✅ 导入解析功能完整")
        print("   结果: ✅ 通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 导入解析测试失败: {str(e)}")
        print("   结果: ❌ 失败")
        return False

def test_file_format_utilities():
    """测试文件格式工具函数"""
    print("📋 文件格式工具测试")
    print("   描述: 测试文件扩展名和MIME类型获取")
    
    try:
        from pages.结果展示和编辑 import (
            get_file_extension, get_mime_type, 
            format_time, format_srt_time, format_vtt_time,
            split_text_for_subtitle
        )
        
        # 测试扩展名获取
        assert get_file_extension("JSON (结构化数据)") == ".json"
        assert get_file_extension("TXT (纯文本)") == ".txt"
        assert get_file_extension("SRT (字幕)") == ".srt"
        assert get_file_extension("VTT (Web字幕)") == ".vtt"
        assert get_file_extension("CSV (表格)") == ".csv"
        
        # 测试MIME类型获取
        assert get_mime_type("JSON (结构化数据)") == "application/json"
        assert get_mime_type("TXT (纯文本)") == "text/plain"
        assert get_mime_type("CSV (表格)") == "text/csv"
        
        # 测试时间格式化
        assert format_time(3661) == "01:01:01"  # 1小时1分1秒
        assert format_srt_time(3661.5) == "01:01:01,500"
        assert format_vtt_time(3661.5) == "01:01:01.500"
        
        # 测试文本分割
        long_text = "这是一个非常长的文本，需要分割成多个部分以适应字幕显示的要求"
        chunks = split_text_for_subtitle(long_text, 20)
        assert len(chunks) > 1
        assert all(len(chunk) <= 20 for chunk in chunks)
        
        print("   ✅ 文件格式工具功能完整")
        print("   结果: ✅ 通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 文件格式工具测试失败: {str(e)}")
        print("   结果: ❌ 失败")
        return False

def test_export_save_functions():
    """测试导出保存功能"""
    print("📋 导出保存功能测试")
    print("   描述: 测试本地文件保存功能")
    
    try:
        from pages.结果展示和编辑 import generate_export_content
        
        # 创建临时目录
        with tempfile.TemporaryDirectory() as temp_dir:
            # 改变当前目录到临时目录
            original_cwd = os.getcwd()
            os.chdir(temp_dir)
            
            try:
                # 创建测试数据
                analysis = create_test_analysis_result()
                if not analysis:
                    print("   ❌ 无法创建测试数据")
                    print("   结果: ❌ 失败")
                    return False
                    
                formats = ["JSON (结构化数据)", "TXT (纯文本)"]
                base_filename = "test_export"
                options = {'include_timestamps': True, 'include_speakers': True}
                
                # 模拟保存功能（不使用Streamlit）
                export_dir = os.path.join(temp_dir, "exports")
                os.makedirs(export_dir, exist_ok=True)
                
                saved_files = []
                for fmt in formats:
                    content = generate_export_content(analysis, fmt, options)
                    
                    if "JSON" in fmt:
                        extension = ".json"
                    else:
                        extension = ".txt"
                    
                    filename = f"{base_filename}{extension}"
                    filepath = os.path.join(export_dir, filename)
                    
                    with open(filepath, 'w', encoding='utf-8') as f:
                        f.write(content)
                    
                    saved_files.append(filename)
                    
                    # 验证文件存在且有内容
                    assert os.path.exists(filepath)
                    assert os.path.getsize(filepath) > 0
                
                assert len(saved_files) == 2
                
            finally:
                # 恢复原目录
                os.chdir(original_cwd)
        
        print("   ✅ 导出保存功能完整")
        print("   结果: ✅ 通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 导出保存测试失败: {str(e)}")
        print("   结果: ❌ 失败")
        return False

def test_import_export_page_access():
    """测试导入导出页面访问"""
    print("📋 导入导出页面访问测试")
    print("   描述: 测试导入导出功能页面是否可访问")
    
    try:
        from pages.结果展示和编辑 import (
            display_import_export, display_export_functions, 
            display_import_functions, display_file_upload,
            display_local_file_import, display_saved_results_import
        )
        
        # 验证主要函数存在
        assert callable(display_import_export)
        assert callable(display_export_functions)
        assert callable(display_import_functions)
        assert callable(display_file_upload)
        assert callable(display_local_file_import)
        assert callable(display_saved_results_import)
        
        print("   ✅ 导入导出页面和核心功能完整")
        print("   结果: ✅ 通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 导入导出页面访问失败: {str(e)}")
        print("   结果: ❌ 失败")
        return False

def test_csv_import_with_pandas():
    """测试CSV导入功能（包含pandas检查）"""
    print("📋 CSV导入功能测试")
    print("   描述: 测试CSV文件导入和解析")
    
    try:
        # 检查pandas是否可用
        try:
            import pandas as pd
            
            from pages.结果展示和编辑 import parse_csv_to_analysis
            
            # 测试CSV解析
            test_csv_data = pd.DataFrame({
                '序号': [1, 2],
                '开始时间': [0.0, 5.0],
                '结束时间': [5.0, 10.0],
                '说话人姓名': ['张三', '李四'],
                '文本内容': ['你好，欢迎来到测试', '很高兴见到你']
            })
            
            analysis_csv = parse_csv_to_analysis(test_csv_data, "test.csv")
            assert analysis_csv is not None
            assert len(analysis_csv.segments) == 2
            
            print("   ✅ CSV导入功能完整")
            print("   结果: ✅ 通过")
            return True
            
        except ImportError:
            print("   ⚠️ pandas 未安装，跳过CSV导入测试")
            print("   结果: ✅ 通过（跳过）")
            return True
        
    except Exception as e:
        print(f"   ❌ CSV导入测试失败: {str(e)}")
        print("   结果: ❌ 失败")
        return False

def test_streamlit_deployment():
    """测试Streamlit部署"""
    print("📋 Streamlit部署测试")
    print("   描述: 测试导入导出功能的Streamlit集成")
    
    try:
        # 启动Streamlit应用
        port = 8511
        cmd = [
            "streamlit", "run", "pages/结果展示和编辑.py",
            "--server.port", str(port),
            "--server.headless", "true"
        ]
        
        print(f"   🚀 启动Streamlit应用测试...")
        print(f"   执行命令: {' '.join(cmd)}")
        
        # 启动进程
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        # 等待应用启动
        time.sleep(8)
        
        # 检查应用是否响应
        try:
            response = requests.get(f"http://localhost:{port}", timeout=10)
            if response.status_code == 200:
                print(f"   ✅ Streamlit应用成功启动在端口 {port}")
                print("   ✅ 页面响应正常")
                
                # 终止进程
                process.terminate()
                process.wait(timeout=5)
                
                return True
            else:
                print(f"   ❌ 页面响应错误: {response.status_code}")
                process.terminate()
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"   ❌ 页面连接失败: {str(e)}")
            process.terminate()
            return False
            
    except Exception as e:
        print(f"   ❌ Streamlit部署测试失败: {str(e)}")
        return False

def run_comprehensive_test():
    """运行综合测试"""
    print("🎯 开始结果导入导出功能测试")
    print("=" * 60)
    print("🧪 测试结果导入导出功能...")
    print()
    
    # 测试用例列表
    test_cases = [
        ("导入导出页面访问测试", test_import_export_page_access),
        ("导出内容生成测试", test_export_content_generation),
        ("导入解析功能测试", test_import_parsing_functions),
        ("文件格式工具测试", test_file_format_utilities),
        ("导出保存功能测试", test_export_save_functions),
        ("CSV导入功能测试", test_csv_import_with_pandas),
    ]
    
    # 运行测试
    results = []
    for test_name, test_func in test_cases:
        result = test_func()
        results.append((test_name, result))
        print()
    
    # 汇总结果
    print("=" * 60)
    print("🎯 结果导入导出功能测试总结")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    print(f"总测试用例: {total}")
    print(f"通过: {passed}")
    print(f"失败: {total - passed}")
    print(f"通过率: {passed/total*100:.1f}%")
    print()
    
    print("📊 详细结果:")
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {status} - {test_name}")
    
    print()
    print("=" * 60)
    print("🚀 Streamlit部署测试")
    print("=" * 60)
    print()
    
    deployment_result = test_streamlit_deployment()
    
    print()
    print("=" * 60)
    print("🏆 最终测试结果")
    print("=" * 60)
    
    basic_passed = all(result for _, result in results)
    print(f"基础功能测试: {'✅ 通过' if basic_passed else '❌ 失败'}")
    print(f"部署测试: {'✅ 通过' if deployment_result else '❌ 失败'}")
    print()
    
    if basic_passed and deployment_result:
        print("总体结果: 🎉 全部通过")
        print()
        print("🎯 任务11.5 - 结果导入导出功能 ✅ 完成")
        print("✨ 包含多格式导出、文件导入、格式转换等全部功能！")
        print("📤 支持JSON、TXT、SRT、VTT、CSV等多种导出格式")
        print("📥 支持文件上传、本地文件、保存结果等多种导入方式")
        return True
    else:
        print("总体结果: ❌ 部分失败")
        return False

if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1) 