#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清空ChromaDB集合的独立脚本
用于解决向量维度不匹配问题
"""

import chromadb
import os
import shutil
from config.models.vector_store import get_chroma_settings

def clear_chroma_database():
    """完全清空ChromaDB数据库"""
    try:
        print("开始清空ChromaDB数据库...")
        
        # 获取配置
        chroma_settings = get_chroma_settings()
        db_path = chroma_settings["persist_directory"]
        collection_name = chroma_settings["collection_name"]
        
        print(f"数据库路径: {db_path}")
        print(f"集合名称: {collection_name}")
        
        # 方法1: 通过API删除集合
        try:
            client = chromadb.PersistentClient(path=db_path)
            client.delete_collection(name=collection_name)
            print("✅ 通过API成功删除集合")
        except Exception as e:
            print(f"⚠️ API删除失败: {str(e)}")
            
            # 方法2: 直接删除数据库文件
            try:
                if os.path.exists(db_path):
                    shutil.rmtree(db_path)
                    print("✅ 通过文件系统成功删除数据库目录")
                else:
                    print("ℹ️ 数据库目录不存在")
            except Exception as e2:
                print(f"❌ 文件系统删除也失败: {str(e2)}")
                return False
        
        print("🎉 ChromaDB数据库清空完成！")
        print("请重新启动应用程序以重新初始化数据库")
        return True
        
    except Exception as e:
        print(f"❌ 清空数据库时出错: {str(e)}")
        return False

if __name__ == "__main__":
    print("ChromaDB 数据库清空工具")
    print("=" * 50)
    
    confirm = input("确定要清空ChromaDB数据库吗？这将删除所有向量数据。(y/N): ")
    
    if confirm.lower() in ['y', 'yes']:
        success = clear_chroma_database()
        if success:
            print("\n数据库已清空，请重新启动应用程序。")
        else:
            print("\n清空失败，请手动删除 chroma_db 目录。")
    else:
        print("操作已取消。") 