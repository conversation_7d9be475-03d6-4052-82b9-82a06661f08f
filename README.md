# 语音处理智能平台 (Speech Processing Intelligence Platform)

[![Python](https://img.shields.io/badge/Python-3.11-blue.svg)](https://python.org)
[![Vue](https://img.shields.io/badge/Vue-3.3-green.svg)](https://vuejs.org)
[![FastAPI](https://img.shields.io/badge/FastAPI-0.115-red.svg)](https://fastapi.tiangolo.com)
[![Celery](https://img.shields.io/badge/Celery-5.3-green.svg)](https://docs.celeryproject.org)
[![Redis](https://img.shields.io/badge/Redis-7.0-red.svg)](https://redis.io)
[![Docker](https://img.shields.io/badge/Docker-Ready-blue.svg)](https://docker.com)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)

> 🎯 **一站式AI语音处理解决方案** - 集成语音识别、说话人识别、文档处理和RAG知识库的智能平台

## 📚 文档导航

- 📖 [项目结构说明](PROJECT_STRUCTURE.md)
- 🚀 [部署指南](DEPLOYMENT_GUIDE.md)
- 🔧 [API文档](http://localhost:8002/docs) (启动后访问)
- 📊 [性能测试报告](test/README.md)
- 🐛 [问题反馈](https://github.com/your-repo/issues)

## 🎯 项目概述

语音处理智能平台是一个基于AI的综合性语音处理和文档管理系统，集成了先进的语音识别、说话人识别、文档处理和RAG知识库功能。系统采用现代化的微服务架构，支持实时处理、批量处理和智能分析。

### 🌟 核心特性

#### 🎤 语音处理功能
- **语音识别**: 基于FunASR的高精度语音转文字
- **说话人识别**: CAM++模型支持的多说话人分离
- **会议转录**: 智能会议记录与说话人标识
- **音频增强**: 降噪、重采样、音频预处理
- **VAD检测**: 语音活动检测与静音分割

#### 📄 文档处理功能
- **OCR识别**: 图片和PDF文档文字提取
- **智能分割**: 文档自动分段与结构化
- **格式转换**: 支持多种文档格式互转
- **批量处理**: 大规模文档并行处理

#### 🧠 RAG知识库
- **向量化存储**: ChromaDB向量数据库
- **智能检索**: 语义搜索与相关性排序
- **重排序**: Qwen3-Reranker提升检索精度
- **流式问答**: 实时对话与知识问答

#### ⚡ 系统特性
- **实时协作**: WebSocket支持的实时进度监控
- **任务队列**: Celery分布式任务处理
- **GPU加速**: CUDA支持的高性能计算
- **离线部署**: 完全本地化的模型部署
- **容器化**: Docker支持的一键部署

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   后端API       │    │   任务队列      │
│   Vue 3 + EP    │◄──►│   FastAPI       │◄──►│   Celery        │
│   Port: 3000    │    │   Port: 8002    │    │   Redis         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   静态资源      │    │   数据存储      │    │   AI模型        │
│   Nginx/CDN     │    │   SQLite        │    │   FunASR        │
│                 │    │   ChromaDB      │    │   CAM++         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 技术栈详情

#### 前端技术栈
- **框架**: Vue 3 + Composition API
- **UI库**: Element Plus
- **构建工具**: Vite
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **图表**: ECharts + Vue-ECharts
- **HTTP客户端**: Axios

#### 后端技术栈
- **Web框架**: FastAPI + Uvicorn
- **数据库**: SQLite + SQLAlchemy 2.0
- **任务队列**: Celery + Redis
- **认证**: JWT + PassLib
- **文件处理**: aiofiles + python-multipart
- **API文档**: OpenAPI + Swagger UI

#### AI模型栈
- **语音识别**: FunASR (SenseVoice)
- **说话人识别**: CAM++ (3D-Speaker)
- **VAD检测**: FSMN-VAD
- **重排序**: Qwen3-Reranker-0.6B
- **向量数据库**: ChromaDB
- **深度学习**: PyTorch + CUDA

## 🚀 快速开始

### 环境要求

- **操作系统**: Windows 10/11, Linux, macOS
- **Python**: 3.11+
- **Node.js**: 16.0+
- **内存**: 8GB+ (推荐16GB+)
- **显卡**: NVIDIA GPU (可选，用于加速)
- **存储**: 10GB+ 可用空间

### 安装步骤

#### 1. 克隆项目
```bash
git clone <repository-url>
cd my_notebook_version_0.1.0
```

#### 2. 后端环境配置
```bash
# 创建虚拟环境
python -m venv .venv

# 激活虚拟环境 (Windows)
.venv\Scripts\activate

# 激活虚拟环境 (Linux/macOS)
source .venv/bin/activate

# 安装依赖
pip install -r backend/requirements.txt
```

#### 3. 前端环境配置
```bash
cd frontend
npm install
```

#### 4. 启动Redis (Docker)
```bash
docker run -d --name redis -p 6379:6379 redis:latest
```

#### 5. 环境变量配置
```bash
# 复制环境变量模板
cp env.template.txt .env

# 编辑环境变量
# 配置模型路径、数据库路径等
```

### 启动服务

#### 🚀 一键启动 (推荐)

**Windows用户:**
```bash
# 双击运行或命令行执行
start_all_services.bat
```

**Linux/macOS用户:**
```bash
# 赋予执行权限
chmod +x start_all_services.sh

# 一键启动所有服务
./start_all_services.sh
```

#### 🛑 停止服务
```bash
# Linux/macOS
./stop_all_services.sh

# Windows (手动关闭各服务窗口)
```

#### 🔄 重启服务
```bash
# Linux/macOS
./restart_all_services.sh
```

#### 手动启动 (开发调试)

1. **启动后端服务**
```bash
# 激活虚拟环境
.venv\Scripts\activate  # Windows
source .venv/bin/activate  # Linux/macOS

# 启动FastAPI服务
python -m uvicorn backend.main:app --host 0.0.0.0 --port 8002 --reload
```

2. **启动Celery Worker**
```bash
# 新终端，激活虚拟环境
.venv\Scripts\activate  # Windows
source .venv/bin/activate  # Linux/macOS

# 启动Celery Worker
python start_worker_windows.py
```

3. **启动前端服务**
```bash
cd frontend
npm run dev
```

#### 访问地址
- **前端界面**: http://localhost:3000
- **后端API**: http://localhost:8002
- **API文档**: http://localhost:8002/docs

### 默认账户
- **用户名**: admin
- **密码**: admin123

## 🎬 快速演示

### 语音识别演示
```bash
# 1. 上传音频文件到音频处理中心
# 2. 选择"语音识别"模式
# 3. 点击"开始处理"
# 4. 实时查看处理进度
# 5. 获得文字转录结果
```

### 会议转录演示
```bash
# 1. 上传会议录音文件
# 2. 选择"会议转录"模式
# 3. 设置说话人数量 (默认2人)
# 4. 开始处理，系统自动识别不同说话人
# 5. 获得带时间轴的对话记录
```

### RAG知识问答演示
```bash
# 1. 上传文档到RAG知识库
# 2. 等待文档向量化完成
# 3. 在问答界面输入问题
# 4. 获得基于文档内容的智能回答
```

## 🎯 核心功能展示

| 功能模块 | 输入格式 | 输出结果 | 处理时间 |
|---------|---------|---------|---------|
| 语音识别 | MP3/WAV/M4A | 文字转录 + 时间轴 | ~0.1x 音频时长 |
| 说话人识别 | 音频文件 | 说话人标签 + 聚类 | ~0.2x 音频时长 |
| 会议转录 | 会议录音 | 多人对话记录 | ~0.3x 音频时长 |
| 文档OCR | PDF/图片 | 结构化文本 | ~2秒/页 |
| RAG问答 | 自然语言 | 智能回答 | ~1-3秒 |

## 📱 功能模块

### 🎵 音频处理中心
- **文件上传**: 支持MP3、WAV、M4A等格式
- **实时录音**: 浏览器内录音功能
- **处理模式**: 语音识别、说话人识别、会议转录、音频增强
- **结果展示**: 文本结果、时间轴、说话人标签
- **批量处理**: 多文件并行处理

### 📄 文档管理系统
- **文档上传**: PDF、图片、Word等格式支持
- **OCR处理**: 高精度文字识别
- **智能分割**: 自动段落和章节识别
- **格式导出**: TXT、DOCX、JSON多格式导出

### 🔍 RAG知识库
- **知识导入**: 文档向量化存储
- **智能问答**: 基于知识库的对话系统
- **语义检索**: 相似度搜索和重排序
- **实时流式**: 流式响应和实时交互

### 📊 系统监控
- **任务监控**: 实时任务状态和进度
- **性能指标**: CPU、内存、GPU使用率
- **错误日志**: 详细的错误追踪和诊断
- **健康检查**: 系统组件状态监控

## 🔧 配置说明

### 模型配置
系统使用本地AI模型，需要下载以下模型文件：

```
models/
├── SenseVoiceSmall/          # 语音识别模型
├── fsmn_vad_zh/             # VAD检测模型
├── cam++/                   # 说话人识别模型
└── Qwen3-Reranker-0.6B/     # 重排序模型
```

### 数据库配置
```bash
# SQLite数据库位置
DATABASE_URL=sqlite:///./data/speech_platform.db

# ChromaDB向量数据库
CHROMA_DB_PATH=./chroma_db
```

### Redis配置
```bash
# Redis连接配置
REDIS_URL=redis://localhost:6379/0
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
```

## 🐳 Docker部署

### 使用Docker Compose
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 单独构建镜像
```bash
# 构建后端镜像
docker build -t speech-platform-backend ./backend

# 构建前端镜像
docker build -t speech-platform-frontend ./frontend

# 运行容器
docker run -d -p 8002:8002 speech-platform-backend
docker run -d -p 3000:3000 speech-platform-frontend
```

## 📋 API文档

### 主要API端点

#### 认证相关
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/register` - 用户注册
- `GET /api/v1/auth/me` - 获取用户信息

#### 音频处理
- `POST /api/v1/audio/upload` - 上传音频文件
- `POST /api/v1/audio/speech-recognition` - 语音识别
- `POST /api/v1/audio/speaker-recognition` - 说话人识别
- `POST /api/v1/audio/meeting-transcription` - 会议转录
- `GET /api/v1/audio/results` - 获取处理结果

#### 文档处理
- `POST /api/v1/documents/upload` - 上传文档
- `POST /api/v1/documents/ocr` - OCR处理
- `GET /api/v1/documents/results` - 获取处理结果

#### RAG知识库
- `POST /api/v1/rag/upload` - 上传知识文档
- `POST /api/v1/rag/query` - 知识问答
- `GET /api/v1/rag/collections` - 获取知识库列表

### WebSocket接口
- `ws://localhost:8002/ws/{user_id}` - 实时进度推送

## 🧪 测试

### 运行测试
```bash
# 后端测试
cd backend
python -m pytest tests/

# 前端测试
cd frontend
npm run test

# 端到端测试
python test/test_end_to_end_workflow.py
```

### 功能测试
```bash
# 测试音频处理API
python test/test_audio_api_detailed.py

# 测试会议转录功能
python test/test_meeting_transcription_fix.py

# 测试离线模型验证
python test/test_offline_models_validation.py
```

## 🔍 故障排除

### 常见问题

#### 1. 模型加载失败
```bash
# 检查模型路径
python -c "from backend.config.settings import settings; print(settings.SENSEVOICE_MODEL_PATH)"

# 验证模型文件
ls -la models/SenseVoiceSmall/
```

#### 2. Redis连接失败
```bash
# 检查Redis状态
docker ps | grep redis

# 测试Redis连接
redis-cli ping
```

#### 3. Celery Worker异常
```bash
# 查看Worker日志
python start_worker_windows.py

# 检查任务队列
python -c "from backend.celery_app import app; print(app.control.inspect().active())"
```

#### 4. 前端无法连接后端
```bash
# 检查后端服务状态
curl http://localhost:8002/health

# 检查CORS配置
# 确保backend/main.py中的CORS设置正确
```

## 📈 性能优化

### 系统优化建议
- **内存**: 推荐16GB+内存，大文件处理需要更多内存
- **GPU**: 使用NVIDIA GPU可显著提升处理速度
- **存储**: 使用SSD可提升文件I/O性能
- **网络**: 确保稳定的网络连接用于模型下载

### 配置优化
```python
# backend/config/settings.py
CELERY_WORKER_CONCURRENCY = 4  # 根据CPU核心数调整
MAX_AUDIO_FILE_SIZE = 100 * 1024 * 1024  # 100MB
BATCH_SIZE = 32  # 批处理大小
GPU_MEMORY_FRACTION = 0.8  # GPU内存使用比例
```

## 🤝 贡献指南

### 开发流程
1. Fork项目仓库
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

### 代码规范
- **Python**: 遵循PEP 8规范
- **JavaScript**: 使用ESLint + Prettier
- **Vue**: 遵循Vue 3官方风格指南
- **提交信息**: 使用约定式提交格式

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持与联系

- **问题反馈**: [GitHub Issues](https://github.com/your-repo/issues)
- **功能请求**: [GitHub Discussions](https://github.com/your-repo/discussions)
- **技术文档**: [Wiki](https://github.com/your-repo/wiki)

## 🙏 致谢

感谢以下开源项目的支持：
- [FunASR](https://github.com/alibaba-damo-academy/FunASR) - 语音识别框架
- [FastAPI](https://fastapi.tiangolo.com/) - 现代Web框架
- [Vue.js](https://vuejs.org/) - 渐进式JavaScript框架
- [Element Plus](https://element-plus.org/) - Vue 3组件库
- [ChromaDB](https://www.trychroma.com/) - 向量数据库

---

⭐ 如果这个项目对您有帮助，请给我们一个星标！
