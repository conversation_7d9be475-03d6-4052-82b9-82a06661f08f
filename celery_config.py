#!/usr/bin/env python
"""
Celery 配置文件 - 用于从项目根目录启动 Worker
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 设置环境变量
os.environ.setdefault('PYTHONPATH', str(project_root))

# 导入 celery 应用
from backend.core.task_queue import celery_app

# 确保任务模块被导入
import backend.tasks.document_tasks
import backend.tasks.vectorization_tasks
import backend.tasks.ocr_tasks

if __name__ == '__main__':
    celery_app.start()
