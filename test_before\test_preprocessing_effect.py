#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试音频预处理对语音识别和说话人识别效果的影响
"""

import streamlit as st
import os
import sys
import tempfile
import traceback
from pathlib import Path
import time

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_preprocessing_effect():
    """测试预处理效果对比"""
    st.title("🔬 音频预处理效果对比测试")
    st.markdown("比较原始音频与预处理后音频在语音识别和说话人识别中的效果差异")
    
    # 文件上传
    uploaded_file = st.file_uploader(
        "上传测试音频文件",
        type=['wav', 'mp3', 'm4a', 'aac', 'flac', 'ogg'],
        help="上传一个音频文件来测试预处理效果"
    )
    
    if uploaded_file is not None:
        st.info(f"已上传文件: {uploaded_file.name}")
        
        # 预处理选项
        st.subheader("🔧 预处理配置")
        col1, col2 = st.columns(2)
        
        with col1:
            enable_denoise = st.checkbox("启用降噪", value=True)
            denoise_method = st.selectbox(
                "降噪方法",
                ["spectral_gating", "wiener", "stationary"],
                index=0
            )
        
        with col2:
            enable_normalize = st.checkbox("启用音量标准化", value=True)
            target_db = st.slider("目标音量 (dB)", -30, -10, -20)
        
        if st.button("🚀 开始对比测试", type="primary"):
            with st.spinner("正在进行对比测试..."):
                try:
                    # 保存原始文件
                    with tempfile.NamedTemporaryFile(delete=False, suffix=Path(uploaded_file.name).suffix) as tmp_file:
                        tmp_file.write(uploaded_file.getvalue())
                        original_audio_path = tmp_file.name
                    
                    # 创建预处理后的文件
                    preprocessed_fd, preprocessed_audio_path = tempfile.mkstemp(suffix='.wav')
                    os.close(preprocessed_fd)
                    
                    # 执行预处理
                    st.info("🔧 正在进行音频预处理...")
                    
                    try:
                        from utils.audio_preprocessing import AudioPreprocessor
                        
                        preprocessor = AudioPreprocessor()
                        
                        # 执行预处理
                        result = preprocessor.preprocess_audio(
                            input_path=original_audio_path,
                            output_path=preprocessed_audio_path,
                            normalize=enable_normalize,
                            denoise=enable_denoise,
                            target_db=target_db,
                            denoise_method=denoise_method
                        )
                        
                        if result:
                            st.success("✅ 音频预处理完成")
                        else:
                            st.error("❌ 音频预处理失败")
                            return
                            
                    except ImportError:
                        st.error("❌ 音频预处理模块不可用")
                        return
                    except Exception as e:
                        st.error(f"❌ 预处理失败: {str(e)}")
                        return
                    
                    # 创建对比结果容器
                    st.subheader("📊 对比测试结果")
                    
                    # 测试1: VAD效果对比
                    st.markdown("### 🎯 VAD语音活动检测对比")
                    
                    col1, col2 = st.columns(2)
                    
                    with col1:
                        st.markdown("**原始音频**")
                        original_segments = test_vad_performance(original_audio_path, "原始")
                    
                    with col2:
                        st.markdown("**预处理后音频**")
                        preprocessed_segments = test_vad_performance(preprocessed_audio_path, "预处理")
                    
                    # VAD结果对比
                    if original_segments is not None and preprocessed_segments is not None:
                        st.markdown("#### 📈 VAD检测结果对比")
                        
                        vad_col1, vad_col2, vad_col3 = st.columns(3)
                        with vad_col1:
                            st.metric("原始音频语音段", len(original_segments))
                        with vad_col2:
                            st.metric("预处理后语音段", len(preprocessed_segments))
                        with vad_col3:
                            improvement = len(preprocessed_segments) - len(original_segments)
                            st.metric("检测改善", improvement, delta=improvement)
                    
                    # 测试2: 语音识别效果对比
                    st.markdown("### 🗣️ 语音识别效果对比")
                    
                    col1, col2 = st.columns(2)
                    
                    with col1:
                        st.markdown("**原始音频识别**")
                        original_recognition = test_speech_recognition(original_audio_path, "原始")
                    
                    with col2:
                        st.markdown("**预处理后识别**")
                        preprocessed_recognition = test_speech_recognition(preprocessed_audio_path, "预处理")
                    
                    # 语音识别结果对比
                    if original_recognition and preprocessed_recognition:
                        st.markdown("#### 📈 语音识别结果对比")
                        
                        rec_col1, rec_col2, rec_col3 = st.columns(3)
                        with rec_col1:
                            st.metric("原始音频置信度", f"{original_recognition.get('confidence', 0):.2f}")
                        with rec_col2:
                            st.metric("预处理后置信度", f"{preprocessed_recognition.get('confidence', 0):.2f}")
                        with rec_col3:
                            conf_improvement = preprocessed_recognition.get('confidence', 0) - original_recognition.get('confidence', 0)
                            st.metric("置信度提升", f"{conf_improvement:.2f}", delta=conf_improvement)
                        
                        # 显示识别文本
                        st.markdown("#### 📝 识别文本对比")
                        
                        text_col1, text_col2 = st.columns(2)
                        with text_col1:
                            st.text_area("原始音频识别文本", original_recognition.get('text', ''), height=100)
                        with text_col2:
                            st.text_area("预处理后识别文本", preprocessed_recognition.get('text', ''), height=100)
                    
                    # 测试3: 说话人识别效果对比
                    st.markdown("### 👥 说话人识别效果对比")
                    
                    col1, col2 = st.columns(2)
                    
                    with col1:
                        st.markdown("**原始音频说话人识别**")
                        original_speakers = test_speaker_recognition(original_audio_path, "原始")
                    
                    with col2:
                        st.markdown("**预处理后说话人识别**")
                        preprocessed_speakers = test_speaker_recognition(preprocessed_audio_path, "预处理")
                    
                    # 说话人识别结果对比
                    if original_speakers is not None and preprocessed_speakers is not None:
                        st.markdown("#### 📈 说话人识别结果对比")
                        
                        spk_col1, spk_col2, spk_col3 = st.columns(3)
                        with spk_col1:
                            st.metric("原始音频说话人数", original_speakers)
                        with spk_col2:
                            st.metric("预处理后说话人数", preprocessed_speakers)
                        with spk_col3:
                            spk_improvement = abs(2 - preprocessed_speakers) - abs(2 - original_speakers)  # 假设目标是2个说话人
                            st.metric("识别准确性改善", spk_improvement, delta=spk_improvement)
                    
                    # 总结
                    st.markdown("### 📋 测试总结")
                    
                    summary_text = "**音频预处理效果总结:**\n\n"
                    
                    if original_segments is not None and preprocessed_segments is not None:
                        summary_text += f"• VAD检测: 原始{len(original_segments)}段 → 预处理{len(preprocessed_segments)}段\n"
                    
                    if original_recognition and preprocessed_recognition:
                        summary_text += f"• 语音识别置信度: {original_recognition.get('confidence', 0):.2f} → {preprocessed_recognition.get('confidence', 0):.2f}\n"
                    
                    if original_speakers is not None and preprocessed_speakers is not None:
                        summary_text += f"• 说话人识别: {original_speakers}人 → {preprocessed_speakers}人\n"
                    
                    summary_text += f"\n**预处理配置:**\n"
                    summary_text += f"• 降噪: {'启用' if enable_denoise else '禁用'} ({denoise_method if enable_denoise else 'N/A'})\n"
                    summary_text += f"• 音量标准化: {'启用' if enable_normalize else '禁用'} ({target_db}dB)\n"
                    
                    st.markdown(summary_text)
                    
                    # 清理临时文件
                    try:
                        os.unlink(original_audio_path)
                        os.unlink(preprocessed_audio_path)
                    except:
                        pass
                        
                except Exception as e:
                    st.error(f"❌ 测试过程中发生错误: {str(e)}")
                    st.error(f"详细错误: {traceback.format_exc()}")

def test_vad_performance(audio_path, label):
    """测试VAD性能"""
    try:
        from utils.speech_recognition_utils import load_vad_model, vad_segment
        
        vad_model_path = r"C:\Users\<USER>\Documents\my_project\models\model_dir\fsmn_vad_zh"
        
        if not os.path.exists(vad_model_path):
            st.error(f"❌ VAD模型路径不存在: {vad_model_path}")
            return None
        
        start_time = time.time()
        vad_model = load_vad_model(vad_model_path)
        
        if vad_model is None:
            st.error("❌ 无法加载VAD模型")
            return None
        
        segments = vad_segment(audio_path, vad_model)
        end_time = time.time()
        
        processing_time = end_time - start_time
        
        if segments:
            st.success(f"✅ {label}: 检测到 {len(segments)} 个语音段")
            st.caption(f"处理时间: {processing_time:.2f}秒")
            
            # 显示前几个语音段
            for i, segment in enumerate(segments[:3]):
                start_time = segment[0] / 1000.0
                end_time = segment[1] / 1000.0
                st.caption(f"片段 {i+1}: {start_time:.2f}s - {end_time:.2f}s")
        else:
            st.warning(f"⚠️ {label}: 未检测到语音段")
        
        return segments
        
    except Exception as e:
        st.error(f"❌ {label} VAD测试失败: {str(e)}")
        return None

def test_speech_recognition(audio_path, label):
    """测试语音识别性能"""
    try:
        from utils.speech_recognition_core import SenseVoiceRecognizer, SpeechRecognitionConfig
        
        sensevoice_model_path = r"C:\Users\<USER>\Documents\my_project\models\model_dir\SenseVoiceSmall"
        
        if not os.path.exists(sensevoice_model_path):
            st.error(f"❌ SenseVoice模型路径不存在: {sensevoice_model_path}")
            return None
        
        start_time = time.time()
        
        config = SpeechRecognitionConfig(
            model_path=sensevoice_model_path,
            language="auto",
            use_itn=True,
            device="auto"
        )
        
        recognizer = SenseVoiceRecognizer(config)
        result = recognizer.recognize_audio(audio_path)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        if result.success:
            st.success(f"✅ {label}: 识别成功")
            st.caption(f"处理时间: {processing_time:.2f}秒")
            st.caption(f"置信度: {result.confidence:.2f}")
            
            return {
                'text': result.text,
                'confidence': result.confidence,
                'language': result.language,
                'processing_time': processing_time
            }
        else:
            st.error(f"❌ {label}: 识别失败 - {result.error}")
            return None
            
    except Exception as e:
        st.error(f"❌ {label} 语音识别测试失败: {str(e)}")
        return None

def test_speaker_recognition(audio_path, label):
    """测试说话人识别性能"""
    try:
        from utils.speech_recognition_utils import load_vad_model, vad_segment
        from utils.speaker_recognition import SpeakerRecognition
        
        # VAD分割
        vad_model_path = r"C:\Users\<USER>\Documents\my_project\models\model_dir\fsmn_vad_zh"
        vad_model = load_vad_model(vad_model_path)
        segments = vad_segment(audio_path, vad_model)
        
        if not segments:
            st.warning(f"⚠️ {label}: 未检测到语音段")
            return None
        
        # 说话人识别
        campplus_model_path = r"C:\Users\<USER>\Documents\my_project\models\model_dir\cam++"
        
        if not os.path.exists(campplus_model_path):
            st.warning(f"⚠️ {label}: CAM++模型不可用")
            return None
        
        start_time = time.time()
        
        speaker_system = SpeakerRecognition(model_path=campplus_model_path)
        
        # 简化测试：只使用前5个语音段
        test_segments = segments[:5]
        embeddings = []
        
        import soundfile as sf
        import tempfile
        
        audio, sr = sf.read(audio_path)
        temp_dir = tempfile.mkdtemp()
        
        try:
            for i, segment in enumerate(test_segments):
                start_sample = int(segment[0] / 1000.0 * sr)
                end_sample = int(segment[1] / 1000.0 * sr)
                segment_audio = audio[start_sample:end_sample]
                
                if len(segment_audio) > 0:
                    segment_file = os.path.join(temp_dir, f"segment_{i}.wav")
                    sf.write(segment_file, segment_audio, sr)
                    
                    embedding = speaker_system.extract_speaker_embedding(segment_file)
                    if embedding is not None:
                        embeddings.append(embedding)
            
            if embeddings:
                speaker_labels = speaker_system.cluster_speakers(embeddings, threshold=0.25)
                unique_speakers = len(set(speaker_labels))
                
                end_time = time.time()
                processing_time = end_time - start_time
                
                st.success(f"✅ {label}: 识别出 {unique_speakers} 个说话人")
                st.caption(f"处理时间: {processing_time:.2f}秒")
                st.caption(f"测试片段: {len(embeddings)}个")
                
                return unique_speakers
            else:
                st.warning(f"⚠️ {label}: 未能提取说话人特征")
                return None
                
        finally:
            import shutil
            try:
                shutil.rmtree(temp_dir)
            except:
                pass
                
    except Exception as e:
        st.error(f"❌ {label} 说话人识别测试失败: {str(e)}")
        return None

if __name__ == "__main__":
    test_preprocessing_effect()
