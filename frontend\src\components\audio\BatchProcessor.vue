<template>
  <div class="batch-processor">
    <!-- 批量处理头部 -->
    <div class="batch-header">
      <div class="header-info">
        <h3>🔄 批量处理工作流</h3>
        <div class="batch-stats">
          <el-tag type="info" size="small">队列: {{ queuedFiles.length }}</el-tag>
          <el-tag type="warning" size="small">处理中: {{ processingFiles.length }}</el-tag>
          <el-tag type="success" size="small">已完成: {{ completedFiles.length }}</el-tag>
          <el-tag v-if="failedFiles.length > 0" type="danger" size="small">失败: {{ failedFiles.length }}</el-tag>
        </div>
      </div>
      
      <div class="header-actions">
        <el-button-group>
          <el-button 
            @click="startBatchProcessing" 
            :disabled="queuedFiles.length === 0 || isProcessing"
            type="primary" 
            size="small"
            :icon="VideoPlay"
          >
            开始处理
          </el-button>
          <el-button 
            @click="pauseBatchProcessing" 
            :disabled="!isProcessing"
            size="small"
            :icon="VideoPause"
          >
            {{ isPaused ? '继续' : '暂停' }}
          </el-button>
          <el-button
            @click="stopBatchProcessing"
            :disabled="!isProcessing"
            type="danger"
            size="small"
            :icon="Close"
          >
            停止
          </el-button>
        </el-button-group>
        
        <el-dropdown @command="handleBatchCommand">
          <el-button :icon="Setting" size="small" circle />
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="clear-completed">清除已完成</el-dropdown-item>
              <el-dropdown-item command="retry-failed">重试失败项</el-dropdown-item>
              <el-dropdown-item command="export-results">导出结果</el-dropdown-item>
              <el-dropdown-item command="save-template">保存为模板</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 批量配置面板 -->
    <div class="batch-config" v-if="showConfig">
      <div class="config-header">
        <h4>⚙️ 批量配置</h4>
        <el-button text @click="showConfig = false" :icon="Close" size="small" />
      </div>
      
      <div class="config-content">
        <el-row :gutter="16">
          <el-col :span="8">
            <div class="config-section">
              <label>并发数量</label>
              <el-input-number
                v-model="batchConfig.concurrency"
                :min="1"
                :max="10"
                size="small"
              />
            </div>
          </el-col>
          
          <el-col :span="8">
            <div class="config-section">
              <label>失败重试次数</label>
              <el-input-number
                v-model="batchConfig.maxRetries"
                :min="0"
                :max="5"
                size="small"
              />
            </div>
          </el-col>
          
          <el-col :span="8">
            <div class="config-section">
              <label>优先级策略</label>
              <el-select v-model="batchConfig.priorityStrategy" size="small">
                <el-option label="文件大小优先" value="size" />
                <el-option label="上传时间优先" value="time" />
                <el-option label="文件类型优先" value="type" />
                <el-option label="自定义优先级" value="custom" />
              </el-select>
            </div>
          </el-col>
        </el-row>
        
        <div class="config-actions">
          <el-button @click="applyConfigToAll" type="primary" size="small">
            应用到所有文件
          </el-button>
          <el-button @click="resetConfig" size="small">
            重置配置
          </el-button>
        </div>
      </div>
    </div>

    <!-- 文件队列管理 -->
    <div class="queue-management">
      <div class="queue-header">
        <div class="queue-info">
          <h4>📋 处理队列</h4>
          <span class="queue-summary">
            总计 {{ totalFiles }} 个文件，预计耗时 {{ estimatedTime }}
          </span>
        </div>
        
        <div class="queue-actions">
          <el-button @click="showConfig = !showConfig" text size="small">
            {{ showConfig ? '隐藏' : '显示' }}配置
          </el-button>
          <el-button @click="optimizeQueue" size="small" :icon="Sort">
            优化队列
          </el-button>
          <el-button @click="clearQueue" size="small" type="danger" plain>
            清空队列
          </el-button>
        </div>
      </div>

      <!-- 队列列表 -->
      <div class="queue-list">
        <div class="queue-controls">
          <el-checkbox 
            v-model="selectAll" 
            @change="handleSelectAll"
            :indeterminate="isIndeterminate"
          >
            全选
          </el-checkbox>
          
          <div class="filter-controls">
            <el-select v-model="statusFilter" size="small" placeholder="状态筛选">
              <el-option label="全部" value="" />
              <el-option label="队列中" value="queued" />
              <el-option label="处理中" value="processing" />
              <el-option label="已完成" value="completed" />
              <el-option label="失败" value="failed" />
            </el-select>
            
            <el-input
              v-model="searchKeyword"
              placeholder="搜索文件名"
              size="small"
              :prefix-icon="Search"
              clearable
            />
          </div>
        </div>

        <div class="file-items">
          <div
            v-for="(file, index) in filteredFiles"
            :key="file.id"
            class="file-item"
            :class="{
              'is-selected': selectedFileIds.includes(file.id),
              'is-processing': file.status === 'processing',
              'is-completed': file.status === 'completed',
              'is-failed': file.status === 'failed'
            }"
          >
            <div class="file-checkbox">
              <el-checkbox 
                :model-value="selectedFileIds.includes(file.id)"
                @change="toggleFileSelection(file.id)"
              />
            </div>
            
            <div class="file-info">
              <div class="file-header">
                <span class="file-name">{{ file.name }}</span>
                <span class="file-priority" v-if="file.priority">
                  优先级: {{ file.priority }}
                </span>
              </div>
              
              <div class="file-meta">
                <span class="file-size">{{ formatFileSize(file.size) }}</span>
                <span class="file-type">{{ getFileExtension(file.name) }}</span>
                <span class="file-duration" v-if="file.duration">
                  {{ formatDuration(file.duration) }}
                </span>
              </div>
              
              <!-- 处理进度 -->
              <div v-if="file.status === 'processing'" class="file-progress">
                <el-progress
                  :percentage="file.progress || 0"
                  :stroke-width="4"
                  :show-text="false"
                  color="var(--accent-primary)"
                />
                <span class="progress-text">{{ file.progress || 0 }}%</span>
                <span class="stage-text" v-if="file.currentStage">
                  {{ file.currentStage }}
                </span>
              </div>
              
              <!-- 错误信息 -->
              <div v-if="file.status === 'failed'" class="file-error">
                <el-icon color="var(--danger-color)"><Warning /></el-icon>
                <span class="error-text">{{ file.error }}</span>
              </div>
            </div>
            
            <div class="file-status">
              <el-icon v-if="file.status === 'queued'" color="var(--text-muted)">
                <Clock />
              </el-icon>
              <el-icon v-else-if="file.status === 'processing'" color="var(--accent-primary)" class="is-loading">
                <Loading />
              </el-icon>
              <el-icon v-else-if="file.status === 'completed'" color="var(--success-color)">
                <CircleCheckFilled />
              </el-icon>
              <el-icon v-else-if="file.status === 'failed'" color="var(--danger-color)">
                <CircleCloseFilled />
              </el-icon>
            </div>
            
            <div class="file-actions">
              <el-dropdown @command="(cmd) => handleFileCommand(cmd, file)">
                <el-button text :icon="MoreFilled" size="small" />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="preview">预览</el-dropdown-item>
                    <el-dropdown-item command="priority">设置优先级</el-dropdown-item>
                    <el-dropdown-item command="config">单独配置</el-dropdown-item>
                    <el-dropdown-item v-if="file.status === 'failed'" command="retry">重试</el-dropdown-item>
                    <el-dropdown-item command="remove" divided>移除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 处理统计 -->
    <div class="processing-stats" v-if="isProcessing || hasProcessedFiles">
      <div class="stats-header">
        <h4>📊 处理统计</h4>
      </div>
      
      <div class="stats-content">
        <el-row :gutter="16">
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ processingStats.totalFiles }}</div>
              <div class="stat-label">总文件数</div>
            </div>
          </el-col>
          
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ processingStats.completedFiles }}</div>
              <div class="stat-label">已完成</div>
            </div>
          </el-col>
          
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ formatTime(processingStats.elapsedTime) }}</div>
              <div class="stat-label">已用时间</div>
            </div>
          </el-col>
          
          <el-col :span="6">
            <div class="stat-item">
              <div class="stat-value">{{ formatTime(processingStats.estimatedRemaining) }}</div>
              <div class="stat-label">预计剩余</div>
            </div>
          </el-col>
        </el-row>
        
        <div class="overall-progress">
          <el-progress
            :percentage="overallProgress"
            :stroke-width="8"
            :show-text="false"
            color="var(--accent-primary)"
          />
          <span class="overall-progress-text">{{ overallProgress }}%</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  VideoPlay,
  VideoPause,
  Close,
  Setting,
  Sort,
  Search,
  Clock,
  Loading,
  CircleCheckFilled,
  CircleCloseFilled,
  Warning,
  MoreFilled
} from '@element-plus/icons-vue'

// 导入API和工具
import { audioFileAPI } from '@/api/audioProcessing'
import { useAudioStore } from '@/stores/audio'

// Props
const props = defineProps({
  files: {
    type: Array,
    default: () => []
  },
  autoStart: {
    type: Boolean,
    default: false
  },
  defaultConfig: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits([
  'batch-start',
  'batch-pause',
  'batch-stop',
  'batch-complete',
  'file-complete',
  'file-error',
  'progress-update'
])

// 使用stores
const audioStore = useAudioStore()

// 响应式数据
const showConfig = ref(false)
const isProcessing = ref(false)
const isPaused = ref(false)
const selectedFileIds = ref([])
const statusFilter = ref('')
const searchKeyword = ref('')
const startTime = ref(null)
const processingQueue = ref([])
const activeProcessors = ref(new Map())

// 批量配置
const batchConfig = ref({
  concurrency: 3,
  maxRetries: 2,
  priorityStrategy: 'time',
  retryDelay: 5000,
  enableSmartScheduling: true,
  maxFileSize: 500 * 1024 * 1024, // 500MB
  timeoutPerFile: 30 * 60 * 1000 // 30分钟
})

// 处理统计
const processingStats = ref({
  totalFiles: 0,
  completedFiles: 0,
  failedFiles: 0,
  elapsedTime: 0,
  estimatedRemaining: 0,
  averageProcessingTime: 0
})

// 计算属性
const queuedFiles = computed(() =>
  props.files.filter(f => f.status === 'queued' || f.status === 'ready')
)

const processingFiles = computed(() =>
  props.files.filter(f => f.status === 'processing')
)

const completedFiles = computed(() =>
  props.files.filter(f => f.status === 'completed')
)

const failedFiles = computed(() =>
  props.files.filter(f => f.status === 'failed')
)

const totalFiles = computed(() => props.files.length)

const hasProcessedFiles = computed(() =>
  completedFiles.value.length > 0 || failedFiles.value.length > 0
)

const filteredFiles = computed(() => {
  let files = [...props.files]

  // 状态筛选
  if (statusFilter.value) {
    files = files.filter(f => f.status === statusFilter.value)
  }

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    files = files.filter(f =>
      f.name.toLowerCase().includes(keyword)
    )
  }

  return files
})

const selectAll = computed({
  get: () => {
    return filteredFiles.value.length > 0 &&
           filteredFiles.value.every(f => selectedFileIds.value.includes(f.id))
  },
  set: (value) => {
    if (value) {
      selectedFileIds.value = [...new Set([
        ...selectedFileIds.value,
        ...filteredFiles.value.map(f => f.id)
      ])]
    } else {
      const filteredIds = filteredFiles.value.map(f => f.id)
      selectedFileIds.value = selectedFileIds.value.filter(id => !filteredIds.includes(id))
    }
  }
})

const isIndeterminate = computed(() => {
  const selectedInFiltered = filteredFiles.value.filter(f =>
    selectedFileIds.value.includes(f.id)
  ).length
  return selectedInFiltered > 0 && selectedInFiltered < filteredFiles.value.length
})

const overallProgress = computed(() => {
  if (totalFiles.value === 0) return 0

  const completed = completedFiles.value.length
  const processing = processingFiles.value.reduce((sum, file) => {
    return sum + (file.progress || 0) / 100
  }, 0)

  return Math.round(((completed + processing) / totalFiles.value) * 100)
})

const estimatedTime = computed(() => {
  if (processingStats.value.averageProcessingTime === 0) {
    return '未知'
  }

  const remainingFiles = queuedFiles.value.length + processingFiles.value.length
  const estimatedSeconds = remainingFiles * processingStats.value.averageProcessingTime

  return formatTime(estimatedSeconds)
})

// 批量处理方法
const startBatchProcessing = async () => {
  if (queuedFiles.value.length === 0) {
    ElMessage.warning('没有可处理的文件')
    return
  }

  try {
    isProcessing.value = true
    isPaused.value = false
    startTime.value = Date.now()

    // 初始化统计
    processingStats.value = {
      totalFiles: totalFiles.value,
      completedFiles: completedFiles.value.length,
      failedFiles: failedFiles.value.length,
      elapsedTime: 0,
      estimatedRemaining: 0,
      averageProcessingTime: 0
    }

    // 优化队列顺序
    optimizeQueue()

    // 准备处理队列
    processingQueue.value = [...queuedFiles.value]

    // 开始并发处理
    for (let i = 0; i < Math.min(batchConfig.value.concurrency, processingQueue.value.length); i++) {
      processNextFile()
    }

    emit('batch-start', {
      totalFiles: totalFiles.value,
      config: batchConfig.value
    })

    ElMessage.success('批量处理已开始')

  } catch (error) {
    isProcessing.value = false
    ElMessage.error(`启动批量处理失败: ${error.message}`)
  }
}

const pauseBatchProcessing = () => {
  isPaused.value = !isPaused.value

  if (isPaused.value) {
    // 暂停所有活跃的处理器
    activeProcessors.value.forEach(processor => {
      if (processor.pause) {
        processor.pause()
      }
    })

    emit('batch-pause', true)
    ElMessage.info('批量处理已暂停')
  } else {
    // 恢复处理
    activeProcessors.value.forEach(processor => {
      if (processor.resume) {
        processor.resume()
      }
    })

    emit('batch-pause', false)
    ElMessage.info('批量处理已恢复')
  }
}

const stopBatchProcessing = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要停止批量处理吗？正在处理的任务将被取消。',
      '确认停止',
      {
        confirmButtonText: '停止处理',
        cancelButtonText: '继续处理',
        type: 'warning'
      }
    )

    isProcessing.value = false
    isPaused.value = false

    // 取消所有活跃的处理器
    activeProcessors.value.forEach(async (processor, fileId) => {
      if (processor.cancel) {
        await processor.cancel()
      }
    })

    activeProcessors.value.clear()
    processingQueue.value = []

    emit('batch-stop')
    ElMessage.info('批量处理已停止')

  } catch (error) {
    // 用户取消操作
  }
}

// 核心处理方法
const processNextFile = async () => {
  if (isPaused.value || !isProcessing.value || processingQueue.value.length === 0) {
    return
  }

  const file = processingQueue.value.shift()
  if (!file) return

  try {
    // 更新文件状态
    file.status = 'processing'
    file.progress = 0
    file.startTime = Date.now()

    // 创建处理器
    const processor = await createFileProcessor(file)
    activeProcessors.value.set(file.id, processor)

    // 开始处理
    const result = await processor.process()

    // 处理成功
    file.status = 'completed'
    file.progress = 100
    file.result = result
    file.completedTime = Date.now()
    file.processingTime = file.completedTime - file.startTime

    // 更新统计
    updateProcessingStats()

    emit('file-complete', { file, result })

  } catch (error) {
    // 处理失败
    file.status = 'failed'
    file.error = error.message
    file.retryCount = (file.retryCount || 0) + 1

    // 检查是否需要重试
    if (file.retryCount < batchConfig.value.maxRetries) {
      setTimeout(() => {
        file.status = 'queued'
        processingQueue.value.unshift(file) // 重新加入队列头部
        processNextFile()
      }, batchConfig.value.retryDelay)
    } else {
      updateProcessingStats()
      emit('file-error', { file, error })
    }
  } finally {
    // 清理处理器
    activeProcessors.value.delete(file.id)

    // 处理下一个文件
    if (processingQueue.value.length > 0 && isProcessing.value) {
      processNextFile()
    } else if (activeProcessors.value.size === 0 && isProcessing.value) {
      // 所有文件处理完成
      completeBatchProcessing()
    }
  }
}

const createFileProcessor = async (file) => {
  // 创建文件处理器
  const processor = {
    file,
    taskId: null,

    async process() {
      // 创建处理任务
      const taskConfig = {
        file_id: file.id,
        mode: props.defaultConfig.mode || 'speech-recognition',
        config: { ...props.defaultConfig, ...file.customConfig }
      }

      const task = await audioFileAPI.createProcessingTask(taskConfig)
      this.taskId = task.id

      // 监听进度更新
      return new Promise((resolve, reject) => {
        const progressHandler = (data) => {
          if (data.task_id === this.taskId) {
            file.progress = data.progress || 0
            file.currentStage = data.stage || ''

            emit('progress-update', { file, progress: data.progress, stage: data.stage })
          }
        }

        const completeHandler = (data) => {
          if (data.task_id === this.taskId) {
            resolve(data.result)
          }
        }

        const errorHandler = (data) => {
          if (data.task_id === this.taskId) {
            reject(new Error(data.error || '处理失败'))
          }
        }

        // 这里应该连接到WebSocket或轮询API来监听任务状态
        // 简化实现，使用模拟进度
        this.simulateProgress(resolve, reject)
      })
    },

    async pause() {
      if (this.taskId) {
        await audioFileAPI.pauseTask(this.taskId)
      }
    },

    async resume() {
      if (this.taskId) {
        await audioFileAPI.resumeTask(this.taskId)
      }
    },

    async cancel() {
      if (this.taskId) {
        await audioFileAPI.cancelTask(this.taskId)
      }
    },

    // 模拟进度更新（实际应该通过WebSocket接收）
    simulateProgress(resolve, reject) {
      let progress = 0
      const stages = ['初始化', '文件验证', '模型加载', '音频处理', '结果生成']
      let currentStageIndex = 0

      const interval = setInterval(() => {
        if (isPaused.value) return

        progress += Math.random() * 10
        if (progress > 100) progress = 100

        // 更新阶段
        const stageProgress = Math.floor(progress / 20)
        if (stageProgress > currentStageIndex && currentStageIndex < stages.length - 1) {
          currentStageIndex = stageProgress
        }

        file.progress = Math.floor(progress)
        file.currentStage = stages[currentStageIndex]

        emit('progress-update', {
          file,
          progress: file.progress,
          stage: file.currentStage
        })

        if (progress >= 100) {
          clearInterval(interval)
          resolve({
            transcription: '模拟转录结果...',
            duration: file.duration || 60,
            confidence: 0.95
          })
        }
      }, 500 + Math.random() * 1000) // 随机间隔模拟真实处理
    }
  }

  return processor
}

const completeBatchProcessing = () => {
  isProcessing.value = false
  isPaused.value = false

  const endTime = Date.now()
  const totalTime = endTime - startTime.value

  emit('batch-complete', {
    totalFiles: totalFiles.value,
    completedFiles: completedFiles.value.length,
    failedFiles: failedFiles.value.length,
    totalTime,
    averageTime: totalTime / totalFiles.value
  })

  ElMessage.success(`批量处理完成！成功: ${completedFiles.value.length}, 失败: ${failedFiles.value.length}`)
}

const updateProcessingStats = () => {
  const now = Date.now()
  const elapsed = startTime.value ? (now - startTime.value) / 1000 : 0

  const completed = completedFiles.value.length
  const failed = failedFiles.value.length
  const remaining = queuedFiles.value.length + processingFiles.value.length

  // 计算平均处理时间
  const completedWithTime = completedFiles.value.filter(f => f.processingTime)
  const avgTime = completedWithTime.length > 0
    ? completedWithTime.reduce((sum, f) => sum + f.processingTime, 0) / completedWithTime.length / 1000
    : 0

  processingStats.value = {
    totalFiles: totalFiles.value,
    completedFiles: completed,
    failedFiles: failed,
    elapsedTime: elapsed,
    estimatedRemaining: remaining * avgTime,
    averageProcessingTime: avgTime
  }
}

// 队列管理方法
const optimizeQueue = () => {
  const files = [...queuedFiles.value]

  switch (batchConfig.value.priorityStrategy) {
    case 'size':
      // 小文件优先
      files.sort((a, b) => a.size - b.size)
      break
    case 'time':
      // 最早上传优先
      files.sort((a, b) => new Date(a.uploadedAt) - new Date(b.uploadedAt))
      break
    case 'type':
      // 按文件类型分组
      files.sort((a, b) => {
        const extA = getFileExtension(a.name)
        const extB = getFileExtension(b.name)
        return extA.localeCompare(extB)
      })
      break
    case 'custom':
      // 自定义优先级
      files.sort((a, b) => (b.priority || 0) - (a.priority || 0))
      break
  }

  // 更新文件顺序
  files.forEach((file, index) => {
    file.queueIndex = index
  })

  ElMessage.success('队列已优化')
}

const clearQueue = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空处理队列吗？这将移除所有未处理的文件。',
      '确认清空',
      {
        confirmButtonText: '清空',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 清空队列中的文件
    const queuedIds = queuedFiles.value.map(f => f.id)
    queuedIds.forEach(id => {
      const index = props.files.findIndex(f => f.id === id)
      if (index > -1) {
        props.files.splice(index, 1)
      }
    })

    ElMessage.success('队列已清空')

  } catch (error) {
    // 用户取消操作
  }
}

// 文件操作方法
const toggleFileSelection = (fileId) => {
  const index = selectedFileIds.value.indexOf(fileId)
  if (index > -1) {
    selectedFileIds.value.splice(index, 1)
  } else {
    selectedFileIds.value.push(fileId)
  }
}

const handleSelectAll = (value) => {
  selectAll.value = value
}

const handleFileCommand = async (command, file) => {
  switch (command) {
    case 'preview':
      // 预览文件
      emit('file-preview', file)
      break

    case 'priority':
      // 设置优先级
      await setPriority(file)
      break

    case 'config':
      // 单独配置
      await setFileConfig(file)
      break

    case 'retry':
      // 重试处理
      file.status = 'queued'
      file.retryCount = 0
      file.error = null
      ElMessage.info('文件已重新加入队列')
      break

    case 'remove':
      // 移除文件
      await removeFile(file)
      break
  }
}

const setPriority = async (file) => {
  try {
    const { value: priority } = await ElMessageBox.prompt(
      '请设置文件优先级（1-10，数字越大优先级越高）',
      '设置优先级',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^([1-9]|10)$/,
        inputErrorMessage: '请输入1-10之间的数字'
      }
    )

    file.priority = parseInt(priority)
    ElMessage.success('优先级设置成功')

  } catch (error) {
    // 用户取消操作
  }
}

const setFileConfig = async (file) => {
  // 这里可以打开一个配置对话框
  ElMessage.info('文件配置功能开发中...')
}

const removeFile = async (file) => {
  try {
    await ElMessageBox.confirm(
      `确定要移除文件 "${file.name}" 吗？`,
      '确认移除',
      {
        confirmButtonText: '移除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const index = props.files.findIndex(f => f.id === file.id)
    if (index > -1) {
      props.files.splice(index, 1)
    }

    // 从选中列表中移除
    const selectedIndex = selectedFileIds.value.indexOf(file.id)
    if (selectedIndex > -1) {
      selectedFileIds.value.splice(selectedIndex, 1)
    }

    ElMessage.success('文件已移除')

  } catch (error) {
    // 用户取消操作
  }
}

// 批量操作方法
const handleBatchCommand = async (command) => {
  switch (command) {
    case 'clear-completed':
      await clearCompleted()
      break
    case 'retry-failed':
      await retryFailed()
      break
    case 'export-results':
      await exportResults()
      break
    case 'save-template':
      await saveTemplate()
      break
  }
}

const clearCompleted = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清除所有已完成的文件吗？',
      '确认清除',
      {
        confirmButtonText: '清除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const completedIds = completedFiles.value.map(f => f.id)
    completedIds.forEach(id => {
      const index = props.files.findIndex(f => f.id === id)
      if (index > -1) {
        props.files.splice(index, 1)
      }
    })

    ElMessage.success('已完成文件已清除')

  } catch (error) {
    // 用户取消操作
  }
}

const retryFailed = () => {
  failedFiles.value.forEach(file => {
    file.status = 'queued'
    file.retryCount = 0
    file.error = null
  })

  ElMessage.success(`${failedFiles.value.length} 个失败文件已重新加入队列`)
}

const exportResults = () => {
  // 导出处理结果
  ElMessage.info('导出结果功能开发中...')
}

const saveTemplate = async () => {
  try {
    const { value: templateName } = await ElMessageBox.prompt(
      '请输入模板名称',
      '保存批量处理模板',
      {
        confirmButtonText: '保存',
        cancelButtonText: '取消',
        inputPattern: /^.{1,50}$/,
        inputErrorMessage: '模板名称长度应在1-50个字符之间'
      }
    )

    const template = {
      name: templateName,
      config: { ...batchConfig.value },
      processingConfig: { ...props.defaultConfig },
      createdAt: new Date()
    }

    // 保存模板到本地存储
    const templates = JSON.parse(localStorage.getItem('batch-templates') || '[]')
    templates.push(template)
    localStorage.setItem('batch-templates', JSON.stringify(templates))

    ElMessage.success('批量处理模板已保存')

  } catch (error) {
    // 用户取消操作
  }
}

// 配置方法
const applyConfigToAll = () => {
  queuedFiles.value.forEach(file => {
    file.customConfig = { ...props.defaultConfig }
  })

  ElMessage.success('配置已应用到所有文件')
}

const resetConfig = () => {
  batchConfig.value = {
    concurrency: 3,
    maxRetries: 2,
    priorityStrategy: 'time',
    retryDelay: 5000,
    enableSmartScheduling: true,
    maxFileSize: 500 * 1024 * 1024,
    timeoutPerFile: 30 * 60 * 1000
  }

  ElMessage.success('配置已重置')
}

// 工具方法
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getFileExtension = (filename) => {
  return filename.slice((filename.lastIndexOf('.') - 1 >>> 0) + 2).toUpperCase()
}

const formatDuration = (seconds) => {
  if (!seconds) return ''
  const mins = Math.floor(seconds / 60)
  const secs = Math.floor(seconds % 60)
  return `${mins}:${secs.toString().padStart(2, '0')}`
}

const formatTime = (seconds) => {
  if (!seconds || seconds < 0) return '00:00'

  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = Math.floor(seconds % 60)

  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 生命周期
onMounted(() => {
  // 如果设置了自动开始，则自动启动处理
  if (props.autoStart && queuedFiles.value.length > 0) {
    nextTick(() => {
      startBatchProcessing()
    })
  }

  // 定时更新统计信息
  const statsInterval = setInterval(() => {
    if (isProcessing.value) {
      updateProcessingStats()
    }
  }, 1000)

  onUnmounted(() => {
    clearInterval(statsInterval)
  })
})

onUnmounted(() => {
  // 清理所有活跃的处理器
  activeProcessors.value.forEach(async (processor) => {
    if (processor.cancel) {
      await processor.cancel()
    }
  })

  activeProcessors.value.clear()
})

// 监听文件变化
watch(() => props.files, (newFiles) => {
  // 更新统计信息
  if (isProcessing.value) {
    updateProcessingStats()
  }
}, { deep: true })

// 暴露方法给父组件
defineExpose({
  startBatchProcessing,
  pauseBatchProcessing,
  stopBatchProcessing,
  optimizeQueue,
  clearQueue,
  isProcessing: computed(() => isProcessing.value),
  processingStats: computed(() => processingStats.value)
})
</script>

<style scoped>
.batch-processor {
  background: var(--card-bg);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
  overflow: hidden;
}

/* 批量处理头部 */
.batch-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  background: var(--surface-bg);
  border-bottom: 1px solid var(--border-color);
}

.header-info h3 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
  font-size: 1.1rem;
}

.batch-stats {
  display: flex;
  gap: var(--spacing-sm);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

/* 批量配置面板 */
.batch-config {
  background: var(--input-bg);
  border-bottom: 1px solid var(--border-color);
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.config-header h4 {
  margin: 0;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.config-content {
  padding: var(--spacing-lg);
}

.config-section {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.config-section label {
  font-size: 0.85rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.config-actions {
  display: flex;
  gap: var(--spacing-sm);
  margin-top: var(--spacing-lg);
  justify-content: flex-end;
}

/* 队列管理 */
.queue-management {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.queue-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.queue-info h4 {
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--text-primary);
  font-size: 1rem;
}

.queue-summary {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.queue-actions {
  display: flex;
  gap: var(--spacing-sm);
}

/* 队列列表 */
.queue-list {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.queue-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  background: var(--input-bg);
  border-bottom: 1px solid var(--border-color);
}

.filter-controls {
  display: flex;
  gap: var(--spacing-sm);
}

.file-items {
  flex: 1;
  overflow-y: auto;
  max-height: 400px;
}

.file-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
  transition: all 0.2s ease;
}

.file-item:hover {
  background: var(--input-bg);
}

.file-item.is-selected {
  background: rgba(88, 166, 255, 0.1);
  border-left: 3px solid var(--accent-primary);
}

.file-item.is-processing {
  background: rgba(255, 193, 7, 0.1);
}

.file-item.is-completed {
  background: rgba(40, 167, 69, 0.1);
}

.file-item.is-failed {
  background: rgba(248, 81, 73, 0.1);
}

.file-checkbox {
  flex-shrink: 0;
}

.file-info {
  flex: 1;
  min-width: 0;
}

.file-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xs);
}

.file-name {
  font-weight: 500;
  color: var(--text-primary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-priority {
  font-size: 0.8rem;
  color: var(--accent-primary);
  font-weight: 500;
}

.file-meta {
  display: flex;
  gap: var(--spacing-md);
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-xs);
}

.file-progress {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.file-progress .el-progress {
  flex: 1;
  min-width: 100px;
}

.progress-text {
  font-size: 0.8rem;
  color: var(--text-secondary);
  min-width: 35px;
}

.stage-text {
  font-size: 0.8rem;
  color: var(--accent-primary);
}

.file-error {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs);
  background: rgba(248, 81, 73, 0.1);
  border-radius: var(--radius-sm);
}

.error-text {
  font-size: 0.8rem;
  color: var(--danger-color);
}

.file-status {
  flex-shrink: 0;
  font-size: 1.2rem;
}

.file-actions {
  flex-shrink: 0;
}

/* 处理统计 */
.processing-stats {
  background: var(--surface-bg);
  border-top: 1px solid var(--border-color);
}

.stats-header {
  padding: var(--spacing-md) var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.stats-header h4 {
  margin: 0;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.stats-content {
  padding: var(--spacing-lg);
}

.stat-item {
  text-align: center;
  padding: var(--spacing-md);
  background: var(--input-bg);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--accent-primary);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

.overall-progress {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-top: var(--spacing-lg);
}

.overall-progress .el-progress {
  flex: 1;
}

.overall-progress-text {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--accent-primary);
  min-width: 50px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .batch-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }

  .queue-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: flex-start;
  }

  .queue-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .queue-controls {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: flex-start;
  }

  .filter-controls {
    width: 100%;
    justify-content: space-between;
  }

  .file-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .file-header {
    width: 100%;
  }

  .file-meta {
    width: 100%;
    justify-content: space-between;
  }

  .file-progress {
    width: 100%;
  }

  .config-content .el-row {
    flex-direction: column;
  }

  .config-content .el-col {
    width: 100%;
    margin-bottom: var(--spacing-md);
  }
}

/* 动画效果 */
.file-item {
  animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滚动条样式 */
.file-items::-webkit-scrollbar {
  width: 6px;
}

.file-items::-webkit-scrollbar-track {
  background: var(--input-bg);
}

.file-items::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.file-items::-webkit-scrollbar-thumb:hover {
  background: var(--accent-primary);
}

/* 状态指示器 */
.file-item.is-processing .file-name::before {
  content: '⚡';
  margin-right: var(--spacing-xs);
}

.file-item.is-completed .file-name::before {
  content: '✅';
  margin-right: var(--spacing-xs);
}

.file-item.is-failed .file-name::before {
  content: '❌';
  margin-right: var(--spacing-xs);
}

/* 优先级指示器 */
.file-priority {
  position: relative;
}

.file-priority::before {
  content: '🔥';
  margin-right: var(--spacing-xs);
}

/* 加载动画 */
.is-loading {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
