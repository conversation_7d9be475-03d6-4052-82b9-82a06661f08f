#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全启动脚本 - 避免torch日志错误
"""

import os
import warnings
import logging

# 设置环境变量
os.environ['TORCH_LOGS'] = ''
os.environ['TORCH_LOG_LEVEL'] = 'ERROR'
os.environ['TORCH_SHOW_CPP_STACKTRACES'] = '0'
os.environ['PYTHONWARNINGS'] = 'ignore'

# 禁用警告
warnings.filterwarnings('ignore')
logging.getLogger().setLevel(logging.ERROR)

# 尝试设置torch日志
try:
    import torch._logging
    torch._logging.set_logs(all=logging.ERROR)
except:
    pass

# 导入streamlit并运行
if __name__ == "__main__":
    import subprocess
    import sys
    
    cmd = [
        sys.executable, "-m", "streamlit", "run", 
        "pages/语音处理分析.py",
        "--server.headless", "true",
        "--server.port", "8501"
    ]
    
    subprocess.run(cmd, env=os.environ.copy())
