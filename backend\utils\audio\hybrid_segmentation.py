"""
混合分段策略模块
实现智能VAD分段 + 强制时间窗口分割的混合策略
解决说话人识别中分段不足的根本问题
"""

import os
import tempfile
import shutil
import logging
from typing import List, Tuple, Dict, Optional
import numpy as np
import librosa
import soundfile as sf

logger = logging.getLogger(__name__)


class HybridSegmentationStrategy:
    """混合分段策略：智能VAD + 强制时间窗口"""
    
    def __init__(self, config: Dict):
        """
        初始化混合分段策略
        
        Args:
            config: 配置字典，包含以下参数：
                - segmentation_strategy: 分段策略 ('vad_only', 'hybrid', 'time_window')
                - time_window_size: 时间窗口大小（秒）
                - min_segments_required: 最小分段数量要求
                - overlap_ratio: 重叠比例
                - force_split_threshold: 强制分割阈值（秒）
        """
        self.strategy = config.get('segmentation_strategy', 'hybrid')
        self.time_window_size = config.get('time_window_size', 4.0)
        self.min_segments_required = config.get('min_segments_required', 3)
        self.overlap_ratio = config.get('overlap_ratio', 0.1)
        self.force_split_threshold = config.get('force_split_threshold', 8.0)
        
        logger.info(f"混合分段策略初始化: {self.strategy}, 窗口大小: {self.time_window_size}s, "
                   f"最小分段数: {self.min_segments_required}, 强制分割阈值: {self.force_split_threshold}s")
    
    def segment_audio(self, audio_path: str, vad_model, vad_params: Dict) -> List[Tuple[float, float]]:
        """
        执行混合分段策略
        
        Args:
            audio_path: 音频文件路径
            vad_model: VAD模型
            vad_params: VAD参数
            
        Returns:
            分段结果列表，每个元素为(start_time, end_time)
        """
        logger.info(f"开始执行混合分段策略: {self.strategy}")
        
        try:
            # 第一阶段：VAD智能分段
            vad_segments = self._vad_segmentation(audio_path, vad_model, vad_params)
            logger.info(f"VAD分段结果: {len(vad_segments)}个片段")
            
            # 检查VAD分段结果是否充足
            if self._is_segmentation_sufficient(vad_segments):
                logger.info(f"VAD分段充足，直接使用VAD结果")
                return self._optimize_segments(vad_segments)
            
            # 第二阶段：根据策略选择处理方式
            if self.strategy == 'hybrid':
                logger.info("VAD分段不足，启用混合策略")
                return self._hybrid_segmentation(audio_path, vad_segments)
            elif self.strategy == 'time_window':
                logger.info("使用纯时间窗口分段")
                return self._time_window_segmentation(audio_path)
            else:  # vad_only
                logger.warning("VAD分段不足但策略为vad_only，返回VAD结果")
                return self._optimize_segments(vad_segments)
                
        except Exception as e:
            logger.error(f"混合分段策略执行失败: {e}")
            # 降级到时间窗口分段
            logger.info("降级到时间窗口分段")
            return self._time_window_segmentation(audio_path)
    
    def _vad_segmentation(self, audio_path: str, vad_model, vad_params: Dict) -> List[Tuple[float, float]]:
        """VAD智能分段"""
        try:
            logger.debug(f"执行VAD分段，参数: {vad_params}")
            
            # 使用FunASR进行VAD分割
            res = vad_model.generate(
                input=audio_path,
                batch_size_s=60,
                merge_vad=True,
                merge_length_s=vad_params.get("merge_length_s", 5)
            )
            
            if res and len(res) > 0 and "value" in res[0] and res[0]["value"]:
                # 转换时间单位从毫秒到秒
                segments = [(start/1000, end/1000) for start, end in res[0]["value"]]
                # 过滤和优化分段
                filtered_segments = self._filter_segments(segments, vad_params)
                logger.debug(f"VAD分段完成: {len(segments)} -> {len(filtered_segments)}个有效片段")
                return filtered_segments
            else:
                logger.warning("VAD模型未返回有效结果")
                return []
                
        except Exception as e:
            logger.error(f"VAD分段失败: {e}")
            return []
    
    def _filter_segments(self, segments: List[Tuple[float, float]], vad_params: Dict) -> List[Tuple[float, float]]:
        """过滤和优化分段"""
        if not segments:
            return []
        
        min_duration = vad_params.get("min_speech_duration", 0.3)
        max_duration = vad_params.get("max_speech_duration", 60)
        
        filtered = []
        for start, end in segments:
            duration = end - start
            if min_duration <= duration <= max_duration:
                filtered.append((start, end))
            else:
                logger.debug(f"过滤片段: {start:.2f}-{end:.2f}s (时长: {duration:.2f}s)")
        
        return filtered
    
    def _is_segmentation_sufficient(self, segments: List[Tuple[float, float]]) -> bool:
        """检查分段是否充足"""
        if len(segments) < self.min_segments_required:
            logger.debug(f"分段数量不足: {len(segments)} < {self.min_segments_required}")
            return False
        
        # 检查是否有过长的片段
        long_segments = 0
        for start, end in segments:
            if (end - start) > self.force_split_threshold:
                long_segments += 1
        
        if long_segments > 0:
            logger.debug(f"发现{long_segments}个过长片段(>{self.force_split_threshold}s)")
            return False
        
        return True
    
    def _hybrid_segmentation(self, audio_path: str, vad_segments: List[Tuple[float, float]]) -> List[Tuple[float, float]]:
        """混合分段：VAD + 时间窗口"""
        final_segments = []
        
        # 处理VAD分段结果
        for start, end in vad_segments:
            duration = end - start
            
            if duration <= self.force_split_threshold:
                # 短片段直接保留
                final_segments.append((start, end))
            else:
                # 长片段进行时间窗口分割
                logger.debug(f"分割长片段: {start:.2f}-{end:.2f}s (时长: {duration:.2f}s)")
                sub_segments = self._split_long_segment(start, end)
                final_segments.extend(sub_segments)
        
        # 如果分段仍然不足，补充时间窗口分段
        if len(final_segments) < self.min_segments_required:
            logger.info(f"混合分段后仍不足({len(final_segments)}个)，补充时间窗口分段")
            audio_duration = self._get_audio_duration(audio_path)
            time_segments = self._time_window_segmentation_full(audio_duration)
            final_segments = self._merge_segments(final_segments, time_segments)
        
        # 排序并优化
        final_segments = sorted(final_segments, key=lambda x: x[0])
        return self._optimize_segments(final_segments)
    
    def _split_long_segment(self, start: float, end: float) -> List[Tuple[float, float]]:
        """分割长片段为时间窗口"""
        segments = []
        current = start
        overlap = self.time_window_size * self.overlap_ratio
        
        while current < end:
            segment_end = min(current + self.time_window_size, end)
            segments.append((current, segment_end))
            
            # 计算下一个窗口的起始位置（考虑重叠）
            current = segment_end - overlap
            
            # 如果剩余时间太短，直接结束
            if segment_end >= end or (end - current) < (self.time_window_size * 0.3):
                break
        
        logger.debug(f"长片段分割结果: {len(segments)}个子片段")
        return segments
    
    def _time_window_segmentation(self, audio_path: str) -> List[Tuple[float, float]]:
        """纯时间窗口分段"""
        try:
            audio_duration = self._get_audio_duration(audio_path)
            return self._time_window_segmentation_full(audio_duration)
        except Exception as e:
            logger.error(f"时间窗口分段失败: {e}")
            return []
    
    def _time_window_segmentation_full(self, audio_duration: float) -> List[Tuple[float, float]]:
        """完整的时间窗口分段"""
        segments = []
        current = 0.0
        overlap = self.time_window_size * self.overlap_ratio
        
        while current < audio_duration:
            segment_end = min(current + self.time_window_size, audio_duration)
            segments.append((current, segment_end))
            
            current = segment_end - overlap
            
            # 避免最后一个片段太短
            if (audio_duration - current) < (self.time_window_size * 0.5):
                break
        
        logger.info(f"时间窗口分段完成: {len(segments)}个片段，总时长: {audio_duration:.2f}s")
        return segments
    
    def _get_audio_duration(self, audio_path: str) -> float:
        """获取音频时长"""
        try:
            # 使用librosa获取音频时长（更快）
            duration = librosa.get_duration(path=audio_path)
            return duration
        except Exception as e:
            logger.error(f"获取音频时长失败: {e}")
            return 60.0  # 默认时长
    
    def _merge_segments(self, segments1: List[Tuple[float, float]], 
                       segments2: List[Tuple[float, float]]) -> List[Tuple[float, float]]:
        """合并两组分段，去除重叠"""
        all_segments = segments1 + segments2
        if not all_segments:
            return []
        
        # 排序
        all_segments.sort(key=lambda x: x[0])
        
        # 去除重叠
        merged = [all_segments[0]]
        for current in all_segments[1:]:
            last = merged[-1]
            
            # 检查重叠
            if current[0] <= last[1]:
                # 有重叠，合并
                merged[-1] = (last[0], max(last[1], current[1]))
            else:
                # 无重叠，添加
                merged.append(current)
        
        logger.debug(f"分段合并: {len(all_segments)} -> {len(merged)}个片段")
        return merged
    
    def _optimize_segments(self, segments: List[Tuple[float, float]]) -> List[Tuple[float, float]]:
        """优化分段结果"""
        if not segments:
            return []
        
        # 移除过短的片段
        min_duration = 0.5
        optimized = []
        
        for start, end in segments:
            if (end - start) >= min_duration:
                optimized.append((start, end))
            else:
                logger.debug(f"移除过短片段: {start:.2f}-{end:.2f}s")
        
        logger.info(f"分段优化完成: {len(segments)} -> {len(optimized)}个有效片段")
        return optimized
