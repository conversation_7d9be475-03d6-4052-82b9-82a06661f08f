# 依赖关系和技术栈

## Python后端依赖

### 核心框架
- **FastAPI**: Web API框架
- **SQLAlchemy**: ORM数据库操作
- **Celery**: 异步任务队列
- **Redis**: 缓存和消息队列
- **Pydantic**: 数据验证和序列化

### AI和机器学习
- **PyTorch**: 深度学习框架 (>=2.0.0, <2.2.0)
- **Transformers**: HuggingFace模型库 (>=4.51.0) - 支持Qwen3 rerank
- **FunASR**: 语音识别框架 (>=1.0.0) - 完全离线配置
- **ModelScope**: 模型管理平台 (>=1.9.5) - 仅用于模型下载
- **sentence-transformers**: 句子嵌入模型
- **NumPy**: 数值计算库 (2.3.0) - 兼容NumPy 2.x

### 语音处理 (完全离线化)
- **SpeechRecognition**: 语音识别库 (>=3.10.0)
- **pydub**: 音频处理 (>=0.25.1)
- **soundfile**: 音频文件读写 (>=0.12.1)
- **librosa**: 音频分析 (>=0.10.0)
- **torchaudio**: PyTorch音频处理
- **SenseVoiceSmall**: 语音识别主模型 (本地化)
- **fsmn_vad_zh**: VAD检测模型 (本地化)
- **CAM++**: 说话人识别模型 (本地化)

### 文档处理
- **PyPDF2**: PDF处理 (>=3.0.0)
- **PyMuPDF**: PDF高级处理 (>=1.21.1)
- **python-docx**: Word文档处理
- **python-pptx**: PowerPoint处理
- **openpyxl**: Excel处理
- **pytesseract**: OCR文字识别
- **opencv-python**: 图像处理

### 向量数据库和RAG
- **llama-index**: RAG框架 (>=0.9.0, <0.11.0)
- **chromadb**: 向量数据库 (>=0.4.0, <0.5.0)
- **langchain**: LLM应用框架
- **Qwen3-Reranker-0.6B**: 重排序模型

## 前端依赖

### 核心框架
- **Vue**: 前端框架 (^3.3.4)
- **Vue Router**: 路由管理 (^4.2.4)
- **Pinia**: 状态管理 (^2.1.6)
- **Vite**: 构建工具 (^4.4.9)

### UI和样式
- **Element Plus**: UI组件库 (^2.3.9)
- **@element-plus/icons-vue**: 图标库 (^2.1.0)
- **sass**: CSS预处理器 (^1.66.1)

### 工具库
- **axios**: HTTP客户端 (^1.5.0)
- **echarts**: 图表库 (^5.4.3)
- **dayjs**: 日期处理 (^1.11.9)
- **js-cookie**: Cookie管理 (^3.0.5)
- **nprogress**: 进度条 (^0.2.0)

## 开发工具和环境

### 包管理和构建
- **uv**: Python包管理器 (替代pip，支持阿里源)
- **npm**: Node.js包管理器
- **Docker**: 容器化 (仅Redis服务)
- **PowerShell**: Windows命令行环境

### 代码质量
- **Prettier**: 代码格式化
- **ESLint**: 代码检查
- **Black**: Python代码格式化 (可选)
- **Flake8**: Python代码检查 (可选)

### 测试框架
- **Playwright**: 前端自动化测试 (通过MCP工具)
- **pytest**: Python单元测试 (可选)
- **自定义测试脚本**: 音频处理功能测试

## 模型和数据存储

### AI模型存储
- **本地路径**: D:\MYproject\MYproject\my_notebook\my_notebook_version_0.1.0\models\model_dir
- **SenseVoiceSmall**: 语音识别主模型
- **fsmn_vad_zh**: VAD语音活动检测模型
- **cam++**: 说话人识别模型
- **Qwen3-Reranker**: 文本重排序模型

### 数据库
- **SQLite**: 主数据库 (data/speech_platform.db)
- **ChromaDB**: 向量数据库 (文档嵌入存储)
- **Redis**: 缓存和任务队列

### 文件存储
- **音频文件**: 用户上传的音频文件
- **文档文件**: PDF、Word等文档文件
- **处理结果**: 转录结果、分析报告等
- **日志文件**: 系统运行日志

## 离线运行配置

### 环境变量设置
```python
offline_vars = {
    'HF_HUB_OFFLINE': '1',
    'HF_DATASETS_OFFLINE': '1', 
    'TRANSFORMERS_OFFLINE': '1',
    'HF_HUB_DISABLE_TELEMETRY': '1',
    'HF_HUB_DISABLE_PROGRESS_BARS': '1',
    'HF_HUB_DISABLE_SYMLINKS_WARNING': '1',
}
```

### 模型路径配置
- **环境变量**: MODEL_BASE_PATH, VAD_MODEL_PATH等
- **验证机制**: 启动前验证所有模型路径有效性
- **错误处理**: 模型缺失时提供明确错误信息

## 性能优化配置

### GPU加速
- **PyTorch**: CUDA支持，自动GPU检测
- **模型推理**: GPU加速的语音识别和说话人识别
- **内存管理**: GPU内存优化和释放机制

### 并发处理
- **Celery**: 多线程模式，4个工作线程
- **任务队列**: 分类队列处理不同类型任务
- **资源控制**: 防止资源过度占用

### 缓存机制
- **Redis**: 任务状态和进度缓存
- **模型缓存**: 避免重复加载模型
- **结果缓存**: 处理结果临时缓存

## 网络和安全

### API安全
- **JWT认证**: 用户身份验证
- **CORS配置**: 跨域请求控制
- **输入验证**: Pydantic数据验证
- **文件上传**: 文件类型和大小限制

### 网络配置
- **端口配置**: 前端3000，后端8002，Redis 6379
- **WebSocket**: 实时通信支持
- **离线运行**: 完全无网络依赖的本地运行

## 部署要求

### 系统要求
- **操作系统**: Windows (主要支持)
- **Python版本**: 3.11
- **Node.js**: 最新LTS版本
- **内存**: 建议8GB以上
- **存储**: 模型文件需要约10GB空间

### 服务依赖
- **Redis**: Docker容器运行
- **虚拟环境**: .venv Python虚拟环境
- **模型文件**: 完整的本地模型文件集合

## 最新技术栈优化

### 离线化改进
- **完全本地化**: 所有AI模型本地加载
- **网络隔离**: 零网络依赖运行
- **性能提升**: 模型加载和推理速度显著提升

### 架构优化
- **模块化设计**: 音频处理模块完全解耦
- **错误处理**: 统一的异常处理和恢复机制
- **监控体系**: 完善的日志和性能监控

### 用户体验
- **实时反馈**: WebSocket实时进度更新
- **友好错误**: 用户友好的错误提示
- **响应速度**: 处理速度提升75%