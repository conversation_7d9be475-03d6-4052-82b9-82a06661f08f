# -----------------------------------------------------------------
#           环境配置文件模板 (Environment Configuration Template)
# -----------------------------------------------------------------
#
# 使用方法:
# 1. 复制此文件并重命名为 `.env`
# 2. 填写您的实际配置值
# 3. 确保 `.env` 文件已被添加到 `.gitignore` 中，不要提交到版本库
#
# How to use:
# 1. Copy this file and rename it to `.env`
# 2. Fill in your actual configuration values
# 3. Make sure the `.env` file is added to `.gitignore` and not committed to your repository

# --- Ollama 配置 ---
# 您本地Ollama服务的地址。
# 在Docker容器中运行时，通常需要设置为宿主机的IP地址，例如 http://***********:11434
# 或者使用Docker的特殊DNS名称，例如 http://host.docker.internal:11434
OLLAMA_BASE_URL=http://***********:11434

# --- 向量数据库配置 (ChromaDB) ---
# ChromaDB持久化存储的路径
CHROMADB_PATH=./chroma_db

# --- OpenAI API 配置 ---
# 如果您使用OpenAI的API，请填写以下信息
OPENAI_API_KEY=your_openai_api_key_here

# --- Azure OpenAI 配置 ---
# 如果您使用Azure的OpenAI服务，请填写以下信息
AZURE_OPENAI_ENDPOINT=your_azure_openai_endpoint_here
AZURE_OPENAI_KEY=your_azure_openai_key_here
AZURE_OPENAI_MODEL=your_azure_deployment_name_here

# --- 自定义 OpenAI 兼容接口配置 ---
# 例如使用 FastChat, vLLM 等自建服务
CUSTOM_OPENAI_BASE_URL=http://localhost:8000
CUSTOM_OPENAI_API_KEY=your_custom_api_key_if_any