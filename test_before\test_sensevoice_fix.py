#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SenseVoice "not registered" 错误修复测试脚本
基于GitHub issues #52 和 #105 的解决方案
"""

import os
import sys
from pathlib import Path

def set_offline_mode():
    """设置离线模式环境变量"""
    os.environ['MODELSCOPE_OFFLINE_MODE'] = '1'
    os.environ['HF_HUB_OFFLINE'] = '1'
    os.environ['HF_DATASETS_OFFLINE'] = '1'
    os.environ['TRANSFORMERS_OFFLINE'] = '1'
    os.environ['DISABLE_MODEL_DOWNLOAD'] = '1'
    os.environ['NO_PROXY'] = '*'
    os.environ['REQUESTS_CA_BUNDLE'] = ''
    print("🔒 离线模式已启用")

def test_sensevoice_model(model_path):
    """测试SenseVoice模型加载"""
    print(f"🔧 测试SenseVoice模型路径: {model_path}")
    
    # 设置离线模式
    set_offline_mode()
    
    # 检查模型路径
    if not os.path.exists(model_path):
        print(f"❌ 模型路径不存在: {model_path}")
        return False
    
    # 检查关键文件
    model_py_path = os.path.join(model_path, "model.py")
    config_path = os.path.join(model_path, "config.yaml")
    
    print(f"📁 模型目录内容:")
    try:
        files = os.listdir(model_path)
        print(f"  - 总文件数: {len(files)}")
        print(f"  - model.py 存在: {os.path.exists(model_py_path)}")
        print(f"  - config.yaml 存在: {os.path.exists(config_path)}")
        
        # 显示前10个文件
        for i, file in enumerate(files[:10]):
            print(f"  - {file}")
        if len(files) > 10:
            print(f"  - ... 还有 {len(files) - 10} 个文件")
            
    except Exception as e:
        print(f"❌ 读取目录失败: {str(e)}")
        return False
    
    # 尝试加载模型
    try:
        from funasr import AutoModel
        print("📦 正在导入FunASR...")
        
        # 方案1：使用trust_remote_code + remote_code
        print("🔧 尝试方案1：trust_remote_code + remote_code")
        try:
            config = {
                'model': model_path,
                'trust_remote_code': True,
                'device': 'cpu',  # 使用CPU避免GPU问题
                'disable_update': True,
                'local_files_only': True,
                'force_download': False,
                'vad_model': None,  # 禁用VAD避免额外下载
            }
            
            # 如果有model.py，使用它
            if os.path.exists(model_py_path):
                config['remote_code'] = model_py_path
                print(f"  - 使用本地model.py: {model_py_path}")
            
            model = AutoModel(**config)
            print("✅ 方案1成功 - SenseVoice模型加载完成")
            
            # 测试模型是否正常工作
            if hasattr(model, 'generate'):
                print("✅ 模型具有generate方法")
                return True
            else:
                print("⚠️ 模型缺少generate方法")
                return False
                
        except Exception as e1:
            print(f"⚠️ 方案1失败: {str(e1)}")
            
            # 方案2：简化配置
            print("🔧 尝试方案2：简化配置")
            try:
                model = AutoModel(
                    model=model_path,
                    trust_remote_code=True,
                    device='cpu',
                    local_files_only=True,
                    disable_update=True
                )
                print("✅ 方案2成功 - SenseVoice模型加载完成")
                return True
                
            except Exception as e2:
                print(f"⚠️ 方案2失败: {str(e2)}")
                
                # 方案3：最小配置
                print("🔧 尝试方案3：最小配置")
                try:
                    model = AutoModel(
                        model=model_path,
                        trust_remote_code=True,
                        device='cpu'
                    )
                    print("✅ 方案3成功 - SenseVoice模型加载完成")
                    return True
                    
                except Exception as e3:
                    print(f"❌ 方案3失败: {str(e3)}")
                    print("❌ 所有加载方案均失败")
                    return False
    
    except ImportError as e:
        print(f"❌ 导入FunASR失败: {str(e)}")
        print("💡 请确保已安装FunASR: pip install funasr")
        return False
    except Exception as e:
        print(f"❌ 模型加载过程失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🎙️ SenseVoice模型修复测试工具")
    print("=" * 50)
    
    # 默认模型路径
    default_path = r"C:\Users\<USER>\Documents\my_project\models\model_dir\SenseVoiceSmall"
    
    # 从命令行参数获取模型路径
    if len(sys.argv) > 1:
        model_path = sys.argv[1]
    else:
        model_path = default_path
    
    print(f"📍 使用模型路径: {model_path}")
    
    # 测试模型
    success = test_sensevoice_model(model_path)
    
    print("=" * 50)
    if success:
        print("🎉 测试成功！SenseVoice模型可以正常加载")
        print("💡 建议在实际应用中使用相同的配置")
    else:
        print("❌ 测试失败！请检查以下几点：")
        print("1. 模型路径是否正确")
        print("2. 模型文件是否完整")
        print("3. 是否已安装FunASR")
        print("4. 网络连接是否已正确禁用")

if __name__ == "__main__":
    main() 