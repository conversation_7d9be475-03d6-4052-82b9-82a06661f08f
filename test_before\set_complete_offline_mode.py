#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
设置完全离线模式，避免任何网络请求
基于互联网搜索结果的最佳实践
"""

import os

def set_complete_offline_mode():
    """设置完全离线模式，禁用所有网络连接"""
    
    # FunASR相关
    os.environ['DISABLE_MODEL_DOWNLOAD'] = '1'
    os.environ['FUNASR_CACHE_OFFLINE'] = '1'
    
    # ModelScope相关
    os.environ['MODELSCOPE_OFFLINE_MODE'] = '1'
    os.environ['MODELSCOPE_CACHE_DIR'] = '/tmp/modelscope_cache'
    
    # HuggingFace相关
    os.environ['HF_HUB_OFFLINE'] = '1'
    os.environ['HF_DATASETS_OFFLINE'] = '1'
    os.environ['TRANSFORMERS_OFFLINE'] = '1'
    os.environ['HF_HUB_DISABLE_TELEMETRY'] = '1'
    
    # 网络代理相关
    os.environ['NO_PROXY'] = '*'
    os.environ['no_proxy'] = '*'
    os.environ['REQUESTS_CA_BUNDLE'] = ''
    os.environ['CURL_CA_BUNDLE'] = ''
    os.environ['SSL_VERIFY'] = '0'
    
    # PyTorch相关
    os.environ['TORCH_HOME'] = '/tmp/torch_cache'
    
    print("🔒 完全离线模式已设置")
    print("✅ 所有网络连接已禁用")
    
    return True

if __name__ == "__main__":
    set_complete_offline_mode() 