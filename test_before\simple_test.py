#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本 - 验证修改后的加载方式
"""

import os
import sys

print("🛠️ 语音处理修复测试")
print("=" * 30)

# 1. 设置离线模式
print("🔒 设置离线模式...")
os.environ['MODELSCOPE_OFFLINE_MODE'] = '1'
os.environ['HF_HUB_OFFLINE'] = '1'
os.environ['TRANSFORMERS_OFFLINE'] = '1'
os.environ['DISABLE_MODEL_DOWNLOAD'] = '1'
print("✅ 离线模式已设置")

# 2. 测试导入
print("\n📦 测试导入...")
try:
    from utils.optimized_speech_processing import OptimizedSpeechProcessor
    print("✅ 优化处理器导入成功")
except Exception as e:
    print(f"❌ 优化处理器导入失败: {e}")

try:
    from funasr import AutoModel
    print("✅ FunASR导入成功")
except Exception as e:
    print(f"❌ FunASR导入失败: {e}")

# 3. 检查语法
print("\n📝 检查Streamlit语法...")
try:
    import ast
    with open('pages/语音识别分析.py', 'r', encoding='utf-8') as f:
        content = f.read()
    ast.parse(content)
    print("✅ Streamlit语法正确")
except Exception as e:
    print(f"❌ Streamlit语法错误: {e}")

print("\n🎯 测试完成！")
print("💡 如果所有项目都显示✅，说明修复成功")
print("💡 现在可以运行: streamlit run Home.py")

def set_offline_mode():
    """设置离线模式环境变量"""
    os.environ['MODELSCOPE_OFFLINE_MODE'] = '1'
    os.environ['HF_HUB_OFFLINE'] = '1'
    os.environ['HF_DATASETS_OFFLINE'] = '1'
    os.environ['TRANSFORMERS_OFFLINE'] = '1'
    os.environ['DISABLE_MODEL_DOWNLOAD'] = '1'
    os.environ['NO_PROXY'] = '*'
    os.environ['REQUESTS_CA_BUNDLE'] = ''
    print("🔒 离线模式已启用")

def test_sensevoice_simple():
    """简单测试SenseVoice加载"""
    print("🎙️ 测试SenseVoice模型加载")
    
    # 设置离线模式
    set_offline_mode()
    
    model_path = r"C:\Users\<USER>\Documents\my_project\models\model_dir\SenseVoiceSmall"
    
    if not os.path.exists(model_path):
        print(f"❌ 模型路径不存在: {model_path}")
        return False
    
    # 检查关键文件
    model_py_path = os.path.join(model_path, "model.py")
    config_path = os.path.join(model_path, "config.yaml")
    model_pt_path = os.path.join(model_path, "model.pt")
    
    print(f"📁 模型文件检查:")
    print(f"  - model.py 存在: {os.path.exists(model_py_path)}")
    print(f"  - config.yaml 存在: {os.path.exists(config_path)}")
    print(f"  - model.pt 存在: {os.path.exists(model_pt_path)}")
    
    try:
        from funasr import AutoModel
        print("✅ 成功导入FunASR")
        
        # 🔧 方案1：使用trust_remote_code + remote_code（测试验证的最佳方案）
        print("🔧 尝试方案1：trust_remote_code + remote_code")
        try:
            config = {
                'model': model_path,
                'trust_remote_code': True,
                'device': 'cpu',
                'disable_update': True,
                'local_files_only': True,
                'force_download': False,
                'vad_model': None,  # 禁用VAD避免额外下载
            }
            
            # 如果有model.py，使用它
            if os.path.exists(model_py_path):
                config['remote_code'] = model_py_path
                print(f"  - 使用本地model.py: {model_py_path}")
            
            model = AutoModel(**config)
            print("✅ 方案1成功 - SenseVoice模型加载完成")
            
            # 测试模型是否正常工作
            if hasattr(model, 'generate'):
                print("✅ 模型具有generate方法")
                return True
            else:
                print("⚠️ 模型缺少generate方法")
                return False
                
        except Exception as e1:
            print(f"⚠️ 方案1失败: {str(e1)}")
            
            # 🔧 方案2：简化配置
            print("🔧 尝试方案2：简化配置")
            try:
                model = AutoModel(
                    model=model_path,
                    trust_remote_code=True,
                    device='cpu',
                    local_files_only=True,
                    disable_update=True
                )
                print("✅ 方案2成功 - SenseVoice模型加载完成")
                return True
                
            except Exception as e2:
                print(f"⚠️ 方案2失败: {str(e2)}")
                
                # 🔧 方案3：最小配置
                print("🔧 尝试方案3：最小配置")
                try:
                    model = AutoModel(
                        model=model_path,
                        trust_remote_code=True,
                        device='cpu'
                    )
                    print("✅ 方案3成功 - SenseVoice模型加载完成")
                    return True
                    
                except Exception as e3:
                    print(f"❌ 方案3失败: {str(e3)}")
                    print("❌ 所有加载方案均失败")
                    return False
    
    except ImportError as e:
        print(f"❌ 导入FunASR失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 模型加载过程失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🧪 简单测试 - 验证修改后的加载方式")
    print("=" * 50)
    
    # 测试SenseVoice
    success = test_sensevoice_simple()
    
    print("=" * 50)
    if success:
        print("🎉 测试成功！修改后的加载方式工作正常")
        print("💡 可以将此配置应用到主程序中")
    else:
        print("❌ 测试失败！需要进一步调试")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 