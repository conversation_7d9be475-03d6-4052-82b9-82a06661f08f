#!/usr/bin/env python3
"""
数据库迁移脚本 - 添加任务管理表
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def migrate_database():
    """执行数据库迁移"""
    print("🔄 开始数据库迁移...")
    
    try:
        # 导入必要的模块
        from backend.core.database import engine, Base, get_db_session
        from backend.models import (
            User, AudioFile, ProcessingResult, SpeakerProfile,
            Conversation, Message, KnowledgeBase, Document,
            SystemConfig, SystemLog, SystemMetrics, TaskQueue,
            ManagedDocument, DocumentSection, DocumentProcessingLog,
            DocumentTag, DocumentTagAssociation,
            TaskRecord, TaskProgressLog, TaskDependency, TaskSchedule
        )
        
        print("✅ 模型导入成功")
        
        # 创建所有表
        print("🔧 创建数据库表...")
        Base.metadata.create_all(bind=engine)
        print("✅ 数据库表创建完成")
        
        # 验证表是否创建成功
        print("🔍 验证表创建...")
        db = get_db_session()
        try:
            # 检查关键表是否存在
            from sqlalchemy import text
            
            # 检查任务相关表
            tables_to_check = [
                'task_records',
                'task_progress_logs', 
                'task_dependencies',
                'task_schedules'
            ]
            
            for table_name in tables_to_check:
                result = db.execute(text(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table_name}'"))
                if result.fetchone():
                    print(f"  ✅ 表 {table_name} 创建成功")
                else:
                    print(f"  ❌ 表 {table_name} 创建失败")
                    
        finally:
            db.close()
            
        print("🎉 数据库迁移完成！")
        return True
        
    except Exception as e:
        print(f"❌ 数据库迁移失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_database_status():
    """检查数据库状态"""
    print("📊 检查数据库状态...")
    
    try:
        from backend.core.database import get_db_session
        from sqlalchemy import text
        
        db = get_db_session()
        try:
            # 获取所有表
            result = db.execute(text("SELECT name FROM sqlite_master WHERE type='table'"))
            tables = [row[0] for row in result.fetchall()]
            
            print(f"📋 数据库中的表 ({len(tables)} 个):")
            for table in sorted(tables):
                print(f"  - {table}")
                
            # 检查任务表
            task_tables = [t for t in tables if 'task' in t.lower()]
            if task_tables:
                print(f"\n✅ 任务相关表 ({len(task_tables)} 个):")
                for table in task_tables:
                    print(f"  - {table}")
            else:
                print("\n❌ 未找到任务相关表")
                
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ 检查数据库状态失败: {e}")

def main():
    """主函数"""
    print("🚀 数据库迁移工具")
    print("=" * 50)
    
    # 检查当前状态
    check_database_status()
    
    print("\n" + "=" * 50)
    
    # 执行迁移
    success = migrate_database()
    
    print("\n" + "=" * 50)
    
    # 再次检查状态
    if success:
        check_database_status()
    
    print("\n" + "=" * 50)
    if success:
        print("✅ 迁移完成！现在可以正常使用文档上传功能了。")
    else:
        print("❌ 迁移失败！请检查错误信息并重试。")

if __name__ == "__main__":
    main()
