"""
创建任务相关数据表的迁移脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from sqlalchemy import create_engine, text
from backend.core.config_simple import settings
from backend.models.task_models import Base
from loguru import logger


def create_task_tables():
    """创建任务相关的数据表"""
    try:
        # 创建数据库引擎
        engine = create_engine(settings.DATABASE_URL)
        
        # 创建所有表
        Base.metadata.create_all(bind=engine)
        
        logger.info("✅ 任务相关数据表创建成功")
        
        # 验证表是否创建成功
        with engine.connect() as conn:
            # 检查表是否存在
            tables_to_check = [
                'task_records',
                'task_progress_logs', 
                'task_dependencies',
                'task_schedules'
            ]
            
            for table_name in tables_to_check:
                result = conn.execute(text(f"""
                    SELECT name FROM sqlite_master 
                    WHERE type='table' AND name='{table_name}'
                """))
                
                if result.fetchone():
                    logger.info(f"✅ 表 {table_name} 创建成功")
                else:
                    logger.error(f"❌ 表 {table_name} 创建失败")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建任务数据表失败: {e}")
        return False


def create_indexes():
    """创建索引以提高查询性能"""
    try:
        engine = create_engine(settings.DATABASE_URL)
        
        with engine.connect() as conn:
            # 为task_records表创建索引
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_task_records_user_id ON task_records(user_id)",
                "CREATE INDEX IF NOT EXISTS idx_task_records_status ON task_records(status)",
                "CREATE INDEX IF NOT EXISTS idx_task_records_task_type ON task_records(task_type)",
                "CREATE INDEX IF NOT EXISTS idx_task_records_created_at ON task_records(created_at)",
                "CREATE INDEX IF NOT EXISTS idx_task_records_user_status ON task_records(user_id, status)",
                
                # 为task_progress_logs表创建索引
                "CREATE INDEX IF NOT EXISTS idx_task_progress_logs_task_id ON task_progress_logs(task_id)",
                "CREATE INDEX IF NOT EXISTS idx_task_progress_logs_timestamp ON task_progress_logs(timestamp)",
                
                # 为task_dependencies表创建索引
                "CREATE INDEX IF NOT EXISTS idx_task_dependencies_task_id ON task_dependencies(task_id)",
                "CREATE INDEX IF NOT EXISTS idx_task_dependencies_depends_on ON task_dependencies(depends_on_task_id)",
                
                # 为task_schedules表创建索引
                "CREATE INDEX IF NOT EXISTS idx_task_schedules_active ON task_schedules(is_active)",
                "CREATE INDEX IF NOT EXISTS idx_task_schedules_next_run ON task_schedules(next_run_at)"
            ]
            
            for index_sql in indexes:
                try:
                    conn.execute(text(index_sql))
                    logger.info(f"✅ 索引创建成功: {index_sql.split('ON')[1].strip()}")
                except Exception as e:
                    logger.warning(f"⚠️ 索引创建失败: {e}")
            
            conn.commit()
        
        logger.info("✅ 所有索引创建完成")
        return True
        
    except Exception as e:
        logger.error(f"❌ 创建索引失败: {e}")
        return False


def insert_sample_data():
    """插入示例数据（可选）"""
    try:
        engine = create_engine(settings.DATABASE_URL)
        
        with engine.connect() as conn:
            # 插入示例任务调度
            sample_schedules = [
                {
                    "schedule_name": "cleanup_old_tasks",
                    "task_type": "maintenance",
                    "cron_expression": "0 2 * * *",  # 每天凌晨2点
                    "interval_seconds": None,
                    "task_args": "[]",
                    "task_kwargs": '{"days": 30, "keep_failed": true}',
                    "is_active": True,
                    "schedule_metadata": '{"description": "清理30天前的旧任务记录"}'
                },
                {
                    "schedule_name": "recover_orphaned_tasks",
                    "task_type": "maintenance",
                    "cron_expression": None,
                    "interval_seconds": 3600,  # 每小时
                    "task_args": "[]",
                    "task_kwargs": "{}",
                    "is_active": True,
                    "schedule_metadata": '{"description": "恢复孤儿任务"}'
                }
            ]
            
            for schedule in sample_schedules:
                insert_sql = text("""
                    INSERT OR IGNORE INTO task_schedules 
                    (schedule_name, task_type, cron_expression, interval_seconds, 
                     task_args, task_kwargs, is_active, schedule_metadata, created_at)
                    VALUES 
                    (:schedule_name, :task_type, :cron_expression, :interval_seconds,
                     :task_args, :task_kwargs, :is_active, :schedule_metadata, datetime('now'))
                """)
                
                conn.execute(insert_sql, schedule)
            
            conn.commit()
            logger.info("✅ 示例数据插入成功")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 插入示例数据失败: {e}")
        return False


def main():
    """主函数"""
    logger.info("🚀 开始创建任务相关数据表...")
    
    # 1. 创建表
    if not create_task_tables():
        logger.error("❌ 数据表创建失败，退出")
        return False
    
    # 2. 创建索引
    if not create_indexes():
        logger.error("❌ 索引创建失败，但表已创建成功")
    
    # 3. 插入示例数据
    if not insert_sample_data():
        logger.error("❌ 示例数据插入失败，但表已创建成功")
    
    logger.info("🎉 任务数据表初始化完成！")
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
