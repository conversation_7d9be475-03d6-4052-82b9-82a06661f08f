import{_ as Bs,h as S,i as ve,m as je,o as Ps,J as _t,n as js,r as w,M as Ns,a as k,c as A,b as s,z as Q,w as a,x as M,d as o,D as se,G as he,A as T,t as f,s as ye,F as ft,p as vt,e as Ws,E as c,q as re,f as m,Q as Ne,R as ht,a8 as yt,U as bt,a9 as qs,Y as Is,Z as Js,B as Gs,aa as We,H as wt,a0 as kt,j as Te,ab as Ve,L as Hs}from"./index-2c134546.js";import{a as K,b as qe}from"./audioProcessing-868a9059.js";import{a as Zs}from"./useAudioProcessing-53cfd45c.js";import"./auth-e6295339.js";const Ks={class:"audio-center"},Xs={class:"audio-center-header"},Qs={class:"header-left"},Ys={class:"workspace-info"},eo={class:"header-actions"},to={class:"sidebar-left",ref:"leftPanel"},so={class:"panel-header"},oo={class:"panel-content"},lo={class:"config-section"},no={key:0,class:"mode-description"},ao={key:0,class:"config-section"},io={class:"config-subgroup"},ro={class:"config-item"},uo={key:0,class:"config-item"},co={key:1,class:"config-item"},mo={key:2,class:"config-item"},po={class:"config-item"},go={key:0,class:"config-subgroup"},_o={class:"config-item"},fo={class:"config-item"},vo={class:"config-item"},ho={class:"config-hint"},yo={class:"config-item"},bo={class:"config-item"},wo={class:"config-section"},ko={class:"config-section"},xo={class:"config-item"},$o={class:"config-item"},So={class:"config-item"},Co={class:"config-item"},To={class:"config-section"},Vo={class:"config-item"},zo={class:"config-item"},Ao={class:"config-item"},Mo={class:"config-item"},Ro={class:"config-hint"},Do={class:"config-section recording-section"},Uo={class:"recording-status-compact"},Eo={class:"status-text"},Lo={class:"recording-time"},Oo={class:"recording-config"},Fo={class:"config-item"},Bo={class:"config-item"},Po={class:"control-buttons-compact"},jo={class:"button-row"},No={key:0,class:"recording-preview-compact"},Wo={class:"preview-header"},qo={class:"duration-text"},Io=["src"],Jo={class:"preview-actions-compact"},Go={class:"recording-tips"},Ho={class:"main-content"},Zo={class:"main-tabs-header"},Ko={class:"tabs-navigation"},Xo={class:"main-tabs-content"},Qo={class:"tab-content-main"},Yo={class:"upload-section"},el={class:"file-list-section"},tl={class:"section-header"},sl={class:"header-actions"},ol={class:"file-name"},ll={class:"file-actions"},nl={class:"tab-content-main"},al={class:"results-header"},il={class:"results-filters"},rl={class:"results-list"},dl={class:"file-info"},ul={class:"progress-text"},cl={class:"result-actions"},ml={key:0,class:"result-detail"},pl={class:"detail-section"},gl={key:0,class:"detail-section"},_l={class:"result-content"},fl={class:"meeting-result"},vl={class:"formatted-text-container"},hl={class:"formatted-text"},yl={class:"text-result"},bl={class:"json-result"},wl={key:1,class:"detail-section"},kl={class:"sidebar-right",ref:"rightPanel"},xl={class:"panel-header"},$l={class:"panel-content"},Sl={class:"monitor-section"},Cl={class:"status-cards"},Tl={class:"status-card"},Vl={class:"status-value"},zl={class:"status-card"},Al={class:"status-value"},Ml={class:"status-card"},Rl={class:"status-value"},Dl={class:"monitor-section"},Ul={class:"active-tasks"},El={key:0,class:"no-tasks"},Ll={key:1},Ol={class:"task-header"},Fl={class:"task-name"},Bl={class:"task-progress"},Pl={class:"task-info"},jl={class:"task-mode"},Nl={class:"task-time"},Wl={class:"monitor-section"},ql={class:"queue-stats"},Il={class:"queue-item"},Jl={class:"queue-count"},Gl={class:"queue-item"},Hl={class:"queue-count"},Zl={class:"queue-item"},Kl={class:"queue-count"},Xl={class:"queue-item"},Ql={class:"queue-count error"},Yl={class:"monitor-section"},en={class:"log-container"},tn={class:"log-time"},sn={class:"log-message"},on={key:0,class:"no-logs"},ln={class:"monitor-section"},nn={class:"quick-actions"},xt=5,an=".mp3,.wav,.m4a,.aac,.flac,.ogg",rn={__name:"AudioCenter",setup(dn){const oe=S("file-management"),Y=S(!0),ee=S(!0),I=S("speech_recognition"),d=ve({language:"auto",use_itn:!0,ban_emo_unk:!1,merge_vad:!0,merge_length_s:5,min_speech_duration:.5,max_speech_duration:60,threshold:.5,output_format:"timeline",include_timestamps:!0,include_confidence:!1,speaker_labeling:!0,segmentation_strategy:"hybrid",time_window_size:4,min_segments_required:3,overlap_ratio:.1,force_split_threshold:8,expected_speakers:2,similarity_threshold:.15,clustering_method:"auto",min_segment_duration:.5}),R=S("idle"),de=S(0),G=S(""),H=ve({quality:"medium",format:"wav"});let B=null,ue=[],be=null,te=null,L=null;const Ie=S(!1),ce=S(0),y=S([]),J=S([]),$=S([]),$t=S([]),ze=S(!1),Je=S("/api/v1/audio/upload"),me=S({}),F=S([]),Ae=S(!1),P=ve({status:"",mode:"",search:""}),we=S(!1),C=S(null),Me=S("text"),V=ve({cpu:0,memory:0,gpu:0}),z=ve({pending:0,processing:0,completed:0,failed:0}),j=S([]),St=je(()=>({"left-panel-hidden":!Y.value,"right-panel-hidden":!ee.value})),pe=je(()=>({speech_recognition:{title:"基础语音识别",desc:"将音频文件转换为文本，支持多种语言，适用于单人录音、采访、讲座等场景。"},speaker_recognition:{title:"说话人识别",desc:"在语音识别的基础上，区分不同的说话人，为每个说话人的语音内容添加标记。"},meeting_transcription:{title:"会议转录模式",desc:"专为多人会议场景优化，自动识别说话人并生成会议纪要，支持说话人统计。"},vad_detection:{title:"VAD语音活动检测",desc:"检测音频中的语音活动段和静音段，生成时间轴分析，用于音频质量评估。"},audio_enhancement:{title:"音频增强",desc:"对音质较差的音频进行预处理，包括降噪、音量均衡等，然后进行语音识别。"}})[I.value]||null),Ct=()=>{c.info("导出功能开发中...")},Tt=()=>{c.info("清空功能开发中...")},Vt=t=>{switch(t){case"toggle-left":Y.value=!Y.value;break;case"toggle-right":ee.value=!ee.value;break;case"reset-layout":Y.value=!0,ee.value=!0;break}},zt=()=>{switch(R.value){case"idle":return"待机";case"recording":return"录音中";case"paused":return"已暂停";default:return"未知"}},ke=t=>{const e=Math.floor(t/60),l=t%60;return`${e.toString().padStart(2,"0")}:${l.toString().padStart(2,"0")}`},At=async()=>{try{te=await navigator.mediaDevices.getUserMedia({audio:{sampleRate:Et(),channelCount:1,echoCancellation:!0,noiseSuppression:!0}}),ue=[],de.value=0,G.value="";let t=H.format==="wav"?"audio/wav":"audio/webm";MediaRecorder.isTypeSupported(t)||(console.warn(`MIME类型 ${t} 不支持，使用默认类型`),t="audio/webm",MediaRecorder.isTypeSupported(t)||(t="")),B=new MediaRecorder(te,t?{mimeType:t}:{}),B.ondataavailable=e=>{e.data.size>0&&ue.push(e.data)},B.onstop=()=>{Lt()},B.start(),R.value="recording",He(),c.success("录音已开始")}catch(t){console.error("录音启动失败:",t);let e="录音启动失败";t.name==="NotAllowedError"?e="麦克风权限被拒绝，请在浏览器设置中允许麦克风访问":t.name==="NotFoundError"?e="未找到麦克风设备，请检查设备连接":t.name==="NotSupportedError"?e="浏览器不支持录音功能":t.name==="NotReadableError"?e="麦克风设备被其他应用占用":e=`录音启动失败: ${t.message}`,c.error(e)}},Mt=()=>{B&&R.value==="recording"&&(B.pause(),R.value="paused",Re(),c.info("录音已暂停"))},Rt=()=>{B&&R.value==="paused"&&(B.resume(),R.value="recording",He(),c.info("录音已继续"))},Dt=()=>{B&&R.value!=="idle"&&(B.stop(),R.value="idle",Re(),te&&(te.getTracks().forEach(t=>t.stop()),te=null),c.success("录音已停止"))},Ut=async()=>{if(!G.value){c.warning("没有可保存的录音");return}try{const e=`录音_${new Date().toISOString().replace(/[:.]/g,"-")}.${H.format}`,n=await(await fetch(G.value)).blob(),i=new FormData;if(i.append("file",n,e),(await fetch(Je.value,{method:"POST",headers:me.value,body:i})).ok)c.success(`录音已保存: ${e}`),Ge(),N();else throw new Error("上传失败")}catch(t){console.error("保存录音失败:",t),c.error("保存录音失败")}},Ge=()=>{G.value="",de.value=0,ue=[],c.info("录音已丢弃")},Et=()=>{switch(H.quality){case"high":return 48e3;case"medium":return 44100;case"low":return 16e3;default:return 44100}},He=()=>{be=setInterval(()=>{de.value+=1},1e3)},Re=()=>{be&&(clearInterval(be),be=null)},Lt=()=>{if(ue.length===0)return;const t=H.format==="wav"?"audio/wav":"audio/webm",e=new Blob(ue,{type:t});G.value=URL.createObjectURL(e)},Ot=t=>{var e;switch(console.log("处理模式已切换到:",t),t){case"vad_detection":d.merge_vad=!1;break;case"speaker_recognition":d.merge_vad=!0;break;case"meeting_transcription":d.merge_vad=!0,d.use_itn=!0,d.speaker_labeling=!0,d.output_format="timeline",d.include_timestamps=!0;break;case"audio_enhancement":d.merge_vad=!0;break;default:d.merge_vad=!0,d.use_itn=!0}c.success(`已切换到${(e=pe.value)==null?void 0:e.title}模式`)},Ft=t=>{const e=["audio/mpeg","audio/wav","audio/mp4","audio/aac","audio/flac","audio/ogg"],l=t.type,n=t.name.toLowerCase();if(!(e.includes(l)||n.endsWith(".mp3")||n.endsWith(".wav")||n.endsWith(".m4a")||n.endsWith(".aac")||n.endsWith(".flac")||n.endsWith(".ogg")))return c.error("只支持 MP3、WAV、M4A、AAC、FLAC、OGG 格式的音频文件"),!1;const u=200*1024*1024;return t.size>u?(c.error("文件大小不能超过 200MB"),!1):!0},Bt=(t,e)=>{console.log("上传进度:",Math.round(t.percent),"%")},Pt=(t,e)=>{console.log("上传成功:",t),t.success?(c.success(`文件 ${e.name} 上传成功`),N()):c.error(t.message||"上传失败")},jt=(t,e)=>{console.error("上传失败:",t),c.error(`文件 ${e.name} 上传失败`)},Nt=()=>{c.warning("最多只能同时上传 10 个文件")},N=async()=>{ze.value=!0;try{const t=await K.getUserFiles();console.log("获取文件列表响应:",t),t.data&&Array.isArray(t.data)?(J.value=t.data,console.log("成功获取文件列表:",t.data.length,"个文件")):t.success&&t.files?J.value=t.files:(console.warn("API响应格式不符合预期，使用模拟数据"),J.value=[{id:"1",name:"会议录音_20241226.mp3",size:15728640,duration:1800,upload_time:"2024-12-26 14:30:00",status:"uploaded"},{id:"2",name:"采访录音_张三.wav",size:52428800,duration:3600,upload_time:"2024-12-26 10:15:00",status:"processing"}])}catch(t){console.error("获取文件列表失败:",t),J.value=[{id:"1",name:"会议录音_20241226.mp3",size:15728640,duration:1800,upload_time:"2024-12-26 14:30:00",status:"uploaded"},{id:"2",name:"采访录音_张三.wav",size:52428800,duration:3600,upload_time:"2024-12-26 10:15:00",status:"processing"}],c.warning("获取文件列表失败，显示模拟数据")}finally{ze.value=!1}},Wt=t=>{$.value=t},Ze=t=>{if(t===0)return"0 B";const e=1024,l=["B","KB","MB","GB"],n=Math.floor(Math.log(t)/Math.log(e));return parseFloat((t/Math.pow(e,n)).toFixed(2))+" "+l[n]},ge=t=>t?new Date(t).toLocaleString("zh-CN"):"-",qt=t=>({.1:"非常宽松",.2:"宽松",.3:"较宽松",.4:"适中",.5:"标准",.6:"较严格",.7:"严格",.8:"很严格",.9:"极严格"})[t]||`${t}`,xe=t=>{if(t&&typeof t=="object"&&t.key&&t.value!==void 0){if(t.key.startsWith("recording_")){const e=t.key.replace("recording_","");H[e]=t.value}else d[t.key]=t.value;console.log("配置已更改:",t.key,"=",t.value)}else console.log("配置已更改:",d)},It=t=>({.1:"极宽松 - 容易合并不同说话人",.15:"很宽松 - 适合两人对话",.2:"宽松 - 适合小组讨论",.25:"适中 - 平衡准确性",.3:"较严格 - 适合多人会议",.4:"严格 - 减少误合并",.5:"很严格 - 保守聚类",.6:"极严格 - 最小化合并"})[t]||`${t.toFixed(2)}`,Jt=()=>{const t=d.expected_speakers;t<=2?(d.similarity_threshold=.15,d.clustering_method="agglomerative",c.success("已应用两人对话优化配置")):t<=4?(d.similarity_threshold=.2,d.clustering_method="agglomerative",c.success("已应用小组讨论优化配置")):(d.similarity_threshold=.25,d.clustering_method="auto",c.success("已应用大型会议优化配置"))},De=t=>{const e={two_person:{expected_speakers:2,similarity_threshold:.15,clustering_method:"agglomerative"},small_group:{expected_speakers:4,similarity_threshold:.2,clustering_method:"agglomerative"},large_meeting:{expected_speakers:8,similarity_threshold:.25,clustering_method:"auto"}};if(e[t]){Object.assign(d,e[t]);const l={two_person:"两人对话",small_group:"小组讨论",large_meeting:"大型会议"};c.success(`已应用${l[t]}场景配置`)}},Ue=je(()=>{let t=F.value;if(P.status&&(t=t.filter(e=>e.status===P.status)),P.mode&&(t=t.filter(e=>e.mode===P.mode)),P.search){const e=P.search.toLowerCase();t=t.filter(l=>l.file_name.toLowerCase().includes(e)||l.task_id.toLowerCase().includes(e))}return t}),le=async()=>{Ae.value=!0;try{console.log("🔄 刷新处理结果...");const t=await K.getProcessingResults();let e=[];t.data&&t.data.results?e=t.data.results:t.results?e=t.results:Array.isArray(t.data)?e=t.data:Array.isArray(t)&&(e=t),F.value=e,console.log("✅ 处理结果已刷新:",F.value.length,"个任务"),console.log("📊 响应数据结构:",t)}catch(t){console.error("❌ 获取处理结果失败:",t),console.log("🔄 使用模拟数据作为备用"),F.value=[{task_id:"5a38c0e0-f32d-49d7-a2ba-280a9e64d186",file_name:"对话.mp3",mode:"speech-recognition",status:"completed",progress:100,created_time:"2024-12-26 16:30:00",result:{text:"这是一段测试音频的识别结果。包含了完整的语音转文字内容。",segments:[{start:0,end:2.5,text:"这是一段测试音频"},{start:2.5,end:5,text:"的识别结果"}]}},{task_id:"abc123-def456-ghi789",file_name:"test_audio.wav",mode:"vad-detection",status:"processing",progress:65,created_time:"2024-12-26 16:25:00"},{task_id:"xyz789-uvw456-rst123",file_name:"会议录音.mp3",mode:"speaker-recognition",status:"failed",progress:0,created_time:"2024-12-26 16:20:00",error:"音频格式不支持，请使用WAV或MP3格式"}],c.warning("获取处理结果失败，显示模拟数据")}finally{Ae.value=!1}},Ee=t=>it(t),Ke=t=>({completed:"success",processing:"warning",failed:"danger",pending:"info"})[t]||"info",Xe=t=>({completed:"已完成",processing:"处理中",failed:"失败",pending:"等待中"})[t]||t,Qe=t=>{C.value=t,we.value=!0,Me.value="text"},Ye=()=>{we.value=!1,C.value=null},et=async(t,e="json")=>{try{c.info(`正在下载结果: ${t.file_name}`);const l=Ve.service({lock:!0,text:"正在准备下载文件...",background:"rgba(0, 0, 0, 0.7)"});try{const n=await qe.downloadResult(t.task_id,e),i=n.headers["content-disposition"];let u=`audio_result_${t.task_id.substring(0,8)}.${e}`;if(i){const b=i.match(/filename=(.+)/);b&&(u=b[1].replace(/"/g,""))}const p=new Blob([n.data]),_=URL.createObjectURL(p),g=document.createElement("a");g.href=_,g.download=u,document.body.appendChild(g),g.click(),document.body.removeChild(g),URL.revokeObjectURL(_),c.success(`文件 ${u} 下载成功`)}finally{l.close()}}catch(l){console.error("下载结果失败:",l),c.error(`下载结果失败: ${l.message}`)}},Gt=async t=>{try{await re.confirm(`确定要删除任务 "${t.task_id}" 的处理结果吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),console.log(`🗑️ 删除处理结果: ${t.task_id}`);const e=await K.deleteProcessingResult(t.task_id);console.log("✅ 删除API响应:",e),c.success("删除成功");const l=F.value.findIndex(n=>n.task_id===t.task_id);l!==-1&&F.value.splice(l,1),le()}catch(e){e!=="cancel"&&(console.error("删除结果失败:",e),c.error("删除结果失败"))}},Ht=async(t,e="txt")=>{const l=Ve.service({lock:!0,text:"正在准备导出文件...",background:"rgba(0, 0, 0, 0.7)"});try{if(!t.result){c.warning("该结果暂无可导出的内容");return}l.setText("正在生成文件内容...");let n="",i=`${t.file_name}_${t.mode}_${new Date().toISOString().slice(0,10)}`;const u=t.result&&typeof t.result=="object",p=u&&(t.result.error_count&&t.result.error_count>0||t.result.results&&t.result.results.some(v=>v.status==="error")||t.result.status==="error");switch(e){case"txt":u?(n=$e(t.result),(!n||n==="暂无文本结果")&&(n=p?`处理失败: ${lt(t.result)}`:"暂无文本结果")):n="暂无文本结果",i+=".txt";break;case"json":n=JSON.stringify(t.result,null,2),i+=".json";break;case"srt":u&&!p?n=tt(t.result):n=p?`# 处理失败
# 错误信息: ${lt(t.result)}`:"# 暂无字幕数据",i+=".srt";break;case"word":try{const v=await at(t.result);if(v instanceof Blob){const x=v.type.includes("application/vnd.openxmlformats-officedocument.wordprocessingml.document"),D=i+(x?".docx":".txt"),O=URL.createObjectURL(v),W=document.createElement("a");W.href=O,W.download=D,document.body.appendChild(W),W.click(),document.body.removeChild(W),URL.revokeObjectURL(O),x?c.success(`文件 ${D} 导出成功`):c.warning(`Word导出失败，已降级为文本格式: ${D}`);return}else throw new Error("generateWordContent返回了非Blob对象")}catch(v){console.error("Word导出失败:",v),c.error(`Word导出失败: ${v.message}`),n=$e(t.result)||"暂无识别结果",i+=".txt"}break;default:throw new Error(`不支持的导出格式: ${e}`)}l.setText("正在下载文件...");const _=new Blob([n],{type:"text/plain;charset=utf-8"}),g=URL.createObjectURL(_),b=document.createElement("a");b.href=g,b.download=i,document.body.appendChild(b),b.click(),document.body.removeChild(b),URL.revokeObjectURL(g),c.success(`文件 ${i} 导出成功`)}catch(n){console.error("导出结果失败:",n),c.error(`导出失败: ${n.message}`)}finally{l.close()}},tt=t=>!t.segments||!Array.isArray(t.segments)?t.text||"":t.segments.map((e,l)=>{const n=st(e.start),i=st(e.end);return`${l+1}
${n} --> ${i}
${e.text}
`}).join(`
`),st=t=>{const e=Math.floor(t/3600),l=Math.floor(t%3600/60),n=Math.floor(t%60),i=Math.floor(t%1*1e3);return`${e.toString().padStart(2,"0")}:${l.toString().padStart(2,"0")}:${n.toString().padStart(2,"0")},${i.toString().padStart(3,"0")}`},Zt=t=>{if(!t||!t.results)return"";const e=[];return t.results.forEach(l=>{if(l.status!=="error"){if(l.result&&l.result.text){e.push(l.result.text);return}if(l.text){e.push(l.text);return}if(l.result&&l.result.result&&Array.isArray(l.result.result)){l.result.result.forEach(n=>{n.processed_text?e.push(n.processed_text):n.text&&e.push(n.text)});return}l.result&&l.result.segments&&Array.isArray(l.result.segments)&&l.result.segments.forEach(n=>{n.text&&e.push(n.text)})}}),e.join(`
`)},ot=t=>!t||!Array.isArray(t)?"":t.filter(e=>e.text&&e.text!=="[识别失败]").map(e=>e.speaker_name?`[${e.speaker_name}] ${e.text}`:e.text).join(`
`),lt=t=>{if(!t)return"未知错误";if(t.error)return t.error;if(t.error_message)return t.error_message;if(t.results&&t.results.length>0){const e=t.results.find(l=>l.status==="error");if(e&&e.error)return e.error}return"处理失败，未知错误"},nt=t=>{if(!t.results||t.results.length===0)return"暂无会议转录结果";let e="";return t.results.forEach((l,n)=>{if(l.status!=="success"||!l.result)return;const i=l.result;if(e+=`时间：${new Date().toLocaleString()}
`,e+=`标题：会议转录结果 ${n+1}
`,e+=`内容：

`,i.speech_segments&&i.speech_segments.length>0){const u=i.speech_segments.filter(p=>p.text&&p.text.trim()&&p.text!=="[识别失败]");if(u.length>0){u.forEach(_=>{Kt(_.start_time||0);const g=_.speaker_name||_.speaker_label||`说话人${(_.speaker_id||0)+1}`,b=_.text.trim();e+=`${g}：${b}
`}),e+=`
`,e+=`聚合内容：
`;const p={};u.forEach(_=>{const g=_.speaker_name||_.speaker_label||`说话人${(_.speaker_id||0)+1}`;p[g]||(p[g]=[]),p[g].push(_.text.trim())}),Object.entries(p).forEach(([_,g])=>{g.length>0&&(e+=`${_}：${g.join("，")}
`)})}else e+=`⚠️ 数据处理异常：检测到 ${i.speech_segments.length} 个语音片段，但文本内容为空

`,i.text&&i.text.trim()&&(e+=`检测到完整文本内容：
${i.text}

`),i.speaker_segments&&i.speaker_segments.length>0&&(e+=`说话人统计信息：
`,i.speaker_segments.forEach(p=>{const _=p.speaker||p.name||`说话人${p.speaker_id+1}`,g=p.total_duration||p.total_time||0,b=p.segment_count||0,v=p.percentage||0;e+=`- ${_}：${b}个片段，总时长${g.toFixed(1)}秒 (${v.toFixed(1)}%)
`})),e+=`
💡 建议：请检查后端文本分配逻辑，确保语音识别结果正确分配到各个时间片段
`}else i.text&&i.text.trim()?e+=`完整内容：
${i.text}

`:e+=`⚠️ 未检测到有效的转录内容

`,i.speaker_segments&&i.speaker_segments.length>0&&(e+=`说话人统计：
`,i.speaker_segments.forEach(u=>{const p=u.speaker||u.name||`说话人${u.speaker_id+1}`,_=u.total_duration||u.total_time||0,g=u.segment_count||0;e+=`- ${p}：${g}个片段，总时长${_.toFixed(1)}秒
`}));e+=`
`}),e||"暂无文本结果"},Kt=t=>{const e=Math.floor(t/60),l=Math.floor(t%60);return`${e.toString().padStart(2,"0")}:${l.toString().padStart(2,"0")}`},$e=t=>{if(!t)return"暂无文本结果";let e="";return t.task_type==="meeting_transcription"?e=nt(t):t.task_type==="speaker_recognition"&&t.segments?e=ot(t.segments):t.text?e=t.text:t.results?e=Zt(t):t.segments&&(e=ot(t.segments)),e||"暂无文本结果"},Xt=t=>{if(!t||typeof t!="object")return{text:"",segments:[]};if(t.text!==void 0||t.segments!==void 0)return{text:t.text||"",segments:t.segments||[]};const e={text:"",segments:[]};if(t.results&&Array.isArray(t.results)){const l=t.results.filter(n=>n.status!=="error");if(l.length>0){const n=l.filter(u=>u.text).map(u=>u.text);e.text=n.join(`
`);const i=[];l.forEach(u=>{u.segments&&Array.isArray(u.segments)&&i.push(...u.segments)}),e.segments=i}}return!e.text&&t.text&&(e.text=t.text),e.segments.length===0&&t.segments&&(e.segments=t.segments),e},at=async t=>{try{const e=Xt(t),l=await fetch("/api/v1/audio/export/docx",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("token")}`},body:JSON.stringify({result:e,title:"音频处理结果",include_metadata:!0,include_timeline:!0})});if(!l.ok){const n=await l.text();throw new Error(`生成Word文档失败: ${l.status} ${n}`)}return await l.blob()}catch(e){console.error("生成Word文档失败:",e);let l=`音频处理结果

`;l+=`处理时间: ${new Date().toLocaleString()}

`,l+=`识别结果:
`;const n=$e(t)||"暂无识别结果";return l+=n,t.segments&&Array.isArray(t.segments)&&(l+=`

详细时间轴:
`,t.segments.forEach((i,u)=>{i.text&&i.start!==void 0&&i.end!==void 0&&(l+=`${u+1}. [${i.start.toFixed(2)}s - ${i.end.toFixed(2)}s] ${i.text}
`)})),new Blob([l],{type:"text/plain;charset=utf-8"})}},Qt=async(t="txt")=>{try{const e=Ue.value.filter(n=>n.selected);if(e.length===0){c.warning("请先选择要导出的结果");return}const l=Ve.service({lock:!0,text:`正在批量导出 ${e.length} 个结果...`,background:"rgba(0, 0, 0, 0.7)"});try{const n=(await Hs(()=>import("./jszip.min-c51ca1b8.js").then(v=>v.j),["static/js/jszip.min-c51ca1b8.js","static/js/index-2c134546.js","static/css/index-5cad6ac7.css"])).default,i=new n;c.info(`开始批量导出 ${e.length} 个结果`);for(let v=0;v<e.length;v++){const x=e[v];if(l.setText(`正在处理第 ${v+1}/${e.length} 个文件...`),!x.result){console.warn(`跳过无结果的任务: ${x.task_id}`);continue}let U="",D="";const O=`${x.file_name}_${x.mode}_${x.task_id.substring(0,8)}`;switch(t){case"txt":U=x.result.text||"",D=".txt";break;case"json":U=JSON.stringify(x.result,null,2),D=".json";break;case"srt":U=tt(x.result),D=".srt";break;case"word":try{const Z=await at(x.result);if(Z instanceof Blob){i.file(`${O}.docx`,Z);continue}else U=Z,D=".txt"}catch(Z){console.error("Word导出失败，降级到文本:",Z),U=x.result.text||"",D=".txt"}break;default:U=x.result.text||"",D=".txt"}const W=`${O}${D}`;i.file(W,U)}const u={export_time:new Date().toISOString(),format:t,total_files:e.length,files:e.map(v=>({task_id:v.task_id,file_name:v.file_name,mode:v.mode,status:v.status}))};i.file("export_manifest.json",JSON.stringify(u,null,2)),l.setText("正在生成ZIP文件...");const p=await i.generateAsync({type:"blob"}),_=URL.createObjectURL(p),g=document.createElement("a");g.href=_;const b=new Date().toISOString().slice(0,10);g.download=`audio_results_batch_${t}_${b}.zip`,document.body.appendChild(g),g.click(),document.body.removeChild(g),URL.revokeObjectURL(_),c.success(`批量导出完成！已导出 ${e.length} 个文件`)}finally{l.close()}}catch(e){console.error("批量导出失败:",e),c.error(`批量导出失败: ${e.message}`)}},Yt=(t,e)=>{Ht(e,t)},X=S([]),es=t=>{X.value=t,console.log("选中的结果:",X.value.length,"个")},ts=t=>{if(X.value.length===0){c.warning("请先选择要导出的结果");return}X.value.map(e=>({...e,selected:!0})),Ue.value.forEach(e=>{e.selected=X.value.some(l=>l.task_id===e.task_id)}),Qt(t)},ss=t=>({pending:"info",processing:"warning",completed:"success",failed:"danger"})[t]||"info",os=t=>({pending:"等待中",processing:"处理中",completed:"已完成",failed:"失败"})[t]||t,it=t=>({audio_enhancement:"音频增强","speech-recognition":"语音识别","speaker-recognition":"说话人识别","meeting-transcription":"会议转录","vad-detection":"VAD语音活动检测","audio-preprocessing":"音频预处理",audio_processing:"语音识别",speech_recognition:"语音识别",speaker_recognition:"说话人识别",meeting_transcription:"会议转录",vad_detection:"VAD语音活动检测",audio_preprocessing:"音频预处理"})[t]||t||"语音识别",ls=t=>new Date(t).toLocaleTimeString("zh-CN"),Le=async()=>{try{console.log("🔄 刷新监控数据...");try{const p=await K.getSystemStatus();console.log("✅ 系统状态响应:",p)}catch(p){console.warn("⚠️ 获取系统状态失败:",p)}try{const p=await K.getActiveTasks();console.log("🔍 活动任务API响应:",p),p.data&&Array.isArray(p.data)?(y.value=p.data.map(_=>{console.log("🔍 处理任务数据:",_);const g=_.progress||0,b=_.id||_.task_id,v=_.file_name||_.name||"未知文件",x=_.task_type||_.mode||"audio_processing",U=it(x),D=_.status||"running",O=_.startTime||_.start_time||_.created_at||new Date().toISOString(),W={id:b,file_name:v,mode:U,status:D,progress:g,start_time:O,updateTime:new Date().toLocaleTimeString()};return console.log("✅ 映射后的任务数据:",W),W}),console.log("✅ 活动任务已更新:",y.value.length,"个任务")):(console.log("📝 无活动任务数据或数据格式不正确"),y.value=[])}catch(p){console.warn("⚠️ 获取活动任务失败:",p),y.value=[]}const t=Date.now();V.cpu=Math.floor(20+Math.sin(t/1e4)*30+Math.random()*20),V.memory=Math.floor(40+Math.cos(t/15e3)*25+Math.random()*15),V.gpu=Math.floor(30+Math.sin(t/8e3)*40+Math.random()*25),V.cpu=Math.max(0,Math.min(100,V.cpu)),V.memory=Math.max(0,Math.min(100,V.memory)),V.gpu=Math.max(0,Math.min(100,V.gpu));const e=F.value.length,l=F.value.filter(p=>p.status==="completed").length,n=F.value.filter(p=>p.status==="failed").length,i=F.value.filter(p=>p.status==="processing").length;z.pending=Math.max(0,e-l-n-i),z.processing=i+y.value.length,z.completed=l,z.failed=n;const u=[{level:"info",message:"音频处理任务已完成"},{level:"success",message:"文件上传成功"},{level:"info",message:"系统监控数据已更新"},{level:"warning",message:"GPU内存使用率较高"},{level:"info",message:"新用户连接到系统"},{level:"success",message:"批量处理任务完成"},{level:"info",message:"WebSocket连接已建立"},{level:"warning",message:"磁盘空间使用率超过80%"}];if(j.value.length<5){const p=u[Math.floor(Math.random()*u.length)];j.value.unshift({id:Date.now()+Math.random(),time:new Date,level:p.level,message:p.message}),j.value.length>10&&(j.value=j.value.slice(0,10))}console.log("✅ 监控数据已更新:",{systemStatus:V,queueStatus:z,activeTasks:y.value.length,logs:j.value.length}),N(),le(),c.success("监控数据已刷新")}catch(t){console.error("❌ 刷新监控数据失败:",t),c.error("刷新监控数据失败")}},ns=async()=>{try{const t=y.value.filter(i=>i.status==="completed"||i.status==="failed");if(t.length===0){c.info("没有需要清理的已完成任务");return}await re.confirm(`确定要清理 ${t.length} 个已完成的任务吗？`,"清理确认",{confirmButtonText:"确定",cancelButtonText:"取消",type:"info"}),t.forEach(i=>{Be(i.id)});const e=y.value.length;y.value=y.value.filter(i=>i.status!=="completed"&&i.status!=="failed");const l=y.value.length,n=e-l;z.completed=0,z.failed=0,c.success(`已清理 ${n} 个已完成的任务`),console.log(`🧹 清理了 ${n} 个已完成任务`)}catch(t){t!=="cancel"&&(console.error("清理任务失败:",t),c.error("清理任务失败"))}},as=()=>{c.info("日志导出功能开发中...")},is=t=>({uploaded:"success",processing:"warning",completed:"info",failed:"danger"})[t]||"info",rs=t=>({uploaded:"已上传",processing:"处理中",completed:"已完成",failed:"失败"})[t]||"未知",ds=t=>{re({title:`🎵 音频预览 - ${t.name}`,message:h("div",{style:"text-align: center; padding: 20px;"},[h("audio",{controls:!0,style:"width: 100%; margin-bottom: 15px;",src:`/api/v1/audio/file/${t.id}/stream`}),h("div",{style:"color: #666; font-size: 14px;"},[h("p",`文件大小: ${Ze(t.size)}`),h("p",`时长: ${ke(t.duration)}`),h("p",`上传时间: ${ge(t.upload_time)}`)])]),showCancelButton:!0,confirmButtonText:"下载文件",cancelButtonText:"关闭",customClass:"audio-preview-dialog"}).then(()=>{rt(t)}).catch(()=>{})},us=async t=>{var e;try{c.info(`开始处理文件: ${t.name}`);const l={language:d.language,use_itn:d.use_itn,ban_emo_unk:d.ban_emo_unk,merge_vad:d.merge_vad,merge_length_s:d.merge_length_s,min_speech_duration:d.min_speech_duration,max_speech_duration:d.max_speech_duration,threshold:d.threshold,output_format:d.output_format,include_timestamps:d.include_timestamps,include_confidence:d.include_confidence,speaker_labeling:d.speaker_labeling,segmentation_strategy:d.segmentation_strategy,time_window_size:d.time_window_size,min_segments_required:d.min_segments_required,overlap_ratio:d.overlap_ratio,force_split_threshold:d.force_split_threshold,expected_speakers:d.expected_speakers,similarity_threshold:d.similarity_threshold,clustering_method:d.clustering_method,min_segment_duration:d.min_segment_duration};console.log("提交处理任务:",{mode:I.value,fileIds:[t.id],config:l});const{startAudioProcessing:n}=Zs(),i=[{id:t.id,uid:t.id,name:t.name}],u=await n({files:i,mode:I.value,config:l});if(console.log("处理任务响应:",u),u.data&&u.data.success){const p=u.data.task_id;c.success(`文件 ${t.name} 处理任务已创建，任务ID: ${p}`),ct({id:p,file_name:t.name,mode:I.value,status:"pending",progress:0,start_time:new Date().toISOString()}),z.pending++,N()}else if(u.success){const p=u.task_id;c.success(`文件 ${t.name} 处理任务已创建，任务ID: ${p}`),ct({id:p,file_name:t.name,mode:I.value,status:"pending",progress:0,start_time:new Date().toISOString()}),z.pending++,N()}else{const p=((e=u.data)==null?void 0:e.message)||u.message||"创建处理任务失败";c.error(p)}}catch(l){console.error("处理文件失败:",l),c.error(`处理文件 ${t.name} 失败`)}},cs=async t=>{try{await re.confirm(`确定要删除文件 "${t.name}" 吗？`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"});const e=await K.deleteAudio(t.id);console.log("🗑️ 删除API响应:",e);const l=e.data||e;l.success?(c.success("删除成功"),N()):c.error(l.message||"删除失败")}catch(e){e!=="cancel"&&(console.error("删除文件失败:",e),c.error("删除文件失败"))}},ms=async()=>{if($.value.length===0){c.warning("请先选择要处理的文件");return}try{c.info(`开始批量处理 ${$.value.length} 个文件`);const t={mode:I.value,files:$.value.map(l=>l.id),config:{language:d.language,use_itn:d.use_itn,ban_emo_unk:d.ban_emo_unk,merge_vad:d.merge_vad,merge_length_s:d.merge_length_s,min_speech_duration:d.min_speech_duration,max_speech_duration:d.max_speech_duration,threshold:d.threshold,output_format:d.output_format,include_timestamps:d.include_timestamps,include_confidence:d.include_confidence,speaker_labeling:d.speaker_labeling,segmentation_strategy:d.segmentation_strategy,time_window_size:d.time_window_size,min_segments_required:d.min_segments_required,overlap_ratio:d.overlap_ratio,force_split_threshold:d.force_split_threshold,expected_speakers:d.expected_speakers,similarity_threshold:d.similarity_threshold,clustering_method:d.clustering_method,min_segment_duration:d.min_segment_duration}};console.log("提交批量处理任务:",t);const e=await qe.createTask(t);e.success?(c.success(`批量处理任务已创建，任务ID: ${e.task_id}`),$.value=[],N()):c.error(e.message||"创建批量处理任务失败")}catch(t){console.error("批量处理失败:",t),c.error("批量处理失败")}},ps=async()=>{if($.value.length===0){c.warning("请先选择要下载的文件");return}try{c.info(`开始批量下载 ${$.value.length} 个文件`);const t=await fetch("/api/v1/audio/files/batch-download",{method:"POST",headers:{"Content-Type":"application/json",...me.value},body:JSON.stringify({file_ids:$.value.map(i=>i.id)})});if(!t.ok)throw new Error("批量下载失败");const e=await t.blob(),l=URL.createObjectURL(e),n=document.createElement("a");n.href=l,n.download=`音频文件批量下载_${new Date().toISOString().slice(0,10)}.zip`,document.body.appendChild(n),n.click(),document.body.removeChild(n),URL.revokeObjectURL(l),c.success("批量下载完成"),$.value=[]}catch(t){console.error("批量下载失败:",t),c.error("批量下载失败")}},gs=async()=>{if($.value.length===0){c.warning("请先选择要删除的文件");return}try{await re.confirm(`确定要删除选中的 ${$.value.length} 个文件吗？此操作不可撤销。`,"批量删除确认",{confirmButtonText:"确定删除",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!0}),c.info(`开始批量删除 ${$.value.length} 个文件`);const t=await fetch("/api/v1/audio/batch/delete",{method:"POST",headers:{"Content-Type":"application/json",...me.value},body:JSON.stringify({file_ids:$.value.map(l=>l.id)})});if(!t.ok)throw new Error("批量删除失败");const e=await t.json();console.log("🗑️ 批量删除API响应:",e),e.success?(c.success(`成功删除 ${e.deleted_count} 个文件${e.failed_count>0?`，${e.failed_count} 个文件删除失败`:""}`),$.value=[],N()):c.error(e.message||"批量删除失败")}catch(t){t!=="cancel"&&(console.error("批量删除失败:",t),c.error("批量删除失败"))}},_s=()=>{$.value.length===J.value.length?($.value=[],c.info("已取消全选")):($.value=[...J.value],c.info(`已选中 ${$.value.length} 个文件`))},fs=()=>{$.value=[],c.info("已清空选择")},vs=t=>{switch(t){case"process":ms();break;case"download":ps();break;case"delete":gs();break;case"clear":fs();break;default:console.warn("未知的批量操作:",t)}},rt=async t=>{try{c.info(`正在下载文件: ${t.name}`);const e=Ve.service({lock:!0,text:"正在准备下载文件...",background:"rgba(0, 0, 0, 0.7)"});try{const l=await K.downloadAudioFile(t.id),n=new Blob([l.data]),i=URL.createObjectURL(n),u=document.createElement("a");u.href=i,u.download=t.name,document.body.appendChild(u),u.click(),document.body.removeChild(u),URL.revokeObjectURL(i),c.success(`文件 ${t.name} 下载成功`)}finally{e.close()}}catch(e){console.error("下载文件失败:",e),c.error(`下载文件失败: ${e.message}`)}},hs=async t=>{try{const{value:e}=await re.prompt("请输入新的文件名","重命名文件",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:t.name.replace(/\.[^/.]+$/,""),inputValidator:u=>!u||u.trim()===""?"文件名不能为空":u.length>100?"文件名不能超过100个字符":!0}),l=t.name.split(".").pop(),n=`${e.trim()}.${l}`,i=await K.renameFile(t.id,n);i.success?(c.success("文件重命名成功"),N()):c.error(i.message||"重命名失败")}catch(e){e!=="cancel"&&(console.error("重命名文件失败:",e),c.error("重命名文件失败"))}},ys=t=>{const e=`${window.location.origin}/api/v1/audio/file/${t.id}/stream`;navigator.clipboard?navigator.clipboard.writeText(e).then(()=>{c.success("文件链接已复制到剪贴板")}).catch(()=>{dt(e)}):dt(e)},dt=t=>{const e=document.createElement("textarea");e.value=t,document.body.appendChild(e),e.select();try{document.execCommand("copy"),c.success("文件链接已复制到剪贴板")}catch{c.error("复制失败，请手动复制")}document.body.removeChild(e)},bs=(t,e)=>{switch(t){case"rename":hs(e);break;case"copy":ys(e);break;case"delete":cs(e);break;default:console.warn("未知的文件操作:",t)}},ut=()=>{const t=_t();if(!t){console.error("没有有效的认证token，无法建立WebSocket连接");return}const e=`ws://localhost:8002/ws/audio-center?token=${t}`;try{L=new WebSocket(e),L.onopen=()=>{console.log("WebSocket连接已建立"),Ie.value=!0,ce.value=0},L.onmessage=l=>{try{const n=JSON.parse(l.data);ws(n)}catch(n){console.error("解析WebSocket消息失败:",n)}},L.onclose=()=>{console.log("WebSocket连接已关闭"),Ie.value=!1,ce.value<xt?(ce.value++,console.log(`尝试重连WebSocket (${ce.value}/${xt})`),Ce=setTimeout(ut,3e3*ce.value)):console.error("WebSocket重连失败，已达到最大重试次数")},L.onerror=l=>{console.error("WebSocket连接错误:",l)}}catch(l){console.error("创建WebSocket连接失败:",l)}},ws=t=>{switch(console.log("📨 收到WebSocket消息:",t),t.type){case"progress_update":if(t.payload&&t.payload.task_id){const e=t.payload.task_id;if(ne.has(e)){console.log(`⚠️ 任务 ${e} 已完成，跳过进度更新`);return}const l=t.payload.progress;let n=0,i="",u="processing";l&&(n=parseFloat(l.percentage)||0,i=l.detail||"",u=l.stage||"processing"),console.log(`📈 任务进度更新: ${e} - ${n}% - ${u} - ${i}`),_e(e,n,u,i)}break;case"task_progress":_e(t.task_id,t.progress,t.status);break;case"task_completed":if(t.payload&&t.payload.task_id){const e=t.payload.task_id;if(ne.has(e)){console.log(`⚠️ 任务 ${e} 已经处理过完成状态，跳过重复处理`);return}console.log(`✅ 任务完成: ${e}`),Oe(e,t.payload.result)}else if(t.task_id){const e=t.task_id;if(ne.has(e)){console.log(`⚠️ 任务 ${e} 已经处理过完成状态，跳过重复处理`);return}Oe(e,t.result)}break;case"task_failed":t.payload&&t.payload.task_id?(console.log(`❌ 任务失败: ${t.payload.task_id} - ${t.payload.error}`),Fe(t.payload.task_id,t.payload.error)):Fe(t.task_id,t.error);break;case"system_status":ks(t.status);break;case"file_uploaded":xs(t.file);break;case"log_message":$s(t.log);break;default:console.log("未知的WebSocket消息类型:",t.type)}},_e=(t,e,l,n="")=>{if(console.log(`📊 更新任务进度: ${t} - ${e}% - ${l} - ${n}`),ne.has(t)){console.log(`⚠️ 任务 ${t} 已完成，跳过进度更新`);return}let i=Math.min(Math.max(e||0,0),100);if(i===0&&l==="processing"&&!n){const _=y.value.find(g=>g.id===t);if(_){const g=new Date(_.start_time).getTime(),v=(Date.now()-g)/1e3;i=Math.min(Math.floor(v*30),95),console.log(`🎯 为快速任务生成模拟进度: ${i}% (经过${v.toFixed(1)}秒)`)}}else i>0&&console.log(`✅ 使用真实进度数据: ${i}%`);const u=y.value.find(_=>_.id===t);u?(u.progress=i,u.status=l,n&&(u.detail=n),u.updateTime=new Date().toLocaleTimeString(),console.log(`✅ 活动任务已更新: ${t}`)):console.log(`⚠️ 未找到活动任务: ${t}`);const p=F.value.find(_=>_.task_id===t);p?(p.progress=i,p.status=l,n&&(p.detail=n),console.log(`✅ 处理结果已更新: ${t}`)):console.log(`⚠️ 未找到处理结果: ${t}`),console.log(`📈 任务 ${t} 进度更新完成: ${i}% (${l})`)},ne=new Set,Oe=(t,e)=>{if(ne.has(t)){console.log(`⚠️ 任务 ${t} 已经处理过完成状态，跳过重复处理`);return}console.log(`🎉 处理任务完成: ${t}`),console.log("📊 任务结果数据结构:",JSON.stringify(e,null,2)),e&&e.task_type==="meeting_transcription"&&(console.log("🎤 会议转录结果详情:",{总文件数:e.total_files,成功数:e.success_count,失败数:e.error_count,结果数量:e.results?e.results.length:0}),e.results&&e.results.length>0&&e.results.forEach((i,u)=>{console.log(`📄 文件 ${u+1} 结果:`,{文件ID:i.file_id,状态:i.status,有文本内容:!!(i.result&&i.result.text),文本长度:i.result&&i.result.text?i.result.text.length:0,说话人数量:i.result&&i.result.speaker_segments?i.result.speaker_segments.length:0})})),ne.add(t),_e(t,100,"completed");const l=F.value.find(i=>i.task_id===t);l?(l.result=e,l.status="completed",l.progress=100,l.completed_at=new Date().toISOString(),console.log(`✅ 处理结果已更新: ${t}`),console.log("🔍 验证结果设置:",{任务ID:l.task_id,状态:l.status,进度:l.progress,有结果数据:!!l.result,结果类型:l.result?l.result.task_type:"unknown"})):console.error(`❌ 未找到处理结果项: ${t}`);const n=y.value.findIndex(i=>i.id===t);n!==-1&&(y.value[n],y.value.splice(n,1),Be(t),console.log(`🗑️ 活动任务已移除: ${t}`),y.value.length===0&&console.log("📴 所有任务已完成，减少轮询频率")),z.processing=Math.max(0,z.processing-1),z.completed++,c.success(`任务 ${t.substring(0,8)}... 处理完成`),setTimeout(()=>{le()},500),console.log(`📊 任务完成处理完毕: ${t}`)},Fe=(t,e)=>{console.log(`❌ 处理任务失败: ${t} - ${e}`),_e(t,0,"failed",e);const l=F.value.find(i=>i.task_id===t);l&&(l.error=e,l.status="failed",l.progress=0,l.failed_at=new Date().toISOString(),console.log(`❌ 处理结果已标记为失败: ${t}`));const n=y.value.findIndex(i=>i.id===t);n!==-1&&(y.value.splice(n,1),Be(t),console.log(`🗑️ 失败任务已移除: ${t}`)),z.processing=Math.max(0,z.processing-1),z.failed++,c.error(`任务 ${t.substring(0,8)}... 处理失败: ${e}`),setTimeout(()=>{le()},100),console.log(`📊 任务失败处理完毕: ${t}`)},ks=t=>{t.cpu!==void 0&&(V.cpu=t.cpu),t.memory!==void 0&&(V.memory=t.memory),t.gpu!==void 0&&(V.gpu=t.gpu)},xs=t=>{c.success(`文件 ${t.name} 上传成功`),N()},$s=t=>{j.value.unshift({id:Date.now(),time:new Date,level:t.level||"info",message:t.message}),j.value.length>20&&(j.value=j.value.slice(0,20))},Ss=()=>{Ce&&(clearTimeout(Ce),Ce=null),L&&(L.close(),L=null)},Cs=t=>{if(L&&L.readyState===WebSocket.OPEN){const e={type:"subscribe",task_id:t};L.send(JSON.stringify(e)),console.log(`📡 订阅任务进度: ${t}`)}else console.warn(`⚠️ WebSocket未连接，无法订阅任务: ${t}`)},Be=t=>{if(L&&L.readyState===WebSocket.OPEN){const e={type:"unsubscribe",task_id:t};L.send(JSON.stringify(e)),console.log(`📡 取消订阅任务: ${t}`)}};let ae=null,Se=null,Ce=null;const Ts=()=>{ae&&clearInterval(ae),ae=setInterval(async()=>{y.value.length>0&&(console.log(`🔄 轮询检查 ${y.value.length} 个活动任务状态`),await zs())},1e4)},Vs=()=>{ae&&(clearInterval(ae),ae=null,console.log("⏹️ 停止任务状态轮询"))},zs=async()=>{try{if(y.value.length===0)return;const e=(await K.getActiveTasks()).data||[];for(const l of y.value){const n=e.find(i=>i.id===l.id||i.task_id===l.id);if(n){const i=n.progress||0,u=n.status||"processing",p=n.detail||"";(u!==l.status||i!==l.progress)&&(console.log(`🔄 检测到任务状态变化: ${l.id} - ${u} ${i}%`),_e(l.id,i,u,p))}else console.log(`🔍 任务不在活动列表中，检查是否已完成: ${l.id}`),await As(l.id)}}catch(t){console.warn("⚠️ 轮询检查任务状态失败:",t)}},As=async t=>{try{const e=await qe.getTaskStatus(t),l=e.data||e;console.log(`🔍 检查任务状态: ${t}`,l),l&&(l.status==="completed"||l.status==="SUCCESS")?(console.log(`✅ 发现已完成的任务: ${t}`),Oe(t,l.result)):l&&(l.status==="failed"||l.status==="FAILURE")?(console.log(`❌ 发现失败的任务: ${t}`),Fe(t,l.error||"任务执行失败")):console.log(`⏳ 任务仍在处理中: ${t} - 状态: ${l==null?void 0:l.status}`)}catch(e){console.warn(`⚠️ 检查任务完成状态失败: ${t}`,e)}},ct=t=>{y.value.push(t),console.log("添加活动任务:",t),Cs(t.id)},Ms=t=>({"speech-recognition":{use_itn:!0,ban_emo_unk:!1,merge_vad:!0,merge_length_s:15},"speaker-recognition":{use_itn:!1,ban_emo_unk:!0,merge_vad:!1,min_speech_duration:1},"vad-detection":{merge_vad:!0,threshold:.5,min_speech_duration:.5,max_speech_duration:60},"meeting-transcription":{use_itn:!0,merge_vad:!0,merge_length_s:10,min_speech_duration:.3},audio_enhancement:{use_itn:!0,ban_emo_unk:!1,merge_vad:!0}})[t]||{},Rs=()=>{var e;const t=Ms(I.value);Object.assign(d,t),c.success(`已应用${(e=pe.value)==null?void 0:e.title}模式的优化配置`)};return Ps(async()=>{console.log("AudioCenter mounted");const t=_t();t&&(me.value={Authorization:`Bearer ${t}`}),N(),le(),await Le(),ut(),Ts(),Se=setInterval(Le,3e4)}),js(()=>{console.log("AudioCenter unmounted"),B&&R.value!=="idle"&&B.stop(),te&&te.getTracks().forEach(t=>t.stop()),Re(),G.value&&URL.revokeObjectURL(G.value),Vs(),Se&&(clearInterval(Se),Se=null),Ss()}),(t,e)=>{var gt;const l=w("el-tag"),n=w("el-button"),i=w("el-button-group"),u=w("el-dropdown-item"),p=w("el-dropdown-menu"),_=w("el-dropdown"),g=w("el-option"),b=w("el-select"),v=w("el-alert"),x=w("el-input-number"),U=w("el-checkbox"),D=w("el-slider"),O=w("el-icon"),W=w("el-upload"),Z=w("ArrowDown"),E=w("el-table-column"),mt=w("el-table"),Ds=w("el-input"),Us=w("el-text"),fe=w("el-progress"),ie=w("el-descriptions-item"),Es=w("el-descriptions"),Pe=w("el-tab-pane"),Ls=w("el-tabs"),Os=w("el-dialog"),Fs=w("el-empty"),pt=Ns("loading");return k(),A("div",Ks,[s("div",Xs,[s("div",Qs,[e[34]||(e[34]=s("h1",{class:"page-title"},"🎵 音频处理中心",-1)),s("div",Ys,[y.value.length>0?(k(),Q(l,{key:0,type:"info",size:"small"},{default:a(()=>[m(f(y.value.length)+" 个活跃任务 ",1)]),_:1})):M("",!0),J.value.length>0?(k(),Q(l,{key:1,type:"success",size:"small"},{default:a(()=>[m(f(J.value.length)+" 个音频文件 ",1)]),_:1})):M("",!0)])]),s("div",eo,[o(i,null,{default:a(()=>[o(n,{onClick:Ct,icon:T(Ne),size:"small"},{default:a(()=>e[35]||(e[35]=[m(" 导出结果 ")])),_:1,__:[35]},8,["icon"]),o(n,{onClick:Tt,icon:T(ht),size:"small"},{default:a(()=>e[36]||(e[36]=[m(" 清空数据 ")])),_:1,__:[36]},8,["icon"])]),_:1}),o(_,{onCommand:Vt},{dropdown:a(()=>[o(p,null,{default:a(()=>[o(u,{command:"toggle-left"},{default:a(()=>[m(f(Y.value?"隐藏":"显示")+"配置面板 ",1)]),_:1}),o(u,{command:"toggle-right"},{default:a(()=>[m(f(ee.value?"隐藏":"显示")+"结果面板 ",1)]),_:1}),o(u,{command:"reset-layout"},{default:a(()=>e[37]||(e[37]=[m("重置布局")])),_:1,__:[37]})]),_:1})]),default:a(()=>[o(n,{icon:T(yt),size:"small",circle:""},null,8,["icon"])]),_:1})])]),s("div",{class:ye(["audio-center-workspace",St.value])},[se(s("div",to,[s("div",so,[e[38]||(e[38]=s("h3",null,"⚙️ 配置面板",-1)),o(n,{text:"",onClick:e[0]||(e[0]=r=>Y.value=!1),icon:T(bt),size:"small"},null,8,["icon"])]),s("div",oo,[s("div",lo,[e[44]||(e[44]=s("h4",null,"处理模式",-1)),o(b,{modelValue:I.value,"onUpdate:modelValue":e[1]||(e[1]=r=>I.value=r),placeholder:"选择处理模式",style:{width:"100%"},onChange:Ot,"popper-class":"mode-select-dropdown"},{default:a(()=>[o(g,{value:"speech_recognition",label:"基础语音识别"},{default:a(()=>e[39]||(e[39]=[s("div",{class:"mode-option"},[s("span",{class:"mode-icon"},"🎤"),s("div",{class:"mode-info"},[s("div",{class:"mode-name"},"基础语音识别"),s("div",{class:"mode-desc"},"直接将音频转换为文字")])],-1)])),_:1}),o(g,{value:"speaker_recognition",label:"说话人识别"},{default:a(()=>e[40]||(e[40]=[s("div",{class:"mode-option"},[s("span",{class:"mode-icon"},"👥"),s("div",{class:"mode-info"},[s("div",{class:"mode-name"},"说话人识别"),s("div",{class:"mode-desc"},"识别并区分不同说话人")])],-1)])),_:1}),o(g,{value:"meeting_transcription",label:"会议转录模式"},{default:a(()=>e[41]||(e[41]=[s("div",{class:"mode-option"},[s("span",{class:"mode-icon"},"📝"),s("div",{class:"mode-info"},[s("div",{class:"mode-name"},"会议转录模式"),s("div",{class:"mode-desc"},"多人会议场景增强识别")])],-1)])),_:1}),o(g,{value:"vad_detection",label:"VAD语音活动检测"},{default:a(()=>e[42]||(e[42]=[s("div",{class:"mode-option"},[s("span",{class:"mode-icon"},"🔍"),s("div",{class:"mode-info"},[s("div",{class:"mode-name"},"VAD语音活动检测"),s("div",{class:"mode-desc"},"检测音频中的语音活动段和静音段")])],-1)])),_:1}),o(g,{value:"audio_enhancement",label:"音频增强"},{default:a(()=>e[43]||(e[43]=[s("div",{class:"mode-option"},[s("span",{class:"mode-icon"},"⚡"),s("div",{class:"mode-info"},[s("div",{class:"mode-name"},"音频增强"),s("div",{class:"mode-desc"},"预处理后识别音质较差的音频")])],-1)])),_:1})]),_:1},8,["modelValue"]),pe.value?(k(),A("div",no,[o(v,{title:pe.value.title,description:pe.value.desc,type:"info",closable:!1,"show-icon":""},null,8,["title","description"])])):M("",!0)]),I.value==="meeting_transcription"?(k(),A("div",ao,[e[67]||(e[67]=s("h4",null,"🎤 会议转录配置",-1)),s("div",io,[e[53]||(e[53]=s("div",{class:"subgroup-header"},[s("h5",null,"🔧 分段策略设置")],-1)),s("div",ro,[e[45]||(e[45]=s("label",null,"分段策略",-1)),o(b,{modelValue:d.segmentation_strategy,"onUpdate:modelValue":e[2]||(e[2]=r=>d.segmentation_strategy=r),size:"small",style:{width:"100%"}},{default:a(()=>[o(g,{label:"混合策略 (推荐)",value:"hybrid"}),o(g,{label:"仅VAD分段",value:"vad_only"}),o(g,{label:"时间窗口分段",value:"time_window"})]),_:1},8,["modelValue"]),e[46]||(e[46]=s("div",{class:"config-hint"},"混合策略结合智能VAD和强制时间窗口，确保充足的分段数量",-1))]),d.segmentation_strategy==="hybrid"||d.segmentation_strategy==="time_window"?(k(),A("div",uo,[e[47]||(e[47]=s("label",null,"时间窗口大小 (秒)",-1)),o(x,{modelValue:d.time_window_size,"onUpdate:modelValue":e[3]||(e[3]=r=>d.time_window_size=r),min:2,max:10,step:.5,size:"small",style:{width:"100%"}},null,8,["modelValue"]),e[48]||(e[48]=s("div",{class:"config-hint"},"设置时间窗口分段的大小，较小的值有助于区分说话人",-1))])):M("",!0),d.segmentation_strategy==="hybrid"?(k(),A("div",co,[e[49]||(e[49]=s("label",null,"最小分段数量",-1)),o(x,{modelValue:d.min_segments_required,"onUpdate:modelValue":e[4]||(e[4]=r=>d.min_segments_required=r),min:2,max:10,step:1,size:"small",style:{width:"100%"}},null,8,["modelValue"]),e[50]||(e[50]=s("div",{class:"config-hint"},"VAD分段不足时触发混合策略的最小分段数量阈值",-1))])):M("",!0),d.segmentation_strategy==="hybrid"?(k(),A("div",mo,[e[51]||(e[51]=s("label",null,"强制分割阈值 (秒)",-1)),o(x,{modelValue:d.force_split_threshold,"onUpdate:modelValue":e[5]||(e[5]=r=>d.force_split_threshold=r),min:5,max:30,step:1,size:"small",style:{width:"100%"}},null,8,["modelValue"]),e[52]||(e[52]=s("div",{class:"config-hint"},"超过此时长的片段将被强制分割为更小的片段",-1))])):M("",!0)]),s("div",po,[o(U,{modelValue:d.speaker_labeling,"onUpdate:modelValue":e[6]||(e[6]=r=>d.speaker_labeling=r)},{default:a(()=>e[54]||(e[54]=[m(" 说话人标注 ")])),_:1,__:[54]},8,["modelValue"])]),d.speaker_labeling?(k(),A("div",go,[e[66]||(e[66]=s("div",{class:"subgroup-header"},[s("h5",null,"👥 说话人识别设置")],-1)),s("div",_o,[e[55]||(e[55]=s("label",null,"预期说话人数量",-1)),o(x,{modelValue:d.expected_speakers,"onUpdate:modelValue":e[7]||(e[7]=r=>d.expected_speakers=r),min:1,max:10,step:1,size:"small",style:{width:"100%"}},null,8,["modelValue"]),e[56]||(e[56]=s("div",{class:"config-hint"},"设置预期的说话人数量，有助于优化聚类效果",-1))]),s("div",fo,[e[57]||(e[57]=s("label",null,"聚类方法",-1)),o(b,{modelValue:d.clustering_method,"onUpdate:modelValue":e[8]||(e[8]=r=>d.clustering_method=r),size:"small",style:{width:"100%"}},{default:a(()=>[o(g,{label:"自动选择",value:"auto"}),o(g,{label:"层次聚类",value:"agglomerative"}),o(g,{label:"DBSCAN",value:"dbscan"})]),_:1},8,["modelValue"]),e[58]||(e[58]=s("div",{class:"config-hint"},"选择说话人聚类算法，自动选择适合大多数场景",-1))]),s("div",vo,[e[59]||(e[59]=s("label",null,"聚类阈值",-1)),o(D,{modelValue:d.similarity_threshold,"onUpdate:modelValue":e[9]||(e[9]=r=>d.similarity_threshold=r),min:.1,max:.8,step:.05,"format-tooltip":It,style:{"margin-bottom":"10px"}},null,8,["modelValue"]),s("div",ho,"调整说话人相似度阈值，值越小越容易区分不同说话人 (当前: "+f(d.similarity_threshold)+")",1)]),s("div",yo,[o(n,{onClick:Jt,size:"small",style:{width:"100%"},type:"primary"},{default:a(()=>e[60]||(e[60]=[m(" 🎯 智能推荐配置 ")])),_:1,__:[60]}),e[61]||(e[61]=s("div",{class:"config-hint"},"根据说话人数量自动推荐最佳配置参数",-1))]),s("div",bo,[e[65]||(e[65]=s("label",null,"场景预设",-1)),o(i,{style:{width:"100%"}},{default:a(()=>[o(n,{onClick:e[10]||(e[10]=r=>De("two_person")),size:"small",style:{flex:"1"}},{default:a(()=>e[62]||(e[62]=[m(" 两人对话 ")])),_:1,__:[62]}),o(n,{onClick:e[11]||(e[11]=r=>De("small_group")),size:"small",style:{flex:"1"}},{default:a(()=>e[63]||(e[63]=[m(" 小组讨论 ")])),_:1,__:[63]}),o(n,{onClick:e[12]||(e[12]=r=>De("large_meeting")),size:"small",style:{flex:"1"}},{default:a(()=>e[64]||(e[64]=[m(" 大型会议 ")])),_:1,__:[64]})]),_:1})])])):M("",!0)])):M("",!0),s("div",wo,[e[68]||(e[68]=s("h4",null,"语言设置",-1)),o(b,{modelValue:d.language,"onUpdate:modelValue":e[13]||(e[13]=r=>d.language=r),placeholder:"选择语言",style:{width:"100%"}},{default:a(()=>[o(g,{value:"auto",label:"自动检测"}),o(g,{value:"zh-CN",label:"中文"}),o(g,{value:"en-US",label:"英文"})]),_:1},8,["modelValue"])]),s("div",ko,[e[74]||(e[74]=s("h4",null,"高级设置",-1)),s("div",xo,[o(U,{modelValue:d.use_itn,"onUpdate:modelValue":e[14]||(e[14]=r=>d.use_itn=r)},{default:a(()=>e[69]||(e[69]=[m(" 启用逆文本标准化 ")])),_:1,__:[69]},8,["modelValue"])]),s("div",$o,[o(U,{modelValue:d.ban_emo_unk,"onUpdate:modelValue":e[15]||(e[15]=r=>d.ban_emo_unk=r)},{default:a(()=>e[70]||(e[70]=[m(" 禁用情感和未知标记 ")])),_:1,__:[70]},8,["modelValue"])]),s("div",So,[o(U,{modelValue:d.merge_vad,"onUpdate:modelValue":e[16]||(e[16]=r=>d.merge_vad=r)},{default:a(()=>e[71]||(e[71]=[m(" 合并VAD片段 ")])),_:1,__:[71]},8,["modelValue"])]),s("div",Co,[o(n,{onClick:Rs,size:"small",style:{width:"100%"},type:"primary"},{default:a(()=>e[72]||(e[72]=[m(" 🎯 应用优化配置 ")])),_:1,__:[72]}),e[73]||(e[73]=s("div",{class:"config-hint"},"根据当前处理模式自动应用最佳配置参数",-1))])]),se(s("div",To,[e[82]||(e[82]=s("h4",null,"🔍 VAD检测配置",-1)),s("div",Vo,[e[75]||(e[75]=s("label",null,"合并长度 (秒)",-1)),o(x,{modelValue:d.merge_length_s,"onUpdate:modelValue":e[17]||(e[17]=r=>d.merge_length_s=r),min:1,max:60,step:1,size:"small",style:{width:"100%"},onChange:xe},null,8,["modelValue"]),e[76]||(e[76]=s("div",{class:"config-hint"},"相邻语音片段间隔小于此值时将被合并",-1))]),s("div",zo,[e[77]||(e[77]=s("label",null,"最小语音时长 (秒)",-1)),o(x,{modelValue:d.min_speech_duration,"onUpdate:modelValue":e[18]||(e[18]=r=>d.min_speech_duration=r),min:.1,max:10,step:.1,precision:1,size:"small",style:{width:"100%"},onChange:xe},null,8,["modelValue"]),e[78]||(e[78]=s("div",{class:"config-hint"},"短于此时长的语音片段将被过滤",-1))]),s("div",Ao,[e[79]||(e[79]=s("label",null,"最大语音时长 (秒)",-1)),o(x,{modelValue:d.max_speech_duration,"onUpdate:modelValue":e[19]||(e[19]=r=>d.max_speech_duration=r),min:10,max:300,step:5,size:"small",style:{width:"100%"},onChange:xe},null,8,["modelValue"]),e[80]||(e[80]=s("div",{class:"config-hint"},"超过此时长的语音片段将被分割",-1))]),s("div",Mo,[e[81]||(e[81]=s("label",null,"检测阈值",-1)),o(D,{modelValue:d.threshold,"onUpdate:modelValue":e[20]||(e[20]=r=>d.threshold=r),min:.1,max:.9,step:.05,"format-tooltip":qt,onChange:xe},null,8,["modelValue"]),s("div",Ro,"阈值越高，检测越严格 (当前: "+f(d.threshold)+")",1)])],512),[[he,I.value==="vad_detection"||d.merge_vad]]),s("div",Do,[e[92]||(e[92]=s("h4",null,"🎙️ 浏览器录音",-1)),s("div",Uo,[s("div",{class:ye(["status-indicator",R.value])},[e[83]||(e[83]=s("div",{class:"status-dot"},null,-1)),s("span",Eo,f(zt()),1)],2),s("div",Lo,f(ke(de.value)),1)]),s("div",Oo,[s("div",Fo,[e[84]||(e[84]=s("label",null,"录音质量",-1)),o(b,{modelValue:H.quality,"onUpdate:modelValue":e[21]||(e[21]=r=>H.quality=r),size:"small",style:{width:"100%"}},{default:a(()=>[o(g,{label:"高质量 (48kHz)",value:"high"}),o(g,{label:"中等质量 (44.1kHz)",value:"medium"}),o(g,{label:"低质量 (16kHz)",value:"low"})]),_:1},8,["modelValue"])]),s("div",Bo,[e[85]||(e[85]=s("label",null,"录音格式",-1)),o(b,{modelValue:H.format,"onUpdate:modelValue":e[22]||(e[22]=r=>H.format=r),size:"small",style:{width:"100%"}},{default:a(()=>[o(g,{label:"WAV格式",value:"wav"}),o(g,{label:"WebM格式",value:"webm"})]),_:1},8,["modelValue"])])]),s("div",Po,[s("div",jo,[o(n,{onClick:At,disabled:R.value!=="idle",icon:T(qs),type:"primary",size:"small",style:{flex:"1"}},{default:a(()=>e[86]||(e[86]=[m(" 开始录音 ")])),_:1,__:[86]},8,["disabled","icon"]),o(n,{onClick:e[23]||(e[23]=r=>R.value==="recording"?Mt():Rt()),disabled:R.value==="idle",icon:R.value==="recording"?T(Is):T(Js),size:"small",style:{flex:"1"}},{default:a(()=>[m(f(R.value==="recording"?"暂停":"继续"),1)]),_:1},8,["disabled","icon"])]),o(n,{onClick:Dt,disabled:R.value==="idle",size:"small",style:{width:"100%","margin-top":"8px"}},{default:a(()=>e[87]||(e[87]=[m(" 停止录音 ")])),_:1,__:[87]},8,["disabled"])]),G.value?(k(),A("div",No,[s("div",Wo,[e[88]||(e[88]=s("span",null,"录音预览",-1)),s("span",qo,f(ke(de.value)),1)]),s("audio",{src:G.value,controls:"",style:{width:"100%",margin:"8px 0"}},null,8,Io),s("div",Jo,[o(n,{onClick:Ut,type:"primary",size:"small",style:{flex:"1"}},{default:a(()=>e[89]||(e[89]=[m(" 保存录音 ")])),_:1,__:[89]}),o(n,{onClick:Ge,size:"small",style:{flex:"1"}},{default:a(()=>e[90]||(e[90]=[m(" 丢弃录音 ")])),_:1,__:[90]})])])):M("",!0),s("div",Go,[o(v,{title:"录音提示",type:"info",closable:!1,"show-icon":""},{default:a(()=>e[91]||(e[91]=[s("ul",{style:{margin:"0","padding-left":"16px","font-size":"12px"}},[s("li",null,"首次使用需要授权麦克风权限"),s("li",null,"录音文件会自动上传到文件列表"),s("li",null,"建议在安静环境下录音以获得最佳效果")],-1)])),_:1})])])])],512),[[he,Y.value]]),s("div",Ho,[s("div",Zo,[s("div",Ko,[s("button",{class:ye(["tab-button",{active:oe.value==="file-management"}]),onClick:e[24]||(e[24]=r=>oe.value="file-management")}," 📁 文件管理 ",2),s("button",{class:ye(["tab-button",{active:oe.value==="results"}]),onClick:e[25]||(e[25]=r=>oe.value="results")}," 📊 处理结果 ",2)])]),s("div",Xo,[se(s("div",Qo,[s("div",Yo,[o(W,{ref:"uploadRef",class:"upload-dragger",drag:"",action:Je.value,headers:me.value,multiple:!0,accept:an,"before-upload":Ft,"on-success":Pt,"on-error":jt,"on-progress":Bt,"file-list":$t.value,"auto-upload":!0,limit:10,"on-exceed":Nt},{tip:a(()=>e[93]||(e[93]=[s("div",{class:"el-upload__tip"}," 支持 MP3、WAV、M4A、AAC、FLAC、OGG 格式，单个文件不超过 200MB，最多同时上传 10 个文件 ",-1)])),default:a(()=>[o(O,{class:"el-icon--upload"},{default:a(()=>[o(T(Gs))]),_:1}),e[94]||(e[94]=s("div",{class:"el-upload__text"},[m(" 将音频文件拖拽到此处，或"),s("em",null,"点击上传")],-1))]),_:1,__:[94]},8,["action","headers","file-list"])]),s("div",el,[s("div",tl,[e[100]||(e[100]=s("h4",null,"📂 音频文件列表",-1)),s("div",sl,[o(n,{onClick:N,icon:T(We),size:"small"},{default:a(()=>e[95]||(e[95]=[m(" 刷新 ")])),_:1,__:[95]},8,["icon"]),o(n,{onClick:_s,size:"small"},{default:a(()=>[m(f($.value.length===J.value.length&&J.value.length>0?"取消全选":"全选"),1)]),_:1}),o(_,{onCommand:vs,disabled:$.value.length===0},{dropdown:a(()=>[o(p,null,{default:a(()=>[o(u,{command:"process"},{default:a(()=>e[96]||(e[96]=[m("批量处理")])),_:1,__:[96]}),o(u,{command:"download"},{default:a(()=>e[97]||(e[97]=[m("批量下载")])),_:1,__:[97]}),o(u,{command:"delete",divided:""},{default:a(()=>e[98]||(e[98]=[m("批量删除")])),_:1,__:[98]}),o(u,{command:"clear",divided:""},{default:a(()=>e[99]||(e[99]=[m("清空选择")])),_:1,__:[99]})]),_:1})]),default:a(()=>[o(n,{size:"small",disabled:$.value.length===0},{default:a(()=>[m(" 批量操作 ("+f($.value.length)+") ",1),o(O,null,{default:a(()=>[o(Z)]),_:1})]),_:1},8,["disabled"])]),_:1},8,["disabled"])])]),se((k(),Q(mt,{data:J.value,onSelectionChange:Wt,style:{width:"100%"},"empty-text":"暂无音频文件"},{default:a(()=>[o(E,{type:"selection",width:"55"}),o(E,{prop:"name",label:"文件名","min-width":"200"},{default:a(({row:r})=>[s("div",ol,[o(O,null,{default:a(()=>[o(T(wt))]),_:1}),s("span",null,f(r.name),1)])]),_:1}),o(E,{prop:"size",label:"大小",width:"100"},{default:a(({row:r})=>[m(f(Ze(r.size)),1)]),_:1}),o(E,{prop:"duration",label:"时长",width:"100"},{default:a(({row:r})=>[m(f(ke(r.duration)),1)]),_:1}),o(E,{prop:"upload_time",label:"上传时间",width:"160"},{default:a(({row:r})=>[m(f(ge(r.upload_time)),1)]),_:1}),o(E,{prop:"status",label:"状态",width:"100"},{default:a(({row:r})=>[o(l,{type:is(r.status)},{default:a(()=>[m(f(rs(r.status)),1)]),_:2},1032,["type"])]),_:1}),o(E,{label:"操作",width:"280",fixed:"right"},{default:a(({row:r})=>[s("div",ll,[o(n,{onClick:q=>ds(r),icon:T(kt),size:"small",title:"预览"},{default:a(()=>e[101]||(e[101]=[m(" 预览 ")])),_:2,__:[101]},1032,["onClick","icon"]),o(n,{onClick:q=>rt(r),icon:T(Ne),size:"small",title:"下载"},{default:a(()=>e[102]||(e[102]=[m(" 下载 ")])),_:2,__:[102]},1032,["onClick","icon"]),o(n,{onClick:q=>us(r),type:"primary",size:"small",title:"处理"},{default:a(()=>e[103]||(e[103]=[m(" 处理 ")])),_:2,__:[103]},1032,["onClick"]),o(_,{onCommand:q=>bs(q,r),trigger:"click"},{dropdown:a(()=>[o(p,null,{default:a(()=>[o(u,{command:"rename"},{default:a(()=>e[105]||(e[105]=[m("重命名")])),_:1,__:[105]}),o(u,{command:"copy"},{default:a(()=>e[106]||(e[106]=[m("复制链接")])),_:1,__:[106]}),o(u,{command:"delete",divided:""},{default:a(()=>e[107]||(e[107]=[m("删除文件")])),_:1,__:[107]})]),_:1})]),default:a(()=>[o(n,{size:"small",icon:T(yt),title:"更多操作"},{default:a(()=>e[104]||(e[104]=[m(" 更多 ")])),_:1,__:[104]},8,["icon"])]),_:2},1032,["onCommand"])])]),_:1})]),_:1},8,["data"])),[[pt,ze.value]])])],512),[[he,oe.value==="file-management"]]),se(s("div",nl,[s("div",al,[s("div",il,[o(b,{modelValue:P.status,"onUpdate:modelValue":e[26]||(e[26]=r=>P.status=r),placeholder:"状态筛选",size:"small",style:{width:"120px","margin-right":"12px"}},{default:a(()=>[o(g,{value:"",label:"全部状态"}),o(g,{value:"completed",label:"已完成"}),o(g,{value:"processing",label:"处理中"}),o(g,{value:"failed",label:"失败"})]),_:1},8,["modelValue"]),o(b,{modelValue:P.mode,"onUpdate:modelValue":e[27]||(e[27]=r=>P.mode=r),placeholder:"模式筛选",size:"small",style:{width:"140px","margin-right":"12px"}},{default:a(()=>[o(g,{value:"",label:"全部模式"}),o(g,{value:"speech-recognition",label:"语音识别"}),o(g,{value:"speaker-recognition",label:"说话人识别"}),o(g,{value:"vad-detection",label:"VAD检测"})]),_:1},8,["modelValue"]),o(Ds,{modelValue:P.search,"onUpdate:modelValue":e[28]||(e[28]=r=>P.search=r),placeholder:"搜索文件名或任务ID",size:"small",style:{width:"200px","margin-right":"12px"},clearable:""},null,8,["modelValue"]),o(n,{onClick:le,icon:T(We),size:"small"},{default:a(()=>e[108]||(e[108]=[m("刷新")])),_:1,__:[108]},8,["icon"]),o(_,{onCommand:ts,disabled:X.value.length===0},{dropdown:a(()=>[o(p,null,{default:a(()=>[o(u,{command:"txt"},{default:a(()=>e[109]||(e[109]=[m("导出TXT")])),_:1,__:[109]}),o(u,{command:"json"},{default:a(()=>e[110]||(e[110]=[m("导出JSON")])),_:1,__:[110]}),o(u,{command:"srt"},{default:a(()=>e[111]||(e[111]=[m("导出SRT字幕")])),_:1,__:[111]}),o(u,{command:"word"},{default:a(()=>e[112]||(e[112]=[m("导出Word")])),_:1,__:[112]})]),_:1})]),default:a(()=>[o(n,{size:"small",disabled:X.value.length===0},{default:a(()=>[m(" 批量导出 ("+f(X.value.length)+") ",1),o(O,null,{default:a(()=>[o(Z)]),_:1})]),_:1},8,["disabled"])]),_:1},8,["disabled"])])]),s("div",rl,[se((k(),Q(mt,{data:Ue.value,style:{width:"100%"},"empty-text":"暂无处理结果",onRowClick:Qe,onSelectionChange:es},{default:a(()=>[o(E,{type:"selection",width:"55"}),o(E,{prop:"task_id",label:"任务ID",width:"200"},{default:a(({row:r})=>[o(Us,{class:"task-id-text",truncated:""},{default:a(()=>[m(f(r.task_id),1)]),_:2},1024)]),_:1}),o(E,{prop:"file_name",label:"文件名","min-width":"200"},{default:a(({row:r})=>[s("div",dl,[o(O,null,{default:a(()=>[o(T(wt))]),_:1}),s("span",null,f(r.file_name),1)])]),_:1}),o(E,{prop:"mode",label:"处理模式",width:"120"},{default:a(({row:r})=>[o(l,{size:"small"},{default:a(()=>[m(f(Ee(r.mode)),1)]),_:2},1024)]),_:1}),o(E,{prop:"status",label:"状态",width:"100"},{default:a(({row:r})=>[o(l,{type:Ke(r.status),size:"small"},{default:a(()=>[m(f(Xe(r.status)),1)]),_:2},1032,["type"])]),_:1}),o(E,{prop:"progress",label:"进度",width:"120"},{default:a(({row:r})=>[o(fe,{percentage:r.progress,status:r.status==="failed"?"exception":r.status==="completed"?"success":"","stroke-width":6,"show-text":!1},null,8,["percentage","status"]),s("span",ul,f(r.progress)+"%",1)]),_:1}),o(E,{prop:"created_time",label:"创建时间",width:"160"},{default:a(({row:r})=>[m(f(ge(r.created_time)),1)]),_:1}),o(E,{label:"操作",width:"200",fixed:"right"},{default:a(({row:r})=>[s("div",cl,[o(n,{onClick:Te(q=>Qe(r),["stop"]),icon:T(kt),size:"small"},{default:a(()=>e[113]||(e[113]=[m(" 查看 ")])),_:2,__:[113]},1032,["onClick","icon"]),r.status==="completed"?(k(),Q(n,{key:0,onClick:Te(q=>et(r),["stop"]),icon:T(Ne),size:"small"},{default:a(()=>e[114]||(e[114]=[m(" 下载 ")])),_:2,__:[114]},1032,["onClick","icon"])):M("",!0),r.status==="completed"?(k(),Q(_,{key:1,onCommand:q=>Yt(q,r)},{dropdown:a(()=>[o(p,null,{default:a(()=>[o(u,{command:"txt"},{default:a(()=>e[116]||(e[116]=[m("导出TXT")])),_:1,__:[116]}),o(u,{command:"json"},{default:a(()=>e[117]||(e[117]=[m("导出JSON")])),_:1,__:[117]}),o(u,{command:"srt"},{default:a(()=>e[118]||(e[118]=[m("导出SRT字幕")])),_:1,__:[118]}),o(u,{command:"word"},{default:a(()=>e[119]||(e[119]=[m("导出Word")])),_:1,__:[119]})]),_:1})]),default:a(()=>[o(n,{onClick:e[29]||(e[29]=Te(()=>{},["stop"])),size:"small"},{default:a(()=>[e[115]||(e[115]=m(" 导出 ")),o(O,null,{default:a(()=>[o(Z)]),_:1})]),_:1,__:[115]})]),_:2},1032,["onCommand"])):M("",!0),o(n,{onClick:Te(q=>Gt(r),["stop"]),icon:T(ht),type:"danger",size:"small"},{default:a(()=>e[120]||(e[120]=[m(" 删除 ")])),_:2,__:[120]},1032,["onClick","icon"])])]),_:1})]),_:1},8,["data"])),[[pt,Ae.value]])]),o(Os,{modelValue:we.value,"onUpdate:modelValue":e[32]||(e[32]=r=>we.value=r),title:`处理结果详情 - ${(gt=C.value)==null?void 0:gt.file_name}`,width:"80%","before-close":Ye},{footer:a(()=>{var r;return[o(n,{onClick:Ye},{default:a(()=>e[124]||(e[124]=[m("关闭")])),_:1,__:[124]}),((r=C.value)==null?void 0:r.status)==="completed"?(k(),Q(n,{key:0,onClick:e[31]||(e[31]=q=>et(C.value)),type:"primary"},{default:a(()=>e[125]||(e[125]=[m(" 下载结果 ")])),_:1,__:[125]})):M("",!0)]}),default:a(()=>[C.value?(k(),A("div",ml,[s("div",pl,[e[121]||(e[121]=s("h4",null,"📋 基本信息",-1)),o(Es,{column:2,border:""},{default:a(()=>[o(ie,{label:"任务ID"},{default:a(()=>[m(f(C.value.task_id),1)]),_:1}),o(ie,{label:"文件名"},{default:a(()=>[m(f(C.value.file_name),1)]),_:1}),o(ie,{label:"处理模式"},{default:a(()=>[m(f(Ee(C.value.mode)),1)]),_:1}),o(ie,{label:"状态"},{default:a(()=>[o(l,{type:Ke(C.value.status)},{default:a(()=>[m(f(Xe(C.value.status)),1)]),_:1},8,["type"])]),_:1}),o(ie,{label:"进度"},{default:a(()=>[m(f(C.value.progress)+"%",1)]),_:1}),o(ie,{label:"创建时间"},{default:a(()=>[m(f(ge(C.value.created_time)),1)]),_:1})]),_:1})]),C.value.status==="completed"&&C.value.result?(k(),A("div",gl,[e[122]||(e[122]=s("h4",null,"📄 处理结果",-1)),s("div",_l,[o(Ls,{modelValue:Me.value,"onUpdate:modelValue":e[30]||(e[30]=r=>Me.value=r)},{default:a(()=>[C.value.result.task_type==="meeting_transcription"?(k(),Q(Pe,{key:0,label:"格式化结果",name:"formatted"},{default:a(()=>[s("div",fl,[s("div",vl,[s("pre",hl,f(nt(C.value.result)),1)])])]),_:1})):M("",!0),o(Pe,{label:"文本结果",name:"text"},{default:a(()=>[s("div",yl,[s("pre",null,f($e(C.value.result)),1)])]),_:1}),o(Pe,{label:"JSON数据",name:"json"},{default:a(()=>[s("div",bl,[s("pre",null,f(JSON.stringify(C.value.result,null,2)),1)])]),_:1})]),_:1},8,["modelValue"])])])):M("",!0),C.value.status==="failed"&&C.value.error?(k(),A("div",wl,[e[123]||(e[123]=s("h4",null,"❌ 错误信息",-1)),o(v,{type:"error",closable:!1},{default:a(()=>[s("pre",null,f(C.value.error),1)]),_:1})])):M("",!0)])):M("",!0)]),_:1},8,["modelValue","title"])],512),[[he,oe.value==="results"]])])]),se(s("div",kl,[s("div",xl,[e[126]||(e[126]=s("h3",null,"📊 监控面板",-1)),o(n,{text:"",onClick:e[33]||(e[33]=r=>ee.value=!1),icon:T(bt),size:"small"},null,8,["icon"])]),s("div",$l,[s("div",Sl,[e[130]||(e[130]=s("h4",null,"🖥️ 系统状态",-1)),s("div",Cl,[s("div",Tl,[e[127]||(e[127]=s("div",{class:"status-label"},"CPU使用率",-1)),s("div",Vl,f(V.cpu)+"%",1),o(fe,{percentage:V.cpu,"stroke-width":4,"show-text":!1},null,8,["percentage"])]),s("div",zl,[e[128]||(e[128]=s("div",{class:"status-label"},"内存使用",-1)),s("div",Al,f(V.memory)+"%",1),o(fe,{percentage:V.memory,"stroke-width":4,"show-text":!1},null,8,["percentage"])]),s("div",Ml,[e[129]||(e[129]=s("div",{class:"status-label"},"GPU使用率",-1)),s("div",Rl,f(V.gpu)+"%",1),o(fe,{percentage:V.gpu,"stroke-width":4,"show-text":!1},null,8,["percentage"])])])]),s("div",Dl,[e[131]||(e[131]=s("h4",null,"⚡ 活动任务",-1)),s("div",Ul,[y.value.length===0?(k(),A("div",El,[o(Fs,{description:"暂无活动任务","image-size":60})])):(k(),A("div",Ll,[(k(!0),A(ft,null,vt(y.value,r=>(k(),A("div",{key:r.id,class:"task-item"},[s("div",Ol,[s("span",Fl,f(r.file_name),1),o(l,{type:ss(r.status),size:"small"},{default:a(()=>[m(f(os(r.status)),1)]),_:2},1032,["type"])]),s("div",Bl,[o(fe,{percentage:r.progress||0,status:r.status==="failed"?"exception":r.status==="completed"?"success":"","stroke-width":6,"show-text":!0,format:q=>`${q}%`},null,8,["percentage","status","format"])]),s("div",Pl,[s("span",jl,f(Ee(r.mode)),1),s("span",Nl,f(ge(r.start_time)),1)])]))),128))]))])]),s("div",Wl,[e[136]||(e[136]=s("h4",null,"📋 队列状态",-1)),s("div",ql,[s("div",Il,[e[132]||(e[132]=s("div",{class:"queue-label"},"等待中",-1)),s("div",Jl,f(z.pending),1)]),s("div",Gl,[e[133]||(e[133]=s("div",{class:"queue-label"},"处理中",-1)),s("div",Hl,f(z.processing),1)]),s("div",Zl,[e[134]||(e[134]=s("div",{class:"queue-label"},"已完成",-1)),s("div",Kl,f(z.completed),1)]),s("div",Xl,[e[135]||(e[135]=s("div",{class:"queue-label"},"失败",-1)),s("div",Ql,f(z.failed),1)])])]),s("div",Yl,[e[137]||(e[137]=s("h4",null,"📝 最近日志",-1)),s("div",en,[(k(!0),A(ft,null,vt(j.value,r=>(k(),A("div",{key:r.id,class:ye(["log-item",r.level])},[s("div",tn,f(ls(r.time)),1),s("div",sn,f(r.message),1)],2))),128)),j.value.length===0?(k(),A("div",on," 暂无日志记录 ")):M("",!0)])]),s("div",ln,[e[141]||(e[141]=s("h4",null,"🚀 快速操作",-1)),s("div",nn,[o(n,{onClick:Le,icon:T(We),size:"small",style:{width:"100%","margin-bottom":"8px"}},{default:a(()=>e[138]||(e[138]=[m(" 刷新监控数据 ")])),_:1,__:[138]},8,["icon"]),o(n,{onClick:ns,size:"small",style:{width:"100%","margin-bottom":"8px"}},{default:a(()=>e[139]||(e[139]=[m(" 清理已完成任务 ")])),_:1,__:[139]}),o(n,{onClick:as,size:"small",style:{width:"100%"}},{default:a(()=>e[140]||(e[140]=[m(" 导出日志 ")])),_:1,__:[140]})])])])],512),[[he,ee.value]])],2),e[142]||(e[142]=Ws('<div class="audio-center-footer" data-v-8cf549e8><div class="footer-left" data-v-8cf549e8><span class="status-text" data-v-8cf549e8>就绪</span></div><div class="footer-right" data-v-8cf549e8><span class="version-info" data-v-8cf549e8>v1.0.0</span></div></div>',1))])}}},gn=Bs(rn,[["__scopeId","data-v-8cf549e8"]]);export{gn as default};
