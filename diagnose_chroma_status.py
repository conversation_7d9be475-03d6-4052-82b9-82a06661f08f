#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ChromaDB状态诊断脚本
用于检查向量数据库的连接状态和基础信息
"""

import os
import sys
import chromadb
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List

def check_database_connection():
    """检查数据库连接和基础状态"""
    print("=" * 60)
    print("ChromaDB状态诊断报告")
    print("=" * 60)
    print(f"诊断时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 配置信息
    db_path = "./data/chroma_db"
    collection_name = "knowledge_base"
    
    print("📋 配置信息:")
    print(f"   数据库路径: {os.path.abspath(db_path)}")
    print(f"   集合名称: {collection_name}")
    print()
    
    # 检查目录存在性
    print("📁 目录检查:")
    if not os.path.exists(db_path):
        print("   ❌ 数据库目录不存在")
        return False
    else:
        print("   ✅ 数据库目录存在")
        
        # 列出目录内容
        try:
            files = list(Path(db_path).rglob("*"))
            print(f"   📊 目录包含 {len(files)} 个文件/子目录")
            
            # 显示主要文件
            main_files = [f for f in files if f.is_file() and f.name in ['chroma.sqlite3', 'index']]
            if main_files:
                print("   🗃️ 主要文件:")
                for file in main_files:
                    size = file.stat().st_size
                    print(f"      - {file.name}: {size:,} bytes")
        except Exception as e:
            print(f"   ⚠️ 读取目录内容失败: {e}")
    
    print()
    
    # 尝试连接ChromaDB
    print("🔗 数据库连接测试:")
    try:
        client = chromadb.PersistentClient(path=db_path)
        print("   ✅ ChromaDB客户端连接成功")
        
        # 列出所有集合
        collections = client.list_collections()
        print(f"   📚 找到 {len(collections)} 个集合:")
        
        target_collection = None
        for collection in collections:
            doc_count = collection.count()
            print(f"      - {collection.name}: {doc_count:,} 个文档")
            print(f"        ID: {collection.id}")
            
            if collection.name == collection_name:
                target_collection = collection
        
        if not target_collection:
            print(f"   ❌ 未找到目标集合: {collection_name}")
            return False, None
        else:
            print(f"   ✅ 目标集合存在: {collection_name}")
            return True, target_collection
            
    except Exception as e:
        print(f"   ❌ 数据库连接失败: {e}")
        return False, None

def check_collection_metadata(collection):
    """检查集合元数据"""
    print("\n📊 集合详细信息:")
    try:
        # 基础信息
        doc_count = collection.count()
        print(f"   文档总数: {doc_count:,}")
        print(f"   集合ID: {collection.id}")
        print(f"   集合名称: {collection.name}")
        
        # 元数据
        metadata = collection.metadata
        if metadata:
            print("   元数据:")
            for key, value in metadata.items():
                print(f"      {key}: {value}")
        else:
            print("   元数据: 无")
            
        return doc_count
        
    except Exception as e:
        print(f"   ❌ 获取集合信息失败: {e}")
        return 0

def sample_documents(collection, sample_size=5):
    """抽样检查文档"""
    print(f"\n🔍 文档抽样检查 (样本数: {sample_size}):")
    try:
        # 获取样本文档
        results = collection.peek(limit=sample_size)
        
        if not results or not results.get('ids'):
            print("   ❌ 无法获取文档样本")
            return
            
        print(f"   ✅ 成功获取 {len(results['ids'])} 个样本文档")
        
        # 检查每个样本
        for i, doc_id in enumerate(results['ids']):
            print(f"\n   📄 样本 {i+1}:")
            print(f"      ID: {doc_id}")
            
            # 检查元数据
            if results.get('metadatas') and i < len(results['metadatas']):
                metadata = results['metadatas'][i]
                if metadata:
                    print("      元数据:")
                    for key, value in metadata.items():
                        if isinstance(value, str) and len(value) > 50:
                            print(f"         {key}: {value[:50]}...")
                        else:
                            print(f"         {key}: {value}")
                else:
                    print("      元数据: 无")
            
            # 检查文档内容
            if results.get('documents') and i < len(results['documents']):
                content = results['documents'][i]
                if content:
                    content_preview = content[:100] + "..." if len(content) > 100 else content
                    print(f"      内容预览: {content_preview}")
                    print(f"      内容长度: {len(content)} 字符")
                else:
                    print("      内容: 空")
            
            # 检查向量
            if results.get('embeddings') and i < len(results['embeddings']):
                embedding = results['embeddings'][i]
                if embedding:
                    print(f"      向量维度: {len(embedding)}")
                    print(f"      向量范围: [{min(embedding):.4f}, {max(embedding):.4f}]")
                else:
                    print("      向量: 无")
                    
    except Exception as e:
        print(f"   ❌ 文档抽样失败: {e}")

def main():
    """主函数"""
    try:
        # 检查数据库连接
        success, collection = check_database_connection()
        
        if not success or not collection:
            print("\n❌ 数据库连接失败，无法继续诊断")
            return False
        
        # 检查集合元数据
        doc_count = check_collection_metadata(collection)
        
        # 抽样检查文档
        if doc_count > 0:
            sample_documents(collection)
        else:
            print("\n⚠️ 集合中没有文档，跳过抽样检查")
        
        print("\n" + "=" * 60)
        print("诊断完成")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 诊断过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    main()
