# -*- coding: utf-8 -*-
"""
SenseVoice模型定义文件
用于支持FunASR的remote_code加载机制
"""

# 这个文件主要是为了解决FunASR加载SenseVoice时的remote_code警告
# 实际的模型实现在FunASR框架内部

def get_model_class():
    """返回SenseVoice模型类"""
    try:
        from funasr.models.sense_voice.model import SenseVoiceSmall
        return SenseVoiceSmall
    except ImportError:
        # 如果导入失败，返回None，让FunASR使用默认机制
        return None

# 为了兼容性，也可以直接导出模型类
try:
    from funasr.models.sense_voice.model import SenseVoiceSmall
    Model = SenseVoiceSmall
except ImportError:
    Model = None
