import os
import numpy as np
import soundfile as sf
import tempfile
import torch
from funasr import AutoModel
import re
import traceback
import sys
import time

def generate_test_audio(file_path, duration=3, sr=16000):
    """生成测试用音频文件"""
    try:
        # 生成一个正弦波
        t = np.linspace(0, duration, int(duration * sr), endpoint=False)
        audio = np.sin(2*np.pi*440*t) * 0.5
        
        # 保存为WAV文件
        sf.write(file_path, audio, sr)
        print(f"生成测试音频: {file_path}")
        return file_path
    except Exception as e:
        print(f"生成测试音频失败: {e}")
        traceback.print_exc()
        return None

def extract_tensor_values(tensor_obj):
    """从tensor对象中提取数值"""
    print(f"提取值，tensor类型: {type(tensor_obj)}")
    
    if isinstance(tensor_obj, torch.Tensor):
        # 直接转换PyTorch张量为numpy数组
        try:
            # 检查是否是CUDA tensor
            if hasattr(tensor_obj, 'is_cuda') and tensor_obj.is_cuda:
                print(f"检测到CUDA tensor，先将其移动到CPU")
                tensor_obj = tensor_obj.cpu()
            values = tensor_obj.numpy()
            print(f"直接从tensor转换得到数组，形状: {values.shape}")
            return values
        except TypeError as e:
            print(f"TypeError: {e}")
            try:
                # 再次尝试使用cpu()方法
                print("尝试使用cpu()方法移动tensor")
                values = tensor_obj.cpu().numpy()
                print(f"成功将CUDA tensor转换为numpy数组")
                return values
            except Exception as e2:
                print(f"转换tensor失败: {e2}")
                traceback.print_exc()
    
    # 处理字符串形式的tensor
    tensor_str = str(tensor_obj)
    print(f"tensor字符串前200字符: {tensor_str[:200]}...")
    
    # 使用正则表达式提取所有浮点数
    values = re.findall(r'-?\d+\.\d+e[+-]\d+|-?\d+\.\d+', tensor_str)
    if values:
        print(f"从字符串中提取到{len(values)}个值")
        return np.array([float(v) for v in values])
    
    print("未能提取到任何数值")
    return None

# 测试CUDA tensor处理
def test_cuda_tensor_handling():
    """测试CUDA tensor处理"""
    print("\n===== 测试CUDA tensor处理 =====")
    
    # 检查CUDA是否可用
    if torch.cuda.is_available():
        print("CUDA可用，创建CUDA tensor进行测试")
        # 创建一个CUDA tensor
        try:
            tensor = torch.tensor([1.0, 2.0, 3.0, 4.0, 5.0], device="cuda")
            print(f"创建的CUDA tensor: {tensor}")
            
            # 测试提取函数
            values = extract_tensor_values(tensor)
            
            if values is not None:
                print(f"成功提取值: {values}")
                print("CUDA tensor处理测试通过")
                return True
            else:
                print("无法从CUDA tensor提取值")
                return False
        except Exception as e:
            print(f"CUDA tensor测试失败: {e}")
            traceback.print_exc()
            return False
    else:
        print("CUDA不可用，跳过CUDA tensor测试")
        return True  # 跳过测试视为成功

def check_embedding_quality(values):
    """检查嵌入向量质量"""
    # 检查向量范数
    norm = np.linalg.norm(values)
    print(f"向量范数: {norm:.4f}")
    
    # 检查数值范围
    min_val = np.min(values)
    max_val = np.max(values)
    print(f"数值范围: [{min_val:.4f}, {max_val:.4f}]")
    
    # 检查零值比例
    zero_ratio = np.sum(np.abs(values) < 1e-6) / values.size
    print(f"接近零的值比例: {zero_ratio:.2%}")
    
    return norm > 0.1 and zero_ratio < 0.5

def test_campplus_model():
    """测试CAM++模型的嵌入向量提取"""
    print("\n===== 测试CAM++模型提取嵌入向量 =====")
    
    # 检查是否有GPU可用
    device = "cuda:0" if torch.cuda.is_available() else "cpu"
    print(f"使用设备: {device}")
    
    # 设置本地模型路径
    local_model_path = r"C:\Users\<USER>\Documents\my_project\models\model_dir\speech_campplus_sv_zh"
    
    # 验证模型路径
    if not os.path.exists(local_model_path):
        print(f"❌ 错误：模型路径不存在: {local_model_path}")
        return False
    
    print(f"✅ 模型路径存在: {local_model_path}")
    
    # 创建临时音频文件
    try:
        with tempfile.NamedTemporaryFile(suffix=".wav", delete=False) as tmp:
            audio_file = tmp.name
            print(f"创建临时文件: {audio_file}")
    except Exception as e:
        print(f"创建临时文件失败: {e}")
        traceback.print_exc()
        return False
    
    try:
        # 生成测试音频
        audio_file = generate_test_audio(audio_file)
        if not audio_file:
            return False
        
        # 加载CAM++模型
        print(f"正在加载本地CAM++模型: {local_model_path}")
        try:
            model = AutoModel(
                model=local_model_path,
                device=device,
                disable_update=True,
                local_files_only=True,
                trust_remote_code=True,
                revision="main"  # 添加revision参数
            )
            print("CAM++模型加载成功")
        except Exception as e:
            print(f"加载CAM++模型失败: {e}")
            traceback.print_exc()
            return False
        
        # 添加性能监控
        start_time = time.time()
        res = model.generate(
            input=audio_file,
            extract_embedding=True
        )
        process_time = time.time() - start_time
        print(f"处理时间: {process_time:.3f}秒")
        print(f"RTF: {process_time/3:.3f}")  # 假设音频长度为3秒
        
        # 处理结果
        if res and len(res) > 0:
            print("\n解析结果...")
            
            # 尝试获取embedding或spk_embedding
            if "embedding" in res[0]:
                embedding = res[0]["embedding"]
                print(f"找到embedding键: {type(embedding)}")
                values = extract_tensor_values(embedding)
            elif "spk_embedding" in res[0]:
                embedding = res[0]["spk_embedding"]
                print(f"找到spk_embedding键: {type(embedding)}")
                values = extract_tensor_values(embedding)
            else:
                print(f"未找到嵌入向量键。可用键: {res[0].keys() if isinstance(res[0], dict) else '非字典类型'}")
                return False
                
            if values is not None:
                # 打印抽取的值
                print(f"提取的值形状: {values.shape}")
                print(f"前5个值: {values.flatten()[:5]}")
                
                # L2归一化
                norm = np.linalg.norm(values)
                if norm > 0:
                    normalized = values / norm
                    print(f"归一化后的前5个值: {normalized.flatten()[:5]}")
                return True
            else:
                print("未能提取有效的嵌入向量值")
                return False
        else:
            print("模型未返回有效结果")
            return False
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        traceback.print_exc()
        return False
    finally:
        # 清理临时文件
        try:
            if os.path.exists(audio_file):
                os.unlink(audio_file)
                print(f"已删除临时文件: {audio_file}")
        except Exception as e:
            print(f"删除临时文件失败: {e}")

if __name__ == "__main__":
    print(f"Python版本: {sys.version}")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"NumPy版本: {np.__version__}")
    try:
        import funasr
        print(f"FunASR版本: {funasr.__version__ if hasattr(funasr, '__version__') else '未知'}")
    except Exception as e:
        print(f"导入FunASR失败: {e}")
    
    # 测试CUDA tensor处理
    cuda_test_success = test_cuda_tensor_handling()
    print(f"CUDA tensor处理测试: {'成功' if cuda_test_success else '失败'}")
    
    # 测试CAM++模型
    try:
        model_test_success = test_campplus_model()
        print(f"\n测试结果: {'成功' if model_test_success else '失败'}")
    except Exception as e:
        print(f"测试失败，发生未捕获的异常: {e}")
        traceback.print_exc() 