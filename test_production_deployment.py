#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生产环境部署验证测试
验证方案二：FunASR VAD模型离线配置修复的实际效果
"""

import os
import sys
import time
import json
import requests
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('test_production_deployment.log', encoding='utf-8'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class ProductionDeploymentTester:
    def __init__(self):
        self.base_url = "http://localhost:8002"
        self.test_audio_path = "resource/对话.mp3"  # 测试音频文件
        self.session = requests.Session()
        
    def check_backend_health(self):
        """检查后端服务健康状态"""
        try:
            response = self.session.get(f"{self.base_url}/health")
            if response.status_code == 200:
                logger.info("✅ 后端服务健康检查通过")
                return True
            else:
                logger.error(f"❌ 后端服务健康检查失败: {response.status_code}")
                return False
        except Exception as e:
            logger.error(f"❌ 后端服务连接失败: {e}")
            return False
    
    def upload_test_audio(self):
        """上传测试音频文件"""
        if not Path(self.test_audio_path).exists():
            logger.error(f"❌ 测试音频文件不存在: {self.test_audio_path}")
            return None
            
        try:
            with open(self.test_audio_path, 'rb') as f:
                files = {'file': ('对话.mp3', f, 'audio/mpeg')}
                response = self.session.post(
                    f"{self.base_url}/api/audio/upload",
                    files=files
                )
            
            if response.status_code == 200:
                result = response.json()
                file_id = result.get('file_id')
                logger.info(f"✅ 音频文件上传成功，文件ID: {file_id}")
                return file_id
            else:
                logger.error(f"❌ 音频文件上传失败: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 音频文件上传异常: {e}")
            return None
    
    def start_meeting_transcription(self, file_id):
        """启动会议转录任务"""
        try:
            payload = {
                "file_id": file_id,
                "speaker_count": 2,  # 指定说话人数量
                "enable_speaker_identification": True
            }
            
            response = self.session.post(
                f"{self.base_url}/api/audio/meeting-transcription",
                json=payload
            )
            
            if response.status_code == 200:
                result = response.json()
                task_id = result.get('task_id')
                logger.info(f"✅ 会议转录任务启动成功，任务ID: {task_id}")
                return task_id
            else:
                logger.error(f"❌ 会议转录任务启动失败: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 会议转录任务启动异常: {e}")
            return None
    
    def monitor_task_progress(self, task_id, timeout=300):
        """监控任务进度"""
        start_time = time.time()
        logger.info(f"🔄 开始监控任务进度，任务ID: {task_id}")
        
        while time.time() - start_time < timeout:
            try:
                response = self.session.get(f"{self.base_url}/api/tasks/{task_id}/status")
                
                if response.status_code == 200:
                    result = response.json()
                    status = result.get('status')
                    progress = result.get('progress', 0)
                    
                    logger.info(f"📊 任务状态: {status}, 进度: {progress}%")
                    
                    if status == 'completed':
                        logger.info("✅ 任务完成！")
                        return True, result
                    elif status == 'failed':
                        error_msg = result.get('error', '未知错误')
                        logger.error(f"❌ 任务失败: {error_msg}")
                        return False, result
                    
                    time.sleep(5)  # 每5秒检查一次
                else:
                    logger.warning(f"⚠️ 获取任务状态失败: {response.status_code}")
                    time.sleep(5)
                    
            except Exception as e:
                logger.error(f"❌ 监控任务进度异常: {e}")
                time.sleep(5)
        
        logger.error(f"❌ 任务监控超时 ({timeout}秒)")
        return False, None
    
    def validate_transcription_result(self, task_result):
        """验证转录结果"""
        try:
            # 检查结果结构
            if not task_result or 'result' not in task_result:
                logger.error("❌ 任务结果为空或格式错误")
                return False
            
            result = task_result['result']
            
            # 检查是否有转录文本
            if 'transcription' not in result:
                logger.error("❌ 转录结果中缺少transcription字段")
                return False
            
            transcription = result['transcription']
            
            # 检查是否有说话人信息
            if 'speaker_segments' not in result:
                logger.error("❌ 转录结果中缺少speaker_segments字段")
                return False
            
            speaker_segments = result['speaker_segments']
            
            # 验证基本内容
            if not transcription or len(transcription.strip()) == 0:
                logger.error("❌ 转录文本为空")
                return False
            
            if not speaker_segments or len(speaker_segments) == 0:
                logger.error("❌ 说话人分段为空")
                return False
            
            # 统计说话人数量
            speakers = set()
            for segment in speaker_segments:
                if 'speaker' in segment:
                    speakers.add(segment['speaker'])
            
            logger.info(f"✅ 转录验证通过:")
            logger.info(f"   - 转录文本长度: {len(transcription)} 字符")
            logger.info(f"   - 说话人分段数: {len(speaker_segments)}")
            logger.info(f"   - 识别说话人数: {len(speakers)} ({list(speakers)})")
            logger.info(f"   - 转录文本预览: {transcription[:100]}...")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 验证转录结果异常: {e}")
            return False
    
    def run_full_test(self):
        """运行完整的生产环境测试"""
        logger.info("🚀 开始生产环境部署验证测试")
        logger.info("=" * 60)
        
        # 步骤1: 检查后端健康状态
        logger.info("📋 步骤1: 检查后端服务健康状态")
        if not self.check_backend_health():
            return False
        
        # 步骤2: 上传测试音频
        logger.info("📋 步骤2: 上传测试音频文件")
        file_id = self.upload_test_audio()
        if not file_id:
            return False
        
        # 步骤3: 启动会议转录
        logger.info("📋 步骤3: 启动会议转录任务")
        task_id = self.start_meeting_transcription(file_id)
        if not task_id:
            return False
        
        # 步骤4: 监控任务进度
        logger.info("📋 步骤4: 监控任务执行进度")
        success, task_result = self.monitor_task_progress(task_id)
        if not success:
            return False
        
        # 步骤5: 验证转录结果
        logger.info("📋 步骤5: 验证转录结果质量")
        if not self.validate_transcription_result(task_result):
            return False
        
        logger.info("🎉 生产环境部署验证测试完全成功！")
        logger.info("✅ 方案二修复效果确认：VAD模型完全离线加载")
        return True

def main():
    """主函数"""
    tester = ProductionDeploymentTester()
    
    try:
        success = tester.run_full_test()
        
        if success:
            print("\n" + "=" * 60)
            print("🎉 方案二生产环境部署验证成功！")
            print("✅ FunASR VAD模型离线配置修复完全有效")
            print("✅ 会议转录功能在生产环境中正常工作")
            print("✅ 无网络依赖，完全离线运行")
            print("=" * 60)
            return 0
        else:
            print("\n" + "=" * 60)
            print("❌ 生产环境部署验证失败")
            print("请检查日志文件获取详细错误信息")
            print("=" * 60)
            return 1
            
    except KeyboardInterrupt:
        logger.info("⚠️ 测试被用户中断")
        return 1
    except Exception as e:
        logger.error(f"❌ 测试执行异常: {e}")
        return 1

if __name__ == "__main__":
    exit(main())
