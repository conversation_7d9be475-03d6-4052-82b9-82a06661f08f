#!/usr/bin/env python3
"""
测试集成后的音频处理系统
验证优化FunASR管理器在整个音频处理后端中的集成效果
"""

import os
import sys
import gc
import time
import psutil
import torch
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def monitor_memory(label: str):
    """监控内存使用"""
    memory = psutil.virtual_memory()
    process = psutil.Process()
    process_memory = process.memory_info().rss / 1024**3  # GB
    
    gpu_info = ""
    if torch.cuda.is_available():
        allocated = torch.cuda.memory_allocated() / 1024**3
        reserved = torch.cuda.memory_reserved() / 1024**3
        total = torch.cuda.get_device_properties(0).total_memory / 1024**3
        gpu_info = f", GPU: {allocated:.1f}GB/{reserved:.1f}GB (总计{total:.1f}GB)"
    
    print(f"[{label}] 系统: {memory.percent:.1f}%, 进程: {process_memory:.1f}GB{gpu_info}")
    return process_memory

def test_speech_recognition_core():
    """测试语音识别核心模块"""
    print("\n🎤 测试语音识别核心模块...")
    
    try:
        from backend.utils.audio.speech_recognition_core import SenseVoiceRecognizer, SpeechRecognitionConfig
        
        model_path = "./models/SenseVoiceSmall"
        test_audio = "resource/对话.mp3"
        
        if not os.path.exists(model_path):
            print(f"⚠️ 模型路径不存在: {model_path}")
            return False
        
        if not os.path.exists(test_audio):
            print(f"⚠️ 测试音频不存在: {test_audio}")
            return False
        
        initial_memory = monitor_memory("语音识别核心-开始")
        
        # 创建配置
        config = SpeechRecognitionConfig(
            model_path=model_path,
            device="auto",
            language="auto",
            use_itn=True
        )
        
        # 创建识别器
        recognizer = SenseVoiceRecognizer(config)
        
        # 加载模型
        if not recognizer.load_model():
            print("❌ 模型加载失败")
            return False
        
        load_memory = monitor_memory("语音识别核心-模型加载后")
        
        # 执行识别
        result = recognizer.recognize_audio(test_audio, {})
        
        if result and result.success:
            print(f"✅ 识别成功: {result.text[:100]}...")
            print(f"📊 置信度: {result.confidence:.2f}")
            print(f"⏱️ 处理时间: {result.processing_time:.2f}秒")
        else:
            print("❌ 识别失败")
            return False
        
        inference_memory = monitor_memory("语音识别核心-推理后")
        
        # 清理
        del recognizer
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        final_memory = monitor_memory("语音识别核心-清理后")
        
        print(f"📊 内存变化: {final_memory - initial_memory:.1f}GB")
        return True
        
    except Exception as e:
        print(f"❌ 语音识别核心测试失败: {e}")
        return False

def test_optimized_funasr_manager():
    """测试优化的FunASR管理器"""
    print("\n🚀 测试优化的FunASR管理器...")
    
    try:
        from backend.utils.audio.optimized_funasr_manager import get_funasr_manager, cleanup_funasr_manager
        
        model_path = "./models/SenseVoiceSmall"
        test_audio = "resource/对话.mp3"
        
        initial_memory = monitor_memory("优化管理器-开始")
        
        # 获取管理器
        funasr_manager = get_funasr_manager()
        
        # 加载模型
        if not funasr_manager.load_model(model_path):
            print("❌ 优化管理器模型加载失败")
            return False
        
        load_memory = monitor_memory("优化管理器-模型加载后")
        
        # 执行识别
        result = funasr_manager.generate(test_audio)
        
        if result and len(result) > 0:
            text = result[0].get('text', '')
            print(f"✅ 识别成功: {text[:100]}...")
        else:
            print("❌ 识别失败")
            return False
        
        inference_memory = monitor_memory("优化管理器-推理后")
        
        # 清理
        cleanup_funasr_manager()
        gc.collect()
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        final_memory = monitor_memory("优化管理器-清理后")
        
        print(f"📊 内存变化: {final_memory - initial_memory:.1f}GB")
        return True
        
    except Exception as e:
        print(f"❌ 优化FunASR管理器测试失败: {e}")
        return False

def test_audio_processing_tasks():
    """测试音频处理任务"""
    print("\n📋 测试音频处理任务...")
    
    try:
        from backend.tasks.audio_processing_tasks import _perform_optimized_speech_recognition
        
        model_path = "./models/SenseVoiceSmall"
        test_audio = "resource/对话.mp3"
        
        config = {
            'language': 'auto',
            'use_itn': True,
            'batch_size_s': 60
        }
        
        def mock_progress_callback(progress, message, stage):
            print(f"  进度: {progress:.0f}% - {message}")
        
        initial_memory = monitor_memory("音频任务-开始")
        
        # 执行优化的语音识别
        result = _perform_optimized_speech_recognition(
            test_audio, model_path, config,
            mock_progress_callback, 0, 100
        )
        
        if result and result.get('status') == 'success':
            print(f"✅ 任务执行成功")
            recognition_result = result.get('result', [])
            if recognition_result and len(recognition_result) > 0:
                text = recognition_result[0].get('text', '')
                print(f"📝 识别结果: {text[:100]}...")
        else:
            print("❌ 任务执行失败")
            return False
        
        final_memory = monitor_memory("音频任务-完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 音频处理任务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_memory_stability():
    """测试内存稳定性"""
    print("\n🔄 测试内存稳定性...")
    
    try:
        from backend.utils.audio.optimized_funasr_manager import get_funasr_manager, cleanup_funasr_manager
        
        model_path = "./models/SenseVoiceSmall"
        test_audio = "resource/对话.mp3"
        
        baseline_memory = monitor_memory("稳定性测试-基线")
        
        # 执行5次识别循环
        for i in range(5):
            print(f"\n--- 第{i+1}次识别 ---")
            
            # 获取管理器并加载模型
            funasr_manager = get_funasr_manager()
            funasr_manager.load_model(model_path)
            
            load_memory = monitor_memory(f"第{i+1}次-加载后")
            
            # 执行识别
            result = funasr_manager.generate(test_audio)
            if result:
                print(f"  识别成功，结果长度: {len(str(result))}")
            
            inference_memory = monitor_memory(f"第{i+1}次-推理后")
            
            # 清理
            cleanup_funasr_manager()
            gc.collect()
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            
            cleanup_memory = monitor_memory(f"第{i+1}次-清理后")
            
            # 检查内存增长
            memory_growth = cleanup_memory - baseline_memory
            if memory_growth > 0.3:  # 超过300MB
                print(f"⚠️ 检测到内存增长: {memory_growth:.1f}GB")
            
            time.sleep(1)  # 短暂等待
        
        # 最终检查
        final_memory = monitor_memory("稳定性测试-最终")
        total_growth = final_memory - baseline_memory
        
        print(f"\n📊 稳定性测试结果:")
        print(f"  总内存增长: {total_growth:.1f}GB")
        
        if total_growth < 0.1:  # 小于100MB
            print("🎉 内存管理优秀！")
            return True
        elif total_growth < 0.3:  # 小于300MB
            print("✅ 内存管理良好")
            return True
        else:
            print("⚠️ 内存管理需要改进")
            return False
        
    except Exception as e:
        print(f"❌ 稳定性测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 集成音频处理系统测试")
    print("验证优化FunASR管理器在整个后端系统中的集成效果")
    print("=" * 70)
    
    # 系统信息
    print(f"🖥️ 系统信息:")
    print(f"  Python: {sys.version.split()[0]}")
    print(f"  PyTorch: {torch.__version__}")
    
    if torch.cuda.is_available():
        print(f"  GPU: {torch.cuda.get_device_name(0)}")
        print(f"  CUDA: {torch.version.cuda}")
        total_memory = torch.cuda.get_device_properties(0).total_memory / 1024**3
        print(f"  GPU显存: {total_memory:.1f}GB")
    else:
        print("  GPU: 不可用")
    
    # 初始内存状态
    initial_memory = monitor_memory("系统初始状态")
    
    # 测试结果
    test_results = {}
    
    # 测试1: 语音识别核心模块
    test_results['speech_recognition_core'] = test_speech_recognition_core()
    
    # 等待内存稳定
    time.sleep(3)
    gc.collect()
    
    # 测试2: 优化的FunASR管理器
    test_results['optimized_funasr_manager'] = test_optimized_funasr_manager()
    
    # 等待内存稳定
    time.sleep(3)
    gc.collect()
    
    # 测试3: 音频处理任务
    test_results['audio_processing_tasks'] = test_audio_processing_tasks()
    
    # 等待内存稳定
    time.sleep(3)
    gc.collect()
    
    # 测试4: 内存稳定性
    test_results['memory_stability'] = test_memory_stability()
    
    # 最终状态
    print("\n" + "=" * 70)
    final_memory = monitor_memory("测试完成")
    
    total_growth = final_memory - initial_memory
    print(f"\n📋 集成测试总结:")
    print(f"  总内存增长: {total_growth:.1f}GB")
    
    # 测试结果统计
    passed_tests = sum(test_results.values())
    total_tests = len(test_results)
    
    print(f"\n🧪 测试结果:")
    for test_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
    
    print(f"\n📊 总体结果: {passed_tests}/{total_tests} 测试通过")
    
    if passed_tests == total_tests and total_growth < 0.5:
        print("🎉 集成测试全部通过，内存优化效果优秀！")
    elif passed_tests >= total_tests * 0.8:
        print("✅ 集成测试基本通过，系统运行良好")
    else:
        print("⚠️ 部分测试失败，需要进一步优化")

if __name__ == "__main__":
    main()
