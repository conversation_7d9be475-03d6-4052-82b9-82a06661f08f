#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
.dockerignore 配置验证脚本
验证Docker构建所需的文件不会被意外排除
"""

import os
import sys
import subprocess
from pathlib import Path

def log_info(message):
    print(f"[INFO] {message}")

def log_success(message):
    print(f"[SUCCESS] ✅ {message}")

def log_warning(message):
    print(f"[WARNING] ⚠️ {message}")

def log_error(message):
    print(f"[ERROR] ❌ {message}")

def check_required_files():
    """检查Docker构建必需的文件是否存在"""
    log_info("检查Docker构建必需的文件...")
    
    required_files = [
        ".venv/",
        "frontend/",
        "backend/",
        "config/",
        "utils/",
        "requirements.txt",
        "my_notebook_docker/nginx.conf",
        "my_notebook_docker/Dockerfile.frontend",
        "my_notebook_docker/Dockerfile.backend",
        "my_notebook_docker/Dockerfile.celery"
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
            log_error(f"缺少必需文件: {file_path}")
        else:
            log_success(f"找到必需文件: {file_path}")
    
    return len(missing_files) == 0

def simulate_docker_context():
    """模拟Docker构建上下文，检查哪些文件会被包含"""
    log_info("模拟Docker构建上下文...")
    
    try:
        # 使用docker build --dry-run来检查构建上下文
        # 注意：这需要Docker 20.10+版本
        result = subprocess.run([
            'docker', 'build', '--dry-run', '--progress=plain', 
            '-f', 'my_notebook_docker/Dockerfile.frontend', '.'
        ], capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            log_success("Docker构建上下文验证通过")
            return True
        else:
            log_warning("Docker构建上下文验证失败，但这可能是正常的")
            log_info("错误信息:")
            print(result.stderr)
            return True  # 不阻塞验证
            
    except subprocess.TimeoutExpired:
        log_warning("Docker构建验证超时")
        return True
    except FileNotFoundError:
        log_warning("Docker命令未找到，跳过构建上下文验证")
        return True
    except Exception as e:
        log_warning(f"Docker构建验证异常: {e}")
        return True

def check_dockerignore_syntax():
    """检查.dockerignore文件语法"""
    log_info("检查.dockerignore文件语法...")
    
    dockerignore_path = ".dockerignore"
    
    if not os.path.exists(dockerignore_path):
        log_error(".dockerignore文件不存在")
        return False
    
    try:
        with open(dockerignore_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        log_success(f".dockerignore文件读取成功，共{len(lines)}行")
        
        # 检查关键排除项
        content = ''.join(lines)
        
        # 检查是否正确排除了不需要的文件
        good_exclusions = [
            '__pycache__',
            '*.log',
            '.git',
            'test/',
            '*.md'
        ]
        
        for exclusion in good_exclusions:
            if exclusion in content:
                log_success(f"正确排除: {exclusion}")
            else:
                log_warning(f"可能遗漏排除: {exclusion}")
        
        # 检查是否错误排除了必需文件
        critical_inclusions = [
            '.venv/',
            'frontend/',
            'backend/',
            'config/',
            'utils/'
        ]
        
        for inclusion in critical_inclusions:
            # 检查是否被排除（不应该被排除）
            if f"\n{inclusion}" in content and not f"# {inclusion}" in content:
                log_error(f"错误排除了必需目录: {inclusion}")
                return False
            else:
                log_success(f"必需目录未被排除: {inclusion}")
        
        return True
        
    except Exception as e:
        log_error(f"读取.dockerignore文件失败: {e}")
        return False

def check_dockerfile_copy_commands():
    """检查Dockerfile中的COPY命令是否与.dockerignore兼容"""
    log_info("检查Dockerfile COPY命令兼容性...")
    
    dockerfiles = [
        "my_notebook_docker/Dockerfile.frontend",
        "my_notebook_docker/Dockerfile.backend", 
        "my_notebook_docker/Dockerfile.celery"
    ]
    
    for dockerfile in dockerfiles:
        if not os.path.exists(dockerfile):
            log_error(f"Dockerfile不存在: {dockerfile}")
            continue
            
        try:
            with open(dockerfile, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取COPY命令
            copy_commands = []
            for line in content.split('\n'):
                line = line.strip()
                if line.startswith('COPY '):
                    copy_commands.append(line)
            
            log_info(f"{dockerfile} 中的COPY命令:")
            for cmd in copy_commands:
                print(f"  {cmd}")
                
                # 检查关键的COPY命令
                if '.venv' in cmd:
                    log_success("找到.venv复制命令")
                elif 'frontend/' in cmd:
                    log_success("找到frontend复制命令")
                elif 'backend/' in cmd:
                    log_success("找到backend复制命令")
                elif 'config/' in cmd:
                    log_success("找到config复制命令")
                elif 'utils/' in cmd:
                    log_success("找到utils复制命令")
                elif 'nginx.conf' in cmd:
                    log_success("找到nginx.conf复制命令")
            
        except Exception as e:
            log_error(f"读取{dockerfile}失败: {e}")
    
    return True

def main():
    """主验证流程"""
    print("🔍 .dockerignore 配置验证")
    print("=" * 50)
    
    tests = [
        ("检查必需文件", check_required_files),
        ("检查.dockerignore语法", check_dockerignore_syntax),
        ("检查Dockerfile兼容性", check_dockerfile_copy_commands),
        ("模拟Docker构建上下文", simulate_docker_context)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            results[test_name] = test_func()
        except Exception as e:
            log_error(f"测试 {test_name} 异常: {e}")
            results[test_name] = False
    
    # 显示总结
    print(f"\n{'='*50}")
    print(f"📊 验证总结:")
    
    passed = sum(1 for v in results.values() if v)
    total = len(results)
    
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print(f"\n🎉 .dockerignore配置验证通过！")
        print(f"✅ Docker构建应该能正常工作")
        return True
    else:
        print(f"\n❌ .dockerignore配置存在问题")
        print(f"请检查失败的测试项目")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
