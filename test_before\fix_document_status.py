#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复文档状态脚本
将有节点但状态为uploaded的文档状态更新为completed
"""

from backend.core.database import get_db_session
from backend.models.document_management import ManagedDocument
from backend.services.document_db_service import document_db_service
from datetime import datetime, timezone

def fix_document_status():
    """修复文档状态"""
    print("=" * 80)
    print("文档状态修复脚本")
    print("=" * 80)
    
    db = get_db_session()
    try:
        # 查找状态为uploaded但有节点的文档
        docs = db.query(ManagedDocument).filter(
            ManagedDocument.status == 'uploaded',
            ManagedDocument.sections_count > 0
        ).all()
        
        print(f"找到 {len(docs)} 个需要修复的文档:")
        
        for doc in docs:
            print(f"\n文档ID: {doc.id}")
            print(f"  文件名: {doc.filename[:50]}...")
            print(f"  当前状态: {doc.status}")
            print(f"  节点数: {doc.sections_count}")
            print(f"  创建时间: {doc.created_at}")
            
            # 更新文档状态
            update_data = {
                "status": "completed",
                "processing_progress": 1.0,
                "processed_at": datetime.now(timezone.utc)
            }
            
            try:
                document_db_service.update_document(db, doc.id, doc.user_id, update_data)
                print(f"  ✅ 状态已更新为 completed")
                
                # 添加处理日志
                document_db_service.add_processing_log(
                    db, doc.id, "INFO",
                    f"状态修复：从 uploaded 更新为 completed（节点数: {doc.sections_count}）",
                    "status_fix"
                )
                print(f"  ✅ 已添加处理日志")
                
            except Exception as e:
                print(f"  ❌ 更新失败: {e}")
        
        # 提交所有更改
        db.commit()
        print(f"\n✅ 成功修复 {len(docs)} 个文档的状态")
        
    except Exception as e:
        db.rollback()
        print(f"❌ 修复失败: {e}")
    finally:
        db.close()

def verify_fix():
    """验证修复结果"""
    print("\n" + "=" * 80)
    print("验证修复结果")
    print("=" * 80)
    
    db = get_db_session()
    try:
        # 检查还有多少uploaded状态的文档有节点
        uploaded_with_sections = db.query(ManagedDocument).filter(
            ManagedDocument.status == 'uploaded',
            ManagedDocument.sections_count > 0
        ).count()
        
        # 检查completed状态的文档数量
        completed_docs = db.query(ManagedDocument).filter(
            ManagedDocument.status == 'completed'
        ).count()
        
        print(f"剩余需要修复的文档: {uploaded_with_sections}")
        print(f"已完成状态的文档: {completed_docs}")
        
        if uploaded_with_sections == 0:
            print("✅ 所有文档状态已修复")
        else:
            print("⚠️ 还有文档需要修复")
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
    finally:
        db.close()

def main():
    """主函数"""
    fix_document_status()
    verify_fix()

if __name__ == "__main__":
    main()
