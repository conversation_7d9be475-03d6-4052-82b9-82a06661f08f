#!/usr/bin/env python3
"""
测试会议转录API
"""

import requests
import json
import time
import os


def test_meeting_transcription_api():
    """测试会议转录API"""
    
    print("🧪 开始测试会议转录API")
    print("=" * 80)
    
    base_url = "http://localhost:8002"
    
    # 1. 登录获取token
    print("1. 登录获取token...")
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        login_response = requests.post(f"{base_url}/api/v1/auth/login", json=login_data)
        if login_response.status_code != 200:
            print(f"❌ 登录失败: {login_response.status_code} - {login_response.text}")
            return False
        
        token_data = login_response.json()
        token = token_data.get("access_token")
        if not token:
            print(f"❌ 未获取到token: {token_data}")
            return False
        
        print(f"✅ 登录成功，获取到token")
        
        headers = {"Authorization": f"Bearer {token}"}
        
    except Exception as e:
        print(f"❌ 登录请求失败: {e}")
        return False
    
    # 2. 上传音频文件
    print("\n2. 上传音频文件...")
    audio_file_path = "resource/对话.mp3"
    
    if not os.path.exists(audio_file_path):
        print(f"❌ 音频文件不存在: {audio_file_path}")
        return False
    
    try:
        with open(audio_file_path, 'rb') as f:
            files = {'file': ('对话.mp3', f, 'audio/mpeg')}
            upload_response = requests.post(
                f"{base_url}/api/v1/audio/upload",
                files=files,
                headers=headers
            )
        
        if upload_response.status_code != 200:
            print(f"❌ 文件上传失败: {upload_response.status_code} - {upload_response.text}")
            return False
        
        upload_data = upload_response.json()
        file_id = upload_data.get("file_id")
        if not file_id:
            print(f"❌ 未获取到file_id: {upload_data}")
            return False
        
        print(f"✅ 文件上传成功，file_id: {file_id}")
        
    except Exception as e:
        print(f"❌ 文件上传失败: {e}")
        return False
    
    # 3. 提交会议转录任务
    print("\n3. 提交会议转录任务...")
    task_data = {
        "mode": "meeting-transcription",
        "files": [file_id],
        "config": {
            "language": "auto",
            "output_format": "text",
            "include_timestamps": True,
            "speaker_labeling": True
        }
    }

    try:
        task_response = requests.post(
            f"{base_url}/api/v1/audio/tasks",
            json=task_data,
            headers=headers
        )
        
        if task_response.status_code != 200:
            print(f"❌ 任务提交失败: {task_response.status_code} - {task_response.text}")
            return False
        
        task_result = task_response.json()
        task_id = task_result.get("task_id")
        if not task_id:
            print(f"❌ 未获取到task_id: {task_result}")
            return False
        
        print(f"✅ 任务提交成功，task_id: {task_id}")
        
    except Exception as e:
        print(f"❌ 任务提交失败: {e}")
        return False
    
    # 4. 等待任务完成并获取结果
    print("\n4. 等待任务完成...")
    max_wait_time = 120  # 最大等待2分钟
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        try:
            status_response = requests.get(
                f"{base_url}/api/v1/audio/task-status/{task_id}",
                headers=headers
            )
            
            if status_response.status_code != 200:
                print(f"❌ 获取任务状态失败: {status_response.status_code}")
                time.sleep(2)
                continue
            
            status_data = status_response.json()
            task_status = status_data.get("status")
            progress = status_data.get("progress", 0)
            
            print(f"📊 任务状态: {task_status}, 进度: {progress}%")
            
            if task_status == "SUCCESS":
                print("✅ 任务完成！")
                break
            elif task_status == "FAILURE":
                print(f"❌ 任务失败: {status_data}")
                return False
            
            time.sleep(3)
            
        except Exception as e:
            print(f"❌ 获取任务状态失败: {e}")
            time.sleep(2)
    else:
        print("❌ 任务等待超时")
        return False
    
    # 5. 获取转录结果
    print("\n5. 获取转录结果...")
    try:
        result_response = requests.get(
            f"{base_url}/api/v1/audio/task-result/{task_id}",
            headers=headers
        )
        
        if result_response.status_code != 200:
            print(f"❌ 获取结果失败: {result_response.status_code} - {result_response.text}")
            return False
        
        result_data = result_response.json()
        print("\n" + "=" * 80)
        print("📊 转录结果:")
        print(json.dumps(result_data, indent=2, ensure_ascii=False))
        
        # 6. 检查文本清理效果
        print("\n6. 检查文本清理效果...")
        
        if result_data and result_data.get('status') == 'success':
            results = result_data.get('results', [])
            if results:
                first_result = results[0]
                transcription = first_result.get('result', {})
                
                # 检查主要文本
                main_text = transcription.get('text', '')
                print(f"\n📝 主要文本: {main_text}")
                
                # 检查是否包含技术标记
                technical_markers = ['<|zh|>', '<|SAD|>', '<|Speech|>', '<|withitn|>', '<|EMO_UNKNOWN|>', '<|NEUTRAL|>', '<|BGM|>']
                has_markers = any(marker in main_text for marker in technical_markers)
                
                if has_markers:
                    print("❌ 主要文本仍包含技术标记")
                    for marker in technical_markers:
                        if marker in main_text:
                            print(f"  - 发现标记: {marker}")
                    return False
                else:
                    print("✅ 主要文本已清理技术标记")
                
                # 检查语音片段
                speech_segments = transcription.get('speech_segments', [])
                print(f"\n🎤 语音片段数量: {len(speech_segments)}")
                
                segments_with_markers = 0
                for i, segment in enumerate(speech_segments):
                    segment_text = segment.get('text', '')
                    segment_has_markers = any(marker in segment_text for marker in technical_markers)
                    
                    if segment_has_markers:
                        segments_with_markers += 1
                        print(f"❌ 片段 {i+1} 包含技术标记: {segment_text}")
                    else:
                        print(f"✅ 片段 {i+1} 已清理: {segment_text}")
                
                if segments_with_markers == 0:
                    print("🎉 所有语音片段都已清理技术标记！")
                    return True
                else:
                    print(f"❌ {segments_with_markers} 个片段仍包含技术标记")
                    return False
            else:
                print("❌ 没有转录结果")
                return False
        else:
            print(f"❌ 转录失败: {result_data}")
            return False
        
    except Exception as e:
        print(f"❌ 获取结果失败: {e}")
        return False

if __name__ == "__main__":
    success = test_meeting_transcription_api()
    if success:
        print("\n🎉 会议转录API测试通过！")
    else:
        print("\n❌ 会议转录API测试失败！")
