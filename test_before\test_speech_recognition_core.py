#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SenseVoice语音识别核心模块测试脚本
任务#5 - 语音识别核心功能测试

测试内容：
1. 🎯 SenseVoice模型加载测试
2. 🔄 单个音频识别测试
3. 📋 批量音频识别测试
4. 📏 长音频分块处理测试
5. 🌐 多语言识别测试
6. 💭 情感和事件检测测试
7. 📊 性能统计测试

作者: AI Assistant
创建时间: 2025-01-27
任务关联: 任务#5 子任务5.1
"""

import os
import sys
import time
import json
import traceback
from pathlib import Path
import numpy as np
import soundfile as sf
import logging

# 添加项目路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入我们的语音识别核心模块
try:
    from utils.speech_recognition_core import (
        SpeechRecognitionConfig,
        RecognitionResult,
        SenseVoiceRecognizer,
        SpeechRecognitionManager,
        create_speech_recognizer,
        quick_recognize,
        get_language_display_name,
        get_emotion_display_name,
        get_event_display_name
    )
    print("✅ 成功导入语音识别核心模块")
except ImportError as e:
    print(f"❌ 导入语音识别核心模块失败: {e}")
    sys.exit(1)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def print_status(message, status="INFO"):
    """打印状态信息"""
    symbols = {
        "SUCCESS": "✅",
        "ERROR": "❌", 
        "WARNING": "⚠️",
        "INFO": "ℹ️",
        "TESTING": "🧪"
    }
    symbol = symbols.get(status, "ℹ️")
    print(f"{symbol} {message}")

def load_config():
    """加载测试配置"""
    print_status("加载测试配置...", "TESTING")
    
    # 默认配置
    config = {
        "model_path": r"C:\Users\<USER>\Documents\my_project\models\model_dir\SenseVoiceSmall",
        "device": "cpu",  # 测试用CPU
        "test_audio_dir": "data/test_audio",
        "create_test_audio": True
    }
    
    # 尝试从配置文件加载
    config_file = "speech_config.ini"
    if os.path.exists(config_file):
        try:
            import configparser
            cp = configparser.ConfigParser()
            cp.read(config_file, encoding='utf-8')
            
            if 'models' in cp:
                config["model_path"] = cp.get('models', 'sensevoice_path', fallback=config["model_path"])
            
            print_status(f"从配置文件加载: {config_file}", "SUCCESS")
        except Exception as e:
            print_status(f"读取配置文件失败: {e}", "WARNING")
    
    return config

def create_test_audio_files(test_dir):
    """创建测试音频文件"""
    print_status("创建测试音频文件...", "TESTING")
    
    os.makedirs(test_dir, exist_ok=True)
    
    # 生成不同长度的测试音频
    test_files = []
    
    try:
        # 短音频 (3秒)
        duration = 3.0
        sample_rate = 16000
        t = np.linspace(0, duration, int(sample_rate * duration))
        # 创建简单的正弦波 + 噪声
        audio = 0.3 * np.sin(2 * np.pi * 440 * t) + 0.1 * np.random.randn(len(t))
        
        short_file = os.path.join(test_dir, "test_short_3s.wav")
        sf.write(short_file, audio, sample_rate)
        test_files.append(("短音频", short_file, duration))
        
        # 中等音频 (15秒)
        duration = 15.0
        t = np.linspace(0, duration, int(sample_rate * duration))
        # 创建变频音频
        audio = 0.3 * np.sin(2 * np.pi * (440 + 100 * np.sin(0.5 * t)) * t) + 0.1 * np.random.randn(len(t))
        
        medium_file = os.path.join(test_dir, "test_medium_15s.wav")
        sf.write(medium_file, audio, sample_rate)
        test_files.append(("中等音频", medium_file, duration))
        
        # 长音频 (40秒)
        duration = 40.0
        t = np.linspace(0, duration, int(sample_rate * duration))
        # 创建复杂音频模拟语音
        audio = np.zeros(len(t))
        for i in range(0, int(duration), 2):
            start_idx = int(i * sample_rate)
            end_idx = min(int((i + 1) * sample_rate), len(t))
            if start_idx < len(t):
                segment = t[start_idx:end_idx]
                freq = 440 + (i * 50) % 300
                audio[start_idx:end_idx] = 0.3 * np.sin(2 * np.pi * freq * segment)
        
        # 添加噪声
        audio += 0.05 * np.random.randn(len(t))
        
        long_file = os.path.join(test_dir, "test_long_40s.wav")
        sf.write(long_file, audio, sample_rate)
        test_files.append(("长音频", long_file, duration))
        
        print_status(f"创建了{len(test_files)}个测试音频文件", "SUCCESS")
        
        for name, path, dur in test_files:
            print(f"  - {name}: {os.path.basename(path)} ({dur}s)")
        
        return test_files
        
    except Exception as e:
        print_status(f"创建测试音频失败: {e}", "ERROR")
        return []

def test_config_creation():
    """测试配置类创建"""
    print_status("测试配置类创建...", "TESTING")
    
    try:
        # 创建默认配置
        config = SpeechRecognitionConfig()
        print_status("默认配置创建成功", "SUCCESS")
        print(f"  - 设备: {config.device}")
        print(f"  - 语言: {config.language}")
        print(f"  - 批处理大小: {config.batch_size_s}s")
        
        # 创建自定义配置
        custom_config = SpeechRecognitionConfig(
            model_path="/test/path",
            device="cpu",
            language="zh",
            use_itn=True,
            max_workers=2
        )
        print_status("自定义配置创建成功", "SUCCESS")
        
        # 测试配置序列化
        config_dict = custom_config.to_dict()
        restored_config = SpeechRecognitionConfig.from_dict(config_dict)
        print_status("配置序列化/反序列化成功", "SUCCESS")
        
        return True
        
    except Exception as e:
        print_status(f"配置测试失败: {e}", "ERROR")
        return False

def test_model_loading(model_path):
    """测试模型加载"""
    print_status("测试SenseVoice模型加载...", "TESTING")
    
    try:
        # 检查模型路径
        if not os.path.exists(model_path):
            print_status(f"模型路径不存在: {model_path}", "WARNING")
            print_status("跳过模型加载测试", "WARNING")
            return False
        
        # 创建配置
        config = SpeechRecognitionConfig(
            model_path=model_path,
            device="cpu",  # 测试使用CPU
            language="auto"
        )
        
        # 创建识别器
        recognizer = SenseVoiceRecognizer(config)
        print_status("SenseVoice识别器创建成功", "SUCCESS")
        
        # 加载模型
        start_time = time.time()
        success = recognizer.load_model()
        load_time = time.time() - start_time
        
        if success:
            print_status(f"模型加载成功 (耗时: {load_time:.2f}s)", "SUCCESS")
            print(f"  - 模型路径: {model_path}")
            print(f"  - 设备: {recognizer._determine_device()}")
            print(f"  - 模型已加载: {recognizer.model_loaded}")
            return recognizer
        else:
            print_status("模型加载失败", "ERROR")
            return None
            
    except Exception as e:
        print_status(f"模型加载测试异常: {e}", "ERROR")
        print_status(f"详细错误: {traceback.format_exc()}", "ERROR")
        return None

def test_single_audio_recognition(recognizer, test_files):
    """测试单个音频识别"""
    if not recognizer or not test_files:
        print_status("跳过单个音频识别测试 - 缺少必要条件", "WARNING")
        return
    
    print_status("测试单个音频识别...", "TESTING")
    
    for name, audio_path, duration in test_files:
        try:
            print(f"\n测试{name}: {os.path.basename(audio_path)}")
            
            # 执行识别
            start_time = time.time()
            result = recognizer.recognize_audio(audio_path)
            total_time = time.time() - start_time
            
            # 输出结果
            if result.success:
                print_status(f"{name}识别成功", "SUCCESS")
                print(f"  - 识别文本: {result.text[:100]}...")
                print(f"  - 音频时长: {result.duration:.2f}s")
                print(f"  - 处理时间: {result.processing_time:.2f}s")
                print(f"  - 推理时间: {result.inference_time:.2f}s") 
                print(f"  - 实时倍率: {result.processing_time/result.duration:.2f}x")
                print(f"  - 置信度: {result.confidence:.3f}")
                print(f"  - 语言: {get_language_display_name(result.language)}")
                
                if result.emotions:
                    emotions_display = [get_emotion_display_name(e) for e in result.emotions]
                    print(f"  - 情感: {emotions_display}")
                
                if result.events:
                    events_display = [get_event_display_name(e) for e in result.events]
                    print(f"  - 事件: {events_display}")
                    
            else:
                print_status(f"{name}识别失败: {result.error}", "ERROR")
                
        except Exception as e:
            print_status(f"{name}识别异常: {e}", "ERROR")

def test_batch_recognition(recognizer, test_files):
    """测试批量识别"""
    if not recognizer or len(test_files) < 2:
        print_status("跳过批量识别测试 - 缺少必要条件", "WARNING")
        return
    
    print_status("测试批量音频识别...", "TESTING")
    
    try:
        # 准备批量音频路径
        audio_paths = [audio_path for _, audio_path, _ in test_files]
        
        print(f"批量处理{len(audio_paths)}个音频文件")
        
        # 执行批量识别
        start_time = time.time()
        results = recognizer.recognize_batch(audio_paths, max_workers=2)
        total_time = time.time() - start_time
        
        # 统计结果
        successful = sum(1 for r in results if r.success)
        failed = len(results) - successful
        total_duration = sum(r.duration for r in results if r.success)
        total_processing = sum(r.processing_time for r in results)
        
        print_status(f"批量识别完成", "SUCCESS")
        print(f"  - 总文件数: {len(results)}")
        print(f"  - 成功: {successful}, 失败: {failed}")
        print(f"  - 总音频时长: {total_duration:.2f}s")
        print(f"  - 总处理时间: {total_processing:.2f}s")
        print(f"  - 平均实时倍率: {total_processing/total_duration:.2f}x")
        
        # 输出每个结果的简要信息
        for i, (result, (name, _, _)) in enumerate(zip(results, test_files)):
            if result.success:
                print(f"  {i+1}. {name}: {len(result.text)}字符, {result.duration:.1f}s")
            else:
                print(f"  {i+1}. {name}: 失败 - {result.error}")
                
    except Exception as e:
        print_status(f"批量识别测试异常: {e}", "ERROR")

def test_long_audio_processing(recognizer, test_files):
    """测试长音频处理"""
    if not recognizer:
        print_status("跳过长音频处理测试 - 缺少识别器", "WARNING") 
        return
    
    # 找到最长的音频文件
    long_file = None
    for name, audio_path, duration in test_files:
        if duration > 30:  # 超过30秒认为是长音频
            long_file = (name, audio_path, duration)
            break
    
    if not long_file:
        print_status("跳过长音频处理测试 - 没有长音频文件", "WARNING")
        return
    
    print_status("测试长音频分块处理...", "TESTING")
    
    try:
        name, audio_path, duration = long_file
        print(f"处理长音频: {name} ({duration}s)")
        
        # 执行长音频识别
        start_time = time.time()
        result = recognizer.recognize_long_audio(
            audio_path,
            chunk_size=10.0,  # 10秒分块
            overlap=1.0  # 1秒重叠
        )
        total_time = time.time() - start_time
        
        if result.success:
            print_status("长音频处理成功", "SUCCESS")
            print(f"  - 音频时长: {result.duration:.2f}s")
            print(f"  - 处理时间: {result.processing_time:.2f}s")
            print(f"  - 推理时间: {result.inference_time:.2f}s")
            print(f"  - 实时倍率: {result.processing_time/result.duration:.2f}x")
            print(f"  - 识别文本长度: {len(result.text)}字符")
            print(f"  - 分段数量: {len(result.segments)}")
            print(f"  - 检测情感: {result.emotions}")
            print(f"  - 检测事件: {result.events}")
        else:
            print_status(f"长音频处理失败: {result.error}", "ERROR")
            
    except Exception as e:
        print_status(f"长音频处理测试异常: {e}", "ERROR")

def test_manager_interface(model_path, test_files):
    """测试管理器接口"""
    print_status("测试语音识别管理器接口...", "TESTING")
    
    if not os.path.exists(model_path):
        print_status("跳过管理器接口测试 - 模型路径不存在", "WARNING")
        return
    
    try:
        # 创建管理器
        manager = SpeechRecognitionManager()
        
        # 初始化
        success = manager.initialize(
            model_path=model_path,
            device="cpu",
            language="auto",
            max_workers=2
        )
        
        if success:
            print_status("管理器初始化成功", "SUCCESS")
            
            # 测试单个文件识别
            if test_files:
                _, audio_path, _ = test_files[0]
                result = manager.recognize(audio_path)
                
                if result.success:
                    print_status("管理器单文件识别成功", "SUCCESS")
                    print(f"  - 文本: {result.text[:50]}...")
                else:
                    print_status(f"管理器单文件识别失败: {result.error}", "ERROR")
            
            # 测试批量识别
            if len(test_files) >= 2:
                audio_paths = [audio_path for _, audio_path, _ in test_files[:2]]
                results = manager.recognize(audio_paths)
                
                successful = sum(1 for r in results if r.success)
                print_status(f"管理器批量识别: {successful}/{len(results)}成功", "SUCCESS")
            
            # 获取统计信息
            stats = manager.get_statistics()
            print_status("获取统计信息成功", "SUCCESS")
            print(f"  - 总处理数: {stats['total_processed']}")
            print(f"  - 成功率: {stats['success_rate']:.2%}")
            print(f"  - 检测语言: {stats['languages_detected']}")
            
        else:
            print_status("管理器初始化失败", "ERROR")
            
    except Exception as e:
        print_status(f"管理器接口测试异常: {e}", "ERROR")

def test_convenience_functions(model_path, test_files):
    """测试便捷函数"""
    print_status("测试便捷函数...", "TESTING")
    
    if not os.path.exists(model_path) or not test_files:
        print_status("跳过便捷函数测试 - 缺少必要条件", "WARNING")
        return
    
    try:
        # 测试 create_speech_recognizer
        recognizer = create_speech_recognizer(model_path, device="cpu")
        
        if recognizer:
            print_status("create_speech_recognizer 成功", "SUCCESS")
            
            # 测试 quick_recognize
            _, audio_path, _ = test_files[0]
            result = quick_recognize(audio_path, model_path, device="cpu")
            
            if result.success:
                print_status("quick_recognize 成功", "SUCCESS")
                print(f"  - 快速识别文本: {result.text[:50]}...")
            else:
                print_status(f"quick_recognize 失败: {result.error}", "ERROR")
        else:
            print_status("create_speech_recognizer 失败", "ERROR")
            
    except Exception as e:
        print_status(f"便捷函数测试异常: {e}", "ERROR")

def test_multilingual_support():
    """测试多语言支持功能"""
    print_status("测试多语言支持...", "TESTING")
    
    try:
        # 测试语言映射
        test_languages = ['zh', 'en', 'yue', 'ja', 'ko', 'auto']
        for lang in test_languages:
            display_name = get_language_display_name(lang)
            print(f"  - {lang} -> {display_name}")
        
        # 测试情感映射
        test_emotions = ['HAPPY', 'SAD', 'ANGRY', 'NEUTRAL']
        for emotion in test_emotions:
            display_name = get_emotion_display_name(emotion)
            print(f"  - {emotion} -> {display_name}")
        
        # 测试事件映射
        test_events = ['Speech', 'Music', 'Applause', 'Laughter']
        for event in test_events:
            display_name = get_event_display_name(event)
            print(f"  - {event} -> {display_name}")
        
        print_status("多语言支持测试成功", "SUCCESS")
        
    except Exception as e:
        print_status(f"多语言支持测试异常: {e}", "ERROR")

def main():
    """主测试函数"""
    print("="*80)
    print("🎯 SenseVoice语音识别核心模块测试")
    print("任务#5 - 语音识别核心功能")
    print("子任务5.1 - 集成SenseVoiceSmall模型")
    print("="*80)
    
    # 加载配置
    config = load_config()
    model_path = config["model_path"]
    test_audio_dir = config["test_audio_dir"]
    
    # 1. 测试配置类
    print("\n" + "="*60)
    print("🧪 测试1: 配置类功能")
    print("="*60)
    test_config_creation()
    
    # 2. 创建测试音频
    print("\n" + "="*60)
    print("🧪 测试2: 创建测试音频")
    print("="*60)
    test_files = []
    if config.get("create_test_audio", True):
        test_files = create_test_audio_files(test_audio_dir)
    
    # 3. 测试模型加载
    print("\n" + "="*60)
    print("🧪 测试3: SenseVoice模型加载")
    print("="*60)
    recognizer = test_model_loading(model_path)
    
    # 4. 测试单个音频识别
    print("\n" + "="*60)
    print("🧪 测试4: 单个音频识别")
    print("="*60)
    test_single_audio_recognition(recognizer, test_files)
    
    # 5. 测试批量识别
    print("\n" + "="*60)
    print("🧪 测试5: 批量音频识别")
    print("="*60)
    test_batch_recognition(recognizer, test_files)
    
    # 6. 测试长音频处理
    print("\n" + "="*60)
    print("🧪 测试6: 长音频分块处理")
    print("="*60)
    test_long_audio_processing(recognizer, test_files)
    
    # 7. 测试管理器接口
    print("\n" + "="*60)
    print("🧪 测试7: 管理器接口")
    print("="*60)
    test_manager_interface(model_path, test_files)
    
    # 8. 测试便捷函数
    print("\n" + "="*60)
    print("🧪 测试8: 便捷函数")
    print("="*60)
    test_convenience_functions(model_path, test_files)
    
    # 9. 测试多语言支持
    print("\n" + "="*60)
    print("🧪 测试9: 多语言支持")
    print("="*60)
    test_multilingual_support()
    
    # 最终统计
    print("\n" + "="*80)
    print("🎉 SenseVoice语音识别核心模块测试完成")
    print("="*80)
    
    if recognizer:
        stats = recognizer.get_stats()
        print("\n📊 最终统计信息:")
        print(f"  - 总处理文件数: {stats['total_processed']}")
        print(f"  - 成功识别数: {stats['successful_recognitions']}")
        print(f"  - 失败识别数: {stats['failed_recognitions']}")
        print(f"  - 成功率: {stats['success_rate']:.2%}")
        print(f"  - 总音频时长: {stats['total_duration']:.2f}s")
        print(f"  - 总处理时间: {stats['total_processing_time']:.2f}s")
        print(f"  - 平均实时倍率: {stats['real_time_factor']:.2f}x")
        print(f"  - 检测语言: {stats['languages_detected']}")
        print(f"  - 检测情感: {stats['emotions_detected']}")
        print(f"  - 检测事件: {stats['events_detected']}")
    
    print("\n✅ 子任务5.1 - 集成SenseVoiceSmall模型 - 测试完成")

if __name__ == "__main__":
    main()