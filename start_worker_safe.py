#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全的Celery Worker启动脚本
防止系统级崩溃，包含完整的错误处理和资源管理
"""

import os
import sys
import time
import signal
import logging
import subprocess
import psutil
from pathlib import Path

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('worker_safe.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SafeWorkerManager:
    """安全的Worker管理器"""
    
    def __init__(self):
        self.worker_process = None
        self.should_stop = False
        self.restart_count = 0
        self.max_restarts = 5
        
    def setup_environment(self):
        """设置安全的环境变量"""
        try:
            # CUDA内存管理 - 在进程启动前设置
            os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:256,garbage_collection_threshold:0.6'
            os.environ['CUDA_LAUNCH_BLOCKING'] = '1'  # 同步CUDA操作，便于调试
            
            # Python内存管理
            os.environ['PYTHONUNBUFFERED'] = '1'
            os.environ['MALLOC_ARENA_MAX'] = '2'  # 限制内存分配器
            
            # 禁用一些可能导致冲突的功能
            os.environ['TOKENIZERS_PARALLELISM'] = 'false'
            os.environ['OMP_NUM_THREADS'] = '2'  # 限制OpenMP线程数
            
            logger.info("✅ 环境变量设置完成")
            
        except Exception as e:
            logger.error(f"❌ 环境设置失败: {e}")
            raise
    
    def check_system_resources(self):
        """检查系统资源"""
        try:
            # 检查内存
            memory = psutil.virtual_memory()
            if memory.percent > 85:
                logger.warning(f"⚠️ 系统内存使用率过高: {memory.percent}%")
                return False
            
            # 检查磁盘空间
            disk = psutil.disk_usage('.')
            if disk.percent > 90:
                logger.warning(f"⚠️ 磁盘空间不足: {disk.percent}%")
                return False
            
            # 检查GPU状态
            try:
                import torch
                if torch.cuda.is_available():
                    for i in range(torch.cuda.device_count()):
                        memory_allocated = torch.cuda.memory_allocated(i)
                        memory_reserved = torch.cuda.memory_reserved(i)
                        logger.info(f"GPU {i}: 已分配 {memory_allocated/1024**2:.1f}MB, 已保留 {memory_reserved/1024**2:.1f}MB")
            except Exception as gpu_error:
                logger.warning(f"GPU检查失败: {gpu_error}")
            
            logger.info("✅ 系统资源检查通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 系统资源检查失败: {e}")
            return False
    
    def start_worker(self):
        """启动Worker进程"""
        try:
            if not self.check_system_resources():
                logger.error("❌ 系统资源不足，无法启动Worker")
                return False
            
            # 构建启动命令
            cmd = [
                sys.executable, '-m', 'celery', 'worker',
                '-A', 'backend.core.task_queue:celery_app',
                '--loglevel=info',
                '--pool=threads',
                '--concurrency=2',  # 降低并发数
                '--max-tasks-per-child=5',  # 更频繁的重启
                '--queues=audio_processing,default',
                '--hostname=audio_worker@%h',
                '--without-gossip',  # 禁用gossip协议
                '--without-mingle',  # 禁用mingle
                '--without-heartbeat',  # 禁用心跳（减少网络开销）
            ]
            
            logger.info(f"🚀 启动Worker命令: {' '.join(cmd)}")
            
            # 启动进程
            self.worker_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                universal_newlines=True,
                bufsize=1
            )
            
            logger.info(f"✅ Worker进程已启动，PID: {self.worker_process.pid}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Worker启动失败: {e}")
            return False
    
    def monitor_worker(self):
        """监控Worker进程"""
        try:
            while not self.should_stop:
                if self.worker_process is None:
                    break
                
                # 检查进程状态
                poll_result = self.worker_process.poll()
                if poll_result is not None:
                    logger.warning(f"⚠️ Worker进程已退出，退出码: {poll_result}")
                    
                    if self.restart_count < self.max_restarts:
                        logger.info(f"🔄 尝试重启Worker ({self.restart_count + 1}/{self.max_restarts})")
                        self.restart_count += 1
                        time.sleep(5)  # 等待5秒后重启
                        
                        if self.start_worker():
                            continue
                        else:
                            logger.error("❌ Worker重启失败")
                            break
                    else:
                        logger.error(f"❌ Worker重启次数已达上限 ({self.max_restarts})")
                        break
                
                # 读取输出
                try:
                    line = self.worker_process.stdout.readline()
                    if line:
                        print(line.strip())
                        
                        # 检查错误模式
                        if any(error in line.lower() for error in [
                            'cuda out of memory', 'segmentation fault', 'access violation',
                            'fatal error', 'core dumped'
                        ]):
                            logger.error(f"🚨 检测到严重错误: {line.strip()}")
                            self.stop_worker()
                            break
                            
                except Exception as read_error:
                    logger.warning(f"读取输出失败: {read_error}")
                
                time.sleep(0.1)  # 短暂休眠
                
        except KeyboardInterrupt:
            logger.info("📝 收到中断信号，正在停止Worker...")
            self.stop_worker()
        except Exception as e:
            logger.error(f"❌ Worker监控失败: {e}")
            self.stop_worker()
    
    def stop_worker(self):
        """停止Worker进程"""
        try:
            self.should_stop = True
            
            if self.worker_process:
                logger.info("🛑 正在停止Worker进程...")
                
                # 发送SIGTERM信号
                self.worker_process.terminate()
                
                # 等待进程结束
                try:
                    self.worker_process.wait(timeout=10)
                    logger.info("✅ Worker进程已正常停止")
                except subprocess.TimeoutExpired:
                    logger.warning("⚠️ Worker进程未在10秒内停止，强制终止...")
                    self.worker_process.kill()
                    self.worker_process.wait()
                    logger.info("✅ Worker进程已强制终止")
                
                self.worker_process = None
            
            # 清理GPU资源
            self.cleanup_gpu_resources()
            
        except Exception as e:
            logger.error(f"❌ 停止Worker失败: {e}")
    
    def cleanup_gpu_resources(self):
        """清理GPU资源"""
        try:
            import torch
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
                torch.cuda.synchronize()
                logger.info("✅ GPU资源清理完成")
        except Exception as e:
            logger.warning(f"GPU清理失败: {e}")
    
    def run(self):
        """运行Worker管理器"""
        try:
            logger.info("🎯 启动安全Worker管理器")
            
            # 设置信号处理
            signal.signal(signal.SIGINT, lambda s, f: self.stop_worker())
            signal.signal(signal.SIGTERM, lambda s, f: self.stop_worker())
            
            # 设置环境
            self.setup_environment()
            
            # 启动Worker
            if self.start_worker():
                self.monitor_worker()
            else:
                logger.error("❌ Worker启动失败")
                return 1
            
            return 0
            
        except Exception as e:
            logger.error(f"❌ Worker管理器运行失败: {e}")
            return 1
        finally:
            self.stop_worker()


if __name__ == "__main__":
    manager = SafeWorkerManager()
    exit_code = manager.run()
    sys.exit(exit_code)
