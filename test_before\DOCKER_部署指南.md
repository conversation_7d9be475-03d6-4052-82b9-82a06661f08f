# Docker 容器化部署指南

本指南详细介绍如何将 RAG 知识库系统容器化部署。

## 📋 部署方式概览

### 🚀 快速部署（推荐）

使用自动化脚本一键部署：

**标准版本:**
```bash
# Linux/macOS
chmod +x scripts/build.sh
./scripts/build.sh

# Windows
scripts\build.bat
```

**阿里云镜像源优化版本（推荐国内用户）:**
```bash
# Windows
scripts\build-aliyun.bat

# 或使用Docker Compose
docker-compose -f docker-compose.aliyun.yml up -d
```

### 🛠 手动部署

适合需要自定义配置的场景。

---

## 🔧 环境要求

- Docker 20.10+
- Docker Compose 2.0+
- 可用内存: 4GB+
- 硬盘空间: 10GB+

---

## 📦 手动构建步骤

### 1. 构建Docker镜像

```bash
# 构建镜像
docker build -t my-notebook-rag:latest .

# 查看构建的镜像
docker images | grep my-notebook-rag
```

### 2. 运行容器

#### 方式1: 使用Docker命令

```bash
docker run -d \
  --name my-notebook-app \
  -p 8501:8501 \
  -v $(pwd)/chroma_db:/app/chroma_db \
  -v $(pwd)/data:/app/data \
  -v $(pwd)/knowledge_base:/app/knowledge_base \
  -e OLLAMA_BASE_URL=http://host.docker.internal:11434 \
  --restart unless-stopped \
  my-notebook-rag:latest
```

#### 方式2: 使用Docker Compose（推荐）

```bash
# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

---

## 🔍 容器管理命令

### 基本操作

```bash
# 查看运行状态
docker ps

# 查看日志
docker logs my-notebook-app

# 进入容器
docker exec -it my-notebook-app bash

# 停止容器
docker stop my-notebook-app

# 启动容器
docker start my-notebook-app

# 重启容器
docker restart my-notebook-app

# 删除容器
docker rm my-notebook-app
```

### 数据管理

```bash
# 备份数据
docker cp my-notebook-app:/app/chroma_db ./backup/chroma_db_$(date +%Y%m%d_%H%M%S)

# 恢复数据
docker cp ./backup/chroma_db my-notebook-app:/app/

# 查看容器内文件
docker exec my-notebook-app ls -la /app/
```

---

## ⚙️ 配置说明

### 环境变量

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `PYTHONPATH` | `/app` | Python模块搜索路径 |
| `STREAMLIT_SERVER_PORT` | `8501` | Streamlit服务端口 |
| `STREAMLIT_SERVER_ADDRESS` | `0.0.0.0` | 服务绑定地址 |
| `OLLAMA_BASE_URL` | `http://host.docker.internal:11434` | Ollama服务地址 |

### 卷挂载

| 容器路径 | 宿主路径 | 说明 |
|----------|----------|------|
| `/app/chroma_db` | `./chroma_db` | 向量数据库存储 |
| `/app/data` | `./data` | 文档数据存储 |
| `/app/knowledge_base` | `./knowledge_base` | 知识库存储 |

### 端口映射

| 容器端口 | 宿主端口 | 说明 |
|----------|----------|------|
| `8501` | `8501` | Streamlit Web界面 |

---

## 🔗 与外部服务集成

### 连接外部Ollama服务

如果您的Ollama运行在宿主机上：

```bash
# 在docker run命令中添加
-e OLLAMA_BASE_URL=http://host.docker.internal:11434
```

### 运行容器化Ollama

如果需要在容器中运行Ollama，取消注释`docker-compose.yml`中的ollama服务：

```yaml
# 取消注释这部分
ollama:
  image: ollama/ollama:latest
  container_name: ollama-server
  ports:
    - "11434:11434"
  volumes:
    - ollama_data:/root/.ollama
  restart: unless-stopped
  networks:
    - notebook-network
```

然后运行：
```bash
docker-compose up -d
```

---

## 🇨🇳 阿里云镜像源优化

### 🌟 阿里云优化特性

为国内用户专门优化，提供更快的构建速度：

- **🚀 阿里云容器镜像仓库**: 使用阿里云的Python基础镜像
- **📦 阿里云APT源**: 系统包从阿里云镜像下载
- **🐍 阿里云PyPI源**: Python包从阿里云PyPI镜像安装
- **⚡ 分批安装**: 大型包分批安装避免超时
- **🛡️ 网络优化**: 增加超时设置和重试机制

### 📁 阿里云相关文件

- `Dockerfile.aliyun` - 阿里云优化的Dockerfile
- `scripts/build-aliyun.bat` - 阿里云构建脚本
- `docker-compose.aliyun.yml` - 阿里云Docker Compose配置

### 🎯 构建性能对比

| 镜像源 | 基础镜像下载 | Python包安装 | 总构建时间 | 推荐用户 |
|--------|-------------|--------------|------------|----------|
| Docker Hub | 2-5分钟 | 5-10分钟 | 10-15分钟 | 海外用户 |
| 阿里云镜像 | 30秒-1分钟 | 2-3分钟 | 3-5分钟 | 国内用户 |

### 🔄 切换到阿里云版本

如果您已经构建了标准版本，可以轻松切换：

```bash
# 构建阿里云优化版本
scripts\build-aliyun.bat

# 访问地址（注意端口不同）
# 标准版本: http://localhost:8501
# 阿里云版本: http://localhost:8502
```

---

## 🔍 依赖包检查

在构建Docker镜像前，可以先检查本地依赖：

```bash
# 运行依赖检查脚本
python scripts/check_dependencies.py
```

### 📦 完整依赖列表

项目包含以下主要依赖包：

#### 核心框架
- `streamlit` - Web应用框架
- `requests` - HTTP请求库
- `numpy` - 数值计算库
- `pandas` - 数据处理库
- `Pillow` - 图像处理库

#### 文档处理
- `PyPDF2` - PDF处理库 ✅ 新增
- `python-docx` - Word文档处理
- `python-pptx` - PowerPoint处理
- `openpyxl` - Excel处理
- `PyMuPDF` - 高级PDF处理

#### LlamaIndex组件
- `llama-index` - 核心库
- `llama-index-embeddings-ollama` - Ollama嵌入 ✅ 新增
- `llama-index-embeddings-huggingface` - HuggingFace嵌入
- `llama-index-llms-ollama` - Ollama LLM
- `llama-index-vector-stores-chroma` - ChromaDB存储 ✅ 新增

#### 其他组件
- `chromadb` - 向量数据库
- `sentence-transformers` - 句子嵌入
- `langchain` - LangChain框架 ✅ 新增
- `langchain-core` - LangChain核心 ✅ 新增

---

## 🐛 故障排除

### 常见问题

#### 1. 容器启动失败

```bash
# 查看详细错误信息
docker logs my-notebook-app

# 检查端口占用
netstat -tlnp | grep 8501
lsof -i :8501
```

#### 2. 无法连接Ollama

```bash
# 检查Ollama服务状态
curl http://localhost:11434/api/tags

# 在容器内测试连接
docker exec my-notebook-app curl http://host.docker.internal:11434/api/tags
```

#### 3. 数据持久化问题

```bash
# 检查卷挂载
docker inspect my-notebook-app | grep -A 10 "Mounts"

# 检查权限
ls -la chroma_db/
```

#### 4. 内存不足

```bash
# 监控容器资源使用
docker stats my-notebook-app

# 限制容器内存使用
docker run --memory="4g" ...
```

### 日志分析

```bash
# 实时查看日志
docker logs -f my-notebook-app

# 查看最近100行日志
docker logs --tail 100 my-notebook-app

# 查看特定时间的日志
docker logs --since="2024-01-01T00:00:00" my-notebook-app
```

---

## 🔄 更新和维护

### 更新应用

```bash
# 1. 停止当前容器
docker stop my-notebook-app
docker rm my-notebook-app

# 2. 重新构建镜像
docker build -t my-notebook-rag:latest .

# 3. 启动新容器
./scripts/build.sh
```

### 清理资源

```bash
# 清理未使用的镜像
docker image prune

# 清理所有未使用的资源
docker system prune

# 强制清理（包括卷）
docker system prune -a --volumes
```

---

## 🔒 安全建议

1. **网络安全**
   - 仅暴露必要端口
   - 使用防火墙限制访问
   - 定期更新基础镜像

2. **数据安全**
   - 定期备份数据卷
   - 使用加密存储
   - 限制容器权限

3. **访问控制**
   - 配置身份验证
   - 使用HTTPS
   - 监控访问日志

---

## 📈 性能优化

1. **资源分配**
```bash
# 设置内存和CPU限制
docker run --memory="4g" --cpus="2.0" ...
```

2. **网络优化**
```bash
# 使用host网络模式（仅Linux）
docker run --network host ...
```

3. **存储优化**
```bash
# 使用SSD存储卷
# 启用Docker BuildKit
export DOCKER_BUILDKIT=1
```

---

## 📞 获取帮助

如果遇到问题，请：

1. 查看日志: `docker logs my-notebook-app`
2. 检查配置: `docker inspect my-notebook-app`
3. 验证网络: `docker network ls`
4. 提交Issue附带详细信息

---

## 🎉 部署完成

部署成功后，您可以：

- 访问应用: http://localhost:8501
- 上传文档构建知识库
- 进行智能问答交互
- 监控系统状态

祝您使用愉快！🚀 