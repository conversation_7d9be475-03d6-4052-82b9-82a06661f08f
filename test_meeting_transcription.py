#!/usr/bin/env python3
"""测试会议转录API"""

import requests
import json
import time

def test_meeting_transcription():
    # API配置
    base_url = "http://localhost:8002"
    audio_file_id = 5  # 最新的对话.mp3文件
    
    # 测试会议转录
    print("开始测试会议转录...")
    
    # 构建请求数据
    request_data = {
        "audio_file_id": audio_file_id,
        "processing_mode": "meeting_transcription",
        "output_format": "json",
        "include_timestamps": True,
        "speaker_labeling": True,
        "speaker_count": 2  # 指定2个说话人
    }
    
    print(f"请求数据: {json.dumps(request_data, indent=2, ensure_ascii=False)}")
    
    # 发送请求
    try:
        response = requests.post(
            f"{base_url}/api/v1/speech/process",
            json=request_data,
            headers={"Content-Type": "application/json"},
            timeout=300  # 5分钟超时
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print("会议转录成功!")
            print(f"响应数据: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            # 检查关键字段
            if 'speech_segments' in result:
                segments = result['speech_segments']
                print(f"\n语音片段数量: {len(segments)}")
                
                # 检查前几个片段的文本内容
                for i, segment in enumerate(segments[:5]):
                    text = segment.get('text', '')
                    speaker = segment.get('speaker_label', '')
                    start_time = segment.get('start_time', 0)
                    end_time = segment.get('end_time', 0)
                    print(f"片段 {i+1}: [{start_time:.2f}-{end_time:.2f}] {speaker}: '{text}'")
                    
                # 统计空文本片段
                empty_text_count = sum(1 for seg in segments if not seg.get('text', '').strip())
                print(f"\n空文本片段数量: {empty_text_count}/{len(segments)}")
                
                if empty_text_count > 0:
                    print("⚠️ 发现空文本片段，这是我们要解决的问题！")
                else:
                    print("✅ 所有片段都有文本内容")
            
            if 'speaker_count' in result:
                print(f"识别的说话人数量: {result['speaker_count']}")
                
        else:
            print(f"请求失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            
    except requests.exceptions.RequestException as e:
        print(f"请求异常: {e}")
    except Exception as e:
        print(f"其他错误: {e}")

if __name__ == "__main__":
    test_meeting_transcription()
