#!/usr/bin/env python3
"""
检查最新的会议转录任务详情
"""

import sqlite3
import json
from datetime import datetime, timed<PERSON><PERSON>

def check_latest_meeting_task():
    """检查最新的会议转录任务"""
    db_path = "data/speech_platform.db"
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查找最新的会议转录任务
        cursor.execute("""
            SELECT
                tr.id,
                tr.task_id,
                tr.task_type,
                tr.status,
                tr.created_at,
                tr.updated_at,
                tr.result_data,
                af.filename,
                af.file_path,
                af.file_size
            FROM task_records tr
            LEFT JOIN audio_files af ON tr.file_id = af.id
            WHERE tr.task_type = 'meeting_transcription'
            ORDER BY tr.created_at DESC
            LIMIT 5
        """)
        
        tasks = cursor.fetchall()
        
        if not tasks:
            print("没有找到会议转录任务")
            return
            
        print(f"找到 {len(tasks)} 个最新的会议转录任务:")
        print("=" * 80)
        
        for i, task in enumerate(tasks, 1):
            (task_record_id, task_id, task_type, status,
             created_at, updated_at, result_data, filename, file_path, file_size) = task

            print(f"\n任务 {i}:")
            print(f"  记录ID: {task_record_id}")
            print(f"  任务ID: {task_id}")
            print(f"  任务类型: {task_type}")
            print(f"  状态: {status}")
            print(f"  创建时间: {created_at}")
            print(f"  更新时间: {updated_at}")
            print(f"  文件名: {filename}")
            print(f"  文件路径: {file_path}")
            print(f"  文件大小: {file_size}")
            
            # 解析结果数据
            if result_data:
                try:
                    result = json.loads(result_data)
                    print(f"  结果数据:")
                    print(f"    文本: {result.get('text', 'N/A')[:100]}...")
                    print(f"    语言: {result.get('language', 'N/A')}")
                    print(f"    置信度: {result.get('confidence', 'N/A')}")
                    
                    # VAD段落信息
                    vad_segments = result.get('vad_segments', [])
                    print(f"    VAD段落数: {len(vad_segments)}")
                    if vad_segments:
                        print(f"    第一个段落: {vad_segments[0]}")
                    
                    # 说话人段落信息
                    speaker_segments = result.get('speaker_segments', [])
                    print(f"    说话人段落数: {len(speaker_segments)}")
                    if speaker_segments:
                        print(f"    第一个说话人段落: {speaker_segments[0]}")
                        
                    # 说话人统计
                    speaker_stats = result.get('speaker_stats', {})
                    print(f"    说话人统计: {speaker_stats}")
                    
                except json.JSONDecodeError as e:
                    print(f"    结果数据解析失败: {e}")
                    print(f"    原始数据: {result_data[:200]}...")
            else:
                print(f"  结果数据: 无")
            
            print("-" * 60)
        
        # 检查任务进度日志
        print("\n检查最新任务的进度日志:")
        latest_task_id = tasks[0][1]
        cursor.execute("""
            SELECT message, created_at
            FROM task_progress_logs
            WHERE task_id = ?
            ORDER BY created_at DESC
            LIMIT 10
        """, (latest_task_id,))

        logs = cursor.fetchall()
        for log in logs:
            message, created_at = log
            print(f"  {created_at}: {message}")
            
    except Exception as e:
        print(f"检查任务时出错: {e}")
    finally:
        if 'conn' in locals():
            conn.close()

if __name__ == "__main__":
    check_latest_meeting_task()
