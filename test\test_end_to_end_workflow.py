#!/usr/bin/env python3
"""
端到端用户流程测试
验证完整的用户操作流程：登录→上传音频→配置参数→开始处理→监控进度→查看结果→下载输出
"""

import sys
import os
import time
import tempfile
import requests
import json
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# 测试配置
BACKEND_URL = "http://localhost:8002"
FRONTEND_URL = "http://localhost:3000"
API_BASE = f"{BACKEND_URL}/api/v1"

# 测试用户信息
TEST_USER = {
    "username": "admin",
    "password": "admin123"
}

class EndToEndWorkflowTest:
    """端到端工作流程测试类"""
    
    def __init__(self):
        self.session = requests.Session()
        self.access_token = None
        self.uploaded_files = []
        self.created_tasks = []
        self.test_audio_file = None
    
    def setup(self):
        """测试初始化"""
        print("🔧 初始化端到端测试...")
        
        # 创建测试音频文件
        self.create_test_audio()
        
        # 设置请求头
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
        
        print("✅ 测试初始化完成")
    
    def cleanup(self):
        """测试清理"""
        print("\n🧹 清理测试数据...")
        
        # 清理上传的文件
        for file_id in self.uploaded_files:
            try:
                response = self.session.delete(f"{API_BASE}/audio/{file_id}")
                if response.status_code == 200:
                    print(f"✅ 清理文件: {file_id}")
            except Exception as e:
                print(f"⚠️ 清理文件失败: {e}")
        
        # 清理创建的任务
        for task_id in self.created_tasks:
            try:
                response = self.session.delete(f"{API_BASE}/speech/task/{task_id}")
                if response.status_code == 200:
                    print(f"✅ 清理任务: {task_id}")
            except Exception as e:
                print(f"⚠️ 清理任务失败: {e}")
        
        # 删除测试音频文件
        if self.test_audio_file and os.path.exists(self.test_audio_file):
            os.unlink(self.test_audio_file)
            print("✅ 删除测试音频文件")
    
    def create_test_audio(self):
        """创建测试音频文件"""
        try:
            import numpy as np
            import soundfile as sf
            
            # 创建2秒的测试音频
            sample_rate = 16000
            duration = 2.0
            t = np.linspace(0, duration, int(sample_rate * duration))
            
            # 生成包含语音特征的复合信号
            signal = (
                0.3 * np.sin(2 * np.pi * 440 * t) +  # 基频
                0.2 * np.sin(2 * np.pi * 880 * t) +  # 二次谐波
                0.1 * np.sin(2 * np.pi * 1320 * t) + # 三次谐波
                0.05 * np.random.normal(0, 0.1, len(t))  # 噪声
            )
            
            # 添加包络以模拟语音
            envelope = np.exp(-0.5 * (t - 1)**2)
            signal = signal * envelope
            
            # 保存为临时文件
            temp_file = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
            sf.write(temp_file.name, signal, sample_rate)
            self.test_audio_file = temp_file.name
            
            print(f"✅ 创建测试音频文件: {self.test_audio_file}")
            
        except ImportError:
            # 如果没有音频库，创建简单的WAV文件
            temp_file = tempfile.NamedTemporaryFile(suffix='.wav', delete=False)
            
            # 写入最小的WAV文件头和数据
            wav_header = b'RIFF\x24\x08\x00\x00WAVEfmt \x10\x00\x00\x00\x01\x00\x01\x00\x80>\x00\x00\x00}\x00\x00\x02\x00\x10\x00data\x00\x08\x00\x00'
            temp_file.write(wav_header)
            temp_file.write(b'\x00' * 8192)  # 添加音频数据
            temp_file.close()
            
            self.test_audio_file = temp_file.name
            print(f"✅ 创建模拟音频文件: {self.test_audio_file}")
    
    def step1_user_login(self):
        """步骤1: 用户登录"""
        print("\n📝 步骤1: 用户登录")
        
        try:
            response = self.session.post(
                f"{API_BASE}/auth/login",
                json=TEST_USER,
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                self.access_token = data.get('access_token')
                
                # 更新请求头
                self.session.headers.update({
                    'Authorization': f'Bearer {self.access_token}'
                })
                
                print(f"✅ 登录成功，获取访问令牌")
                return True
            else:
                print(f"❌ 登录失败: {response.status_code}")
                print(f"   响应: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 登录异常: {e}")
            return False
    
    def step2_upload_audio(self):
        """步骤2: 上传音频文件"""
        print("\n📤 步骤2: 上传音频文件")
        
        try:
            # 准备文件上传
            with open(self.test_audio_file, 'rb') as f:
                file_content = f.read()

            files = {'file': ('test_audio.wav', file_content, 'audio/wav')}

            # 准备请求头，保留Authorization但移除Content-Type
            headers = {'Authorization': self.session.headers.get('Authorization')}

            response = requests.post(
                f"{API_BASE}/audio/upload",
                files=files,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                file_id = data.get('file_id')
                self.uploaded_files.append(file_id)
                
                print(f"✅ 音频上传成功: {file_id}")
                print(f"   文件大小: {data.get('file_size', 0)} 字节")
                print(f"   音频时长: {data.get('duration', 0):.2f} 秒")
                return file_id
            else:
                print(f"❌ 音频上传失败: {response.status_code}")
                print(f"   响应: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 音频上传异常: {e}")
            return None
    
    def step3_configure_processing(self, file_id):
        """步骤3: 配置处理参数并启动任务"""
        print("\n⚙️ 步骤3: 配置处理参数并启动任务")
        
        if not file_id:
            print("❌ 没有有效的文件ID")
            return None
        
        try:
            # 配置语音识别任务
            config = {
                "file_ids": [file_id],
                "language": "auto",
                "use_itn": True,
                "ban_emo_unk": False,
                "config": {
                    "merge_vad": True,
                    "merge_length_s": 15.0,
                    "batch_size_s": 60
                }
            }
            
            response = self.session.post(
                f"{API_BASE}/speech/speech-recognition",
                json=config,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                task_id = data.get('task_id')
                self.created_tasks.append(task_id)
                
                print(f"✅ 语音识别任务创建成功: {task_id}")
                print(f"   预估处理时间: {data.get('estimated_time', 0)} 秒")
                return task_id
            else:
                print(f"❌ 任务创建失败: {response.status_code}")
                print(f"   响应: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ 任务创建异常: {e}")
            return None
    
    def step4_monitor_progress(self, task_id):
        """步骤4: 监控处理进度"""
        print("\n📊 步骤4: 监控处理进度")
        
        if not task_id:
            print("❌ 没有有效的任务ID")
            return False
        
        try:
            max_attempts = 10
            attempt = 0
            
            while attempt < max_attempts:
                response = self.session.get(
                    f"{API_BASE}/speech/task/{task_id}",
                    timeout=10
                )
                
                if response.status_code == 200:
                    data = response.json()
                    status = data.get('status', 'unknown')
                    
                    print(f"📈 任务状态: {status}")
                    
                    if status in ['SUCCESS', 'FAILURE', 'REVOKED']:
                        print(f"✅ 任务完成，最终状态: {status}")
                        return status == 'SUCCESS'
                    
                    # 等待一段时间再检查
                    time.sleep(2)
                    attempt += 1
                    
                else:
                    print(f"❌ 状态查询失败: {response.status_code}")
                    return False
            
            print("⏰ 监控超时，任务可能仍在处理中")
            return True  # 不算失败，可能是长时间任务
            
        except Exception as e:
            print(f"❌ 进度监控异常: {e}")
            return False
    
    def step5_get_results(self, task_id):
        """步骤5: 获取处理结果"""
        print("\n📋 步骤5: 获取处理结果")
        
        if not task_id:
            print("❌ 没有有效的任务ID")
            return False
        
        try:
            response = self.session.get(
                f"{API_BASE}/speech/task/{task_id}",
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                result = data.get('result')
                
                if result:
                    print("✅ 获取处理结果成功")
                    print(f"   结果类型: {type(result)}")
                    
                    # 如果结果是字符串，显示前100个字符
                    if isinstance(result, str):
                        preview = result[:100] + "..." if len(result) > 100 else result
                        print(f"   结果预览: {preview}")
                    elif isinstance(result, dict):
                        print(f"   结果字段: {list(result.keys())}")
                    
                    return True
                else:
                    print("⚠️ 任务完成但没有结果数据")
                    return True  # 任务完成算成功
            else:
                print(f"❌ 结果获取失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 结果获取异常: {e}")
            return False
    
    def step6_download_output(self, task_id):
        """步骤6: 下载输出文件（可选）"""
        print("\n💾 步骤6: 下载输出文件")
        
        if not task_id:
            print("❌ 没有有效的任务ID")
            return False
        
        try:
            # 尝试下载JSON格式的结果
            response = self.session.get(
                f"{API_BASE}/speech/task/{task_id}/download",
                params={'format': 'json'},
                timeout=10
            )
            
            if response.status_code == 200:
                print("✅ 结果下载成功")
                print(f"   文件大小: {len(response.content)} 字节")
                print(f"   内容类型: {response.headers.get('content-type', 'unknown')}")
                return True
            elif response.status_code == 404:
                print("ℹ️ 下载功能未实现或结果不可下载")
                return True  # 不算失败
            else:
                print(f"❌ 下载失败: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 下载异常: {e}")
            return False
    
    def run_complete_workflow(self):
        """运行完整的工作流程"""
        print("🚀 开始端到端用户流程测试")
        print("=" * 60)
        
        steps = [
            ("用户登录", self.step1_user_login),
            ("上传音频", self.step2_upload_audio),
            ("配置处理", lambda: self.step3_configure_processing(self.uploaded_files[-1] if self.uploaded_files else None)),
            ("监控进度", lambda: self.step4_monitor_progress(self.created_tasks[-1] if self.created_tasks else None)),
            ("获取结果", lambda: self.step5_get_results(self.created_tasks[-1] if self.created_tasks else None)),
            ("下载输出", lambda: self.step6_download_output(self.created_tasks[-1] if self.created_tasks else None))
        ]
        
        results = []
        
        for step_name, step_func in steps:
            try:
                result = step_func()
                results.append((step_name, bool(result)))
                
                if not result:
                    print(f"❌ 步骤失败: {step_name}")
                    break  # 如果某步失败，停止后续步骤
                    
            except Exception as e:
                print(f"❌ 步骤异常: {step_name} - {e}")
                results.append((step_name, False))
                break
        
        # 输出测试结果
        print("\n" + "=" * 60)
        print("端到端流程测试结果:")
        print("=" * 60)
        
        passed = 0
        total = len(results)
        
        for step_name, success in results:
            status = "✅ 通过" if success else "❌ 失败"
            print(f"{step_name}: {status}")
            if success:
                passed += 1
        
        print(f"\n总计: {passed}/{total} 个步骤通过")
        
        if passed == total:
            print("🎉 端到端流程测试完全成功！")
            return True
        else:
            print("⚠️ 部分步骤失败，请检查系统状态")
            return False


def main():
    """主函数"""
    test = EndToEndWorkflowTest()
    
    try:
        test.setup()
        success = test.run_complete_workflow()
        return success
    finally:
        test.cleanup()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
