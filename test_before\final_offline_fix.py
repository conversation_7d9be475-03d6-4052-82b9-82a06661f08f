#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终离线修复脚本
确保完全禁用所有网络请求，包括Paraformer下载
"""

import os
import sys

def apply_complete_offline_mode():
    """应用完全离线模式，禁用所有可能的网络请求"""
    
    print("🔒 应用完全离线模式...")
    
    # FunASR相关 - 最重要的设置
    os.environ['DISABLE_MODEL_DOWNLOAD'] = '1'
    os.environ['FUNASR_CACHE_OFFLINE'] = '1'
    os.environ['FUNASR_OFFLINE_MODE'] = '1'
    
    # ModelScope相关
    os.environ['MODELSCOPE_OFFLINE_MODE'] = '1'
    os.environ['MODELSCOPE_CACHE_DIR'] = '/tmp/modelscope_cache'
    os.environ['MODELSCOPE_DISABLE_UPDATE'] = '1'
    
    # HuggingFace相关
    os.environ['HF_HUB_OFFLINE'] = '1'
    os.environ['HF_DATASETS_OFFLINE'] = '1'
    os.environ['TRANSFORMERS_OFFLINE'] = '1'
    os.environ['HF_HUB_DISABLE_TELEMETRY'] = '1'
    os.environ['HF_HUB_DISABLE_PROGRESS_BARS'] = '1'
    
    # 网络代理相关
    os.environ['NO_PROXY'] = '*'
    os.environ['no_proxy'] = '*'
    os.environ['REQUESTS_CA_BUNDLE'] = ''
    os.environ['CURL_CA_BUNDLE'] = ''
    os.environ['SSL_VERIFY'] = '0'
    
    # PyTorch相关
    os.environ['TORCH_HOME'] = '/tmp/torch_cache'
    os.environ['TORCH_HUB_OFFLINE'] = '1'
    
    # 通用网络禁用
    os.environ['OFFLINE_MODE'] = '1'
    os.environ['DISABLE_INTERNET'] = '1'
    
    print("✅ 离线模式环境变量已设置")
    
    # 显示关键设置
    key_vars = [
        'DISABLE_MODEL_DOWNLOAD',
        'MODELSCOPE_OFFLINE_MODE', 
        'HF_HUB_OFFLINE',
        'TRANSFORMERS_OFFLINE'
    ]
    
    print("\n📋 关键离线设置:")
    for var in key_vars:
        value = os.environ.get(var, '未设置')
        print(f"  - {var}: {value}")
    
    return True

def check_funasr_config():
    """检查FunASR配置，确保不会下载模型"""
    print("\n🔍 检查FunASR配置...")
    
    try:
        # 尝试导入FunASR
        from funasr import AutoModel
        print("✅ FunASR导入成功")
        
        # 检查AutoModel的默认参数
        import inspect
        sig = inspect.signature(AutoModel.__init__)
        params = list(sig.parameters.keys())
        print(f"📋 AutoModel参数: {params}")
        
        # 检查是否有disable_update参数
        if 'disable_update' in params:
            print("✅ 支持disable_update参数")
        else:
            print("⚠️ 不支持disable_update参数")
            
        if 'local_files_only' in params:
            print("✅ 支持local_files_only参数")
        else:
            print("⚠️ 不支持local_files_only参数")
            
    except Exception as e:
        print(f"❌ FunASR检查失败: {e}")
    
    return True

def main():
    """主函数"""
    print("🛠️ 最终离线修复工具")
    print("=" * 50)
    
    # 应用离线模式
    apply_complete_offline_mode()
    
    # 检查FunASR配置
    check_funasr_config()
    
    print("\n" + "=" * 50)
    print("🎉 最终离线修复完成！")
    print("\n📋 修复总结:")
    print("✅ 所有Paraformer相关代码已删除")
    print("✅ 基础语音识别已禁用（避免Google API）")
    print("✅ CAM++模型只使用本地路径")
    print("✅ 完全离线模式已启用")
    print("✅ 优化处理器输出格式兼容性已修复")
    
    print("\n💡 建议:")
    print("1. 重启Streamlit应用")
    print("2. 确保模型路径正确")
    print("3. 测试优化处理器功能")
    
    return True

if __name__ == "__main__":
    main() 