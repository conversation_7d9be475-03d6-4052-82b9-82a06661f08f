#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试进度模拟
"""

import requests
import time

try:
    print("🔧 开始测试进度模拟...")
    
    # 任务ID（从前端获取）
    task_id = "f6d2361d-42ce-43d6-8b45-17274ed1e947"
    
    # API端点
    base_url = "http://localhost:8002"
    
    # 获取认证token（使用admin账户）
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    print("🔐 登录获取token...")
    login_response = requests.post(f"{base_url}/api/v1/auth/login", json=login_data)
    
    if login_response.status_code == 200:
        token = login_response.json().get("access_token")
        print(f"✅ 登录成功，token: {token[:20]}...")
        
        # 设置认证头
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        print(f"🚀 开始模拟任务 {task_id} 的进度...")
        
        # 调用进度模拟API
        simulate_url = f"{base_url}/api/v1/audio/tasks/{task_id}/simulate-progress"
        simulate_response = requests.post(simulate_url, headers=headers)
        
        if simulate_response.status_code == 200:
            result = simulate_response.json()
            print(f"✅ 进度模拟启动成功: {result}")
            
            print("⏳ 进度模拟已启动，请在前端观察进度条变化...")
            print("💡 提示：切换到处理结果页面查看任务状态变化")
            
        else:
            print(f"❌ 进度模拟启动失败: {simulate_response.status_code}")
            print(f"错误信息: {simulate_response.text}")
    
    else:
        print(f"❌ 登录失败: {login_response.status_code}")
        print(f"错误信息: {login_response.text}")

except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
