#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单并行处理测试脚本
任务#7.1 - 实现多线程并行处理框架

作者: AI Assistant
创建时间: 2025-01-27
"""

import sys
import time
import os
from pathlib import Path

# 添加utils目录到路径
sys.path.insert(0, str(Path(__file__).parent / "utils"))

def test_parallel_processor():
    """测试并行处理器"""
    print("🚀 开始测试并行处理框架...")
    
    try:
        # 导入模块
        from parallel_processor import ParallelConfig, create_parallel_processor, parallel_map
        print("✅ 成功导入并行处理模块")
        
        # 测试配置
        config = ParallelConfig(max_workers=2, timeout=10.0)
        print(f"✅ 配置创建成功: max_workers={config.max_workers}")
        
        # 测试简单任务
        def simple_task(x):
            time.sleep(0.1)  # 模拟处理时间
            return x * x
        
        print("📊 测试并行映射...")
        data = [1, 2, 3, 4, 5]
        start_time = time.time()
        results = parallel_map(simple_task, data, max_workers=2)
        end_time = time.time()
        
        print(f"✅ 并行处理完成")
        print(f"   输入: {data}")
        print(f"   输出: {results}")
        print(f"   用时: {end_time - start_time:.2f}秒")
        
        # 验证结果
        expected = [x * x for x in data]
        success_count = sum(1 for r in results if isinstance(r, int) and r in expected)
        print(f"   成功率: {success_count}/{len(data)} ({success_count/len(data)*100:.1f}%)")
        
        print("🎉 并行处理框架测试成功！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_resource_monitor():
    """测试资源监控"""
    print("\n📊 测试资源监控...")
    
    try:
        from parallel_processor import ParallelConfig, ResourceMonitor
        
        config = ParallelConfig(monitor_interval=0.1)
        monitor = ResourceMonitor(config)
        
        print("启动监控...")
        monitor.start_monitoring()
        time.sleep(0.5)
        
        metrics = monitor.get_current_metrics()
        if metrics:
            print(f"✅ CPU使用率: {metrics.cpu_percent:.1f}%")
            print(f"✅ 内存使用: {metrics.memory_used_gb:.1f}GB")
        else:
            print("❌ 未获取到监控数据")
        
        monitor.stop_monitoring()
        print("✅ 资源监控测试成功")
        return True
        
    except Exception as e:
        print(f"❌ 资源监控测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("并行处理框架简单测试 - 任务#7.1")
    print("=" * 60)
    
    tests = [
        ("并行处理器", test_parallel_processor),
        ("资源监控", test_resource_monitor),
    ]
    
    passed = 0
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name}测试失败")
        except Exception as e:
            print(f"❌ {test_name}测试出错: {e}")
    
    print(f"\n📈 测试结果: {passed}/{len(tests)} 通过")
    
    if passed == len(tests):
        print("🎉 所有测试通过！并行处理框架运行正常")
    else:
        print("⚠️ 部分测试失败，请检查环境配置")

if __name__ == "__main__":
    main() 