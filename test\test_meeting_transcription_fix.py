#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
会议转录修复功能测试
测试数据结构修复、离线模式和WebSocket进度同步
"""

import os
import sys
import pytest
import json
import asyncio
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from backend.tasks.audio_processing_tasks import _integrate_meeting_results
from backend.tasks.base_task import BaseTask
from backend.api.websocket import ConnectionManager


class TestMeetingTranscriptionFix:
    """测试会议转录修复功能"""
    
    def setup_method(self):
        """测试前设置"""
        # 模拟VAD片段
        self.vad_segments = [
            {'start_time': 0.0, 'end_time': 2.5, 'duration': 2.5},
            {'start_time': 3.0, 'end_time': 5.5, 'duration': 2.5},
            {'start_time': 6.0, 'end_time': 8.0, 'duration': 2.0}
        ]
        
        # 模拟语音识别片段
        self.recognition_segments = [
            {
                'start_time': 0.0, 'end_time': 2.5, 'duration': 2.5,
                'text': '你好，今天的会议开始了', 'confidence': 0.95,
                'language': 'zh', 'emotions': [], 'events': []
            },
            {
                'start_time': 3.0, 'end_time': 5.5, 'duration': 2.5,
                'text': '好的，我们来讨论项目进展', 'confidence': 0.92,
                'language': 'zh', 'emotions': [], 'events': []
            },
            {
                'start_time': 6.0, 'end_time': 8.0, 'duration': 2.0,
                'text': '这个功能需要优化', 'confidence': 0.88,
                'language': 'zh', 'emotions': [], 'events': []
            }
        ]
        
        # 模拟说话人识别结果
        self.speaker_segments = [
            {'id': 0, 'name': '说话人1'},
            {'id': 1, 'name': '说话人2'}
        ]
        
        self.full_text = "你好，今天的会议开始了。好的，我们来讨论项目进展。这个功能需要优化。"
    
    def test_integrate_meeting_results_structure(self):
        """测试_integrate_meeting_results返回正确的数据结构"""
        result = _integrate_meeting_results(
            self.vad_segments,
            self.recognition_segments,
            self.speaker_segments,
            self.full_text
        )
        
        # 验证返回的是字典而不是数组
        assert isinstance(result, dict), "返回结果应该是字典"
        
        # 验证必需的字段存在
        assert 'speech_segments' in result, "应该包含speech_segments字段"
        assert 'speaker_segments' in result, "应该包含speaker_segments字段"
        assert 'text' in result, "应该包含text字段"
        assert 'total_segments' in result, "应该包含total_segments字段"
        assert 'total_speakers' in result, "应该包含total_speakers字段"
        
        # 验证speech_segments结构
        speech_segments = result['speech_segments']
        assert isinstance(speech_segments, list), "speech_segments应该是数组"
        assert len(speech_segments) > 0, "应该有语音片段"
        
        # 验证每个语音片段的结构
        for segment in speech_segments:
            assert 'segment_id' in segment, "应该包含segment_id"
            assert 'start_time' in segment, "应该包含start_time"
            assert 'end_time' in segment, "应该包含end_time"
            assert 'duration' in segment, "应该包含duration"
            assert 'text' in segment, "应该包含text"
            assert 'speaker_id' in segment, "应该包含speaker_id"
            assert 'speaker_name' in segment, "应该包含speaker_name"
            assert 'confidence' in segment, "应该包含confidence"
        
        # 验证speaker_segments结构
        speaker_segments = result['speaker_segments']
        assert isinstance(speaker_segments, list), "speaker_segments应该是数组"
        
        for speaker in speaker_segments:
            assert 'name' in speaker, "应该包含name字段"
            assert 'segment_count' in speaker, "应该包含segment_count字段"
            assert 'total_time' in speaker, "应该包含total_time字段"
    
    def test_integrate_meeting_results_speaker_assignment(self):
        """测试说话人分配逻辑"""
        result = _integrate_meeting_results(
            self.vad_segments,
            self.recognition_segments,
            self.speaker_segments,
            self.full_text
        )
        
        speech_segments = result['speech_segments']
        
        # 验证说话人分配
        speaker_ids = [seg['speaker_id'] for seg in speech_segments]
        assert all(isinstance(sid, int) for sid in speaker_ids), "speaker_id应该是整数"
        assert all(0 <= sid < len(self.speaker_segments) for sid in speaker_ids), "speaker_id应该在有效范围内"
        
        # 验证说话人名称
        speaker_names = [seg['speaker_name'] for seg in speech_segments]
        expected_names = ['说话人1', '说话人2']
        assert all(name in expected_names for name in speaker_names), "说话人名称应该正确"
    
    def test_integrate_meeting_results_empty_input(self):
        """测试空输入的处理"""
        result = _integrate_meeting_results([], [], [], "")
        
        assert isinstance(result, dict), "空输入也应该返回字典"
        assert result['speech_segments'] == [], "空输入应该返回空的speech_segments"
        assert result['speaker_segments'] == [], "空输入应该返回空的speaker_segments"
        assert result['total_segments'] == 0, "空输入的total_segments应该为0"
        assert result['total_speakers'] == 0, "空输入的total_speakers应该为0"


class TestOfflineMode:
    """测试离线模式配置"""
    
    def test_offline_environment_variables(self):
        """测试离线环境变量设置"""
        # 导入模块时应该设置环境变量
        import backend.utils.audio.optimized_funasr_manager
        import backend.utils.audio.speech_recognition_core
        import backend.utils.audio.speaker_recognition
        
        # 验证关键的离线环境变量
        offline_vars = [
            'HF_HUB_OFFLINE',
            'HF_DATASETS_OFFLINE',
            'TRANSFORMERS_OFFLINE'
        ]
        
        for var in offline_vars:
            assert os.environ.get(var) == '1', f"环境变量 {var} 应该设置为 '1'"
    
    def test_funasr_offline_config(self):
        """测试FunASR离线配置"""
        try:
            from backend.utils.audio.optimized_funasr_manager import OptimizedFunASRManager

            # 创建管理器实例
            manager = OptimizedFunASRManager()

            # 模拟配置
            config = {
                'model_path': '/test/model/path',
                'device': 'cpu',
                'trust_remote_code': True,
                'disable_update': True,
                'local_files_only': True
            }

            # 调用优化配置方法
            optimized_config = manager._create_optimized_config(config)

            # 验证离线参数
            assert optimized_config.get('local_files_only') == True, "应该设置local_files_only=True"
            assert optimized_config.get('offline') == True, "应该设置offline=True"
            assert optimized_config.get('use_auth_token') == False, "应该设置use_auth_token=False"
            assert optimized_config.get('force_download') == False, "应该设置force_download=False"

        except ImportError:
            # 如果模块不可用，跳过测试
            pytest.skip("FunASR模块不可用")


class TestWebSocketProgressSync:
    """测试WebSocket进度同步"""
    
    def setup_method(self):
        """测试前设置"""
        self.connection_manager = ConnectionManager()
    
    @pytest.mark.asyncio
    async def test_connection_manager_send_message(self):
        """测试连接管理器发送消息"""
        # 模拟WebSocket连接
        mock_websocket = Mock()
        mock_websocket.client_state.name = 'CONNECTED'
        mock_websocket.send_text = Mock(return_value=asyncio.Future())
        mock_websocket.send_text.return_value.set_result(None)
        
        user_id = "test_user"
        
        # 添加连接
        self.connection_manager.active_connections[user_id] = {
            'websocket': mock_websocket,
            'subscribed_tasks': set()
        }
        
        # 发送消息
        message = {'type': 'test', 'data': 'test_data'}
        result = await self.connection_manager.send_personal_message(message, user_id)
        
        # 验证结果
        assert result == True, "发送消息应该成功"
        mock_websocket.send_text.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_connection_manager_error_handling(self):
        """测试连接管理器错误处理"""
        # 模拟WebSocket连接错误
        mock_websocket = Mock()
        mock_websocket.client_state.name = 'CONNECTED'
        mock_websocket.send_text = Mock(side_effect=Exception("Connection error"))
        
        user_id = "test_user"
        
        # 添加连接
        self.connection_manager.active_connections[user_id] = {
            'websocket': mock_websocket,
            'subscribed_tasks': set()
        }
        
        # 发送消息（应该失败）
        message = {'type': 'test', 'data': 'test_data'}
        result = await self.connection_manager.send_personal_message(message, user_id)
        
        # 验证错误处理
        assert result == False, "发送失败应该返回False"
        assert user_id not in self.connection_manager.active_connections, "连接应该被清理"
    
    def test_base_task_progress_update_redis_fallback(self):
        """测试BaseTask进度更新的Redis回退机制"""
        with patch('backend.tasks.base_task.get_progress_notifier') as mock_notifier, \
             patch('backend.tasks.base_task.get_db_session') as mock_db, \
             patch('redis.Redis') as mock_redis:
            
            # 模拟Redis客户端
            mock_redis_client = Mock()
            mock_redis_client.exists.return_value = False
            mock_redis_client.hset.return_value = None
            mock_redis_client.expire.return_value = None
            mock_redis_client.publish.return_value = None
            
            # 模拟进度通知器不可用
            mock_notifier.return_value = None
            
            # 创建BaseTask实例
            task = BaseTask()
            task.redis_client = mock_redis_client
            task.persistence_service = Mock()
            
            # 调用进度更新
            task.update_progress("test_task", 50.0, "测试进度", "测试阶段")
            
            # 验证Redis操作
            mock_redis_client.hset.assert_called()
            mock_redis_client.expire.assert_called()


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
