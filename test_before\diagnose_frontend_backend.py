#!/usr/bin/env python3
"""
前端后端连接诊断脚本
"""

import requests
import json
import time
import subprocess
import sys
from pathlib import Path

def check_service_status():
    """检查服务状态"""
    print("🔍 检查服务状态...")
    
    # 检查后端服务
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            print("   ✅ 后端服务 (8000) 正常运行")
            print(f"      响应: {response.json()}")
        else:
            print(f"   ❌ 后端服务异常: {response.status_code}")
    except Exception as e:
        print(f"   ❌ 后端服务无法连接: {e}")
        return False
    
    # 检查前端服务（尝试3000和3001端口）
    frontend_running = False
    for port in [3000, 3001]:
        try:
            response = requests.get(f"http://localhost:{port}", timeout=5)
            if response.status_code == 200:
                print(f"   ✅ 前端服务 ({port}) 正常运行")
                frontend_running = True
                break
            else:
                print(f"   ❌ 前端服务 ({port}) 异常: {response.status_code}")
        except Exception as e:
            print(f"   ❌ 前端服务 ({port}) 无法连接: {e}")

    if not frontend_running:
        return False
    
    return True

def test_api_endpoints():
    """测试API端点"""
    print("\n🔍 测试API端点...")
    
    # 测试认证API
    print("\n1. 测试认证API...")
    try:
        login_data = {"username": "admin", "password": "admin123"}
        response = requests.post("http://localhost:8000/api/v1/auth/login", json=login_data, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            token = data.get("access_token")
            print("   ✅ 登录API正常")
            print(f"   Token: {token[:30]}..." if token else "   ❌ 未获取到token")
            return token
        else:
            print(f"   ❌ 登录API失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return None
    except Exception as e:
        print(f"   ❌ 登录API请求失败: {e}")
        return None

def test_document_api(token):
    """测试文档管理API"""
    print("\n2. 测试文档管理API...")
    
    if not token:
        print("   ❌ 没有有效token，跳过测试")
        return False
    
    headers = {"Authorization": f"Bearer {token}"}
    
    # 测试文档列表API
    try:
        response = requests.get("http://localhost:8000/api/v1/documents/documents", headers=headers, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            print("   ✅ 文档列表API正常")
            print(f"   文档数量: {data.get('total', 0)}")
            return True
        else:
            print(f"   ❌ 文档列表API失败: {response.status_code}")
            print(f"   响应: {response.text}")
            return False
    except Exception as e:
        print(f"   ❌ 文档列表API请求失败: {e}")
        return False

def test_frontend_proxy():
    """测试前端代理"""
    print("\n3. 测试前端代理...")

    # 通过前端代理访问后端API（尝试3000和3001端口）
    for port in [3000, 3001]:
        try:
            response = requests.get(f"http://localhost:{port}/api/v1/auth/me", timeout=10)
            print(f"   前端代理 ({port}) 状态: {response.status_code}")

            if response.status_code in [401, 403]:
                print(f"   ✅ 前端代理 ({port}) 工作正常（返回认证错误是正常的）")
                return True
            elif response.status_code == 200:
                print(f"   ✅ 前端代理 ({port}) 工作正常")
                return True
            else:
                print(f"   ❌ 前端代理 ({port}) 异常: {response.text}")
        except Exception as e:
            print(f"   ❌ 前端代理 ({port}) 请求失败: {e}")

    return False

def check_cors_headers():
    """检查CORS配置"""
    print("\n4. 检查CORS配置...")
    
    try:
        # 发送OPTIONS请求检查CORS
        headers = {
            'Origin': 'http://localhost:3000',
            'Access-Control-Request-Method': 'GET',
            'Access-Control-Request-Headers': 'Authorization'
        }
        
        response = requests.options("http://localhost:8000/api/v1/documents/documents", headers=headers, timeout=5)
        
        cors_headers = {
            'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
            'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
            'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers'),
        }
        
        print(f"   CORS Headers: {cors_headers}")
        
        if cors_headers['Access-Control-Allow-Origin']:
            print("   ✅ CORS配置正常")
            return True
        else:
            print("   ❌ CORS配置可能有问题")
            return False
            
    except Exception as e:
        print(f"   ❌ CORS检查失败: {e}")
        return False

def check_worker_status():
    """检查Worker状态"""
    print("\n5. 检查Celery Worker状态...")
    
    try:
        # 检查Celery Worker
        result = subprocess.run(
            [".venv/Scripts/activate", "&&", "celery", "-A", "backend.core.task_queue", "inspect", "active"],
            shell=True,
            capture_output=True,
            text=True,
            timeout=10,
            cwd="."
        )
        
        if result.returncode == 0:
            print("   ✅ Celery Worker正常运行")
            print(f"   输出: {result.stdout[:200]}...")
            return True
        else:
            print(f"   ❌ Celery Worker异常: {result.stderr}")
            return False
            
    except Exception as e:
        print(f"   ❌ Celery Worker检查失败: {e}")
        return False

def check_database():
    """检查数据库状态"""
    print("\n6. 检查数据库状态...")
    
    try:
        # 检查数据库文件
        db_file = Path("speech_platform.db")
        if db_file.exists():
            print(f"   ✅ 数据库文件存在: {db_file.stat().st_size} bytes")
            return True
        else:
            print("   ❌ 数据库文件不存在")
            return False
            
    except Exception as e:
        print(f"   ❌ 数据库检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 开始前端后端连接诊断...")
    print("=" * 50)
    
    # 检查服务状态
    if not check_service_status():
        print("\n❌ 基础服务检查失败，请先确保前后端服务正常运行")
        return
    
    # 测试API端点
    token = test_api_endpoints()
    
    # 测试文档管理API
    test_document_api(token)
    
    # 测试前端代理
    test_frontend_proxy()
    
    # 检查CORS
    check_cors_headers()
    
    # 检查Worker
    check_worker_status()
    
    # 检查数据库
    check_database()
    
    print("\n" + "=" * 50)
    print("🎉 诊断完成！")
    
    print("\n💡 如果文档管理页面仍无法访问，请检查：")
    print("   1. 浏览器控制台是否有JavaScript错误")
    print("   2. 浏览器网络面板中的API请求状态")
    print("   3. 前端路由守卫是否正确处理认证")
    print("   4. 用户是否已正确登录")

if __name__ == "__main__":
    main()
