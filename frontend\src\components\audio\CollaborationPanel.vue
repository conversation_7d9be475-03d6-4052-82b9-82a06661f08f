<template>
  <div class="collaboration-panel">
    <!-- 协作头部 -->
    <div class="collaboration-header">
      <div class="header-info">
        <h3>👥 实时协作</h3>
        <div class="collaboration-status">
          <el-tag :type="connectionStatus === 'connected' ? 'success' : 'danger'" size="small">
            {{ connectionStatus === 'connected' ? '已连接' : '未连接' }}
          </el-tag>
          <span class="online-users">在线用户: {{ onlineUsers.length }}</span>
        </div>
      </div>
      
      <div class="header-actions">
        <el-button @click="shareWorkspace" :icon="Share" size="small" type="primary">
          分享工作区
        </el-button>
        <el-button @click="inviteUsers" :icon="UserPlus" size="small">
          邀请用户
        </el-button>
        <el-dropdown @command="handleCollaborationCommand">
          <el-button :icon="Setting" size="small" circle />
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="permissions">权限设置</el-dropdown-item>
              <el-dropdown-item command="history">操作历史</el-dropdown-item>
              <el-dropdown-item command="export">导出日志</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <!-- 在线用户列表 -->
    <div class="online-users-section">
      <div class="section-header">
        <h4>👤 在线用户</h4>
        <span class="user-count">{{ onlineUsers.length }} 人在线</span>
      </div>
      
      <div class="users-list">
        <div
          v-for="user in onlineUsers"
          :key="user.id"
          class="user-item"
          :class="{ 'is-current': user.id === currentUser.id }"
        >
          <div class="user-avatar">
            <el-avatar :size="32" :src="user.avatar">
              {{ user.name.charAt(0) }}
            </el-avatar>
            <div class="user-status" :class="`status-${user.status}`"></div>
          </div>
          
          <div class="user-info">
            <div class="user-name">{{ user.name }}</div>
            <div class="user-activity">{{ user.currentActivity || '空闲中' }}</div>
          </div>
          
          <div class="user-actions" v-if="user.id !== currentUser.id">
            <el-dropdown @command="(cmd) => handleUserCommand(cmd, user)">
              <el-button text :icon="MoreFilled" size="small" />
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="follow">跟随操作</el-dropdown-item>
                  <el-dropdown-item command="share">分享屏幕</el-dropdown-item>
                  <el-dropdown-item command="message">发送消息</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </div>
    </div>

    <!-- 共享任务 -->
    <div class="shared-tasks-section">
      <div class="section-header">
        <h4>📋 共享任务</h4>
        <el-button @click="createSharedTask" size="small" type="primary" plain>
          创建任务
        </el-button>
      </div>
      
      <div class="tasks-list">
        <div
          v-for="task in sharedTasks"
          :key="task.id"
          class="task-item"
          :class="`task-${task.status}`"
          @click="selectTask(task)"
        >
          <div class="task-header">
            <div class="task-title">{{ task.title }}</div>
            <div class="task-assignee" v-if="task.assignedTo">
              <el-avatar :size="20" :src="task.assignedTo.avatar">
                {{ task.assignedTo.name.charAt(0) }}
              </el-avatar>
            </div>
          </div>
          
          <div class="task-description">{{ task.description }}</div>
          
          <div class="task-meta">
            <span class="task-status">{{ getTaskStatusText(task.status) }}</span>
            <span class="task-progress" v-if="task.progress">{{ task.progress }}%</span>
            <span class="task-time">{{ formatTime(task.updatedAt) }}</span>
          </div>
          
          <div class="task-actions">
            <el-button 
              v-if="task.status === 'pending' && !task.assignedTo"
              @click.stop="assignTask(task)"
              size="small"
              type="primary"
              plain
            >
              接受任务
            </el-button>
            <el-button 
              v-if="task.assignedTo?.id === currentUser.id && task.status === 'in_progress'"
              @click.stop="completeTask(task)"
              size="small"
              type="success"
              plain
            >
              完成任务
            </el-button>
          </div>
        </div>
      </div>
    </div>

    <!-- 实时聊天 -->
    <div class="chat-section">
      <div class="section-header">
        <h4>💬 实时聊天</h4>
        <el-button @click="toggleChat" text size="small">
          {{ chatVisible ? '隐藏' : '显示' }}
        </el-button>
      </div>
      
      <div v-show="chatVisible" class="chat-container">
        <div class="chat-messages" ref="chatMessages">
          <div
            v-for="message in chatMessages"
            :key="message.id"
            class="message-item"
            :class="{ 'is-own': message.userId === currentUser.id }"
          >
            <div class="message-avatar">
              <el-avatar :size="24" :src="message.user.avatar">
                {{ message.user.name.charAt(0) }}
              </el-avatar>
            </div>
            
            <div class="message-content">
              <div class="message-header">
                <span class="message-user">{{ message.user.name }}</span>
                <span class="message-time">{{ formatTime(message.timestamp) }}</span>
              </div>
              <div class="message-text">{{ message.text }}</div>
            </div>
          </div>
        </div>
        
        <div class="chat-input">
          <el-input
            v-model="newMessage"
            placeholder="输入消息..."
            @keyup.enter="sendMessage"
            :maxlength="500"
            show-word-limit
          >
            <template #append>
              <el-button @click="sendMessage" :icon="Promotion" />
            </template>
          </el-input>
        </div>
      </div>
    </div>

    <!-- 操作日志 -->
    <div class="activity-log-section">
      <div class="section-header">
        <h4>📝 操作日志</h4>
        <el-button @click="clearActivityLog" text size="small">
          清空日志
        </el-button>
      </div>
      
      <div class="activity-list">
        <div
          v-for="activity in recentActivities"
          :key="activity.id"
          class="activity-item"
          :class="`activity-${activity.type}`"
        >
          <div class="activity-icon">
            <el-icon v-if="activity.type === 'file_upload'"><Upload /></el-icon>
            <el-icon v-else-if="activity.type === 'task_complete'"><CircleCheckFilled /></el-icon>
            <el-icon v-else-if="activity.type === 'user_join'"><Plus /></el-icon>
            <el-icon v-else-if="activity.type === 'user_leave'"><Remove /></el-icon>
            <el-icon v-else><Operation /></el-icon>
          </div>
          
          <div class="activity-content">
            <div class="activity-text">{{ activity.description }}</div>
            <div class="activity-time">{{ formatTime(activity.timestamp) }}</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 权限设置对话框 -->
    <el-dialog
      v-model="permissionsDialogVisible"
      title="权限设置"
      width="500px"
    >
      <div class="permissions-content">
        <div class="permission-item" v-for="user in onlineUsers" :key="user.id">
          <div class="user-info">
            <el-avatar :size="32" :src="user.avatar">
              {{ user.name.charAt(0) }}
            </el-avatar>
            <span class="user-name">{{ user.name }}</span>
          </div>
          
          <div class="permission-controls">
            <el-select v-model="user.permission" size="small">
              <el-option label="只读" value="read" />
              <el-option label="编辑" value="edit" />
              <el-option label="管理员" value="admin" />
            </el-select>
          </div>
        </div>
      </div>
      
      <template #footer>
        <el-button @click="permissionsDialogVisible = false">取消</el-button>
        <el-button @click="savePermissions" type="primary">保存</el-button>
      </template>
    </el-dialog>

    <!-- 邀请用户对话框 -->
    <el-dialog
      v-model="inviteDialogVisible"
      title="邀请用户"
      width="400px"
    >
      <div class="invite-content">
        <el-form :model="inviteForm" label-width="80px">
          <el-form-item label="邀请方式">
            <el-radio-group v-model="inviteForm.method">
              <el-radio value="email">邮箱邀请</el-radio>
              <el-radio value="link">分享链接</el-radio>
            </el-radio-group>
          </el-form-item>
          
          <el-form-item v-if="inviteForm.method === 'email'" label="邮箱地址">
            <el-input v-model="inviteForm.email" placeholder="输入邮箱地址" />
          </el-form-item>
          
          <el-form-item v-if="inviteForm.method === 'link'" label="分享链接">
            <el-input v-model="shareLink" readonly>
              <template #append>
                <el-button @click="copyShareLink" :icon="DocumentCopy" />
              </template>
            </el-input>
          </el-form-item>
          
          <el-form-item label="权限级别">
            <el-select v-model="inviteForm.permission">
              <el-option label="只读" value="read" />
              <el-option label="编辑" value="edit" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
      
      <template #footer>
        <el-button @click="inviteDialogVisible = false">取消</el-button>
        <el-button @click="sendInvite" type="primary">
          {{ inviteForm.method === 'email' ? '发送邀请' : '生成链接' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Share,
  Plus,
  Remove,
  Setting,
  MoreFilled,
  Promotion,
  Upload,
  CircleCheckFilled,
  Operation,
  DocumentCopy
} from '@element-plus/icons-vue'

// 导入WebSocket和API (暂时注释掉以修复错误)
// import { useWebSocket } from '@/composables/useWebSocket'
// import { collaborationAPI } from '@/api/collaboration'

// Props
const props = defineProps({
  workspaceId: {
    type: String,
    required: true
  },
  currentUser: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits([
  'user-joined',
  'user-left',
  'task-created',
  'task-assigned',
  'task-completed',
  'message-sent',
  'activity-logged'
])

// WebSocket连接 (暂时注释掉以修复错误)
// const {
//   connect: connectWebSocket,
//   disconnect: disconnectWebSocket,
//   isConnected,
//   send: sendWebSocketMessage,
//   subscribe
// } = useWebSocket()

// 临时的模拟数据
const isConnected = ref(false)
const connectWebSocket = () => { console.log('模拟WebSocket连接') }
const disconnectWebSocket = () => { console.log('模拟WebSocket断开') }
const sendWebSocketMessage = () => { console.log('模拟发送WebSocket消息') }

// 响应式数据
const connectionStatus = ref('disconnected')
const onlineUsers = ref([])
const sharedTasks = ref([])
const chatMessages = ref([])
const activityLog = ref([])
const newMessage = ref('')
const chatVisible = ref(true)
const permissionsDialogVisible = ref(false)
const inviteDialogVisible = ref(false)
const chatMessages_ref = ref(null)

// 当前用户
const currentUser = computed(() => props.currentUser)

// 邀请表单
const inviteForm = ref({
  method: 'email',
  email: '',
  permission: 'read'
})

const shareLink = ref('')

// 计算属性
const recentActivities = computed(() =>
  activityLog.value.slice(-20).reverse()
)

// WebSocket事件处理 (暂时简化)
const setupWebSocketHandlers = () => {
  console.log('设置WebSocket事件处理器 (模拟)')
  // 暂时注释掉复杂的WebSocket逻辑
  /*
  // 用户加入/离开
  subscribe('user_joined', (data) => {
    const existingUser = onlineUsers.value.find(u => u.id === data.user.id)
    if (!existingUser) {
      onlineUsers.value.push(data.user)
      addActivity('user_join', `${data.user.name} 加入了工作区`)
      emit('user-joined', data.user)
    }
  })

  subscribe('user_left', (data) => {
    const userIndex = onlineUsers.value.findIndex(u => u.id === data.userId)
    if (userIndex > -1) {
      const user = onlineUsers.value[userIndex]
      onlineUsers.value.splice(userIndex, 1)
      addActivity('user_leave', `${user.name} 离开了工作区`)
      emit('user-left', data.userId)
    }
  })
  */

  // 任务更新
  subscribe('task_created', (data) => {
    sharedTasks.value.push(data.task)
    addActivity('task_create', `${data.user.name} 创建了任务: ${data.task.title}`)
    emit('task-created', data.task)
  })

  subscribe('task_updated', (data) => {
    const taskIndex = sharedTasks.value.findIndex(t => t.id === data.task.id)
    if (taskIndex > -1) {
      sharedTasks.value[taskIndex] = data.task
      addActivity('task_update', `任务 "${data.task.title}" 已更新`)
    }
  })

  subscribe('task_assigned', (data) => {
    const taskIndex = sharedTasks.value.findIndex(t => t.id === data.taskId)
    if (taskIndex > -1) {
      sharedTasks.value[taskIndex].assignedTo = data.assignee
      sharedTasks.value[taskIndex].status = 'in_progress'
      addActivity('task_assign', `${data.assignee.name} 接受了任务: ${sharedTasks.value[taskIndex].title}`)
      emit('task-assigned', data)
    }
  })

  subscribe('task_completed', (data) => {
    const taskIndex = sharedTasks.value.findIndex(t => t.id === data.taskId)
    if (taskIndex > -1) {
      sharedTasks.value[taskIndex].status = 'completed'
      sharedTasks.value[taskIndex].progress = 100
      addActivity('task_complete', `任务 "${sharedTasks.value[taskIndex].title}" 已完成`)
      emit('task-completed', data)
    }
  })

  // 聊天消息
  subscribe('chat_message', (data) => {
    chatMessages.value.push(data.message)
    scrollToBottom()
    emit('message-sent', data.message)
  })

  // 用户活动更新
  subscribe('user_activity', (data) => {
    const userIndex = onlineUsers.value.findIndex(u => u.id === data.userId)
    if (userIndex > -1) {
      onlineUsers.value[userIndex].currentActivity = data.activity
    }
  })

  // 连接状态
  subscribe('connection', (data) => {
    connectionStatus.value = data.status
  })
}

// 协作方法 (简化版本)
const shareWorkspace = async () => {
  try {
    // 模拟分享链接
    shareLink.value = `https://example.com/share/${props.workspaceId}`

    await navigator.clipboard.writeText(shareLink.value)
    ElMessage.success('工作区分享链接已复制到剪贴板')
  } catch (error) {
    ElMessage.error('创建分享链接失败')
  }
}

const inviteUsers = () => {
  inviteDialogVisible.value = true
  if (!shareLink.value) {
    shareWorkspace()
  }
}

const sendInvite = async () => {
  try {
    if (inviteForm.value.method === 'email') {
      await collaborationAPI.sendEmailInvite({
        workspaceId: props.workspaceId,
        email: inviteForm.value.email,
        permission: inviteForm.value.permission
      })
      ElMessage.success('邀请邮件已发送')
    } else {
      await copyShareLink()
    }

    inviteDialogVisible.value = false
  } catch (error) {
    ElMessage.error('发送邀请失败')
  }
}

const copyShareLink = async () => {
  try {
    await navigator.clipboard.writeText(shareLink.value)
    ElMessage.success('分享链接已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制链接失败')
  }
}

// 任务管理
const createSharedTask = async () => {
  try {
    const { value: taskData } = await ElMessageBox.prompt(
      '请输入任务标题和描述',
      '创建共享任务',
      {
        confirmButtonText: '创建',
        cancelButtonText: '取消',
        inputPattern: /^.{1,100}$/,
        inputErrorMessage: '任务标题长度应在1-100个字符之间'
      }
    )

    const task = {
      title: taskData,
      description: '',
      status: 'pending',
      createdBy: currentUser.value.id,
      workspaceId: props.workspaceId
    }

    const createdTask = await collaborationAPI.createTask(task)

    // 通过WebSocket广播
    sendWebSocketMessage({
      type: 'task_created',
      task: createdTask,
      user: currentUser.value
    })

  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('创建任务失败')
    }
  }
}

const assignTask = async (task) => {
  try {
    await collaborationAPI.assignTask(task.id, currentUser.value.id)

    // 通过WebSocket广播
    sendWebSocketMessage({
      type: 'task_assigned',
      taskId: task.id,
      assignee: currentUser.value
    })

  } catch (error) {
    ElMessage.error('接受任务失败')
  }
}

const completeTask = async (task) => {
  try {
    await collaborationAPI.completeTask(task.id)

    // 通过WebSocket广播
    sendWebSocketMessage({
      type: 'task_completed',
      taskId: task.id,
      completedBy: currentUser.value
    })

  } catch (error) {
    ElMessage.error('完成任务失败')
  }
}

const selectTask = (task) => {
  // 选择任务进行详细查看或编辑
  console.log('选择任务:', task)
}

// 聊天功能
const sendMessage = async () => {
  if (!newMessage.value.trim()) return

  const message = {
    id: Date.now() + Math.random(),
    text: newMessage.value,
    userId: currentUser.value.id,
    user: currentUser.value,
    timestamp: new Date(),
    workspaceId: props.workspaceId
  }

  try {
    // 发送到服务器
    await collaborationAPI.sendMessage(message)

    // 通过WebSocket广播
    sendWebSocketMessage({
      type: 'chat_message',
      message
    })

    newMessage.value = ''
  } catch (error) {
    ElMessage.error('发送消息失败')
  }
}

const toggleChat = () => {
  chatVisible.value = !chatVisible.value
  if (chatVisible.value) {
    nextTick(() => {
      scrollToBottom()
    })
  }
}

const scrollToBottom = () => {
  nextTick(() => {
    if (chatMessages_ref.value) {
      chatMessages_ref.value.scrollTop = chatMessages_ref.value.scrollHeight
    }
  })
}

// 用户操作
const handleUserCommand = async (command, user) => {
  switch (command) {
    case 'follow':
      // 跟随用户操作
      ElMessage.info(`开始跟随 ${user.name} 的操作`)
      break
    case 'share':
      // 分享屏幕
      ElMessage.info('屏幕分享功能开发中...')
      break
    case 'message':
      // 发送私信
      ElMessage.info('私信功能开发中...')
      break
  }
}

// 权限管理
const handleCollaborationCommand = (command) => {
  switch (command) {
    case 'permissions':
      permissionsDialogVisible.value = true
      break
    case 'history':
      // 查看操作历史
      ElMessage.info('操作历史功能开发中...')
      break
    case 'export':
      exportActivityLog()
      break
  }
}

const savePermissions = async () => {
  try {
    const permissions = onlineUsers.value.map(user => ({
      userId: user.id,
      permission: user.permission
    }))

    await collaborationAPI.updatePermissions(props.workspaceId, permissions)

    permissionsDialogVisible.value = false
    ElMessage.success('权限设置已保存')
  } catch (error) {
    ElMessage.error('保存权限设置失败')
  }
}

// 活动日志
const addActivity = (type, description) => {
  const activity = {
    id: Date.now() + Math.random(),
    type,
    description,
    timestamp: new Date(),
    userId: currentUser.value.id
  }

  activityLog.value.push(activity)

  // 限制日志数量
  if (activityLog.value.length > 100) {
    activityLog.value = activityLog.value.slice(-100)
  }

  emit('activity-logged', activity)
}

const clearActivityLog = () => {
  activityLog.value = []
  ElMessage.success('操作日志已清空')
}

const exportActivityLog = () => {
  const logData = activityLog.value.map(activity => ({
    timestamp: activity.timestamp.toISOString(),
    type: activity.type,
    description: activity.description,
    userId: activity.userId
  }))

  const blob = new Blob([JSON.stringify(logData, null, 2)], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `collaboration-log-${new Date().toISOString().split('T')[0]}.json`
  a.click()
  URL.revokeObjectURL(url)

  ElMessage.success('操作日志已导出')
}

// 工具方法
const getTaskStatusText = (status) => {
  const statusMap = {
    pending: '待处理',
    in_progress: '进行中',
    completed: '已完成',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

const formatTime = (date) => {
  if (!date) return ''
  const now = new Date()
  const time = new Date(date)
  const diff = now - time

  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) { // 24小时内
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return time.toLocaleDateString()
  }
}

// 生命周期
onMounted(async () => {
  console.log('🚀 CollaborationPanel 组件已挂载')

  // 暂时注释掉复杂的初始化逻辑
  /*
  // 连接WebSocket
  const wsUrl = `ws://localhost:8000/ws/collaboration/${props.workspaceId}`
  connectWebSocket(wsUrl)

  // 设置WebSocket事件处理
  setupWebSocketHandlers()

  // 加载初始数据
  try {
    const [users, tasks, messages] = await Promise.all([
      collaborationAPI.getOnlineUsers(props.workspaceId),
      collaborationAPI.getSharedTasks(props.workspaceId),
      collaborationAPI.getChatMessages(props.workspaceId)
    ])

    onlineUsers.value = users
    sharedTasks.value = tasks
    chatMessages.value = messages

    // 滚动到聊天底部
    scrollToBottom()

  } catch (error) {
    console.error('加载协作数据失败:', error)
  }

  // 通知其他用户当前用户已加入
  sendWebSocketMessage({
    type: 'user_joined',
    user: currentUser.value
  })
  */

  // 设置一些模拟数据
  onlineUsers.value = [
    {
      id: 1,
      name: '当前用户',
      avatar: '👤',
      status: 'online',
      currentActivity: '正在处理音频文件'
    }
  ]

  sharedTasks.value = [
    {
      id: 1,
      title: '音频转录任务',
      description: '处理会议录音文件',
      status: 'pending',
      createdBy: 1
    }
  ]

  chatMessages.value = [
    {
      id: 1,
      text: '欢迎使用协作功能！',
      userId: 1,
      user: { name: '系统', avatar: '🤖' },
      timestamp: new Date()
    }
  ]
})

onUnmounted(() => {
  // 通知其他用户当前用户已离开
  sendWebSocketMessage({
    type: 'user_left',
    userId: currentUser.value.id
  })

  // 断开WebSocket连接
  disconnectWebSocket()
})

// 监听连接状态
watch(isConnected, (connected) => {
  connectionStatus.value = connected ? 'connected' : 'disconnected'
})

// 暴露方法
defineExpose({
  addActivity,
  sendMessage: sendMessage,
  createSharedTask,
  onlineUsers: computed(() => onlineUsers.value),
  connectionStatus: computed(() => connectionStatus.value)
})
</script>

<style scoped>
.collaboration-panel {
  background: var(--card-bg);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-color);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* 协作头部 */
.collaboration-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  background: var(--surface-bg);
  border-bottom: 1px solid var(--border-color);
}

.header-info h3 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--text-primary);
  font-size: 1.1rem;
}

.collaboration-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.online-users {
  font-size: 0.85rem;
  color: var(--text-secondary);
}

.header-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

/* 通用区域样式 */
.online-users-section,
.shared-tasks-section,
.chat-section,
.activity-log-section {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.section-header h4 {
  margin: 0;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.user-count {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

/* 在线用户列表 */
.users-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  max-height: 200px;
  overflow-y: auto;
}

.user-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-sm);
  background: var(--surface-bg);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  transition: all 0.2s ease;
}

.user-item:hover {
  border-color: var(--accent-primary);
  box-shadow: 0 2px 4px rgba(88, 166, 255, 0.2);
}

.user-item.is-current {
  border-color: var(--accent-primary);
  background: rgba(88, 166, 255, 0.1);
}

.user-avatar {
  position: relative;
}

.user-status {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  border: 2px solid var(--surface-bg);
}

.user-status.status-online {
  background: var(--success-color);
}

.user-status.status-busy {
  background: var(--warning-color);
}

.user-status.status-away {
  background: var(--text-muted);
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.user-activity {
  font-size: 0.8rem;
  color: var(--text-secondary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-actions {
  flex-shrink: 0;
}

/* 共享任务 */
.tasks-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  max-height: 300px;
  overflow-y: auto;
}

.task-item {
  padding: var(--spacing-md);
  background: var(--surface-bg);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-color);
  cursor: pointer;
  transition: all 0.2s ease;
}

.task-item:hover {
  border-color: var(--accent-primary);
  box-shadow: 0 2px 4px rgba(88, 166, 255, 0.2);
}

.task-item.task-pending {
  border-left: 3px solid var(--warning-color);
}

.task-item.task-in_progress {
  border-left: 3px solid var(--info-color);
}

.task-item.task-completed {
  border-left: 3px solid var(--success-color);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-xs);
}

.task-title {
  font-weight: 500;
  color: var(--text-primary);
  font-size: 0.9rem;
}

.task-assignee {
  flex-shrink: 0;
}

.task-description {
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.task-meta {
  display: flex;
  gap: var(--spacing-md);
  font-size: 0.75rem;
  color: var(--text-muted);
  margin-bottom: var(--spacing-sm);
}

.task-status {
  padding: 2px 6px;
  border-radius: var(--radius-xs);
  background: var(--input-bg);
  font-weight: 500;
}

.task-actions {
  display: flex;
  gap: var(--spacing-sm);
  justify-content: flex-end;
}

/* 实时聊天 */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 300px;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: var(--spacing-sm);
  background: var(--input-bg);
  border-radius: var(--radius-sm);
  margin-bottom: var(--spacing-md);
}

.message-item {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;
}

.message-item:hover {
  background: var(--surface-bg);
}

.message-item.is-own {
  flex-direction: row-reverse;
}

.message-item.is-own .message-content {
  text-align: right;
}

.message-item.is-own .message-text {
  background: var(--accent-primary);
  color: white;
}

.message-avatar {
  flex-shrink: 0;
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-header {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-xs);
  font-size: 0.8rem;
}

.message-user {
  font-weight: 500;
  color: var(--text-primary);
}

.message-time {
  color: var(--text-muted);
}

.message-text {
  padding: var(--spacing-xs) var(--spacing-sm);
  background: var(--surface-bg);
  border-radius: var(--radius-sm);
  font-size: 0.9rem;
  color: var(--text-primary);
  word-wrap: break-word;
}

.chat-input {
  flex-shrink: 0;
}

/* 操作日志 */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  max-height: 200px;
  overflow-y: auto;
}

.activity-item {
  display: flex;
  gap: var(--spacing-sm);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;
}

.activity-item:hover {
  background: var(--input-bg);
}

.activity-icon {
  flex-shrink: 0;
  font-size: 1rem;
  color: var(--text-secondary);
}

.activity-content {
  flex: 1;
  min-width: 0;
}

.activity-text {
  font-size: 0.8rem;
  color: var(--text-primary);
  margin-bottom: var(--spacing-xs);
}

.activity-time {
  font-size: 0.75rem;
  color: var(--text-muted);
}

/* 对话框内容 */
.permissions-content {
  max-height: 300px;
  overflow-y: auto;
}

.permission-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
}

.permission-item:last-child {
  border-bottom: none;
}

.permission-item .user-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.permission-item .user-name {
  font-weight: 500;
  color: var(--text-primary);
}

.invite-content {
  padding: var(--spacing-md) 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .collaboration-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: flex-start;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }

  .section-header {
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: flex-start;
  }

  .user-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .task-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-xs);
  }

  .task-meta {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .chat-container {
    height: 250px;
  }
}

/* 滚动条样式 */
.users-list::-webkit-scrollbar,
.tasks-list::-webkit-scrollbar,
.chat-messages::-webkit-scrollbar,
.activity-list::-webkit-scrollbar {
  width: 6px;
}

.users-list::-webkit-scrollbar-track,
.tasks-list::-webkit-scrollbar-track,
.chat-messages::-webkit-scrollbar-track,
.activity-list::-webkit-scrollbar-track {
  background: var(--input-bg);
}

.users-list::-webkit-scrollbar-thumb,
.tasks-list::-webkit-scrollbar-thumb,
.chat-messages::-webkit-scrollbar-thumb,
.activity-list::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.users-list::-webkit-scrollbar-thumb:hover,
.tasks-list::-webkit-scrollbar-thumb:hover,
.chat-messages::-webkit-scrollbar-thumb:hover,
.activity-list::-webkit-scrollbar-thumb:hover {
  background: var(--accent-primary);
}

/* 动画效果 */
.user-item,
.task-item,
.message-item,
.activity-item {
  animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
