#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
音频处理工具函数
提供文本分配、片段验证和质量评估功能
"""

import logging
import numpy as np
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
import re

logger = logging.getLogger(__name__)

@dataclass
class SegmentAlignment:
    """片段对齐结果"""
    segment_id: int
    start_time: float
    end_time: float
    text: str
    speaker_id: int
    speaker_label: str
    confidence: float
    alignment_score: float

@dataclass
class TextAllocationQuality:
    """文本分配质量评估结果"""
    overall_score: float
    coverage_ratio: float  # 文本覆盖率
    alignment_accuracy: float  # 对齐准确度
    speaker_consistency: float  # 说话人一致性
    temporal_coherence: float  # 时间连贯性
    issues: List[str]  # 发现的问题
    recommendations: List[str]  # 改进建议

def align_text_with_segments(
    full_text: str,
    vad_segments: List[Dict],
    speaker_segments: List[Dict],
    method: str = "time_based"
) -> List[SegmentAlignment]:
    """
    将完整文本与VAD片段和说话人信息对齐
    
    Args:
        full_text: 完整的识别文本
        vad_segments: VAD分段信息
        speaker_segments: 说话人分段信息
        method: 对齐方法 ("time_based", "length_based", "hybrid")
        
    Returns:
        List[SegmentAlignment]: 对齐结果列表
    """
    logger.info(f"开始文本对齐，方法: {method}")
    
    if not full_text or not vad_segments:
        logger.warning("输入数据为空，无法进行对齐")
        return []
    
    # 清理文本
    cleaned_text = _clean_text_for_alignment(full_text)
    
    # 根据方法选择对齐策略
    if method == "time_based":
        return _align_by_time(cleaned_text, vad_segments, speaker_segments)
    elif method == "length_based":
        return _align_by_length(cleaned_text, vad_segments, speaker_segments)
    elif method == "hybrid":
        return _align_hybrid(cleaned_text, vad_segments, speaker_segments)
    else:
        logger.error(f"未知的对齐方法: {method}")
        return []

def _clean_text_for_alignment(text: str) -> str:
    """清理文本以便对齐"""
    # 移除技术标记
    text = re.sub(r'<\|[^|]*\|>', '', text)
    # 移除多余空格
    text = re.sub(r'\s+', ' ', text)
    return text.strip()

def _align_by_time(
    text: str,
    vad_segments: List[Dict],
    speaker_segments: List[Dict]
) -> List[SegmentAlignment]:
    """基于时间的文本对齐"""
    alignments = []
    
    # 计算总时长
    total_duration = max(seg.get('end_time', 0) for seg in vad_segments) if vad_segments else 0
    if total_duration == 0:
        return alignments
    
    # 按字符数比例分配文本
    text_chars = list(text)
    char_index = 0
    
    for i, vad_seg in enumerate(vad_segments):
        start_time = vad_seg.get('start_time', 0)
        end_time = vad_seg.get('end_time', 0)
        duration = end_time - start_time
        
        # 计算该片段应分配的字符数
        time_ratio = duration / total_duration
        chars_for_segment = int(len(text_chars) * time_ratio)
        
        # 提取文本片段
        segment_text = ''.join(text_chars[char_index:char_index + chars_for_segment])
        char_index += chars_for_segment
        
        # 查找对应的说话人信息
        speaker_info = _find_speaker_for_segment(start_time, end_time, speaker_segments)
        
        alignment = SegmentAlignment(
            segment_id=i,
            start_time=start_time,
            end_time=end_time,
            text=segment_text.strip(),
            speaker_id=speaker_info.get('speaker_id', 0),
            speaker_label=speaker_info.get('speaker_label', f'说话人{speaker_info.get("speaker_id", 0) + 1}'),
            confidence=0.7,  # 基于时间的对齐置信度
            alignment_score=0.7
        )
        alignments.append(alignment)
    
    return alignments

def _align_by_length(
    text: str,
    vad_segments: List[Dict],
    speaker_segments: List[Dict]
) -> List[SegmentAlignment]:
    """基于长度的文本对齐"""
    alignments = []
    
    # 将文本按句子分割
    sentences = _split_text_into_sentences(text)
    if not sentences:
        return alignments
    
    # 平均分配句子到片段
    sentences_per_segment = len(sentences) / len(vad_segments) if vad_segments else 0
    sentence_index = 0
    
    for i, vad_seg in enumerate(vad_segments):
        start_time = vad_seg.get('start_time', 0)
        end_time = vad_seg.get('end_time', 0)
        
        # 计算该片段的句子数量
        sentences_count = max(1, int(sentences_per_segment))
        if i == len(vad_segments) - 1:  # 最后一个片段包含剩余所有句子
            sentences_count = len(sentences) - sentence_index
        
        # 提取句子
        segment_sentences = sentences[sentence_index:sentence_index + sentences_count]
        segment_text = ' '.join(segment_sentences)
        sentence_index += sentences_count
        
        # 查找对应的说话人信息
        speaker_info = _find_speaker_for_segment(start_time, end_time, speaker_segments)
        
        alignment = SegmentAlignment(
            segment_id=i,
            start_time=start_time,
            end_time=end_time,
            text=segment_text.strip(),
            speaker_id=speaker_info.get('speaker_id', 0),
            speaker_label=speaker_info.get('speaker_label', f'说话人{speaker_info.get("speaker_id", 0) + 1}'),
            confidence=0.8,  # 基于长度的对齐置信度
            alignment_score=0.8
        )
        alignments.append(alignment)
    
    return alignments

def _align_hybrid(
    text: str,
    vad_segments: List[Dict],
    speaker_segments: List[Dict]
) -> List[SegmentAlignment]:
    """混合对齐方法"""
    # 结合时间和长度两种方法
    time_alignments = _align_by_time(text, vad_segments, speaker_segments)
    length_alignments = _align_by_length(text, vad_segments, speaker_segments)
    
    # 选择更好的对齐结果
    hybrid_alignments = []
    for i, (time_align, length_align) in enumerate(zip(time_alignments, length_alignments)):
        # 简单的启发式：选择文本更合理的对齐
        if len(time_align.text.strip()) > len(length_align.text.strip()):
            best_align = time_align
        else:
            best_align = length_align
        
        # 更新置信度
        best_align.confidence = 0.85
        best_align.alignment_score = 0.85
        hybrid_alignments.append(best_align)
    
    return hybrid_alignments

def _split_text_into_sentences(text: str) -> List[str]:
    """将文本分割为句子"""
    # 简单的句子分割
    sentences = re.split(r'[。！？.!?]+', text)
    return [s.strip() for s in sentences if s.strip()]

def _find_speaker_for_segment(
    start_time: float,
    end_time: float,
    speaker_segments: List[Dict]
) -> Dict:
    """为时间片段查找对应的说话人信息"""
    segment_center = (start_time + end_time) / 2
    
    for speaker_seg in speaker_segments:
        speaker_start = speaker_seg.get('start_time', 0)
        speaker_end = speaker_seg.get('end_time', 0)
        
        # 检查时间重叠
        if speaker_start <= segment_center <= speaker_end:
            return speaker_seg
    
    # 如果没有找到重叠，返回默认信息
    return {
        'speaker_id': 0,
        'speaker_label': '说话人1'
    }

def validate_speech_segments(segments: List[Dict]) -> Tuple[bool, List[str]]:
    """
    验证语音片段的完整性和一致性
    
    Args:
        segments: 语音片段列表
        
    Returns:
        Tuple[bool, List[str]]: (是否有效, 问题列表)
    """
    issues = []
    
    if not segments:
        issues.append("片段列表为空")
        return False, issues
    
    # 检查必需字段
    required_fields = ['start_time', 'end_time', 'text']
    for i, segment in enumerate(segments):
        for field in required_fields:
            if field not in segment:
                issues.append(f"片段{i+1}缺少必需字段: {field}")
        
        # 检查时间有效性
        start_time = segment.get('start_time', 0)
        end_time = segment.get('end_time', 0)
        
        if start_time >= end_time:
            issues.append(f"片段{i+1}时间无效: start_time({start_time}) >= end_time({end_time})")
        
        if start_time < 0:
            issues.append(f"片段{i+1}开始时间为负数: {start_time}")
        
        # 检查文本内容
        text = segment.get('text', '')
        if not text or not text.strip():
            issues.append(f"片段{i+1}文本为空")
    
    # 检查时间连续性
    sorted_segments = sorted(segments, key=lambda x: x.get('start_time', 0))
    for i in range(len(sorted_segments) - 1):
        current_end = sorted_segments[i].get('end_time', 0)
        next_start = sorted_segments[i + 1].get('start_time', 0)
        
        if next_start < current_end:
            issues.append(f"片段{i+1}和{i+2}时间重叠")
    
    return len(issues) == 0, issues

def evaluate_text_allocation_quality(
    segments: List[Dict],
    original_text: str = ""
) -> TextAllocationQuality:
    """
    评估文本分配质量
    
    Args:
        segments: 分配后的语音片段
        original_text: 原始完整文本（可选）
        
    Returns:
        TextAllocationQuality: 质量评估结果
    """
    issues = []
    recommendations = []
    
    # 基础验证
    is_valid, validation_issues = validate_speech_segments(segments)
    issues.extend(validation_issues)
    
    if not is_valid:
        return TextAllocationQuality(
            overall_score=0.0,
            coverage_ratio=0.0,
            alignment_accuracy=0.0,
            speaker_consistency=0.0,
            temporal_coherence=0.0,
            issues=issues,
            recommendations=["修复基础验证问题后重新评估"]
        )
    
    # 计算各项指标
    coverage_ratio = _calculate_coverage_ratio(segments, original_text)
    alignment_accuracy = _calculate_alignment_accuracy(segments)
    speaker_consistency = _calculate_speaker_consistency(segments)
    temporal_coherence = _calculate_temporal_coherence(segments)
    
    # 计算总体评分
    overall_score = (coverage_ratio + alignment_accuracy + speaker_consistency + temporal_coherence) / 4
    
    # 生成建议
    if coverage_ratio < 0.8:
        recommendations.append("文本覆盖率较低，建议检查文本分配算法")
    if alignment_accuracy < 0.7:
        recommendations.append("对齐准确度较低，建议优化时间对齐策略")
    if speaker_consistency < 0.8:
        recommendations.append("说话人一致性较低，建议检查说话人识别结果")
    if temporal_coherence < 0.9:
        recommendations.append("时间连贯性较低，建议检查VAD分段结果")
    
    return TextAllocationQuality(
        overall_score=overall_score,
        coverage_ratio=coverage_ratio,
        alignment_accuracy=alignment_accuracy,
        speaker_consistency=speaker_consistency,
        temporal_coherence=temporal_coherence,
        issues=issues,
        recommendations=recommendations
    )

def _calculate_coverage_ratio(segments: List[Dict], original_text: str) -> float:
    """计算文本覆盖率"""
    if not original_text:
        return 1.0  # 无法比较时假设完全覆盖
    
    allocated_text = ' '.join(seg.get('text', '') for seg in segments)
    allocated_chars = len(allocated_text.replace(' ', ''))
    original_chars = len(original_text.replace(' ', ''))
    
    if original_chars == 0:
        return 1.0
    
    return min(1.0, allocated_chars / original_chars)

def _calculate_alignment_accuracy(segments: List[Dict]) -> float:
    """计算对齐准确度"""
    if not segments:
        return 0.0
    
    # 基于置信度的简单评估
    confidences = [seg.get('confidence', 0.5) for seg in segments]
    return np.mean(confidences)

def _calculate_speaker_consistency(segments: List[Dict]) -> float:
    """计算说话人一致性"""
    if not segments:
        return 0.0
    
    # 检查说话人标签的一致性
    speaker_ids = [seg.get('speaker_id', 0) for seg in segments]
    unique_speakers = len(set(speaker_ids))
    
    if unique_speakers <= 1:
        return 1.0  # 单说话人或无说话人信息
    
    # 简单的一致性评估：检查说话人切换的合理性
    switches = sum(1 for i in range(1, len(speaker_ids)) if speaker_ids[i] != speaker_ids[i-1])
    max_reasonable_switches = len(segments) * 0.3  # 最多30%的片段可以切换说话人
    
    return max(0.0, 1.0 - switches / max_reasonable_switches)

def _calculate_temporal_coherence(segments: List[Dict]) -> float:
    """计算时间连贯性"""
    if len(segments) <= 1:
        return 1.0
    
    # 检查时间间隔的合理性
    gaps = []
    sorted_segments = sorted(segments, key=lambda x: x.get('start_time', 0))
    
    for i in range(len(sorted_segments) - 1):
        current_end = sorted_segments[i].get('end_time', 0)
        next_start = sorted_segments[i + 1].get('start_time', 0)
        gap = next_start - current_end
        gaps.append(gap)
    
    # 评估间隔的合理性
    reasonable_gaps = sum(1 for gap in gaps if 0 <= gap <= 2.0)  # 0-2秒的间隔被认为是合理的
    
    return reasonable_gaps / len(gaps) if gaps else 1.0

def optimize_segment_boundaries(
    segments: List[Dict],
    audio_duration: float
) -> List[Dict]:
    """
    优化片段边界以提高连贯性
    
    Args:
        segments: 原始片段列表
        audio_duration: 音频总时长
        
    Returns:
        List[Dict]: 优化后的片段列表
    """
    if not segments:
        return segments
    
    optimized = []
    sorted_segments = sorted(segments, key=lambda x: x.get('start_time', 0))
    
    for i, segment in enumerate(sorted_segments):
        optimized_segment = segment.copy()
        
        # 调整开始时间
        if i > 0:
            prev_end = optimized[i-1].get('end_time', 0)
            current_start = segment.get('start_time', 0)
            
            # 如果间隔过小，调整边界
            if 0 < current_start - prev_end < 0.1:
                optimized_segment['start_time'] = prev_end
        
        # 调整结束时间
        if i < len(sorted_segments) - 1:
            next_start = sorted_segments[i + 1].get('start_time', 0)
            current_end = segment.get('end_time', 0)
            
            # 如果间隔过小，调整边界
            if 0 < next_start - current_end < 0.1:
                optimized_segment['end_time'] = next_start
        else:
            # 最后一个片段，确保不超过音频总时长
            optimized_segment['end_time'] = min(segment.get('end_time', 0), audio_duration)
        
        optimized.append(optimized_segment)
    
    return optimized
