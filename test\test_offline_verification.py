#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
离线环境验证测试 - 验证修复后的离线功能
专注于验证我们的三个核心修复是否有效
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def test_environment_variables_setup():
    """测试环境变量设置"""
    print("🧪 测试环境变量设置...")
    
    # 导入模块时应该自动设置环境变量
    try:
        import backend.utils.audio.speech_recognition_core
        import backend.utils.audio.optimized_funasr_manager
        import backend.utils.audio.speaker_recognition
        
        # 检查关键的离线环境变量
        offline_vars = {
            'HF_HUB_OFFLINE': '1',
            'HF_DATASETS_OFFLINE': '1', 
            'TRANSFORMERS_OFFLINE': '1',
            'HF_HUB_DISABLE_TELEMETRY': '1',
            'TOKENIZERS_PARALLELISM': 'false'
        }
        
        set_vars = 0
        for var, expected in offline_vars.items():
            actual = os.environ.get(var)
            if actual == expected:
                print(f"✅ {var} = {actual}")
                set_vars += 1
            else:
                print(f"⚠️ {var} = {actual} (期望: {expected})")
        
        print(f"📊 环境变量设置: {set_vars}/{len(offline_vars)} 个正确")
        return set_vars >= 3  # 至少3个关键变量设置正确
        
    except Exception as e:
        print(f"❌ 环境变量测试失败: {e}")
        return False


def test_data_structure_fix():
    """测试数据结构修复"""
    print("\n🧪 测试数据结构修复...")
    
    try:
        from backend.tasks.audio_processing_tasks import _integrate_meeting_results
        
        # 测试正常输入
        vad_segments = [{'start_time': 0.0, 'end_time': 2.0, 'duration': 2.0}]
        recognition_segments = [{
            'start_time': 0.0, 'end_time': 2.0, 'duration': 2.0,
            'text': '测试', 'confidence': 0.9, 'language': 'zh',
            'emotions': [], 'events': []
        }]
        speaker_segments = [{'id': 0, 'name': '说话人1'}]
        full_text = "测试"
        
        result = _integrate_meeting_results(vad_segments, recognition_segments, speaker_segments, full_text)
        
        # 验证返回结构
        assert isinstance(result, dict), "应该返回字典"
        assert 'speech_segments' in result, "应该包含speech_segments"
        assert 'speaker_segments' in result, "应该包含speaker_segments"
        assert 'total_segments' in result, "应该包含total_segments"
        assert 'total_speakers' in result, "应该包含total_speakers"
        
        print("✅ 正常输入数据结构正确")
        
        # 测试空输入
        empty_result = _integrate_meeting_results([], [], [], "")
        assert isinstance(empty_result, dict), "空输入应该返回字典"
        assert empty_result['speech_segments'] == [], "空输入speech_segments应该为空数组"
        assert empty_result['total_segments'] == 0, "空输入total_segments应该为0"
        
        print("✅ 空输入处理正确")
        return True
        
    except Exception as e:
        print(f"❌ 数据结构测试失败: {e}")
        return False


def test_websocket_progress_fix():
    """测试WebSocket进度修复"""
    print("\n🧪 测试WebSocket进度修复...")
    
    try:
        from backend.tasks.base_task import BaseTask
        from backend.api.websocket import ConnectionManager
        
        # 验证BaseTask的update_progress方法存在
        assert hasattr(BaseTask, 'update_progress'), "BaseTask应该有update_progress方法"
        print("✅ BaseTask.update_progress方法存在")
        
        # 验证ConnectionManager的改进
        manager = ConnectionManager()
        assert hasattr(manager, 'send_personal_message'), "ConnectionManager应该有send_personal_message方法"
        print("✅ ConnectionManager.send_personal_message方法存在")
        
        # 验证WebSocket异常处理导入
        try:
            from backend.api.websocket import ConnectionClosedError, ConnectionClosedOK
            print("✅ WebSocket异常类导入成功")
        except ImportError:
            print("⚠️ WebSocket异常类使用模拟实现")
        
        return True
        
    except Exception as e:
        print(f"❌ WebSocket进度测试失败: {e}")
        return False


def test_offline_model_configuration():
    """测试离线模型配置"""
    print("\n🧪 测试离线模型配置...")
    
    try:
        from backend.utils.audio.optimized_funasr_manager import OptimizedFunASRManager
        
        # 创建管理器实例
        manager = OptimizedFunASRManager()
        
        # 测试配置优化
        model_path = '/test/path'
        device = 'cpu'

        optimized_config = manager._create_optimized_config(model_path, device)
        
        # 验证离线参数
        offline_params = ['local_files_only', 'offline', 'use_auth_token', 'force_download']
        for param in offline_params:
            if param in optimized_config:
                print(f"✅ 离线参数 {param} = {optimized_config[param]}")
            else:
                print(f"⚠️ 离线参数 {param} 未设置")
        
        # 验证关键的离线设置
        assert optimized_config.get('local_files_only') == True, "应该设置local_files_only=True"
        assert optimized_config.get('offline') == True, "应该设置offline=True"
        assert optimized_config.get('use_auth_token') == False, "应该设置use_auth_token=False"
        
        print("✅ 离线模型配置正确")
        return True
        
    except Exception as e:
        print(f"❌ 离线模型配置测试失败: {e}")
        return False


def test_import_stability():
    """测试模块导入稳定性"""
    print("\n🧪 测试模块导入稳定性...")
    
    try:
        # 测试核心模块导入
        modules_to_test = [
            'backend.utils.audio.speech_recognition_core',
            'backend.utils.audio.optimized_funasr_manager', 
            'backend.utils.audio.speaker_recognition',
            'backend.tasks.audio_processing_tasks',
            'backend.api.websocket',
            'backend.tasks.base_task'
        ]
        
        imported = 0
        for module_name in modules_to_test:
            try:
                __import__(module_name)
                print(f"✅ {module_name}")
                imported += 1
            except Exception as e:
                print(f"❌ {module_name}: {e}")
        
        print(f"📊 模块导入: {imported}/{len(modules_to_test)} 个成功")
        return imported >= len(modules_to_test) - 1  # 允许1个模块失败
        
    except Exception as e:
        print(f"❌ 模块导入测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🔒 开始离线环境验证")
    print("=" * 60)
    
    tests = [
        ("环境变量设置", test_environment_variables_setup),
        ("数据结构修复", test_data_structure_fix),
        ("WebSocket进度修复", test_websocket_progress_fix),
        ("离线模型配置", test_offline_model_configuration),
        ("模块导入稳定性", test_import_stability)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 执行测试: {test_name}")
            result = test_func()
            if result:
                print(f"✅ {test_name} 通过")
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print(f"\n📊 验证结果汇总:")
    print("=" * 60)
    print(f"通过: {passed}/{total} 个测试")
    
    if passed >= total - 1:  # 允许1个测试失败
        print("🎉 离线环境验证基本通过！核心修复有效！")
        return True
    else:
        print("❌ 离线环境验证失败，需要进一步检查")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
