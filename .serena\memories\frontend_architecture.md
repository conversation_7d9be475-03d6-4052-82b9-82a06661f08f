# 前端架构分析

## 技术栈
- **框架**: Vue 3 (Composition API)
- **UI库**: Element Plus
- **构建工具**: Vite
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **HTTP客户端**: Axios
- **样式**: SCSS + CSS Variables
- **实时通信**: WebSocket

## 目录结构
```
frontend/src/
├── api/                 # API接口封装
│   ├── audio.js        # 音频处理API
│   ├── auth.js         # 认证API
│   └── websocket.js    # WebSocket通信
├── components/          # 可复用组件
│   ├── audio/          # 音频处理组件
│   │   ├── AudioConfigPanel.vue
│   │   ├── AudioUploader.vue
│   │   └── ProcessingProgress.vue
│   └── common/         # 通用组件
├── composables/         # 组合式函数
│   ├── useAuth.js      # 认证逻辑
│   ├── useAudioProcessing.js
│   └── useWebSocket.js
├── stores/              # Pinia状态管理
│   ├── auth.js         # 认证状态
│   ├── audio.js        # 音频处理状态
│   └── system.js       # 系统状态
├── utils/               # 工具函数
├── views/               # 页面组件
│   ├── AudioCenter.vue # 音频处理中心
│   ├── Login.vue       # 登录页面
│   └── Dashboard.vue   # 仪表板
└── router/              # 路由配置
```

## 主要页面路由
- `/` - 首页仪表板
- `/login` - 用户登录 (admin/admin123)
- `/audio-center` - 音频处理中心 (主要功能页面)
- `/document-manager` - 文档管理
- `/knowledge-base` - 知识库管理
- `/settings` - 系统设置

## 核心组件架构

### 音频处理中心 (AudioCenter.vue)
- **布局**: 三栏布局设计
  - 左侧: 音频配置面板 (AudioConfigPanel)
  - 中央: 文件管理和处理结果
  - 右侧: 实时进度监控
- **功能模块**:
  - 文件上传和管理
  - 音频录音功能
  - 批量处理管理
  - 实时进度显示

### 音频配置面板 (AudioConfigPanel)
- **模块化设计**: 可复用的配置组件
- **配置项**:
  - 语言选择 (中文、英文、自动检测)
  - 输出格式 (时间线、纯文本)
  - 说话人识别配置
  - 高级处理参数

### 实时进度组件 (ProcessingProgress)
- **WebSocket集成**: 实时进度更新
- **进度显示**: 百分比和状态文本
- **错误处理**: 友好的错误提示
- **完成通知**: 任务完成自动通知

## 状态管理 (Pinia Stores)

### 认证状态 (auth.js)
- 用户登录状态管理
- JWT Token存储和刷新
- 权限控制逻辑

### 音频处理状态 (audio.js)
- 音频文件列表管理
- 处理任务状态跟踪
- 配置参数存储
- 结果数据缓存

### 系统状态 (system.js)
- 系统配置管理
- WebSocket连接状态
- 全局错误处理
- 用户偏好设置

## 组合式函数 (Composables)

### useAuth
- 登录/登出逻辑
- Token管理
- 路由守卫集成

### useAudioProcessing
- 音频上传处理
- 任务提交和监控
- 结果格式化
- 错误处理

### useWebSocket
- WebSocket连接管理
- 消息订阅和处理
- 自动重连机制
- 心跳检测

### useProgressMonitor
- 实时进度监控
- 状态变化通知
- 完成回调处理

## 用户界面优化

### 设计特点
- **大字体**: 提升可读性
- **统一色彩**: Element Plus主题一致性
- **单列布局**: 配置选项清晰排列
- **响应式设计**: 适配不同屏幕尺寸

### 交互优化
- **实时反馈**: 操作状态即时显示
- **进度可视化**: 直观的进度条和状态指示
- **错误友好**: 清晰的错误提示和操作指引
- **批量操作**: 支持多文件同时处理

### 容错处理
- **空数据处理**: 优雅处理空文本片段
- **网络异常**: 自动重试和错误恢复
- **Vue生命周期**: 修复组件生命周期警告
- **异常状态**: 提升异常情况用户体验

## API集成

### HTTP客户端配置
- **Axios拦截器**: 统一请求/响应处理
- **Token自动添加**: JWT认证头自动注入
- **错误统一处理**: 全局错误处理机制
- **请求重试**: 网络异常自动重试

### WebSocket通信
- **实时连接**: 与后端WebSocket服务连接
- **消息类型**: 进度更新、状态变化、错误通知
- **连接管理**: 自动连接、断线重连
- **消息队列**: 离线消息缓存和重发

## 会议转录功能优化

### 结果显示优化
- **对话格式**: 说话人1/说话人2时间线显示
- **时间戳**: 精确的时间戳对齐
- **文本格式化**: `formatMeetingTranscriptionText()` 函数优化
- **空片段处理**: 增强空文本片段容错逻辑

### 用户体验提升
- **实时进度**: 从0%到100%精确进度显示
- **处理速度**: 显示优化后的处理时间 (3-5秒)
- **状态反馈**: 清晰的任务状态指示
- **结果导出**: 支持多种格式导出 (TXT, DOCX, JSON)

## 性能优化

### 组件优化
- **懒加载**: 路由级别的代码分割
- **组件缓存**: 合理使用keep-alive
- **计算属性**: 避免不必要的重复计算
- **事件防抖**: 用户输入防抖处理

### 数据管理
- **状态持久化**: 关键状态本地存储
- **缓存策略**: API响应数据缓存
- **内存管理**: 及时清理不需要的数据
- **批量更新**: 减少频繁的状态更新

## 开发和调试

### 开发工具
- **Vue DevTools**: 组件状态调试
- **Vite HMR**: 热模块替换
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化

### 调试功能
- **控制台日志**: 详细的调试信息
- **错误边界**: 组件错误捕获
- **性能监控**: 组件渲染性能跟踪
- **网络监控**: API请求状态监控

## 部署和构建

### 构建配置
- **Vite配置**: 优化的构建配置
- **环境变量**: 开发/生产环境区分
- **资源优化**: 图片、字体等资源优化
- **代码分割**: 合理的chunk分割策略

### 部署特点
- **静态资源**: 构建后的静态文件
- **API代理**: 开发环境API代理配置
- **路由模式**: History模式路由
- **缓存策略**: 静态资源缓存配置