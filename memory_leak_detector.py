#!/usr/bin/env python3
"""
音频处理系统内存泄漏检测器
专门检测和分析音频处理过程中的内存泄漏问题
"""

import os
import sys
import gc
import time
import psutil
import tracemalloc
from pathlib import Path
from typing import Dict, List, Any
import threading

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

class MemoryLeakDetector:
    """内存泄漏检测器"""
    
    def __init__(self):
        self.process = psutil.Process()
        self.initial_memory = None
        self.snapshots = []
        
    def start_detection(self):
        """开始内存泄漏检测"""
        # 启动内存跟踪
        tracemalloc.start()
        
        # 记录初始内存状态
        self.initial_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        print(f"🔍 开始内存泄漏检测，初始内存: {self.initial_memory:.1f}MB")
        
        # 强制垃圾回收
        gc.collect()
        
    def take_snapshot(self, label: str):
        """拍摄内存快照"""
        snapshot = tracemalloc.take_snapshot()
        current_memory = self.process.memory_info().rss / 1024 / 1024  # MB
        
        self.snapshots.append({
            'label': label,
            'snapshot': snapshot,
            'memory_mb': current_memory,
            'memory_increase': current_memory - self.initial_memory
        })
        
        print(f"📸 快照 [{label}]: 当前内存 {current_memory:.1f}MB, "
              f"增长 {current_memory - self.initial_memory:.1f}MB")
    
    def analyze_leaks(self):
        """分析内存泄漏"""
        if len(self.snapshots) < 2:
            print("⚠️ 快照数量不足，无法分析内存泄漏")
            return
        
        print("\n" + "="*60)
        print("🔬 内存泄漏分析报告")
        print("="*60)
        
        # 比较第一个和最后一个快照
        first_snapshot = self.snapshots[0]['snapshot']
        last_snapshot = self.snapshots[-1]['snapshot']
        
        # 获取内存增长最多的代码行
        top_stats = last_snapshot.compare_to(first_snapshot, 'lineno')
        
        print(f"\n📊 内存增长最多的代码位置 (前10个):")
        for index, stat in enumerate(top_stats[:10]):
            print(f"{index+1:2d}. {stat}")
        
        # 分析内存增长趋势
        print(f"\n📈 内存增长趋势:")
        for i, snapshot_info in enumerate(self.snapshots):
            print(f"{i+1:2d}. {snapshot_info['label']:20s} "
                  f"{snapshot_info['memory_mb']:8.1f}MB "
                  f"(+{snapshot_info['memory_increase']:6.1f}MB)")
        
        # 检测可能的内存泄漏
        total_increase = self.snapshots[-1]['memory_increase']
        if total_increase > 100:  # 超过100MB认为是严重泄漏
            print(f"\n🚨 检测到严重内存泄漏: {total_increase:.1f}MB")
        elif total_increase > 50:  # 超过50MB认为是中等泄漏
            print(f"\n⚠️ 检测到中等内存泄漏: {total_increase:.1f}MB")
        elif total_increase > 20:  # 超过20MB认为是轻微泄漏
            print(f"\n💡 检测到轻微内存增长: {total_increase:.1f}MB")
        else:
            print(f"\n✅ 内存使用正常: {total_increase:.1f}MB")
    
    def stop_detection(self):
        """停止内存泄漏检测"""
        tracemalloc.stop()
        print("🛑 内存泄漏检测已停止")


def test_funasr_model_loading():
    """测试FunASR模型加载的内存使用"""
    print("\n🤖 测试FunASR模型加载...")
    
    try:
        # 尝试导入FunASR
        try:
            from funasr import AutoModel
            print("✅ FunASR可用")
        except ImportError:
            print("⚠️ FunASR不可用，跳过测试")
            return
        
        # 测试模型加载和卸载
        model_path = "./models/SenseVoiceSmall"  # 假设的模型路径
        
        if not os.path.exists(model_path):
            print(f"⚠️ 模型路径不存在: {model_path}")
            return
        
        # 多次加载和卸载模型
        for i in range(3):
            print(f"  第{i+1}次加载模型...")
            try:
                model = AutoModel(
                    model=model_path,
                    trust_remote_code=True,
                    device='cpu'  # 使用CPU避免GPU内存问题
                )
                
                # 模拟推理
                test_audio = "resource/对话.mp3"
                if os.path.exists(test_audio):
                    result = model.generate(input=test_audio)
                    print(f"    推理结果长度: {len(str(result))}")
                
                # 显式删除模型
                del model
                gc.collect()
                
            except Exception as e:
                print(f"    模型加载失败: {e}")
        
        print("✅ FunASR模型测试完成")
        
    except Exception as e:
        print(f"❌ FunASR模型测试失败: {e}")


def test_audio_data_processing():
    """测试音频数据处理的内存使用"""
    print("\n🎵 测试音频数据处理...")
    
    try:
        import numpy as np
        import soundfile as sf
        
        audio_file = "resource/对话.mp3"
        if not os.path.exists(audio_file):
            print(f"⚠️ 测试文件不存在: {audio_file}")
            return
        
        # 多次处理同一音频文件
        for i in range(5):
            print(f"  第{i+1}次处理音频...")
            
            # 加载音频
            audio, sr = sf.read(audio_file)
            original_size = audio.nbytes / 1024 / 1024  # MB
            print(f"    原始音频大小: {original_size:.1f}MB")
            
            # 模拟各种音频处理操作
            # 1. 重采样
            if sr != 16000:
                import librosa
                audio_resampled = librosa.resample(audio, orig_sr=sr, target_sr=16000)
            else:
                audio_resampled = audio.copy()
            
            # 2. 音频标准化
            audio_normalized = audio_resampled / np.max(np.abs(audio_resampled))
            
            # 3. 特征提取（MFCC）
            try:
                import librosa
                mfccs = librosa.feature.mfcc(y=audio_normalized, sr=16000, n_mfcc=13)
                print(f"    MFCC特征形状: {mfccs.shape}")
            except Exception as e:
                print(f"    MFCC提取失败: {e}")
                mfccs = None
            
            # 4. 频谱分析
            fft_result = np.fft.fft(audio_normalized)
            spectrum = np.abs(fft_result)
            
            # 显式删除大对象
            del audio, audio_resampled, audio_normalized, fft_result, spectrum
            if mfccs is not None:
                del mfccs
            
            # 强制垃圾回收
            gc.collect()
        
        print("✅ 音频数据处理测试完成")
        
    except Exception as e:
        print(f"❌ 音频数据处理测试失败: {e}")


def test_temporary_file_management():
    """测试临时文件管理"""
    print("\n📁 测试临时文件管理...")
    
    try:
        import tempfile
        import shutil
        
        temp_files = []
        temp_dirs = []
        
        # 创建多个临时文件和目录
        for i in range(10):
            # 创建临时文件
            with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as tmp_file:
                tmp_file.write(b"fake audio data" * 1000)  # 写入一些数据
                temp_files.append(tmp_file.name)
            
            # 创建临时目录
            temp_dir = tempfile.mkdtemp(prefix=f'audio_test_{i}_')
            temp_dirs.append(temp_dir)
            
            # 在临时目录中创建文件
            test_file = os.path.join(temp_dir, f'test_{i}.txt')
            with open(test_file, 'w') as f:
                f.write("test data" * 100)
        
        print(f"  创建了 {len(temp_files)} 个临时文件")
        print(f"  创建了 {len(temp_dirs)} 个临时目录")
        
        # 检查文件是否存在
        existing_files = sum(1 for f in temp_files if os.path.exists(f))
        existing_dirs = sum(1 for d in temp_dirs if os.path.exists(d))
        
        print(f"  存在的临时文件: {existing_files}")
        print(f"  存在的临时目录: {existing_dirs}")
        
        # 清理临时文件和目录
        for temp_file in temp_files:
            try:
                if os.path.exists(temp_file):
                    os.unlink(temp_file)
            except Exception as e:
                print(f"    清理文件失败: {temp_file}, {e}")
        
        for temp_dir in temp_dirs:
            try:
                if os.path.exists(temp_dir):
                    shutil.rmtree(temp_dir)
            except Exception as e:
                print(f"    清理目录失败: {temp_dir}, {e}")
        
        print("✅ 临时文件管理测试完成")
        
    except Exception as e:
        print(f"❌ 临时文件管理测试失败: {e}")


def run_memory_leak_detection():
    """运行内存泄漏检测"""
    detector = MemoryLeakDetector()
    
    try:
        # 开始检测
        detector.start_detection()
        detector.take_snapshot("初始状态")
        
        # 测试1: FunASR模型加载
        test_funasr_model_loading()
        detector.take_snapshot("FunASR模型测试后")
        
        # 测试2: 音频数据处理
        test_audio_data_processing()
        detector.take_snapshot("音频数据处理后")
        
        # 测试3: 临时文件管理
        test_temporary_file_management()
        detector.take_snapshot("临时文件管理后")
        
        # 强制垃圾回收
        print("\n🧹 执行垃圾回收...")
        collected = gc.collect()
        print(f"垃圾回收清理了 {collected} 个对象")
        detector.take_snapshot("垃圾回收后")
        
        # 分析结果
        detector.analyze_leaks()
        
    finally:
        detector.stop_detection()


if __name__ == "__main__":
    run_memory_leak_detection()
