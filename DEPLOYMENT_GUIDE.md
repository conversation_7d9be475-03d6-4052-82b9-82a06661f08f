# 🚀 部署指南

## 📋 部署前准备

### 系统要求
- **操作系统**: Windows 10/11, Ubuntu 18.04+, CentOS 7+
- **内存**: 最低8GB，推荐16GB+
- **存储**: 最低20GB可用空间
- **网络**: 稳定的互联网连接（用于依赖下载）

### 必需软件
- **Python**: 3.11+
- **Node.js**: 16.0+
- **Docker**: 20.10+ (可选)
- **Git**: 2.0+

## 🔧 环境配置

### 1. 克隆项目
```bash
git clone <repository-url>
cd my_notebook_version_0.1.0
```

### 2. 环境变量配置
```bash
# 复制环境变量模板
cp env.template.txt .env

# 编辑环境变量文件
nano .env
```

#### 关键环境变量
```bash
# 数据库配置
DATABASE_URL=sqlite:///./data/speech_platform.db

# Redis配置
REDIS_URL=redis://localhost:6379/0

# 模型路径配置
SENSEVOICE_MODEL_PATH=./models/SenseVoiceSmall
VAD_MODEL_PATH=./models/fsmn_vad_zh
CAMPPLUS_MODEL_PATH=./models/cam++

# 安全配置
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key

# 文件上传配置
MAX_UPLOAD_SIZE=100MB
UPLOAD_PATH=./data/uploads
```

## 🐳 Docker部署 (推荐)

### 使用Docker Compose
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### Docker Compose配置
```yaml
# docker-compose.yml
version: '3.8'

services:
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  backend:
    build: ./backend
    ports:
      - "8002:8002"
    environment:
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./data:/app/data
      - ./models:/app/models
    depends_on:
      - redis

  celery:
    build: ./backend
    command: celery -A backend.celery_app worker --loglevel=info
    environment:
      - REDIS_URL=redis://redis:6379/0
    volumes:
      - ./data:/app/data
      - ./models:/app/models
    depends_on:
      - redis

  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    depends_on:
      - backend

volumes:
  redis_data:
```

## 🔨 手动部署

### 1. 后端部署

#### 创建虚拟环境
```bash
python -m venv .venv

# Windows
.venv\Scripts\activate

# Linux/macOS
source .venv/bin/activate
```

#### 安装依赖
```bash
cd backend
pip install -r requirements.txt
```

#### 初始化数据库
```bash
python -c "from backend.core.database import init_db; init_db()"
```

#### 启动后端服务
```bash
python -m uvicorn backend.main:app --host 0.0.0.0 --port 8002
```

### 2. Celery Worker部署

#### 启动Worker (Windows)
```bash
python start_worker_windows.py
```

#### 启动Worker (Linux/macOS)
```bash
celery -A backend.celery_app worker --loglevel=info --concurrency=4
```

### 3. 前端部署

#### 安装依赖
```bash
cd frontend
npm install
```

#### 开发环境启动
```bash
npm run dev
```

#### 生产环境构建
```bash
npm run build
npm run preview
```

### 4. Redis部署

#### Docker方式
```bash
docker run -d --name redis -p 6379:6379 redis:latest
```

#### 系统安装方式
```bash
# Ubuntu/Debian
sudo apt-get install redis-server

# CentOS/RHEL
sudo yum install redis

# 启动Redis
sudo systemctl start redis
sudo systemctl enable redis
```

## 🌐 生产环境部署

### 使用Nginx反向代理

#### Nginx配置
```nginx
# /etc/nginx/sites-available/speech-platform
server {
    listen 80;
    server_name your-domain.com;

    # 前端静态文件
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    # 后端API
    location /api/ {
        proxy_pass http://localhost:8002;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    # WebSocket连接
    location /ws/ {
        proxy_pass http://localhost:8002;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }

    # 文件上传大小限制
    client_max_body_size 100M;
}
```

#### 启用配置
```bash
sudo ln -s /etc/nginx/sites-available/speech-platform /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 使用Supervisor管理进程

#### Supervisor配置
```ini
# /etc/supervisor/conf.d/speech-platform.conf
[program:speech-backend]
command=/path/to/.venv/bin/python -m uvicorn backend.main:app --host 0.0.0.0 --port 8002
directory=/path/to/my_notebook_version_0.1.0
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/speech-backend.log

[program:speech-celery]
command=/path/to/.venv/bin/python start_worker_windows.py
directory=/path/to/my_notebook_version_0.1.0
user=www-data
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/var/log/speech-celery.log
```

#### 启动服务
```bash
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start speech-backend
sudo supervisorctl start speech-celery
```

## 🔍 健康检查

### 服务状态检查
```bash
# 检查后端服务
curl http://localhost:8002/health

# 检查Redis连接
redis-cli ping

# 检查Celery Worker
python -c "from backend.celery_app import app; print(app.control.inspect().active())"
```

### 系统监控脚本
```python
# scripts/health_check.py
import requests
import redis
import sys

def check_backend():
    try:
        response = requests.get('http://localhost:8002/health', timeout=5)
        return response.status_code == 200
    except:
        return False

def check_redis():
    try:
        r = redis.Redis(host='localhost', port=6379, db=0)
        return r.ping()
    except:
        return False

def main():
    checks = {
        'Backend': check_backend(),
        'Redis': check_redis()
    }
    
    all_healthy = all(checks.values())
    
    for service, status in checks.items():
        print(f"{service}: {'✅ Healthy' if status else '❌ Unhealthy'}")
    
    sys.exit(0 if all_healthy else 1)

if __name__ == '__main__':
    main()
```

## 🔧 故障排除

### 常见问题解决

#### 1. 端口占用
```bash
# 查找占用端口的进程
netstat -tulpn | grep :8002
lsof -i :8002

# 终止进程
kill -9 <PID>
```

#### 2. 权限问题
```bash
# 修改文件权限
chmod +x start_worker_windows.py
chown -R www-data:www-data /path/to/project
```

#### 3. 依赖冲突
```bash
# 清理Python缓存
find . -type d -name __pycache__ -delete
find . -name "*.pyc" -delete

# 重新安装依赖
pip uninstall -r requirements.txt -y
pip install -r requirements.txt
```

#### 4. 模型加载失败
```bash
# 检查模型文件完整性
ls -la models/SenseVoiceSmall/
python -c "import torch; print(torch.load('models/SenseVoiceSmall/model.pt', map_location='cpu').keys())"
```

## 📊 性能调优

### 系统优化
```bash
# 增加文件描述符限制
echo "* soft nofile 65536" >> /etc/security/limits.conf
echo "* hard nofile 65536" >> /etc/security/limits.conf

# 优化内核参数
echo "net.core.somaxconn = 65535" >> /etc/sysctl.conf
echo "net.ipv4.tcp_max_syn_backlog = 65535" >> /etc/sysctl.conf
sysctl -p
```

### 应用优化
```python
# backend/config/settings.py
# 数据库连接池
DATABASE_POOL_SIZE = 20
DATABASE_MAX_OVERFLOW = 30

# Celery配置
CELERY_WORKER_CONCURRENCY = 4
CELERY_WORKER_PREFETCH_MULTIPLIER = 1
CELERY_TASK_ACKS_LATE = True

# 缓存配置
REDIS_CACHE_TTL = 3600
REDIS_MAX_CONNECTIONS = 50
```

## 🔐 安全配置

### SSL/TLS配置
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;
    
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;
    
    # 其他配置...
}
```

### 防火墙配置
```bash
# UFW配置
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# iptables配置
iptables -A INPUT -p tcp --dport 22 -j ACCEPT
iptables -A INPUT -p tcp --dport 80 -j ACCEPT
iptables -A INPUT -p tcp --dport 443 -j ACCEPT
iptables -A INPUT -j DROP
```

## 📈 监控和日志

### 日志配置
```python
# backend/core/logging.py
LOGGING_CONFIG = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'default': {
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        },
    },
    'handlers': {
        'file': {
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': 'logs/app.log',
            'maxBytes': 10485760,  # 10MB
            'backupCount': 5,
            'formatter': 'default',
        },
    },
    'root': {
        'level': 'INFO',
        'handlers': ['file'],
    },
}
```

### 监控脚本
```bash
#!/bin/bash
# scripts/monitor.sh

# 检查服务状态
systemctl is-active nginx
systemctl is-active redis
supervisorctl status

# 检查资源使用
df -h
free -h
top -bn1 | head -20

# 检查日志错误
tail -n 100 /var/log/speech-backend.log | grep ERROR
```

## 🔄 备份和恢复

### 数据备份
```bash
#!/bin/bash
# scripts/backup.sh

BACKUP_DIR="/backup/$(date +%Y%m%d_%H%M%S)"
mkdir -p $BACKUP_DIR

# 备份数据库
cp data/speech_platform.db $BACKUP_DIR/

# 备份上传文件
tar -czf $BACKUP_DIR/uploads.tar.gz data/uploads/

# 备份向量数据库
tar -czf $BACKUP_DIR/chroma_db.tar.gz chroma_db/

# 备份配置文件
cp .env $BACKUP_DIR/
```

### 数据恢复
```bash
#!/bin/bash
# scripts/restore.sh

BACKUP_DIR=$1

# 停止服务
supervisorctl stop speech-backend
supervisorctl stop speech-celery

# 恢复数据
cp $BACKUP_DIR/speech_platform.db data/
tar -xzf $BACKUP_DIR/uploads.tar.gz -C data/
tar -xzf $BACKUP_DIR/chroma_db.tar.gz

# 启动服务
supervisorctl start speech-backend
supervisorctl start speech-celery
```
