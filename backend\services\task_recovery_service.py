"""
任务恢复和重试服务
负责处理失败任务的恢复和重试逻辑
"""

import asyncio
from typing import List, Dict, Any, Optional
from datetime import datetime, timezone, timedelta
from loguru import logger

from backend.services.task_persistence_service import get_task_persistence_service
from backend.core.task_queue import get_task_manager
from backend.core.database import get_db_session
from backend.models.task_models import TaskRecord, TaskStatus


class TaskRecoveryService:
    """任务恢复服务"""
    
    def __init__(self):
        self.persistence_service = get_task_persistence_service()
        self.task_manager = get_task_manager()
    
    def retry_failed_task(self, task_id: str) -> bool:
        """重试失败的任务"""
        db = get_db_session()
        try:
            # 获取任务记录
            task_record = self.persistence_service.get_task_record(db, task_id)
            if not task_record:
                logger.error(f"任务记录不存在: {task_id}")
                return False
            
            # 检查是否可以重试
            if task_record.status != TaskStatus.FAILURE:
                logger.warning(f"任务状态不是失败，无法重试: {task_id}, 当前状态: {task_record.status}")
                return False
            
            if task_record.retry_count >= task_record.max_retries:
                logger.warning(f"任务重试次数已达上限: {task_id}, {task_record.retry_count}/{task_record.max_retries}")
                return False
            
            # 增加重试次数
            self.persistence_service.increment_retry_count(db, task_id)
            
            # 重置任务状态
            self.persistence_service.update_task_status(
                db=db,
                task_id=task_id,
                status=TaskStatus.PENDING,
                progress_percentage=0.0,
                progress_detail="准备重试...",
                progress_stage="retrying",
                error_message=None,
                traceback=None
            )
            
            # 重新提交任务
            success = self._resubmit_task(task_record)
            
            if success:
                logger.info(f"任务重试成功: {task_id}")
                return True
            else:
                logger.error(f"任务重试失败: {task_id}")
                return False
                
        except Exception as e:
            logger.error(f"重试任务时出错: {task_id}, {e}")
            return False
        finally:
            db.close()
    
    def _resubmit_task(self, task_record: TaskRecord) -> bool:
        """重新提交任务"""
        try:
            task_type = task_record.task_type
            task_args = task_record.task_args or []
            
            if task_type == "document_processing":
                # 重新提交文档处理任务
                new_task_id = self.task_manager.submit_document_processing_task(
                    user_id=task_args[1] if len(task_args) > 1 else "",
                    file_content=b"",  # 需要从Redis重新获取
                    filename=task_args[2] if len(task_args) > 2 else "",
                    use_ocr=task_args[4] if len(task_args) > 4 else False,
                    ocr_config=task_args[5] if len(task_args) > 5 else None
                )
                return new_task_id is not None
                
            elif task_type == "vectorization":
                # 重新提交向量化任务
                new_task_id = self.task_manager.submit_vectorization_task(
                    user_id=task_args[1] if len(task_args) > 1 else "",
                    document_id=task_args[2] if len(task_args) > 2 else 0,
                    sections_data=task_args[3] if len(task_args) > 3 else [],
                    embedding_config=task_args[4] if len(task_args) > 4 else None
                )
                return new_task_id is not None
                
            elif task_type == "ocr_processing":
                # 重新提交OCR任务
                new_task_id = self.task_manager.submit_ocr_task(
                    user_id=task_args[1] if len(task_args) > 1 else "",
                    image_data=b"",  # 需要从Redis重新获取
                    ocr_config=task_args[4] if len(task_args) > 4 else {},
                    filename=task_args[2] if len(task_args) > 2 else ""
                )
                return new_task_id is not None
            
            else:
                logger.warning(f"未知的任务类型，无法重试: {task_type}")
                return False
                
        except Exception as e:
            logger.error(f"重新提交任务失败: {e}")
            return False
    
    def batch_retry_failed_tasks(self, user_id: str = None, hours: int = 24) -> Dict[str, Any]:
        """批量重试失败的任务"""
        db = get_db_session()
        try:
            # 获取可重试的任务
            retryable_tasks = self.persistence_service.get_retryable_tasks(db, user_id)
            
            results = {
                "total_tasks": len(retryable_tasks),
                "success_count": 0,
                "failed_count": 0,
                "results": []
            }
            
            for task_record in retryable_tasks:
                try:
                    success = self.retry_failed_task(task_record.task_id)
                    if success:
                        results["success_count"] += 1
                        results["results"].append({
                            "task_id": task_record.task_id,
                            "task_name": task_record.task_name,
                            "success": True
                        })
                    else:
                        results["failed_count"] += 1
                        results["results"].append({
                            "task_id": task_record.task_id,
                            "task_name": task_record.task_name,
                            "success": False,
                            "error": "重试失败"
                        })
                        
                except Exception as e:
                    results["failed_count"] += 1
                    results["results"].append({
                        "task_id": task_record.task_id,
                        "task_name": task_record.task_name,
                        "success": False,
                        "error": str(e)
                    })
            
            logger.info(f"批量重试完成: 成功 {results['success_count']}, 失败 {results['failed_count']}")
            return results
            
        except Exception as e:
            logger.error(f"批量重试失败: {e}")
            return {"error": str(e)}
        finally:
            db.close()
    
    def recover_orphaned_tasks(self) -> Dict[str, Any]:
        """恢复孤儿任务（长时间运行但没有更新的任务）"""
        db = get_db_session()
        try:
            # 查找长时间运行的任务（超过1小时没有更新）
            cutoff_time = datetime.now(timezone.utc) - timedelta(hours=1)
            
            orphaned_tasks = db.query(TaskRecord).filter(
                TaskRecord.status == TaskStatus.STARTED,
                TaskRecord.updated_at < cutoff_time
            ).all()
            
            results = {
                "total_tasks": len(orphaned_tasks),
                "recovered_count": 0,
                "failed_count": 0,
                "results": []
            }
            
            for task_record in orphaned_tasks:
                try:
                    # 检查Celery中的任务状态
                    celery_result = self.task_manager.celery_app.AsyncResult(task_record.task_id)
                    
                    if celery_result.state == 'PENDING':
                        # 任务在Celery中不存在，标记为失败
                        self.persistence_service.update_task_status(
                            db=db,
                            task_id=task_record.task_id,
                            status=TaskStatus.FAILURE,
                            error_message="任务丢失，可能由于Worker重启"
                        )
                        
                        results["recovered_count"] += 1
                        results["results"].append({
                            "task_id": task_record.task_id,
                            "task_name": task_record.task_name,
                            "action": "marked_as_failed",
                            "reason": "task_lost"
                        })
                        
                    elif celery_result.state in ['SUCCESS', 'FAILURE']:
                        # 任务已完成但数据库状态未更新
                        new_status = TaskStatus.SUCCESS if celery_result.state == 'SUCCESS' else TaskStatus.FAILURE
                        
                        self.persistence_service.update_task_status(
                            db=db,
                            task_id=task_record.task_id,
                            status=new_status,
                            result=celery_result.result if celery_result.successful() else None,
                            error_message=str(celery_result.result) if celery_result.failed() else None
                        )
                        
                        results["recovered_count"] += 1
                        results["results"].append({
                            "task_id": task_record.task_id,
                            "task_name": task_record.task_name,
                            "action": "status_updated",
                            "new_status": new_status
                        })
                        
                except Exception as e:
                    results["failed_count"] += 1
                    results["results"].append({
                        "task_id": task_record.task_id,
                        "task_name": task_record.task_name,
                        "action": "recovery_failed",
                        "error": str(e)
                    })
            
            logger.info(f"孤儿任务恢复完成: 恢复 {results['recovered_count']}, 失败 {results['failed_count']}")
            return results
            
        except Exception as e:
            logger.error(f"恢复孤儿任务失败: {e}")
            return {"error": str(e)}
        finally:
            db.close()
    
    def get_task_recovery_status(self, user_id: str = None) -> Dict[str, Any]:
        """获取任务恢复状态统计"""
        db = get_db_session()
        try:
            # 获取失败任务
            failed_tasks = self.persistence_service.get_failed_tasks(db, user_id, hours=24)
            
            # 获取可重试任务
            retryable_tasks = self.persistence_service.get_retryable_tasks(db, user_id)
            
            # 统计信息
            stats = {
                "failed_tasks_24h": len(failed_tasks),
                "retryable_tasks": len(retryable_tasks),
                "max_retry_reached": len([t for t in failed_tasks if t.retry_count >= t.max_retries]),
                "task_types": {}
            }
            
            # 按任务类型统计
            for task in failed_tasks:
                task_type = task.task_type
                if task_type not in stats["task_types"]:
                    stats["task_types"][task_type] = {
                        "failed": 0,
                        "retryable": 0
                    }
                stats["task_types"][task_type]["failed"] += 1
                
                if task.retry_count < task.max_retries:
                    stats["task_types"][task_type]["retryable"] += 1
            
            return stats
            
        except Exception as e:
            logger.error(f"获取任务恢复状态失败: {e}")
            return {"error": str(e)}
        finally:
            db.close()


# 全局任务恢复服务实例
task_recovery_service = TaskRecoveryService()


def get_task_recovery_service() -> TaskRecoveryService:
    """获取任务恢复服务实例"""
    return task_recovery_service
