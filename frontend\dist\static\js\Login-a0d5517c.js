import{_ as k,u as R,g as L,h as f,i as U,r as t,a as S,c as h,b as r,d as o,w as l,j as q,f as d,k as z,t as B,E as c}from"./index-2c134546.js";const C={class:"login-container"},E={class:"login-card"},F={class:"login-footer"},N={__name:"Login",setup(K){const _=R(),v=L(),m=f(),s=U({username:"",password:"",remember:!1}),b={username:[{required:!0,message:"请输入用户名",trigger:"blur"},{min:3,max:20,message:"用户名长度在 3 到 20 个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"密码长度在 6 到 20 个字符",trigger:"blur"}]},i=f(!1),p=async()=>{if(m.value)try{await m.value.validate(),i.value=!0;const n=await v.login({username:s.username,password:s.password});if(n.success){c.success("登录成功");const e=_.currentRoute.value.query.redirect||"/dashboard";_.push(e)}else c.error(n.message||"登录失败")}catch(n){console.error("登录错误:",n),c.error("登录过程中出现错误")}finally{i.value=!1}};return(n,e)=>{const g=t("el-input"),u=t("el-form-item"),w=t("el-checkbox"),y=t("el-button"),x=t("el-form"),V=t("router-link");return S(),h("div",C,[r("div",E,[e[7]||(e[7]=r("div",{class:"login-header"},[r("div",{class:"brand-icon"},"🧠"),r("h1",null,"RAG智能问答平台"),r("p",null,"欢迎回来，请登录您的账户")],-1)),o(x,{ref_key:"loginFormRef",ref:m,model:s,rules:b,class:"login-form",onSubmit:q(p,["prevent"])},{default:l(()=>[o(u,{prop:"username"},{default:l(()=>[o(g,{modelValue:s.username,"onUpdate:modelValue":e[0]||(e[0]=a=>s.username=a),placeholder:"请输入用户名",size:"large","prefix-icon":"User"},null,8,["modelValue"])]),_:1}),o(u,{prop:"password"},{default:l(()=>[o(g,{modelValue:s.password,"onUpdate:modelValue":e[1]||(e[1]=a=>s.password=a),type:"password",placeholder:"请输入密码",size:"large","prefix-icon":"Lock","show-password":"",onKeyup:z(p,["enter"])},null,8,["modelValue"])]),_:1}),o(u,null,{default:l(()=>[o(w,{modelValue:s.remember,"onUpdate:modelValue":e[2]||(e[2]=a=>s.remember=a)},{default:l(()=>e[3]||(e[3]=[d("记住我")])),_:1,__:[3]},8,["modelValue"])]),_:1}),o(u,null,{default:l(()=>[o(y,{type:"primary",size:"large",loading:i.value,onClick:p,class:"login-button"},{default:l(()=>[d(B(i.value?"登录中...":"登录"),1)]),_:1},8,["loading"])]),_:1})]),_:1},8,["model"]),r("div",F,[r("p",null,[e[5]||(e[5]=d(" 还没有账户？ ")),o(V,{to:"/register",class:"register-link"},{default:l(()=>e[4]||(e[4]=[d("立即注册")])),_:1,__:[4]})]),e[6]||(e[6]=r("p",{class:"demo-info"}," 演示账户：admin / admin123 ",-1))])])])}}},j=k(N,[["__scopeId","data-v-a1ddcfc3"]]);export{j as default};
