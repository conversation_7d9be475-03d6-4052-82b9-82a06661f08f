# 使用阿里云Python基础镜像
FROM registry.cn-hangzhou.aliyuncs.com/acs/python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1
ENV STREAMLIT_SERVER_PORT=8501
ENV STREAMLIT_SERVER_ADDRESS=0.0.0.0
ENV STREAMLIT_SERVER_HEADLESS=true
ENV STREAMLIT_BROWSER_GATHER_USAGE_STATS=false

# 检查并配置阿里云APT源
RUN if [ -f /etc/apt/sources.list ]; then \
        cp /etc/apt/sources.list /etc/apt/sources.list.bak && \
        sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list && \
        sed -i 's/security.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list; \
    else \
        echo "deb https://mirrors.aliyun.com/debian/ bookworm main non-free-firmware" > /etc/apt/sources.list && \
        echo "deb https://mirrors.aliyun.com/debian-security/ bookworm-security main" >> /etc/apt/sources.list && \
        echo "deb https://mirrors.aliyun.com/debian/ bookworm-updates main non-free-firmware" >> /etc/apt/sources.list; \
    fi

# 升级pip并配置阿里云PyPI镜像源
RUN python -m pip install --upgrade pip -i https://mirrors.aliyun.com/pypi/simple/ && \
    pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/ && \
    pip config set install.trusted-host mirrors.aliyun.com && \
    pip config set global.timeout 60

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    software-properties-common \
    git \
    tesseract-ocr \
    tesseract-ocr-chi-sim \
    tesseract-ocr-chi-tra \
    poppler-utils \
    libgl1-mesa-glx \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 复制requirements.txt并安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir --timeout 300 -r requirements.txt

# 分批安装LlamaIndex组件以避免超时
RUN pip install --no-cache-dir --timeout 300 \
    llama-index \
    llama-index-embeddings-huggingface \
    llama-index-embeddings-ollama

RUN pip install --no-cache-dir --timeout 300 \
    llama-index-llms-ollama \
    llama-index-vector-stores-chroma \
    chromadb

RUN pip install --no-cache-dir --timeout 300 \
    sentence-transformers \
    PyPDF2 \
    langchain \
    langchain-core

# 复制项目文件
COPY . .

# 创建必要的目录
RUN mkdir -p /app/chroma_db /app/data/documents /app/knowledge_base /app/models

# 设置文件权限
RUN chmod -R 755 /app

# 暴露Streamlit默认端口
EXPOSE 8501

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl --fail http://localhost:8501/_stcore/health || exit 1

# 启动命令
CMD ["streamlit", "run", "Home.py", "--server.port=8501", "--server.address=0.0.0.0"] 